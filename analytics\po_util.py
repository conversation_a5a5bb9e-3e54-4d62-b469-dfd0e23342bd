import new_pgdb as db
from new_pgdb import po_reorder_db
from datetime import datetime
import new_utils
from utils.common import conver_to_json, get_six_month_data_using_sku, month_array, paginate_data, convert_to_timestamp
from sqlalchemy import text
import traceback
import logging
from appconfig import is_pgdb_read_only_enabled
import new_mongodb

logger = logging.getLogger()

reorder_table = ['sale_history_months',
                 'days_to_replenishment', 'quantity', 'modified_by']
skuvault_catalog = ['sku', 'title', 'cost',
                    'brand', 'classification', 'quantity_available', 'quantity_incoming', 'reorder_point', 'incremental_quantity', 'primary_supplier']
month_filter_array = ['month_1', 'month_2', 'month_3',
                      'month_4', 'month_5', 'month_6', 'month_7', 'suggested_order_qty', 'total_sold', 'total_cost', 'total_sold_30', 'weeks_on_hand', 'turn_rate', 'to_order_qty']


def build_reorder_model(dto):
    return po_reorder_db.POReorders(
        variant_id=dto['variant_id'],
        product_id=dto['product_id'],
        sku=dto['sku'],
        quantity=dto['quantity'],
        sale_history_months=dto['sale_history_months'],
        days_to_replenishment=dto['days_to_replenishment'],
        modified_by=dto['user_id'],
        suggested_order_qty=dto['suggested_order_qty'],
        total_sold=dto['total_sold'],
        weeks_on_hand=dto['weeks_on_hand'],
        turn_rate=dto['turn_rate'],
        to_order_qty=dto['to_order_qty'],
        quantity_available=dto['quantity_available'],
        quantity_incoming=dto['quantity_incoming'],
        incremental_quantity=dto['incremental_quantity'],
        reorder_point=dto['reorder_point'],
        modified_at=datetime.now()
    )


def save_reorder_quantity(store, user_id, sku, quantity, payload):
    ret = False
    message = ''

    if quantity == 0:
            rs = delete_single_record(store, sku)
            
            if rs == 1:
                ret = True
                message = 'Record removed successfully.'

    else:
        query = f"select variants_id, product_id from variants where variants_sku='{sku.strip()}'"
        variants = db.run_query(store['id'], query)
    
        dto = {
            "sku": sku,
            "sale_history_months": payload['sale_history_months'],
            "days_to_replenishment": payload['days_to_replenishment'],
            "quantity": quantity,
            "quantity_available": payload['quantity_available'],
            "quantity_incoming": payload['quantity_incoming'],
            "incremental_quantity": payload['incremental_quantity'],
            "reorder_point": payload['reorder_point'] if 'reorder_point' in payload else 0,
            "user_id": user_id,
            "suggested_order_qty": payload['suggested_order_qty'],
            "total_sold": payload['total_sold'],
            "weeks_on_hand": payload['weeks_on_hand'],
            "turn_rate": payload['turn_rate'],
            "to_order_qty": payload['to_order_qty'],
        }
        if variants and len(variants) > 0:
            variant = variants[0]
            dto["variant_id"] = variant[0]
            dto["product_id"] = variant[1]
            ret = True

        else:
            query = f"select product_id from products where sku='{sku.strip()}'"
            p_id = db.run_query(store['id'], query)

            if p_id:
                dto["product_id"] = p_id[0]
                dto['variant_id'] = -1
                ret = True

        if ret:
            session = db.get_session(store['id'])
            try:
                model = build_reorder_model(dto)
                session.merge(model)
                ret = True
                message = 'Saved successfully.'
            finally:
                session.commit()
                session.close()
        else:
            message = "SKU doesn't exist in Bigcommerce"
        
        return ret, message
    return ret, message

def get_reorders_data(store, body):
    response = {
        "status": 400,
    }
    conn = db.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:           
        limit = int(body.get('limit', 50))
        page = int(body.get('page', 1))
        offset = (page - 1) * limit

        current_month_name = datetime.now().strftime("%b")                       
        user = body.get('user', None)
        
        query_primary_supplier = body.get('primary_supplier', None) 

        if query_primary_supplier and query_primary_supplier.strip() != '':
            query_primary_supplier = query_primary_supplier.split(";")
        else:
            query_primary_supplier = []

        append_query = ''
        sort_array = body['sort_by'].split(
            "/") if body['sort_by'] != '' else []
        
        # search filter append query
        if body['searchKey'] != '' and body['searchValue']:
            if body['searchKey'] in skuvault_catalog:
                append_query = append_query + " AND  sv." + \
                    body['searchKey'] + " ILIKE '%" + \
                    body['searchValue'] + "%'"
            elif body['searchKey'] in reorder_table:
                append_query = append_query + " AND  po." + \
                    body['searchKey'] + " ILIKE '%" + \
                    body['searchValue'] + "%'"
        
        if len(query_primary_supplier) == 0 and user:
            query = f"SELECT suppliers FROM user_supplier_mapping where user_name='{user.strip()}'"
            rs = conn.execute(text(query))

            for _supplier in rs:
                query_primary_supplier.append(_supplier[0])

            # if len(query_primary_supplier) == 0:
            #     response['message'] = 'Not found'
            #     response['data'] = {'data':[]}
            #     response['status'] = 200
            #     return response      
        
        user_email = None
        if user:
            user_doc = new_mongodb.fetch_one_document_from_admin_collection(store['id'], "users", {"name": user.strip()}, {"username": 1})
            if user_doc:
                user_email = user_doc["username"]
        
        if len(query_primary_supplier):
            primary_supplier_query = "sv.primary_supplier = ANY(:suppliers)"
            modified_by_query = "po.modified_by = :user_email"
            append_query = append_query + " AND (" + primary_supplier_query + " OR " + modified_by_query + ")"
        else:
            if user_email:
                append_query = append_query + ' AND po.modified_by = :user_email'

        count_query = f"SELECT COUNT(*) FROM (select distinct(sv.sku), sv.title, sv.cost, sv.brand, sv.classification, sv.primary_supplier, sv.created_date, sv.modified_date, sv.quantity_available, sv.quantity_incoming, sv.incremental_quantity, sv.reorder_point, po.variant_id, po.product_id, po.quantity, po.sale_history_months, po.days_to_replenishment, po.suggested_order_qty, po.total_sold, po.weeks_on_hand, po.turn_rate, po.to_order_qty, po.modified_at, po.modified_by from skuvault_catalog sv right join po_reorders as po ON sv.sku = po.sku where sv.sku = po.sku" + append_query + ") AS subquery"
        count_rs = conn.execute(text(count_query), {"suppliers": query_primary_supplier, "user_email": user_email})
        total_count = int(count_rs.scalar())            

        # sort append query
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            if sort_array[0] in skuvault_catalog:
                append_query = append_query + " ORDER BY sv." + \
                    sort_array[0] + " " + sort_direction
            elif sort_array[0] in reorder_table:
                append_query = append_query + " ORDER BY po." + \
                    sort_array[0] + " " + sort_direction
        
        append_query = append_query + " OFFSET " + \
            str(offset) + " LIMIT " + str(limit)

        query = f"select distinct(sv.sku), sv.title, sv.cost, sv.brand, sv.classification, sv.primary_supplier, sv.created_date, sv.modified_date, sv.quantity_available, sv.quantity_incoming, sv.incremental_quantity, sv.reorder_point, po.variant_id, po.product_id, po.quantity, po.sale_history_months, po.days_to_replenishment, po.suggested_order_qty, po.total_sold, po.weeks_on_hand, po.turn_rate, po.to_order_qty, po.modified_at, po.modified_by from skuvault_catalog sv right join po_reorders as po ON sv.sku = po.sku where sv.sku = po.sku" + append_query
        rs = conn.execute(text(query), {"suppliers": query_primary_supplier, "user_email": user_email})
        columns = [col.strip() for col in query.split("select", 1)
                    [1].split("from", 1)[0].split(",")]
        replacements = ['distinct(sv.', ')', 'sv.',
                        'po.quantity as ', 'po.']
        for i, col in enumerate(columns):
            for replace_str in replacements:
                col = col.replace(replace_str, '')
            columns[i] = col.strip()

        results = conver_to_json(rs, columns)  
        for record in results:
            # Apply the convert_to_timestamp function on created_date and modified_date fields
            record['created_date'] = convert_to_timestamp(record.get('created_date'))
            record['modified_date'] = convert_to_timestamp(record.get('modified_date')) 
            record['modified_at'] = convert_to_timestamp(record.get('modified_at'))

        sku_string = ''
        for res in results:
            sku_string = sku_string + "'" + res['sku'] + "', "
        sku_string = sku_string.rstrip(', ')
        test_month_names = {}
        if sku_string != '':
            monthly_result, month_names, day_difference = get_six_month_data_using_sku(
                sku_string, conn, 6, True)
            test_month_names = month_names

            # get last 30 days data for total sold
            monthly_result_30, month_names_30, day_difference_30 = get_six_month_data_using_sku(sku_string, conn, 1, False)   
            
            for res in results:
                res['total_sold'] = 0
                res['total_cost'] = 0
                res['total_sold_30'] = 0
                res['sale_history_months'] = 6
                res['days_to_replenishment'] = 30
                for month_key, month_value in month_names.items():
                    res[month_key] = 'NA'
                for mdata in monthly_result:
                    if str(mdata[0]) == str(res['sku']):
                        for month_key, month_value in month_names.items():
                            if month_value == mdata[3]:
                                res[month_key] = mdata[2]
                                if month_value != current_month_name:
                                    res['total_sold'] = res['total_sold'] + mdata[2]
                
                for mdata_30 in monthly_result_30:
                    if str(mdata_30[0]) == str(res['sku']):
                        for month_key, month_value in month_names_30.items():
                            if month_value == mdata_30[3]:
                                res['total_sold_30'] = res['total_sold_30'] + mdata_30[2] 

            for res in results:
                future_sales = res['total_sold_30']
                res['suggested_order_qty'] = round((
                    future_sales - res['quantity_available']) - res['quantity_incoming'])
                res['to_order_qty'] = res['incremental_quantity'] if res['suggested_order_qty'] > 0 else int(
                    res['suggested_order_qty'])
                res['turn_rate'] = (
                    ((res['total_sold'] / res['quantity_available'] if res['quantity_available'] != 0 else 1) * 365) / day_difference)
                res['weeks_on_hand'] = (
                    52 / res['turn_rate'] if res['turn_rate'] != 0 else 1)
                res['total_cost'] = res['quantity'] * res['cost']

            if len(sort_array):
                if sort_array[0] in month_filter_array:
                    if sort_array[1] == '1':
                        results = sorted(
                            results, key=lambda x: custom_sort(x[sort_array[0]]))
                    else:
                        results = sorted(
                            results, key=lambda x: custom_sort(x[sort_array[0]]), reverse=True)            
        

        paginated_rows, current_page, total_pages, total_items = paginate_data(
            total_count, results, page, limit)

        data = new_utils.calculate_pagination(
            paginated_rows, current_page, limit, total_items)

        data['meta']['month_rows'] = test_month_names
        response['message'] = 'data retrived successfully'
        response['data'] = data
        response['status'] = 200
    except Exception as e:
        logger.error(traceback.format_exc())
    finally:
        conn.close()

    return response

def get_reorders_data_csv(store, body):
    response = {
        "status": 400,
    }
    conn = db.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        user = body.get('user', None)
        
        query_primary_supplier = body.get('primary_supplier', None) 

        if query_primary_supplier and query_primary_supplier.strip() != '':
            query_primary_supplier = query_primary_supplier.split(";")
        else:
            query_primary_supplier = []

        append_query = ''
        # search filter append query
        if body['searchKey'] != '' and body['searchValue']:
            if body['searchKey'] in skuvault_catalog:
                append_query = append_query + " AND  sv." + \
                    body['searchKey'] + " LIKE '%" + \
                    body['searchValue'] + "%'"
            elif body['searchKey'] in reorder_table:
                append_query = append_query + " AND  po." + \
                    body['searchKey'] + " LIKE '%" + \
                    body['searchValue'] + "%'"

        if len(query_primary_supplier) == 0 and user:
            query = f"SELECT suppliers FROM user_supplier_mapping where user_name='{user.strip()}'"
            rs = conn.execute(text(query))

            for _supplier in rs:
                query_primary_supplier.append(_supplier[0])

            # if len(query_primary_supplier) == 0:
            #     response['message'] = 'Not found'
            #     response['data'] = {'data':[]}
            #     response['status'] = 200
            #     return response      

        # if len(query_primary_supplier):
        #     append_query = append_query + ' AND sv.primary_supplier = ANY(:suppliers)'
        user_email = None
        if user:
            user_doc = new_mongodb.fetch_one_document_from_admin_collection(store['id'], "users", {"name": user.strip()}, {"username": 1})
            if user_doc:
                user_email = user_doc["username"]
        
        if len(query_primary_supplier):
            append_query = append_query + ' AND sv.primary_supplier = ANY(:suppliers)'
            append_query = append_query + ' OR po.modified_by = :user_email'
        else:
            if user_email:
                append_query = append_query + ' AND po.modified_by = :user_email'
            
        query = f"select distinct(sv.sku), sv.part_number, sv.title, sv.cost, sv.brand, sv.classification, sv.primary_supplier, sv.created_date, sv.modified_date, po.variant_id, po.product_id, po.quantity, po.sale_history_months, po.days_to_replenishment, po.suggested_order_qty, po.total_sold, po.weeks_on_hand, po.turn_rate, po.to_order_qty, po.quantity_available, po.quantity_incoming, po.incremental_quantity, po.reorder_point, po.modified_at, po.modified_by from skuvault_catalog sv right join po_reorders as po ON sv.sku = po.sku where sv.sku = po.sku" + append_query + " ORDER BY sv.primary_supplier ASC"
        rs = conn.execute(text(query), {"suppliers": query_primary_supplier, "user_email": user_email})
        columns = [col.strip() for col in query.split("select", 1)
                    [1].split("from", 1)[0].split(",")]
        replacements = ['distinct(sv.', ')', 'sv.',
                        'po.quantity as ', 'po.']
        for i, col in enumerate(columns):
            for replace_str in replacements:
                col = col.replace(replace_str, '')
            columns[i] = col.strip()

        results = conver_to_json(rs, columns)

        # get skus from results...
        sku_string = ''
        sale_history_months = 6
        current_month_name = datetime.now().strftime("%b")

        for res in results:
            sku_string = sku_string + "'" + res['sku'] + "', "
        sku_string = sku_string.rstrip(', ')

        if sku_string != '':
            monthly_result, month_names, day_difference = get_six_month_data_using_sku(sku_string, conn, sale_history_months, True)

            # get last 30 days data for total sold
            monthly_result_30, month_names_30, day_difference_30 = get_six_month_data_using_sku(sku_string, conn, 1, False) 

            for res in results:
                res['total_sold_30'] = 0
                for month_key, month_value in month_names.items():
                    res[month_value] = 'NA'
                
                for mdata in monthly_result:
                    if str(mdata[0]) == str(res['sku']):
                        for month_key, month_value in month_names.items():
                            if month_value == mdata[3]:
                                res[month_value] = mdata[2]
                                if month_value != current_month_name:
                                    res['total_sold'] = res['total_sold'] + mdata[2]

                for mdata_30 in monthly_result_30:
                    if str(mdata_30[0]) == str(res['sku']):
                        for month_key, month_value in month_names_30.items():
                            if month_value == mdata_30[3]:
                                res['total_sold_30'] = res['total_sold_30'] + mdata_30[2] 

        # Convert keys to PascalCase with spaces and data convertion
        new_data = []
        for item in results:
            obj = {}

            obj = {
                'Item Search Term': item['sku'],
                'Part Number': item['part_number'],
                'Title': item['title'],
                'Supplier Company Name': item['primary_supplier'],
                'Item Ordered Quantity': item['quantity'],
                'Item Cost': item['cost'],
                'Total Cost': item['quantity'] * item['cost'],
                'Available': item['quantity_available'],
                'Incoming': item['quantity_incoming'],
                'Total Sold (30)': item['total_sold_30'],
                'Reorder Points': item['reorder_point'],
                'Incremental Quantity': item['incremental_quantity'],
                'Suggested Order Quantity': round(item['suggested_order_qty'], 2),
                'To Order Quantity': item['to_order_qty']
            }

            # Include the 'month' key only if it exists in the original item.
            for month in month_array:
                if month in item:
                    obj[month] = item[month]
            
            obj['Sale History Months'] = item['sale_history_months']
            obj['Days To Replenishment'] = item['days_to_replenishment']
            obj['Brand'] = item['brand']
            obj['Classification'] = item['classification']
            obj['Weeks On hand'] = round(item['weeks_on_hand'], 2)
            obj['Turn Rate'] = round(item['turn_rate'], 2)
            obj['Modified By'] = item['modified_by']
            obj['Modified Date'] = convert_to_timestamp(item['modified_at'])

            new_data.append(obj)

        response['message'] = 'data retrived successfully'
        response['data'] = new_data
        response['status'] = 200
    finally:
        conn.close()
    return response


def delete_single_record(store, sku):
    conn = db.get_connection(store['id'])
    try:
        query = "DELETE FROM po_reorders WHERE sku = '" + str(sku.strip()) + "'"
        rs = conn.execute(text(query))
        conn.commit()
        return rs.rowcount
    finally:
        conn.close()

def clear_reorders(store):
    db.truncate_table(store['id'], db.DBTables.po_reorders_table)

def clear_filtered_reorders(store, body):
    conn = db.get_connection(store['id'], read_only=False)
    try:
        user = body.get('user', None)
        query_primary_supplier = body.get('primary_supplier', None)
        search_key = body.get('searchKey', '')
        search_value = body.get('searchValue', '')

        query_parts = ["1=1"]
        query_params = {}

        if query_primary_supplier and query_primary_supplier.strip() != '':
            suppliers = query_primary_supplier.split(";")
            query_parts.append("sv.primary_supplier = ANY(:suppliers)")
            query_params['suppliers'] = suppliers

        if user:
            # get user-mapped suppliers if no supplier filter is passed
            if not query_primary_supplier:
                suppliers = []
                rs = conn.execute(text("SELECT suppliers FROM user_supplier_mapping WHERE user_name=:user"), {"user": user})
                for row in rs:
                    suppliers.append(row[0])
                if suppliers:
                    query_parts.append("sv.primary_supplier = ANY(:suppliers)")
                    query_params['suppliers'] = suppliers
            user_doc = new_mongodb.fetch_one_document_from_admin_collection(store['id'], "users", {"name": user.strip()}, {"username": 1})
            if user_doc:
                query_parts.append("po_reorders.modified_by = :user")
                query_params['user'] = user_doc["username"]

        if search_key and search_value:
            if search_key in skuvault_catalog:
                query_parts.append(f"sv.{search_key} ILIKE :search_value")
            elif search_key in reorder_table:
                query_parts.append(f"po.{search_key} ILIKE :search_value")
            query_params['search_value'] = f"%{search_value}%"

        where_clause = " AND ".join(query_parts)
        delete_query = f"""
            DELETE FROM po_reorders
            USING skuvault_catalog sv
            WHERE sv.sku = po_reorders.sku AND {where_clause}
        """
        conn.execute(text(delete_query), query_params)
        conn.commit()
    finally:
        conn.close()

def custom_sort(val):
    if val == 'NA':
        return float('inf')  # Use a large value (positive infinity) for '-'
    try:
        return int(val)  # Convert other numeric values to integers
    except ValueError:
        return val  # For non-numeric values, return the original string

