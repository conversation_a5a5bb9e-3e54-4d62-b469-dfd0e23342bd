

from datetime import datetime
from bson import ObjectId
from mongo_db import user_db
from new_mongodb import StoreDBCollections, customer_db, get_store_db_client_for_store_id, loyalty_points_db
import new_mongodb
import new_utils
from utils.common import calculatePaginationData, get_paginated_records, parse_json, processDocument, processList
from fields.loyalty_app_fields import reward_fields

def _get_paginated_records(store, payload, fields, db, flag):

        def create_reg_ex_query(filterBy, value):
            regex = "^" + value + ".*"

            return {
                filterBy: {
                    "$regex": regex,
                    "$options": 'i'
                }
            }

        limit = int(payload["limit"]) if payload.__contains__("limit") else 10
        page = int(payload["page"]) if payload.__contains__("page") else 1
        skips = payload['skips'] if payload.__contains__('skips') else 0

        query = create_reg_ex_query(
            payload["filterBy"], payload['filterValue'])

        # Calculate number of records to skip ...
        skips = limit * (page - 1)

        # Sort by date item last added ...

        sort_by = str(payload['filterBy']) if payload.__contains__(
            "filterBy") else "date_created"
        data = []
        document_length = any
        if flag:
            if payload['table_collection'] in db.list_collection_names():
                collection = db[payload['table_collection']]
                sort_by = payload['sortBy']
                if (not sort_by):
                    sort_by = ' '

                # sort_direction = 1 if payload['sortDirection'] else -1
                sort_direction = payload['sortDirection']

                if int(sort_direction) == 1:
                    documents = list(collection.find(query, fields).sort(
                        sort_by, 1).skip(skips).limit(limit))
                else:
                    documents = list(collection.find(query, fields).sort(
                        sort_by, -1).skip(skips).limit(limit))

                for doc in documents:
                    doc['_id'] = str(doc['_id'])

                data = documents
                document_length = collection.count_documents(query)
        else:
            sort_by = "created_at" if sort_by == "coupon_value" else sort_by
            rewards_collection = new_mongodb.StoreDBCollections.LOYALTY_REWARDS_COLLECTION
            data = new_mongodb.fetchall_documents_from_storefront_collection(store['id'], rewards_collection, query, fields) \
            .sort(sort_by, -1).skip(skips).limit(limit)
    
            # data = self.repository.find(query, fields).sort(
            #     sort_by, -1).skip(skips).limit(limit)
            # document_length = self.repository.count_documents(query)
            document_length = new_mongodb.count_documents_storefront_collection(store['id'], rewards_collection, query)

        # ProcessList ...
        data = processList(data)
        return parse_json(data), document_length, page, limit


def get_all_loyalty_list(store, payload):   
        response = {
            "status": 400
        }          
        customers = {}  
        customers = customer_db.fetch_all_customer_with_points(store)               
        if customers:
            customer_ids = []
            for customer in customers:
                customer_ids.append(customer['customer_id'])                            
                    
            customer_data = customer_db.get_customers_using_id(store, customer_ids)  
            if customer_data:
                data_array = processList(customer_data)               
                limit = int(payload["limit"]) if payload.__contains__("limit") else 1
                page = int(payload["page"]) if payload.__contains__("page") else 1
                
                customers = customer_db.fetch_all_customer_with_points(store)                 
                for customer in customers:                    
                    for c_data in data_array:                                                                
                        if int(c_data['id']) == customer['customer_id']:                            
                            c_data['loyalty_points'] = customer['loyalty_points'] if 'loyalty_points' in customer else 0
                            c_data['reward_count'] = len(customer['coupons'])
                            c_data['status'] = ''

                #filter the data array
                filtered_data = data_array
                if payload['filter'] != '':               
                    filtered_data = [
                        item for item in data_array
                        if payload['filter'].lower().strip() in item["email"].lower().strip() or payload['filter'].lower().strip() in item["name"].lower().strip()
                    ]

                # added latest reward history date in data array
                filtered_data = _get_latest_reward_history(store, filtered_data)
                
                # sort the data array after filter
                filtered_data = sorted(filtered_data, key=lambda x: x[payload['sort_by']], reverse=payload['sort_order'] == "desc")

                start_limit = ((page - 1) * limit) 
                end_limit = start_limit + limit
                converted_array = filtered_data[start_limit:end_limit]                              
                data = new_utils.calculate_pagination(converted_array, page, limit, len(filtered_data))            

                response['data'] = data
                response['status'] = 200    
            else:
                response['message'] = "No data found."
                response['status'] = 404
        else:
                response['message'] = "No data found."
                response['status'] = 404

        return response 

def _get_latest_reward_history(store, customer_data):
        customer_ids = []
        for customer in customer_data:
            customer_ids.append(int(customer['id']))

        latest_data = customer_db.get_customers_latest_history(store, customer_ids) 
        for customer in customer_data:
            for latest in latest_data:
                if int(latest['_id']) == int(customer['id']):
                    customer['reward_date'] = latest['latest_entry']['created_at']        

        return customer_data

def get_all_rewards(store, payload):
        payload['filterBy'] = ['reward_points', 'coupon_value']
        db_client = get_store_db_client_for_store_id(store['id'])
        rewards, total_data_length, page, limit = new_utils.get_paginated_records_updated(db_client, StoreDBCollections.LOYALTY_REWARDS_COLLECTION, payload, reward_fields, '')
        # include pagination data in response ...
        data = new_utils.calculate_pagination(rewards, page, limit, total_data_length)
        return data

def create_reward(store,body):
        response = {
            "status": 400
        }
        user = _get_user_by_username(body['created_by'])
        points = body['reward_points']
        c_value = body['coupon_value']
        if c_value:
            c_value = "$" + c_value.strip() + " Off"
        else:
            response['message'] = "Coupon value can't be empty"
            response['status'] = 204

        isUniqueReward = _check_for_unique_reward(store, int(points.strip()), c_value, '')
        if isUniqueReward['status'] == True:
            created_by = {
                "user_id": user['id'],
                "user_name": user['name']
            }
            body["created_at"] = int(datetime.utcnow().timestamp())
            body["updated_at"] = int(datetime.utcnow().timestamp())
            body["created_by"] = created_by
            body["updated_by"] = created_by
            body['reward_points'] = int(points.strip())
            body['coupon_value'] = c_value

            id = loyalty_points_db.create(store, body)
            response['message'] = "Reward created successfully"
            response['status'] = 200
        else:
            response['status'] = 409
            response['message'] = isUniqueReward['reasion']

        return response

def _check_for_unique_reward(store, reward_points, coupon_value, r_id):
        result = {
            "status": False
        }
        reward_points = reward_points
        coupon_value = coupon_value.strip()
        rewards = loyalty_points_db.get_rewards(store)
        rewards = parse_json(processList(rewards))
        for reward in rewards:
            if reward['status'] == 'active' and reward['id'] != r_id:
                if reward['coupon_value'] == coupon_value:
                    result['status'] = False
                    result['reasion'] = 'The coupon code with the same value already exist'
                    return result
                elif reward['reward_points'] == reward_points:
                    result['status'] = False
                    result['reasion'] = 'The coupon code with the same points already exist'
                    return result
        result['status'] = True
        result['reasion'] = 'All good'
        return result

def _get_user_by_username(username):
        res = user_db.fetch_user_by_username(username)
        res['id'] = str(res['_id'])

        del res["yoyo"]
        del res['_id']
        del res['created_at']
        del res['updated_at']

        return res

def update_reward(store, body, reward_id=None):
    response = {
        "status": 400
    }
    user = _get_user_by_username(body['updated_by'])
    points = body['reward_points']
    points = points.replace(",", "")
    c_value = body['coupon_value'].strip()

    isUniqueReward = _check_for_unique_reward(store, int(points.strip()), c_value, reward_id)
    if isUniqueReward['status'] == True:
        updated_by = {
            "user_id": user['id'],
            "user_name": user['name']
        }
        update_obj={
                    "status": body['status'],
                    "updated_at":  int(datetime.utcnow().timestamp()),
                    "updated_by": updated_by
                }
        id = loyalty_points_db.update(store, {"_id": ObjectId(str(reward_id))}, {"$set": update_obj})
        
        response['message'] = "Reward updated successfully"
        response['status'] = 200
    else:
        response['status'] = 409
        response['message'] = isUniqueReward['reasion']

    return response

def get_welcome_point(store_id):
    response = {
            "status": 400
        }
    try:
        mongo_db = new_mongodb.get_admin_db_client_for_store_id(store_id)

        # Get the first document from the collection
        result = mongo_db["store_info"].find_one({"type": "welcome_loyalty_points"})
        result = processDocument(result)

        if result and "welcome_points" in result:
            response['data'] = result
            response['status'] = 200
        else:
            response['message'] = "Welcome point not data found."
            response['status'] = 404

    except Exception as e:
        response['message'] = f"Failed to fetch welcome point: {str(e)}"
        response['status'] = 500
    
    return response

def update_welcome_point(store_id, payload): 
    welcome_points = payload.get("welcome_points")

    if welcome_points is None:
        return {
            "status": 400,
            "message": "Missing required field: welcome_point"
        }
    
    if not isinstance(welcome_points, int):
        return {
            "status": 400,
            "message": "welcome_point must be an integer"
        }

    try:
        mongo_db = new_mongodb.get_admin_db_client_for_store_id(store_id)

        update_fields = {
            "welcome_points": welcome_points
        }

        result = mongo_db["store_info"].update_one(
            {"type": "welcome_loyalty_points"}, {"$set": update_fields},
            upsert=True
        )

        return {
            "status": 200,
            "message": "Welcome point updated successfully."
        }

    except Exception as e:
        return {
            "status": 500,
            "message": f"Failed to update welcome point: {str(e)}"
        }