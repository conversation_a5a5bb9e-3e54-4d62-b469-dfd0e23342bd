from datetime import datetime
from decimal import Decimal
import json
from sqlalchemy import Boolean, Column,  DateTime,String, Integer, delete, text, update
from sqlalchemy.dialects.postgresql import insert
from mongo_db import user_db
import pg_db as db
from utils.common import parse_json


class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        return super().default(obj)
    
class MessageSchema(db.Base):
    __tablename__ = db.chat_messages_table

    id = Column(Integer, primary_key=True)
    sender_user_id = Column(Integer)
    vender_user_id = Column(Integer)
    parent_chat_id = Column(Integer)
    root_chat_id = Column(Integer)
    message_type = Column(String)
    message = Column(String)
    is_archived= Column(Boolean)
    is_deleted =Column(Boolean)
    is_search_found=Column(Boolean)
    parent_message=Column(String)
    created_at = Column(Integer)

    @classmethod
    def add_chat_messages(cls, data, session=None):
       stmt = insert(MessageSchema).on_conflict_do_nothing()
       values = []
       values.append({
        "sender_user_id":data['sender_user_id'],
        "vender_user_id":data['vender_user_id'],
        "parent_chat_id":data['parent_chat_id'],
        "root_chat_id":data['root_chat_id'],
        "message_type":data['type'],
        "message":data['message'],
        "is_archived":data['is_archived'],
        "is_deleted":data['is_deleted'],
        "is_search_found":False,
        "parent_message":data['parent_message'],
        "created_at":int(datetime.utcnow().timestamp()) ,
       })
       res=db.execute_stmt(stmt, values, session)
       return values
    
    @classmethod
    def has_parent(cls,parent_chat_id, session=None):
        with db.get_connection() as conn:
            query = text(
                f"SELECT parent_chat_id FROM {db.chat_messages_table} WHERE id = :id"
               
            )
            user = conn.execute(query.params(id=parent_chat_id)).fetchone()
            if user is not None:
               for record in user:
                 return record
            return None
   
    @classmethod
    def get_message_list(cls,payload,vender_user_id, session=None):
            result=get_paginated_records_updated(payload,vender_user_id)

            listOfChatUser=[]
            for record in result:
                 chatUser={}
                 if record[1] is not None:
                    decimal_value1 = Decimal(record[1])
                    hexval=int(decimal_value1)
                    converted_hex_string = hex(hexval)[2:]
                 else:
                    converted_hex_string=""
                 decimal_value2 = Decimal(record[2])
                 if record[3] is not None:
                    decimal_value3=Decimal(record[3])
                    parent_id=int(decimal_value3)
                 else:
                    decimal_value3=None
                    parent_id=None
                 if record[4] is not None:
                    decimal_value4=Decimal(record[4])
                    root_id=int(decimal_value4)
                 else:
                    decimal_value4=None
                    root_id=None
                 chatUser['id']=record[0]
                 chatUser["sender_user_id"]=converted_hex_string
                 chatUser["vender_user_id"]=int(decimal_value2)
                 chatUser['parent_chat_id']=parent_id
                 chatUser['root_chat_id']=root_id
                 chatUser['message_type']=record[5]
                 chatUser['message']=record[6]
                 chatUser['is_archived']=record[7]
                 chatUser['is_deleted']=record[8]
                 chatUser['is_search_found']=record[9]
                 chatUser['parent_message']=record[10]
                 chatUser['created_at']=record[11]
                 listOfChatUser.append(chatUser)
            
            return listOfChatUser
    
    @classmethod
    def get_messages_count(cls,vender_user_id, session=None):
      with db.get_connection() as conn:
        query = text(
            f"SELECT COUNT(*) FROM {db.chat_messages_table} where vender_user_id= :vender_user_id"
        )
        total_records = conn.execute(query.params(vender_user_id=vender_user_id)).scalar()
        return total_records  

    @classmethod
    def get_message_detail(cls,payload,message_id, session=None):
        with db.get_connection() as conn:
            query = text(
                f"SELECT * FROM {db.chat_messages_table} WHERE id = :id"
               
            )
            user = conn.execute(query.params(id=message_id)).fetchone()
            record=user
            chatUser={}
            if record is not None:
               if record[1] is not None:
                  decimal_value1 = Decimal(record[1])
                  hexval=int(decimal_value1)
                  converted_hex_string = hex(hexval)[2:]
               else:
                  converted_hex_string=""
               decimal_value2 = Decimal(record[2])
               if record[3] is not None:
                    decimal_value3=Decimal(record[3])
                    parent_id=int(decimal_value3)
               else:
                    decimal_value3=None
                    parent_id=None
               if record[4] is not None:
                    decimal_value4=Decimal(record[4])
                    root_id=int(decimal_value4)
               else:
                    decimal_value4=None
                    root_id=None
               chatUser['id']=record[0]
               chatUser["sender_user_id"]=converted_hex_string
               chatUser["vender_user_id"]=int(decimal_value2)
               chatUser['parent_chat_id']=parent_id
               chatUser['root_chat_id']=root_id
               chatUser['message_type']=record[5]
               chatUser['message']=record[6]
               chatUser['is_archived']=record[7]
               chatUser['is_deleted']=record[8]
               chatUser['is_search_found']=record[9]
               chatUser['parent_message']=record[10]
               chatUser['created_at']=record[11]
                 
        return chatUser

class ChatUserSchema(db.Base):
    __tablename__ = db.chat_users_table

    id = Column(Integer, primary_key=True)
    whatsapp_user_id = Column(Integer)
    first_name=Column(String)
    last_name=Column(String)
    display_name=Column(String)
    mobile_number=Column(Integer)
    is_admin_user=Column(Boolean)
    admin_user_id=Column(Integer)
    created_at = Column(Integer)
    modified_at = Column(Integer)

    @classmethod
    def add_user(cls,data, session=None):
        stmt = insert(ChatUserSchema).on_conflict_do_nothing()
        values = []
        values.append({
            "whatsapp_user_id": data['wa_id'], 
            "first_name": data['name'],
            "last_name": data['name'],
            "display_name": data['name'],
            "mobile_number": data['mobile'],
            "is_admin_user": False,
            "admin_user_id": 122,
            "created_at": int(datetime.utcnow().timestamp()),
            "modified_at": int(datetime.utcnow().timestamp())
            })
        db.execute_stmt(stmt, values, session)
        return values
    
    @classmethod
    def get_user(cls,whatsapp_user_id, session=None):
        with db.get_connection() as conn:
            query = text(
                f"SELECT * FROM {db.chat_users_table} WHERE whatsapp_user_id = :whatsapp_user_id"
               
            )
            user = conn.execute(query.params(whatsapp_user_id=whatsapp_user_id))
            result=user
            exists = result.scalar()
            return exists
        
    @classmethod
    def get_vender_by_id(cls,vender_id, session=None):
        with db.get_connection() as conn:
            query = text(
                f"SELECT * FROM {db.chat_users_table} WHERE whatsapp_user_id = :whatsapp_user_id"
               
            )
            user = conn.execute(query.params(whatsapp_user_id=vender_id))
            result=user.fetchall()
            queryadmin= text( f"SELECT admin_user_id FROM {db.chat_user_mapping_table} WHERE vender_user_id = :vender_user_id")
            user1 = conn.execute(queryadmin.params(vender_user_id=vender_id))
            admin=user1.fetchone()
            converted_hex_string=""
            if admin[0] is not None:
               hexval=int(admin[0])
               converted_hex_string = hex(hexval)[2:]
            userDataList=[]
            for record in result:
                userData={}
                decimal_value1 = Decimal(record[1])
                decimal_value2= Decimal(record[5])
                vender_id = int(decimal_value1)
                mobile_no= int(decimal_value2) 
                userData['id']=vender_id
                userData['first_name']=record[2]
                userData['last_name']=record[3]
                userData['mobile_number']=mobile_no
                userData['admin_user_id']=converted_hex_string
                userDataList.append(userData)
            return userDataList
        
       
class ChatUserMappingSchema(db.Base):
    __tablename__ = db.chat_user_mapping_table

    id = Column(Integer, primary_key=True)
    vender_user_id = Column(Integer)
    admin_user_id = Column(Integer)
    created_by_id = Column(Integer)
    received_message_count=Column(Integer)
    created_at = Column(Integer)
    modified_at = Column(Integer)
    message_created_at=Column(Integer)

    @classmethod
    def add_chat_user_mapping(cls, data, session=None):
        stmt = insert(ChatUserMappingSchema).on_conflict_do_nothing()
        values = []
        values.append({ 
            "vender_user_id" : data['wa_id'],
            "admin_user_id" : None,
            "created_by_id" : data['wa_id'],
            "received_message_count":0,
            "created_at" :int(datetime.utcnow().timestamp()),
            "modified_at" :int(datetime.utcnow().timestamp()),
            "message_created_at":int(datetime.utcnow().timestamp()),
            })
        db.execute_stmt(stmt, values, session)
        return values
   
    @classmethod
    def add_received_messages_count(cls,vender_user_id, session=None):
      with db.get_connection() as conn:
            query = text(
               f"UPDATE {db.chat_user_mapping_table} SET received_message_count = received_message_count + 1 WHERE vender_user_id = :vender_user_id"
            )
            conn.execute(query.params(vender_user_id=vender_user_id))
            conn.commit()

            count=text(
              f"SELECT received_message_count from {db.chat_user_mapping_table} where vender_user_id = :vender_user_id"  
            )
            conn.execute(count.params(vender_user_id=vender_user_id))
    @classmethod
    def send_received_messages_count(cls,vender_user_id, session=None):
      with db.get_connection() as conn:
            count=text(
              f"SELECT received_message_count from {db.chat_user_mapping_table} where vender_user_id = :vender_user_id"  
            )
            total_count=conn.execute(count.params(vender_user_id=vender_user_id)).scalar() 
            return total_count      

    @classmethod
    def set_received_messages_count_zero(cls,vender_user_id, session=None):
      with db.get_connection() as conn:
            query = text(
               f"UPDATE {db.chat_user_mapping_table} SET received_message_count = 0 WHERE vender_user_id = :vender_user_id"
            )
            conn.execute(query.params(vender_user_id=vender_user_id))
            conn.commit()  

    @classmethod
    def get_chat_user_mapping_and_update(cls,data, session=None):
        vender_user_id=data['vendor_user_id']
        admin_user_id=int(data['admin_user_id'],16)
        with db.get_connection() as conn:
            query = text(
               f"UPDATE {db.chat_user_mapping_table} SET admin_user_id = :admin_user_id WHERE vender_user_id = :vender_user_id"
            )
            conn.execute(query.params(admin_user_id=admin_user_id, vender_user_id=vender_user_id))
            conn.commit() 

    @classmethod
    def add_chat_user_mapping_message_time(cls,data, session=None):
        vender_user_id=data['vender_user_id']
        # admin_user_id=int(data['admin_user_id'],16)
        with db.get_connection() as conn:
            query = text(
               f"UPDATE {db.chat_user_mapping_table} SET message_created_at = :message_created_at WHERE vender_user_id = :vender_user_id"
            )
            conn.execute(query.params(message_created_at=int(datetime.utcnow().timestamp()), vender_user_id=vender_user_id))
            conn.commit() 

    @classmethod
    def get_chat_user_count(cls, session=None):
      with db.get_connection() as conn:
        query = text(
            f"SELECT COUNT(*) FROM {db.chat_user_mapping_table}"
        )
        total_records = conn.execute(query).scalar()
        return total_records       
   
    @classmethod
    def get_all_chat_user_mapping(cls,payload, session=None):
        limit = int(payload.get("limit", 10))
        page = int(payload.get("page", 1))
        offset = limit * (page - 1)
        userName=None
        result=[]
        listOfChatUser=[]
        if payload['filterBy'] or payload['filterValue']:
            if payload['filterBy']:
               userID=int(payload['filterBy'],16)

            if payload['filterValue']:
                vendorName=payload['filterValue']
                with db.get_connection() as conn:
                    query = text(
                f"SELECT whatsapp_user_id FROM {db.chat_users_table} WHERE LOWER(first_name) LIKE LOWER('%{vendorName}%')"
               )
                    user_mapping_data = conn.execute(query)
                    result = user_mapping_data.fetchall()
                    listOfId=[]
                    for record in result:
                        decimal_value1 = Decimal(record[0])
                        vender_id = int(decimal_value1)
                        listOfId.append(vender_id)
                    id_string = ', '.join(map(str, listOfId))
                    if id_string:
                        if payload['filterBy']:
                           query1=text(f"SELECT * FROM {db.chat_user_mapping_table} WHERE vender_user_id IN ({id_string}) AND admin_user_id = {userID} LIMIT {limit} OFFSET {offset}")
                        else:
                           query1=text(f"SELECT * FROM {db.chat_user_mapping_table} WHERE vender_user_id IN ({id_string}) LIMIT {limit} OFFSET {offset}")
                        result=conn.execute(query1).fetchall()
            else:
                with db.get_connection() as conn:
                    query = text(
                f"SELECT * FROM {db.chat_user_mapping_table} WHERE admin_user_id = :admin_user_id LIMIT {limit} OFFSET {offset}"
               )
                    user_mapping_data = conn.execute(query.params(admin_user_id=userID))
            
                    result = user_mapping_data.fetchall()
        else:
            with db.get_connection() as conn:
                query = text(f"SELECT * FROM {db.chat_user_mapping_table} ORDER BY message_created_at DESC NULLS LAST LIMIT {limit} OFFSET {offset}")
                user_mapping_data = conn.execute(query)
                result=user_mapping_data.fetchall()
        
        for record in result:        
            chatUser={}

            ### get vender name
            with db.get_connection() as conn:
                query = text(
                   f"SELECT first_name FROM {db.chat_users_table} WHERE whatsapp_user_id = :whatsapp_user_id"
                              )
                user = conn.execute(query.params(whatsapp_user_id=record[1]))
                vender_name=user.fetchone()
                query_message=text(
                                 f"SELECT message, created_at FROM {db.chat_messages_table} WHERE vender_user_id = :vender_user_id ORDER BY created_at DESC LIMIT 1"
                             )
                message=conn.execute(query_message.params(vender_user_id=record[1])).fetchone()
                last_message=message[0]
                last_message_time=message[1]  

            ### get user name
            userName=""
            if record[2] is not None:
                decimal_value = Decimal(record[2])
                integer_value = int(decimal_value)
                hexValue = hex(integer_value)[2:]
                userList=user_db.fetch_user_by_userID(hexValue)
                userName=userList['name']
                        
            decimal_value1 = Decimal(record[1])
            decimal_value2=None
            if record[2] is not None:
                decimal_value2= Decimal(record[2])
                admin_id = int(decimal_value2)
            else:
                admin_id=None
            decimal_value3 = Decimal(record[3])
            vender_id = int(decimal_value1)          
            created_id = int(decimal_value3)

            chatUser["vender_name"]=vender_name[0]
            chatUser["user_name"]=userName
            chatUser['vender_user_id']=vender_id
            chatUser['admin_user_id']=admin_id
            chatUser['created_by_id']=created_id
            chatUser['last_message']=last_message
            chatUser['received_message_count']=record[4]
            chatUser['created_at']=record[5]
            chatUser['modified_at']=record[6]
            chatUser['last_message_time']=record[7]
            listOfChatUser.append(chatUser) 

        return listOfChatUser

def get_paginated_records_updated(payload, vender_user_id):
        limit = int(payload.get("limit", 10))
        page = int(payload.get("page", 1))
        offset = limit * (page - 1)
        sort_by='created_at'
        sort_order='DESC'
        with db.get_connection() as conn:
            filter_value = payload.get("filter")
            if filter_value:
                update_query = text(f"""
                UPDATE {db.chat_messages_table}
                SET is_search_found = (message ILIKE :filter_value)
                WHERE vender_user_id = :vender_user_id
                 """)
                conn.execute(update_query.params(vender_user_id=vender_user_id, filter_value=f"%{filter_value}%"))
                conn.commit()
            else:
                update_query = text(f"""
                UPDATE {db.chat_messages_table}
                SET is_search_found = false
                WHERE vender_user_id = :vender_user_id
                 """)
                conn.execute(update_query.params(vender_user_id=vender_user_id))
                conn.commit()
              
            query = text(f"""
            SELECT *
            FROM {db.chat_messages_table} WHERE vender_user_id = :vender_user_id
            ORDER BY {sort_by} {sort_order}
            LIMIT {limit}
            OFFSET {offset}
             """)
            cursor = conn.execute(query.params(vender_user_id=vender_user_id))
            data = cursor.fetchall()
        return data