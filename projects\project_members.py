from projects import check_project_access
from sqlalchemy import text
import pg_db
from mongo_db import user_db
import traceback
import logging
from utils.common import calculatePaginationData
from datetime import datetime
from utils.common import convert_to_timestamp

logger = logging.getLogger()

def _fetch_project_members(conn, search_value, project_id, sort_array=[]):
    query = text(
        f"""SELECT id, username, is_owner, created_at,
            (SELECT COUNT(*)
                    FROM {pg_db.project_cards} apc
                    WHERE apc.assigned_to = pa.username
                    and apc.project_id = pa.project_id) as card_count, status
            FROM {pg_db.project_access} pa
            WHERE project_id = :project_id and pa.status = 'active'
        """
    )
    if len(sort_array):
        sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
        if sort_array[0] in ["card_count", "created_at"]: 
            query = text(query.text + f" ORDER BY {sort_array[0]} {sort_direction}")                   
        
    query = query.params(project_id=project_id)
    result = conn.execute(query)
    member_info = []
    for row in result:
        member_id = row[0]
        username = row[1]
        is_owner = row[2]
        # created_at = row[3].strftime("%Y-%m-%d %H:%M:%S") if isinstance(row[3], datetime) else str(row[3])
        created_at = convert_to_timestamp(row[3])
        user_data = user_db.fetch_user_by_username(username)
        if user_data:
            role = user_data.get('role', '')
            name = user_data.get('name', '')
            username = user_data.get('username', '')
            
            # if is_owner and show_owner:
            #     name += " (owner)"
            if search_value.lower() in name.lower() or search_value.lower() in username.lower():
                member_info.append({
                    'member_id': member_id,
                    'role': role,
                    'name': name,
                    'username': username,
                    'created_at': created_at,
                    'card_count': row[4],
                    'is_owner': is_owner,
                    'status': row[5],
                    'is_active': True if row[5] == 'active' else False
                })
    
    return member_info


def get_project_members(req_body, project_id, username):
    response = {
        "status" :400        
    }
    conn = pg_db.get_connection()
    try:
        project_access = check_project_access(project_id, username) 
        if not project_access:
            response['status'] = 403
            response['message'] = "You don't have access for this project."
            return response   
        search_value = req_body.get("search", "").strip()
        page = int(req_body.get("page", 0))
        limit = int(req_body.get("limit", 0))
        sort_by = req_body.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []
        # show_owner = False if page and limit else True
        members_data = _fetch_project_members(conn, search_value, project_id, sort_array)

        if page and limit:
            start_index = (page - 1) * limit
            end_index = start_index + limit

            if len(sort_array):
                sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
                if sort_array[0] in ["name"]: 
                    members_data = sorted(members_data, key=lambda x: x[sort_array[0]].lower(), reverse=(sort_direction == 'DESC'))

            paginated_data = members_data[start_index:end_index]
            
            total_records = len(members_data)
            data = calculatePaginationData(paginated_data, page, limit, total_records)
        else: 
            members_data = sorted(members_data, key=lambda x: x["name"].lower())
            # Add static "Unassigned" record
            unassigned_record = {
                "member_id": None,
                "role": None,
                "name": "Unassigned",
                "username": "",
                "created_at": None,
                "card_count": None,
                "is_owner": None,
                "status": None,
                "is_active": True
            }
            members_data.insert(0, unassigned_record)

            # Find and modify the record for the current user
            for member in members_data:
                if member["username"] == username:
                    member["name"] = "Me"
                    # Place this modified record at the second position
                    members_data.remove(member)
                    members_data.insert(1, member)
                    break

            data = members_data
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['data'] = []
            response['status'] = 200
    finally:
        if conn:
            conn.close()
    return response


def _insert_new_member(conn, project_id, username):
    try:
        query = text(
            f"""SELECT * FROM {pg_db.project_access} WHERE project_id = :project_id AND username = :username AND status = 'deleted';"""
        )
        query = query.params(project_id=project_id, username=username)
        result = conn.execute(query)
        if result.rowcount > 0:
            query = text(
                f"""UPDATE {pg_db.project_access} SET status = 'active', created_at = now() WHERE project_id = :project_id AND username = :username AND status = 'deleted';"""
            )
            query = query.params(project_id=project_id, username=username)
            conn.execute(query)
            conn.commit()
            return True
        query = text(
            f"""INSERT INTO {pg_db.project_access} (project_id, username, is_owner)
                VALUES (:project_id, :username, :is_owner);
            """
        )
        query = query.params(project_id=project_id, username=username, is_owner=False)
        conn.execute(query)
        conn.commit()

        return True
    except Exception as e:
        logger.error(traceback.format_exc())
        return False
    
def add_new_member(usernames, project_id):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:
        success_count = 0
        for username in usernames:
            data = _insert_new_member(conn, project_id, username)
            if data:
                success_count += 1

        if success_count == len(usernames):
            response['status'] = 200
            response['message'] = f"All {len(usernames)} members inserted successfully."
        elif success_count > 0:
            response['status'] = 200
            response['message'] = f"Only {success_count} out of {len(usernames)} members inserted successfully. Because other users already exists"
        else:
            response['status'] = 409
            response['message'] = "Data insertion failed for all members because users already exists"     
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response

def change_project_ownership(project_id, new_owner, username):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        if new_owner:
            # Check if the current user is the project owner
            query = text(
                f"""SELECT * FROM {pg_db.projects} WHERE id = :project_id AND owner_username = :username;"""
            )
            result = conn.execute(query.params(project_id=project_id, username=username)).fetchone()
            
            if result:
                # Assign new owner
                conn.execute(text(
                    f"""UPDATE {pg_db.project_access} SET is_owner = true WHERE project_id = :project_id AND username = :new_owner;"""
                ).params(project_id=project_id, new_owner=new_owner))

                # Remove ownership from the current owner
                conn.execute(text(
                    f"""UPDATE {pg_db.project_access} SET is_owner = false WHERE project_id = :project_id AND username = :username;"""
                ).params(project_id=project_id, username=username))

                # Update project owner
                conn.execute(text(
                    f"""UPDATE {pg_db.projects} SET owner_username = :new_owner WHERE id = :project_id;"""
                ).params(project_id=project_id, new_owner=new_owner))

                conn.commit()  # Commit all changes together
                response['status'] = 200
                response['message'] = "Project ownership changed successfully."
            else:
                response['status'] = 404
                response['message'] = "Project not found or you are not the owner."
        else:
            response['status'] = 400
            response['message'] = "Please check your payload."
    except Exception as e:
        conn.rollback()  # Rollback in case of any error
        response['status'] = 500
        response['message'] = f"An error occurred: {str(e)}"
    finally:
        if conn:
            conn.close()
    return response