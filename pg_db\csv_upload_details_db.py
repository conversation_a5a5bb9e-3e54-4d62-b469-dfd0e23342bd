from sqlalchemy.dialects.postgresql import insert
import pg_db as db
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, String, Integer, ForeignKey
from sqlalchemy.sql import func


class csv_upload_details(db.Base):
    __tablename__ = "csv_upload_details"

    id = Column(Integer, primary_key=True, autoincrement=True)
    file_name = Column(String(100), nullable=False)
    date = Column(DateTime, server_default=func.now())
    status = Column(String(100), nullable=False)