from services import Service
from utils.common import calculatePaginationData, convert_to_timestamp
import pg_db
from datetime import datetime
from sqlalchemy import text
from fields.customers import list_fields
from utils.common import parse_json
from mongo_db import customer_db
from utils import bc,store_util
import logging
import time
import timeit

logger = logging.getLogger(__name__)

class Customers(Service):
    def get_all(self, payload, store):
        response = {
            'status': 400
        }
        conn = pg_db.get_connection()
        try:
            page = int(payload.get('page', 1))
            limit = int(payload.get('limit', 10))
            sort_by = str(payload.get('sort_by', ''))
            sort_order = str(payload.get('sort_order', ''))
            search_term = payload.get('search_term', '')
            rep_name = payload.get('rep_name', '')

            rep_name_condition = ""
            if rep_name:
                rep_name_condition = f"AND scr.rep_name = '{rep_name}'"

            search_condition = ""            
            if search_term and search_term != '':
                search_term = search_term.replace(" ", "")
                search_condition = search_condition + "AND (cu.first_name ILIKE '%" + search_term + "%' or cu.last_name ILIKE '%" + search_term + "%' or REPLACE(CONCAT(cu.first_name,' ',cu.last_name), ' ', '') ILIKE '%" + search_term + "%' or cu.email ILIKE '%" + search_term + "%')"

            total_count_query = f"""SELECT COUNT(*) FROM (SELECT cu.customer_id,                           
                                    COUNT(o.order_id) AS order_count FROM customers cu 
                                    LEFT JOIN orders o ON cu.customer_id = o.customer_id
                                    LEFT JOIN salesforce_customer_rep AS scr ON cu.customer_id = scr.customer_id
                                    WHERE 1=1 {search_condition} {rep_name_condition}
                                    GROUP BY cu.customer_id) AS subquery""" 
                
            result_count = conn.execute(text(total_count_query))
            total_count = int(result_count.scalar())                    
                                    
            query = f"""SELECT 
                            cu.customer_id,
                            CONCAT(cu.first_name, ' ', cu.last_name) AS name,
                            cu.email,
                            cu."store_credit_in_USD" AS storecredit,
                            cu.phone,
                            cu.customer_group_name,
                            cu.customer_group_id,
                            cu.company,
                            cu.date_created,
                            cu.first_name,
                            cu.last_name,
                            COUNT(o.order_id) AS order_count,
                            scr.rep_name
                        FROM customers cu
                        LEFT JOIN orders o ON cu.customer_id = o.customer_id
                        LEFT JOIN salesforce_customer_rep AS scr ON cu.customer_id = scr.customer_id
                        WHERE 1=1 {search_condition} {rep_name_condition}
                        GROUP BY cu.customer_id, cu.first_name, cu."store_credit_in_USD", cu.phone, cu.customer_group_name, cu.date_created, scr.rep_name
                    """

            if sort_by and sort_order:
                query += f" ORDER BY {sort_by} {sort_order}"

            offset = (page - 1) * limit
            query += f" LIMIT {limit} OFFSET {offset}"
            
            result = conn.execute(text(query))
            res = result.fetchall()           
            customers = []
            if res:
                for row in res:
                    row_data = {
                        'id': str(row[0]),
                        'name': row[1],
                        'email': row[2],
                        'storecredit': row[3],
                        'phone': row[4],
                        'customer_group_name': row[5],
                        'customer_group_id': row[6],
                        'company': row[7],
                        'date_created': convert_to_timestamp(row[8]),
                        'first_name': row[9],
                        'last_name': row[10],
                        'orders_count': row[11],
                        'customer_rep_name': row[12]
                    }
                    customers.append(row_data)

            data = calculatePaginationData(customers, page, limit, total_count)

            response['data'] = data
            response['status'] = 200

        except Exception as e:
            error_message = str(e)
            response['status'] = 422
            response['message'] = error_message
            raise e
        finally:
            if conn:
                conn.close()
        return response

    
    def get_customer(self, customer_id):
        res = super().find_one({"id": int(customer_id)})
        
        return parse_json(res)
    
    def get_customer_representatives(self):
        response = {
            'status': 400
        }
        conn = pg_db.get_connection()
        try:
            query = f"""SELECT DISTINCT(rep_name), rep_email FROM salesforce_customer_rep WHERE rep_name != '' order by rep_name"""
            result = conn.execute(text(query))
            reps = {}
            for row in result.fetchall():
                reps[row[1]] = row[0]
                # reps.append(row[0])

            if reps:
                response['data'] = reps
                response['status'] = 200
            else:
                response['message'] = "No data found"
                response['status'] = 400
            
        except Exception as e:
            error_message = str(e)
            response['status'] = 422
            response['message'] = error_message
            raise e
        finally:
            if conn:
                conn.close()
        return response
    
    def get_all_customer_groups(self, api):
        response = {
            'status': 400
        }
        response['data'] = bc.get_bc_customer_groups(api, {})
        conn = pg_db.get_connection()
        try: 
            for customer in response['data']:    
                query = text("""
                    SELECT COUNT(customer_id) AS customer_count
                    FROM customers
                    WHERE customer_group_id = :your_customer_group_id
                """)
                result = conn.execute(query.params(your_customer_group_id=customer['id']))
                customer_count = result.scalar()  
                customer['customer_count']= customer_count                   

        except Exception as e:
            error_message = str(e)
            response['status'] = 422
            response['message'] = error_message
            raise e
        finally:
            if conn:
                conn.close()
        return response
