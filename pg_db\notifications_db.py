from sqlalchemy.dialects.postgresql import insert
import pg_db as db
from sqlalchemy import <PERSON>olean, Column, DateTime, String, Integer, ForeignKey, Float
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import insert
import pg_db as db
from sqlalchemy import Boolean, Column, DateTime, String, Integer, ForeignKey, BigInteger
from sqlalchemy.sql import func


class NotificationModules(db.Base):
    __tablename__ = "notification_modules"
    id = Column(Integer, primary_key=True, unique=True, nullable=False)
    module_name = Column(String, nullable=False)

class NotificationSubModules(db.Base):
    __tablename__ = "notification_sub_modules"
    id = Column(Integer, primary_key=True, unique=True, nullable=False)
    parent_id = Column(Integer, ForeignKey("notification_modules.id"), nullable=False)
    module_name = Column(String)
    table_name = Column(String)
    primary_column = Column(String)
    receiver = Column(String)

class Notifications(db.Base):
    __tablename__ = "notifications"
    id = Column(Integer, primary_key=True, unique=True, nullable=False)
    module_id = Column(Integer, ForeignKey("notification_modules.id"), nullable=False)
    sub_module_id = Column(Integer, ForeignKey("notification_sub_modules.id"), nullable=False)
    operation = Column(String)
    customer_email = Column(String)
    sales_rep_email = Column(String)
    entity_id = Column(String)
    created_at = Column(DateTime, server_default=func.now())
    created_by = Column(String)
    updated_at = Column(DateTime, onupdate=func.now())
    updated_by = Column(String)
    is_read = Column(Boolean, default=False)
    is_system_generated = Column(Boolean, default=False)
