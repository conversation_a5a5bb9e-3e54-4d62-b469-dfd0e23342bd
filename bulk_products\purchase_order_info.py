from new_mongodb import fetch_one_document_from_storefront_collection, tenant_db
from sqlalchemy import text
from graphql import bulk_query
import pg_db
from datetime import datetime
from sqlalchemy.exc import IntegrityError
from utils import bc, email_util,  store_util
import logging
import traceback
from sqlalchemy.exc import  IntegrityError
from utils import bc,  store_util
from bulk_products import purchase_order
from mongo_db import product_db
from plugin import bc_customers, bc_products
from utils.common import convert_to_timestamp
from new_pgdb.analytics_db import AnalyticsDB
import task
from new_mongodb import AdminAppNotification
from notifications import notifications
from utils import order_util
logger = logging.getLogger()

def fetch_po_info(po_id):
    conn = pg_db.get_connection()
    igen_tax_flag = tenant_db.fetch_igen_tax_flag()
    try:
        if po_id:
            query = text(f"""
                SELECT 
                    bpo.po_id,
                    bpo.created_at,
                    bpo.status,
                    bpo.customer_name,
                    bpo.customer_rep_name,
                    bpo.customer_rep_email,
                    bpo.customer_id,
                    sc.price_list,
                    c.customer_group_id,
                    c.customer_group_name     
                FROM {pg_db.bo_purchase_orders} bpo
                LEFT JOIN salesforce_customer_rep sc ON bpo.customer_id = sc.customer_id
                LEFT JOIN customers c ON bpo.customer_id = c.customer_id
                WHERE 
                    bpo.po_id = :po_id
            """)
            query = query.params(po_id=po_id)
            result = conn.execute(query).fetchall()
            po_data={}
            customer_id = None
            for row in result:
                po_data['po_id']=row[0]
                po_data['created_at']=convert_to_timestamp(row[1])
                po_data['status']=row[2]
                po_data['customer_name']=row[3]
                po_data['customer_rep_name']=row[4]
                po_data['customer_rep_email']=row[5]
                po_data['customer_id']=row[6]
                po_data['customer_email'] = ''
                po_data['is_using_igen_tax'] = igen_tax_flag['use_igen_tax'] if igen_tax_flag else False
                po_data['customer_price_list'] = row[7]
                po_data['customer_group_id'] = row[8]
                po_data['customer_group_name'] = row[9]
            
            customer_query=text(f"""SELECT email, company FROM customers 
                WHERE customer_id= :customer_id
            """ )
            customer_query=customer_query.params(customer_id=po_data['customer_id'])
            customer_id=conn.execute(customer_query).fetchone()
            po_data['customer_email']=customer_id[0] if customer_id else ''
            po_data['customer_company']=customer_id[1] if customer_id else ''
            return po_data
    finally:
        if conn:
            conn.commit()
            conn.close()

def fetch_product_details(bop_id):
    conn = pg_db.get_connection()
    try:
        if bop_id:
            query = text(f"""
                SELECT 
                    bc_sku,
                    bc_name,
                    product_image,
                    bc_product_id,
                    display_qty,
                    case_qty,
                    type
                       
                FROM {pg_db.bo_bulk_order_products}
                WHERE 
                    bop_id = :bop_id
            """)
            query = query.params(bop_id=bop_id)
            result = conn.execute(query).fetchall()
            product_data={}
            for row in result:
                
                product_data['bc_sku']=row[0]
                product_data['bc_name']=row[1]
                product_data['product_image']=row[2]
                product_data['bc_product_id']=row[3]
                product_data['display_qty']=row[4]
                product_data['case_qty']=row[5]
                product_data['type']=row[6]

            return product_data
    finally:
        if conn:
            conn.commit()
            conn.close()

def fetch_variant_details(bc_upc, bop_id):
    
    conn = pg_db.get_connection()
    try:
        if bc_upc:
            query = text(f"""
                SELECT 
                    option,
                    bc_variant_id,
                    rv.total_sold_30
                FROM {pg_db.bo_product_variants}
                LEFT JOIN {AnalyticsDB.get_replenishment_variants_table()} rv ON bc_sku = sku
                WHERE 
                    bc_sku = :bc_sku and bop_id = :bop_id
            """)

            query = query.params(bc_sku=bc_upc, bop_id=bop_id)
            result = conn.execute(query).fetchall()
           
            variant_data={}
            for row in result:
                variant_data['option']=row[0]
                variant_data['bc_variant_id']=row[1]
                variant_data['total_sold_30'] = row[2]
                
            return variant_data
        return {}
    finally:
        if conn:
            conn.commit()
            conn.close()

def fetch_po_details_info(store,po_id):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:
        if po_id:
            query = text(f"""
                SELECT 
                    pol.bop_id,
                    pol.variant_id,
                    pol.bc_upc,
                    pol.bo_upc,
                    pol.bc_sku,
                    pol.po_option,
                    pol.requested_qty,
                    pol.approved_qty,
                    pol.fullfilled_qty,
                    pol.remaining_qty,
                    pol.price,
                    pol.status,
                    bop.is_qty_locked,
                    po.customer_id,   
                    bop.is_po_locked,
                    bop.min_market_price,
                    bop.is_marketing_product
                FROM 
                    bo_purchase_order_lineitems AS pol
                JOIN
                    bo_purchase_orders AS po ON pol.po_id = po.po_id
                JOIN 
                    bo_bulk_order_products AS bop ON pol.bop_id = bop.bop_id
                WHERE 
                    pol.po_id = :po_id
            """)
            query = query.params(po_id=po_id)
            result = conn.execute(query).fetchall()
            res_query= text(f"""
                SELECT locked_qty, distributed_qty FROM bo_distribution_lineitems
                WHERE distribution_id = (SELECT DISTINCT id FROM bo_distributions WHERE bop_id = :bop_id and end_date_time is null and is_published = true) and variant_id = :variant_id and customer_id = :customer_id
            """)
            formatted_result = []
            products_dict = {}            
            po_details=fetch_po_info(po_id)
            po_details['line_items']=[]
            formatted_result.append(po_details) 
            product_ids=[]
            variant_ids=[]      
            marketing_product_skus=[]                

            for row in result:
                bop_id = row[0]
                db_variant_id=row[1]
                bo_upc = row[3]
                bc_upc = row[2]
                var_bc_sku = row[4]
                po_option = row[5]
                requested_qty = row[6]
                approved_qty = row[7]
                fullfilled_qty = row[8]
                remaining_qty = row[9]
                price = row[10]
                status = row[11]
                customer_id = row[13]

                if bop_id not in products_dict:
                    product = fetch_product_details(bop_id)
                    bc_sku = product['bc_sku']
                    bc_name = product['bc_name']
                    product_image = product['product_image']
                    product_id = product['bc_product_id']
                    display_qty = product['display_qty']
                    case_qty = product['case_qty']
                    bulk_product_type = product['type']

                    if bulk_product_type == 'bulkorder':
                        query = {
                            "id": int(product_id),   
                        }
                        projection = {
                            "images": {
                                "$elemMatch": {
                                    "is_thumbnail": True
                                }
                            }
                        }
                        res = fetch_one_document_from_storefront_collection(store['id'], 'products', query, projection)
                        if res:
                            images = res.get('images', [])
                            if len(images) > 0:
                                image = images[0].get('url_thumbnail', '')
                                if image != '':
                                    product_image = image

                    if row[16]:
                        marketing_product_skus.append(bc_sku)

                    products_dict[bop_id] = {
                        'bop_id': bop_id,
                        'bc_sku': bc_sku,
                        'bc_name': bc_name,
                        'product_image': product_image,
                        'product_id': product_id,
                        'is_qty_restricted': row[12],
                        'is_po_locked': row[14],
                        'display_qty': display_qty,
                        'case_qty': case_qty,
                        'min_market_price': row[15],
                        'is_marketing_product': row[16],
                        'variants': []
                    }
                    product_ids.append(str(product_id))

                variant = fetch_variant_details(var_bc_sku, bop_id)                
                if variant:
                    variant_ids.append(int(variant['bc_variant_id']))
                    restricted_qty=0
                    distributed_qty = 0
                    if products_dict[bop_id]['is_qty_restricted']:
                        res_query = res_query.params(variant_id=db_variant_id, customer_id=customer_id, bop_id=bop_id)
                        res = conn.execute(res_query).fetchone()
                        if res is not None:
                           restricted_qty = res[0]
                           distributed_qty = res[1]
                    variant_data = {
                        'option': variant['option'],
                        'bc_variant_id': variant['bc_variant_id'],
                        'variant_id': db_variant_id,
                        'total_sold_30': variant['total_sold_30'],
                        'bc_upc': bc_upc,
                        'bo_upc': bo_upc,
                        'bc_sku': var_bc_sku,
                        'requested_qty': requested_qty,
                        'restricted_qty': restricted_qty,
                        'distributed_qty': distributed_qty,
                        'fullfilled_qty': fullfilled_qty,
                        'remaining_qty': remaining_qty,
                        'approved_qty': approved_qty,
                        'price': price,
                        'status': "" if status is None else status
                    }
                    products_dict[bop_id]['variants'].append(variant_data)
                else:
                    variant_data = {
                        'option': po_option,
                        'bc_variant_id': None,
                        'variant_id': db_variant_id,
                        'total_sold_30': None,
                        'bc_upc': bc_upc,
                        'bo_upc': bo_upc,
                        'bc_sku': var_bc_sku,
                        'requested_qty': requested_qty,
                        'restricted_qty': 0,
                        'distributed_qty': 0,
                        'fullfilled_qty': fullfilled_qty,
                        'remaining_qty': remaining_qty,
                        'approved_qty': approved_qty,
                        'price': price,
                        'status': "" if status is None else status
                    }
                    products_dict[bop_id]['variants'].append(variant_data)

            for product_data in products_dict.values():               
                po_details['line_items'].append(product_data) 

            product_inventory_data = bc_products.fetch_products_by_ids(store, product_ids)
            
            if product_inventory_data: 
                graphql_inventory = {}
                for product in product_inventory_data:
                    for variant in product['variants']:
                        variant_id = variant['id']
                        product_id = product['id']
                        available_to_sell = variant['inventory_level']
                        graphql_inventory[(product_id, variant_id)] = available_to_sell
                for item in po_details['line_items']:
                    product_id = item['product_id']
                    for variant in item['variants']:
                        variant_id = variant['bc_variant_id']
                        inventory_level = graphql_inventory.get((product_id, variant_id))
                        variant['stock_level'] = inventory_level 

            if len(marketing_product_skus):
                data = bc_products.fetch_bc_product_by_sku(store, marketing_product_skus)
                if data:
                    if 'data' in data:
                        for i in data['data']:
                            p_id = i['id']
                            if i['sku'] in marketing_product_skus:                                                           
                                for item in po_details['line_items']:
                                    if int(item['product_id']) == int(p_id):                                        
                                        item['variants'][0]['stock_level'] = i['inventory_level']     

            response['data']=formatted_result   
            response['status'] = 200             
    except IntegrityError as e:
                error_message = str(e)
                response['status'] = 422
                response['message'] = f"Something went wrong. {error_message}"
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response
    
def get_po_info(store,po_id):
    res = fetch_po_details_info(store,po_id)
    if res:
        return res
    
def get_price_lists_data(store_id, variant_ids, customer_id, price_list_id):
    variant_ids = [int(v.strip()) for v in variant_ids.split(',') if v.strip().isdigit()]
    res = order_util.get_variant_prices(store_id, variant_ids, price_list_id, customer_id)
    if res:
        return res
        
def get_po_info_completed(po_id):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:
        if po_id:

            base_query = text(f"""SELECT po.po_id, po.status, pbo.bc_order_id, SUM(pbo.order_total), pbo.created_at    
                    FROM bo_purchase_orders AS po JOIN bo_purchase_order_bc_order_mapping AS pbo ON po.po_id = pbo.po_id WHERE po.po_id = :po_id GROUP BY po.po_id, po.status, pbo.bc_order_id, pbo.created_at ORDER BY pbo.created_at DESC
                """) 
            base_query = base_query.params(po_id=po_id)
            result = conn.execute(base_query).fetchall() 

            po_order_data=[]
            for row in result:                    
                    po_order_list={}
                    po_order_list['po_id'] = row[0]
                    po_order_list['bc_order_id']=row[2]
                    po_order_list['order_total']=row[3]
                    po_order_list['created_at']=convert_to_timestamp(row[4])

                    po_order_data.append(po_order_list)            
            
            response['data']=po_order_data
            response['status']=200
           
            return response
    except IntegrityError as e:
                error_message = str(e)
                response['status'] = 422
                response['message'] = f"Something went wrong. {error_message}"
    finally:
        if conn:
            conn.commit()
            conn.close()
            
def fetch_bc_order_by_customer_id(store, customer_id):
    if customer_id:
        url = f"v2/orders"
        bc_api = store_util.get_bc_api_creds(store)
        query_params = { 
            "customer_id":customer_id,               
            "status_id": 9,
            "sort":"date_created:desc",
            "limit":4
        }
        order_data = bc.call_api(bc_api, "GET", url, query_params=query_params) 
        order = {}
        if order_data.status_code == 200:  
            order = order_data.json()
        return order if order != [] else []
    else:
        return []
    
def get_po_customer_info(store, sku):
    response = {
        "status" :400
    }
    res = fetch_bc_order_by_customer_id(store, sku)
    order_ids=[]
    if res:
        for data in res:
            order_ids.append(data['id'])
        response['data'] = order_ids
        response['status'] = 200
    else:
        response['data'] = order_ids
        response['status'] = 200

    return response

def create_purchase_order(product_id,product_line_items,product_variant_items, updated_by, po_id, is_draft, product_sku,bc_variant_id,variant_id, bc_upc, bo_upc, bc_sku, stock_value, requested_qty, fullfilled_qty, remaining_qty, approved_qty, remaining_qty_status, price,case_qty, is_qty_restricted, bop_id, customer_id, conn, is_bulk_order=False):
    response = {
        "status": 400
    }
   
    if is_draft:
        query = text(
            f"""UPDATE {pg_db.bo_purchase_order_lineitems} SET approved_qty = :approved_qty, price = :price, status = :status WHERE variant_id = :variant_id and po_id = :po_id
            """
        )
        query = query.params(approved_qty = approved_qty, price = price, variant_id = variant_id,status=remaining_qty_status, po_id = po_id)
        conn.execute(query)
        
        response['status'] = 200
        response['message'] = "Operation successful"

    else:
        product_variant_list={}
        if approved_qty !=0:
            product_variant_list['product_id']=product_id
            product_variant_list['variant_id']=bc_variant_id
            product_variant_list['quantity']=approved_qty * case_qty
            product_variant_list['price']= price

            product_line_items.append(product_variant_list)
            # add list to update db
            data_to_update={}
            data_to_update['variant_id']=variant_id
            data_to_update['requested_qty']=requested_qty
            data_to_update['remaining_qty']=remaining_qty
            data_to_update['fullfilled_qty']=fullfilled_qty
            data_to_update['approved_qty']=approved_qty
            data_to_update['remaining_qty_status']=remaining_qty_status
            data_to_update['is_qty_restricted'] = is_qty_restricted
            data_to_update['price']=price
            data_to_update['bop_id'] = bop_id
            product_variant_items.append(data_to_update)

        if approved_qty == 0 and remaining_qty_status == 'cancelled':
            dl_query = text(
                f"""UPDATE bo_distribution_lineitems SET distributed_qty = :distributed_qty, requested_qty = :requested_qty, locked_qty = :locked_qty WHERE distribution_id = (SELECT DISTINCT id FROM bo_distributions WHERE bop_id = :bop_id and end_date_time is null and is_published = true) and variant_id = :variant_id and customer_id = :customer_id
                """
            )
            dl_query = dl_query.params(variant_id = variant_id, distributed_qty=0, customer_id = customer_id, bop_id = bop_id, requested_qty = 0, locked_qty = 0)
            conn.execute(dl_query)            
            
        u_query = text(
            f"""UPDATE {pg_db.bo_purchase_order_lineitems} SET approved_qty= :approved_qty, status = :status, price = :price WHERE variant_id = :variant_id and po_id = :po_id
            """
        )
        u_query = u_query.params(approved_qty=0, status=remaining_qty_status, price = price, variant_id=variant_id, po_id=po_id)
        conn.execute(u_query)
        response['status'] = 200
        response['message'] = "Operation successful"
            
    return response

def check_variants_completed(variants):
     for variant in variants:
        remaining_qty_status=variant.get('status','completed')
        if remaining_qty_status=='backorder':
             return False
        elif remaining_qty_status=='cancelled' or remaining_qty_status=='completed':
             pass
     return True

def process_purchase_order_data(store,is_draft,po_id,po_status,po_created,customer_name,customer_rep,customer_rep_email,updated_by,customer_id,created_by,customer_email_id,line_items, bulling_address_id, payment_method, shipping_option_id, consignment_id, cart_id, shipping_address_id=0, shipping_cost=0.0, shipping_method='', is_using_igen_tax=True):  
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        with conn.begin():       
            product_line_items=[]
            product_variant_items=[]
            variants_status_check=True 
            purchase_order_status = ''
            if line_items:
                for items in line_items:
                    product_sku=items.get('bc_sku','')
                    product_id=items.get('product_id','')
                    variants=items.get('variants',[])
                    is_qty_restricted = items.get('is_qty_restricted', False)
                    bop_id = items.get('bop_id', 0)
                    for variant in variants:
                        bc_variant_id= variant.get('bc_variant_id', '')
                        variant_id = variant.get('variant_id','')
                        bc_upc = variant.get('bc_upc', '')
                        bo_upc = variant.get('bo_upc', '')
                        bc_sku = variant.get('bc_sku','')
                        stock_value=variant.get('stock_value',0)
                        requested_qty = variant.get('requested_qty', 0)
                        fullfilled_qty = variant.get('fullfilled_qty', 0)
                        remaining_qty=variant.get('remaining_qty',0)
                        approved_qty=variant.get('approved_qty',0)
                        remaining_qty_status=variant.get('status',0)
                        price=variant.get('price',0)
                        case_qty=variant.get('case_qty',0)

                        res = create_purchase_order(product_id,product_line_items,product_variant_items,updated_by,po_id,is_draft,product_sku,bc_variant_id, variant_id, bc_upc, bo_upc, bc_sku, stock_value, requested_qty, fullfilled_qty, remaining_qty, approved_qty,remaining_qty_status,price, case_qty, is_qty_restricted, bop_id, customer_id, conn, is_bulk_order=True)
                    
                    if not is_draft and variants_status_check:
                        variants_status_check=check_variants_completed(variants)
                if variants_status_check:
                    purchase_order_status = 'completed'
                    set_product_status_completed(po_id,updated_by,'completed', conn)
                else:
                    po_status = 'partially fulfilled'
                    purchase_order_status = 'partially fulfilled'
                    set_product_status_completed(po_id,updated_by,'partially fulfilled', conn)
                                        
                #create order
                if not is_draft :                
                    if len(product_line_items):
                        res = update_po_fields(product_variant_items, customer_id, po_id, conn)
                        if res['status'] == 200:
                            order_data=None
                            if is_using_igen_tax:
                                order_data=purchase_order.create_purchase_order_with_igen_tax(conn, store, po_id, customer_id, customer_email_id, bulling_address_id, payment_method, shipping_option_id, consignment_id, cart_id, line_items)                        
                            else:
                                order_data=purchase_order.create_purchase_order_without_igen_tax(store,customer_id,customer_email_id,product_line_items, bulling_address_id, shipping_address_id, payment_method, shipping_cost, shipping_method)                        
                            res=add_po_bulk_orders(customer_email_id,customer_rep_email, order_data,po_id,po_status,po_created,customer_name,customer_rep,created_by, updated_by, conn)                    
                            if res['status'] == 200:
                                response['status'] = 200
                                response['message']="Order created successfully."
                                bc_order_id = order_data.order_id

                                update_ticket_status_using_po_id(conn, po_id, purchase_order_status, updated_by, store['id'])

                                task.send_task(task.GENERATE_ADMIN_APP_NOTIFICATION_TASK, args=(store['id'], AdminAppNotification.ORDER_PLACED, bc_order_id))
                        else:
                            response['status'] = res['status']
                            response['message'] = res['message']  
                    else:                    
                        res = update_po_fields(product_variant_items, customer_id, po_id, conn)            
                        response['status'] = res['status']
                        response['message'] = res['message']
                        update_ticket_status_using_po_id(conn, po_id, purchase_order_status, updated_by, store['id'])
                        notifications.update_po_status_in_notifications(store['id'], po_id)                                       
                else: 
                    update_query = text(
                        f"""UPDATE {pg_db.bo_purchase_orders} SET status= 'draft', updated_at=:updated_at, updated_by=:updated_by WHERE po_id = :po_id
                    """
                    )
                    update_query = update_query.params(po_id=po_id, updated_at=datetime.now(), updated_by=updated_by)
                    conn.execute(update_query)
                    response['status'] = 200
                    response['message']="Details saved as draft successfully."
            else:
                response['status'] = 200
                response['message']="There is no line items in this order to be placed."
    finally:
        conn.commit()
        conn.close()
    return response 

def update_ticket_status_using_po_id(conn, po_id, purchase_order_status, updated_by, store_id):
    query = text (f"""SELECT id, project_id, title, pipeline_record_id, table_name from agile_project_cards where pipeline_record_id = :id and table_name = 'bo_purchase_orders';""")
    query = query.params(id=po_id)
    res = conn.execute(query).fetchone()
    if res:
        data = {
            'ticket_id': res[0],
            'title': res[2],
            'status': purchase_order_status,
            'project_id': res[1],
            'resource_id': res[3],
            'table_name': res[4],
            'updated_by': updated_by
        }
        task.send_task(task.UPDATE_PROJECT_TICKET_TASK, args=(store_id, data))

def add_po_bulk_orders(customer_email_id,customer_rep_email, orders_data,po_id,po_status,po_created,customer_name,customer_rep,created_by, updated_by, conn):
    response = {
        "status": 400,
        "message": "Data insertion failed."
    }   
    bc_order_id=orders_data.order_id
    order_total=orders_data.total_inc_tax
    cart_id=orders_data.cart_id
    
    query = text(
        f"""INSERT INTO {pg_db.bo_purchase_order_bc_order_mapping} (po_id, bc_order_id, order_total, cart_id, created_by, created_at, updated_by)
            VALUES (:po_id, :bc_order_id, :order_total, :cart_id, :created_by, :created_at, :updated_by);
        """
    )
    query = query.params(po_id=po_id, bc_order_id=bc_order_id, order_total=order_total, cart_id=cart_id, created_by=created_by, created_at=datetime.now(), updated_by=updated_by)
    conn.execute(query)
    #send email
    data=get_created_order(po_id, conn)
    if data['status'] == 200:
        email_util.send_po_request_email(data['data'],po_id,po_status,po_created,customer_name,customer_rep,customer_rep_email,customer_email_id)
        response['status'] = 200
        response['message'] = "Data inserted successfully."
    else:
        raise Exception("Data insertion failed.")
    return response

def get_created_order(po_id, conn):
    response = {
        "status" :400
    }
    if po_id:
        base_query = text(f"""SELECT created_at, bc_order_id, order_total
                    FROM bo_purchase_order_bc_order_mapping
                    WHERE po_id = :po_id
                    ORDER BY created_at DESC
                    LIMIT 1;
            """) 
        base_query = base_query.params(po_id=po_id)
        result = conn.execute(base_query).fetchall() 
        po_order_list={}

        for row in result:                    
                # po_order_list['po_id'] = row[0]
                po_order_list['bc_order_id']=row[1]
                po_order_list['order_total']=row[2]
                po_order_list['created_at']=convert_to_timestamp(row[0])         
        
        response['data']=po_order_list
        response['status']=200
    return response

def update_po_fields(product_variant_items, customer_id, po_id, conn):
    response = {
        "status": 400,
        "message": "Data insertion failed."
    }
    for item in product_variant_items:
        remaining_qty=item['remaining_qty']
        requested_qty=item['requested_qty']
        approved_qty=item['approved_qty']
        fullfilled_qty=item['fullfilled_qty']
        price=item['price']
        variant_id=item['variant_id']
        remaining_qty_status=item['remaining_qty_status']
        is_qty_restricted=item['is_qty_restricted']
        bop_id=item['bop_id']
        
        # if is_qty_restricted:
        query = text(
            f"""SELECT distributed_qty, requested_qty, locked_qty FROM bo_distribution_lineitems WHERE distribution_id = (SELECT DISTINCT id FROM bo_distributions WHERE bop_id = :bop_id and end_date_time is null and is_published = true) and variant_id = :variant_id and customer_id = :customer_id
            """
        )
        query = query.params(variant_id = variant_id, customer_id = customer_id, bop_id = bop_id)
        res = conn.execute(query).fetchone()
        if res is not None:
            curr_dist_qty = res[0]
            curr_req_qty = res[1]
            curr_locked_qty = res[2]
            if curr_dist_qty is not None and curr_req_qty is not None:

                new_dist_qty = curr_dist_qty
                if is_qty_restricted:
                    new_dist_qty = curr_dist_qty + approved_qty
                
                new_req_qty = curr_req_qty - approved_qty
                if remaining_qty_status == 'cancelled':
                    new_req_qty = 0                

                new_locked_qty = 0                                  

                query = text(
                    f"""UPDATE bo_distribution_lineitems SET distributed_qty = :distributed_qty, requested_qty = :requested_qty, locked_qty = :locked_qty WHERE distribution_id = (SELECT DISTINCT id FROM bo_distributions WHERE bop_id = :bop_id and end_date_time is null and is_published = true) and variant_id = :variant_id and customer_id = :customer_id
                    """
                )
                query = query.params(variant_id = variant_id, distributed_qty=new_dist_qty, customer_id = customer_id, bop_id = bop_id, requested_qty = new_req_qty, locked_qty = new_locked_qty)
                conn.execute(query)

        if remaining_qty == requested_qty:
            remaining_qty = requested_qty
        
        remaining=remaining_qty - approved_qty
        fullfilled=fullfilled_qty + approved_qty

        query = text(
            f"""UPDATE {pg_db.bo_purchase_order_lineitems} SET price = :price, fullfilled_qty = :fullfilled_qty, remaining_qty=:remaining_qty,  status=:status WHERE variant_id = :variant_id and po_id = :po_id
            """
        )
        query = query.params(price = price, fullfilled_qty = fullfilled, remaining_qty = remaining, variant_id = variant_id, status = remaining_qty_status, po_id = po_id)
        conn.execute(query)
    response['status'] = 200
    response['message'] = "Po updated successfully."
         
    return response

def set_product_status_completed(po_id,updated_by,status, conn):
    response = {
        "status" :400
    }
    if po_id:
        update_query = text(
                            f"""UPDATE {pg_db.bo_purchase_orders} SET status= :status, updated_at=:updated_at, updated_by=:updated_by WHERE po_id = :po_id
                                """
        )
        update_query = update_query.params(status=status, po_id=po_id, updated_at=datetime.now(), updated_by=updated_by)
        conn.execute(update_query)
        response['status']=200
        response['message']="Operation successfull"
    return response

def delete_po_info(po_id, reason, username, store_id):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:
        with conn.begin():
            if po_id:
                pob_query = text(f"""select count(*) from {pg_db.bo_purchase_order_bc_order_mapping} WHERE po_id = :po_id """)
                pob_query = pob_query.params(po_id=po_id)
                pob_result = conn.execute(pob_query)
                total_count = int(pob_result.scalar())  

                if total_count > 0:
                    response['status']=422
                    response['message']="Parcially completed PO can not be deleted."
                    return response

                delete_po_query = text(f"""update {pg_db.bo_purchase_orders} set status = 'deleted', reason = :reason, updated_at = CURRENT_TIMESTAMP, updated_by = :updated_by  WHERE po_id = :po_id """)
                delete_po_query = delete_po_query.params(po_id=po_id, reason = reason, updated_by = username)
                conn.execute(delete_po_query)

                query = text(f"select pol.bop_id, pol.variant_id, pol.bc_variant_id, pol.bc_upc, pol.bc_sku, pol.requested_qty, po.customer_id from {pg_db.bo_purchase_order_lineitems} as pol join bo_purchase_orders AS po ON pol.po_id = po.po_id where pol.po_id = :po_id")
                query = query.params(po_id=po_id)
                result = conn.execute(query).fetchall()

                for row in result:
                    bop_id = row[0]
                    variant_id = row[1]
                    requested_qty = row[5]
                    customer_id = row[6]

                    get_query = text(f"select d.bop_id, dl.id, dl.distribution_id, dl.variant_id, dl.bc_variant_id, dl.bc_sku, dl.bc_upc, dl.requested_qty, dl.locked_qty, dl.distributed_qty, dl.previously_distributed_qty, dl.customer_id from bo_distribution_lineitems as dl join bo_distributions as d ON dl.distribution_id = d.id  where d.bop_id = :bop_id and d.is_published = true and d.end_date_time is null and dl.customer_id = :customer_id and dl.variant_id = :variant_id")  
                    get_query = get_query.params(bop_id = bop_id, customer_id = customer_id, variant_id = variant_id)   
                    get_result = conn.execute(get_query).fetchall()

                    for rs in get_result:
                        id = rs[1]
                        distribution_id = rs[2]
                        variant_id = rs[3]
                        customer_id = rs[11]


                        new_requested_qty = rs[7] - requested_qty if rs[7] >= requested_qty else rs[7]
                        new_locked_qty = new_requested_qty if rs[8] >= new_requested_qty else rs[8]
                        new_distributed_qty = rs[9] - requested_qty if rs[9] > new_requested_qty else rs[9]
                        new_distributed_qty = new_distributed_qty if new_distributed_qty > 0 else 0
                        # new_previously_distributed_qty = rs[10] - requested_qty if rs[10] > new_requested_qty else rs[10]
                        # new_previously_distributed_qty = new_previously_distributed_qty if new_previously_distributed_qty > 0 else 0

                        update_query = text(f"""update bo_distribution_lineitems set requested_qty = :new_requested_qty, locked_qty = :new_locked_qty, distributed_qty = :new_distributed_qty where customer_id = :customer_id and variant_id = :variant_id and distribution_id = :distribution_id and id = :id""")
                        update_query = update_query.params(customer_id = customer_id, variant_id = variant_id, distribution_id = distribution_id, id = id, new_requested_qty = new_requested_qty, new_locked_qty = new_locked_qty, new_distributed_qty = new_distributed_qty)
                        update_result = conn.execute(update_query)


                response['status']=200
                response['message']="PO deleted successfully."
            else:
                response['status']=422
                response['message']="PO id is required."
    except IntegrityError as e:
        conn.rollback()
        error_message = str(e)
        response['status'] = 422
        response['message'] = f"Something went wrong. {error_message}"
    finally:
        if conn:
            conn.commit()
            notifications.update_po_status_in_notifications(store_id, po_id)
            conn.close()
    return response

def cancel_entire_po(po_id, username, store_id):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:
        with conn.begin():
            if po_id:
                complete_query = text(f"""update {pg_db.bo_purchase_orders} set status = 'completed', updated_at = CURRENT_TIMESTAMP, updated_by = :updated_by  WHERE po_id = :po_id """)
                complete_query = complete_query.params(po_id=po_id, updated_by = username)
                conn.execute(complete_query)

                query = text(f"select pol.id, pol.bop_id, pol.variant_id, pol.bc_variant_id, pol.bc_upc, pol.bc_sku, pol.requested_qty, pol.fullfilled_qty, pol.remaining_qty, pol.approved_qty, pol.status from {pg_db.bo_purchase_order_lineitems} as pol join bo_purchase_orders AS po ON pol.po_id = po.po_id where pol.po_id = :po_id and pol.remaining_qty > 0 and (pol.status IS NULL or pol.status NOT IN ('cancelled', 'completed'))")
                query = query.params(po_id=po_id)
                result = conn.execute(query).fetchall()

                if result:
                    for row in result:
                        id = row[0]
                        bop_id = row[1]
                        variant_id = row[2]
                        
                        update_query = text(f"""update {pg_db.bo_purchase_order_lineitems} set status = 'cancelled' where id = :id and variant_id = :variant_id and bop_id = :bop_id""")
                        update_query = update_query.params(id = id, variant_id = variant_id, bop_id = bop_id)
                        update_result = conn.execute(update_query)
                    
                    query = text (f"""SELECT id, project_id, title, pipeline_record_id, table_name from agile_project_cards where pipeline_record_id = :id and table_name = 'bo_purchase_orders';""")
                    query = query.params(id=po_id)
                    res = conn.execute(query).fetchone()
                    if res:
                        data = {
                            'ticket_id': res[0],
                            'title': res[2],
                            'status': 'completed',
                            'project_id': res[1],
                            'resource_id': res[3],
                            'table_name': res[4],
                            'updated_by': username
                        }
                        task.send_task(task.UPDATE_PROJECT_TICKET_TASK, args=(store_id, data))

                response['status']=200
                response['message']="PO cancelled successfully."
            else:
                response['status']=422
                response['message']="PO id is required."
    except IntegrityError as e:
        conn.rollback()
        error_message = str(e)
        response['status'] = 422
        response['message'] = f"Something went wrong. {error_message}"
    finally:
        if conn:
            conn.commit()
            notifications.update_po_status_in_notifications(store_id, po_id)
            conn.close()
    return response



def fetch_customer_addresses(store, po_id, customer_id):
    response = {
        "status" :400
    }
    try:
        if customer_id:
            customer_address = bc_customers.fetch_customer_addresses(store, customer_id)
            addresses = []
            if len(customer_address):                
                for address in customer_address:
                    address_parts = []
                    if address.get("company"):
                        address_parts.append(address.get("company"))
                    if address.get("address1"):
                        address_parts.append(address.get("address1"))
                    if address.get("address2"):
                        address_parts.append(address.get("address2"))
                    if address.get("city"):
                        address_parts.append(address.get("city"))
                    if address.get("postal_code"):
                        address_parts.append(address.get("postal_code"))
                    if address.get("country"):
                        address_parts.append(address.get("country"))
                    
                    address_str = ', '.join(address_parts)
                    data = {
                        'id': address['id'],
                        'address':  address_str
                    }
                    addresses.append(data)
            
            response['status'] = 200
            response['data'] = addresses
        else:
            response['status'] = 422
            response['message'] = "Customer id is required."
            
    except IntegrityError as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = f"Something went wrong. {error_message}"
    
    return response