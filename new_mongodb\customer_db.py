import logging
import new_mongodb
from pymongo import DESCENDING
from utils.common import processList

logger = logging.getLogger()

CUSTOMERS_COLLECTION = "customers"
CUSTOMER_GROUPS_COLLECTION = "customer_groups"
LOYALTY_HISTORY_COLLECTION = "loyalty_history"
CUSTOMER_COUPONS_COLLECTION = "customer_coupons"

def fetch_customer_by_id(store, customer_id):
    db = new_mongodb.get_store_db_client(store)
    customer = db[CUSTOMERS_COLLECTION].find_one({"_id": int(customer_id)})
    return customer

def fetch_all_customer_group_id(store):
    db = new_mongodb.get_store_db_client(store)
    customer_groups = db[CUSTOMER_GROUPS_COLLECTION].find({},{"_id":1})
    groups = []
    for g_id in customer_groups:
        groups.append(g_id['_id'])
    return groups

def fetch_customer_group_by_id(store,id):
    db = new_mongodb.get_store_db_client(store)
    customer_groups = db[CUSTOMER_GROUPS_COLLECTION].find_one({"id": id})
    if customer_groups!= None:
       return customer_groups['name']
    else:
        return None

def insert_into_customer(store, data):
    db = new_mongodb.get_store_db_client(store)
    doc = db[CUSTOMERS_COLLECTION].insert_one(data)

def update_customer_by_id(store, data):
    customer_id = data['id']
    db = new_mongodb.get_store_db_client(store)
    doc = db[CUSTOMERS_COLLECTION].update_one({"_id": int(customer_id)}, { "$set": data }, upsert=True)

def remove_customer_by_id(store, customer_id):
    db = new_mongodb.get_store_db_client(store)
    doc = db[CUSTOMERS_COLLECTION].delete_one({'_id': int(customer_id)})

def add_customer_loyalty_history(store, data):
    db = new_mongodb.get_store_db_client(store)
    doc = db[LOYALTY_HISTORY_COLLECTION].insert_one(data)    

def update_customer_coupon_status(store, data):        
    customer_id = data['customer_id']
    coupon_id = data['coupon_id']
    new_enabled_value = False
    db = new_mongodb.get_store_db_client(store)
    doc = db[CUSTOMER_COUPONS_COLLECTION].update_one({"customer_id": int(customer_id), "coupons.coupon_id": int(coupon_id)}, { "$set": {"coupons.$.enabled": new_enabled_value} }, upsert=True)

def fetch_all_customer_with_points(store):
    db = new_mongodb.get_store_db_client(store)
    customers = db[CUSTOMER_COUPONS_COLLECTION].find()
    return customers

def get_customers_using_id(store, customer_ids):
    db = new_mongodb.get_store_db_client(store)
    customers = db[CUSTOMERS_COLLECTION].find({"_id": { "$in": customer_ids}}, {
                                       "id": 1, "email": 1, "name": 1, "company": 1, "phone": 1, "date_created": 1,
                                       "first_name": 1, "last_name": 1, "is_loyalty_point_enable": 1})
    return customers

def get_customers_latest_history(store, customer_ids):
    db = new_mongodb.get_store_db_client(store)    
    pipeline = [
        {
            "$match": {
                "customer_id": { "$in": customer_ids }
            }
        },
        {
            "$sort": {
                "customer_id": 1,
                "created_at": DESCENDING
            }
        },
        {
            "$group": {
                "_id": "$customer_id",
                "latest_entry": { "$first": "$$ROOT" }
            }
        }
    ]
    customer_history = db[LOYALTY_HISTORY_COLLECTION].aggregate(pipeline)       
    return list(customer_history)

def get_customer_from_loyalty_history_using_id(store, customer_id):
    db = new_mongodb.get_store_db_client(store)
    orders = db[LOYALTY_HISTORY_COLLECTION].find({"customer_id":  int(customer_id)}, {"order_id": 1 })
    orders_ids=processList(orders)
    order_ids = [order['order_id'] for order in orders_ids if 'order_id' in order]
    return order_ids

def get_order_reward_points(store, order_id):
    db = new_mongodb.get_store_db_client(store)
    result = db[LOYALTY_HISTORY_COLLECTION].find({"order_id":  int(order_id)}, {
                                       "earned/used": 1, })
    rewards=processList(result)
    rewardSum=0
    for reward in rewards:
        if reward['earned/used'] < 0:
            reward['earned/used'] = 0
        rewardSum += reward['earned/used']
    return rewardSum    

def fetch_loyalty_points_for_customer(store,customer_id):
    db = new_mongodb.get_store_db_client(store)
    res = db[CUSTOMER_COUPONS_COLLECTION].find({"customer_id":int(customer_id)},{
        "loyalty_points":1
    })
    val=processList(res)
    if val:
       loyalty_points=val[0]['loyalty_points']
    else:
       loyalty_points=None 
    return loyalty_points
