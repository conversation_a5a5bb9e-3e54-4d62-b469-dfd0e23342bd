
from sqlalchemy import text
import new_pgdb
from utils.common import calculatePaginationData
from utils.common import convert_to_timestamp, get_order_status_name
import logging
import traceback
from datetime import datetime
from utils import bc
from mongo_db import user_db

logger = logging.getLogger()


def update_order_price_audit(store, order_id, username):
    response = {'status': 200, 'message': 'No need to audit this order'}
    conn = new_pgdb.get_connection(store['id'])
    try:
        res = bc.get_order_products_for_audit_report(store, order_id)
        accepted_prices, order_products = res

        # Quick checks
        if not order_products or not accepted_prices:
            response['status'] = 404
            response['message'] = 'Order not found'

        # Flag to determine if any variant price is invalid
        invalid_price_found = False

        for product in order_products:
            variant_id = product['variant_id']
            base_price = float(product.get('base_price', 0))

            accepted = accepted_prices.get(variant_id, {}).get('accepted_prices', [])

            if not accepted:
                continue

            # Convert accepted prices to floats for safe comparison
            accepted = list(map(float, accepted))

            if base_price not in accepted:
                # Found an invalid price, no need to check further
                invalid_price_found = True
                break

        if invalid_price_found:
            order_details_query = text("""
                SELECT order_status_id, order_status, customer_id, order_created_date_time, total_including_tax FROM orders WHERE order_id = :order_id
            """)
            order_details = conn.execute(order_details_query, {'order_id': order_id}).fetchone()

            # Insert or Update into order_audit_report table
            sql_check = text("""
                SELECT id FROM order_audit_report WHERE order_id = :order_id
            """)
            result = conn.execute(sql_check, {'order_id': order_id}).fetchone()

            if result:
                # Update existing record
                sql_update = text("""
                    UPDATE order_audit_report
                    SET order_status_id = :order_status_id,
                        order_status = :order_status,
                        updated_by = :updated_by,
                        updated_at = :updated_at,
                        order_total = :order_total
                    WHERE order_id = :order_id
                """)
                conn.execute(sql_update, {
                    'order_status_id': order_details[0],
                    'order_status': order_details[1],
                    'updated_by': username,
                    'updated_at': datetime.utcnow(),
                    'order_id': order_id,
                    'order_total': order_details[4]
                })
                conn.commit()
                response['status'] = 200
                response['message'] = 'Order price audit updated successfully'
            else:
                # Insert new record
                sql_insert = text("""
                    INSERT INTO order_audit_report 
                    (order_date, order_id, customer_id, order_status_id, order_status, updated_by, updated_at, order_total)
                    VALUES 
                    (:order_date, :order_id, :customer_id, :order_status_id, :order_status, :updated_by, :updated_at, :order_total)
                """)
                conn.execute(sql_insert, {
                    'order_date': order_details[3],
                    'order_id': order_id,
                    'customer_id': order_details[2],
                    'order_status_id': order_details[0],
                    'order_status': order_details[1],
                    'updated_by': username,
                    'updated_at': datetime.utcnow(),
                    'order_total': order_details[4]
                })
                conn.commit()
                response['status'] = 200
                response['message'] = 'Order price audit updated successfully'

        else:
            # All prices valid — if exists in order_audit_report, delete it
            sql_check = text("""
                SELECT id FROM order_audit_report WHERE order_id = :order_id
            """)
            result = conn.execute(sql_check, {'order_id': order_id}).fetchone()

            if result:
                sql_delete = text("""
                    DELETE FROM order_audit_report WHERE order_id = :order_id
                """)
                conn.execute(sql_delete, {'order_id': order_id})
                conn.commit()
                response['status'] = 200
                response['message'] = 'Order price audit deleted successfully'

    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        conn.close()
    return response



def get_order_price_audit_report(store_id, search, page, limit, sales_rep, sort_array, is_archived):
    response = {
        'status': 400
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        count_query = """SELECT COUNT(*) FROM order_audit_report ar 
                        LEFT JOIN orders o ON ar.order_id::INTEGER = o.order_id
                        LEFT JOIN customers c ON o.customer_id = c.customer_id
                        LEFT JOIN salesforce_customer_rep scr ON o.customer_id = scr.customer_id
                        WHERE 1=1"""

        query = """SELECT 
                    ar.id,
                    ar.order_date,
                    ar.order_id, 
                    o.customer_id, 
                    ar.order_status_id, 
                    ar.order_status, 
                    ar.updated_by, 
                    ar.updated_at,
                    o.total_including_tax,
                    c.first_name || ' ' || c.last_name as customer_name,
                    c.email as customer_email,
                    scr.rep_name as sales_rep_name,
                    scr.rep_email as sales_rep_email,
                    ar.archived_by,
                    ar.note
                FROM order_audit_report ar
                LEFT JOIN orders o ON ar.order_id::INTEGER = o.order_id
                LEFT JOIN customers c ON o.customer_id = c.customer_id
                LEFT JOIN salesforce_customer_rep scr ON o.customer_id = scr.customer_id
                WHERE 1=1"""
        
        # Archive filtering
        if is_archived == 'true':
            query += " AND ar.is_archived = true"
            count_query += " AND ar.is_archived = true"
        else:
            query += " AND (ar.is_archived = false OR ar.is_archived IS NULL)"
            count_query += " AND (ar.is_archived = false OR ar.is_archived IS NULL)"
        
        if search:
            query += " AND (ar.order_id ILIKE :search OR (c.first_name || ' ' || c.last_name) ILIKE :search OR c.email ILIKE :search)"
            count_query += " AND (ar.order_id ILIKE :search OR (c.first_name || ' ' || c.last_name) ILIKE :search OR c.email ILIKE :search)"
        
        if sales_rep:
            query += " AND scr.rep_email ILIKE :sales_rep"
            count_query += " AND scr.rep_email ILIKE :sales_rep"
        
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            if sort_array[0] in ["order_date", "order_id", "updated_at", "updated_by"]:
                query += f" ORDER BY ar.{sort_array[0]} {sort_direction}"
            elif sort_array[0] in ["customer_name", "customer_email", "sales_rep_name", "sales_rep_email"]:
                query += f" ORDER BY {sort_array[0]} {sort_direction}"
            elif sort_array[0] in ["order_status"]:
                query += f" ORDER BY ar.order_status_id {sort_direction}"
            elif sort_array[0] in ["total_inc_tax"]:
                query += f" ORDER BY o.total_including_tax {sort_direction}"
            
        if page and limit:
            page = int(page)
            limit = int(limit)
            offset = (page - 1) * limit
            query += f" LIMIT :limit OFFSET :offset"

        total_records = conn.execute(text(count_query), {"search": f"%{search}%", "sales_rep": f"%{sales_rep}%"}).scalar()
        result = conn.execute(text(query), {"limit": limit, "offset": offset, "search": f"%{search}%", "sales_rep": f"%{sales_rep}%"})
        orders = []
        updated_by_users = set()
        archived_by_users = set()
        for row in result:
            if row[13] and row[13] != "":
                archived_by_users.add(row[13])
            if row[6] and row[6] != "BigCommerce":
                updated_by_users.add(row[6])  # Collect unique usernames
            orders.append({
                "id": row[0],
                "order_date": convert_to_timestamp(row[1]),
                "order_id": row[2],
                "customer_id": row[3],
                "order_status_id": row[4],
                "order_status": get_order_status_name(row[4]),
                "updated_by": row[6],
                "updated_at": convert_to_timestamp(row[7]),
                "total_inc_tax": row[8],
                "customer_name": row[9],
                "customer_email": row[10],
                "sales_rep_name": row[11],
                "sales_rep_email": row[12],
                "archived_by": row[13],
                "note": row[14]
            })
        if archived_by_users:
            archived_user_data_map = user_db.fetch_users_by_usernames(archived_by_users)
            for item in orders:
                if item["archived_by"] != "" and item["archived_by"] is not None:
                    item["archived_by"] = archived_user_data_map.get(item["archived_by"], {}).get("name", "")
        if updated_by_users:
            user_data_map = user_db.fetch_users_by_usernames(updated_by_users)
            for item in orders:
                if item["updated_by"] != "BigCommerce":
                    item["updated_by"] = user_data_map.get(item["updated_by"], {}).get("name", "")
        paginated_data = calculatePaginationData(orders, page, limit, total_records)
        response['status'] = 200
        response['data'] = paginated_data
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        conn.close()
    return response

def update_order_audit_details(store, payload, order_id, username):
    response = {"status": 400}
    conn = new_pgdb.get_connection(store['id'])

    try:
        is_archived = payload.get('is_archived', None)
        note = payload.get('note', '').strip()

        if is_archived is None:
            response['message'] = "Missing 'is_archived' in payload"
            return response

        if is_archived and not note:
            response['message'] = "note is required when archiving an order"
            return response

        query = """
            UPDATE order_audit_report
            SET is_archived = :is_archived,
                archived_by = :archived_by,
                note = :note,
                updated_at = CURRENT_TIMESTAMP
            WHERE order_id = :order_id
        """

        archived_by_value = username if is_archived else ""
        result = conn.execute(text(query), {
            'is_archived': is_archived,
            'archived_by': archived_by_value,
            'note': note,
            'order_id': order_id
        })
        conn.commit()

        if result.rowcount > 0:
            response['status'] = 200
            response['data'] = {"order_id": order_id, "is_archived": is_archived}
        else:
            response['status'] = 404
            response['message'] = "No order audit report found for update"

    except Exception as e:
        logger.error(traceback.format_exc())
        response['status'] = 500
        response['message'] = str(e)
    finally:
        conn.close()

    return response

        
