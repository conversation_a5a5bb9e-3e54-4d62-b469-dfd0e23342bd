from sqlalchemy import text
import pg_db
from mongo_db import user_db
import traceback
import logging
from utils import common
from datetime import datetime
from projects import project_members

logger = logging.getLogger()


def _fetch_project_member_detail(conn, project_id, id):
    query = text(
        f"""SELECT *
            FROM {pg_db.project_access}
            WHERE project_id = :project_id
            AND id = :id;
        """
    )
    query = query.params(project_id=project_id, id=id)
    result = conn.execute(query)
    member_info = []
    for row in result:
        id = row[0]
        project_id = row[1]
        username = row[2]
        is_owner = row[4]
        user_data = user_db.fetch_user_by_username(username)
        if user_data:
            role = user_data.get('role', '')
            name = user_data.get('name', '')
            if is_owner:
                name += " (owner)"
            member_info.append({
                'id': id,
                'project_id': project_id,
                'role': role,
                'name': name
            })
    
    return member_info


def get_member_detail(project_id, id):
    response = {
        "status" :400        
    }
    conn = pg_db.get_connection()
    try:
        data = _fetch_project_member_detail(conn, project_id, id)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['message'] = 'No members found for given project id or id.'
            response['status'] = 404
    finally:
        if conn:
            conn.close()
    return response

def _remove_project_members(conn, project_id, id):
    # Step 1: Fetch the username from project_access based on id and project_id
    query = text(
        f"""SELECT username, is_owner
            FROM {pg_db.project_access}
            WHERE id = :id 
            AND project_id = :project_id;
        """
    )
    query = query.params(id=id, project_id=project_id)
    result = conn.execute(query)
    member = result.fetchone()

    if not member:
        # If no member is found, return False with message
        return False, "Member not found"

    username = member[0]
    is_owner = member[1]

    # Step 2: Check if the user is an owner; if yes, return specific message
    if is_owner:
        return False, "Data deletion failed, Owner cannot be deleted"

    # Step 3: Check if the user is assigned to any cards in the agile_project_cards table
    # card_query = text(
    #     f"""SELECT COUNT(*)
    #         FROM {pg_db.project_cards}
    #         WHERE project_id = :project_id
    #         AND assigned_to = :username;
    #     """
    # )
    # card_query = card_query.params(project_id=project_id, username=username)
    # card_count = conn.execute(card_query).scalar()

    # if card_count > 0:
    #     return False, "User is assigned to project ticket(s) and cannot be deleted"

    # Step 4: Proceed with deletion if the user is not assigned to any card
    delete_query = text(
        f"""UPDATE {pg_db.project_access} SET status = 'deleted' WHERE id = :id AND project_id = :project_id;"""
    )
    delete_query = delete_query.params(id=id, project_id=project_id)
    result = conn.execute(delete_query)
    conn.commit()
    
    # Return True if any rows were deleted
    return result.rowcount > 0, "Data deleted successfully"


def delete_member(project_id, id):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        if id:
            success, message = _remove_project_members(conn, project_id, id)
            if success:
                response['status'] = 200
                response['message'] = message  # Success message (deletion success)
            else:
                response['status'] = 400
                response['message'] = message  # Failure message (owner or assigned user)
        else:
            response['status'] = 400
            response['message'] = "id or project_id is missing."
    finally:
        conn.commit()
        conn.close()

    return response
