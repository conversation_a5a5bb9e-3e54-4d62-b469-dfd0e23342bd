from sqlalchemy import Column, DateTime, String, Integer, Float
import new_pgdb as db

class POReorders(db.Base):
    __tablename__ = db.DBTables.po_reorders_table

    variant_id = Column(Integer, primary_key=True)
    product_id = Column(Integer)
    sku = Column(String)
    quantity = Column(Integer)
    quantity_available = Column(Integer)
    quantity_incoming = Column(Integer)
    incremental_quantity = Column(Integer)
    reorder_point = Column(Integer)
    sale_history_months = Column(Integer)
    days_to_replenishment = Column(Integer)
    modified_by = Column(String)
    modified_at = Column(DateTime)
    suggested_order_qty = Column(Float)
    total_sold = Column(Integer)
    weeks_on_hand = Column(Float)
    turn_rate = Column(Float)
    to_order_qty = Column(Integer)
