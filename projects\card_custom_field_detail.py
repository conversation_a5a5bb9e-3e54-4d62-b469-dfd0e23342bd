from sqlalchemy import text
import pg_db
from mongo_db import user_db
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from datetime import datetime
from decimal import Decimal
from projects import card_custom_field
import logging
import traceback
from utils.common import convert_to_timestamp

logger = logging.getLogger()


def _fetch_field_detail_from_card(conn, project_id, card_id, id):
    data = []
    query = text (
        f"""SELECT * FROM {pg_db.agile_customfield_value} WHERE project_id = :project_id AND card_id = :card_id AND id = :id;
        """
    )
    query = query.params(project_id=project_id, card_id=card_id, id=id)
    result = conn.execute(query)
    for row in result.fetchall():
        row_data = {
            'id': row[0],
            'project_customfield_id': row[1],
            'card_id':row[2],
            'project_id': row[3],
            'str_value': row[4],
            'number_value': float(row[5]) if isinstance(row[5], Decimal) else row[5],
            'created_by': row[6],
            'updated_by': row[7],
            'created_at': convert_to_timestamp(row[8]),
            'updated_at': convert_to_timestamp(row[9])
        }
        data.append(row_data)
    
    return data

def get_field_detail_from_card(project_id, card_id, id):
    response = {
        "status": 400 
    }
    conn = pg_db.get_connection()
    try:
        data = _fetch_field_detail_from_card(conn, project_id, card_id, id)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response



def delete_field_from_card(project_id, card_id, id):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:
        delete_query = text(
            f"""DELETE FROM {pg_db.agile_customfield_value}
                WHERE id = :id
                AND project_id = :project_id
                AND card_id = :card_id;
            """
        )
        delete_query = delete_query.params(id=id, project_id=project_id, card_id=card_id)
        delete_result = conn.execute(delete_query)

        if delete_result.rowcount > 0:
            response['status'] = 200
            response['message'] = "Data deleted successfully."
        else:
            response['status'] = 400
            response['message'] = "Data deletion failed"
    finally:
        if conn:
            conn.commit()
            conn.close()

    return response