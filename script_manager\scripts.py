from datetime import datetime, timezone
from new_mongodb import StoreDBCollections, fetch_one_document_from_storefront_collection, get_store_db_client_for_store_id, insert_document_in_storefront_collection, update_document_in_storefront_collection, delete_documents_from_storefront_collection
import new_utils
from bson import ObjectId
import csv
import io

def get_all_scripts(store_id, body):
    response = {
        "status": 400
    }
    try:
        fields = {
            "_id": 1,
            "name": 1,
            "description": 1,
            "placement": 1,
            "content": 1,
            "status": 1,
            "created_at": 1,
            "created_by": 1,
            "updated_by": 1,
            "updated_at": 1
        }
        body['filterBy'] = ['name', 'description']

        db_client = get_store_db_client_for_store_id(store_id)

        scripts, total_data_length, page, limit = new_utils.get_paginated_records_updated(db_client=db_client, \
                                                collection_name=StoreDBCollections.SCRIPT_MANAGER, payload=body, fields=fields, additional_query='')
        data = new_utils.calculate_pagination(scripts, page, limit, total_data_length)
        response['status'] = 200
        response['data'] = data
    except Exception as e:
        response['message'] = str(e)
    return response

def create_script(store, body, user):
    body['name'] = body['name'].strip()
    body['description'] = body['description'].strip()
    body['placement'] = body['placement']
    body['status'] = "active"
    body['content'] = body['content']
    body['created_by'] = {
        "user_id": str(user['_id']),
        "user_name": user['name']
    }
    body['created_at'] = int(datetime.now(timezone.utc).timestamp())
    body['updated_at'] = int(datetime.now(timezone.utc).timestamp())
    body['updated_by'] = {
        "user_id": str(user['_id']),
        "user_name": user['name']
    }
    
    id = insert_document_in_storefront_collection(store, StoreDBCollections.SCRIPT_MANAGER, body)
    if id:
        return {'message': 'script created successfully.'}, 200
    else:
        return {'message': 'Failed to create script.'}, 400

def get_script_details(store_id, script_id):
    response = {
        "status": 400
    }
    try:
        script = fetch_one_document_from_storefront_collection(store_id, StoreDBCollections.SCRIPT_MANAGER, {"_id": ObjectId(str(script_id))})
        if script:
            response['status'] = 200
            response['data'] = script
        else:
            response['status'] = 404
            response['message'] = 'Script not found.'
    except Exception as e:
        response['message'] = str(e)
    return response

def update_script(store_id, script_id, body, user):
    response = {
        "status": 400
    }
    try:
        body['updated_at'] = int(datetime.now(timezone.utc).timestamp())
        body['updated_by'] = {
            "user_id": str(user['_id']),
            "user_name": user['name']
        }
        update_data = { 
            "$set": body
        }
        script = fetch_one_document_from_storefront_collection(store_id, StoreDBCollections.SCRIPT_MANAGER, {"_id": ObjectId(str(script_id))})
        if script:
            update_document_in_storefront_collection(store_id, StoreDBCollections.SCRIPT_MANAGER, {"_id": ObjectId(str(script_id))}, update_data)
            response['status'] = 200
            response['message'] = 'Script updated successfully.'
        else:
            response['status'] = 404
            response['message'] = 'Script not found.'
    except Exception as e:
        response['message'] = str(e)
    return response

def delete_script(store_id, script_id, user):
    response = {
        "status": 400
    }
    try:
        script = fetch_one_document_from_storefront_collection(store_id, StoreDBCollections.SCRIPT_MANAGER, {"_id": ObjectId(str(script_id))})
        if script:
            delete_documents_from_storefront_collection(store_id, StoreDBCollections.SCRIPT_MANAGER, {"_id": ObjectId(str(script_id))})
            response['status'] = 200
            response['message'] = 'Script deleted successfully.'
        else:
            response['status'] = 404
            response['message'] = 'Script not found.'
    except Exception as e:
        response['message'] = str(e)
    return response

def import_loyalty_points(store_id, csv_file):
    response = {
        "status": 400
    }
    try:
        # Read and validate CSV file
        if not csv_file or not csv_file.filename.endswith('.csv'):
            response['message'] = 'Please upload a valid CSV file.'
            return response
        
        uploaded_file = csv_file.read()
        
        # Validate CSV structure
        required_fields = ['Email', 'Points Balance']
        missing_fields = []
        
        with io.StringIO(uploaded_file.decode('utf-8')) as file:
            reader = csv.DictReader(file)
            
            # Check for required fields
            for field in required_fields:
                if field not in reader.fieldnames:
                    missing_fields.append(field)
            
            if missing_fields:
                response['message'] = f"Missing required fields in CSV: {', '.join(missing_fields)}"
                return response
            
            # Process CSV data
            csv_data = []
            for row in reader:
                email = row['Email'].strip().lower() if row['Email'] else ''
                points_balance = row['Points Balance'].strip() if row['Points Balance'] else '0'
                
                if email and points_balance:
                    try:
                        points_balance = int(points_balance)
                        csv_data.append({
                            'email': email,
                            'points_balance': points_balance
                        })
                    except ValueError:
                        response['message'] = f"Invalid points balance value for email {email}: {row['Points Balance']}"
                        return response
        
        if not csv_data:
            response['message'] = 'No valid data found in CSV file.'
            return response
        
        # Get database client
        db_client = get_store_db_client_for_store_id(store_id)
        
        # Extract all emails from CSV
        emails = [item['email'] for item in csv_data]
        
        # Fetch customer IDs from customers collection in one shot
        customers = list(db_client['customers'].find(
            {"email": {"$in": emails}}, 
            {"_id": 1, "email": 1}
        ))
        
        # Create email to customer_id mapping
        email_to_customer_id = {customer['email'].lower(): customer['_id'] for customer in customers}
        
        # Track processing results
        processed_count = 0
        skipped_count = 0
        errors = []
        
        current_timestamp = int(datetime.now(timezone.utc).timestamp())
        
        # Process each CSV row
        for csv_row in csv_data:
            email = csv_row['email']
            points_balance = csv_row['points_balance']
            
            customer_id = email_to_customer_id.get(email)
            
            if not customer_id:
                errors.append(f"Customer not found for email: {email}")
                skipped_count += 1
                continue
            
            try:
                # Check if customer exists in customer_coupons_temp collection
                existing_customer_coupon = db_client['customer_coupons_temp'].find_one({"customer_id": customer_id})
                
                if existing_customer_coupon:
                    # Update existing record
                    update_data = {
                        "loyalty_points": points_balance,
                        "updated_at": current_timestamp
                    }
                    
                    db_client['customer_coupons_temp'].update_one(
                        {"customer_id": customer_id},
                        {"$set": update_data}
                    )
                else:
                    # Create new record
                    new_customer_coupon = {
                        "customer_id": customer_id,
                        "coupons": [],
                        "loyalty_points": points_balance,
                        "created_at": current_timestamp,
                        "updated_at": current_timestamp
                    }
                    
                    db_client['customer_coupons_temp'].insert_one(new_customer_coupon)
                
                # Create loyalty history entry
                loyalty_history_entry = {
                    "customer_id": customer_id,
                    "balance": points_balance,
                    "earned/used": points_balance,
                    "created_at": current_timestamp,
                    "description": f"Earned {points_balance} Points through CSV import.",
                    "coupon_code": "",
                    "coupon_id": 0,
                    "order_id": 0
                }
                
                db_client['loyalty_history_temp'].insert_one(loyalty_history_entry)
                
                processed_count += 1
                
            except Exception as e:
                errors.append(f"Error processing email {email}: {str(e)}")
                skipped_count += 1
        
        # Prepare response
        response['status'] = 200
        response['message'] = f'Loyalty points imported successfully. Processed: {processed_count}, Skipped: {skipped_count}'
        
        if errors:
            response['errors'] = errors[:10]  # Limit to first 10 errors
            if len(errors) > 10:
                response['errors'].append(f"... and {len(errors) - 10} more errors")
        
    except Exception as e:
        response['message'] = f'Error processing CSV file: {str(e)}'
    
    return response