import new_mongodb

CALLBACK_COLLECTION = "callbacks"

def create_callback(store, payload):
    db = new_mongodb.get_store_db_client(store)
    result=db[CALLBACK_COLLECTION].update_one({"_id": payload["_id"]}, {"$set": payload}, upsert=True)
    return result

def get_order_payments(store, order_ids=[]):
    db = new_mongodb.get_store_db_client(store)
    order_ids = [str(id) for id in order_ids]
    query = {
        "_id": {
            "$in": order_ids
        }
    }
    projection = {
        "payload": 1
    }
    cur = db[CALLBACK_COLLECTION].find(query,projection)
    res = []
    if cur:
        for order in cur:
            res.append(order['payload'])
    return res
