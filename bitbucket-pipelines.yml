pipelines:
  branches:
    stage:
      - step:
          name: Build and Push Docker Image
          runs-on:
            - 'self.hosted'
            - 'linux.shell'
            - 'stage.runner'
          script:
            - echo "$BIT_KEY" > stage_key.json
            - gcloud auth activate-service-account --project $PROJECT_ID --key-file=stage_key.json
            - gcloud auth configure-docker us-central1-docker.pkg.dev
            - echo Building the Docker image...
            - docker build -t us-central1-docker.pkg.dev/$PROJECT_ID/$REPOSITORY/$BITBUCKET_REPO_SLUG:$BITBUCKET_BRANCH-$BITBUCKET_BUILD_NUMBER --file Dockerfile .
            - docker push us-central1-docker.pkg.dev/$PROJECT_ID/$REPOSITORY/$BITBUCKET_REPO_SLUG:$BITBUCKET_BRANCH-$BITBUCKET_BUILD_NUMBER

      - step:
          name: Deploy to GCP VM
          runs-on:
            - 'self.hosted'
            - 'linux.shell'
            - 'stage.runner'
          script:
            - echo "$BIT_KEY" > stage_key.json
            - gcloud auth activate-service-account --project $PROJECT_ID --key-file=stage_key.json
            - gcloud auth configure-docker us-central1-docker.pkg.dev
            - docker pull us-central1-docker.pkg.dev/$PROJECT_ID/$REPOSITORY/$BITBUCKET_REPO_SLUG:$BITBUCKET_BRANCH-$BITBUCKET_BUILD_NUMBER
            # - docker stop docker-runner || true && docker rm docker-runner || true
            # - docker run -p 3001:80 -d --name docker-runner us-central1-docker.pkg.dev/$PROJECT_ID/$REPOSITORY/$BITBUCKET_REPO_SLUG:$BITBUCKET_BRANCH-$BITBUCKET_BUILD_NUMBER
