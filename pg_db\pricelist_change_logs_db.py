from sqlalchemy.dialects.postgresql import insert
import pg_db as db
from sqlalchemy import Boolean, Column, DateTime, Numeric, String, Integer, ForeignKey, Enum, Interval, Float
from sqlalchemy.sql import func

class PricelistChangeLogs(db.Base):
    __tablename__ = "pricelist_change_logs"

    id = Column(Integer, primary_key=True, autoincrement=True)
    price_list_id = Column(Integer)
    product_id = Column(Integer)
    variant_id = Column(Integer)
    parent_product_name = Column(String)
    parent_product_sku = Column(String)
    variant_name = Column(String)
    variant_sku = Column(String)
    old_price = Column(Float)
    updated_price = Column(Float)
    is_active = Column(Boolean)
    updated_by = Column(String)
    updated_at = Column(String)

