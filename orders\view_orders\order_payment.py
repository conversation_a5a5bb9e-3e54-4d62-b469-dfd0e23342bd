from datetime import datetime, timezone
import plugin
from plugin.bc_products import process_options
from sqlalchemy import text
import new_pgdb
from utils import bc, store_util
from new_pgdb import blocked_orders_db
from graphql import products_variant_query
import new_utils
from utils import auth_util
import json 
from utils.common import get_order_status_name
import logging
from plugin import bc_order, bc_products
from utils.common import convert_to_timestamp
import task
from orders import get_order_by_order_id
from new_mongodb.order_db import update_order_payment_status

logger = logging.getLogger()

def update_order_payment(store, request_body):
    conn = new_pgdb.get_connection(store['id'])
    try:
        customer_id = None
        customer_id_query = "SELECT customer_id FROM orders WHERE order_id = :order_id"
        customer_id_result = conn.execute(text(customer_id_query), {"order_id": request_body["_id"]})
        result = customer_id_result.fetchone()
        
        if result:
            customer_id = result[0]
        
        if not customer_id:
            order = get_order_by_order_id(store, request_body["_id"])
            customer_id = order.get("customer_id", None) if order else None
        
        request_body["customer_id"] = customer_id
        update_order_payment_status(store, request_body)
        return True
    except Exception as e:
        logger.error(f"Error updating order payment: {e}")
        return False
    finally:
        conn.close()
