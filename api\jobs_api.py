from flask import request
import logging
import traceback
from api import APIResource
from settings import job_service

logger = logging.getLogger()

class Job(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Job GET")
        try:
            query_params = request.args.to_dict()
            job_id = ''
            if 'job_id' in query_params:
                job_id = query_params.get('job_id', '')
            if job_id != '':  
                res = job_service.get_job(job_id, store)
                if res['status'] == 200:
                    return res['data'], 200
            else:
                return {"message": "Please provide job_id"}, 400
        finally:
            logger.debug("Exiting Job GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)

class Jobs(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Jobs GET")
        try:
          query_params = request.args.to_dict()
          res = job_service.get_jobs(store, query_params)
          
          # Return response ...
          return res, 200
        finally:
            logger.debug("Exiting Jobs GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class JobDetails(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Job details GET")
        try:
          query_params = request.args.to_dict()
          res = job_service.get_job_details(query_params)
          
          # Return response ...
          return res, 200
        finally:
            logger.debug("Exiting Job details GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)