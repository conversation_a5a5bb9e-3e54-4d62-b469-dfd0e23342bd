from new_mongodb import fetchall_documents_from_admin_collection, process_documents, update_document_in_admin_collection
from mongo_db import user_db
from datetime import datetime
from bson import ObjectId

STORE_INFO_DB = "store_info"

def get_data(store_id, cdn_baseurl):
        response = {
            'status': 400,            
        }
        store_info = fetchall_documents_from_admin_collection(store_id, STORE_INFO_DB, {})                 
        store_info = process_documents(store_info)

        if store_info:
            if 'content' in store_info:
                content = store_info['content']
                if 'header_info' in content:
                    header_info = content['header_info']
                    if 'header_logo' in header_info:
                        if header_info['header_logo'] != '':
                            header_info['header_logo'] = cdn_baseurl + '/storeinfo' + header_info['header_logo'].replace('/storeinfo/images', '')                   
                    if 'header_pencil_banner' in header_info:
                        if header_info['header_pencil_banner'] != '':
                            header_info['header_pencil_banner'] = cdn_baseurl + '/storeinfo' + header_info['header_pencil_banner'].replace('/storeinfo/images', '')
                    if 'mobile_header_pencil_banner' in header_info:
                        if header_info['mobile_header_pencil_banner'] != '':
                            header_info['mobile_header_pencil_banner'] = cdn_baseurl + '/storeinfo' + header_info['mobile_header_pencil_banner'].replace('/storeinfo/images', '')
                
                if 'age_verification_info' in content:
                     age_verification_info = content['age_verification_info']
                     if 'aging_img' in age_verification_info:
                         if age_verification_info['aging_img'] != '':
                             age_verification_info['aging_img'] = cdn_baseurl + '/storeinfo' + age_verification_info['aging_img'].replace('/storeinfo/images', '')
                             
            response['data'] = store_info
            response['status'] = 200
        else:
            response['message'] = "Data not found"
            response['status'] = 403

        return response

def set_data(store_id, body):
  response = {
    'status': 400,
    'message': 'Data is not saved successfully'
  }
  created_by = {}
  if 'created_by' in body:
    user = user_db.fetch_user_by_username(body['created_by'])        
    created_by = {
      "user_id": str(user['_id']),
      "user_name": user['name']
    }
        
  content = {
    'header_info': body['header_info'],
    'footer_info': body['footer_info'],
    'age_verification_info': body['age_verification_info']
  }
  query = {"_id": ObjectId(str(body['id']))}
  data = {"$set": {  
    "content": content,                                                                                                                                                                                                                                   
    "created_by": created_by,
    "updated_at":  int(datetime.utcnow().timestamp())
  }}
  id = update_document_in_admin_collection(store_id, STORE_INFO_DB, query, data)
  response['message'] = "Data saved successfully"
  response['status'] = 200
    
  return response