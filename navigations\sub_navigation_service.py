from bson import ObjectId
from new_mongodb import fetch_one_document_from_storefront_collection, \
  update_document_in_storefront_collection \
  ,delete_documents_from_storefront_collection \
  ,insert_document_in_storefront_collection
from new_mongodb.storefront_db import SUB_NAVIGATIONS
from datetime import datetime

def delete_sub_navigation_by_id(store_id, nav_id):
    query = {"navigation_id": ObjectId(str(nav_id))}
    res = delete_documents_from_storefront_collection(store_id, SUB_NAVIGATIONS, query)
    return res.deleted_count

def create_sub_nav(store, id, name, short_code):
    sub_nav = {}
        
    sub_nav["created_at"] = int(datetime.utcnow().timestamp())
    sub_nav["updated_at"] = ''
    sub_nav['navigation_id'] = ObjectId(str(id))
    sub_nav['navigations'] = []
    sub_nav['name'] = name
    sub_nav['short_code'] = short_code
    sub_nav['status'] = "active"

    if len(sub_nav['navigations']):
        for idx, x in enumerate(sub_nav['navigations']):
            x['sort_order'] = idx + 1

    id = insert_document_in_storefront_collection(store, SUB_NAVIGATIONS, sub_nav)
    return id

def update_sub_navigation_name(store_id, nav_id, name):
    query = {"navigation_id": ObjectId(str(nav_id))}
    data = { 
        "$set": {
            'name': name,
            "updated_at": int(datetime.utcnow().timestamp())
        }
    }

    id = update_document_in_storefront_collection(store_id, SUB_NAVIGATIONS, query, data)
    return id

def get_sub_nav(store_id, nav_id):
  query = {
    "navigation_id": ObjectId(str(nav_id)),
    "status": "active"
  }
  print(nav_id,store_id)
  res = fetch_one_document_from_storefront_collection(store_id, SUB_NAVIGATIONS, query)
  
  return res

def update_sub_nav(store_id, body, nav_id):
  
  def add_unique_id(data, parent_id=''):
    for index, item in enumerate(data):
      child_id = parent_id + str(index+1)
      
      if item['type']=='Brands':
        item['id']=item['id']
      elif item['type']=='Categories':
        item['id']=item['id']
      elif item['type']=='Web Pages':
        item['id']=item['id']
      else:
        item['id'] = child_id
      if item['children']:
        add_unique_id(item['children'], child_id)
        
  add_unique_id(body)
        
  for idx, x in enumerate(body):
    x['sort_order'] = idx + 1
    
    for idcx, cx in enumerate(x['children']):   
      temp = str(x['sort_order']) + '_'             
      cx['sort_order'] = temp + str(idcx + 1)
      for idscx, scx in enumerate(cx['children']):
        temp2 = str(cx['sort_order']) + '_'             
        scx['sort_order'] = temp2 + str(idscx + 1)    
        
    query = {"navigation_id": ObjectId(str(nav_id))}
    data = {"$set": {
                'navigations': body,
                "updated_at": int(datetime.utcnow().timestamp())
            }}
    id = update_document_in_storefront_collection(store_id, SUB_NAVIGATIONS, query, data)
      
    return id

def get_navigation_by_short_code(store_id, short_code):
  query = {
    "short_code": str(short_code),
    "status": "active"
  }
  res = fetch_one_document_from_storefront_collection(store_id, SUB_NAVIGATIONS, query)
  return res