from datetime import datetime, timezone
from new_mongodb import fetchall_documents_from_storefront_collection, insert_document_in_storefront_collection, process_data ,fetch_one_document_from_storefront_collection 


def get_all_builds(store_id):
  navigations = fetchall_documents_from_storefront_collection(store_id, "builds", {})
  res = process_data(navigations)
        
  for nav in res:
    nav['created_at'] = datetime.fromtimestamp(nav['created_at'], timezone.utc).strftime('%m/%Y, %H:%M%p')            

  return res


def create_build(store, body):
  response = {
    "status": 400
  }  
  store_id = store['id']
  query = {"deploy_id": str(body['deploy_id']), "status": "active"}
  build = fetch_one_document_from_storefront_collection(store_id, "builds", query)
  
  if build:
    response['message'] = "can't enter duplicate entry"
    response['status'] = 409 
    return response
        
  body["created_at"] = int(datetime.now(timezone.utc).timestamp())   
  body["status"] = "active"              
  id = insert_document_in_storefront_collection(store, "builds", body)
  if id:       
    response['message'] = str(id)
    response['status'] = 200
  else:
    response["message"] = "Failed to insert document"
    response["status"] = 500 

  return response
