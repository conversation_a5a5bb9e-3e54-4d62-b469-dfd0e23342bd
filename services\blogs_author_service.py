from services import Service
from utils.common import parse_json
from bson import ObjectId
from datetime import datetime
from flask import send_file, make_response
import os
from werkzeug.utils import secure_filename
import logging
import requests

logger = logging.getLogger()


class Authors(Service):
    def get_authors(self, body):        
        result = []
        if(body): 
            res={}                  
            author_id = body['author_id']
            author = super().find_one({"_id": ObjectId(str(author_id))})   
                                                           
            res['id'] = author['id']
            res['name'] = author['name']
            res['description'] = author['description']
            res['created_at'] = author['created_at']
            res['updated_at'] = author['updated_at']
            res['status'] = author['status']                      

            result.append(res)
        else:
            authors = super().find_all()                 
            authors = parse_json(self.processList(authors))             
            for author in authors:
                res = {}                                      
                
                res['id'] = author['id']
                res['name'] = author['name']
                res['description'] = author['description']
                res['created_at'] = author['created_at']
                res['updated_at'] = author['updated_at']               
                res['status'] = author['status']                                                                                    

                result.append(res)              
        
        return result

    def create_author(self, body):
        response = {
            "status": 400
        }                              
        isUniqueName =  self.checkForUniqueAuthorName(body['name'].strip(), '')   
        if isUniqueName:
            body["created_at"] = int(datetime.utcnow().timestamp())
            body["updated_at"] = ""           

            body['name'] = body['name'].strip()                                      
            id = super().create(body)

            response['message'] = "Author created successfully"
            response['status'] = 200
        else:
            response['message'] = "The provided author name has already been matched with other authors. Please provide a different author name."
            response['status'] = 409   
           
        return response
    
    def update_author(self, body, author_id=None):
        response = {
            "status": 400
        }        
        if body['status_update'] == 'false':
            isUniqueName =  self.checkForUniqueAuthorName(body['name'].strip(), author_id)   
        else:
            isUniqueName = True

        if isUniqueName:
            id = super().update_one({"_id": ObjectId(str(author_id))}, { "$set": 
                {
                    "name": body['name'].strip(),
                    "description": body['description'],
                    "status": body['status'],                    
                    "updated_at":  int(datetime.utcnow().timestamp())
                }
            })
            response['message'] = "Author Updated successfully"
            response['status'] = 200
        else:
            response['message'] = "The provided author name has already been matched with other authors. Please provide a different author name."
            response['status'] = 409

        return response

    
    def get_author(self, author_id=None):   
        result = {}                      
        author = super().find_one({"_id": ObjectId(str(author_id))})                     
                
        result['id'] = author['id']
        result['name'] = author['name']
        result['description'] = author['description']
        result['created_at'] = author['created_at']
        result['updated_at'] = author['updated_at']  
        result['status'] = author['status']              
          

        return result

    def delete_by_id(self, author_id):
        return super().delete({"_id": ObjectId(str(author_id))})
    
    def checkForUniqueAuthorName(self, name, author_id):  
        name = name.replace(" ", "").lower()
        # url = '/pages/' + url.replace(" ", "_").lower()                  
        authors = super().find_all()                 
        authors = parse_json(self.processList(authors))  
        for author in authors:  
            if not author['id'] == author_id:
                if author['name'].replace(" ", "").lower().strip() == name:
                    return False
        return True