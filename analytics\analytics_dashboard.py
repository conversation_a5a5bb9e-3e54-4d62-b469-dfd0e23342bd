from sqlalchemy import text
import new_pgdb
import logging
import traceback
from new_pgdb.analytics_db import AnalyticsDB
from appconfig import is_pgdb_read_only_enabled
logger = logging.getLogger()


def get_summary(store, start_date, end_date):
    response = {
        "status" :400
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        query = text (
            f"""SELECT
                    COUNT(DISTINCT order_id) AS order_count,
                    SUM(total) AS earning
                FROM
                    {AnalyticsDB.get_products_trend_table()}
                WHERE
                    order_date_time BETWEEN :start_date AND :end_date
            """
        )
        results = conn.execute(query.params(start_date=start_date, end_date=end_date))
        data = []
        for row in results.fetchall():
            order_count = "{:,.0f}".format(row[0])
            earning = 0
            if row[1]:
                if row[1] >= 1000000:
                    earning = "{:.1f}M".format(row[1] / 1000000)
                elif row[1] >= 1000:
                    earning = "{:.1f}k".format(row[1] / 1000)
                else:
                    earning = "{:,.0f}".format(row[1])

            row_data = {
                'order_count': order_count,
                'earning': earning
            }
            data.append(row_data)

        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response

def get_top_products(store, start_date, end_date):
    response = {
        "status" :400
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        query = text (
            f"""SELECT 
                    product_id, 
                    product_name, 
                    SUM(total) AS revenue, 
                    SUM(quantity) AS quantity,
                    COUNT(order_id) AS order_count, 
                    AVG(price) AS price
                FROM 
                    {AnalyticsDB.get_products_trend_table()}
                WHERE 
                    order_date_time BETWEEN :start_date AND :end_date
                GROUP BY 
                    product_id, 
                    product_name
                ORDER BY 
                    revenue DESC
                LIMIT 10;
            """
        )
        results = conn.execute(query.params(start_date=start_date, end_date=end_date))
        data = []
        for row in results.fetchall():
            revenue = "{:,.0f}".format(row[2])
            quantity_sold = "{:,.0f}".format(row[3])
            row_data = {
                'product_id': row[0],
                'product_name': row[1],
                'revenue': revenue,
                'quantity_sold': quantity_sold,
                'order_count': row[4]
            }
            data.append(row_data)

        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response


def get_top_customers(store, start_date, end_date):
    response = {
        "status" :400
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        query = text (
            f"""SELECT 
                    tr.customer_id, 
                    cu.first_name, 
                    cu.last_name, 
                    SUM(tr.total) AS revenue, 
                    COUNT(DISTINCT tr.order_id) AS order_count
                FROM 
                    {AnalyticsDB.get_products_trend_table()} tr
                JOIN
                    customers cu ON tr.customer_id = cu.customer_id 
                WHERE
                    tr.order_date_time BETWEEN :start_date AND :end_date
                GROUP BY 
                    tr.customer_id, 
                    cu.first_name, 
                    cu.last_name
                ORDER BY 
                    revenue DESC
                LIMIT 10;
            """
        )
        results = conn.execute(query.params(start_date=start_date, end_date=end_date))
        data = []
        for row in results.fetchall():
            full_name = f"{row[1]} {row[2]}"
            revenue = "{:,.0f}".format(row[3])
            row_data = {
                'customer_id': row[0],
                'name': full_name,
                'revenue': revenue,
                'order_count': row[4]
            }
            data.append(row_data)

        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response


def get_top_states(store, start_date, end_date):
    response = {
        "status" :400
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        query = text (
            f"""SELECT
                    sh.state,
                    SUM(pt.total) AS revenue,
                    COUNT(DISTINCT pt.order_id) AS order_count
                FROM
                    {AnalyticsDB.get_products_trend_table()} pt
                JOIN 
                    order_shipping_addresses sh ON pt.order_id = sh.order_id
                WHERE
                    pt.order_date_time BETWEEN :start_date AND :end_date
                    AND sh.state != ''
                GROUP BY
                    sh.state
                LIMIT 10;
            """
        )
        results = conn.execute(query.params(start_date=start_date, end_date=end_date))
        data = []
        for row in results.fetchall():
            revenue = "{:,.0f}".format(row[1])
            row_data = {
                'state': row[0],
                'revenue': revenue,
                'order_count': row[2],
            }
            data.append(row_data)

        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response