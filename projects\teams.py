from sqlalchemy import text
import pg_db
from mongo_db import user_db
from datetime import datetime
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from projects.project_modules import _insert_new_module
from projects.project_columns import add_new_column
from projects.default_columns import _fetch_default_coulmns
from utils.common import calculatePaginationData, convert_to_timestamp
import logging
import traceback
from projects.project_members import add_new_member

logger = logging.getLogger()


def create_team(email, name, usernames):
    response = {"status": 400}
    conn = pg_db.get_connection()
    try:
         # Check if a team with the same name already exists
        check_query = text(
            """
            SELECT COUNT(*) FROM agile_project_teams
            WHERE name = :name;
            """
        )
        team_exists = conn.execute(check_query, {"name": name}).scalar()

        if team_exists > 0:
            response["message"] = "name: A team with the same name already exists."
            response["status"] = 409
            return response
        
        team_query = text(
            f"""INSERT INTO agile_project_teams (name, status, created_by, created_at)
                VALUES (:name, 'active', :email, CURRENT_TIMESTAMP)
                RETURNING id;
            """
        )
        team_id = conn.execute(team_query, {"name": name, "email": email}).scalar()
        conn.commit()

        if not team_id:
            response["message"] = "Failed to create team."
            return response
        
        # Insert into `agile_project_team_members` for each username
        members_query = text(
            """
            INSERT INTO agile_project_team_members (team_id, username)
            VALUES (:team_id, :username);
            """
        )
        for username in usernames:
            conn.execute(members_query, {"team_id": team_id, "username": username})
        conn.commit()

        response["status"] = 200
        response["message"] = "Team and members created successfully."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response

def get_teams(search=None, status=None, sort_array=[]):
    response = {"status": 400}
    conn = pg_db.get_connection()
    try:
        # Base query for fetching teams with additional counts
        query = """
        SELECT 
            teams.id AS team_id,
            teams.name AS team_name,
            teams.status,
            teams.created_by,
            teams.created_at,
            COUNT(DISTINCT members.username) AS member_counts,
            COUNT(DISTINCT access.project_id) AS project_counts
        FROM agile_project_teams AS teams
        LEFT JOIN agile_project_team_members AS members
            ON teams.id = members.team_id
        LEFT JOIN agile_project_access AS access
            ON teams.id = access.team_id
        """

        # Add search condition if `search` is provided
        if search:
            query += f" WHERE teams.name ILIKE '%" + search + "%'"

        if status and status != '':
            query += f" WHERE teams.status = '{status}'"

        # Add GROUP BY to aggregate counts
        query += """
        GROUP BY teams.id
        """

        # Add sorting if `sort_array` is provided
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ["status", "created_at"]:                
                query += f" ORDER BY teams.{sort_array[0]} {sort_direction}"

        # Execute the query and fetch results
        results = conn.execute(text(query)).fetchall()

        # Format response
        data = [
            {
                "id": row[0],
                "name": row[1],
                "status": row[2],
                "created_by": row[3],
                "created_at": convert_to_timestamp(row[4]),
                "member_counts": row[5],
                "project_counts": row[6],
            }
            for row in results
        ]

        response["status"] = 200
        response["data"] = data
    except Exception as e:
        response["status"] = 500
        response["message"] = f"An error occurred: {str(e)}"
    finally:
        if conn:
            conn.close()
    return response

def get_team_detail(team_id):
    response = {"status": 400}
    conn = pg_db.get_connection()
    try:
        query = text(
            """
            SELECT username FROM agile_project_team_members WHERE team_id = :team_id
            """
        )
        results = conn.execute(query, {"team_id": team_id}).fetchall()
        usernames = []
        for row in results:
            user_data = user_db.fetch_user_by_username(row[0])
            if user_data:
                role = user_data.get('role', '')
                name = user_data.get('name', '')
                usernames.append({'name': name, 'username': row[0], 'role': role})
        
        usernames = sorted(usernames, key=lambda x: x["name"].lower())
        response["status"] = 200
        response["data"] = usernames
    finally:
        if conn:
            conn.close()
    return response


def update_team(payload, username, team_id):
    response = {"status": 400}
    conn = pg_db.get_connection()
    try:
        update_fields = {}
        for field in ['name', 'usernames', 'status']:
            if field in payload:
                update_fields[field] = payload[field]
        
        # Only include fields that are valid for `agile_project_teams`
        valid_team_fields = ['name', 'status']

        # If 'name' or 'status' is in the payload, update `agile_project_teams`
        if any(field in update_fields for field in valid_team_fields):
            if 'name' in update_fields:
                # Check if a team with the same name already exists
                check_query = text(
                    """
                    SELECT COUNT(*) FROM agile_project_teams
                    WHERE name = :name AND id != :id;
                    """
                )
                team_exists = conn.execute(check_query, {"name": update_fields['name'], "id": team_id}).scalar()

                if team_exists > 0:
                    response["message"] = "name: A team with the same name already exists."
                    response["status"] = 409
                    return response

            # Build the set_clause dynamically for valid fields
            set_clause = ", ".join([f"{field} = :{field}" for field in valid_team_fields if field in update_fields])
            query = text(
                f"""UPDATE agile_project_teams
                    SET {set_clause},
                        updated_by = :updated_by,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = :id"""
            )
            params = {field: update_fields[field] for field in valid_team_fields if field in update_fields}
            params.update({'updated_by': username, 'id': team_id})
            conn.execute(query, params)
            conn.commit()

        # If 'usernames' is in the payload, update `agile_project_team_members`
        if 'usernames' in update_fields:
            query = text("DELETE FROM agile_project_team_members WHERE team_id = :team_id")
            conn.execute(query, {"team_id": team_id})
            conn.commit()

            members_query = text(
                """INSERT INTO agile_project_team_members (team_id, username)
                    VALUES (:team_id, :username)"""
            )
            for user in update_fields['usernames']:
                conn.execute(members_query, {"team_id": team_id, "username": user})
            conn.commit()
            
        response["status"] = 200
        response["message"] = "Team updated successfully."
    except Exception as e:
        response["status"] = 500
        response["message"] = f"An error occurred: {str(e)}"
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response

def delete_team(team_id):
    response = {"status": 400}
    conn = pg_db.get_connection()
    try:
        members_query = text("DELETE FROM agile_project_team_members WHERE team_id = :team_id")
        conn.execute(members_query, {"team_id": team_id})
        conn.commit()
        
        query = text("DELETE FROM agile_project_teams WHERE id = :team_id")
        results = conn.execute(query, {"team_id": team_id})
        conn.commit()
        if results.rowcount > 0:
            response["status"] = 200
            response["message"] = "Team deleted successfully."
        else:
            response["status"] = 404
            response["message"] = "Team not found."
    finally:
        conn.commit()
        conn.close()
    return response

def add_teams_to_project(team_ids, project_id):
    response = {"status": 400}
    conn = pg_db.get_connection()
    try:
        for team_id in team_ids:
            # Query to fetch usernames from agile_project_team_members
            query_team_members = text("""
                SELECT username FROM agile_project_team_members WHERE team_id = :team_id
            """)
            team_members = conn.execute(query_team_members, {"team_id": team_id}).fetchall()

            if team_members:
                # Extract usernames from the query result
                usernames = [row[0] for row in team_members]

                # Query to check if any username already exists in agile_project_access
                query_existing_users = text("""
                    SELECT username FROM agile_project_access 
                    WHERE project_id = :project_id AND username = ANY(:usernames)
                """)
                existing_users = conn.execute(
                    query_existing_users, 
                    {"project_id": project_id, "usernames": usernames}
                ).fetchall()

                # Extract existing usernames to exclude
                existing_usernames = {row[0] for row in existing_users}

                # Filter out usernames that are already associated with the project
                new_usernames = [username for username in usernames if username not in existing_usernames]

                if new_usernames:
                    # Add only new usernames to the project
                    add_new_member(new_usernames, project_id)
                response['status'] = 200
                response['message'] = 'Teams added successfully.'
            else:
                response['status'] = 404
                response['message'] = 'Team not found.'
    finally:
        conn.commit()
        conn.close()
    return response

