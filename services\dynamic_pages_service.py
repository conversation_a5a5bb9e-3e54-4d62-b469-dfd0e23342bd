from plugin import bc_products
from services import Service
from utils.common import parse_json, calculatePaginationData, processList
from bson import ObjectId
from datetime import datetime
from flask import send_file, make_response
import os
from werkzeug.utils import secure_filename
import logging
import requests
from fields.dynamic_pages_fields import page_fields
from mongo_db import cms_db, user_db
from PIL import Image

logger = logging.getLogger()

ALLOWED_EXTENSIONS = set(['png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov', 'wmv', 'mkv'])


def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def change_file_name(filename):
    rendom = str(round(datetime.utcnow().timestamp()))
    if ('.' in filename):
        ext = filename.rsplit('.', 1)[1].lower()
        fname = rendom + '.' + ext
        return fname


class Pages(Service):
    def get_pages(self, store, body, cdn_baseurl):        
        result = []        
        if 'webpage_id' in body:
            res = {}
            webpage_id = body['webpage_id']
            webPage = super().find_one({"_id": ObjectId(str(webpage_id))})
            if 'preview_state' in webPage and webPage['preview_state']:               
                preview_state = webPage['preview_state']
            else:
                preview_state = webPage['default_layout']

            preview_state =  self.addCdnToImageUrl(cdn_baseurl, preview_state)

            res['id'] = webPage['id']
            res['name'] = webPage['name']
            res['created_at'] = webPage['created_at']
            res['updated_at'] = webPage['updated_at']
            res['url'] = webPage['url']
            res['load_iframe'] = webPage['load_iframe']
            res['is_customers_only'] = webPage['is_customers_only']
            res['versions'] = preview_state

            result.append(res)
            return result
        else:            
            body['filterBy'] = ['name']            
            pages, total_data_length, paginationPage, limit = cms_db.get_paginated_records_updated(store, body, page_fields,'')

            webpages = super().find_all()
            nameResult = []
            for page in webpages:
                test = {}
                test['Name'] = page['name']
                test['URL'] = page['url']
                nameResult.append(test)

            for page in pages:
                res = {}
                versions = page['versions']
                if (len(versions) == 0):
                    activeVersion = page['default_layout']
                else:
                    for version in versions:
                        if (version['status'] == 'active'):
                            activeVersion = version
                            break

                res['id'] = page['id']
                res['name'] = page['name']
                res['created_at'] = page['created_at']
                res['updated_at'] = page['updated_at']
                res['url'] = page['url']
                res['status'] = page['status']
                res['load_iframe'] = page['load_iframe']
                res['is_customers_only'] = page['is_customers_only']
                res['versions'] = activeVersion

                result.append(res)
            data = calculatePaginationData(
                result, paginationPage, limit, total_data_length)
            data['meta']['name_info'] = nameResult
            return data                                

    def get_pages_storefront(self, body, cdn_baseurl):
        result = []                        
        body['filterBy'] = ['name']            
        pages, total_data_length, paginationPage, limit = super().get_paginated_records_updated(body, page_fields,'')

        webpages = super().find_all()
        nameResult = []
        for page in webpages:
            test = {}
            test['Name'] = page['name']
            test['URL'] = page['url']
            nameResult.append(test)

        for page in pages:
            res = {}
            versions = page['versions']
            if (len(versions) == 0):
                activeVersion = page['default_layout']
            else:
                for version in versions:
                    if (version['status'] == 'active'):
                        activeVersion = version
                        break
            activeVersion =  self.addCdnToImageUrl(cdn_baseurl, activeVersion)
                                                                                                                                                                    

            res['id'] = page['id']
            res['name'] = page['name']
            res['created_at'] = page['created_at']
            res['updated_at'] = page['updated_at']
            res['url'] = page['url']
            res['status'] = page['status']
            res['load_iframe'] = page['load_iframe']
            res['is_customers_only'] = page['is_customers_only']
            res['versions'] = activeVersion

            result.append(res)
        data = calculatePaginationData(
            result, paginationPage, limit, total_data_length)
        data['meta']['name_info'] = nameResult
        return data          


    def create_page(self, body):
        response = {
            "status": 400
        }
        url = body['name']
        # url = '/pages/' + url.replace(" ", "_").lower()
        url = url.replace(" ", "_").lower()
        isUniqueName = self.checkForUniquePageName(body['name'])
        if isUniqueName:
            body["created_at"] = int(datetime.utcnow().timestamp())
            body["updated_at"] = ""
            body["url"] = url
            body["default_layout"] = {}
            body["versions"] = []
            body["preview_state"] = {}
            body['load_iframe'] = False
            body['is_customers_only'] = False
            
            id = super().create(body)
            response['message'] = "Page created successfully"
            response['status'] = 200
        else:
            response['message'] = "The provided page Name has already been matched with other pages. Please provide a different page Name."
            response['status'] = 409

        return response

    # Below function update only if updated_at does not have any value.
    # In other words update only if it's not modified before.
    def create_bc_page(self, page, req_body,store):
        
        created_by = {}
        if 'created_by' in req_body:
            user = user_db.fetch_user_by_username(req_body['created_by'])        
            created_by = {
                "user_id": str(user['_id']),
                "user_name": user['name']
            }
        # Find in DB if available.
        web_page = super().find_one({"bc_id": page['id']})
        page_content = page['body']
        page_content = page_content.replace("%%GLOBAL_ShopPathSSL%%", "https://cdn11.bigcommerce.com/s-964anr")
        page_content = page_content.replace("%%GLOBAL_CdnStorePath%%", "https://cdn11.bigcommerce.com/s-964anr") 
        page_content = page_content.replace('<a href="%%GLOBAL_ShopPathSSL%%', '<a href="') 
        page_content = page_content.replace('<a href="%%GLOBAL_CdnStorePath%%', '<a href="') 
        page_content = page_content.replace('<a href="https://cdn11.bigcommerce.com/s-964anr', '<a href="')    

        load_iframe = False
        if '<script type="text/javascript">' in page_content:
                load_iframe = True
   # If webpage not exists then create
        if not web_page:            
            self.generate_page( page, load_iframe,page_content,created_by)

        # If exists then check if it's modified or not if not then replace content
        if web_page:                   
            self.update_webpage( page, load_iframe,web_page['updated_at'],web_page,page_content,created_by)
            self.getNavigationForWebpages(page,store) 

    def getNavigationForWebpages(self,page,store):
            navigations=cms_db.get_sub_nav(store)
            navigations=processList(navigations)
            for navigation in navigations:
                navigation['navigation_id']=ObjectId(navigation['navigation_id'])
                is_updated=False
                for data in navigation['navigations']:
                    if data['type'] == 'Web Pages' and data['id'] == 'Web Pages'+'_'+str(page['id']):
                        is_updated=True
                        data['url']=page['url']
                if is_updated:
                   cms_db.update_sub_nav_by_id(store,navigation)
            return
        
        
    def generate_page(self, page, load_iframe,page_content,created_by):
            obj = {}
            obj['name'] = page['name']
            obj['bc_id'] = page['id']
            obj['is_visible'] = page['is_visible']
            obj["created_at"] = int(datetime.utcnow().timestamp())
            obj["updated_at"] = ""
            obj["url"] = page['url']
            obj["default_layout"] = {}
            obj["versions"] = [{
                'name': 'Current Bigcommerce Page',
                'status': 'active',
                'class': '',                
                'components': [{
                    "name": 'HTML Block',
                    'code': 'html_block',
                    'variant': {
                        "id": '',
                        'name': 'HTML Block',
                        'admin_layout': 'style1',
                        'class': [],                        
                        'config': {
                            "data": page_content
                        }
                    }
                }],
                'seo_details': {
                    'page_name': page['name'],
                    'page_url': page['url'],
                    'meta_title': page['meta_title'],
                    'meta_description': page['meta_description']
                },
                'created_by': created_by,
                'created_at': int(datetime.utcnow().timestamp()),
                'updated_at': '',
                'version': 1
            }]
            obj['load_iframe'] = load_iframe
            obj['is_customers_only'] = page['is_customers_only']
            obj["preview_state"] = {}

            return super().create(obj)

    def update_webpage(self, page, load_iframe,updated_at,web_page,page_content,created_by):
            # print(page['url'],"page")
            web_page['updated_at'] == ''
            if updated_at=='':
               update_obj = {
                'name': page['name'],
                'is_visible': page['is_visible'],
                "url": page['url'],
                "load_iframe": load_iframe,
                "is_customers_only": page['is_customers_only'],
                "versions": [{
                    'name': 'Current Bigcommerce Page',
                    'status': 'active',
                    'class': '',                    
                    'components': [{
                            "name": 'HTML Block',
                            'code': 'html_block',
                            'variant': {
                                "id": '',
                                'name': 'HTML Block',
                                'admin_layout': 'style1',
                                'class': [],                                
                                'config': {
                                    "data": page_content
                                }
                            }
                    }],
                    'seo_details': {
                        'page_name': page['name'],
                        'page_url': page['url'],
                        'meta_title': page['meta_title'],
                        'meta_description': page['meta_description']
                    },
                    'created_by': created_by,
                    'created_at': int(datetime.utcnow().timestamp()),
                    'updated_at': int(datetime.utcnow().timestamp()),
                    'version': 1
                }]
               }
            else:
                for version in web_page['versions']:
                    if version['status'] == 'active':
                       seo_details = version["seo_details"]
                       seo_details["page_url"] = page['url']
                       seo_details["page_name"]=page['name']
                       
                update_obj= {
                    "url": page['url'],
                    "name":page['name'],
                    "versions": web_page['versions'],
                    "updated_at":  int(datetime.utcnow().timestamp())
                        }
                
            return super().update_one({"bc_id": page['id']}, {"$set": update_obj})

     
    def update_page(self, body, webpage_id=None):
        response = {
            "status": 400
        }
        url = body['name']
        # url = '/pages/' + url.replace(" ", "_").lower()
        url = url.replace(" ", "_").lower()
        if body['status_update'] == 'false':
            isUniqueName = self.checkForUniquePageName(body['name'])
        else:
            isUniqueName = True

        if isUniqueName:
            id = super().update_one({"_id": ObjectId(str(webpage_id))}, {"$set":
                                                                         {
                                                                             "name": body['name'],
                                                                             "status": body['status'],
                                                                             "url": url,
                                                                             "updated_at":  int(datetime.utcnow().timestamp())
                                                                         }
                                                                         })
            response['message'] = "Page Updated successfully"
            response['status'] = 200
        else:
            response['message'] = "The provided page Name has already been matched with other pages. Please provide a different page Name."
            response['status'] = 409

        return response

    def update_customer_only_flag(self, body, webpage_id=None):
        response = {
            "status": 400
        }
        if webpage_id:
            id = super().update_one({"_id": ObjectId(str(webpage_id))}, {"$set":
                                                                         {                                                                             
                                                                             "is_customers_only": body['is_customers_only'],                                                                             
                                                                             "updated_at":  int(datetime.utcnow().timestamp())
                                                                         }
                                                                         })
            response['message'] = "Page Updated successfully"
            response['status'] = 200        

        return response
        

    def delete_by_id(self, webpage_id):
        return super().delete({"_id": ObjectId(str(webpage_id))})

    def checkForUniquePageName(self, name):
        url = name
        # url = '/pages/' + url.replace(" ", "_").lower()
        url = url.replace(" ", "_").lower()
        pages = super().find_all()
        pages = parse_json(self.processList(pages))
        for page in pages:
            if page['name'] == name or page['url'] == url:
                return False
        return True      

    def addCdnToImageUrl(self, cdn_baseurl, activeVersion): 
        if 'components' in activeVersion:   
            for component in activeVersion['components']:
                if 'variant' in component:
                    variant = component['variant']
                    if 'config' in variant:
                        config = variant['config']
                        if 'image_url' in config:
                            if config['image_url'] != '':
                                config['image_url'] = cdn_baseurl + '/banner' + config['image_url']
                        if 'mobile_image_url' in config:
                            if config['mobile_image_url'] != '':
                                config['mobile_image_url'] = cdn_baseurl + '/banner' + config['mobile_image_url']  

                        elif 'slider' in  config and 'side_images' in config:                            
                            sliders = config['slider']
                            side_images = config['side_images']
                            for slider in sliders:
                                if 'image_url' in slider:
                                    if slider['image_url'] != '':
                                        slider['image_url'] = cdn_baseurl + '/banner' + slider['image_url']

                            for side_image in side_images:
                                if 'image_url' in side_image:
                                    if side_image['image_url'] != '':
                                        side_image['image_url'] = cdn_baseurl + '/banner' + side_image['image_url']
                        elif 'banners' in  config:
                            banners = config['banners']
                            for banner in banners:
                                if 'image_url' in banner:
                                    if banner['image_url'] != '':
                                        banner['image_url'] = cdn_baseurl + '/banner' + banner['image_url'] 
                                if 'mobile_image_url' in banner:
                                    if banner['mobile_image_url'] != '':
                                        banner['mobile_image_url'] = cdn_baseurl + '/banner' + banner['mobile_image_url']                          
                        elif 'logos' in config:
                            logos = config['logos'] 
                            for logo in logos:
                                if 'image_url' in logo:
                                    if logo['image_url'] != '':
                                        logo['image_url'] = cdn_baseurl + '/banner' + logo['image_url']

        return activeVersion             



class PageVersions(Service):
    def set_version(self, body, webpage_id=None):
        response = {
            "status": 400
        }
        created_by = {}
        if 'created_by' in body:
            user = user_db.fetch_user_by_username(body['created_by'])        
            created_by = {
                "user_id": str(user['_id']),
                "user_name": user['name']
            }
        new_page_url = body["seo_details"]["page_url"]
        new_page_name = body["seo_details"]["page_name"]
        isUrlUnique = self.checkForUniqueUrl(new_page_url, webpage_id)
        if isUrlUnique:
            webPage = super().find_one({"_id": ObjectId(str(webpage_id))})
            totalVersions = len(webPage['versions'])
            versions = webPage['versions']

            if totalVersions > 0:
                lastVersion = webPage['versions'][-1]['version']
                for version in versions:
                    version['status'] = 'inactive'
                    seo_details = version["seo_details"]
                    seo_details["page_url"] = new_page_url
                    # for seo_detail in seo_details:
                        # Update the desired key-value pair
                        # seo_detail["page_url"] = new_page_url
            else:
                lastVersion = 0

            if (totalVersions >= 10):
                webPage['versions'].pop(0)

            body['created_by'] = created_by
            body['created_at'] = int(datetime.utcnow().timestamp())
            body['version'] = lastVersion + 1
            versions.append(body)

            id = super().update_one({"_id": ObjectId(str(webpage_id))}, {"$set":
                                                                         {
                                                                             "name": new_page_name,
                                                                             "url": new_page_url,
                                                                             "versions": webPage['versions'],
                                                                             "updated_at":  int(datetime.utcnow().timestamp())
                                                                         }})
            response['message'] = "changes saved sucessfuly"
            response['status'] = 200
        else:
            response['message'] = "The provided page URL has already been matched with other pages. Please provide a different page URL."
            response['status'] = 409
        return response

    def get_webpage(self, webpage_id=None):
        result = {}
        webPage = super().find_one({"_id": ObjectId(str(webpage_id))})
        if (len(webPage['versions']) == 0):
            activeVersion = webPage['default_layout']
        else:
            versions = webPage['versions']
            for version in versions:
                if (version['status'] == 'active'):
                    activeVersion = version
                    break

        result['id'] = webPage['id']
        result['name'] = webPage['name']
        result['created_at'] = webPage['created_at']
        result['updated_at'] = webPage['updated_at']
        result['url'] = webPage['url']
        result['is_customers_only'] = webPage['is_customers_only']
        result['versions'] = activeVersion

        return result

    def get_webpage_versions(self, webpage_id=None):
        webPage = super().find_one({"_id": ObjectId(str(webpage_id))})
        result = {}
        versionArray = []
        if (len(webPage['versions']) > 0):

            versions = webPage['versions']
            for version in versions:
                data = {}
                data['id'] = int(version['version'])
                if 'created_by' in version:
                    user_name = version['created_by'].get('user_name', '')
                    if user_name:
                        data['title'] = f"{version['version']} ({user_name})"
                    else:
                        data['title'] = str(version['version'])
                else:
                    data['title'] = str(version['version'])
                versionArray.append(data)

            result['webpageVersions'] = versionArray

        return result

    def set_active_version(self, body, webpage_id=None):
        response = {
            "status": 400
        }
        webPage = super().find_one({"_id": ObjectId(str(webpage_id))})

        if (len(webPage['versions']) > 0):
            versions = webPage['versions']
            for version in versions:
                if (version['version'] == int(body['version'])):
                    version['status'] = 'active'
                else:
                    version['status'] = 'inactive'

        id = super().update_one({"_id": ObjectId(str(webpage_id))}, {"$set":
                                                                     {
                                                                         "versions": webPage['versions'],
                                                                         "updated_at":  int(datetime.utcnow().timestamp())
                                                                     }})

        if id:
            response['message'] = "version active status changed successfully!"
            response['status'] = 200
        return response

    def get_webpage_versionData(self, body, webpage_id=None):
        result = {}
        webPage = super().find_one({"_id": ObjectId(str(webpage_id))})
        if (len(webPage['versions']) == 0):
            activeVersion = webPage['default_layout']
        else:
            versions = webPage['versions']
            for version in versions:
                if (version['version'] == int(body['version'])):
                    activeVersion = version
                    break

        result['id'] = webPage['id']
        result['name'] = webPage['name']
        result['created_at'] = webPage['created_at']
        result['updated_at'] = webPage['updated_at']
        result['url'] = webPage['url']
        result['is_customers_only'] = webPage['is_customers_only']        
        result['versions'] = activeVersion

        return result

    def set_page_preview(self, body, webpage_id=None):
        response = {
            "status": 400
        }
        webPage = super().find_one({"_id": ObjectId(str(webpage_id))})

        created_by = {}
        if 'created_by' in body:
            user = user_db.fetch_user_by_username(body['created_by'])        
            created_by = {
                "user_id": str(user['_id']),
                "user_name": user['name']
            }

        body['created_by'] = created_by
        body['created_at'] = int(datetime.utcnow().timestamp())

        webPage['preview_state'] = body

        id = super().update_one({"_id": ObjectId(str(webpage_id))}, {"$set":
                                                                     {
                                                                         "preview_state": webPage['preview_state'],
                                                                         "updated_at":  int(datetime.utcnow().timestamp())
                                                                     }})
        response['message'] = "changes saved sucessfuly"
        response['status'] = 200
        return response

    def get_bc_page_data(self, body):
        store_hash = body['store_hash']
        webpage_id = body['webpage_id']
        access_token = body['access_token']
        api_url = f"https://api.bigcommerce.com/stores/{store_hash}/v3/content/pages/{webpage_id}"
        headers = {
            "X-Auth-Token": access_token,
            "Accept": "application/json"

        }
        response = requests.get(api_url, headers=headers)
        temp_body = {}
        component_body = []

        if response.status_code == 200:
            webpage_data = response.json()
            variant_schema = {}

            variant_schema['id'] = '1'
            variant_schema['name'] = 'HTML Block'
            variant_schema['admin_layout'] = 'style1'
            variant_schema['class'] = [
                'test_globle_class', 'test_globle_class2']            
            variant_schema['config'] = {
                'data': webpage_data
            }
            component_body.append({
                'name': 'HTML Block',
                'code': 'html_block',
                'variant': variant_schema
            })

            temp_body['name'] = body['page_name']
            temp_body['status'] = body['status']
            temp_body['class'] = ''            
            temp_body['components'] = component_body

            return "Webpage data retrieved successfully"

        else:
            return f"Failed to retrieve webpage data. Status code: {response.status_code}"

    def checkForUniqueUrl(self, url, webpage_id):
        pages = super().find_all()
        pages = parse_json(self.processList(pages))
        for page in pages:
            if not page['id'] == webpage_id:
                if page['url'] == url:
                    return False
        return True


class SetImages():
    def setImage(self, body):
        response = {
            "status": 400
        }

        file = body.get('image')
        type = body.get('type')
        UPLOAD_FOLDER = os.path.join('images')

        if (file.filename == ''):
            response['message'] = "No image selected for uploading"
            response['status'] = 500

        if (file and allowed_file(file.filename)):
            newName = change_file_name(file.filename)
            fname = secure_filename(newName)

            if not os.path.exists(UPLOAD_FOLDER):
                os.makedirs(UPLOAD_FOLDER)

            file_path = os.path.join(UPLOAD_FOLDER, fname)

            file.save(file_path)

            # Check the file extension
            _, file_extension = os.path.splitext(file_path)
            file_extension = file_extension.lower()

            if file_extension in ['.png', '.jpg', '.jpeg', '.gif']:
                image = Image.open(file_path)
            
                original_width, original_height = image.size
                
                # Specify the desired width for each type
                desired_width = {
                    'top_ten_products': 200,
                    'full_width_banner': 1656,
                    'five_column_banner': 312,
                    'four_column_banner': 396,
                    'four_column_text_banner': 396
                }

                # Check if the type is in the desired_width dictionary
                if type in desired_width:
                    # Calculate the new height to maintain the aspect ratio
                    new_height = int((desired_width[type] / original_width) * original_height)
                    
                    # Resize the image
                    image = image.resize((desired_width[type], new_height))
                    image.save(file_path)
                else:
                    # If the type is not in the dictionary, save the image without resizing
                    image.save(file_path)

                base_path = os.path.join(os.path.abspath(os.getcwd()), UPLOAD_FOLDER, newName)
                
                if '/app/images' in base_path:
                    base_path = base_path.replace('/app/images', '')  
                response['message'] = base_path
                response['status'] = 200
            else:
                base_path = os.path.join(os.path.abspath(
                    os.getcwd()), UPLOAD_FOLDER, newName)
            
                if '/app/images' in base_path:
                    base_path = base_path.replace('/app/images', '')  
                
                response['message'] = base_path
                response['status'] = 200
        else:
            response['message'] = "Allowed image types are -> png, jpg, jpeg, gif, mp4, avi, mov, wmv, mkv"
            response['status'] = 500

        return response

    def getImage(self, body):
        url = './images/' + body['image']
        if not os.path.exists(url):
            return make_response({'error': 'Image not found'}, 404)

        return send_file(url, as_attachment=True)

class BcProductData():
    def get_bc_data(self,store, body):
        result = {}
        products=bc_products.fetch_bc_product_by_sku(store,[body['sku']])
        if products['data']:
            # print(product)
            product=products['data'][0]
            result['id']=product['id']
            result['name']=product['name']
            result['sku']=product['sku']
            result['is_visible']=product['is_visible']
            result['page_title']=product['page_title']
            result['custom_url']=product['custom_url']
            result['thumbnail_image']=""
    
            for images in product['images']:
               if images['is_thumbnail']:
                  result['thumbnail_image']=images['url_thumbnail']
            
        return result