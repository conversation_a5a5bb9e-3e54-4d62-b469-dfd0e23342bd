from new_mongodb import get_store_db_client_for_store_id, StoreDBCollections

def get_categories_by_id(store_id, category_ids):
    db = get_store_db_client_for_store_id(store_id)
    category_names = []
    for category_id in category_ids:
        category = db[StoreDBCollections.CATEGORIES].find_one({"_id": category_id})
        if category:
            category_names.append(category.get("name"))
    return category_names

def get_product_by_id(store_id, product_id):
    db = get_store_db_client_for_store_id(store_id)
    product = db[StoreDBCollections.PRODUCTS].find_one({"_id": int(product_id)})
    return product