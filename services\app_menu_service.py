from services import Service
from bson import ObjectId
import datetime
from mongo_db import permission_db, role_db

class AppMenu(Service):
  def __init__(self, repository):
    super().__init__(repository)
  
  def process_data(self, store, payload, permissions,  all_roles):
    after = payload["after"]
    side_menu = payload['sidebar']
    permission = payload['permission']
    new_role = payload['role']

    store_id = store['id']
    app_menu = super().find_one({"store_id": store_id})        

    def create_obj(data, new_data):
      updated_obj = {}
      
      for app_key, app_value in data.items():
        updated_obj[app_key] = app_value

        if after == app_key:
          new_data_key, new_data_value = next(iter(new_data.items()))
          updated_obj[new_data_key] = new_data_value
        
        if app_value['children']:
          child_obj = {}
          
          for child_key, child_value in app_value['children'].items():
            child_obj[child_key] = child_value
          
            if after == child_key:
              new_data_key, new_data_value = next(iter(new_data.items()))
              child_obj[new_data_key] = new_data_value  

          updated_obj[app_key]['children'] = child_obj
      return updated_obj
    
    # update side bar object in app collection...
    app_menu_id = app_menu['id']
    del app_menu['store_id']
    del app_menu['id']
    del app_menu['created_at']

    if 'updated_at' in app_menu:
      del app_menu['updated_at']

    updated_side_menu = create_obj(app_menu, side_menu)
    updated_side_menu['updated_at'] = int(datetime.datetime.utcnow().timestamp())
    super().update_one({
      "_id": ObjectId(app_menu_id)
    }, {
      "$set": updated_side_menu
    })

    # update permission object...
    updated_permissions = create_obj(permissions['permissions'], permission)
    permission_db.update_permissions(permissions['id'], updated_permissions)
    
    # update roles ...
    for role in all_roles:
      permissions = role['permissions']
      role_key = ''

      for key,value in new_role.items():
        role_key = key 
      
      if role['is_super_admin'] or role['is_administrator_access']:
        operations = {
          'read': True,
          'write': True
        }
        new_role[role_key].update({role_key: operations})
        updated_permissions = create_obj(permissions, new_role)
        role_db.update_single_role(role['id'], updated_permissions)
      else:
        operations = {
            'read': False,
            'write': False
          }
        new_role[role_key].update({role_key: operations})
        updated_permissions = create_obj(permissions, new_role)
        role_db.update_single_role(role['id'], updated_permissions)
      

    