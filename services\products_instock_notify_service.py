from sqlalchemy import text
import new_pgdb
import logging
from new_utils import calculate_pagination
from utils.common import convert_to_timestamp
import json

logger = logging.getLogger()

def get_products_instock_notify(store, page, limit, sort_array, search):
    response = {'status': 400}
    
    conn = new_pgdb.get_connection(store['id'])
    try:
        # Build search condition and params
        search_condition = ""
        variant_search_condition = ""
        search_params = {}
        
        if search:
            # Search in parent product details
            parent_search = """(p.product_name ILIKE :search_term 
                              OR parent_inquiries.parent_sku ILIKE :search_term)"""
            
            # Search in variant details  
            variant_search = """(v.variant_options ILIKE :search_term 
                               OR v.variants_sku ILIKE :search_term)"""
            
            search_condition = f"AND ({parent_search})"
            variant_search_condition = f"AND ({variant_search})"
            search_params['search_term'] = f'%{search}%'

        # Build sort condition
        sort_condition = "ORDER BY inquiry DESC"
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            if sort_array[0] in ['inquiry', 'product_name', 'product_sku', 'last_created_at']:
                sort_condition = f"ORDER BY {sort_array[0]} {sort_direction}"

        # Count query for pagination - count parent products that match either parent or variant search
        count_query = f"""
            WITH parent_inquiries AS (
                SELECT DISTINCT
                    pin.product_id,
                    pin.parent_sku
                FROM product_instock_notify pin
            ),
            matching_parents_by_parent AS (
                SELECT DISTINCT parent_inquiries.product_id
                FROM parent_inquiries
                LEFT JOIN products p ON parent_inquiries.product_id = p.product_id
                WHERE 1=1 {search_condition if search else ""}
            ),
            matching_parents_by_variant AS (
                SELECT DISTINCT pin.product_id
                FROM product_instock_notify pin
                LEFT JOIN variants v ON pin.variant_id = v.variants_id
                WHERE pin.variant_id IS NOT NULL 
                  AND pin.variant_sku IS NOT NULL 
                  AND pin.variant_sku != pin.parent_sku
                  {variant_search_condition if search else ""}
            ),
            all_matching_parents AS (
                SELECT product_id FROM matching_parents_by_parent
                UNION
                SELECT product_id FROM matching_parents_by_variant
            )
            SELECT COUNT(*) as total_count
            FROM all_matching_parents
        """

        if search:
            result_count = conn.execute(text(count_query), search_params)
        else:
            # If no search, use simpler count query
            count_query_simple = """
                WITH parent_inquiries AS (
                    SELECT DISTINCT
                        pin.product_id,
                        pin.parent_sku
                    FROM product_instock_notify pin
                )
                SELECT COUNT(*) as total_count
                FROM parent_inquiries
            """
            result_count = conn.execute(text(count_query_simple))
        
        total_count = int(result_count.scalar())

        # Main query to get parent products with their variants
        query = f"""
            WITH parent_inquiries AS (
                SELECT 
                    pin.product_id,
                    pin.parent_sku,
                    COUNT(*) as parent_inquiry_count,
                    MAX(pin.created_at) as parent_most_recent_created_at
                FROM product_instock_notify pin
                GROUP BY pin.product_id, pin.parent_sku
            ),
            matching_parents_by_parent AS (
                SELECT DISTINCT parent_inquiries.product_id
                FROM parent_inquiries
                LEFT JOIN products p ON parent_inquiries.product_id = p.product_id
                WHERE 1=1 {search_condition if search else ""}
            ),
            matching_parents_by_variant AS (
                SELECT DISTINCT pin.product_id
                FROM product_instock_notify pin
                LEFT JOIN variants v ON pin.variant_id = v.variants_id
                WHERE pin.variant_id IS NOT NULL 
                  AND pin.variant_sku IS NOT NULL 
                  AND pin.variant_sku != pin.parent_sku
                  {variant_search_condition if search else ""}
            ),
            all_matching_parents AS (
                SELECT product_id FROM matching_parents_by_parent
                UNION
                SELECT product_id FROM matching_parents_by_variant
            ),
            variant_data AS (
                SELECT 
                    variant_counts.product_id,
                    JSON_AGG(
                        JSON_BUILD_OBJECT(
                            'id', CONCAT('variant_', variant_counts.variant_id),
                            'product_id', variant_counts.product_id,
                            'variant_name', variant_counts.variant_name,
                            'variant_sku', variant_counts.variant_sku,
                            'inquiry', variant_counts.inquiry_count,
                            'variant_id', variant_counts.variant_id,
                            'last_created_at', variant_counts.variant_most_recent_created_at
                        )
                        ORDER BY variant_counts.inquiry_count DESC
                    ) as variants_array,
                    MAX(variant_counts.variant_most_recent_created_at) as variants_most_recent_created_at
                FROM (
                    SELECT 
                        pin.product_id,
                        pin.variant_id,
                        v.variant_options as variant_name,
                        v.variants_sku as variant_sku,
                        COUNT(*) as inquiry_count,
                        MAX(pin.created_at) as variant_most_recent_created_at
                    FROM product_instock_notify pin
                    LEFT JOIN variants v ON pin.variant_id = v.variants_id
                    WHERE pin.variant_id IS NOT NULL 
                      AND pin.variant_sku IS NOT NULL 
                      AND pin.variant_sku != pin.parent_sku
                      {f"""AND (
                          -- Show all variants for parents that match parent search
                          pin.product_id IN (SELECT product_id FROM matching_parents_by_parent)
                          OR
                          -- Show only matching variants for parents that match by variant search
                          (pin.product_id IN (SELECT product_id FROM matching_parents_by_variant) 
                           {variant_search_condition})
                      )""" if search else ""}
                    GROUP BY pin.product_id, pin.variant_id, v.variant_options, v.variants_sku
                ) variant_counts
                GROUP BY variant_counts.product_id
            )
            SELECT 
                CONCAT('parent_', parent_inquiries.product_id) as id,
                parent_inquiries.product_id,
                p.product_name,
                parent_inquiries.parent_sku as product_sku,
                parent_inquiries.parent_inquiry_count as inquiry,
                COALESCE(variant_data.variants_array, '[]'::json) as variants,
                GREATEST(
                    parent_inquiries.parent_most_recent_created_at,
                    COALESCE(variant_data.variants_most_recent_created_at, parent_inquiries.parent_most_recent_created_at)
                ) as last_created_at
            FROM parent_inquiries
            LEFT JOIN products p ON parent_inquiries.product_id = p.product_id
            LEFT JOIN variant_data ON parent_inquiries.product_id = variant_data.product_id
            WHERE {f"parent_inquiries.product_id IN (SELECT product_id FROM all_matching_parents)" if search else "1=1"}
            {sort_condition}
            LIMIT :limit OFFSET :offset
        """

        # Calculate offset
        offset = (page - 1) * limit
        query_params_dict = {**search_params, 'limit': limit, 'offset': offset}

        result = conn.execute(text(query), query_params_dict)
        res = result.fetchall()

        products = []
        if res:
            for row in res:
                # Parse variants JSON if it exists
                variants_data = row[5] if row[5] else []
                if isinstance(variants_data, str):
                    variants_data = json.loads(variants_data)
                
                # Convert variant created_at timestamps
                if variants_data:
                    for variant in variants_data:
                        if 'last_created_at' in variant and variant['last_created_at']:
                            variant['last_created_at'] = convert_to_timestamp(variant['last_created_at'])
                
                row_data = {
                    'id': row[0],
                    'product_id': row[1],
                    'product_name': row[2],
                    'product_sku': row[3],
                    'inquiry': row[4],
                    'variants': variants_data,
                    'last_created_at': convert_to_timestamp(row[6]) if row[6] else None
                }
                
                products.append(row_data)

        # Calculate pagination using the utility function
        data = calculate_pagination(products, page, limit, total_count)
        
        response = data
        response['status'] = 200

    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        logger.error(f"Error getting products instock notify listing: {error_message}")
        raise e
    finally:
        if conn:
            conn.close()
    
    return response

def get_products_instock_notify_details(store, product_id, variant_id):
    response = {'status': 400}
    
    conn = new_pgdb.get_connection(store['id'])
    try:
        # Build variant filter condition
        variant_condition = ""
        query_params_dict = {'product_id': product_id}
        if variant_id:
            variant_condition = "AND pin.variant_id = :variant_id"
            query_params_dict['variant_id'] = variant_id

        # Main query to get detailed inquiries
        query = f"""
            SELECT 
                pin.created_at,
                CASE 
                    WHEN c.first_name IS NULL AND c.last_name IS NULL THEN NULL
                    ELSE CONCAT(c.first_name, ' ', c.last_name)
                END as customer_name,
                c.email as customer_email,
                c.customer_group_name as customer_group,
                scr.rep_name as sales_rep,
                CASE 
                    WHEN pin.variant_sku IS NOT NULL AND pin.variant_sku != pin.parent_sku 
                    THEN pin.variant_sku 
                    ELSE pin.parent_sku 
                END as sku,
                pin.variant_id,
                pin.notify_email
            FROM product_instock_notify pin
            LEFT JOIN customers c ON pin.customer_id = c.customer_id
            LEFT JOIN salesforce_customer_rep scr ON pin.customer_id = scr.customer_id
            WHERE pin.product_id = :product_id {variant_condition}
            ORDER BY pin.created_at DESC
        """

        result = conn.execute(text(query), query_params_dict)
        res = result.fetchall()

        inquiries = []
        if res:
            for row in res:
                row_data = {
                    'created_at': convert_to_timestamp(row[0]) if row[0] else None,
                    'customer_name': row[1],
                    'customer_email': row[2],
                    'customer_group': row[3],
                    'sales_rep': row[4],
                    'sku': row[5],
                    'variant_id': row[6],
                    'notify_email': row[7]
                }
                inquiries.append(row_data)

        response = {
            'data': inquiries,
            'status': 200
        }

    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        logger.error(f"Error getting products instock notify details: {error_message}")
        raise e
    finally:
        if conn:
            conn.close()
    
    return response
