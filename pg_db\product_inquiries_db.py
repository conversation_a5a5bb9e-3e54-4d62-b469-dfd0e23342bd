from sqlalchemy.dialects.postgresql import insert
import pg_db as db
from sqlalchemy import Boolean, Column, DateTime, Numeric, String, Integer, ForeignKey, Enum, Interval
from sqlalchemy.sql import func


class ProductInquiries(db.Base):
    __tablename__ = "product_inquiries"

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(Integer)
    customer_name = Column(String(100), nullable=False)
    customer_email = Column(String(100), nullable=False)
    product_id = Column(Integer)
    date_submitted = Column(DateTime)
    message = Column(String)
    status = Column(String)
    updated_by = Column(String(100))
    updated_at = Column(DateTime, onupdate=func.now())