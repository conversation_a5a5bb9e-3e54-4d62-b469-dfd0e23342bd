from plugin import bc_brands, bc_category, bc_products
from utils import redis_util, product_util, catalog_util
from mongo_db import customer_db, catalog_db

def update_brands(store):
    status, message = bc_brands.fetch_all_brands(store)
    catalog_util.update_brand_cache(store)
    return status, message

def update_categories(store):
    return bc_category.fetch_all_categories(store)

def update_products(store):
    result, data = bc_products.fetch_all_products(store)
    update_product_invetory_cache(store)
    return result, data

def update_product_invetory_cache(store):
    sku_inventory = product_util.get_all_product_inventory(store)
    redis_util.update_sku_invetory_cache(store['id'], sku_inventory)
    return True, {"message": "Completed successfully"}
    
def get_unique_product_id(*args):
    items = set()
    for products in args:
        for product in products:
            items.add(product["id"])
    return items

def update_product_cache(store):
    catalog_util.update_brand_cache(store)
    status, new_products = bc_products.fetch_new_products(store)
    status, featured_products = bc_products.fetch_featured_products(store)
    status, popular_products = bc_products.fetch_popular_products(store)
    #redis_util.update_product_cache(store["id"], new_products, featured_products, popular_products)

    products = get_unique_product_id(new_products, featured_products, popular_products)
    if products and len(products) > 0:
        items = []
        for product_id in products:
            items.append({"product_id": product_id})
        customer_groups = customer_db.fetch_all_customer_group_id(store)
        if customer_groups and len(customer_groups) > 0:
            for g_id in customer_groups:
                pricing = bc_products.fetch_customer_group_pricing(store, g_id, items)
                payload = {}
                for p in pricing:
                    payload[p['product_id']] = p['price']
                redis_util.update_product_list_pricing(store['id'], g_id, payload)

    return True, {"message":"Completed successfully"}