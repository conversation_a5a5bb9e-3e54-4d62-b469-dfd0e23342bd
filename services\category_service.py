from mongo_db import cms_db
from services import Service
from fields.navigation_fields import categories_fields
from utils.common import parse_json, processListCategory

class Categories(Service):
    
    def get_categories(self, payload):        
        # def get_query(search_result):
        #     # return all products if search params is empty.
        #     if search_result == "":
        #         return {"is_visible": True}

        #     # return text based search if search param's exists.        
        #     return {"$and": [{"$text": {"$search": search_result}},{"is_visible": True}]}                                  

        # def set_limit(limit):
        #     page_limit = 0
        #     skips = 0
            
        #     if int(limit) != 0 and int(limit) > 0:
        #         page_limit = int(limit)

        #     return page_limit, skips
        
        # page_limit , skips = set_limit(payload['limit'] if 'limit' in payload else 0)
        
        # query = get_query(payload["search"]) if "search" in payload else {"is_visible": True}       
        
        data = cms_db.get_categories(payload,categories_fields)        

        # ProcessList ...
        # data = processListCategory(data)        
        return parse_json(data)
