from datetime import datetime, timezone
from new_mongodb import store_catalog_db, StoreDBCollections, get_store_db_client_for_store_id, get_admin_db_client_for_store_id
from new_mongodb import fetch_one_document_from_storefront_collection, insert_document_in_storefront_collection, update_document_in_storefront_collection, delete_documents_from_storefront_collection
import new_utils
from plugin import bc_products
import logging
import os
from werkzeug.utils import secure_filename
from fields.products import my_products_list_fields
from bson import ObjectId
from utils import store_util, bc
from copy import deepcopy
import task
from new_utils.store_util import get_skuvault_api_info 

logger = logging.getLogger()

ALLOWED_EXTENSIONS = set(['png', 'jpg', 'jpeg', 'webp', 'gif'])

int_field_list = ['width', 'depth', 'height', 'cost_price', 'retail_price', 'sale_price', 'sort_order', 'fixed_cost_shipping_price', 'order_quantity_minimum', 'order_quantity_maximum']

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def change_file_name(filename, index):
    rendom = str(round(datetime.utcnow().timestamp()))
    if ('.' in filename):
        ext = filename.rsplit('.', 1)[1].lower()
        fname = rendom + '_' + str(index) + '.' + ext
        return fname

def get_products(store, payload):
    payload['filterBy'] = ['name','sku']
    db_client = get_store_db_client_for_store_id(store['id'])
    status = payload.get('status', '') 
    additional_query = {}
    if status:
        additional_query = {'status': status}
    products, total_data_length, page, limit = new_utils.get_paginated_records_updated(db_client, StoreDBCollections.MY_PRODUCTS, payload, my_products_list_fields, additional_query)
    
    for product in products:
        images = product.get('images', [])
        if images:
            for image in images:
                if image['is_thumbnail']:
                    product['image_url'] = image['image_url']
                    break
            if 'image_url' not in product:
                product['image_url'] = images[0]['image_url']
        del product['images']

    
    data = new_utils.calculate_pagination(products, page, limit, total_data_length)

    return data

def create_product(store, req_body, user):
    # check for the unique name and sku
    product_data = fetch_one_document_from_storefront_collection(store['id'], StoreDBCollections.MY_PRODUCTS, {'name': {'$regex': f'^{req_body["name"]}$', '$options': 'i'}})
    if product_data:
        return {'data': "name: Product with the same name already exists."}, 400
    
    product_data = fetch_one_document_from_storefront_collection(store['id'], StoreDBCollections.MY_PRODUCTS, 
    {
        '$or': [
            {'sku': req_body['sku']},  # Search for matching sku in the main product
            {'variants.sku': req_body['sku']}  # Search for matching sku inside the product_variants array
        ]
    })
    if product_data:
        return {'data': "sku: Product with the same SKU already exists."}, 400
    
    product_data = bc_products.fetch_bc_product_by_name(store, req_body['name'])
    if product_data['data']:
        return {'data': "name: Product with the same name already exist."}, 400

    product_data = bc_products.fetch_bc_product_by_sku(store, req_body['sku'])
    if product_data['data']:
        return {'data': "sku: Product with the same SKU already exist."}, 400
    
    product_data = bc_products.fetch_bc_variant_by_sku(store, req_body['sku'])
    if product_data['data']:
        return {'data': "sku: Product with the same SKU already exist."}, 400

    req_body['variants'] = _process_variant_options(req_body['variants'], req_body['sku'], req_body['price'])
    
    if len(req_body['variants']) > 200 and not req_body.get('is_draft', False):
        payload = deepcopy(req_body)
        payload['status'] = 'publishing'
        payload['created_by'] = {
            "user_id": str(user['_id']),
            "user_name": user['name']
        }
        payload['created_at'] = int(datetime.now(timezone.utc).timestamp())
        payload['updated_at'] = int(datetime.now(timezone.utc).timestamp())
        payload['updated_by'] = {}
        payload['error'] = ''

        # Insert the product directly in MongoDB and skip BigCommerce API call
        id = insert_document_in_storefront_collection(store, StoreDBCollections.MY_PRODUCTS, payload)
        task.send_task(task.CREATE_PRODUCT_IN_BACKGROUND_TASK, args=(store['id'], id))
        return {'data': str(id)}, 200
    
    payload = deepcopy(req_body)
    req_body['upc'] = str(req_body['upc']) if req_body.get('upc') else ''
    req_body['gtin'] = str(req_body['gtin']) if req_body.get('gtin') else ''

    created_by = {
        "user_id": str(user['_id']),
        "user_name": user['name']
    }
    # payload['status'] = 'draft'
    payload['error'] = ''
    
    # bc_id = 0
    # create product in bc
    if not req_body.get('is_draft', False):        
        req_body = _process_categories_and_images(store, req_body)
        req_body = _build_product_dto_for_bc(req_body)
        res, status = bc_products.create_bc_product(store, req_body) 
        if status != 200:
            return {'data': res}, status 
        else:
            # bc_id = res['data']['id']
            payload['status'] = 'published'

    payload['created_by'] = created_by
    payload['created_at'] = int(datetime.now(timezone.utc).timestamp())
    payload['updated_at'] = int(datetime.now(timezone.utc).timestamp())
    payload['updated_by'] = {}
    
    id = insert_document_in_storefront_collection(store, StoreDBCollections.MY_PRODUCTS, payload)
    # if not req_body.get('is_draft', False) and payload.get('status') == 'published':
    #     task.send_task(task.UPDATE_PRODUCT_IMAGES_VARIANTS_TASK, args=(store, bc_id, id)) 

    return {'data': str(id)}, 200

def get_product(store, product_id):
    result = {
        'status': 400
    }
    product_data = fetch_one_document_from_storefront_collection(store['id'], StoreDBCollections.MY_PRODUCTS, {"_id": ObjectId(str(product_id))})
    if not product_data:
        result['status'] = 404
        result['message'] = "product not found."
        return result
    result['status'] = 200
    result['data'] = product_data
    return result

def update_product(store, req_body, product_id, user):
    if req_body ==  {}:
        retrived_data = get_product(store, product_id)
        if retrived_data['status'] != 200:
            return {'data': retrived_data['message']}, retrived_data['status']
        req_body = retrived_data['data']
        req_body['is_draft'] = False
        req_body['ready_to_sync'] = True
    
    product_data = fetch_one_document_from_storefront_collection(store['id'], StoreDBCollections.MY_PRODUCTS, {'name': {'$regex': f'^{req_body["name"]}$', '$options': 'i'}, '_id': {'$ne': ObjectId(str(product_id))}})
    if product_data:
        return {'data': "name: Product with the same name already exists."}, 400
    
    product_data = fetch_one_document_from_storefront_collection(store['id'], StoreDBCollections.MY_PRODUCTS, {
        '$or': [
            {'sku': req_body['sku']},
            {'variants.sku': req_body['sku']} 
        ],
        '_id': {'$ne': ObjectId(str(product_id))}  # Exclude the current product by its _id
    })
    if product_data:
        return {'data': "sku: Product with the same SKU already exists."}, 400
    
    product_data = bc_products.fetch_bc_product_by_name(store, req_body['name'])
    if product_data['data']:
        return {'data': "name: Product with the same name already exist."}, 400

    product_data = bc_products.fetch_bc_product_by_sku(store, req_body['sku'])
    if product_data['data']:
        return {'data': "sku: Product with the same SKU already exist."}, 400
    
    product_data = bc_products.fetch_bc_variant_by_sku(store, req_body['sku'])
    if product_data['data']:
        return {'data': "sku: Product with the same SKU already exist."}, 400

    req_body['variants'] = _process_variant_options(req_body['variants'], req_body['sku'], req_body['price'])
    
    if len(req_body['variants']) > 200 and not req_body.get('is_draft', False):
            payload = deepcopy(req_body)
            payload['status'] = 'publishing'
            updated_by = {
                "user_id": str(user['_id']),
                "user_name": user['name']
            }
            payload['updated_at'] = int(datetime.now(timezone.utc).timestamp())
            payload['updated_by'] = {}
            update_data = { 
                "$set": payload
            }
            # update the product directly in MongoDB and skip BigCommerce API call
            update_document_in_storefront_collection(store['id'], StoreDBCollections.MY_PRODUCTS, {"_id": ObjectId(str(product_id))}, update_data)
            task.send_task(task.CREATE_PRODUCT_IN_BACKGROUND_TASK, args=(store['id'], product_id))
            return {'data': "product updated successfully."}, 200
    
    payload = deepcopy(req_body)
    req_body['upc'] = str(req_body['upc']) if req_body.get('upc') else ''
    req_body['gtin'] = str(req_body['gtin']) if req_body.get('gtin') else ''

    updated_by = {
        "user_id": str(user['_id']),
        "user_name": user['name']
    }
    # payload['status'] = 'draft'
    
    # create product in bc
    if not req_body.get('is_draft', False):
        req_body = _process_categories_and_images(store, req_body)
        req_body = _build_product_dto_for_bc(req_body)
        res, status = bc_products.create_bc_product(store, req_body) 
        if status != 200:
            payload['status'] = 'error'
            payload['error'] = res['title']
            update_data = { 
                "$set": payload
            }
            update_document_in_storefront_collection(store['id'], StoreDBCollections.MY_PRODUCTS, {"_id": ObjectId(str(product_id))}, update_data)
            return {'data': res}, status 
        else:
            payload['status'] = 'published'

    payload['updated_at'] = int(datetime.now(timezone.utc).timestamp())
    payload['updated_by'] = updated_by

    update_data = { 
        "$set": payload
    }
    id = update_document_in_storefront_collection(store['id'], StoreDBCollections.MY_PRODUCTS, {"_id": ObjectId(str(product_id))}, update_data)      
    
    return {'data': "product updated successfully."}, 200

def _process_categories_and_images(store, product_data):
    cdn_baseurl = store_util.get_cdn_base_url(store)
    if 'images' in product_data:
        images = product_data['images']
        if len(images) > 0:
            for image in images:
                if 'image_url' in image:
                    image['image_url'] = cdn_baseurl.replace('/storefront/api/static/images', '/admin/api/my-products/image') + image['image_url']

    if 'categories' in product_data:
        categories = product_data['categories']
        category_ids = []
        if len(categories) > 0:
            for category in categories:
                category_ids.append(category['id'])
        product_data['categories'] = category_ids

    return product_data


def _build_product_dto_for_bc(data):
    if isinstance(data, dict):
        keys_to_remove = []
        for key, value in data.items():
            # Check if the key is in the int_field_list and the value is None
            if key in int_field_list and value is None:
                keys_to_remove.append(key)
            elif isinstance(value, (dict, list)):
                _build_product_dto_for_bc(value)
        
        # Remove the identified keys after iteration
        for key in keys_to_remove:
            del data[key]

    elif isinstance(data, list):
        items_to_remove = []
        for i in range(len(data)):
            if isinstance(data[i], dict):
                # Check for keys in int_field_list with None value inside the dict
                keys_to_remove = []
                for key, value in data[i].items():
                    if key in int_field_list and value is None:
                        keys_to_remove.append(key)
                # Remove identified keys in the current dict
                for key in keys_to_remove:
                    del data[i][key]

                # Recursively handle nested structures
                _build_product_dto_for_bc(data[i])
            elif data[i] is None or data[i] == 'null':
                items_to_remove.append(i)

        # Remove items from the list in reverse order to avoid index issues
        for i in reversed(items_to_remove):
            data.pop(i)
    
    return data

# def _build_product_dto_for_bc(data):
#     # Keys to remove if found in the dictionary
#     keys_to_remove_directly = ['images', 'variants']

#     if isinstance(data, dict):
#         keys_to_remove = []
#         for key, value in data.items():
#             # Remove keys directly if they are 'images' or 'variants'
#             if key in keys_to_remove_directly:
#                 keys_to_remove.append(key)

#             # Check if the key is in the int_field_list and the value is None
#             elif key in int_field_list and value is None:
#                 keys_to_remove.append(key)

#             # Recursively handle nested structures
#             elif isinstance(value, (dict, list)):
#                 _build_product_dto_for_bc(value)
        
#         # Remove the identified keys after iteration
#         for key in keys_to_remove:
#             del data[key]

#     elif isinstance(data, list):
#         items_to_remove = []
#         for i in range(len(data)):
#             if isinstance(data[i], dict):
#                 # Check for keys in int_field_list with None value inside the dict
#                 keys_to_remove = []
#                 for key, value in data[i].items():
#                     if key in keys_to_remove_directly:
#                         keys_to_remove.append(key)
#                     elif key in int_field_list and value is None:
#                         keys_to_remove.append(key)

#                 # Remove identified keys in the current dict
#                 for key in keys_to_remove:
#                     del data[i][key]

#                 # Recursively handle nested structures
#                 _build_product_dto_for_bc(data[i])
#             elif data[i] is None or data[i] == 'null':
#                 items_to_remove.append(i)

#         # Remove items from the list in reverse order to avoid index issues
#         for i in reversed(items_to_remove):
#             data.pop(i)
    
#     return data

def _process_variant_options(variant_array, primary_sku, product_price):
    processed_variants = []
    sku_set = set()

    if variant_array:
        for variant in variant_array:
            # Generate SKU if missing
            if not variant.get('sku'):
                # Generate SKU from the first 1 or 2 initials of option values
                sku_initials = "-".join([value['label'][:2].upper() for value in variant['option_values']])
                generated_sku = f"{primary_sku}-{sku_initials}"

                # Ensure SKU is unique
                unique_sku = generated_sku
                counter = 1
                while unique_sku in sku_set:
                    unique_sku = f"{generated_sku}-{counter}"
                    counter += 1
                
                variant['sku'] = unique_sku
                sku_set.add(unique_sku)
            else:
                sku_set.add(variant['sku'])

            #  Handle price: Use variant price if available, else use product price if it's valid
            if variant.get('price') is not None:
                variant['price'] = float(variant['price'])
            elif product_price is not None:
                variant['price'] = float(product_price)
            else:
                variant['price'] = 0.0  # Fallback to 0 if both are None

            # Handle inventory level
            variant['inventory_level'] = variant.get('inventory_level', 0)
            
            processed_variants.append(variant)
    
    return processed_variants

def delete_product(store, product_id):
    try:
        db = get_store_db_client_for_store_id(store['id'])

        delete_result = delete_documents_from_storefront_collection(store['id'], StoreDBCollections.MY_PRODUCTS, {"_id": ObjectId(product_id)})

        if delete_result.deleted_count > 0:
            return {"status": 200, "message": f"Product with ID {product_id} deleted successfully."}
        else:
            return {"status": 404, "message": f"Product with ID {product_id} not found."}
    
    except Exception as e:
        logger.error("An error occurred while deleting the product: %s", str(e))
        return {"status": 409, "message": "An error occurred while deleting the product.", "error": str(e)}

def setImage(body):
    response = {
        "status": 200,
        "files": [],
        "message": ""
    }
    failed_count = 0
    # Get the current month abbreviation and the last two digits of the year
    current_month_year = datetime.now().strftime('%b_%y').lower()
    monthly_folder = os.path.join('images', 'products', current_month_year)
    if not os.path.exists(monthly_folder):
        os.makedirs(monthly_folder)

    

    for index, file in enumerate(body):
        if file.filename == '':
            failed_count += 1
            response['message'] = "No image selected for uploading"
            continue

        if file and allowed_file(file.filename):
            newName = change_file_name(file.filename, index)
            fname = secure_filename(newName)
            file.save(os.path.join(monthly_folder, fname))
            base_path = os.path.join(os.path.abspath(os.getcwd()), monthly_folder, newName)

            if '/app/images' in base_path:
                base_path = base_path.replace('/app/images', '')

            response['files'].append(base_path)
        else:
            failed_count += 1
            response['message'] = "Allowed image types are -> png, jpg, jpeg, webp, gif"
    
    # If any of the uploads failed, set the overall status to 500
    if failed_count > 0:
        response['message'] = str(failed_count) + " files failed to upload."

    if len(response['files']) == 0:
        response['message'] = response['message']
        response['status'] = 500

    return response

def get_skuvault_brands(store):
    result = {
        'status': 400
    }
    api_info = get_skuvault_api_info(store['id'])
    tenant_token = api_info['tenant_token']
    user_token = api_info['user_token']
    url = "products/getBrands"
    body = {
            "TenantToken": tenant_token,
            "UserToken": user_token
        }
    res = bc.call_api_skuvault("POST", url, {}, body, False)
    if res.status_code != 200:
        result['status'] = 200
        result['data'] = []
    else:
        data = res.json()['Brands'] if res.json()['Brands'] else []
        sorted_brands = sorted(data, key=lambda x: x['Name'])
        result['status'] = 200
        result['data'] = sorted_brands
    return result

def get_skuvault_classification(store):
    result = {
        'status': 400
    }
    api_info = get_skuvault_api_info(store['id'])
    tenant_token = api_info['tenant_token']
    user_token = api_info['user_token']
    url = "products/getClassifications"
    body = {
            "TenantToken": tenant_token,
            "UserToken": user_token
        }
    res = bc.call_api_skuvault("POST", url, {}, body, False)
    if res.status_code != 200:
        result['status'] = 200
        result['data'] = []
    else:
        data = res.json()['Classifications'] if res.json()['Classifications'] else []
        # Remove 'Attributes' key from each item in the data array
        cleaned_data = [{k: v for k, v in item.items() if k != 'Attributes'} for item in data]
        sorted_classifications = sorted(cleaned_data, key=lambda x: x['Name'])
        result['status'] = 200
        result['data'] = sorted_classifications
    return result

def get_skuvault_suppliers(store):
    result = {
        'status': 400
    }
    api_info = get_skuvault_api_info(store['id'])
    tenant_token = api_info['tenant_token']
    user_token = api_info['user_token']
    url = "products/getSuppliers"
    body = {
            "TenantToken": tenant_token,
            "UserToken": user_token
        }
    res = bc.call_api_skuvault("POST", url, {}, body, False)
    if res.status_code != 200:
        result['status'] = 200
        result['data'] = []
    else:
        data = res.json()['Suppliers'] if res.json()['Suppliers'] else []
        sorted_suppliers = sorted(data, key=lambda x: x['Name'])
        result['status'] = 200
        result['data'] = sorted_suppliers
    return result