import threading
import new_pgdb
 
class TaskExecutor(threading.Thread):
     
     def __init__(self, func, *args):
         super(TaskExecutor, self).__init__()
         self.func = func
         self.args = args
         self.is_complete = False
         self.result = None
 
     def run(self):
        try:
            self.result = self.func(*self.args)
        finally:
            self.is_complete = True

def concurrent_db_execution(store, tasks=[]):
    conn_list = []
    executors = []
    result = []
    try:
        for task in tasks:
            conn = new_pgdb.get_connection(store['id'])
            conn_list.append(conn)
            args = task.get('args', [])
            args.insert(0, conn)
            executor = TaskExecutor(task['func'], *task['args'])
            executor.start()
            executors.append(executor)

        for _exe in executors:
            _exe.join()

        for _exe in executors:
            result.append(_exe.result)
    finally:
        for conn in conn_list:
            conn.close()

    return result