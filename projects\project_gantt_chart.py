from projects import check_project_access
from projects.project_card import add_card_history_log, convert_hours_to_format, modify_card_detail
from projects.project_cards import parse_time
from projects.project_timesheet import minutes_to_time, time_to_minutes
from sqlalchemy import text
import pg_db
from mongo_db import user_db
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from datetime import datetime, timedelta
from projects.project_details import _fetch_project_detail
import logging
import traceback
import re
from utils.common import calculate_working_hours, convert_to_date_string, convert_to_timestamp, calculatePaginationData, convert_time_format
import copy
from new_mongodb import get_admin_db_client_for_store_id
from projects import project_task_report

logger = logging.getLogger() 

def get_gantt_chart_data(store_id, project_id, username, report_type, search=None):
    response = {
        'status': 400
    }
    db = get_admin_db_client_for_store_id(store_id)
    conn = pg_db.get_connection()
    try:        
        project_access = check_project_access(project_id, username) 
        if not project_access:
            response['status'] = 403
            response['message'] = "You don't have access for this project."
            return response

        # Fetch user data
        user_data = user_db.fetch_user_by_username(username)
        user_id = str(user_data['_id'])

        # Fetch user preferences from MongoDB
        user_pref = db["user_preference"].find_one({"user_id": user_id, "type": report_type})  

        module_ids = []
        assignees = []
        current_column_ids = []
        priorities = []
        ticket_statuses = []

        if user_pref:
            columns = user_pref.get("columns", {})
            module_ids = columns.get("modules", [])
            assignees = columns.get("assignee", [])
            current_column_ids = columns.get("stages", [])
            priorities = columns.get("priority", [])
            ticket_statuses = columns.get("ticket_status", [])

            if "unassigned" in assignees:
                assignees.append("")         
        
        query = f"""SELECT id, project_id, module_id, ticket_ids from agile_project_cards_gantt_sorting WHERE project_id = :project_id"""
        query = text(query).bindparams(project_id=project_id)
        sorting_result = conn.execute(query).fetchall()
        sorting_details = {}
        if sorting_result:  
            for row in sorting_result:
                sorting_details[row[2]] = [int(ticket_id.strip()) for ticket_id in row[3].split(',')]


        query = f"""SELECT pc.id, pc.project_id, pc.module_id, pc.current_column_id, pc.title, pc.card_identifier, pc.assigned_to, pc.is_archived,  
                    pm.name AS module_name, bc.name AS column_name, pc.due_date, pc.start_date, pc.spent_time, pc.estimation,
                    MIN(pc.start_date) OVER (PARTITION BY pc.project_id) AS lowest_start_date,
                    MAX(pc.due_date) OVER (PARTITION BY pc.project_id) AS highest_due_date,
                    CASE
						WHEN pc.estimation IS NOT NULL AND pc.spent_time IS NOT NULL AND pc.estimation > pc.spent_time THEN 
							pc.estimation - pc.spent_time
						ELSE INTERVAL '0 seconds'
					END AS remaining_time
                FROM agile_project_cards pc
                JOIN agile_project_modules pm ON pc.module_id = pm.id
                JOIN agile_project_columns bc ON pc.current_column_id = bc.id
                WHERE pc.project_id = :project_id and pm.is_visible = true and bc.is_visible = true and bc.is_resolved = false and (pc.due_date IS NOT NULL and pc.due_date >= CURRENT_DATE)"""
        
        # Add search condition if search_value is provided
        if search:
            query += f" AND (pc.card_identifier ILIKE '%" + search + "%' OR pc.title ILIKE '%" + search + "%')"

        # Add filters based on user preferences
        if module_ids:
            query += " AND pc.module_id = ANY(:module_ids)"
        if assignees:
            query += " AND pc.assigned_to = ANY(:assignees)"
        if current_column_ids:
            query += " AND pc.current_column_id = ANY(:current_column_ids)"
        if priorities:
            query += " AND pc.priority = ANY(:priorities)"
        if ticket_statuses:
            query += " AND pc.status = ANY(:ticket_statuses)"
        
        query += " ORDER BY pm.sort_id, bc.sort_id, pc.sort_id"

        filters_response = project_task_report.get_filters([project_id], user_pref)

        query = text(query)
        query = query.params(project_id=project_id, module_ids=module_ids, assignees=assignees, current_column_ids=current_column_ids, priorities=priorities, ticket_statuses=ticket_statuses)
        result = conn.execute(query)
        rows = result.fetchall()
        lowest_start_date = None
        highest_due_date = None
        data = []
        for row in rows:
            if not lowest_start_date:
                lowest_start_date = row[14] if row[14] else datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            if not highest_due_date:
                highest_due_date = row[15]
            card = {}
            card['tickedID'] = row[0]
            card['projectID'] = row[1]
            card['moduleID'] = row[2]
            card['moduleName'] = row[8]
            card['columnID'] = row[3]
            card['columnName'] = row[9]
            card['card_identifier'] = row[5]
            card['title'] = row[4]
            card['assignee'] = row[6]
            card['is_archived'] = row[7]
            card['due_date'] = convert_to_timestamp(row[10])
            card['start_date'] = convert_to_timestamp(row[11])
            card['spent_time'] = minutes_to_time(time_to_minutes(convert_time_format(row[12])))
            card['estimation'] = minutes_to_time(time_to_minutes(convert_time_format(row[13])))
            card['remaining_time'] = convert_time_format(row[16])
            card['total_duration'] = calculate_working_hours(row[11], row[10])
            
            data.append(card)

        converted_data = {}
        for item in data:
            column_name = item['columnName']
            module_name = item['moduleName']
            column_id = f"{module_name.lower()}_{column_name.replace(' ', '-').lower()}"
            module_id = f"{module_name.lower()}_{str(item['moduleID'])}"
            ticket_id = item['tickedID']            

            # Check if the column entry exists in the converted data, if not create it
            if module_id not in converted_data:
                converted_data[module_id] = {
                    "columnName": column_name.split('_')[0],
                    "columnID": column_id,
                    "cID": item['columnID'],
                    "moduleName": module_name.split('_')[0],
                    "moduleID": module_id,
                    "mID": item['moduleID'],
                    "columnItems": [],
                    "total_spent_time": 0,
                    "total_estimation": 0,
                    "total_progress": 0
                }
            
            progress_parcentage = round((parse_time(item['spent_time']) / parse_time(item['estimation'])) * 100, 2) if parse_time(item['estimation']) else 0
            user_data = user_db.fetch_user_by_username(item['assignee'])
            if user_data:
                name = user_data.get('name', '')
            else:
                name = 'Unassigned'

            # Add the ticket under columnItems for the corresponding column
            converted_data[module_id]['columnItems'].append({
                "ticketID": ticket_id,
                "columnID": column_id,
                "cID": item['columnID'],                
                "moduleName": module_name.split('_')[0],
                "moduleID": module_id,
                "mID": item['moduleID'],
                "projectID": item['projectID'],
                "title": item['title'],
                "card_identifier": item['card_identifier'],
                "assignee": item['assignee'],
                "asignee_name": name,
                "is_archived": item['is_archived'],
                "due_date": item['due_date'],
                "start_date": item['start_date'],
                "spent_time": item['spent_time'],
                "estimation": item['estimation'],
                "progress": progress_parcentage if progress_parcentage <= 100 else 100,
                "remaining_time": item['remaining_time'],
                "total_duration": convert_hours_to_format(item['total_duration'])
            })
            # Update total spent time and estimation
            converted_data[module_id]['total_spent_time'] += parse_time(item['spent_time'])
            converted_data[module_id]['total_estimation'] += parse_time(item['estimation'])
        
        for module_id, module_data in converted_data.items():
            mID = module_data["mID"]  # Retrieve mID
            column_items = module_data["columnItems"]
            total_progress = round((module_data['total_spent_time'] / module_data['total_estimation']) * 100, 2) if module_data['total_estimation'] else 0
            module_data['total_progress'] = total_progress if total_progress <= 100 else 100

            module_data['total_spent_time'] = minutes_to_time(time_to_minutes(convert_hours_to_format(module_data['total_spent_time'])))
            module_data['total_estimation'] = minutes_to_time(time_to_minutes(convert_hours_to_format(module_data['total_estimation'])))
            
            
            # Check if mID is in sorting_details
            if mID in sorting_details:
                ticket_order = sorting_details[mID]  # Get the preferred order of ticketIDs

                # Create a sort key function
                def sort_key(item):
                    ticket_id = item["ticketID"]
                    # If ticketID is in the preferred order, use its index; otherwise, assign a high index
                    return ticket_order.index(ticket_id) if ticket_id in ticket_order else len(ticket_order)

                # Sort columnItems
                column_items.sort(key=sort_key)

        converted_data_list = list(converted_data.values())

        res = {}
        res['lowest_start_date'] = convert_to_timestamp(lowest_start_date)
        res['highest_due_date'] = convert_to_timestamp(highest_due_date)
        res['total_duration'] = convert_hours_to_format(calculate_working_hours(lowest_start_date, highest_due_date))
        res['data'] = converted_data_list
        response['status'] = 200
        response['data'] = {
            "gantt_chart": res,
            "filters": filters_response["filters"]
        }
    finally:       
        conn.close()
    return response   


def update_ticket_using_gantt_chart(store, project_id, req_body, username):
    response = {
        'status': 400
    }
    conn = pg_db.get_connection()
    try:     
        ticket_id = req_body.get('ticket_id', None)   
        project_access = check_project_access(project_id, username) 
        if not project_access:
            response['status'] = 403
            response['message'] = "You don't have access for this project."
            return response         
        if ticket_id:
            card_query = text(
                f"""SELECT pc.id, pc.project_id, pc.assigned_to, pc.spent_time, pc.estimation, pc.due_date, pc.is_archived, pc.start_date                    
                    FROM agile_project_cards pc WHERE pc.id = :card_id and pc.project_id = :project_id""")
            card_data = conn.execute(card_query, {'card_id': ticket_id, 'project_id': project_id}).fetchone()
            if card_data:
                old_spent_time = card_data[3]
                old_estimation = card_data[4]
                old_estimation_str = copy.deepcopy(card_data[4])
                new_estimation = 0
                old_due_date = card_data[5]
                old_start_date = card_data[7]

                old_difference = old_estimation - old_spent_time if old_estimation and old_spent_time and old_estimation > old_spent_time else 0
                
                log_payload = []
                update_fields = {}
                for field in ['due_date', 'start_date', 'remaining_time']:
                    if field in req_body:
                        if field == 'due_date' or field == 'start_date':
                            req_body[field] = convert_to_date_string(req_body[field]) 
                        log_payload.append((field, req_body[field]))   
                            
                        if field == 'remaining_time':
                            updated_estimation = parse_time(req_body[field])
                            old_difference = convert_hours_to_format(old_difference) if isinstance(old_difference, int) else convert_time_format(old_difference)
                            updated_estimation = updated_estimation - parse_time(old_difference)

                            old_estimation = convert_time_format(old_estimation)
                            new_estimation = updated_estimation + parse_time(old_estimation)

                            conn.execute(text(f"""UPDATE agile_project_cards SET estimation = '0 hours' WHERE 
                                             id = {ticket_id} AND project_id = {project_id};"""))
                            conn.commit()

                            update_fields['estimation'] = str(new_estimation) + " hours"
                        else:
                            update_fields[field] = req_body[field]
                
                # add logs for the updated changes 
                log_payload_array = []
                if log_payload:
                    for field, value in log_payload:
                        field_label = field
                        if field == 'due_date':
                            old_value = old_due_date
                        elif field == 'start_date':
                            old_value = old_start_date
                        elif field == 'remaining_time':
                            field_label = 'estimation'
                            old_value = convert_time_format(old_estimation_str)
                            value = convert_hours_to_format(new_estimation)
                        
                        data = {}
                        data['field_name'] = field_label
                        data['old_value'] = old_value
                        data['new_value'] = value
                        data['created_by'] = username
                        data['ticket_id'] = ticket_id
                        log_payload_array.append(data)

                if log_payload_array:
                    add_card_history_log(log_payload_array)
                        
                data = modify_card_detail(conn, update_fields, project_id, ticket_id, username)
                if data:
                    response['status'] = 200
                    response['message'] = "Card updated successfully."
                else:
                    response['status'] = 400
                    response['message'] = "Card updation failed."
    except IntegrityError as e:
        response['status'] = 500
        response['message'] = "Something went wrong."  
    finally:       
        if conn:
            conn.commit()
            conn.close()
    return response   

def update_ticket_sorting_for_gantt_chart(project_id, card_id, module_id, ticket_ids, username):
    response = {
        'status': 400
    }
    conn = pg_db.get_connection()
    try:        
        project_access = check_project_access(project_id, username) 
        if not project_access:
            response['status'] = 403
            response['message'] = "You don't have access for this project."
            return response
        if card_id and module_id:
            # Update the card details for the given card_id and module_id
            query = text(f"""SELECT pc.id, pc.project_id, pc.current_column_id, pc.module_id FROM {pg_db.project_cards} pc WHERE pc.id = :card_id and pc.project_id = :project_id""")
            card_data = conn.execute(query, {'card_id': card_id, 'project_id': project_id}).fetchone()
            if card_data:
                old_column_id = card_data[2]
                
                # Fetch the highest sort_id for the given column and module
                sort_id_query = conn.execute(
                    text(f"SELECT MAX(sort_id) FROM {pg_db.project_cards} WHERE project_id = :project_id AND module_id = :module_id AND current_column_id = :current_column_id"),
                    {'project_id': project_id, 'module_id': module_id, 'current_column_id': old_column_id}
                )
                highest_sort_id = sort_id_query.scalar()
                new_sort_id = highest_sort_id + 1 if highest_sort_id is not None else 1

                # Update the card details 
                update_query = text(f"""UPDATE {pg_db.project_cards} SET module_id = :module_id, current_column_id = :current_column_id, sort_id = :sort_id WHERE id = :card_id and project_id = :project_id""")
                update_query = update_query.params(module_id=module_id, current_column_id=old_column_id, sort_id=new_sort_id, project_id=project_id, card_id=card_id)
                conn.execute(update_query)
                conn.commit()

            #store sorting details of tickets in modules for gantt chart
            query = text(
                """
                INSERT INTO agile_project_cards_gantt_sorting (project_id, module_id, ticket_ids, created_by, updated_by)
                VALUES (:project_id, :module_id, :ticket_ids, :created_by, :created_by)
                ON CONFLICT (project_id, module_id) 
                DO UPDATE SET 
                    ticket_ids = EXCLUDED.ticket_ids, 
                    updated_by = EXCLUDED.updated_by;
                """
            )
            query = query.params(project_id=project_id, module_id=module_id, ticket_ids=ticket_ids, created_by=username)
            conn.execute(query)
            conn.commit()
            response['status'] = 200
            response['message'] = "Card sorting updated successfully."
        else:
            response['status'] = 400
            response['message'] = "Module id or ticket ids not found. Please check payload."
            
    except IntegrityError as e:
        response['status'] = 500
        response['message'] = "Something went wrong."  
    finally:       
        if conn:
            conn.close()
    return response   