from new_mongodb import fetchall_documents_from_admin_collection
from sqlalchemy import text
import pg_db
from mongo_db import user_db
import mongo_db
import traceback
import logging
from utils import common
from datetime import datetime

logger = logging.getLogger()


def get_global_permitted_members(store_id):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        selected_users = []
        query = f"""SELECT pa.id, pa.username, pa.status FROM {pg_db.project_access} pa WHERE pa.project_id IS NULL AND pa.status = 'active'"""
        result = conn.execute(text(query))
        for row in result:
            selected_users.append(row[1])
        users_cursor = fetchall_documents_from_admin_collection(store_id, "users", {'status': 'active', 'type': 'admin_app_default_user'})
        users_with_permissions = []

        if users_cursor:
            for user_document in users_cursor:
                if user_document:
                    name = user_document.get('name', '')
                    username = user_document.get('username', '')
                    role_id = user_document.get('role_id', '')
                    if role_id:
                        permissions = user_db.get_usr_permissions_with_role_id(role_id)
                        if permissions and username not in selected_users:
                            users_with_permissions.append({'name': name, 'username': username})
            if users_with_permissions:
                response['data'] = users_with_permissions
                response['status'] = 200
            else:
                response['data'] = []
                response['status'] = 200
        else:
            response['message'] = 'No users retrieved from database.'
            response['status'] = 404
    except Exception as e:
        response['message'] = str(e)
        response['status'] = 500
    finally:
        if conn:
            conn.close()

    return response



    
