from services import Service
from bson import ObjectId
from utils import redis_util
import mongo_db
from datetime import datetime


class Component(Service):
    def __init__(self, repository):
        super().__init__(repository)

    def create(self, req_body):
        req_body["code"] = req_body["name"].replace(" ", "_").lower()
        req_body["created_at"] = int(datetime.utcnow().timestamp())
        req_body["updated_at"] = ""
        req_body["created_by"] = "user"
        req_body["modified_by"] = "user"

        id = super().create(req_body)
        return id

    def get_all_component(self):
        components = super().find_all()
        return super().processList(components)

    def getVariant(self, req_body):
        result = {}
        reqArray = req_body["unique_key"].split("_")
        id = reqArray[0]
        style = reqArray[1]

        component = super().find_one({"_id": ObjectId(str(id))})

        for variant in component["variants"]:
            if variant["admin_layout"] == style:
                selectedVariant = variant

        result["code"] = component["code"]
        result["name"] = component["name"]
        result["variant"] = selectedVariant
        result["visibility"] = component.get("visibility", "both")

        return result
