from utils import bc, store_util
import requests
import json
from new_mongodb import store_admin_db
import new_pgdb
from datetime import datetime
import traceback
import logging
from sqlalchemy import text
import pytz

logger = logging.getLogger()

ORDER_API = "v2/orders/{order_id}"

def fetch_order(store, order_id):
    url = ORDER_API.format(order_id=order_id)
    bc_api = store_util.get_bc_api_creds(store)
    return bc.call_api(bc_api, "GET", url)

def fetch_order_coupon(store, url):
    bc_api = store_util.get_bc_api_creds(store)
    return bc.call_api(bc_api, "GET", url)

def create_order(store, order_payload):
    bc_api = store_util.get_bc_api_creds(store)
    api = "v2/orders"
    return bc.call_api(api_data=bc_api, method="POST", url=api, req_body=order_payload)

def update_order(store, order_payload, order_id):
    bc_api = store_util.get_bc_api_creds(store)
    api = f"v2/orders/{order_id}"
    return bc.call_api(api_data=bc_api, method="PUT", url=api, req_body=order_payload)

def get_order_products(store, order_id):
    bc_api = store_util.get_bc_api_creds(store)
    api = f"v2/orders/{order_id}/products"
    return bc.call_api(api_data=bc_api, method="GET", url=api)

def create_cart(store, cart_payload):
    bc_api = store_util.get_bc_api_creds(store)
    cart_payload['channel_id'] = bc_api['channel_id']
    api = "v3/carts"    
    return bc.call_api(api_data=bc_api, method="POST", url=api, req_body=cart_payload)

def create_consignment(store, consignment_payload, cart_id):
    bc_api = store_util.get_bc_api_creds(store)
    api = f"v3/checkouts/{cart_id}/consignments?include=consignments.available_shipping_options"   
    return bc.call_api(api_data=bc_api, method="POST", url=api, req_body=consignment_payload)

def update_consignment_shipping_option(store, consignment_payload, cart_id, consignment_id):
    bc_api = store_util.get_bc_api_creds(store)
    api = f"v3/checkouts/{cart_id}/consignments/{consignment_id}"
    return bc.call_api(api_data=bc_api, method="PUT", url=api, req_body=consignment_payload)

def update_order_notes(store_id, order_id, username, customer_id):
    """
    Update order notes by calling the external serial_history API
    
    Args:
        store: Store configuration
        order_id: BigCommerce order ID
        order_notes: New note content to add
    
    Returns:
        API response from the serial_history endpoint
    """
    # API endpoint
    url = "https://midwestgoods.mobi/serial_history"
    
    # Headers
    headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'tok': 'saddasdgf1PFKxsfsfazzEKnRcxsgYdsfxzzzI2'
    }
    user = store_admin_db.fetch_user_by_username(store_id, username)
    rep = _fetch_customer_rep(store_id, customer_id)
    rep_name = rep[0] if rep else None
    rep_email = rep[1] if rep else None

    # Get current time in CST/CDT (automatically handles daylight saving time)
    central_tz = pytz.timezone('US/Central')
    current_time = datetime.now(central_tz).strftime('%Y-%m-%d %H:%M:%S %Z')

    if user:
        order_notes = f"Order was placed by {user['name']}: {username} from Bulk order feature at {current_time}. Order managed by {rep_name}: {rep_email}"
    else:
        order_notes = f"Order was placed by {username} from Bulk order feature at {current_time}. Order managed by {rep_name}: {rep_email}"
    
    # Request payload
    payload = {
        "Clicker": username,
        "BC_OrderId__c": str(order_id),
        "CBD_Order_Id__c": "",
        "new_note": order_notes
    }
    
    try:
        # Make the API call
        response = requests.post(
            url=url,
            headers=headers,
            json=payload,
            timeout=30  # 30 second timeout
        )
        
        # Return the response
        return response
        
    except requests.exceptions.RequestException as e:
        # Log the error and return None or raise exception based on your error handling strategy
        logger.error(traceback.format_exc())
        return None

def _fetch_customer_rep(store_id, customer_id):
    conn = new_pgdb.get_connection(store_id)
    try:
        query = """SELECT rep_name, rep_email FROM salesforce_customer_rep WHERE customer_id = :customer_id"""
        result = conn.execute(text(query), {'customer_id': int(customer_id)})
        return result.fetchone()
    except Exception as e:
        logger.error(traceback.format_exc())
        return None
    finally:
        conn.close()
    
