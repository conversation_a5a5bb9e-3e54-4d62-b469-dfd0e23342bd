from utils import bc, store_util

ORDER_API = "v2/orders/{order_id}"

def fetch_order(store, order_id):
    url = ORDER_API.format(order_id=order_id)
    bc_api = store_util.get_bc_api_creds(store)
    return bc.call_api(bc_api, "GET", url)

def fetch_order_coupon(store, url):
    bc_api = store_util.get_bc_api_creds(store)
    return bc.call_api(bc_api, "GET", url)

def create_order(store, order_payload):
    bc_api = store_util.get_bc_api_creds(store)
    api = "v2/orders"
    return bc.call_api(api_data=bc_api, method="POST", url=api, req_body=order_payload)

def update_order(store, order_payload, order_id):
    bc_api = store_util.get_bc_api_creds(store)
    api = f"v2/orders/{order_id}"
    return bc.call_api(api_data=bc_api, method="PUT", url=api, req_body=order_payload)

def get_order_products(store, order_id):
    bc_api = store_util.get_bc_api_creds(store)
    api = f"v2/orders/{order_id}/products"
    return bc.call_api(api_data=bc_api, method="GET", url=api)

def create_cart(store, cart_payload):
    bc_api = store_util.get_bc_api_creds(store)
    cart_payload['channel_id'] = bc_api['channel_id']
    api = "v3/carts"    
    return bc.call_api(api_data=bc_api, method="POST", url=api, req_body=cart_payload)

def create_consignment(store, consignment_payload, cart_id):
    bc_api = store_util.get_bc_api_creds(store)
    api = f"v3/checkouts/{cart_id}/consignments?include=consignments.available_shipping_options"   
    return bc.call_api(api_data=bc_api, method="POST", url=api, req_body=consignment_payload)

def update_consignment_shipping_option(store, consignment_payload, cart_id, consignment_id):
    bc_api = store_util.get_bc_api_creds(store)
    api = f"v3/checkouts/{cart_id}/consignments/{consignment_id}"
    return bc.call_api(api_data=bc_api, method="PUT", url=api, req_body=consignment_payload)