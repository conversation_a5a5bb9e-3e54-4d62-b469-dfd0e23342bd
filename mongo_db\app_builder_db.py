import logging
import datetime
from bson import ObjectId
import mongo_db

logger = logging.getLogger()

METADATA_COLLECTION = "metadata"
DATABASE_NAME = "app_builder"
db_client = mongo_db.get_db_client(DATABASE_NAME)


def fetch_section_by_name(section_name):
    section_name = section_name.strip().lower().replace(' ', '_')
    result = db_client[METADATA_COLLECTION].find_one(
        {
            "template.collection_name": str(section_name),
            "status": "active"
        })
    return result


def fetch_all_main_app(search=None):
    query = {}
    
    # Add search condition if search term is provided
    if search:
        query = {"name": {"$regex": search, "$options": "i"}}  # Case-insensitive partial match
    
    results = db_client[METADATA_COLLECTION].find(query)
    results = mongo_db.processList(results)
    return results


def fetch_all_sub_section(parent):
    parent_section = fetch_section_by_name(parent)
    if parent_section:
        children = parent_section.get('children', [])
        return children  


def fetch_section_by_id(section_id):
    result = db_client[METADATA_COLLECTION].find_one(
        {"_id": ObjectId(str(section_id))})
    return result

def fetch_leaf_by_name(leaf_name, grand_parent,parent):
     grand_parent_section = fetch_section_by_name(grand_parent)
     if grand_parent_section:
        parent_result = next((child for child in grand_parent_section.get('children', []) if child.get(
            'name', '').strip().lower().replace(' ', '_') == parent), None)
        if parent_result:
            parent_children = parent_result.get('children', [])
            child_to_find = next((child for child in parent_children if child.get(
                'name').strip().lower().replace(' ', '_') == str(leaf_name)), None)
            if child_to_find is None:
               return {
                "status": 200,
                "message": "Sub-section is valid and not a duplicate."
               }  
            else:
                return {
                    "status": 404,
                    "message": "Leaf already exist."
                }
        else:
            return {
                "status": 404,
                "message": "Sub-section not found in the child array."
            }
     else:
        return {
            "status": 404,
            "message": "Parent section not found."
        }
   
def fetch_sub_section_by_name(sub_section_name, parent_section):
    sub_section = sub_section_name.strip().lower().replace(' ', '_')
    parent_result = db_client[METADATA_COLLECTION].find_one(
        {
            "template.collection_name": str(parent_section),
            "status": "active"
        })
    if parent_result:
        children = parent_result.get('children', [])
       
        child_with_name = next((child for child in children if child.get(
            'name', '').strip().lower().replace(' ', '_') == sub_section), None)
       
        if child_with_name is None:
            return {
                "status": 200,
                "message": "name: Sub-section is valid and not a duplicate."
            }
        else:
            return {
                "status": 409,
                "message": "name: Sub-section already exists with the same name."
            }
    else:
        return {
            "status": 404,
            "message": "Parent section not found."
        }


def set_main_app_to_db(payload):
    result = db_client[METADATA_COLLECTION].insert_one(payload)
    return result

def update_leaf_to_db(grandparent_parent_collection, prev_collection_name,leaf, name, is_status_update,grand_parent, parent):
    result=''
    if not is_status_update:
        new_collection_name =grand_parent+'_'+parent+'_'+ leaf['name'].strip().lower().replace(' ', '_')
        grandparent_result = fetch_section_by_name(grand_parent)
        if grandparent_result:
            parent_result = next((child for child in grandparent_result.get('children', []) if child.get(
            'name', '').strip().lower().replace(' ', '_') == parent), None)
            if parent_result:
               old_childrens = parent_result.get('children', [])
               for i, child in enumerate(old_childrens):
                   if 'template' in child and 'collection_name' in child['template'] and child['template']['collection_name'] == prev_collection_name:
                       old_childrens[i] = leaf
        parent_result['children']=old_childrens
        grand_parent_childrens=grandparent_result.get('children',[])
        for i, child in enumerate(grand_parent_childrens):
            if 'template' in child and 'collection_name' in child['template'] and child['template']['collection_name'] == grandparent_parent_collection:
                       grand_parent_childrens[i] = parent_result
        grandparent_result['children']=grand_parent_childrens
        updated_parent_query = {"_id": grandparent_result["_id"]}
        updated_parent_data = {"$set": {"children": grand_parent_childrens}}
        result=db_client[METADATA_COLLECTION].update_one(
                updated_parent_query, updated_parent_data)
        update_collection_name(prev_collection_name,new_collection_name)
    
    else:
               document =fetch_section_by_name(grand_parent)
               grand_parent_childrens=document.get('children',[])         
               status=leaf['status']
               index_1 = next((i for i, child in enumerate(document['children']) if child['template']['collection_name'] == grandparent_parent_collection), None)
               index_2 = next((i for i, child in enumerate(document['children'][index_1]['children']) if child['name'] == leaf['old_name']), None)
               if index_1 is not None and index_2 is not None:
                  result = db_client[METADATA_COLLECTION].update_one(
                           {"_id": ObjectId(str(document["_id"]))},
                           {"$set": {f"children.{index_1}.children.{index_2}.status": status}}
                    )
                 
    return result

def update_subsection_to_db(prev_collection_name,sub_section, name, is_status_update,parent_section):
    
       result=''
       if not is_status_update:
           new_collection_name = parent_section+'_'+ sub_section['name'].strip().lower().replace(' ', '_')
           leaf_children = sub_section.get('children', [])
           if len(leaf_children):
               updated_child = update_leaf_collection(
                leaf_children, parent_section, sub_section['name'].strip().lower().replace(' ', '_'))
               sub_section['children'] = updated_child            
           parent_section_array = fetch_section_by_name(parent_section)
           old_childrens = parent_section_array.get('children', [])
           for i, child in enumerate(old_childrens):
               if 'template' in child and 'collection_name' in child['template'] and child['template']['collection_name'] == prev_collection_name:
                   old_childrens[i] = sub_section
           parent_section_array['children'] = old_childrens            
           updated_parent_query = {"_id": parent_section_array["_id"]}
           updated_parent_data = {"$set": {"children": old_childrens}}
           result=db_client[METADATA_COLLECTION].update_one(
                updated_parent_query, updated_parent_data)
           update_collection_name(prev_collection_name,new_collection_name)
       else:
               status=sub_section['status']
               parent_section_array = fetch_section_by_name(parent_section)
               result = db_client[METADATA_COLLECTION].update_one(
                 {
              "_id": ObjectId(str(parent_section_array["_id"])),
              "children.name": sub_section['name']
                 },
                 {
                   "$set": {"children.$.status": status}
                  }
               ) 
                
       return result

def update_main_app_to_db(payload, id, is_status_update):
    result=''
    if not is_status_update:
        new_collection_name = payload['name'].strip().lower().replace(' ', '_')
        parent_children = payload.get('children', [])
        if len(parent_children):           
            updated_child = update_child_collection(
                parent_children, new_collection_name)
            payload['children'] = updated_child
        result = db_client[METADATA_COLLECTION].update_one(
                {"_id": ObjectId(str(id))},
                {"$set": payload}
            )            
    else:
        result = db_client[METADATA_COLLECTION].update_one({"_id": ObjectId(str(id))}, {"$set":
                                                                      {
                                                                          "name": payload['name'].strip(),
                                                                          "description": payload['description'],
                                                                          "status": payload['status'],
                                                                          "icon_image":payload['icon_image']
                                                                      }
                                                                      })
        
    return result


def update_child_collection(children, new_collection_name):
    for child in children:
        leaf_children = child.get('children', [])
        if leaf_children:
            parent_name = child['name'].strip().lower().replace(' ', '_')
            updated_leaf_children = update_leaf_collection(
                leaf_children, new_collection_name, parent_name)
            child['children'] = updated_leaf_children
        child['parent_collection'] = new_collection_name.strip(
        ).lower().replace(' ', '_')
        template = child.get('template')
        if template:
            old_name = template['collection_name']
            new_name = new_collection_name.strip().lower().replace(' ', '_') + '_' + \
                child['name'].strip().lower().replace(' ', '_')
            template['collection_name'] = new_name
            update_collection(new_name, old_name)
    return children


def update_leaf_collection(children, grand_parent_name, parent_name):
    for child in children:
        child['parent_collection'] = parent_name
        template = child.get('template')
        if template:
            old_name = template['collection_name']
            new_name = grand_parent_name + '_' + parent_name + \
                '_' + child['name'].strip().lower().replace(' ', '_')
            template['collection_name'] = new_name
            update_collection(new_name, old_name)
    return children


def set_sub_section_to_main(payload, parent_name):
    parent_query = {
        "template.collection_name": str(parent_name),
        "status": "active"
    }
    parent_result = db_client[METADATA_COLLECTION].find_one(parent_query)
    if parent_result:
        children = parent_result.get('children', [])
        children.append(payload)

        updated_parent_query = {"_id": parent_result["_id"]}
        updated_parent_data = {"$set": {"children": children}}
        db_client[METADATA_COLLECTION].update_one(
            updated_parent_query, updated_parent_data)
        return {
            "status": 200,
            "message": "Child added to parent successfully."
        }
    else:
        return {
            "status": 404,
            "message": "Parent section not found."
        }


def set_leaf_to_sub_section(payload, parent, grand_parent):
    grandparent_name = grand_parent.strip().lower().replace(' ', '_')
    parent_name = parent.strip().lower().replace(' ', '_')

    grandparent_query = {
        "template.collection_name": str(grandparent_name),
        "status": "active"
    }
    grandparent_result = db_client[METADATA_COLLECTION].find_one(
        grandparent_query)

    if grandparent_result:
        parent_result = next((child for child in grandparent_result.get('children', []) if child.get(
            'name', '').strip().lower().replace(' ', '_') == parent_name), None)
        if parent_result:
            parent_children = parent_result.get('children', [])
            if any(child.get('name', '').strip().lower().replace(' ', '_') == payload['name'].strip().lower().replace(' ', '_') for child in parent_children):
                return {
                    "status": 409,
                    "message": "name: Child with the same name already exists."
                }
            parent_children.append(payload)
            for child in grandparent_result.get('children', []):
                if child.get('name', '').strip().lower().replace(' ', '_') == parent_name:
                    child['children'] = parent_children
                    break

            updated_grandparent_query = {"_id": grandparent_result["_id"]}
            updated_grandparent_data = {
                "$set": {"children": grandparent_result["children"]}}
            db_client[METADATA_COLLECTION].update_one(
                updated_grandparent_query, updated_grandparent_data)

            return {
                "status": 200,
                "message": "Child added to parent successfully."
            }
        else:
            return {
                "status": 404,
                "message": "Parent not found."
            }
    else:
        return {
            "status": 404,
            "message": "Grandparent not found."
        }


def delete_section_by_id(section_id):
    result = db_client[METADATA_COLLECTION].delete_one(
        {"_id": ObjectId(str(section_id))})
    if result.deleted_count > 0:
        return {
            "status": 200,
            "message": "Section deleted successfully."
        }
    else:
        return {
            "status": 404,
            "message": "Section not found."
        }
  

def fetch_all_leafs(grand_parent,parent):
    parent_section = fetch_section_by_name(parent)
    if parent_section:
        children = parent_section.get('children', [])
    return children
def find_leaf(old_name,grand_parent,parent):
     grand_parent_section = fetch_section_by_name(grand_parent)
     if grand_parent_section:
        parent_result = next((child for child in grand_parent_section.get('children', []) if child.get(
            'name', '').strip().lower().replace(' ', '_') == parent), None)
        if parent_result:
            parent_children = parent_result.get('children', [])
            child_to_find = next((child for child in parent_children if child.get(
                'name').strip().lower().replace(' ', '_') == str(old_name)), None)
            if child_to_find:
               return child_to_find
   

def find_sub_section(section_name,parent):
    parent_section = fetch_section_by_name(parent)
    if parent_section:
        children = parent_section.get('children', [])
        child_to_find = next((child for child in children if child.get(
            'name').strip().lower().replace(' ', '_') == str(section_name)), None) 
        if child_to_find:
            return child_to_find

def delete_sub_section(section_name, parent):
    parent_section = fetch_section_by_name(parent)
    if parent_section:
        children = parent_section.get('children', [])
        child_to_delete = next((child for child in children if child.get(
            'name').strip().lower().replace(' ', '_') == str(section_name)), None)
        if child_to_delete:
            children.remove(child_to_delete)
            parent_section['children'] = children

            updated_parent_query = {"_id": parent_section["_id"]}
            updated_parent_data = {"$set": {"children": children}}
            db_client[METADATA_COLLECTION].update_one(
                updated_parent_query, updated_parent_data)

            return {
                "status": 200,
                "message": "Sub-section deleted successfully."
            }
        else:
            return {
                "status": 404,
                "message": "Sub-section not found in the child array."
            }
    else:
        return {
            "status": 404,
            "message": "Parent section not found."
        }
def get_leaf_section( grand_parent, parent):
    grand_parent_section = fetch_section_by_name(grand_parent)
    if grand_parent_section:
        children = grand_parent_section.get('children', [])
        parent_result = next((child for child in children if child.get(
            'name', '').strip().lower().replace(' ', '_') == parent), None)
        if parent_result:
            parent_children = parent_result.get('children', [])
            return parent_children

def delete_leaf_section(leaf_name, grand_parent, parent):
    grand_parent_section = fetch_section_by_name(grand_parent)
    if grand_parent_section:
        parent_result = next((child for child in grand_parent_section.get('children', []) if child.get(
            'name', '').strip().lower().replace(' ', '_') == parent), None)
        if parent_result:
            parent_children = parent_result.get('children', [])
            child_to_delete = next((child for child in parent_children if child.get(
                'name').strip().lower().replace(' ', '_') == str(leaf_name)), None)
            if child_to_delete:
                parent_children.remove(child_to_delete)
                parent_result['children'] = parent_children

                for child in grand_parent_section.get('children', []):
                    if child.get('name', '').strip().lower().replace(' ', '_') == parent:
                        child['children'] = parent_children
                        break

                updated_grandparent_query = {
                    "_id": grand_parent_section["_id"]}
                updated_grandparent_data = {
                    "$set": {"children": grand_parent_section["children"]}}
                db_client[METADATA_COLLECTION].update_one(
                    updated_grandparent_query, updated_grandparent_data)

                return {
                    "status": 200,
                    "message": "Leaf-section deleted successfully."
                }
            else:
                return {
                    "status": 404,
                    "message": "Leaf-section not found in the child array."
                }
        else:
            return {
                "status": 404,
                "message": "Sub-section not found in the child array."
            }
    else:
        return {
            "status": 404,
            "message": "Parent section not found."
        }


def create_collection(COLLECTION_NAME):
    result = db_client.create_collection(COLLECTION_NAME)
    return result


def update_collection(new_name, old_name):
    documents = list(db_client[old_name].find())
    if documents:
        result = db_client[new_name].insert_many(documents)
      
        return result
    db_client[old_name].rename(new_name)
  

def update_collection_name(old_name,new_name):    
    db_client[old_name].rename(new_name)

def delete_collection(collection_name):
    db_client[collection_name].drop()
