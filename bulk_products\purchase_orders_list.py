from sqlalchemy import text
import pg_db
from datetime import datetime
from utils.common import calculatePaginationData, convert_to_timestamp
import logging
logger = logging.getLogger()

def get_purchase_orders_list(page, limit, filter, sort_array=[],status='', rep_filter='', product_id_array=[]):
    response = {
        "status": 400,
    }
    conn = pg_db.get_connection()
    try:
        append_query = ''

        if status == '':
            append_query = f"WHERE po.status IN ('pending', 'draft', 'partially fulfilled') "
        else:
            append_query = f"WHERE po.status = '{status}' "

        if filter and filter != '':
            append_query += f" and CAST(po.po_id AS TEXT) ILIKE '%{filter}%'"

        if rep_filter and rep_filter != '':
            append_query += f" and CAST(po.customer_rep_email AS TEXT) ILIKE '%{rep_filter}%'"

        if len(product_id_array):
            p_id_string = ",".join(product_id_array)
            append_query += f" and po.po_id IN (SELECT DISTINCT po_id FROM bo_purchase_order_lineitems WHERE bop_id IN ({p_id_string}) )"

               
        count_query = f"""SELECT COUNT(*) FROM (SELECT po.po_id, po.status FROM bo_purchase_orders AS po {append_query} GROUP BY po.po_id, po.status) AS subquery""" 
        final_count_query = count_query.format(append_query)         
        result_count = conn.execute(text(final_count_query))
        total_count = int(result_count.scalar())
   
        base_query = f"""WITH salesforce_totals AS (
                            SELECT sod.customer_id, COALESCE(SUM(sod.amount_due), 0) AS total_amount_due
                            FROM salesforce_order_details sod GROUP BY sod.customer_id
                        )
                        SELECT po.po_id, po.customer_id, po.customer_name, po.customer_rep_id, po.customer_rep_name, po.customer_rep_email, po.type, po.created_by, po.created_at AS date_created, po.updated_at, 
                        po.status, COUNT(pob.po_id) AS entry_count, SUM(pob.order_total) AS total_order_total,
                        (SELECT COUNT(DISTINCT bop_id) FROM bo_purchase_order_lineitems WHERE po_id = po.po_id) AS distinct_bop_count, po.reason, scr.payment_term, 
                        COALESCE(sod.total_amount_due, 0) AS total_amount_due, scr.credit_limit
                        FROM bo_purchase_orders AS po 
                        LEFT JOIN bo_purchase_order_bc_order_mapping AS pob ON po.po_id = pob.po_id 
                        LEFT JOIN salesforce_customer_rep AS scr ON scr.customer_id = po.customer_id
                        LEFT JOIN salesforce_totals sod ON sod.customer_id = po.customer_id
                        {append_query}
                        GROUP BY po.po_id, po.customer_id, po.customer_name, po.customer_rep_id, po.customer_rep_name, po.customer_rep_email, po.type, po.created_by, po.created_at, po.updated_at, po.status, scr.payment_term, sod.total_amount_due, scr.credit_limit
                    """        
        
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ["po_id", "date_created", "customer_name", "total_amount_due", "payment_term", "credit_limit"]:
                nulls_order = "NULLS FIRST" if sort_direction.lower() == "asc" else "NULLS LAST"                
                base_query += f" ORDER BY {sort_array[0]} {sort_direction} {nulls_order}"
        offset = (page - 1) * limit
        if page and limit:
            base_query += f" LIMIT {limit} OFFSET {offset}"
        
        final_query = base_query.format(append_query)     
        result = conn.execute(text(final_query))
        data = []    
        for row in result.fetchall():
            customer_query=text(f"""SELECT email, company FROM customers 
            WHERE customer_id= :customer_id
        """ )
            customer_query=customer_query.params(customer_id=row[1])
            customer_id=conn.execute(customer_query).fetchone()
            products_data = {
                'po_id': row[0],
                'customer_id':row[1],
                'customer_name': row[2],
                'customer_rep_id': row[3],
                'customer_rep_name': row[4],
                'customer_rep_email':row[5],
                'po_type': row[6],
                'customer_email':customer_id[0],
                'customer_company':customer_id[1],
                'orders':row[11],
                'product_count':row[13],
                'order_total':row[12],
                'completion_date': convert_to_timestamp(row[9]),
                'created_by': row[7],
                'date_created': convert_to_timestamp(row[8]),
                'status': row[10],
                'reason': row[14],
                'payment_term': row[15],
                'total_amount_due': round(row[16], 2) if row[16] else 0,
                'credit_limit': row[17]
            }           
            data.append(products_data)

        data = calculatePaginationData(data, page, limit, total_count)
        response['data'] = data
        response['status'] = 200
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response