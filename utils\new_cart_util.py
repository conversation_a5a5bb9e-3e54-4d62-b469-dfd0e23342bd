import new_mongodb
from utils import bc, bc, redis_util, product_util, auth_util, redis_util, store_util
from mongo_db import cart_db, catalog_db
from plugin import bc_cart, bc_products
import time
import logging
import traceback
from operator import itemgetter
from threading import Thread
from graphql import cart_query
from sqlalchemy import text

logger = logging.getLogger()



def _calculate_product_quantity(cart, line_items, product_id):
    cart_line_items = cart['line_items']
    cart_products = cart['products']
    product_id = str(product_id)
    for line_item in line_items:
        sku = line_item['sku']
        cart_sku = cart_line_items.get(sku, None)
        cart_product = cart_products.get(product_id, None)
        total_product_quantity = 0

        if cart_product:
            total_product_quantity = cart_product.get('total_quantity', 0)
        else:
            cart_product = {
                "total_quantity": total_product_quantity, "sku": {}}
            cart_products[product_id] = cart_product

        total_product_quantity = total_product_quantity + line_item['quantity']

        if cart_sku:
            cart_sku["quantity"] = cart_sku["quantity"] + line_item["quantity"]
        else:
            cart_sku = line_item

        cart_product["sku"][sku] = True
        cart_product["total_quantity"] = total_product_quantity
        cart_line_items[sku] = cart_sku
        cart_products[product_id] = cart_product

    return cart


def _parse_options(product_options=[]):
    options = []
    if len(product_options) > 0:
        for option in product_options:
            options.append({
                option["option_display_name"]: option["label"]
            })
    return options


def _get_inventory(store, product, skus):
    result = {}
    invetory = []
    result["name"] = product["name"]
    result["url"] = ""
    result["image_url"] = ""
    if product["custom_url"] and product["custom_url"]["url"]:
        result["url"] = product["custom_url"]["url"]
    if product["images"] and len(product["images"]):
        for image in product["images"]:
            if image["is_thumbnail"]:
                result["image_url"] = image["url_thumbnail"]
                break

    invetory_db = {}
    invetory_db[product['sku']] = {
        "inventory": product['inventory_level'],
        "options": []
    }
    for variant in product['variants']:
        invetory_db[variant['sku']] = {
            "options": _parse_options(variant["option_values"]),
            "inventory": variant['inventory_level']
        }

    for sku in skus:
        _inventory_level = invetory_db.get(sku, {})
        invetory.append(_inventory_level)

    result["invetory"] = invetory
    return result


def _get_sku_inventory(store, data):  
    variants_array = []     
    for item in data['data']['site']['products']['edges']:
        variants_array.append(item['node'])
    result = {}
    for i in variants_array:
        if len(i['variants']) > 0:
            for var in i['variants']['edges']:                
                node = var['node']
                sku = node['entityId']
                inventory = node['inventory']['aggregated']['availableToSell']
                result[sku] = inventory       
    return result                                                        

def _get_product_thumbnail(data):      
    result = {}
    for product in data:
        product_id = int(product['id'])
        if product["images"] and len(product["images"]):
            for image in product["images"]:
                if image["is_thumbnail"]:
                    result[product_id] = image["url_thumbnail"]
                    break  
    return result 

def _get_cart_inventory(store, cart):
    line_items = cart["line_items"] 
    customer_id = cart['customer_id']
    products = []
    p_ids = []
    variants = []
    result = {}
    thumb_image_result = {}
    for _line_item in line_items.values():     
        if not str(_line_item['product_id']) in p_ids:
            p_ids.append(str(_line_item['product_id']))
        if not int(_line_item['product_id']) in products:
            products.append(int(_line_item['product_id']))            
        variants.append(int(_line_item['variant_id']))
        if len(variants) == 250 or len(products) == 50:            
            query = cart_query.get_query(products, variants)                        
            status, res_body = bc.process_bc_graphql_request(store, query, customer_id=customer_id)
            if status == 200:   
                result.update(_get_sku_inventory(store, res_body))
            variants = []
            products = []
        
        if len(p_ids) == 50:            
            products_data = bc_products.fetch_products_by_ids(store, p_ids)
            if products_data:
                thumb_image_result.update(_get_product_thumbnail(products_data))
            p_ids = []

    if len(variants) > 0:
        query = cart_query.get_query(products, variants)                        
        status, res_body = bc.process_bc_graphql_request(store, query, customer_id=customer_id)
        if status == 200:   
            result.update(_get_sku_inventory(store, res_body))

    if len(p_ids) > 0:
        products_data = bc_products.fetch_products_by_ids(store, p_ids)
        if products_data:
            thumb_image_result.update(_get_product_thumbnail(products_data))
                    
    return result, thumb_image_result

def identify_variants_to_remove(hidden_modifier_ids, products):
    lineitem_to_remove = []    
    for product in products:        
        if 'variants' in product:
            for varinat in product['variants']:
                varinat_sku = varinat['sku']
                if 'option_values' in varinat:
                    for option_value in varinat['option_values']:
                        if option_value['id'] in hidden_modifier_ids:
                            lineitem_to_remove.append(varinat_sku)       
    return lineitem_to_remove

def __filter_hidden_modifier_ids(product_complex_rules):
    ids = []
    product_ids = []   
    if len(product_complex_rules):
        for i in product_complex_rules:            
            if i['enabled'] == True and i['purchasing_disabled'] == True and len(i['conditions']):
                if not int(i['product_id']) in product_ids:
                    product_ids.append(int(i['product_id']))
                for j in i['conditions']:
                    ids.append(j['modifier_value_id'])
    return ids, product_ids

def _remove_variant_with_complex_rule(store, line_items):
    line_item_product_ids = []   
    for _line_item in line_items.values():        
        if not int(_line_item['product_id']) in line_item_product_ids:
            line_item_product_ids.append(int(_line_item['product_id']))     

    product_rules = cart_db.fetch_products_complex_rules_by_id(store, line_item_product_ids)
    if len(product_rules):        
        hidden_modifier_ids, filtered_product_ids = __filter_hidden_modifier_ids(product_rules)                
        products = catalog_db.get_products_by_ids(store, filtered_product_ids)                    
        if products:              
            lineitems_to_remove = identify_variants_to_remove(hidden_modifier_ids, products)            
            if len(lineitems_to_remove):
                for v_sku in lineitems_to_remove:
                    if v_sku in line_items:
                        del line_items[v_sku]                                               
    return line_items


def _model_to_dto(store, cart, login_wrapper_enabled=False):
    dto = {}
    dto["id"] = cart["id"]
    dto["line_items"] = []
    line_items = cart.get("line_items", {})      
    line_items = _remove_variant_with_complex_rule(store, line_items)
    cart_db.update_cart(store, cart)
    cart['line_items'] = line_items
    # current_inventory, thumb_images = _get_cart_inventory(store, cart)   
    products = cart['products']
    cart_total = 0

    for sku, line_item in line_items.items():
        variant_id = line_item.get("variant_id", None)
        product_id = line_item.get("product_id", None)
        # line_item["inventory"] = current_inventory.get(variant_id, 0)
        line_item["inventory"] = 0
        # image_url = thumb_images.get(product_id, '')
        # if image_url and image_url != '':
            # line_item['image_url'] = image_url
        extended_sale_price = line_item.get("extended_sale_price", 0)
        product = products.get(str(line_item['product_id']), None)
        if product:
            line_item['brand_name'] = product.get('brand_name', "")
            line_item['order_quantity_minimum'] = product.get(
                'order_quantity_minimum', 0)
            line_item['order_quantity_maximum'] = product.get(
                'order_quantity_maximum', 0)
        if extended_sale_price == 0:
            price = line_item.get("price", 0)
            quantity = line_item.get("quantity", 0)
            extended_sale_price = price * quantity
            line_item['extended_list_price'] = extended_sale_price
            line_item['extended_sale_price'] = extended_sale_price
        cart_total = cart_total + extended_sale_price
        dto["line_items"].append(line_item)

    if len(dto["line_items"]) > 1:
        dto["line_items"] = sorted(
            dto["line_items"], key=itemgetter('product_id', 'variant_id'))
    dto["base_amount"] = cart.get("base_amount", cart_total)
    dto["cart_amount"] = cart.get("cart_amount", cart_total)
    dto["tax_included"] = cart.get("tax_included", False)
    dto["discount_amount"] = cart.get("discount_amount", 0)
    dto["coupons"] = cart.get("coupons", cart.get("coupons", []))
    dto["discounts"] = cart.get("discounts", cart.get("discounts", []))
    dto["currency"] = cart.get("currency", {"code": "USD"})
    # dto["redirect_urls"] = None
    if "redirect_urls" in cart and cart['redirect_urls']:
        if login_wrapper_enabled:
            redirect_urls = cart['redirect_urls']
            bc_api = store_util.get_bc_api_creds(store)
            for url_type, url in redirect_urls.items():
                dto[url_type] = auth_util.build_bc_login_url(
                    bc_api, cart['customer_id'], url)
        else:
            dto["redirect_urls"] = cart['redirect_urls']
    return dto


def _validate_inventory(store, cart, new_line_items, product):
    result = []
    skus = []
    cart_line_items = cart['line_items']

    for line_item in new_line_items:
        skus.append(line_item['sku'])

    product_data = _get_inventory(store, product, skus)
    sku_invetory = product_data["invetory"]

    for idx, line_item in enumerate(new_line_items):
        sku = line_item['sku']
        cart_line_item = cart_line_items[sku]
        cart_sku_quantity = cart_line_item['quantity']
        sku_data = sku_invetory[idx]
        current_inventory = sku_data.get("inventory", 0)
        if cart_sku_quantity > current_inventory:
            result.append({
                "sku": sku,
                "product_id": line_item["product_id"],
                "variant_id": line_item["variant_id"] if 'variant_id' in line_item else line_item["id"],
                "invetory": current_inventory,
                "quantity": cart_sku_quantity
            })
        else:
            cart_line_item["options"] = sku_data["options"]
            cart_line_item["name"] = product_data["name"]
            cart_line_item["url"] = product_data["url"]
            cart_line_item["image_url"] = product_data["image_url"]

    return result


def _update_sku_price(store, cart, product):
    customer_id = cart['customer_id']
    cart_line_items = cart['line_items']
    cart_products = cart['products']

    product_id = str(product['id'])
    product_total_quantity = cart_products[product_id]['total_quantity']
    sku_list = list(cart_products[product_id]['sku'].keys())
    sku_pricing = product_util.get_price_single_product(store, customer_id, product, product_total_quantity, sku_list)
    for sku in sku_list:
        price = sku_pricing[sku]
        cart_line_item = cart_line_items.get(sku, None)
        if cart_line_item:
            cart_line_item['original_price'] = price['original_price']
            cart_line_item['list_price'] = price['list_price']
            cart_line_item['sale_price'] = price['sale_price']
            cart_line_item['extended_list_price'] = round(
                price['sale_price'] * cart_line_item['quantity'], 2)
            cart_line_item['extended_sale_price'] = round(
                price['sale_price'] * cart_line_item['quantity'], 2)

    return cart


def _get_customer_active_cart(store, customer_id):
    cart = cart_db.fetch_customer_active_cart(store, customer_id)
    if not cart:
        cart_id = cart_db.create_new_cart(store, customer_id)
        cart = cart_db.fetch_customer_active_cart(store, customer_id)
    return cart


def _update_cart_database(store, cart):
    cart_amount = 0
    for sku, line_item in cart['line_items'].items():
        cart_amount = cart_amount + line_item['extended_sale_price']

    cart['cart_amount'] = round(cart_amount, 2)
    cart['base_amount'] = cart['cart_amount']
    cart['tax_included'] = False
    cart['currency'] = {
        "code": "USD"
    }
    cart['discount_amount'] = 0
    cart['coupons'] = []
    cart['discounts'] = []
    cart['gift_certificates'] = []
    cart['redirect_urls'] = None

    cart = cart_db.update_cart(store, cart)
    return cart


def _validat_line_item(line_item):
    ret = False
    if line_item:
        product_id = line_item.get('product_id', None)
        sku = line_item.get('sku', None)
        variant_id = line_item.get(
            'variant_id', None)
        quantity = line_item.get('quantity', None)
        if product_id and sku and variant_id and quantity:
            ret = True
    return ret


def _validat_line_items(line_items):
    ret = False
    if line_items and len(line_items) > 0:
        flag = True
        for line_item in line_items:
            if not _validat_line_item(line_item):
                flag = False
                break
        ret = flag
    return ret


def _min_max_quantity_check(store, product, cart):
    error = None
    line_items = cart['line_items']
    product_id = str(product['id'])
    cart_product = cart['products'].get(product_id, None)

    if cart_product and cart_product['sku']:
        min_quantity = product.get("order_quantity_minimum", 0)
        max_quantity = product.get("order_quantity_maximum", 0)

        cart_product['order_quantity_minimum'] = min_quantity
        cart_product['order_quantity_maximum'] = max_quantity
        # cart_product['brand_name'] = product_util.get_brand_name(store, product)

        if min_quantity > 0 and max_quantity > 0:
            for sku, obj in cart_product['sku'].items():
                line_item = line_items.get(sku, None)
                if line_item and (line_item["quantity"] < min_quantity or line_item["quantity"] > max_quantity):
                    error = "SKU " + sku + ", min order quantity is " + str(min_quantity) + \
                            ", max order quantity is " + \
                        str(max_quantity) + ", cart quantity is " + \
                        str(line_item["quantity"])
                    break
        elif min_quantity > 0:
            for sku, obj in cart_product['sku'].items():
                line_item = line_items.get(sku, None)
                if line_item and line_item["quantity"] < min_quantity:
                    error = "SKU " + sku + ", min order quantity is " + str(min_quantity) + \
                        ", cart quantity is " + str(line_item["quantity"])
                    break
        elif max_quantity > 0:
            for sku, obj in cart_product['sku'].items():
                line_item = line_items.get(sku, None)
                if line_item and line_item["quantity"] > max_quantity:
                    error = "SKU " + sku + ", max order quantity is " + str(max_quantity) + \
                            ", cart quantity is " + str(line_item["quantity"])
                    break

    return error


def _process_add_line_items(store, cart, line_items, product, disable_validation):
    result = {}
    product_id = product['id']
    cart = _calculate_product_quantity(cart, line_items, product_id)
    quantity_check_error = _min_max_quantity_check(store, product, cart)
    if quantity_check_error:
        result = {
            "status": 409,
            "error": quantity_check_error
        }
        return result

    validation_result = _validate_inventory(store, cart, line_items, product)
    
    # Bypassing validation check for reorder using key "is_reorder"
    if len(validation_result) > 0 and not disable_validation:
        result["status"] = 409
        result["data"] = {
            "error": "Quantity is more than current inventory.",
            "data": validation_result
        }
    else:
        if len(cart['line_items']) > 250:
            result["status"] = 409
            result["data"] = {
                "error": "You cannot add more than 250 line items in cart."
            }
        else:
            cart = _update_sku_price(store, cart, product)
            bc_cart_id = cart.get("bc_cart_id", None)
            if bc_cart_id:
                del cart['bc_cart_id']
                redis_util.queue_cart_delete(store["id"], bc_cart_id)
            cart['cart_updated'] = True
            cart = _update_cart_database(store, cart)
            cart = _model_to_dto(store, cart)
            result = {
                "status": 200,
                "data": cart
            }
    return result


def add_line_items(store, customer_id, line_items, disable_validation=False):
    result = {
        "status": 409
    }
    if _validat_line_items(line_items):
        lineitems_by_product = {}
        for line_item in line_items:
            product_line_items = lineitems_by_product.get(line_item["product_id"], [])
            product_line_items.append(line_item)
            lineitems_by_product[line_item["product_id"]] = product_line_items

        cart = _get_customer_active_cart(store, customer_id)
        for product_id, product_line_items in lineitems_by_product.items():
            product = product_util.get_product_by_id(store, product_id)
            if product:
                result = _process_add_line_items(store, cart, product_line_items, product, disable_validation)
                return result
            else:
                result = {
                    "status": 409,
                    "error": "Invalid product."
                }
                return result
    else:
        result = {
            "status": 409,
            "error": "Invalid line item."
        }
    return result


def update_cart_line_item(store, customer_id, line_item):
    result = {}
    if _validat_line_item(line_item):
        product_id = line_item["product_id"]
        product = None
        if product_id:
            product = new_mongodb.fetch_one_document_from_storefront_collection(store['id'], "products", {"id": int(product_id)})
        if product:
            cart = cart_db.fetch_customer_active_cart(store, customer_id)
            if cart:
                sku = line_item['sku']
                cart_line_items = cart.get('line_items', {})
                cart_line_item = cart_line_items.get(sku, None)
                if cart_line_item:
                    del cart_line_items[sku]
                    cart['line_items'] = cart_line_items
                    result = _process_add_line_items(
                        store, cart, [line_item], product, disable_validation=False)
                else:
                    result = {
                        "status": 404,
                        "error": "Line item with the given sku doesn't exist in the cart."
                    }
            else:
                result = {
                    "status": 404,
                    "error": "No active cart for the given cutomer id."
                }
        else:
            result = {
                "status": 409,
                "error": "Invalid product."
            }
    else:
        result = {
            "status": 409,
            "error": "Invalid line item."
        }

    return result


def _validat_delete_line_items(line_items):
    ret = False
    if line_items and len(line_items) > 0:
        flag = True
        for line_item in line_items:
            sku = line_item['sku']
            if not sku:
                flag = False
                break
        ret = flag
    return ret


def delete_line_items(store, customer_id, line_items):
    result = {
        "status": 200,
        "data": "Deleted successfully."
    }
    if _validat_delete_line_items(line_items):
        cart = cart_db.fetch_customer_active_cart(store, customer_id)
        if cart:
            cart_line_items = cart.get('line_items', [])
            cart_products = cart.get('products', {})
            deleted_products = {}
            for line_item in line_items:
                sku = line_item['sku']
                cart_line_item = cart_line_items.get(sku, None)
                if cart_line_item:
                    product_id = cart_line_item['product_id']
                    skus = deleted_products.get(str(product_id), [])
                    skus.append(sku)
                    deleted_products[str(product_id)] = skus
                    del cart_line_items[sku]
                    cart_product = cart_products[str(product_id)]
                    cart_product['total_quantity'] = cart_product['total_quantity'] - \
                        cart_line_item['quantity']
                    del cart_product['sku'][sku]
                    if len(cart_product['sku']) == 0:
                        del cart_products[str(product_id)]
                        del deleted_products[str(product_id)]

            if len(deleted_products) > 0:
                for product_id, line_item in deleted_products.items():
                    product = product_util.get_product_by_id(store, int(product_id))
                    cart = _update_sku_price(store, cart, product)

            bc_cart_id = cart.get('bc_cart_id', None)
            if bc_cart_id:
                del cart['bc_cart_id']
                redis_util.queue_cart_delete(store["id"], bc_cart_id)

            cart['cart_updated'] = True
            cart = _update_cart_database(store, cart)
            cart = _model_to_dto(store, cart)
            result = {
                "status": 200,
                "data": cart
            }
        else:
            result = {
                "status": 404,
                "error": "No active cart for the given cutomer id."
            }
    else:
        result = {
            "status": 409,
            "error": "Invalid line item."
        }

    return result

def get_bc_cart(store, bc_cart_id):
    result = None
    res = bc_cart.fetch_cart(store, bc_cart_id)
    if res.status_code < 299:
        result = res.json()['data']
    return result



















