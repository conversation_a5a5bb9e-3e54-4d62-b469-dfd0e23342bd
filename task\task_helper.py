import time
import datetime
from utils import bc, store_util
import logging
import traceback
from mongo_db import task_db
from new_utils import cache_util

logger = logging.getLogger()

HOUR_IN_SECOND = 3600
DAY_IN_SECOND = 24 * HOUR_IN_SECOND
MONTH_IN_SECOND = 30 * DAY_IN_SECOND

def purge_task_log(time_minutes=1440):
    task_db.delete_older_task(time_minutes)

def check_and_update_graphql_token(store):
    result = False
    store_id = store['id']
    token = cache_util.get_graphql_token(store_id)
    current_time = int(time.time()) + (HOUR_IN_SECOND)
    if not token or token['expires_at'] <= current_time:
        expires_at = int(time.time()) + MONTH_IN_SECOND
        bc_api = store_util.get_bc_api_creds(store)
        token = bc.get_bc_graphql_token(bc_api, expires_at)
        if token:
            cache_util.set_graphql_token(store_id, token, expires_at)
            result = True
    else:
        result = True
    
    return result

def update_catalog_data(store, adhoc):
    import task
    task.update_product_card_cache.apply_async(args=[store, adhoc])
    task.update_brands.apply_async(args=[store, adhoc])
    task.update_categories.apply_async(args=[store, adhoc])
    task.update_products.apply_async(args=[store, adhoc])
    task.update_price_list_assignment.apply_async(args=[store, adhoc])

def update_customer_data(store, adhoc):
    import task
    task.update_customers.apply_async(args=[store, adhoc])
    task.update_customer_groups.apply_async(args=[store, adhoc])

def build_store(store, adhoc):
    result = check_and_update_graphql_token(store)
    update_catalog_data(store, adhoc)
    update_customer_data(store, adhoc)
    return result, {"message": "Completed successfully"}
    # if result:
    #     import task
    #     task.update_product_card_cache.apply_async(args=[store, adhoc])
    #     task.update_brands.apply_async(args=[store, adhoc])
    #     task.update_categories.apply_async(args=[store, adhoc])
    #     task.update_products.apply_async(args=[store, adhoc])
    #     return result, {"message": "Completed successfully"}
    # else:
    #     return result, {"message": "API doesn't have enough permission"}
    

def store_task_executor(task_context, store_id, adhoc, task_function, *args):
    logger.info("Begin " + task_context.task)
    start_time = int(datetime.datetime.utcnow().timestamp())
    task_db.create_task(task_context.id, task_context.parent_id, task_context.root_id, task_context.task, store_id)
    outcome = False
    message = ""
    try:
        outcome, message = task_function(*args)
    except Exception as ex:
        outcome = False
        message = "Exception: " + str(traceback.format_exc())
    end_time = int(datetime.datetime.utcnow().timestamp())
    task_db.end_task(task_context.id, (end_time - start_time), outcome, message)
    logger.info("End " + task_context.task)

def bc_webhook_executor(store):
    bc_webhook_url = "v3/hooks"
    webhook_list = ["category", "product", "order"]
    
    for i in webhook_list:
        # Create webhooks whos register in webhooklist...
        req_data = {
            "scope": "store/" + i + "/*",
            "destination": "https://api.atlantixdigital.com/bc/webhook",
            "is_active": True
        }
        bc_api = store_util.get_bc_api_creds(store)
        bc.create_bc_webhook(bc_api, bc_webhook_url, req_data)
    