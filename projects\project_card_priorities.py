from sqlalchemy import text
import pg_db
from mongo_db import user_db
from datetime import datetime
from utils import common
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import logging
import traceback
from utils.common import convert_to_timestamp

logger = logging.getLogger()


def _fetch_card_priorities(conn, id=None):
    data = []  
    if id:
        query = text(
            f"""SELECT * 
                FROM agile_card_priorities
                WHERE id = :id;
            """
        )
        result = conn.execute(query.bindparams(id=id))
    else:
        query = text(
            f"""SELECT * 
                FROM agile_card_priorities;
            """
        )
        result = conn.execute(query)
    for row in result.fetchall():
        card_status_data = {
            'id': row[0],
            'label': row[1],
            'description': row[2],
            'level': row[3],
            'color_code': row[4],
            'is_default': row[5],
            'created_by': row[6],
            'updated_by': row[7],
            'created_at': convert_to_timestamp(row[8]),
            'updated_at': convert_to_timestamp(row[9])
        }
        data.append(card_status_data)
    return data


def get_project_card_priorities():
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        data = _fetch_card_priorities(conn)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response


def get_project_card_priorities_detail(id):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        data = _fetch_card_priorities(conn, id)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response