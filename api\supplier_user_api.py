from threading import current_thread, get_ident, get_native_id
from analytics import sold_product_analytics
from flask import request
import logging
import traceback
from api import APIResource
from iam import supplier_user_service, user_service
from exceptions.common_exceptions import ResourceAlreadyExistException, InvalidInputException, ResourceNotFoundException
import datetime
from analytics.supplier_app import supplier_analytics_dashboard, supplier_sold_product_analytics
from threading import current_thread, get_ident, get_native_id

logger = logging.getLogger()

class SupplierUsers(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering SupplierUsers GET")
        try:
            query_params = request.args.to_dict()
            users = user_service.get_all_users(store, query_params)
            return users, 200
        finally:
            logger.debug("Exiting SupplierUsers GET")

    def post_executor(self, request, token_payload, store):
        logger.debug("Entering SupplierUsers POST")
        try:
            user = request.get_json(force=True)
            email = ''
            if token_payload and 'username' in token_payload:
                email = token_payload['username']
            result = user_service.create_user(store, user, email)
            return result, 200
        except ResourceAlreadyExistException as e:
            return {"message": "username: User with the given username is already exist."}, 409
        except InvalidInputException as e:
            return {"message": "Provide valid input."}, 400
        except ResourceNotFoundException as e:
            return {"message": "role_id: User role doesn't exist."}, 404
        finally:
            logger.debug("Exiting SupplierUsers POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class SupplierUser(APIResource):

    def get_executor(self, request, token_payload, store, user_id):
        logger.debug("Entering SupplierUser GET")
        try:
            user = user_service.get_user_by_id(store, user_id)
            return {"data":user}, 200
        except ResourceAlreadyExistException as e:
            return {"message": e.message}, 409
        except InvalidInputException as e:
            return {"message": e.message}, 400
        except ResourceNotFoundException as e:
            return {"message": e.message}, 404
        finally:
            logger.debug("Exiting SupplierUser GET")

    def put_executor(self, request, token_payload, store, user_id):
        logger.debug("Entering SupplierUser PUT")
        try:
            user = request.get_json(force=True)
            result = user_service.update_user(store, user_id, user)
            return result, 200
        except ResourceAlreadyExistException as e:
            return {"message": e.message}, 409
        except InvalidInputException as e:
            return {"message": e.message}, 400
        except ResourceNotFoundException as e:
            return {"message": e.message}, 404
        finally:
            logger.debug("Exiting SupplierUser PUT")

    def delete_executor(self, request, token_payload, store, user_id):
        logger.debug("Entering SupplierUser DELETE")
        try:
            result = user_service.delete_user(store, user_id)
            return result, 200
        except InvalidInputException as e:
            return {"message": e.message }, 400
        except ResourceNotFoundException as e:
            return {"message": e.message }, 404
        finally:
            logger.debug("Exiting AdminUsers POST")

    def get(self, user_id):
        return self.execute_store_request(request, self.get_executor, user_id)

    def delete(self, user_id):
        return self.execute_store_request(request, self.delete_executor, user_id)

    def put(self, user_id):
        return self.execute_store_request(request, self.put_executor, user_id)


class ChangeSupplierUserPassword(APIResource):

    def post_executor(self, request, token_payload, store):
        logger.debug("Entering ChangeUserPassword POST")
        try:
            payload = request.get_json(force=True, silent=True)
            if payload:
                username = payload.get("username", None)
                password = payload.get("password", None)
                if username and password:
                    user = user_service.change_password(username, password)
                    if user:
                        return {"message": "Password has been changed successfully."}, 200
                    else:
                        return {"message": "User doesn't exist"}, 404
            return {"message": "Provide valid username and password"}, 400
        finally:
            logger.debug("Exiting ChangeUserPassword POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)

class DropdownUsersList(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering SupplierUsers GET")
        try:
            users = supplier_user_service.get_all_users(store)
            if users['status'] == 200:
                return {"data": users['data']}, 200
            else:
                return {"data": users['data']}, 200
        finally:
            logger.debug("Exiting SupplierUsers GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class AllBrandsDropdown(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Suppliers GET")
        try:
            query_params = request.args.to_dict()
            user = query_params.get('user', '')
            res = supplier_user_service.get_brands_dropdown(store, user)
            
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Suppliers GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)

class UserSuppliersMapping(APIResource):
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Supplier App User Supplier Mapping POST")
        try:
            payload = request.get_json(force=True)
            if payload:
                result = supplier_user_service.save_user_supplier_mapping_data(store, payload, False)

                if result['status'] == 200:
                    return {"message": "Data Saved successfully."}, 200
                else:
                    return {'message': result['message']}, result['status']
            else:
                return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Supplier App User Supplier Mapping POST")


    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Supplier App User Supplier Mapping GET")
        try:
            query_params = request.args.to_dict()
            page = query_params.get('page', 1)
            limit = query_params.get('limit', 30)
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            search = query_params.get('search', '')
            res = supplier_user_service.get_user_supplier_mapping_data(store, page, limit, sort_array, search)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Supplier App User Supplier Mapping GET")


    def post(self):
        return self.execute_store_request(request, self.post_executor)

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class UserSupplierMappingGet(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering User Supplier Mapping GET")
        try:
            query_params = request.args.to_dict()
            email_id = query_params.get('email_id', None)
            if email_id:
                res = supplier_user_service.get_user_mapped_suppliers(store, email_id)

                if res['status'] == 200:
                    return res['data'], 200
                else:
                    return {"message": res['message']}, res['status']
            else:
                return {"message": "Email ID is required"}, 400
        finally:
            logger.debug("Exiting User Supplier Mapping GET")

    def put_executor(self, request, token_payload, store):
        logger.debug("Entering User Supplier Mapping PUT")
        try:
            payload = request.get_json(force=True)
            if payload:
                result = supplier_user_service.save_user_supplier_mapping_data(store, payload, True)

                if result['status'] == 200:
                    return {"message": "Data Saved successfully."}, 200
                else:
                    return {'message': result['message']}, result['status']
            else:
                return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting User Supplier Mapping PUT")

    def delete_executor(self, request, token_payload, store):
        logger.debug("Entering Supplier App User Supplier Mapping Delete Record")
        try:
            payload = request.get_json(force=True)
            if payload:
                supplier_user_service.delete_user_record(store, payload)
                return {"message": "Record deleted successfully"}, 200
            return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Supplier App User Supplier Mapping Delete Record")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def put(self):
        return self.execute_store_request(request, self.put_executor)
    
    def delete(self):
        return self.execute_store_request(request, self.delete_executor)

class SuppliersDashboardSummary(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering summary GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            brands = query_params.get('brands', '')
            user = query_params.get('user', '')
            res = supplier_analytics_dashboard.get_supplier_summary(store, start_date, end_date, brands, user)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting summary GET {end_time-start_time}")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class SuppliersDashboardTopProducts(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Top products GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            brands = query_params.get('brands', '')
            user = query_params.get('user', '')
            res = supplier_analytics_dashboard.get_supplier_top_products(store, start_date, end_date, brands, user)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting Top products GET {end_time-start_time}")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class SuppliersDashboardTopStates(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Top states GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            brands = query_params.get('brands', '')
            user = query_params.get('user', '')
            res = supplier_analytics_dashboard.get_supplier_top_states(store, start_date, end_date, brands, user)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting Top states GET {end_time-start_time}")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class SupplierAggregatedProductReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering AggregatedProductReport GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            brands = query_params.get('brands', '')
            user = query_params.get('user', '')

            if start_date and end_date:
                res = sold_product_analytics.get_supplier_aggregated_product_report(store, start_date, end_date, page, limit, sort_array, brands, user)
                return res, 200
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting AggregatedProductReport GET {end_time-start_time}")


    def get(self):
        return self.execute_store_request(request, self.get_executor)

class SoldProductSummarySuppliers(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering SoldProductSummary GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            query_params = request.args.to_dict()

            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)

            if product_id and start_date and end_date:
                res = supplier_sold_product_analytics.get_sold_product_analytics_summary_suppliers(store, product_id, start_date, end_date, username)
                if res['status'] == 200:
                    return {'data': res['data']}, 200
                else:
                    return {'message': res['message']}, res['status']
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting SoldProductSummary GET {end_time-start_time}")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class VariantSalesByStatesSuppliers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering VariantSalesByStates GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            
            query_params = request.args.to_dict()
            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort_by = query_params.get('sort_by', "").strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            thread = current_thread()
            logging.info(f'Main thread: name={thread.name}, idnet={get_ident()}, id={get_native_id()}')
            res = supplier_sold_product_analytics.get_variant_sales_by_states_suppliers(store=store, product_id=product_id, start_date=start_date,
                                                                                 end_date=end_date, username=username, sort_array=sort_array)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting VariantSalesByStates GET {end_time-start_time}")


    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ProductSalesSuppliers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering ProductSales GET")
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            
            query_params = request.args.to_dict()
            res = supplier_sold_product_analytics.get_product_sale_supplier(store, query_params['product_id'],
                    query_params['start_date'], query_params['end_date'],
                    username, query_params['period'])
            if res['status'] == 200:
                return {'data': res['data']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting ProductSales GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class SoldProductPriceReportSuppliers(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering SoldProductPriceReport GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)

            if product_id and start_date and end_date:
                res = supplier_sold_product_analytics.get_product_price_report_suppliers(store, product_id, start_date, end_date)
                res = {
                    'data': res
                }
                return res, 200
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting SoldProductPriceReport GET {end_time-start_time}")


    def get(self):
        return self.execute_store_request(request, self.get_executor)

class VariantSalesByCustomersSuppliers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering VariantSalesByCustomers GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort = query_params.get('sort', "desc")
            state = query_params.get('state', None)
            thread = current_thread()
            logging.info(f'Main thread: name={thread.name}, idnet={get_ident()}, id={get_native_id()}')
            res = supplier_sold_product_analytics.get_variant_sales_by_customers_suppliers(store=store, product_id=product_id, start_date=start_date,
                                                                                 end_date=end_date, sort=sort, state=state)
            return res, 200
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting VariantSalesByCustomers GET {end_time-start_time}")


    def get(self):
        return self.execute_store_request(request, self.get_executor)

class SoldProductSkuReportSuppliers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering SoldProductSkuReport GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
        
            query_params = request.args.to_dict()
            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort_by = query_params.get('sort_by', "")
            sort_array = sort_by.split("/") if sort_by != '' else []
            state = query_params.get('state', None)
            thread = current_thread()
            logging.info(f'Main thread: name={thread.name}, idnet={get_ident()}, id={get_native_id()}')
            res = supplier_sold_product_analytics.get_sku_report_supplier(store=store, product_id=product_id, start_date=start_date,
                                                        end_date=end_date, username=username, sort_array=sort_array,state=state)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting SoldProductSkuReport GET {end_time-start_time}")


    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ProductPerformanceTableSuppliers(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering ProductPerformanceTable GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            supplier = query_params.get('supplier', '')
            user = query_params.get('user', '')
            res = supplier_sold_product_analytics.get_product_performance_data_suppliers(store, product_id, start_date, end_date, supplier, user)
            res = {
                'data': res
            }
            return res, 200
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting ProductPerformanceTable GET {end_time-start_time}")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class AllStatesForFilterSuppliers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering AllStatesForFilter GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            
            query_params = request.args.to_dict()
            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            res = supplier_sold_product_analytics.get_product_sold_states_suppliers(store, product_id, start_date, end_date, username)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting AllStatesForFilter GET {end_time-start_time}")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class SupplierProducts(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Supplier Products GET")
        try:
            query_params = request.args.to_dict()
            res = supplier_sold_product_analytics.get_supplier_products(store, query_params)
            return res['data'], 200
        finally:
            logger.debug("Exiting Supplier Products GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class AllBcBrandList(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Brands GET")
        try:
            res = supplier_user_service.get_brand_list(store)
            
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Brands GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ProductsList(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Suppliers GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            search = query_params.get('search', '').strip()
            brand_ids = query_params.get("brand_ids")
            email_id = query_params.get("email_id")
            res = supplier_user_service.get_product_list(store, brand_ids, search, page, limit, email_id)
            
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Suppliers GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ProductsDropDown(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Suppliers GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            search = query_params.get('search', '').strip()
            res = supplier_user_service.get_product_dropdown(store, search, page, limit)
            
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Suppliers GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ProductsMapping(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Suppliers GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            search = query_params.get('search', '').strip()
            user_email = query_params.get('user_email', '').strip()
            brand = query_params.get('brand', '').strip()
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            res = supplier_user_service.get_product_mapping(store, search, user_email, brand, page, limit, sort_array)
            
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Suppliers GET")

    def delete_executor(self, request, token_payload, store):
        logger.debug("Entering Supplier App products mapping delete record")
        try:
            payload = request.get_json(force=True)
            if payload:
                res = supplier_user_service.delete_product_from_mapping(store['id'], payload)
                if res['status'] == 200:
                    return {"message": res['message']}, 200
                else:
                    return {"message": res['message']}, res['status']
            return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Supplier App products mapping delete record")

    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Supplier App products mapping post record")
        try:
            payload = request.get_json(force=True)
            if payload:
                res = supplier_user_service.add_products_to_mapping(store['id'], payload)
                if res['status'] == 200:
                    return {"message": res['message']}, 200
                else:
                    return {"message": res['message']}, res['status']
            return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Supplier App products mapping post record")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
    def delete(self):
        return self.execute_store_request(request, self.delete_executor)
    
    def post(self):
        return self.execute_store_request(request, self.post_executor)