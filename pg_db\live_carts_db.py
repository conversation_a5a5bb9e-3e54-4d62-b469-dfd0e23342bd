from sqlalchemy.dialects.postgresql import insert
import pg_db as db
from sqlalchemy import Boolean, Column, DateTime, String, Integer, ForeignKey, Float
from sqlalchemy.sql import func


class LiveCarts(db.Base):
    __tablename__ = "live_carts"

    id = Column(Integer, primary_key=True, autoincrement=True)
    cart_id = Column(String(100))
    customer_id = Column(Integer)
    customer_rep_id = Column(Integer)
    customer_name = Column(String(100), nullable=False)
    customer_rep = Column(String(100))
    channel = Column(String(100))
    line_items = Column(Integer)
    cart_value = Column(Float)
    is_live = Column(Boolean, nullable=False, default=False)
    updated_at = Column(DateTime, onupdate=func.now())
    last_login_at = Column(DateTime)
    last_logout_at = Column(DateTime)
    email = Column(String(100), nullable=False)