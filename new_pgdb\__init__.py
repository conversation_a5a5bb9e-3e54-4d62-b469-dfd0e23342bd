from sqlalchemy import create_engine, inspect, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import datetime
import threading 
import logging
import traceback
import atexit
from config import appconfig

logger = logging.getLogger()

Base = declarative_base()

class DBTables:
    analytics_products_trend_table = "analytics_products_trend"
    analytics_variants_trend_table = "analytics_variants_trend"
    order_shipping_addresses_table = "order_shipping_addresses"
    customers_table = "customers"
    order_consignment_table = "order_consignment"
    po_reorders_table = "po_reorders"
    user_supplier_mapping_table = 'user_supplier_mapping'
    product_tags_table = "product_tags"
    tags_table = "tags"
    salesforce_customer_rep = "salesforce_customer_rep"
    products = "products"
    blocked_orders = "blocked_orders"
    blocked_orders_logs = "blocked_order_logs"
    skuvault_catalog = "skuvault_catalog"
    brand_purchaser_mapping = "brand_purchaser_mapping"
    supplier_app_user_supplier_mapping = "supplier_app_user_supplier_mapping"
    variants = "variants"

class PGDBConnectionPool:
    _instance = None    

    def __new__(cls):
        if cls._instance is None:
            logger.info('Creating the PG DB connection pool')
            cls._instance = super(PGDBConnectionPool, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        logger.info("Entering PGDBConnectionPool")
        self.conn_pool = {}
        self.lock = threading.Lock()
        logger.info("Exiting PGDBConnectionPool")

    def get_analytics_db_info(self, store_id):
        analytics_db_info = None
        import new_mongodb
        store = new_mongodb.get_store_by_id(store_id)
        if store:
            analytics_db_info = appconfig.get_analytics_db_config_from_store(store)
        return analytics_db_info
    
    def get_db_engine(self, store_id, read_only):
        db_engine = self.conn_pool.get(store_id, None)
        if db_engine is None:
            self.lock.acquire()
            try:
                if read_only:
                    db_engine = self.conn_pool.get(store_id + "_ro", None)
                else:
                    db_engine = self.conn_pool.get(store_id, None)
                if db_engine is None:
                    cur_time = str(datetime.datetime.now())
                    analytics_db_info = self.get_analytics_db_info(store_id)
                    db_user = analytics_db_info[appconfig.ADConfigKey.DB_APP_USER_KEY][appconfig.ADConfigKey.DB_TASWORKER_APP_USER_KEY]
                    username = db_user[appconfig.ADConfigKey.DB_USERNAME_KEY]
                    pswd = db_user[appconfig.ADConfigKey.DB_PSWD_KEY] 
                    host = analytics_db_info[appconfig.ADConfigKey.DB_HOST_NAME_KEY]
                    port = analytics_db_info[appconfig.ADConfigKey.DB_PORT_KEY]
                    db_name = analytics_db_info[appconfig.ADConfigKey.DB_NAME_KEY]
                    if read_only:
                        host = host + "_replica"
                    if store_id == "63da3e98b702e324567f76f9":
                        conn_str = f"******************************************************************/stage"
                    else:
                        conn_str = f"******************************************************************/cbdtostore"
                    db_engine = create_engine(conn_str, connect_args={"application_name":cur_time}, 
                                    pool_size=5, max_overflow=10, pool_pre_ping=True,echo=False)
                    if db_engine is not None:
                        if read_only:
                            self.conn_pool[store_id + "_ro"] = db_engine
                        else:
                            from new_pgdb import order_consignment_db, blocked_orders_db, classified_as_db
                            Base.metadata.create_all(db_engine)
            finally:
                self.lock.release()
        return db_engine

    def get_connection(self, store_id, read_only):
        return self.get_db_engine(store_id, read_only).connect()
    
    def get_session(self, store_id, read_only):
        db_engine = self.get_db_engine(store_id, read_only)
        session = sessionmaker(bind=db_engine)
        return session()
    
    def dispose_engines(self):
        for store_id, engine in self.conn_pool.items():
            engine.dispose()

conn_pool = PGDBConnectionPool()

@atexit.register
def shutdown_db_engine():
    logger.error("new_pgdb: shutdown_db_engine: Exiting from the application. Closing DB connection pool")
    conn_pool.dispose_engines()

def get_connection(store_id, read_only=False):
    return conn_pool.get_connection(store_id=store_id, read_only=read_only)

def get_session(store_id, read_only=False):
    return conn_pool.get_session(store_id=store_id, read_only=read_only)

def object_as_dict(obj):
    return {c.key: getattr(obj, c.key)
            for c in inspect(obj).mapper.column_attrs}

def fetch_all(session, model):
    query = session.query(model)
    result = []
    for user in query:
        result.append(object_as_dict(user))
    return result

def execute_stmt(store, statment, values=None, session=None):
    local_session = None
    if not session:
        session = get_session(store['id'])
        local_session = session
    try:
        if values:
            session.execute(statment, values)
        else:
            session.execute(statment)
    finally:
        if local_session:
            local_session.commit()
            local_session.close()

def run_query(store_id, query):
    result = []
    conn = get_connection(store_id)
    try:
        rs = conn.execute(text(query))
        for row in rs:
            result.append(list(row))
    finally:
        conn.close()
    return result

def truncate_table(store_id, table_name):
    conn = get_connection(store_id)
    result = False
    try:
        query = f"TRUNCATE {table_name} CASCADE"
        conn.execute(text(query))
        conn.commit()
        result = True
    finally:
        conn.close()    
    return result