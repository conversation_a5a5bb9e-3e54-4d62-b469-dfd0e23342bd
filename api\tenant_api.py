from flask import request, make_response, json
from flask_restful import Api, Resource
import logging
import traceback
from api import APIResource
import task
from utils import store_util
from settings import mail_service

logger = logging.getLogger()


class Tenants(APIResource):
    def post_executor(self, request, token_payload):
        logger.debug("Entering Tenants POST")
        try:
            tenant = request.get_json(force=True)
            tenant_id = self.service.get_tenant_service().create(tenant)
            return {"tenant_id": str(tenant_id)}, 200
        finally:
            logger.debug("Exiting Tenants POST")

    def get_executor(self, request, token_payload):
        logger.debug("Entering Tenants GET")
        try:
            tenants = self.service.get_tenant_service().get_all_tenants()
            return {"status": "OK", "data": tenants}, 200
        finally:
            logger.debug("Exiting Tenants GET")

    def post(self):
        return self.execute_request(request, self.post_executor)

    def get(self):
        return self.execute_request(request, self.get_executor)


class Tenant(APIResource):

    def put_executor(self, request, token_payload, tenant_id):
        logger.debug("Entering Tenant PUT")
        try:
            tenant = request.get_json(force=True)
            # TODO: Add logic to update tenant
            # tenant_id = self.service.get_tenant_service().create(tenant)
            return {"tenant_id": str(tenant_id)}, 200
        finally:
            logger.debug("Exiting Tenant PUT")

    def get_executor(self, request, token_payload, tenant_id):
        logger.debug("Entering Tenant GET")
        try:
            tenant = self.service.get_tenant_service().find_by_id(tenant_id)
            return {"status": "OK", "data": tenant}, 200
        finally:
            logger.debug("Exiting Tenant GET")

    def delete_executor(self, request, token_payload, tenant_id):
        logger.debug("Entering Tenant DELETE")
        try:
            result = self.service.get_tenant_service().delete_by_id(tenant_id)
            if result:
                self.service.get_store_service().delete_tanant_store(tenant_id)
                return {"status": "OK"}, 200
            else:
                return {"status": "Resource not found"}, 404
        finally:
            logger.debug("Exiting Tenant DELETE")

    def put(self, tenant_id):
        return self.execute_request(request, self.post_executor, tenant_id)

    def get(self, tenant_id):
        return self.execute_request(request, self.get_executor, tenant_id)

    def delete(self, tenant_id):
        return self.execute_request(request, self.delete_executor, tenant_id)


class Stores(APIResource):
    def post_executor(self, request, token_payload, tenant_id):
        logger.debug("Entering Stores POST")
        try:
            store = request.get_json(force=True)
            store_id = self.service.get_store_service().create(tenant_id, store)
            return {"status": "OK", "store_id": str(store_id)}, 200
        finally:
            logger.debug("Exiting Stores POST")

    def get_executor(self, request, token_payload, tenant_id):
        logger.debug("Entering Stores GET")
        try:
            stores = self.service.get_store_service().get_tanant_stores(tenant_id)
            return {"status": "OK", "data": stores}, 200
        finally:
            logger.debug("Exiting Stores GET")

    def post(self, tenant_id):
        return self.execute_request(request, self.post_executor, tenant_id)

    def get(self, tenant_id):
        return self.execute_request(request, self.get_executor, tenant_id)


class Store(APIResource):

    def post_executor(self, request, token_payload, tenant_id, store_id):
        logger.debug("Entering Store PUT")
        try:
            store = request.get_json(force=True)
            store_id = self.service.get_store_service().create(tenant_id, store)
            return {"status": "OK", "store_id": str(store_id)}, 200
        finally:
            logger.debug("Exiting Store PUT")

    def get_executor(self, request, token_payload, tenant_id, store_id):
        logger.debug("Entering Store GET")
        try:
            store = self.service.get_store_service().find_by_id(store_id)
            return {"status": "OK", "data": store}, 200
        finally:
            logger.debug("Exiting Store GET")

    def delete_executor(self, request, token_payload, tenant_id, store_id):
        logger.debug("Entering Store DELETE")
        try:
            result = self.service.get_store_service().delete_by_id(store_id)
            if result:
                return {"status": "OK"}, 200
            else:
                return {"status": "Resource not found"}, 404
        finally:
            logger.debug("Exiting Store DELETE")

    def put_executor(self, request, token_payload, tenant_id, store_id):
        logger.debug("Entering put_executor")
        try:
            data = request.get_json(force=True)
            updated = self.service.get_store_service().update_store(tenant_id, store_id, data)
            if updated:
                return {"status": "OK"}, 200
            else:
                return {"data": "Store doesn't exist."}, 404
        finally:
            logger.debug("Exiting put_executor")

    def post(self, tenant_id, store_id):
        return self.execute_request(request, self.post_executor, tenant_id, store_id)

    def put(self, tenant_id, store_id):
        return self.execute_request(request, self.put_executor, tenant_id, store_id)

    def get(self, tenant_id, store_id):
        return self.execute_request(request, self.get_executor, tenant_id, store_id)

    def delete(self, tenant_id, store_id):
        return self.execute_request(request, self.delete_executor, tenant_id, store_id)


class SyncStore(APIResource):
    def post_executor(self, request, token_payload):
        logger.debug("Entering Sync Store POST")
        try:
            store = request.get_json(force=True)
            store_id = self.service.get_store_sync_service().create(store)
            return {"status": "ok", "store_id": str(store_id)}, 200
        finally:
            logger.debug("Exiting Sync Store POST")

    def post(self):
        return self.execute_request(request, self.post_executor)


class BuildStore(APIResource):
    def post_executor(self, request, token_payload, tenant_id, store_id):
        logger.debug("Entering BuildStore POST")
        try:
            store = self.service.get_store_by_id(tenant_id, store_id)
            if store:
                payload = request.get_json(force=True)
                if "type" in payload:
                    task_id = None
                    if payload["type"] == "build":
                        task_id = task.send_task(
                            task.BUILD_STORE_TASK, args=(store, True))
                    elif payload["type"] == "update_cache":
                        task_id = task.send_task(
                            task.UPDATE_PRODUCT_LISTING_CACHE_TASK, args=(store, True))
                    elif payload["type"] == "update_price_list":
                        task_id = task.send_task(
                            task.UPDATE_PRICE_LIST_TASK, args=(store, True))
                    elif payload["type"] == "flush_cache":
                        task_id = task.send_task(
                            task.FLUSH_CACHE_TASK, args=(store, True))
                    elif payload["type"] == "inventory_cache":
                        task_id = task.send_task(
                            task.UPDATE_INVENTORY_CACHE_TASK, args=(store, True))
                    if task_id:
                        return {"status": "ok", "task_id": str(task_id)}, 200
                return {"message": "Invalid operation"}, 400
            else:
                return {"message": "Not found"}, 404
        finally:
            logger.debug("Exiting BuildStore POST")

    def post(self, tenant_id, store_id):
        return self.execute_request(request, self.post_executor, tenant_id, store_id)


class EnvCreds(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Store GET")
        try:
            bc_api = store_util.get_bc_api_creds(store)
            env_variables = {
                "store_name": store['name'],
                "store_id": store['id'],
                "navigation_ids": store['navigation_ids'],
                "env_variables": {
                    "GATSBY_API_STORE_HASH": bc_api['store_hash'],
                    "GATSBY_API_CLIENT_ID": bc_api['client_id'],
                    "GATSBY_API_TOKEN": bc_api['access_token'],
                    "GATSBY_API_SECRET": bc_api['secret'],
                    "GATSBY_BC_STORE_URL": bc_api['store_url'],
                    "GATSBY_API_BASEURL": store['backend_url'] + '/storefront/api',
                    "GATSBY_HTTP_ORIGIN": store['store_url'],
                    "GATSBY_API_TENANT_ID": store['tenant_id'],
                    "GATSBY_API_STORE_ID": store['id'],
                    "GATSBY_ADMIN_API_URL": store['backend_url'] + '/admin/api',
                    "GATSBY_ADMIN_ORIGIN": "http://localhost"
                }
            }
            return {"data": env_variables}, 200
        finally:
            logger.debug("Exiting Store GET")

    def get(self, tenant_id, store_id):
        return self.execute_store_request(request, self.get_executor)


class NavigationIds(APIResource):
    def put_executor(self, request, token_payload, tenant_id, store_id):
        try:
            req_body = request.get_json(force=True)
            updated = self.service.get_store_service().update_store_navigationIds(store_id, req_body)
            if updated:
                return {"status": "OK"}, 200
            else:
                return {"data": "Store doesn't exist."}, 404
        finally:
            logger.debug("Exiting Sub Navigation PUT")

    def put(self, tenant_id, store_id):
        return self.execute_request(request, self.put_executor, tenant_id, store_id)


class Templates(APIResource):   
    def get_executor(self, request, token_payload, store, tenant_id, store_id):
        try:                       
            query_params = request.args.to_dict()
            res = mail_service.get_all_templates(store_id, query_params)
            if res['status'] == 200:
                return {"data": res['data']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Email Template GET") 
   
    def get(self, tenant_id, store_id):
        return self.execute_store_request(request, self.get_executor, tenant_id, store_id)


class TemplateOperations(APIResource):
    def put_executor(self, request, token_payload, store, tenant_id, store_id):
        try:
            req_body = request.get_json(force=True)
            res = mail_service.update_mail_template(store_id, req_body)
            if res['status'] == 200:
                return {"message": res['message']}, res['status']
            else:
                return {"message": res['message']}, res['status']
        finally:
            logger.debug("Exiting Mail Templates PUT")

    def put(self, tenant_id, store_id):
        return self.execute_store_request(request, self.put_executor, tenant_id, store_id)


class TemplateDelete(APIResource):
    def delete_executor(self, request, token_payload, tenant_id, store_id, template_code):
        logger.debug("Entering Mail Template DELETE")
        try:
            res = mail_service.delete_mail_template(store_id, template_code)
            if res['status'] == 200:
                return res['message'], res['status']
            else:
                return res['message'], res['status']
        finally:
            logger.debug("Exiting Mail Template DELETE")

    def delete(self, tenant_id, store_id, template_code):
        return self.execute_request(request, self.delete_executor, tenant_id, store_id, template_code)
