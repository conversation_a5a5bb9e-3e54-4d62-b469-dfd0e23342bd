from sqlalchemy.dialects.postgresql import insert
import pg_db as db
from sqlalchemy import UniqueConstraint
from sqlalchemy import Boolean, Column, DateTime, Numeric, String, Integer, ForeignKey, Enum, Interval
from sqlalchemy.sql import func

class BusinessUnits(db.Base):
    __tablename__ = "business_units"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), unique=True, nullable=False)
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

class ProjectBoards(db.Base):
    __tablename__ = "agile_project_boards"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), unique=True, nullable=False)
    description = Column(String)
    is_archived = Column(Boolean, default=False)     
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())


class Projects(db.Base):
    __tablename__ = "agile_projects"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), unique=True, nullable=False)
    bu_id = Column(Integer, ForeignKey(BusinessUnits.__tablename__ + '.id'), nullable=False)
    description = Column(String, default='')
    owner_username = Column(String, nullable=False)
    due_date = Column(DateTime)
    is_archived = Column(Boolean, default=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
    project_initials = Column(String, default='')
    project_type = Column(String, default='standerd') 
    status_module_table_id = Column(Integer)
    status_default_module_id = Column(Integer)
    status_default_column_id = Column(Integer)

class ProjectColumns(db.Base):
    __tablename__ = "agile_project_columns"

    id = Column(Integer, primary_key=True, autoincrement=True)
    project_id = Column(Integer, ForeignKey(Projects.__tablename__ + '.id'), nullable=True)
    name = Column(String(100), unique=True, nullable=False)
    sort_id = Column(Integer, nullable=False)
    is_first_column = Column(Boolean, nullable=False, default=False)
    is_last_column = Column(Boolean, nullable=False, default=False)
    description = Column(String)
    is_archived = Column(Boolean, default=False)     
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
    is_visible = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)
    is_resolved = Column(Boolean, default=False)

    __table_args__ = (
        UniqueConstraint('project_id', 'name', name='unique_project_column'),
    )


class ProjectModules(db.Base):
    __tablename__ = "agile_project_modules"

    id = Column(Integer, primary_key=True, autoincrement=True)
    project_id = Column(Integer, ForeignKey(Projects.__tablename__ + '.id'), nullable=False)
    name = Column(String(100), unique=True, nullable=False)
    description = Column(String, default='')
    is_archived = Column(Boolean, default=False)
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
    sort_id = Column(Integer, default=0, nullable=False) 
    is_visible = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)

    __table_args__ = (
        UniqueConstraint('project_id', 'name', name='project_module_unique_constraint'),
    )


class Teams(db.Base):
    __tablename__ = "agile_project_teams"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False)
    status = Column(String(100), nullable=False)
    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_by = Column(String(255), nullable=True)
    updated_at = Column(DateTime, onupdate=func.now())

class TeamMembers(db.Base):
    __tablename__ = "agile_project_team_members"

    id = Column(Integer, primary_key=True, autoincrement=True)
    team_id = Column(Integer, ForeignKey(Teams.__tablename__ + '.id'), nullable=False)
    username = Column(String(255), nullable=False)


class ProjectAccess(db.Base):
    __tablename__ = "agile_project_access"

    id = Column(Integer, primary_key=True, autoincrement=True)
    project_id = Column(Integer, ForeignKey(Projects.__tablename__ + '.id'), nullable=True)
    username = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    is_owner = Column(Boolean, default=False)
    status = Column(String(255), default='active')
    team_id = Column(Integer, ForeignKey(Teams.__tablename__ + '.id'), nullable=True)

    __table_args__ = (
        UniqueConstraint('project_id', 'username', name='user_name_unique_constraint'),
    )

class FavoriteProjects(db.Base):
    __tablename__ = "agile_fevorite_projects"

    id = Column(Integer, primary_key=True, autoincrement=True)
    project_id = Column(Integer, ForeignKey(Projects.__tablename__ + '.id'), nullable=False)
    username = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())    

class CardStatus(db.Base):
    __tablename__ = "agile_card_status"

    id = Column(Integer, primary_key=True, autoincrement=True)
    label = Column(String(50), unique=True, nullable=False)
    description = Column(String(100))
    is_default = Column(Boolean, default = False)
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

class CardPriorities(db.Base):
    __tablename__ = "agile_card_priorities"

    id = Column(Integer, primary_key=True, autoincrement=True)
    label = Column(String(50), unique=True, nullable=False)
    description = Column(String(100))
    level = Column(Integer, default=0)
    color_code = Column(String(20))
    is_default = Column(Boolean, default = False)
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

class ProjectCards(db.Base):
    __tablename__ = "agile_project_cards"

    id = Column(Integer, primary_key=True, autoincrement=True)
    project_id = Column(Integer, ForeignKey(Projects.__tablename__ + '.id'), nullable=False)
    module_id = Column(Integer, ForeignKey(ProjectModules.__tablename__ + '.id'), nullable=False)
    current_column_id = Column(Integer, ForeignKey(ProjectColumns.__tablename__ +  '.id'), nullable=False)
    status = Column(Integer, ForeignKey(CardStatus.__tablename__ + '.id'))
    priority = Column(Integer, ForeignKey(CardPriorities.__tablename__ + '.id'))
    title = Column(String(500), nullable=False)
    card_identifier = Column(String(100), nullable=False)
    description = Column(String)
    assigned_to = Column(String(100), nullable=True)
    spent_time = Column(Interval, default='00:00:00', nullable=True)
    estimation = Column(Interval, default='00:00:00', nullable=True)
    due_date = Column(DateTime, default= None, nullable=True)
    is_archived = Column(Boolean, default=False)    
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
    sort_id = Column(Integer, nullable=False)
    pipeline_record_id = Column(String, nullable=True)
    # pipeline_column_mapping_id = Column(Integer, nullable=True)
    table_name = Column(String(255), nullable=True)
    parent_card_id = Column(Integer, nullable=True)


class CardTags(db.Base):
    __tablename__ = "agile_card_tags"

    id = Column(Integer, primary_key=True, autoincrement=True)
    card_id = Column(Integer, ForeignKey(ProjectCards.__tablename__ + '.id'), nullable=False)
    tag_key = Column(String(50), unique=True, nullable=False)
    tag_value = Column(String(200), unique=True, nullable=False)
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
    

class CardComments(db.Base):
    __tablename__ = "agile_card_comments"

    id = Column(Integer, primary_key=True, autoincrement=True)
    card_id = Column(Integer, ForeignKey(ProjectCards.__tablename__ + '.id'), nullable=False)
    comment = Column(String, nullable=False)   
    created_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

class ProjectCardsGanttSorting(db.Base):
    __tablename__ = "agile_project_cards_gantt_sorting"

    id = Column(Integer, primary_key=True, autoincrement=True)
    project_id = Column(Integer, ForeignKey(Projects.__tablename__ + '.id'), nullable=False)
    module_id = Column(Integer, ForeignKey(ProjectModules.__tablename__ + '.id'), nullable=False)
    ticket_ids = Column(String, nullable=True)
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

    __table_args__ = (
        UniqueConstraint('project_id', 'module_id', name='module_id_unique_constraint'),
    )

class TaggedUsers(db.Base):
    __tablename__ = "agile_tagged_users"

    card_id = Column(Integer, ForeignKey(ProjectCards.__tablename__ + '.id'), primary_key=True, nullable=False)
    username = Column(String(100),  primary_key=True, nullable=False)


class CustomFields(db.Base):
    __tablename__ = "agile_customfield_meta"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100),  unique=True, nullable=False)
    datatype = Column(String(10),  Enum("string", "number", "date", "select", "multi select", name="custom_field_datatypes", create_type=False))
    is_multiselect_group = Column(Boolean, default=False)
    group_values = Column(String) # Comma separated values
    sort_id = Column(Integer, default=0)
    is_mandatory = Column(Boolean, default=False)
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
    project_id = Column(Integer, nullable=True)
    status = Column(String(200), default="active")


class ProjectCustomFields(db.Base):
    __tablename__ = "agile_project_customfield"

    id = Column(Integer, primary_key=True, autoincrement=True)
    project_id = Column(Integer, ForeignKey(Projects.__tablename__ + '.id'), nullable=False)
    name = Column(String(100), nullable=False)  #composite unique key
    customfield_meta_id = Column(Integer, ForeignKey(CustomFields.__tablename__ + '.id'), nullable=False)
    sort_id = Column(Integer, default=0)
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
    is_visible = Column(Boolean, default=True)
    status = Column(String(200), default="active")
    read_only = Column(Boolean, default=False)

    __table_args__ = (
        UniqueConstraint('project_id', 'name', name='project_name_unique_constraint'),
    )


class CustomFieldValues(db.Base):
    __tablename__ = "agile_customfield_value"

    id = Column(Integer, primary_key=True, autoincrement=True)
    project_customfield_id = Column(Integer, ForeignKey(ProjectCustomFields.__tablename__ + '.id'), nullable=False)
    card_id = Column(Integer, ForeignKey(ProjectCards.__tablename__ + '.id'), nullable=False)
    project_id = Column(Integer, ForeignKey(Projects.__tablename__ + '.id'), nullable=False)
    str_value = Column(String) # use for string, group, date
    number_value = Column(Numeric)
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())


class CardIndex(db.Base):
    __tablename__ = "agile_project_cardindex"

    id = Column(Integer, primary_key=True, autoincrement=True)
    project_id = Column(Integer, ForeignKey(Projects.__tablename__ + '.id'), nullable=False)
    card_index = Column(Integer)
    created_at = Column(DateTime, server_default=func.now())

class PipelineModules(db.Base):
    __tablename__ = "pipeline_modules"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False)
    description = Column(String(255), nullable=True)

class PipelineDBTables(db.Base):
    __tablename__ = "pipeline_db_tables"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    pipeline_module_id = Column(Integer, ForeignKey(PipelineModules.__tablename__ + '.id'), nullable=False)
    table_name = Column(String(255), nullable=False)
    title = Column(String(255), nullable=False)
    description = Column(String(255), nullable=True)
    mapping_column = Column(String(255), nullable=True)
    related_table_1 = Column(String(255), nullable=True)
    mapping_column_1 = Column(String(255), nullable=True)
    related_table_2 = Column(String(255), nullable=True)
    mapping_column_2 = Column(String(255), nullable=True)
    related_table_3 = Column(String(255), nullable=True)
    mapping_column_3 = Column(String(255), nullable=True)

class PipelineProjectTableMapping(db.Base):
    __tablename__ = "pipeline_project_table_mapping"
		
    id = Column(Integer, primary_key=True, autoincrement=True) 
    project_id = Column(Integer, ForeignKey(Projects.__tablename__ + '.id'), nullable=True)
    pipeline_db_tables_id = Column(Integer, ForeignKey(PipelineDBTables.__tablename__ + '.id'), nullable=False)  #(pipeline_db_tables(id))
    # default_project_module_id = Column(Integer, ForeignKey(ProjectModules.__tablename__ + '.id'), nullable=True) #(projects_modules(id))
    # default_project_column_id = Column(Integer, ForeignKey(ProjectColumns.__tablename__ + '.id'), nullable=True)  #(projects_columns(id))
    ticket_name = Column(String(255), nullable=False)
    default_assignee = Column(String(255), nullable=True)
    db_table_column = Column(String(255), nullable=False)

    __table_args__ = (
        UniqueConstraint('project_id', 'pipeline_db_tables_id', name='uq_project_database_table'),
    )


class PipelineColumnMapping(db.Base):
    __tablename__ = "pipeline_column_mapping"

    id = Column(Integer, primary_key=True, autoincrement=True)
    project_id = Column(Integer, ForeignKey(Projects.__tablename__ + '.id'), nullable=True)
    project_column_id = Column(Integer, ForeignKey(ProjectColumns.__tablename__ + '.id'), nullable=True)
    project_table_mapping_id = Column(Integer, ForeignKey(PipelineProjectTableMapping.__tablename__ + '.id'), nullable=True)
    table_column_value = Column(String(255), nullable=True)
    table_column_value_type = Column(String(255), nullable=True)

class PipelineCustomFieldMapping(db.Base):
    __tablename__ = "pipeline_custom_field_mapping"

    id = Column(Integer, primary_key=True, autoincrement=True)
    project_id = Column(Integer, ForeignKey(Projects.__tablename__ + '.id'), nullable=True)
    project_customfield_id = Column(Integer, ForeignKey(ProjectCustomFields.__tablename__ + '.id'), nullable=True)
    project_table_mapping_id = Column(Integer, ForeignKey(PipelineProjectTableMapping.__tablename__ + '.id'), nullable=True)
    db_table_column = Column(String(255), nullable=False)
    

class TicketLogs(db.Base):
    __tablename__ = "agile_project_cards_logs"

    id = Column(Integer, primary_key=True, autoincrement=True)
    card_id = Column(Integer, ForeignKey(ProjectCards.__tablename__ + '.id'), nullable=False)
    field_name = Column(String, nullable=False)
    old_value = Column(String, nullable=True)
    new_value = Column(String, nullable=True)
    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, server_default=func.now()) 
    custom_field_id = Column(Integer, nullable=True)

class TicketAttachments(db.Base):
    __tablename__ = "agile_project_card_attachments"

    id = Column(Integer, primary_key=True, autoincrement=True)
    project_id = Column(Integer, ForeignKey(Projects.__tablename__ + '.id'), nullable=False)
    card_id = Column(Integer, ForeignKey(ProjectCards.__tablename__ + '.id'), nullable=False)
    file_name = Column(String, nullable=True)
    file_size = Column(Integer, nullable=True)
    relative_path = Column(String, nullable=True)
    file_type = Column(String, nullable=True)
    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, server_default=func.now()) 
