from utils import product_util, cart_util, store_util, customer_util, order_util, bc
import logging
from new_mongodb import get_tenant_db_client
from bson import ObjectId
from utils import redis_util
from utils.common import convert_to_timestamp

logger = logging.getLogger()

# product inventory scopes ...
PRODUCT_INVENTORY_UPDATED_SCOPE = "store/product/inventory/updated"

# sku scopes ...
SKU_INVENTORY_UPDATED_SCOPE = "store/sku/inventory/updated"
SKU_UPDATED_SCOPE = "store/sku/updated"
SKU_DELETED_SCOPE = "store/sku/deleted"
SKU_CREATED_SCOPE = "store/sku/created"

# order scopes ...
ORDER_CREATED_SCOPE = "store/order/created"
ORDER_UPDATED_SCOPE = "store/order/updated"

# product scopes ...
PRODUCT_UPDATED_SCOPE = "store/product/updated"
PRODUCT_CREATED_SCOPE = "store/product/created"
PRODUCT_DELETED_SCOPE = "store/product/deleted"

# customer scopes ...
CUSTOMER_CREATED_SCOPE = "store/customer/created"
CUSTOMER_UPDATED_SCOPE = "store/customer/updated"
CUSTOMER_DELETED_SCOPE = "store/customer/deleted"


def get_store_for_webhook_payload(payload):
    store_hash = payload["producer"].split("/")[1]
    store = store_util.get_store_by_storehash(store_hash)
    return store


def process_webhook(payload, token):
    logger.info('webhook proccesing')
    scope = payload["scope"]
    store = get_store_for_webhook_payload(payload)
    if not store:
        return False
    webhook_token = store.get("webhook_token", None)
    if not webhook_token or webhook_token != token:
        return False

    if scope == PRODUCT_INVENTORY_UPDATED_SCOPE:
        product_util.handle_product_inventory_webhook(store, payload)
        # payload = {'producer': 'stores/i5c9ggdve', 'hash': '291313a99bee4677da44a8a5ae500aed12f6a928', 'created_at': 1670693373, 'store_id': '999678339', 'scope': 'store/product/inventory/updated', 'data': {'type': 'product', 'id': 17078, 'inventory': {'product_id': 17078, 'method': 'absolute', 'value': 100}}}
    elif scope == SKU_INVENTORY_UPDATED_SCOPE:
        product_util.handle_sku_inventory_webhook(store, payload)
        # payload = {'producer': 'stores/i5c9ggdve', 'hash': 'da4539821161ed8a2903ec79981199635805a141', 'created_at': 1670692462, 'store_id': '999678339', 'scope': 'store/sku/inventory/updated', 'data': {'type': 'sku', 'id': 82704, 'inventory': {'product_id': 12410, 'method': 'absolute', 'value': 200, 'variant_id': 99638}}}
    elif scope == SKU_UPDATED_SCOPE:
        product_util.handle_sku_update_webhook(store, payload)
        # payload = {'producer': 'stores/i5c9ggdve', 'hash': '2f829bb32c2ad4b5180db6c92bb36f2d8023ad2a', 'created_at': 1670692341, 'store_id': '999678339', 'scope': 'store/sku/updated', 'data': {'type': 'sku', 'id': 82704, 'sku': {'product_id': 12410, 'variant_id': 99638}}}
    elif scope == PRODUCT_UPDATED_SCOPE:
        product_util.handle_product_update_webhook(store, payload)
        # payload = {'producer': 'stores/i5c9ggdve', 'hash': '4150686849231f2ffface9310a070ffa6bb6d3b1', 'created_at': 1670693206, 'store_id': '999678339', 'scope': 'store/product/updated', 'data': {'type': 'product', 'id': 17077}}
    elif scope == PRODUCT_CREATED_SCOPE:
        product_util.handle_product_update_webhook(store, payload)
        # payload = {'producer': 'stores/i5c9ggdve', 'hash': '684c6994ba4bff3b619965554f8f65129fa752de', 'created_at': 1670693373, 'store_id': '999678339', 'scope': 'store/product/created', 'data': {'type': 'product', 'id': 17078}}
    elif scope == CUSTOMER_CREATED_SCOPE:
        customer_util.process_customer_created(store, payload)
    elif scope == CUSTOMER_UPDATED_SCOPE:
        customer_util.process_customer_updated(store, payload)
    elif scope == CUSTOMER_DELETED_SCOPE:
        customer_util.process_customer_deleted(store, payload)
    elif scope == ORDER_CREATED_SCOPE:
        cart_util.process_order_created(store, payload)
    elif scope == ORDER_UPDATED_SCOPE:
        order_util.process_order_updated(store, payload)
    elif scope == PRODUCT_DELETED_SCOPE:
        product_util.process_product_delete(store, payload)
    elif scope == SKU_CREATED_SCOPE:
        payload = {'producer': 'stores/i5c9ggdve', 'hash': '4c4e0b168dde1ddf5af31d18d4da61257d4ddd99', 'created_at': 1670692720,
                   'store_id': '999678339', 'scope': 'store/sku/created', 'data': {'type': 'sku', 'id': 82713, 'sku': {'product_id': 12410, 'variant_id': 99648}}}
    elif scope == SKU_DELETED_SCOPE:
        payload = {'producer': 'stores/i5c9ggdve', 'hash': 'd2813af9d08dc67907901dcd2968c44930d7c66c', 'created_at': 1670692620,
                   'store_id': '999678339', 'scope': 'store/sku/deleted', 'data': {'type': 'sku', 'id': 82708, 'sku': {'product_id': 12410, 'variant_id': 99642}}}

    return True


def get_registered_webhook(store_id=None, store=None):
    ret = None
    if not store:
        store = store_util.get_store_by_id(store_id)
    if store:
        bc_api = store_util.get_bc_api_creds(store)
        bc_api_url = "v3/hooks"
        res = bc.call_api(bc_api, "GET", bc_api_url)
        ret = {
            "status": res.status_code,
            "data": res.json()
        }
    return ret


def update_registered_webhook(store_id=None, store=None, webhooks=[]):
    ret = {
        'data': []
    }
    if not store:
        store = store_util.get_store_by_id(store_id)
    if store:
        bc_api_url = "v3/hooks/"
        for wh in webhooks:
            wh_id = wh["id"]
            payload = wh["update"]
            bc_api = store_util.get_bc_api_creds(store)
            res = bc.call_api(bc_api, "PUT",
                              bc_api_url + str(wh_id), req_body=payload)
            ret['data'].append({
                "id": wh_id,
                "status": res.status_code,
                "response": res.json()
            })

    return ret


def delete_registered_webhook(store_id=None, store=None, webhooks=[]):
    ret = {
        'data': []
    }
    if not store:
        store = store_util.get_store_by_id(store_id)
    if store:
        bc_api_url = "v3/hooks/"
        for wh in webhooks:
            wh_id = wh["id"]
            bc_api = store_util.get_bc_api_creds(store)
            res = bc.call_api(bc_api, "DELETE", bc_api_url + str(wh_id))
            ret['data'].append({
                "id": wh_id,
                "status": res.status_code,
                "response": res.json()
            })

    return ret


def register_webhook(store_id=None, store=None, scope=None):

    ret = {
        'data': []
    }

    if not store:
        store = store_util.get_store_by_id(store_id)

    if store:
        bc_api = store_util.get_bc_api_creds(store)
        bc_api_url = "v3/hooks"

        callback_url = store['backend_url'] + "/admin/api/bc/config/webhook"
        payload = {
            "scope": scope,
            "destination": callback_url,
            "headers": {
                "Authorization": store['webhook_token']
            }
        }

        if scope:
            res = bc.call_api(bc_api, "POST",
                              bc_api_url, req_body=payload)
            ret['data'].append({
                "status": res.status_code,
                "scope": payload['scope'],
                "response": res.json()
            })
        else:
            payload['scope'] = "store/product/*"
            bc_api = store_util.get_bc_api_creds(store)
            res = bc.call_api(bc_api, "POST",
                              bc_api_url, req_body=payload)
            ret['data'].append({
                "status": res.status_code,
                "scope": payload['scope'],
                "response": res.json()
            })

            payload['scope'] = "store/sku/*"
            res = bc.call_api(bc_api, "POST",
                              bc_api_url, req_body=payload)
            ret['data'].append({
                "status": res.status_code,
                "scope": payload['scope'],
                "response": res.json()
            })

            payload['scope'] = "store/order/created"
            res = bc.call_api(bc_api, "POST",
                              bc_api_url, req_body=payload)
            ret['data'].append({
                "status": res.status_code,
                "scope": payload['scope'],
                "response": res.json()
            })

            payload['scope'] = "store/customer/created"
            res = bc.call_api(bc_api, "POST",
                              bc_api_url, req_body=payload)
            ret['data'].append({
                "status": res.status_code,
                "scope": payload['scope'],
                "response": res.json()
            })
    return ret


def get_webhook_listing(store):
    # Fetch registered webhooks data
    response = get_registered_webhook(store=store)

    # Initialize list to hold the detailed webhook information
    webhook_info = []
    
    # Check if response has valid data
    if response.get("status") == 200 and "data" in response:
        webhook_data = response["data"].get("data", [])
        
        # Loop through each webhook and extract data
        for webhook in webhook_data:
            id = webhook.get("id", None)
            scope = webhook.get("scope", None)
            is_active = webhook.get("is_active", None)
            
            # Fetch webhook status from Redis, providing scope-specific info
            status = redis_util.get_webhook_status(store, scope)

            # Combine Redis status with other webhook details
            webhook_info.append({
                "id": id,
                "scope": scope,
                "is_active": is_active,
                "count": status.get("count", 0),
                "last_webhook_call": convert_to_timestamp(status.get("last_webhook_call", None)),
                "last_webhook_succeeded":convert_to_timestamp(status.get("last_webhook_succeeded", None)),
                "error": status.get("error", None)
            })
    
    return webhook_info


def get_webhooks(store_id):
    db = get_tenant_db_client()
    store = store_util.get_store_by_id(store_id)
    # Access the `stores` collection
    stores_collection = db.stores
    
    # Find the store document by store_id (convert it to ObjectId if it's not already)
    store_document = stores_collection.find_one({"_id": ObjectId(store_id)})
    
    if store_document:
        # Navigate to the bc_config -> webhook_config section
        bc_config = store_document.get("bc_config", {})
        webhook_config = bc_config.get("webhook_config", {})
        
        # Fetch the destination and token from webhook_config
        destination = webhook_config.get("destination")
        token = webhook_config.get("token")

        webhook_data = get_webhook_listing(store)
        
        # Filter active webhooks and collect their scopes
        scopes = [
            {"id": webhook["id"], "scope": webhook["scope"]}
            for webhook in webhook_data
        ]
        
        return {
            "destination": destination,
            "token": token,
            "scopes": scopes
        }
    else:
        return {"error": "Store not found"}


def register_webhook_to_store(store_id=None, store=None, webhooks=[]):
    ret = {
        'data': []
    }

    if not store:
        store = store_util.get_store_by_id(store_id)

    if store:
        bc_api_url = "v3/hooks/"
        for wh in webhooks:
            payload = wh["update"]
            bc_api = store_util.get_bc_api_creds(store)
            res = bc.call_api(bc_api, "POST", bc_api_url, req_body=payload)

            # Add the webhook response to the result
            response_data = {
                "status": res.status_code,
                "response": res.json()
            }

            # Add the scope from the payload to the response, especially for failed cases
            if "scope" in payload:
                response_data["scope"] = payload["scope"]

            ret['data'].append(response_data)

    return ret


def add_webhooks(store_id, webhooks_payload):
    db = get_tenant_db_client()
    store = db["stores"].find_one({"_id": ObjectId(store_id)})

    if not store:
        return {"error": "Store not found"}

    destination = webhooks_payload.get("destination")
    token = webhooks_payload.get("token")

    update_webhooks = []
    create_webhooks = []

    for webhook in webhooks_payload.get("scopes", []):
        scope = webhook["scope"]
        if webhook.get("id"):
            update_webhooks.append({
                "id": webhook["id"],
                "update": {
                    "scope": scope,
                    "destination": destination,
                    "is_active": True,
                    "headers": {"Authorization": token}
                }
            })
        else:
            create_webhooks.append({
                "update": {
                    "scope": scope,
                    "destination": destination,
                    "is_active": True,
                    "headers": {"Authorization": token}
                }
            })

    updated_ids = []
    created_ids = []
    failed_updates = []
    failed_creations = []

    # Handle updates
    if update_webhooks:
        update_result = update_registered_webhook(store_id=store_id, webhooks=update_webhooks)
        for webhook in update_result.get("data", []):
            if webhook.get("status") == 200:
                updated_ids.append({
                    "id": webhook["id"],
                    "scope": webhook["response"]["data"]["scope"],
                    "updated_at": webhook["response"]["data"]["updated_at"]
                })
            else:
                failed_updates.append({
                    "id": webhook.get("id")
                })

    # Handle creations
    if create_webhooks:
        create_result = register_webhook_to_store(store_id=store_id, webhooks=create_webhooks)
        for webhook in create_result.get("data", []):
            response_data = webhook.get("response", {}).get("data", {})
            if "id" in response_data:
                created_ids.append({
                    "id": response_data["id"],
                    "scope": response_data.get("scope"),
                    "created_at": response_data.get("created_at")
                })
            else:
                failed_creations.append({
                    "scope": webhook.get("scope")
                })

    # Prepare the list of scopes that were successfully created or updated
    successful_scopes = [webhook["scope"] for webhook in updated_ids + created_ids]

    # Update MongoDB with the successful scopes
    try:
        db["stores"].update_one(
            {"_id": ObjectId(store_id)},
            {
                "$set": {
                    "bc_config.webhook_config.destination": destination,
                    "bc_config.webhook_config.token": token,
                    "bc_config.webhook_config.webhooks": successful_scopes
                }
            }
        )
    except Exception as e:
        return {"error": f"Database update failed: {str(e)}"}
    
    success_count = len(updated_ids) + len(created_ids)
    message = f"{success_count} webhook(s) updated or created successfully."

    return {
        "message": message,
        "successful_updates": updated_ids,
        "successful_creations": created_ids,
        "failed_updates": failed_updates,
        "failed_creations": failed_creations
    }
