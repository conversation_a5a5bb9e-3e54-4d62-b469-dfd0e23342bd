from new_mongodb import tenant_db, RoleKeys, fetch_one_document_from_admin_collection \
    ,upsert_documents, update_document_in_admin_collection \
    ,fetchall_documents_from_admin_collection, process_documents \
    ,delete_documents_from_admin_collection
import traceback
import logging
from utils.common import parse_json
from bson import ObjectId

logger = logging.getLogger()

PERMISSION_DB = "permissions"

def get_permissions_for_role(role_id):
    permissions = None
    if role_id:
        role = tenant_db.fetch_role_by_id(role_id=role_id)
        if role:
            permissions = role[RoleKeys.PERMISSIONS]
    return permissions

def get_permission_by_store_id(store):
    store_id = store['id']
    query = {"store_id": store_id, "status": "active"}
    res = fetch_one_document_from_admin_collection(store_id, PERMISSION_DB, query)        

    if res == None:
        return {}
    else:
        return parse_json(res)

def create_permissions(store, data):
    data['store_id'] = store['id']
        
    # check if permission is already exits in DB.
    permission = get_permission_by_store_id(store)

    if permission == None:
        id = upsert_documents(store, PERMISSION_DB, data)
        if id:
            return {"status": "success" }, 201
    else:
        return {'message': '1 permission object already attched with the store name: ' + store['name']}, 400

def update_permissions(store, id, data):
        updated_data = {
            "permissions": data['permissions']
        }
        query = {"_id": ObjectId(id)}
        id = update_document_in_admin_collection(store, PERMISSION_DB, query, {"$set": updated_data})
        return id

def delete_by_id(store, id):
    return delete_documents_from_admin_collection(store, PERMISSION_DB, {"_id": ObjectId(id)})
    
def get_all_permissions(store_id):
    permissions = fetchall_documents_from_admin_collection(store_id, PERMISSION_DB, {})        
    return process_documents(permissions)
