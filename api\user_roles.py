from flask import request
import logging
import traceback
from api import APIResource
from mongo_db import get_admin_db_client, processList, update_one
from iam import role_service
from exceptions.common_exceptions import ResourceAlreadyExistException, InvalidInputException, ResourceNotFoundException

logger = logging.getLogger()

class Roles(APIResource):

    def post_executor(self, request, token_payload, store):
        try:
            if store:
                payload = request.get_json(force=True)
                new_role_id = role_service.create_role(store, payload)
                if new_role_id:
                    return {"id": str(new_role_id)}, 201
                else:
                    return {"message": "Failed to create role."}, 400
        except ResourceAlreadyExistException as e:
            return {"message": "role: Role already exists."}, 409
        except InvalidInputException as e:
            return {"message": "Provide valid permissions and role name.."}, 400
        finally:
            logger.debug("Exiting Role POST")
    
    def get_executor(self, request, token_payload, store):
        try:
            res = role_service.get_roles(store)
            return {'data': res}, 200
        finally:
            logger.debug("Exiting Role GET")

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class Role(APIResource):
    
    def get_executor(self, request, token_payload, store, role_id):
        store_id = store['id']
        try:
            if not role_id:
                return {"message": "role_id required"}, 400
            else:
                res = role_service.get_one(store_id, role_id)
                return {'data': res}, 200
        finally:
            logger.debug("Exiting Role GET")

    def put_executor(self, request, token_payload, store, role_id):
        try:
            payload = request.get_json(force=True)
            role_service.update_role(store, role_id, payload)
            return {"message":"Role has been updated successfully."}, 200
        except InvalidInputException as e:
            return {"message": "Provide valid role id, role name and permissions."}, 400
        except ResourceAlreadyExistException as e:
            return {"message": "role: Role with the same name is already exist."}, 409
        except ResourceNotFoundException as e:
            return {"message": f"role_id: Role with id {role_id} doesn't exist."}, 400
        finally:
            logger.debug("Exiting Role Update")

    def delete_executor(self, request, token_payload, store, role_id):
        try:
            if role_id:
                res = role_service.delete_by_id(store, role_id)
                if res['status'] == 200:
                    return {"message": res['message']}, 200
                else:
                    return {"message": res['message']}, res['status']
            else:
                return {"status": "Invalid role id."}, 400
        except ResourceAlreadyExistException as e:
            logger.error("Role Delete : Exception caught: " + traceback.format_exc())
            return {"message": "Role is assigned to users."}, 409
        finally:
            logger.debug("Exiting Role Delete")

    def get(self, role_id):
        return self.execute_store_request(request, self.get_executor, role_id)
    
    def put(self, role_id):
        return self.execute_store_request(request, self.put_executor, role_id)
    
    def delete(self, role_id):
        return self.execute_store_request(request, self.delete_executor, role_id)

class ConvertRoles(APIResource):
    def post_executor(self, request, token_payload, store):
        db = get_admin_db_client()
        roles = db['roles'].find({})
        all_roles = processList(roles)
        
        def convert_role(role):
            permissions = role['permissions']        
            new_permissions = {}

            for item in permissions:
                obj = item
                obj['children'] = {}

                if len(item['childrens']):
                    for child in item['childrens']:
                        child['children'] = {}
                        
                        if 'childrens' in child:
                            del child['childrens']

                        obj['children'].update({ child['name']: child }) 
                    
                    del obj['childrens']

                else:
                    if 'childrens' in obj:
                        del obj['childrens']

                new_permissions[obj['name']] = obj
            
            role['permissions'] = new_permissions
            return role
        
        for i in all_roles:
            role = convert_role(i)
            update_one(role['id'], role)

    def post(self):
        return self.execute_store_request(request, self.post_executor)