import logging
from datetime import datetime, timezone
import new_utils
import task
from utils.common import conver_to_json, paginate_data_postgres, parse_json, calculatePaginationData,get_six_month_data_using_sku, get_table_record_count, get_search_query, get_six_month_data_using_parent_sku, get_month_array_for_meta, convert_to_timestamp
import new_pgdb
from sqlalchemy import text
import traceback
import os
from flask import send_file, make_response
from new_mongodb import StoreAdminDBCollections, get_admin_db_client_for_store_id, fetch_one_document_from_admin_collection
from new_mongodb import store_admin_db
from products.all_products import products_list
from new_utils import store_util
from utils import bc
from new_pgdb.analytics_db import AnalyticsDB
from appconfig import is_pgdb_read_only_enabled

logger = logging.getLogger()

def create_classified_as(store, name, username):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        # Check if the name already exists (case-insensitive) using LIMIT 1
        check_query = """
            SELECT 1 
            FROM replenishment_classified_as 
            WHERE LOWER(name) = LOWER(:name) 
            LIMIT 1
        """
        result = conn.execute(text(check_query), {'name': name}).fetchone()

        if result:
            # If a result is found, it means the name already exists
            response["status"] = 409
            response["message"] = f"The name '{name}' already exists. Please use a different name."
        else:
            # Proceed to insert the new record if no duplicate is found
            insert_query = """
                INSERT INTO replenishment_classified_as (name, created_by, created_at) 
                VALUES (:name, :username, CURRENT_TIMESTAMP)
            """
            result = conn.execute(text(insert_query), {'name': name, 'username': username})
            conn.commit()
            if result.rowcount > 0:
                response["status"] = 200
                response["message"] = "Classified as created successfully"
            else:
                response["status"] = 400
                response["message"] = "Failed to create classified as"
    except Exception as e:
        error_details = traceback.format_exc()
        print(f"An error occurred: {str(e)}")
        print(f"Traceback details:\n{error_details}")
        response["status"] = 500
        response["message"] = "An error occurred while processing your request."
    finally:
        conn.commit()
        conn.close()
    return response


def get_classified_as_data(store, page, limit, search, sort_array=[]):
    response = {
        "status": 400
    }
    data = []
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        query = """
            SELECT 
                rca.id, 
                rca.name, 
                rca.created_by, 
                rca.updated_by, 
                rca.created_at, 
                rca.updated_at, 
                COUNT(rd.classified_as_id) as product_count 
            FROM 
                replenishment_classified_as rca 
            LEFT JOIN 
                replenishment_dashboard rd 
            ON 
                rca.id = rd.classified_as_id
        """
        
        count_query = "SELECT COUNT(*) FROM replenishment_classified_as rca"

        # Add search condition if provided
        if search:
            search_condition = f" WHERE rca.name ILIKE '%{search}%'"
            query += search_condition
            count_query += search_condition

        # Add GROUP BY to aggregate correctly
        query += """
            GROUP BY rca.id, rca.name, rca.created_by, rca.updated_by, rca.created_at, rca.updated_at
        """
        
        if len(sort_array) > 0:
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ["name", "created_at", "updated_at"]:                
                query += f" ORDER BY rca.{sort_array[0]} {sort_direction}"

        if page and limit:
            page = int(page)
            limit = int(limit)
            offset = (page - 1) * limit  
            query += " LIMIT :limit OFFSET :offset"
            result = conn.execute(text(query), {'limit': limit, 'offset': offset})
        else:
            result = conn.execute(text(query))
        
        total_records = conn.execute(text(count_query)).scalar()
        for row in result:
            data.append({
                "id": row[0],
                "name": row[1],
                "created_by": row[2],
                "updated_by": row[3],
                "created_at": convert_to_timestamp(row[4]),
                "updated_at": convert_to_timestamp(row[5]),
                "product_count": row[6]
            })

        if page and limit:
            paginated_data = calculatePaginationData(data, page, limit, total_records)
            response["status"] = 200
            response["data"] = paginated_data
        else:
            response["status"] = 200
            response["data"] = data
    except Exception as e:
        error_details = traceback.format_exc()
        print(f"An error occurred: {str(e)}")
        print(f"Traceback details:\n{error_details}")
    finally:
        conn.close()
    return response


def get_classified_as_details(store, id):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        data = {}
        query = """
            SELECT * FROM replenishment_classified_as WHERE id = :id
        """
        result = conn.execute(text(query), {'id': id})
        for row in result:
            data = {
                "id": row[0],
                "name": row[1],
                "created_by": row[2],
                "updated_by": row[3],
                "created_at": convert_to_timestamp(row[4]),
                "updated_at": convert_to_timestamp(row[5])
            }
        if data:
            response["status"] = 200
            response["data"] = data
        else:
            response["status"] = 404
            response["message"] = "Classified as not found"
    except Exception as e:
        error_details = traceback.format_exc()
        print(f"An error occurred: {str(e)}")
        print(f"Traceback details:\n{error_details}")
    finally:
        conn.close()
    return response

def update_classified_as(store, id, name, username):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        # Check if the name already exists (case-insensitive) using LIMIT 1
        check_query = """
            SELECT 1
            FROM replenishment_classified_as 
            WHERE LOWER(name) = LOWER(:name) AND id != :id
            LIMIT 1
        """
        result = conn.execute(text(check_query), {'name': name, 'id': id}).fetchone()
        
        old_name_query = """
            SELECT name FROM replenishment_classified_as WHERE id = :id
        """
        old_name_result = conn.execute(text(old_name_query), {'id': id}).fetchone()
        old_name = old_name_result[0]

        if result:
            # If a result is found, it means the name already exists
            response["status"] = 409
            response["message"] = f"The name '{name}' already exists. Please use a different name."
        else:
            query = """
                UPDATE replenishment_classified_as SET name = :name, updated_by = :username, updated_at = CURRENT_TIMESTAMP WHERE id = :id
            """
            res = conn.execute(text(query), {'name': name, 'username': username, 'id': id})
            conn.commit()
            dashboard_query = """
                UPDATE replenishment_dashboard SET classified_as = :name WHERE classified_as_id = :id
            """
            conn.execute(text(dashboard_query), {'name': name, 'id': id})
            update = update_custom_field_in_projects(store, old_name, name)

            conn.commit()
            if res.rowcount > 0:
                response["status"] = 200
                response["message"] = "Classified as updated successfully"
            else:
                response["status"] = 400
                response["message"] = "Failed to update classified as"
    except Exception as e:
        error_details = traceback.format_exc()
        print(f"An error occurred: {str(e)}")
        print(f"Traceback details:\n{error_details}")
    finally:
        conn.commit()
        conn.close()
    return response


def update_custom_field_in_projects(store, old_name, new_name):
    response = {
        "status": 400,
        "message": "Failed to update custom fields."
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        # Step 1: Fetch project_customfield_ids from pipeline_custom_field_mapping
        query = """
            SELECT project_customfield_id
            FROM pipeline_custom_field_mapping
            WHERE db_table_column = 'replenishment_dashboard.classified_as'
        """
        result = conn.execute(text(query))
        project_customfield_ids = [row[0] for row in result.fetchall()]
        
        if not project_customfield_ids:
            response['message'] = "No project_customfield_ids found."
            return response
        
        custome_field_query = """SELECT customfield_meta_id FROM agile_project_customfield WHERE id = :customfield_id"""
        for customfield_id in project_customfield_ids:
            result = conn.execute(text(custome_field_query), {'customfield_id': customfield_id})
            customfield_meta_id = result.fetchone()[0]
        
            # Step 2: Update group_values in agile_customfield_meta
            query_meta = """
                SELECT group_values
                FROM agile_customfield_meta
                WHERE id = :customfield_id
            """
            result = conn.execute(text(query_meta), {'customfield_id': customfield_meta_id})
            result = result.fetchone()
            if result:
                group_values = result[0]
                # Split and process group_values
                group_values_list = [value.strip() for value in group_values.split(",") if value.strip()]
                
                if new_name is None:
                    # Remove old_name from the list if new_name is None
                    updated_group_values = [value for value in group_values_list if value != old_name]
                else:
                    # Replace old_name with new_name
                    updated_group_values = [
                        new_name if value == old_name else value for value in group_values_list
                    ]
                
                # Join the list back into a comma-separated string
                updated_group_values = ",".join(updated_group_values)
                update_meta_query = """
                    UPDATE agile_customfield_meta
                    SET group_values = :updated_group_values
                    WHERE id = :customfield_id
                """
                conn.execute(text(update_meta_query), {'updated_group_values': updated_group_values, 'customfield_id': customfield_meta_id})
                conn.commit()
        
            # Step 3: Update str_value in agile_customfield_value
            update_value_query = """
                UPDATE agile_customfield_value
                SET str_value = :new_name
                WHERE project_customfield_id = :customfield_id AND str_value = :old_name
            """
            
            conn.execute(text(update_value_query), {'new_name': new_name, 'customfield_id': customfield_id, 'old_name': old_name})
        
        conn.commit()
        response['status'] = 200
        response['message'] = "Custom fields updated successfully."
    
    except Exception as e:
        error_details = traceback.format_exc()
        print(f"An error occurred: {str(e)}")
        print(f"Traceback details:\n{error_details}")
        conn.rollback()
    
    finally:
        conn.commit()
        conn.close()
    
    return response

def delete_classified_as(store, id):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        
        dashboard_query = """
            UPDATE replenishment_dashboard SET classified_as_id = NULL, classified_as = '' WHERE classified_as_id = :id
        """
        conn.execute(text(dashboard_query), {'id': id})

        old_name_query = """
            SELECT name FROM replenishment_classified_as WHERE id = :id
        """
        old_name_result = conn.execute(text(old_name_query), {'id': id}).fetchone()
        old_name = old_name_result[0]
        update = update_custom_field_in_projects(store, old_name, None)

        query = """
            DELETE FROM replenishment_classified_as WHERE id = :id
        """
        conn.execute(text(query), {'id': id})
        conn.commit()

        response["status"] = 200
        response["message"] = "Classified as deleted successfully"
    except Exception as e:
        error_details = traceback.format_exc()
        print(f"An error occurred: {str(e)}")
        print(f"Traceback details:\n{error_details}")
    finally:
        conn.commit()
        conn.close()
    return response