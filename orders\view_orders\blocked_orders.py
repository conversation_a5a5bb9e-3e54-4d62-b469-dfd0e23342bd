from datetime import datetime, timezone
import plugin
from plugin.bc_products import process_options
from sqlalchemy import text
import new_pgdb
from utils import bc, store_util
from new_pgdb import blocked_orders_db
from graphql import products_variant_query
import new_utils
from utils import auth_util
import json 
from utils.common import get_order_status_name
import logging
from plugin import bc_order, bc_products
from utils.common import convert_to_timestamp
import task
import traceback
logger = logging.getLogger()
from utils import common
import new_mongodb

def get_blocked_orders(store, limit, page):
    conn = new_pgdb.get_connection(store['id'])

    try:
        count_query = text("""
            SELECT COUNT(*)
            FROM blocked_orders
        """)
        count_result = conn.execute(count_query)
        total_count = count_result.fetchone()[0]
        
        # Initial SQL query
        base_query = """
                SELECT *
                FROM
                    blocked_orders
                ORDER BY
                    order_created_date_time DESC
        """

        # Pagination logic
        offset = (page - 1) * limit
        base_query += f" LIMIT {limit} OFFSET {offset}"

        # Execute the query using SQLAlchemy's text function
        query = text(base_query)
        result = conn.execute(query)
        res = result.fetchall()
        
        orders = []

        if res:
            for row in res:
                query = text("""
                    SELECT rep_name
                    FROM salesforce_customer_rep
                    WHERE customer_name = :customer_name
                """)
                query = query.params(customer_name=row[3])
                result = conn.execute(query)
                res = result.fetchone()
                customer_rep_name = res[0] if res else ""

                status_query = text("""
                    SELECT
                        order_status_id,
                        total_including_tax
                    FROM
                        orders
                    WHERE
                        order_id = :order_id
                """)
                status_query = status_query.params(order_id=row[1])
                status_query_result = conn.execute(status_query)
                status_res = status_query_result.fetchone()
                
                order_status = row[6] if row[6] else get_order_status_name(status_res[0])

                product_query = text("""SELECT DISTINCT p.product_name from order_line_items ol 
                                     join products p on p.product_id = ol.product_id where ol.order_id = :order_id and ol.quantity > 0""")
                product_query_result = conn.execute(product_query.params(order_id=row[1])).fetchall()
                product_names = [product[0] for product in product_query_result]

                orders.append({
                    'order_id': row[1],
                    'order_created_date_time': convert_to_timestamp(row[2]),
                    'customer_name': row[3],
                    'customer_representative': customer_rep_name,
                    'blocked_by': row[5],
                    'status': order_status,
                    'total': status_res[1],
                    'product_names': product_names if status_res[1] > 0 else []
                })

        if len(orders):
            data = new_utils.calculate_pagination(orders, page, limit, total_count)
            return data, 200
        
    except Exception as e:
        raise e
    
    finally:
        if conn:
            conn.close()

    return {'data': [], 'meta': { "pagination": {} }}, 200

def get_blocked_order_products(store, order_id):
    api = store_util.get_bc_api_creds(store)
    res = bc.get_bc_order_products_new(api,  order_id, store)

    if (len(res)):
        variant_ids = list(set([item['variant_id'] for item in res if item['quantity']]))
        product_ids = list(set([item['product_id'] for item in res if item['quantity']]))

        # check available quantity after removing sku from order ...
        graphql_query = products_variant_query.get_query(product_ids, variant_ids)
        status, gql_res = bc.process_bc_graphql_request(store, graphql_query)
        
        variants_inventory = {}
        min_max_rules = {}
        variants_options = {}

        if status == 200 and 'data' in gql_res and len(gql_res['data']['site']['products']['edges']):
            products = gql_res['data']['site']['products']['edges']
            
            for product in products:
                min_purchase_quantity = product['node']['minPurchaseQuantity']
                max_purchase_quantity = product['node']['maxPurchaseQuantity']
                min_max_rules[product['node']['entityId']] = {
                    'min_purchase_quantity': min_purchase_quantity,
                    'max_purchase_quantity': max_purchase_quantity
                }

                variants = product['node']['variants']['edges']
                for variant in variants:
                    variants_inventory[variant['node']['entityId']] = variant['node']['inventory']['aggregated']['availableToSell']
                    variants_options[variant['node']['entityId']] = plugin.process_edges(variant['node'], 'options', process_options)

        for item in res:
            item['quantity_available'] = variants_inventory.get(item['variant_id'], 0)
            item['min_purchase_quantity'] = min_max_rules.get(item['product_id'], {}).get('min_purchase_quantity', 0)
            item['max_purchase_quantity'] = min_max_rules.get(item['product_id'], {}).get('max_purchase_quantity', 0)
            item['variant_name'] = ''
            options = variants_options.get(item['variant_id'], [])
            if len(options):
                option_name = ''
                for option in options:                    
                        option_name = option_name + option['values'][0]['label'] + ' - '                
                item['variant_name'] = option_name.rstrip(' - ')

        data = [{
            'id': item['id'], 
            'order_id': item['order_id'], 
            'product_id': item['product_id'], 
            'variant_id': item['variant_id'],
            'name': item['name'],
            'product_image': item['images'],
            'sku': item['sku'],
            'blocked_qty': item['quantity'],
            'unit_price': item['base_price'],
            'total': item['base_total'],
            'available_qty': item['quantity_available'],
            'min_purchase_quantity': item['min_purchase_quantity'],
            'max_purchase_quantity': item['max_purchase_quantity'],
            'variant_name': item['variant_name']
            } for item in res if item['quantity']]
        
        return {'status': 200, 'data': data}

    else:
      return {'status': 400, 'message': 'Order not found'}
    

def call_bc_order_api(api, order_id, req_body):
    res_order = bc.call_api(api, "PUT", "v2/orders/" + order_id, None, req_body, None)

    return res_order

def update_log_table(store, order_id, username, sku, restock_qty, available_qty):
    result, role_id, user_id, current_user = auth_util.get_user_by_username(username)
    
    log_res = blocked_orders_db.add_logs(store, {
                'order_id': order_id,
                'date_time': datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
                'sku': sku,
                'triggered_by': current_user,
                'restock_qty': restock_qty,
                'available_qty': available_qty,
                })
                
    if log_res:
        return {'status': 200, 'message': 'Order updated successfully'}    
    else:
        return {'status': 200, 'message': 'Failed to update order'}

def update_blocked_order(store, order_id, payload, method, username, call_from_unit_price=False, name=None, is_recursive_call=False):
    req_body = {
        "products": []
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        # token = new_utils.cache_util.get_graphql_token(store['id'])
        token = store_util.reset_graphql_token(store)
        api = store_util.get_bc_api_creds(store)

        if not token:
            return {'status': 400, 'message': 'Invalid token'}
        
        order_products = bc_order.get_order_products(store, order_id)
        order_products = order_products.json()
        
        # 1. ADD New SKU ...
        if method == 'POST':
            new_skus = []
            input_skus = payload['sku']

            if not call_from_unit_price:
                if isinstance(input_skus, str):
                    new_skus = [input_skus]  # convert single SKU to list

            else:
                if len(payload.get('deleted_skus', [])):
                    deleted_skus = payload['deleted_skus']
                    update_blocked_order(store, order_id, deleted_skus, 'DELETE', username, call_from_unit_price=True, name=name, is_recursive_call=True)

                all_skus = []
                for sku_obj in input_skus:
                    if sku_obj.get('id') is None:
                        new_skus.append({
                            'sku': sku_obj['sku'],
                            'price_inc_tax': sku_obj.get('price_inc_tax', 0.0),
                            'price_ex_tax': sku_obj.get('price_ex_tax', 0.0),
                            'quantity': sku_obj.get('quantity', 0)
                        })
                    else:
                        existing_skus = payload.get('sku', [])
                        all_skus = [sku for sku in existing_skus if sku.get('id') is not None]

                # Build new PUT-ready payload
                update_payload = {
                    'products': [{
                        'id': sku['id'],
                        'product_id': sku['product_id'],
                        'variant_id': sku['variant_id'],
                        'price_inc_tax': sku['price_inc_tax'],
                        'price_ex_tax': sku['price_ex_tax'],
                        'quantity': sku['quantity']
                    } for sku in all_skus]
                }
                if len(update_payload['products']):
                    update_blocked_order(store, order_id, update_payload, 'PUT', username, call_from_unit_price=True, name=name, is_recursive_call=True)

                # If we're only deleting SKUs and not adding any new ones
                if not input_skus:
                    return {
                        'status': 200,
                        'message': 'Products deleted successfully',
                        'deleted_skus': deleted_skus
                    }

            # Initialize list to store all valid products
            valid_products = []
            invalid_skus = []
            affected_product_ids = []
            req_body = {"products": []}

            # First validate all SKUs
            for sku_obj in new_skus:
                entered_sku = sku_obj.get('sku').strip() if isinstance(sku_obj, dict) else sku_obj.strip()
                unit_price_inc_tax = sku_obj.get('price_inc_tax', None) if isinstance(sku_obj, dict) else None
                unit_price_ex_tax = sku_obj.get('price_ex_tax', None) if isinstance(sku_obj, dict) else None
                quantity = sku_obj.get('quantity', None) if isinstance(sku_obj, dict) else None
                validation_error = None

                if not call_from_unit_price:
                    # Check if SKU exists in current order
                    bo_items = get_blocked_order_products(store, order_id)
                    if bo_items['data']:
                        for item in bo_items['data']:
                            if item['sku'] == entered_sku:
                                invalid_skus.append({'sku': entered_sku, 'error': f'{entered_sku} SKU already exists in the order'})
                                validation_error = True
                                break
                else:
                    # Check if SKU exists in current order
                    # order_products = bc_order.get_order_products(store, order_id)
                    # order_products = order_products.json()
                    
                    if isinstance(order_products, list):
                        if len(order_products) > 0 and 'status' in order_products[0] and order_products[0]['status'] == 404:
                            return {"message": "Order not found.", "status": 404}
                        
                        # for product in order_products:
                        #     if 'sku' in product and product['sku'] == entered_sku:
                        #         if 'quantity' in product and product['quantity'] > 0:
                        #             invalid_skus.append({'sku': entered_sku, 'error': f'{entered_sku} SKU already exists in the order'})
                        #             validation_error = True
                        #             break

                if validation_error:
                    continue

                # Validate product existence and get product/variant IDs
                products_data = bc_products.get_products(store, entered_sku)
                products_data = products_data.json()

                if products_data['data'] != []:
                    product_sku = products_data['data'][0]['sku']
                    variant_sku = products_data['data'][0]['variants'][0]['sku'] if 'variants' in products_data['data'][0] and products_data['data'][0]['variants'] else None

                    if product_sku != variant_sku:
                        invalid_skus.append({'sku': entered_sku, 'error': 'Please enter a child SKU for this product.'})
                        continue

                product_id, variant_id = common.get_product_id_by_sku_from_mongo(store, entered_sku)
                if product_id == 0 and variant_id == 0:
                    invalid_skus.append({'sku': entered_sku, 'error': f'{entered_sku} is an invalid SKU'})
                    continue
                
                # Check availability and quantity
                graphql_query = products_variant_query.get_query([product_id], [variant_id])
                gql_status, gql_res = bc.process_bc_graphql_request(store, graphql_query)

                if gql_status == 200 and len(gql_res['data']['site']['products']['edges']) == 0:
                    invalid_skus.append({'sku': entered_sku, 'error': f'{entered_sku} is not available for purchase.'})
                    continue

                for item in gql_res['data']['site']['products']['edges']:
                    is_sku_available = item['node']['availabilityV2']['status']
                    if is_sku_available != 'Available':
                        invalid_skus.append({'sku': entered_sku, 'error': f'{entered_sku} is not available for purchase.'})
                        continue

                    available_qty = item['node']['variants']['edges'][0]['node']['inventory']['aggregated']['availableToSell']
                    if available_qty == 0:
                        invalid_skus.append({'sku': entered_sku, 'error': f'{entered_sku} is out of stock.'})
                        continue

                    min_purchase_qty = item['node']['minPurchaseQuantity'] or 1
                    available_qty -= min_purchase_qty

                    if not call_from_unit_price:
                        valid_products.append({
                            'sku': entered_sku,
                            'product_id': product_id,
                            'variant_id': variant_id,
                            'available_qty': available_qty,
                            'quantity': min_purchase_qty
                        })
                    else:
                        valid_products.append({
                            'sku': entered_sku,
                            'product_id': product_id,
                            'variant_id': variant_id,
                            'quantity': quantity,
                            'unit_price_inc_tax': unit_price_inc_tax,
                            'unit_price_ex_tax': unit_price_ex_tax
                        })

            # If we have any valid products, add them to the request body
            if valid_products:
                if not call_from_unit_price:
                    req_body['products'] = [{
                        "product_id": p['product_id'],
                        "variant_id": p['variant_id'],
                        "quantity": p['quantity']
                    } for p in valid_products]
                else:
                    req_body['products'] = [{
                        "product_id": p['product_id'],
                        "variant_id": p['variant_id'],
                        "quantity": p['quantity'],
                        "price_inc_tax": p['unit_price_inc_tax'],
                        "price_ex_tax": p['unit_price_ex_tax']
                    } for p in valid_products]
                
                payload_for_min_max_rule = transform_order_products_data(order_products)
                for item in req_body['products']:
                    payload_for_min_max_rule.append(item)
                affected_product_ids = check_min_max_rule_for_lineitems(store, payload_for_min_max_rule)
                res = call_bc_order_api(api, order_id, req_body)
                if affected_product_ids:
                    set_min_max_rule_for_lineitems_in_bc(store, affected_product_ids)
                
                
                # Make single API call to BigCommerce with all valid products
                if not call_from_unit_price:
                    # res = call_bc_order_api(api, order_id, req_body)
                    if res:
                        # Update log table for all valid products
                        for product in valid_products:
                            update_log_table(store, order_id, username, product['sku'], -product['quantity'], product['available_qty'])
                        
                        response = {
                            'status': 200,
                            'message': 'Order updated successfully',
                            'invalid_skus': invalid_skus if invalid_skus else None
                        }
                        return response
                    else:
                        return {'status': 400, 'message': 'Failed to update order'}
                else:
                    # res = call_bc_order_api(api, order_id, req_body)
                    if res.status_code == 200:
                        if res.json().get('id'):
                            # Update order total
                            order_res = bc_order.fetch_order(store, order_id)
                            if order_res.status_code < 299:
                                order_data = order_res.json()
                                new_total_including_tax = order_data['total_inc_tax']
                                query = text(f"""UPDATE orders SET total_including_tax = :total_including_tax, updated_by = :name WHERE order_id = :order_id""")
                                results = conn.execute(query.params(total_including_tax=new_total_including_tax, name=name, order_id=order_id))
                                conn.commit()

                            
                            return {
                                'status': 200, 
                                'message': 'Order updated successfully',
                                'invalid_skus': invalid_skus if invalid_skus else None
                            }
                        else:
                            return {'status': 400, 'message': res.json()[0].get('message')}
                    else:
                        return {'status': 400, 'message': res.json()[0].get('message')}
            else:
                if invalid_skus:
                    return {
                        'status': 400,
                        'message': invalid_skus[0].get('error')
                    }
                else:
                    return {'status': 200, 'message': 'Order updated successfully'}

        # 2. UPDATE SKUS ...
        if method == 'PUT':
            all_skus = payload['products']
            restock_threshold = payload.get('restock_threshold', None)
            restock_quantity = payload.get('restock_quantity', None)

            product_list = list(set([sku['product_id'] for sku in all_skus]))
            variant_list = [sku['variant_id'] for sku in all_skus]
            
            result = []
            affected_product_ids = []
            # order_products = bc_order.get_order_products(store, order_id)
            # order_products = order_products.json()

            query = products_variant_query.get_query(product_list, variant_list)
            status, gql_res = bc.process_bc_graphql_request(store, query)
            
            
            if status != 200:
                return {'status': 400, 'message': 'Failed to update order'}
            
            if status == 200:  
                for item in gql_res['data']['site']['products']['edges']:
                    result.append(item['node']) 

                req_data = []
                available_qty_mapping = {}
                
                bo_items = get_blocked_order_products(store, order_id)

                current_blocked_qty_mapping = {}
                for bo_item in bo_items['data']:
                    current_blocked_qty_mapping[bo_item['variant_id']] = bo_item['blocked_qty']
                
                log_array = []
                for item in result:
                    product_id = item['entityId']
                    for edge in item['variants']['edges']:
                        variant_id = edge['node']['entityId']
                        item_sku = edge['node']['sku'] 
                        available_qty = edge['node']['inventory']['aggregated']['availableToSell']
                        
                        for sku in all_skus:
                            if sku['variant_id'] == variant_id:
                                prev_blocked_qty = current_blocked_qty_mapping[sku['variant_id']]  # Previous blocked quantity
                                upcoming_qty = sku['quantity']  # Updated (new) blocked quantity
                                qty_difference = upcoming_qty - prev_blocked_qty  # Calculate the difference

                                future_available_qty = available_qty
                                if qty_difference < 0:
                                    future_available_qty += -(qty_difference)
                                else:
                                    future_available_qty -= qty_difference

                                available_qty_mapping[sku['variant_id']] = available_qty
                                
                                product_data = {
                                    "id": sku['id'],
                                    "product_id": product_id,
                                    "variant_id": variant_id,
                                    "quantity": sku['quantity']
                                }
                                
                                if call_from_unit_price:
                                    product_data.update({
                                        "price_inc_tax": sku['price_inc_tax'],
                                        "price_ex_tax": sku['price_ex_tax']
                                    })
                                
                                req_body['products'].append(product_data)
                                
                                # Blocked quantity increased (new > old), so we display a negative value (red background)
                                if upcoming_qty < prev_blocked_qty:
                                    qty_difference = -(qty_difference)  # Make it negative
                                else:
                                    qty_difference = -qty_difference

                                if qty_difference != 0:
                                    log_array.append({
                                        'sku': item_sku,
                                        'qty_difference': qty_difference,
                                        'future_available_qty': future_available_qty
                                    })
                
                payload_for_min_max_rule = transform_order_products_data(order_products)
                affected_product_ids = check_min_max_rule_for_lineitems(store, payload_for_min_max_rule)
                res = call_bc_order_api(api, order_id, req_body)
                if affected_product_ids:
                    set_min_max_rule_for_lineitems_in_bc(store, affected_product_ids)


                if res and res.status_code == 200:
                    for log in log_array:
                        item_sku = log['sku']
                        qty_difference = log['qty_difference']
                        future_available_qty = log['future_available_qty']

                        update_log_table(store, order_id, username, item_sku, qty_difference, future_available_qty)
                else:
                    error_message = res.json()[0]['message']
                    return {'status': 400, 'message': error_message}

                if not call_from_unit_price:
                    blocked_orders_db.BlockedOrdersSchema.update_threshold_and_restock_qty(store,{
                        'order_id': order_id,
                        'restock_qty': restock_quantity,
                        'restock_threshold': restock_threshold
                    })        
                
                return {'status': 200, 'message': 'Order updated successfully'}

        # 3. While Deleting SKU ...
        if method == 'DELETE':
            if isinstance(payload, dict):
                payload = [payload]  # convert single SKU to list

            for item in payload:
                entity_id = item['id'],
                variant_id  = item['variant_id']
                product_id = item['product_id']
                if not call_from_unit_price:
                    blocked_quantity = item['blocked_quantity']
                sku = item['sku']

                req_data = {
                    "id": entity_id[0],
                    "variant_id": variant_id,
                    "product_id": product_id,
                    "quantity": 0
                }

                req_body['products'].append(req_data)
            
            affected_product_ids = []
            # order_products = bc_order.get_order_products(store, order_id)
            # order_products = order_products.json()
            payload_for_min_max_rule = transform_order_products_data(order_products)
            affected_product_ids = check_min_max_rule_for_lineitems(store, payload_for_min_max_rule)
            res = call_bc_order_api(api, order_id, req_body)
            if affected_product_ids:
                set_min_max_rule_for_lineitems_in_bc(store, affected_product_ids)

            if not call_from_unit_price:
                # check available quantity after removing sku from order ...
                graphql_query = products_variant_query.get_query([product_id], [variant_id])
                status, gql_res = bc.process_bc_graphql_request(store, graphql_query)
                
                available_qty = 0
                if status == 200:
                    for item in gql_res['data']['site']['products']['edges']:
                        available_qty = item['node']['variants']['edges'][0]['node']['inventory']['aggregated']['availableToSell']

                if res:
                    order_details = res.json()
                    log_status = update_log_table(store, order_details['id'], username, sku, blocked_quantity, available_qty)

                    if log_status['status'] == 200:
                        return {'status': 200, 'message': 'Order updated successfully'}
                    else:
                        return {'status': 400, 'message': 'SKU not found'}
            else:
                if res.json().get('id'):
                    order_res = bc_order.fetch_order(store, order_id)
                    if order_res.status_code < 299:
                        order_data = order_res.json()
                        new_total_including_tax = order_data['total_inc_tax']
                        query = text(f"""UPDATE orders SET total_including_tax = :total_including_tax, updated_by = :name WHERE order_id = :order_id""")
                        results = conn.execute(query.params(total_including_tax=new_total_including_tax, name=name, order_id=order_id))
                        conn.commit()
                    # # Only send notification for top-level calls, not recursive calls
                    # if not is_recursive_call:
                    #     task.send_task(task.GENERATE_ADMIN_APP_NOTIFICATION_TASK, (store['id'], "order_updated", order_id))
                    return {'status': 200, 'message': 'product removed successfully'}
                else:
                    return {'status': 400, 'message': res.json()[0].get('message')}
    except Exception as e:
        logger.error(traceback.format_exc())
        error_message = str(e)
        return {'status': 422, 'message': error_message}
    finally:
        # Only send notification for top-level calls, not recursive calls
        if not is_recursive_call:
            task.send_task(task.SEND_BULK_ORDER_UPDATED_NOTIFICATION_TASK, (store['id'], order_id))
            task.send_task(task.GENERATE_ADMIN_APP_NOTIFICATION_TASK, (store['id'], "order_updated", order_id))
        if conn:
            conn.close()
    
def get_blocked_order_logs(store, order_id, page, limit, search_term, sort_array):
    res = blocked_orders_db.get_all_logs(store, order_id, page, limit, search_term, sort_array)
    api = store_util.get_bc_api_creds(store)
    products = bc.get_bc_order_products_new(api,  order_id, store)

    for res_item in res['data']['data']:
        for product in products:
            if res_item['sku'] == product['sku']:
                res_item['product_name'] = product['name']
                res_item['product_image'] = product['images']
                break

    return res
    
def check_min_max_rule_for_lineitems(store, line_items):
    try:
        bc_api = store_util.get_bc_api_creds(store)
        affected_product_ids = []
        product_ids = [] 
        variant_ids = []
        products = []
        
        # Group line items by product_id and collect variant_ids
        product_variants = {}
        for item in line_items:
            product_id = item['product_id']
            variant_id = item['variant_id']
            if product_id not in product_variants:
                product_variants[product_id] = []
            if variant_id:
                product_variants[product_id].append(variant_id)

        # Process products in batches
        for product_id, v_ids in product_variants.items():
            if len(product_ids) < 50:
                product_ids.append(product_id)
                
                for variant_id in v_ids:
                    if variant_id:
                        variant_ids.append(variant_id)

            if len(product_ids) == 50 or len(variant_ids) == 250:
                graphql_query = products_variant_query.get_query(product_ids, variant_ids)
                gql_status, gql_res = bc.process_bc_graphql_request(store, graphql_query)
                if gql_status == 200:
                    for item in gql_res['data']['site']['products']['edges']:
                        products.append(item['node'])    
                
                product_ids = []
                variant_ids = []
        
        if len(product_ids) > 0 and len(variant_ids) > 0:
            graphql_query = products_variant_query.get_query(product_ids, variant_ids)
            gql_status, gql_res = bc.process_bc_graphql_request(store, graphql_query)
            if gql_status == 200:
                for item in gql_res['data']['site']['products']['edges']:
                    products.append(item['node'])   
        
        # process the product information if products found
        product_payload = []
        if products:
            for product in products:
                min_purchase_quantity = product['minPurchaseQuantity']
                max_purchase_quantity = product['maxPurchaseQuantity']
                if (max_purchase_quantity and int(max_purchase_quantity) > 0) or (min_purchase_quantity and int(min_purchase_quantity) > 0):
                    exists_product = new_mongodb.fetch_one_document_from_admin_collection(store['id'], 'order_edit_logs', {'product_id': product['entityId']})
                    if exists_product:
                        new_mongodb.delete_documents_from_admin_collection(store['id'], 'order_edit_logs', {'product_id': product['entityId']})
                    
                    product_logs = {
                        'product_id': product['entityId'],
                        'min_purchase_quantity': int(min_purchase_quantity) if min_purchase_quantity else 0,    
                        'max_purchase_quantity': int(max_purchase_quantity) if max_purchase_quantity else 0,
                        'sku': product['sku'],
                        'available_quantity': product['inventory']['aggregated']['availableToSell'],
                        'status': product['availabilityV2']['status'],
                        'variants': product['variants']['edges']
                    }
                    new_mongodb.insert_document_in_admin_collection(store['id'], 'order_edit_logs', product_logs)
                    product_payload.append({"id": int(product['entityId']), "order_quantity_minimum": 0, "order_quantity_maximum": 0})
                    affected_product_ids.append(product['entityId'])
        
        # reset the product information in bc if products found
        if len(product_payload) > 0:
            # If the length of product_payload is greater than 10, break it into sets of 10 products
            if len(product_payload) > 10:
                # Split the product_payload into chunks of 10
                for i in range(0, len(product_payload), 10):
                    product_chunk = product_payload[i:i+10]
                    
                    req_data = {
                        "query_params": {},
                        "method": "PUT",
                        "url": "v3/catalog/products",
                        "body": product_chunk
                    }
                    res = bc.process_api(bc_api, req_data)
            else:
                # If the length of product_payload is 10 or less, process it as is
                req_data = {
                    "query_params": {},
                    "method": "PUT",
                    "url": "v3/catalog/products",
                    "body": product_payload
                }
                res = bc.process_api(bc_api, req_data)

        return affected_product_ids
    except Exception as e:
        logger.exception("Exception in check_min_max_rule_for_lineitems")

def set_min_max_rule_for_lineitems_in_bc(store, product_ids):
    try:
        # db = new_mongodb.get_admin_db_client(store)
        bc_api = store_util.get_bc_api_creds(store)
        req_data = {
                "query_params": {},
                "method": "PUT",
                "url": "v3/catalog/products",
                "body": []
            }
        payload = []
        for product_id in product_ids:
            exists_product = new_mongodb.fetch_one_document_from_admin_collection(store['id'], 'order_edit_logs', {'product_id': product_id})
            if exists_product:
                min_purchase_quantity = exists_product['min_purchase_quantity']
                max_purchase_quantity = exists_product['max_purchase_quantity']
                # complex_rules = exists_product['complex_rules']
                if (max_purchase_quantity and int(max_purchase_quantity) > 0) or (min_purchase_quantity and int(min_purchase_quantity) > 0):
                   #update bulk products in bc
                    data = {
                        "id": int(product_id),
                        "order_quantity_minimum": int(min_purchase_quantity) if min_purchase_quantity else 0,
                        "order_quantity_maximum": int(max_purchase_quantity) if max_purchase_quantity else 0,
                    }
                    payload.append(data)
                    if len(payload) == 10:
                        req_data['body'] = payload
                        res = bc.process_api(bc_api, req_data)
                        if res and res['status_code'] == 200:
                            payload = []
                            req_data['body'] = []
                
                # if len(complex_rules) > 0:
                #     for rule in complex_rules:
                #         rule_id = rule.get("id")
                #         url =  f"v3/catalog/products/{product_id}/complex-rules/{rule_id}"
                #         req_body = {
                #             "prdocut_id": int(product_id),  
                #             "enabled": True
                #         }
                #         res = bc.call_api(bc_api, "PUT", url, {}, req_body)
        
        if len(payload) > 0:
            req_data['body'] = payload
            res = bc.process_api(bc_api, req_data)
        new_mongodb.delete_documents_from_admin_collection(store['id'], 'order_edit_logs', {'product_id': {'$in': product_ids}})

        return True
    except Exception as e:
        logger.exception("Exception in check_min_max_rule_for_lineitems")
        return False

def transform_order_products_data(order_products):
    """
    Transform order products data into a simplified payload format.
    
    Args:
        order_products (list): List of order product objects from BigCommerce
        
    Returns:
        list: List of transformed product objects with required fields
        Example: [{'product_id': 26684, 'variant_id': 215107, 'quantity': 15, 'price_inc_tax': 47, 'price_ex_tax': 47}]
    """
    transformed_products = []
    
    for product in order_products:
        if product.get('quantity', 0) > 0:  # Only include products with quantity > 0
            transformed_product = {
                'product_id': product.get('product_id'),
                'variant_id': product.get('variant_id'),
                'quantity': product.get('quantity', 0),
                'price_inc_tax': float(product.get('price_inc_tax', 0)),
                'price_ex_tax': float(product.get('price_ex_tax', 0))
            }
            transformed_products.append(transformed_product)
    
    return transformed_products
