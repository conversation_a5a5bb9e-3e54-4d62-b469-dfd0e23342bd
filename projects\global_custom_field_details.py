from sqlalchemy import text
import pg_db
from mongo_db import user_db
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from datetime import datetime
import logging
import traceback
from utils.common import convert_to_timestamp

logger = logging.getLogger()


def _fetch_custom_field_detail(conn, id):
    query = text (f"""SELECT * from {pg_db.agile_customfield_meta} where id = :id;""")

    query = query.params(id=id)
    result = conn.execute(query)
    data = []
    for row in result.fetchall():
        row_data = {
            'id': row[0],
            'name': row[1],
            'datatype': row[2],
            'is_multiselect_group': row[3],
            'group_values': row[4],
            'sort_id': row[5],
            'is_mandatory': row[6],
            'created_by': row[7],
            'updated_by': row[8],
            'created_at': convert_to_timestamp(row[9]),
            'updated_at': convert_to_timestamp(row[10])
        }
        data.append(row_data)
    return data

def get_custom_field_detail(id):
    response = {
        "status": 400 
    }
    conn = pg_db.get_connection()
    try:
        data = _fetch_custom_field_detail(conn, id)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response


def _update_custom_field_detail(conn, update_fields, id, username):
    try: 
        set_clauses = []
        params = {'updated_by': username, 'id': id}
        
        # Generate SET clause for each field in update_fields
        for field, value in update_fields.items():
            set_clause = f"{field} = :{field}"
            set_clauses.append(set_clause)
            params[field] = value
        
        set_clause = ", ".join(set_clauses)
    
        query = text(
            f"""UPDATE {pg_db.agile_customfield_meta}
                SET 
                    {set_clause},
                    updated_by = :updated_by,
                    updated_at = CURRENT_TIMESTAMP
                WHERE 
                    id = :id;"""
        )

        conn.execute(query, params)    
        return True
    except Exception as e:
        logger.error(traceback.format_exc())
        return False

    
def patch_global_custom_field(name, group_values, username, id):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        existing_field = _fetch_custom_field_detail(conn, id)
        if not existing_field:
            response['status'] = 404
            response['message'] = "Custom field not found."
            return response
       
        existing_field = existing_field[0]
        existing_field_datatype = list(existing_field.values())[2]

        update_fields = {}
        
        if (str(existing_field_datatype) != 'select' and str(existing_field_datatype) != 'multi select') and group_values:
            response['status'] = 400
            response['message'] = "Cannot provide group_values for a non-select datatype."
            return response

        # Update the field with provided values
        if name is not None:
            update_fields['name'] = name
        if group_values is not None:
            update_fields['group_values'] = group_values

        if name is not None:
            check_query = text(f"""SELECT COUNT(*) FROM {pg_db.agile_customfield_meta} WHERE LOWER(name) = LOWER(:name) AND id != :id AND status = 'active';""")
            check_result = conn.execute(check_query, {'name': name, 'id': id}).scalar()
            if check_result > 0:
                response['status'] = 409
                response['message'] = "name: Data updation failed, field with the same name already exists."
                return response

        # If both name and group_values are provided, update both fields
        if name is not None and group_values is not None:
            update_fields = {'name': name, 'group_values': group_values}
       
        
        data = _update_custom_field_detail(conn, update_fields, id, username)
        if data:
            response['status'] = 200
            response['message'] = "Data updated successfully."
        else:
            response['status'] = 409
            response['message'] = "name: Data updation failed, field already exists."
    except IntegrityError as e:
        logger.error(traceback.format_exc())
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 422
            response['message'] = "name: This field already exists."
        else:
            response['status'] = 500
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response
    

def _remove_custom_field(conn, id):
    dependency_query = text(
        f"""SELECT COUNT(pc.project_id) AS project_counts
            FROM {pg_db.agile_customfield_meta} AS meta
            LEFT JOIN {pg_db.agile_project_customfield} AS pc 
            ON meta.id = pc.customfield_meta_id
            WHERE meta.id = :id
        """
    )
    dependency_query = dependency_query.params(id=id)
    result = conn.execute(dependency_query)
    dependency_result = result.fetchone()
    project_counts = dependency_result[0]
   
    if project_counts > 0:
        return False
    
    delete_query = text(
        f"""DELETE FROM {pg_db.agile_customfield_meta}
            WHERE id = :id
        """
    )
    delete_query = delete_query.params(id=id)
    delete_result = conn.execute(delete_query)       
    
    return delete_result.rowcount > 0


def delete_field(id):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:
        if id:
            existing_field = _fetch_custom_field_detail(conn, id)
            if not existing_field:
                response['status'] = 404
                response['message'] = "Custom field not found."
                return response

            existing_field = existing_field[0]
            field_name = existing_field['name'] if existing_field and 'name' in existing_field else ''
            data = _remove_custom_field(conn, id)
            if data:
                response['status'] = 200
                response['message'] = "Data deleted successfully."
            else:
                response['status'] = 409
                response['message'] = f"The custom field {field_name} is use for a project, so you can't delete this field."
        else:
            response['status'] = 400
            response['message'] = "id is missing."
    finally:
        if conn:
            conn.commit()
            conn.close()

    return response