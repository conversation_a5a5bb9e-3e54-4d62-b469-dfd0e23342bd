from utils import bc, store_util
import plugin
import mongo_db
from mongo_db import customer_db
import logging
from pymongo import TEXT

logger = logging.getLogger()

def process_bc_customers(customers=[]):
    result = []
    for customer in customers:
        name = customer['first_name'] + " " + customer['last_name']
        obj = {
            "id": customer["id"],
            "email": customer["email"],
            "name": name,
            "phone": customer['phone'],
            "company": customer['company'],
            "notes": customer['notes'],
            "date_created": customer['date_created'],
            "search_field": customer['email'] + name + " " + customer['company'] + " " + str(customer['id']),
            "customer_group_id": customer["customer_group_id"]
        }
        result.append(obj)
    return result

def fetch_all_customers(store):
    query_params = {
        "limit": 250,
        "page": 0
    }
    api = "v3/customers"
    db = mongo_db.get_store_db_client(store)
    index = db[customer_db.CUSTOMERS_COLLECTION].create_index([('customer_search_field', TEXT)], default_language='english')
    return plugin.fetch_all_by_rest_api(store, api, limit_per_req=250, query_params=query_params, 
            db_collection=customer_db.CUSTOMERS_COLLECTION,db_process_threshold=250, max_resource_count=-1, resource_processor=process_bc_customers)

def fetch_all_customer_groups(store):
    query_params = {
        "limit": 250,
        "page": 0
    }
    api = "v2/customer_groups"
    return plugin.fetch_all_by_rest_api(store, api, limit_per_req=250, query_params=query_params, 
            db_collection=customer_db.CUSTOMER_GROUPS_COLLECTION, db_process_threshold=250)

def fetch_customer_by_id(store, customer_id):
    query_params = {
        "id:in": customer_id
    }
    url = "v3/customers"
    bc_api = store_util.get_bc_api_creds(store)
    return bc.call_api(bc_api, "GET", url, query_params)


def fetch_customer_addresses(store: object, customer_id: int, address_id: int = None):
    addresses = []
    api = "v3/customers/addresses"
    if address_id:
        query_params = {
            "customer_id:in": customer_id,
            "id:in": address_id
        }
    else:
        query_params = {
            "customer_id:in": customer_id
        }
    bc_api = store_util.get_bc_api_creds(store)
    res = bc.call_api(bc_api, "GET", api, query_params)
    if res and res.status_code == 200:
        addresses = res.json()['data']
    return addresses