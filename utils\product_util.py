from mongo_db import catalog_db, pricing_db
from utils import redis_util, customer_util,bc
import logging
import traceback
from new_mongodb import StoreDBCollections, count_documents_storefront_collection
from sqlalchemy import text

logger = logging.getLogger()

def _update_inventory_cache(store, product_id, variant_id, new_inventory):
    product = catalog_db.fetch_product_by_id(store, product_id)
    if product:
        inventory = {}
        if variant_id:
            for variant in product["variants"]:
                if variant_id == variant["id"]:
                    if variant["sku"]:
                        inventory[variant["sku"]] = new_inventory
                    break
        else:
            sku = product.get('sku', None)
            if sku:
                inventory[sku] = new_inventory
        if len(inventory) > 0:
            redis_util.update_sku_invetory_cache(store['id'], inventory)

def get_all_product_inventory(store):
    return catalog_db.fetch_all_product_inventory(store)

def get_price_for_skus(store, customer_id, sku_list):
    customer = customer_util.get_customer_by_id(store, customer_id)
    if customer:
        customer_group_id = customer["customer_group_id"]
        return pricing_db.fetch_customer_price_for_skus(store, customer_group_id, sku_list)
    return None

def get_product_by_id(store, product_id):
    product = catalog_db.fetch_product_by_id(store, product_id)
    return product

def calculate_bulk_price(bulk_rule=None, price=0):
    bulk_price = price
    if bulk_rule:
        if bulk_rule["type"] == "percent":
            bulk_price = price * (1 - bulk_rule["amount"]/100)
        elif bulk_rule["type"] == "price":
            bulk_price = price - bulk_rule["amount"]
        elif bulk_rule["type"] == "fixed":
            bulk_price = bulk_rule["amount"]
    
    return bulk_price

def get_price_single_product(store, customer_id, product, total_quantity=0, sku_list=[]):
    sku_pricing = {}
    if sku_list and len(sku_list) > 0:
        bulk_rule = None
        if len(product["bulk_pricing_rules"]) > 0 and total_quantity > 0:
            for rule in product["bulk_pricing_rules"]:
                if total_quantity >= rule['quantity_min'] and (rule['quantity_max'] == 0 or total_quantity <= rule['quantity_max']):
                    bulk_rule = rule
                    break

        price_list = get_price_for_skus(store, customer_id, sku_list)
        variants = {}
        variants[product['sku']] = product
        for variant in product['variants']:
            variants[variant['sku']] = variant

        for sku in sku_list:
            price = variant["price"] or variant["calculated_price"]

            price_obj = {
                "original_price": price,
                "list_price": price,
                "sale_price": price
            }
            sku_pricing[sku] = price_obj

            final_price = price
            bulk_price = calculate_bulk_price(bulk_rule, price)
            price_list_price = 0

            sku_price_list = None
            if price_list:
                sku_price_list = price_list.get(sku, None)

            if sku_price_list:
                price_list_price = sku_price_list["price"] or sku_price_list["calculated_price"]

            if price_list_price > 0 and final_price > price_list_price:
                final_price = price_list_price

            if bulk_price > 0 and final_price > bulk_price:
                final_price = bulk_price

            price_obj['list_price'] = final_price
            price_obj['sale_price'] = final_price

    return sku_pricing

def get_product_price(store, customer_group_id, product_list=[]):
    customer = customer_util.get_customer_by_id(store, customer_id)
    if customer_group_id:
        return pricing_db.fetch_customer_price_for_skus(store, customer_group_id, sku_list)
    return None

def get_listing_products(store, customer_id=None, listing_type="all"):
    products = None
    if listing_type == "all":
        products = redis_util.get_home_products(store['id'])
    elif listing_type == "new":
        products = redis_util.get_new_products(store['id'])
        products = {"new": products}
    elif listing_type == "featured":
        products = redis_util.get_featured_products(store['id'])
        products = {"featured": products}
    elif listing_type == "popular":
        products = redis_util.get_popular_products(store['id'])
        products = {"popular": products}

    if customer_id and products:
        customer = customer_util.get_customer_by_id(store, customer_id)
        if customer:
            pricing = redis_util.get_product_list_pricing(store['id'], customer['customer_group_id'])
            if pricing:
                if "new" in products:
                    for p in products["new"]:
                        if str(p["id"]) in pricing:
                            p['sale_price'] = pricing[str(p["id"])]
                if "featured" in products:
                    for p in products["featured"]:
                        if p["id"] in pricing:
                            p['sale_price'] = pricing[p["id"]]
                if "popular" in products:
                    for p in products["popular"]:
                        if p["id"] in pricing:
                            p['sale_price'] = pricing[p["id"]]
    
    return products

################################################################################################
#  Bigcommerce Webhook Handler
################################################################################################
# update_bc_product_invetory_cache: Update inventory cache for a list of the given bc products
# Calling From: plugin.product_util
def update_bc_product_invetory_cache(bc_products=[]):
    inventory = {}
    for product in bc_products:
        sku = product.get("sku", None)
        if sku:
            inventory[sku] = product["inventory_level"]
        variants = product.get("variants", [])
        for variant in variants:
            sku = variant.get("sku", None)
            if sku:
                inventory[sku] = variant["inventory_level"]

    if len(inventory) > 0:
        redis_util.update_sku_invetory_cache(store['id'], inventory)
        
    return inventory
        
# handle_product_inventory_webhook: BC product inventory change webhook handler
# Calling From: utils.webhook_util
def handle_product_inventory_webhook(store, payload):
    ret = False
    try:
        if store:
            data = payload['data']['inventory']
            product_id = data['product_id']
            new_inventory = data['value']
            ret = catalog_db.update_product_inventory(store, product_id, new_inventory)
            if ret:
                _update_inventory_cache(store, product_id, None, new_inventory)
    except Exception as e:
        logger.error(traceback.format_exc())
    return ret

# handle_sku_inventory_webhook: BC sku inventory change webhook handler
# Calling From: utils.webhook_util
def handle_sku_inventory_webhook(store, payload):
    try:
        if store:
            data = payload['data']['inventory']
            product_id = data['product_id']
            variant_id = data['variant_id']
            new_inventory = data['value']
            catalog_db.update_sku_inventory(store, product_id, variant_id, new_inventory)
            _update_inventory_cache(store, product_id, variant_id, new_inventory)
    except Exception as e:
        logger.error(traceback.format_exc())

# handle_sku_update_webhook: BC sku update webhook handler
# Calling From: utils.webhook_util
def handle_sku_update_webhook(store, payload):
    ret = False
    try:
        if store:
            data = payload['data']
            product_id = data['sku']['product_id']
            redis_util.queue_webhook_product_update(store["id"], product_id, payload)
            ret = True
    except Exception as e:
        logger.error(traceback.format_exc())
    return ret

# handle_product_update_webhook: BC product update webhook handler
# Calling From: utils.webhook_util
def handle_product_update_webhook(store, payload):
    ret = False
    try:
        if store:
            data = payload['data']
            product_id = data['id']
            redis_util.queue_webhook_product_update(store["id"], product_id, payload)
            ret = True
    except Exception as e:
        logger.error(traceback.format_exc())
    return ret

# handle_product_delete_webhook: BC product delete webhook handler
# Calling From: utils.webhook_util
def handle_product_delete_webhook(store, payload):
    if store:
        data = payload['data']
        product_id = data['id']
        catalog_db.remove_product_by_id(store, product_id)      

# call batch product update api.
def change_products_visibility(api_data, product_ids, visible):
    return bc.update_products_visibility(api_data, product_ids, visible)

def get_products_count_for_category(conn, category_id):
  total_records = 0
  try:
      query =text(
            f"SELECT COUNT(product_id) FROM product_categories where category_id= :category_id"
        )
      total_records = conn.execute(query.params(category_id=category_id)).scalar()
  except Exception as e:
    logger.error(str(traceback.format_exc()))
    return 0
  return total_records

def get_product_count_for_category_using_mongo(store, category_id):
    try:
        # Get all store DB clients since we need to check across all stores
        total_count = 0
        
        # Count products in each store's database that have this category_id
        query = {"categories": category_id}
        total_count = count_documents_storefront_collection(store['id'], StoreDBCollections.PRODUCTS, query)
        return total_count
    except Exception as e:
        logger.error(str(traceback.format_exc()))
        return 0


