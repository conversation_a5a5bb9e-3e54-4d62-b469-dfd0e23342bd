from sqlalchemy import text
import pg_db
from mongo_db import user_db
from datetime import datetime
from utils import common
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import logging
import traceback

logger = logging.getLogger()

def _fetch_business_units(conn):
    data = [] 
    query = text(
        f"""SELECT id, name 
            FROM {pg_db.business_units};
        """
    )
    result = conn.execute(query)     
    for row in result.fetchall():
        business_unit_data = {
            'id': row[0],
            'business_unit_name': row[1]
        }
        data.append(business_unit_data)
    return data


def get_business_unit():
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        data = _fetch_business_units(conn)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 404
            response['message'] = 'No data found.'
    finally:
        if conn:
            conn.close()
    return response

def _insert_business_unit(conn, name, username):
    query = text(
        f"""INSERT INTO {pg_db.business_units} (name, created_by, updated_by, updated_at)
            VALUES (:name, :created_by, :created_by, CURRENT_TIMESTAMP);
        """
    )
    query = query.params(name=name, created_by=username)
    conn.execute(query)

    return True

def post_business_unit(name, username):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        data = _insert_business_unit(conn, name, username)
        if data:
            response['status'] = 200
            response['message'] = "Data inserted successfully."
        else:
            response['status'] = 400
            response['message'] = "Data insertion failed."
    except IntegrityError as e:
        logger.error(traceback.format_exc())
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 409
            response['message'] = "Duplicate key violation: This module already exists in the rules."
        else:
            response['status'] = 500
            response['message'] = "Something went wrong"
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response