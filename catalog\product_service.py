from bson import ObjectId
from fields.products import products_list_fields
from new_mongodb import StoreAdminDBCollections, store_catalog_db, task_db, store_info_db, StoreDBCollections
from new_mongodb import fetchall_documents_from_storefront_collection, fetch_one_document_from_admin_collection, process_documents
from new_mongodb import count_documents_storefront_collection, update_document_in_admin_collection, get_store_db_client_for_store_id
import new_utils
from pymongo.collation import Collation
from plugin import bc_products
import new_pgdb
import csv
import io
from sqlalchemy import text
import logging
import traceback
from utils.common import convert_to_timestamp

logger = logging.getLogger()

def get_products(store, payload):
    payload['filterBy'] = ['name','sku']
    store_id = store['id']
    db_client = get_store_db_client_for_store_id(store_id)
    products, total_data_length, page, limit = new_utils.get_paginated_records_updated(db_client=db_client, \
                                                collection_name=StoreDBCollections.PRODUCTS, payload=payload, \
                                                fields=products_list_fields, additional_query='')
    
    for product in products:
        category_ids = product.get('categories', [])
        category_names = store_catalog_db.get_categories_by_id(store['id'], category_ids)  # Function to retrieve category names
        product['category_names'] = category_names
    
    # include pagination data in response ...
    task_data= task_db.fetch_task_by_id(store['id'], "update_product_card_cache")
    data = new_utils.calculate_pagination(products, page, limit, total_data_length)
    last_sync_time = ""
    if task_data:
        last_sync_time = task_data['store_run_status'][store['id']]['last_run_end_time']
    data['meta']['last_sync_time'] = last_sync_time
    return data

def get_product(store, product_id):
    res = bc_products.fetch_bc_product(store, product_id)
    variants = res['data']['variants'] if 'variants' in res['data'] else []
    response, meta_dict = bc_products.get_customer_group_pricing(store, product_id, variants)

    res['data']['customer_group_pricing'] = response

    res['meta'] = meta_dict
            
    # res = super().find_one({"id": int(product_id)})  

    res['data']['is_featured_product'] = False    
    res['data']['is_new_product'] = False  
    res['data']['is_pre_order_product'] = False

    product_listing_types = store_info_db.fetch_all_product_listing_types(store['id'])    
    
    for product_type in product_listing_types:
        if int(product_id) in product_type['product_ids']:
            if product_type['type'] == 'featured_products':
                res['data']['is_featured_product'] = True
            elif product_type['type'] == 'new_products':
                res['data']['is_new_product'] = True
            elif product_type['type'] == 'preorder_products':
                res['data']['is_pre_order_product'] = True
    return new_utils.parse_json(res)

def update_product(store, req_body, product_id):
    res = bc_products.update_bc_product(store, req_body, product_id)   
    update_response = bc_products.update_bc_customer_group_price(store, req_body)
    if update_response == ({'data': {}, 'meta': {}}, 200) or update_response[1] == 204:
        data = res[0]
        if isinstance(data, dict):
            data["group_pricing_message"] = "Customer Group pricing updated"
    else:
        data = res[0]
        data["group_pricing_message"] = update_response
    
    return res

def get_products_by_id(store, product_ids, params, fields):
    params['filterBy'] = ['name','sku']
    sort = {
            'sort_by': params.get('sort_by', None) or 'date_created'
        }
    sort_order = params.get('sort_order', 'desc')
    if sort_order == 'asc':
        sort['sort_order'] = 1
    else:
        sort['sort_order'] = -1

    limit = int(params["limit"]) if params.__contains__("limit") else 10
    page = int(params["page"]) if params.__contains__("page") else 1
    skips = params['skips'] if params.__contains__('skips') else 0

    id_query = {'id': {'$in': product_ids}}

    filterBy = params.get("filterBy", [])
    filter = params.get('filter', None)
    query = {}
    if len(filterBy) > 0 and filter:
        query = {
                "$or": [],
            }
        for i in filterBy:
            query['$or'].append({i: {"$regex": filter, "$options": "i"}},)
    
    query = {'$and': [query, id_query]} if query else id_query
    
    skips = limit * (page - 1)
    collation = Collation(locale='en', strength=2)

    data = fetchall_documents_from_storefront_collection(store['id'], StoreDBCollections.PRODUCTS, query=query, projection=fields).collation(collation).sort(sort['sort_by'], sort['sort_order']).skip(skips).limit(limit)
    
    data = process_documents(data)

    document_length = count_documents_storefront_collection(store['id'], StoreDBCollections.PRODUCTS, query)

    return new_utils.parse_json(data), document_length, page, limit

def get_products_list(store, params):
    data = None
    type = params.get('status', '')       
    if type:
        products_data = fetch_one_document_from_admin_collection(store['id'], StoreAdminDBCollections.STORE_INFO_COLLECTION, \
                                                                 {'type': type})
        product_ids = products_data['product_ids']
        products, total_data_length, page, limit = get_products_by_id(store, product_ids, params, products_list_fields)

        for product in products:
            category_ids = product.get('categories', [])
            category_names = store_catalog_db.get_categories_by_id(store['id'], category_ids)  # Function to retrieve category names
            product['category_names'] = category_names
        
        data = new_utils.calculate_pagination(products, page, limit, total_data_length)
    return data   

def update_product_listing(store, body):
    response = {
        'status': 400,
        'message': 'Data is not saved successfully'
    }
    check = body['checked']
    productType = body['type'].strip()
    productID = body['product_id']
    product = fetch_one_document_from_admin_collection(store['id'], StoreAdminDBCollections.STORE_INFO_COLLECTION, \
                                                                 {'type': productType})
    if product:
        finalproducts = product['product_ids']
        finalproduct = []
                
        if check == True:
            finalproduct=finalproducts
            finalproduct.append(productID)
        else:
            for p_id in finalproducts:
                if p_id != productID:
                    finalproduct.append(p_id)        

        query = {"_id": ObjectId(str(product['id']))}
        update_data = {
                        "$set":{  
                            "product_ids": finalproduct
                        }
                    }
        id = update_document_in_admin_collection(store['id'], StoreAdminDBCollections.STORE_INFO_COLLECTION, \
                                                 query=query, update_data=update_data)                        

        response['message'] = "Data saved successfully"
        response['status'] = 200
    else:
        response['message'] = "Please ensure that the given type is correct."
        response['status'] = 404

    return response

def update_product_with_csv(store, req_body):
    response = {
        "status": 400
    }
    
    conn = new_pgdb.get_connection(store['id'])
    try:
        # Extracting file name from request body
        file_name = req_body.filename
        
        uploaded_file = req_body.read()

        # Validate CSV data
        required_fields = ['price_list_id', 'sku', 'price']
        missing_fields = []
        empty_fields = []

        with io.StringIO(uploaded_file.decode('utf-8')) as file:
            reader = csv.DictReader(file)

            for field in required_fields:
                if field not in reader.fieldnames:
                    missing_fields.append(field)
            
            for row in reader:
                for field in required_fields:
                    if not row.get(field):
                        empty_fields.append(field)

        if missing_fields:
            status = "Failed"
            message = f"Missing fields in CSV data: {', '.join(missing_fields)}"
            query = text("INSERT INTO csv_upload_details (file_name, date, status) VALUES (:file_name, CURRENT_TIMESTAMP, :status)")
            query = query.params(file_name=file_name, status=status)
            conn.execute(query)
            conn.commit()
            response['status'] = status
            response['message'] = message
            return response

        if empty_fields:
            status = "Failed"
            message = f"Empty fields in CSV data: {', '.join(empty_fields)}"
            query = text("INSERT INTO csv_upload_details (file_name, date, status) VALUES (:file_name, CURRENT_TIMESTAMP, :status)")
            query = query.params(file_name=file_name, status=status)
            conn.execute(query)
            conn.commit()
            response['status'] = status
            response['message'] = message
            return response

        # Process CSV data
        customer_group_pricing = []
        with io.StringIO(uploaded_file.decode('utf-8')) as file:
            reader = csv.DictReader(file)
            for row in reader:
                customer_group_pricing.append({
                    "price_list_id": int(row['price_list_id']),
                    "sku": row['sku'],
                    "currency": "usd",
                    "price": float(row['price'])
                })

        formatted_data = {"customer_group_pricing": customer_group_pricing}

        # Update BigCommerce customer group pricing
        update_response = bc_products.update_bc_customer_group_price(store, formatted_data)

        status = "Completed" if update_response == ({'data': {}, 'meta': {}}, 200) else "Failed"

        # Insert into CSV upload details table
        query = text("INSERT INTO csv_upload_details (file_name, date, status) VALUES (:file_name, CURRENT_TIMESTAMP, :status)")
        query = query.params(file_name=file_name, status=status)
        conn.execute(query)
        conn.commit()

        if status == "Completed":
            response['status'] = 200
            response['message'] = "Customer group pricing updated"
        else:
            response['status'] = 500
            response['message'] = "Customer group pricing updation failed"
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response


def get_csv_upload_details(store, page, limit, sort_array=[]):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        total_count_query = text("SELECT COUNT(*) FROM csv_upload_details;")
        result_count = conn.execute(total_count_query)
        total_count = int(result_count.scalar())

        query = "SELECT * FROM csv_upload_details"

        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            if sort_array[0] in ["date"]:
                query += f" ORDER BY {sort_array[0]} {sort_direction}"

        offset = (page - 1) * limit
        query += f" LIMIT {limit} OFFSET {offset}"

        result = conn.execute(text(query))
        file_attributes = []
        for row in result.fetchall():
            file_details = {
                'id': row[0],
                'file_name': row[1],
                'date': convert_to_timestamp(row[2]),
                'status': row[3]
            }
            file_attributes.append(file_details)

        data = new_utils.calculate_pagination(file_attributes, page, limit, total_count)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 404
            response['message'] = 'No data found.'
    finally:
        if conn:
            conn.close()
    return response


def get_tax_classes(store):
    response = {
        "status": 400
    }
    tax_classes = bc_products.fetch_bc_tax_classes(store)
    if tax_classes:
        response['status'] = 200
        response['data'] = new_utils.parse_json(tax_classes)
    else:
        response['status'] = 404
        response['message'] = "data not found"
    return response