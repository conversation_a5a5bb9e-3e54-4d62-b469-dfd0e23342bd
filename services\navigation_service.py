from services import Service
from bson import ObjectId
from datetime import datetime


class Navigations(Service):
    def create_nav(self, body):
        body["created_at"] = int(datetime.utcnow().timestamp())
        body["updated_at"] = ""
        body['short_code'] = body['name'][:2] + "-" + \
            str(round(datetime.utcnow().timestamp()))

        id = super().create(body)
        return id, body['name'], body['short_code']

    def update_nav(self, body, navigation_id=None):
        id = super().update_one({"_id": ObjectId(str(navigation_id))}, {"$set":
                                                                        {
                                                                            "name": body['name'],
                                                                            "description": body['description'],
                                                                            "status": body['status'],
                                                                            "updated_at":  int(datetime.utcnow().timestamp())
                                                                        }
                                                                        })
        return id

    def get_all_navigations(self):
        navigations = super().find_all()
        res = super().processList(navigations)

        # for nav in res:
        #     nav['created_at'] = datetime.utcfromtimestamp(
        #         nav['created_at']).strftime('%m/%Y, %H:%M%p')
        #     nav['updated_at'] = datetime.utcfromtimestamp(nav['updated_at']).strftime(
        #         '%m/%Y, %H:%M%p') if nav['updated_at'] != "" else None

        return res
