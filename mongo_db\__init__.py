from pymongo import MongoClient
import logging
import datetime
from bson import ObjectId
import appconfig
import json
from utils import store_util

logger = logging.getLogger()

class DBClientPool:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            logger.info('Creating the DBClientPool')
            cls._instance = super(DBClientPool, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        logger.info("Entering DBClientPool")
        self.connection_pool = {}
        self.get_admin_db_client()
        logger.info("Exiting DBClientPool")

    def get_client(self):
        return MongoClient(appconfig.get_mongodb_connection_str())

    def get_db_client(self, db_name):
        if not db_name in self.connection_pool:
            self.connection_pool[db_name] = self.get_client()[db_name]
        return self.connection_pool[db_name]
    
    def get_store_db_client(self, store):
        if store and store.get("db", None):
            return self.get_db_client(store.get("db"))
        return None
    
    def get_admin_db_client(self):
        return self.get_db_client(appconfig.get_admin_db_name())
    
    def drop_db(self,db_name):
        return self.get_client().drop_database(db_name)

db_client_pool = DBClientPool()

def get_db_client(db_name):
    return db_client_pool.get_db_client(db_name)

def get_store_db_client(store):
    return db_client_pool.get_store_db_client(store)

def get_admin_db_client():
    return db_client_pool.get_admin_db_client()

def process_data(data):
    if '_id' in data:
        data['id'] = str(data['_id'])
        del data['_id']

    for key, value in data.items():
        if isinstance(value, ObjectId):
            data[key] = str(value)
    return data

def processList(data):
    result = []
    if data:
        for _obj in data:
            result.append(process_data(_obj))
    return result
    
def upsert_documents(store, collection, documents):
    db = get_db_client(store_util.get_bc_db(store))
    for document in documents:
        result = db[collection].replace_one({"_id": document['_id']},document,upsert=True)

def upsertMany(db_name, collection, documents):
    db = get_db_client(db_name)
    return db[collection].update_many({},documents, upsert=True)

def update_one(id,  documents):
    db = get_db_client('admin')
    del documents['id']
    return db['roles'].update_many({"_id": ObjectId(id)}, {'$set': documents}, upsert=True)

def drop_db(db_name):
    db_client_pool.drop_db(db_name)

def update_all(db_name, collection, documents):
    db = get_db_client(db_name)
    return db[collection].update_many({}, documents)


def _insert_one(store, collection, data):
    data['store_id'] = store['id']
    data['created_at'] = int(datetime.datetime.utcnow().timestamp())

    res = collection.insert_one(data)
    return res

def collection_check():
    logger.info('In Check Collections...')

    # db = get_admin_db_client()
    
    # stores_instance = db['store'].find({"status": "active"})
    # all_active_stores = processList(stores_instance)

    
    
    # if 'permissions' not in db.list_collection_names():
    #     db.create_collection('permissions')
    
    #permissions_collection = db['permissions']

    # if 'roles' not in db.list_collection_names():
    #   db.create_collection('roles')

    #role_collection = db['roles']

    # for store in all_active_stores:
    #     with open('./config/default_permissions.json') as p:
    #         data = json.load(p)
    #         permission_data = data.copy()
    #         res = _insert_one(store, permissions_collection, permission_data) 

    #         if res:
    #             with open('./config/default_role.json') as r:
    #                 role_data = json.load(r)
    #                 record_data = role_data.copy()
    #                 _insert_one(store, role_collection, record_data)

    # if 'app' not in db.list_collection_names():
    #     for store in all_active_stores:
    #         db.create_collection('app')
    #         app_collection = db['app']
            
    #         with open('./config/app.json') as app:
    #             app_data = json.load(app)
    #             _insert_one(store, app_collection, app_data)

    logger.info('Exit Check Collections...')
