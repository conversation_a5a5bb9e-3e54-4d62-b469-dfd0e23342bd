from new_mongodb import store_admin_db, AdminAppNotification, ProjectNotification
from projects import check_project_access
from sqlalchemy import text
import pg_db
from mongo_db import user_db
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from datetime import datetime, timedelta, timezone
from projects.project_cards import parse_time, update_card_hierarchy
import task
from utils import email_util, common
import logging
import traceback
from utils.common import convert_timedelta_to_hhmmss, parse_time_format, calculatePaginationData, convert_to_timestamp, convert_time_format
import os
import cv2
from PIL import Image
from io import BytesIO

logger = logging.getLogger()  

def get_card_details(project_id, card_id, store, username):
    response = {
        'status': 400
    }
    conn = pg_db.get_connection()
    try:            
        project_access = check_project_access(project_id, username) 
        if not project_access:
            response['status'] = 403
            response['message'] = "You don't have access for this project ticket."
            return response       
        query = text(
            f"""SELECT pc.id, pc.project_id, pc.module_id, pc.current_column_id, pc.status, 
                    pc.priority, pc.title, pc.card_identifier, pc.description, pc.assigned_to, pc.spent_time, pc.estimation, pc.due_date, pc.is_archived, pc.created_by, pc.updated_by, pc.created_at, pc.updated_at,
                    pm.name AS module_name, bc.name AS column_name, cs.label as status_label, cp.label as priority_label, pc.start_date, pc.parent_card_id
                FROM agile_project_cards pc
                JOIN agile_project_modules pm ON pc.module_id = pm.id
                JOIN agile_project_columns bc ON pc.current_column_id = bc.id
                LEFT JOIN agile_card_status cs ON pc.status = cs.id
                LEFT JOIN agile_card_priorities cp ON pc.priority = cp.id
                WHERE pc.id = :card_id and pc.project_id = :project_id"""
        )
        query = query.bindparams(card_id=card_id, project_id=project_id)
        result = conn.execute(query)        
        row_data = result.fetchone() 
        
        data = []    
        if row_data:
            if row_data[14] is not None:
                user = store_admin_db.fetch_user_by_username(store['id'], row_data[14])
                update_user = store_admin_db.fetch_user_by_username(store['id'], row_data[15])
            else:
                user = None  
                update_user = None          
            card = {}
            card['tickedID'] = row_data[0]
            card['projectID'] = row_data[1]
            card['moduleID'] = row_data[2]
            card['moduleName'] = row_data[18]
            card['columnID'] = row_data[3]
            card['columnName'] = row_data[19]
            card['status'] = row_data[4]
            card['statusLabel'] = row_data[20]
            card['priority'] = row_data[5]
            card['priorityLabel'] = row_data[21]
            card['title'] = row_data[6]
            card['card_identifier'] = row_data[7]

            card['description'] = row_data[8]
            card['assignee'] = row_data[9]
            card['spent_time'] = convert_time_format(row_data[10])
            card['estimation'] = convert_time_format(row_data[11])
            card['due_date'] = convert_to_timestamp(row_data[12])                     
            card['is_archived'] = row_data[13]
            card['created_by'] = row_data[14]
            card['created_at'] = convert_to_timestamp(row_data[16])
            card['updated_by'] = row_data[15]
            card['updated_at'] = convert_to_timestamp(row_data[17])
            card['start_date'] = convert_to_timestamp(row_data[22])
            card['ticket_creator'] = user['name'] if user else ''    
            card['ticket_updater'] = update_user['name'] if update_user else None
            card['child_cards'] = []
            card['parent_cards'] = []
            data.append(card)   
            related_cards = fetch_parent_and_child_cards_by_card_id(project_id, card_id, row_data[23])
            if related_cards['status'] == 200:
                data[0]['child_cards'] = related_cards['data']['child_cards']
                data[0]['parent_cards'] = related_cards['data']['parent_cards']
            response['status'] = 200
            response['data'] = data 
        else:
            response['status'] = 200
            response['data'] = []
    finally:        
        conn.close()
    return response


def _get_card_details(conn, project_id, card_id):
    query = text(
            f"""SELECT pc.id, pc.project_id, pc.module_id, pc.current_column_id, pc.status, 
                    pc.priority, pc.title, pc.card_identifier, pc.description, pc.assigned_to, pc.spent_time, pc.estimation, pc.due_date, pc.is_archived, pc.created_by, pc.updated_by, pc.created_at, pc.updated_at,
                    pc.sort_id, pc.pipeline_record_id, pc.table_name, pc.start_date, pc.parent_card_id                   
                FROM agile_project_cards pc
                WHERE pc.id = :card_id and pc.project_id = :project_id"""
        )
    query = query.bindparams(card_id=card_id, project_id=project_id)
    result = conn.execute(query)        
    row_data = result.fetchone() 
    card = {}
    if row_data:        
        card['id'] = row_data[0]
        card['project_id'] = row_data[1]
        card['module_id'] = row_data[2]
        card['current_column_id'] = row_data[3]
        card['status'] = row_data[4]
        card['priority'] = row_data[5]
        card['title'] = row_data[6]
        card['card_identifier'] = row_data[7]
        card['description'] = row_data[8]
        card['assigned_to'] = row_data[9]
        card['spent_time'] = convert_time_format(row_data[10])
        card['estimation'] = convert_time_format(row_data[11])
        card['due_date'] = convert_to_timestamp(row_data[12])                    
        card['is_archived'] = row_data[13]
        card['created_by'] = row_data[14]
        card['updated_by'] = row_data[15]
        card['created_at'] = row_data[16]
        card['updated_at'] = row_data[17]  
        card['sort_id'] = row_data[18]
        card['pipeline_record_id'] = row_data[19]
        card['table_name'] = row_data[20]
        card['start_date'] = convert_to_timestamp(row_data[21])
        card['parent_card_id'] = row_data[22]
    return card

def convert_hours_to_format(hours):
    total_minutes = int(hours * 60)  # Convert hours to minutes
    weeks, remainder = divmod(total_minutes, 5 * 8 * 60)  # 5 workdays * 8 hours * 60 minutes
    days, remainder = divmod(remainder, 8 * 60)  # 8 work hours * 60 minutes
    hours, minutes = divmod(remainder, 60)

    time_str = ""
    if weeks > 0:
        time_str += f"{weeks}w "
    if days > 0:
        time_str += f"{days}d "
    if hours > 0:
        time_str += f"{hours}h "
    if minutes > 0:
        time_str += f"{minutes}m "
    return time_str.rstrip() if time_str else "0m"


def modify_card_detail(conn, update_fields, project_id, id, username):
    set_clauses = []
    for field, value in update_fields.items():
        if field == 'spent_time' or field == 'estimation':
            set_clauses.append(f"{field} = {field} + INTERVAL '{value}'")
        else:
            set_clauses.append(f"{field} = :{field}")

    set_clause = ", ".join(set_clauses)
    row_count = 0
    if not set_clause:
        return 1
    query = text(
        f"""UPDATE agile_project_cards
            SET 
                {set_clause},
                updated_by = :updated_by,
                updated_at = :updated_at
            WHERE 
                id = :id
                AND project_id = :project_id;"""
    )        
    params = update_fields.copy()
    params.update({'updated_by': username, 'id': id, 'project_id': project_id, 'updated_at': datetime.utcnow()})            
    result = conn.execute(query, params)
    row_count = result.rowcount
    return row_count > 0


def update_card(payload, username, project_id, id, store):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        if not id or not project_id:
            response['status'] = 500
            response['message'] = "Invalid payload, 'id' or 'project Id'."
            return response
        
        existing_card = _get_card_details(conn, project_id, id)       
        if not existing_card:
            response['status'] = 404
            response['message'] = "Card not found."
            return response
 
        child_card_id = payload.get('child_card_id', None)

        if child_card_id:
                message, flag = update_card_hierarchy(conn, id, child_card_id)

        log_payload = []
        update_fields = {}
        for field in ['module_id', 'is_archived', 'due_date', 'estimation', 'spent_time', 'assigned_to', 'description', 'title', 'priority', 'status', 'current_column_id', 'start_date', 'parent_card_id']:
            if field in payload:
                log_payload.append((field, payload[field]))
                if field == 'estimation':
                    conn.execute(text
                                 (f"""UPDATE agile_project_cards
                                    SET 
                                        estimation = '0 hours'
                                    WHERE 
                                        id = {id}
                                        AND project_id = {project_id};""")
                                )
                    conn.commit()
                    
                    update_fields[field] = str(parse_time(payload[field])) + " hours"
                elif field == 'spent_time':
                    update_fields[field] = str(parse_time(payload[field])) + " hours"
                else:
                    update_fields[field] = payload[field]

        # Update ticket sort order if ticket_ids are provided
        if 'ticket_ids' in payload:
            ticket_ids = payload['ticket_ids']
            if ticket_ids:
                for index, ticket_id in enumerate(ticket_ids, start=1):
                    conn.execute(text(
                        f"""UPDATE agile_project_cards
                            SET sort_id = :sort_id
                            WHERE id = :ticket_id
                            AND project_id = :project_id;"""
                    ), {'sort_id': index, 'ticket_id': ticket_id, 'project_id': project_id})
                conn.commit()
            # Remove ticket_ids from payload after updating sort_id
            del payload['ticket_ids']
        
        # add logs for the updated changes 
        log_payload_array = []
        if log_payload:
            for field, value in log_payload:
                if field != 'description':
                    old_value = existing_card[field]
                    spent_time_diff = None
                    log_description = None
                    spent_time_created_at = None
                    if field in ['spent_time']:
                        new_time = parse_time(payload[field])
                        old_time = parse_time(existing_card[field])
                        total_value = new_time + old_time
                        value = convert_hours_to_format(total_value)
                        spent_time_diff = str(parse_time(payload[field])) + " hours"
                        log_description = payload['log_description'] if 'log_description' in payload else None
                        spent_time_created_at = payload['spent_time_created_at'] if 'spent_time_created_at' in payload else None
                    # elif field == 'description':
                    #     old_value = ''
                    #     value = ''
                    data = {}
                    data['field_name'] = field
                    data['old_value'] = old_value
                    data['new_value'] = value
                    data['created_by'] = username
                    data['ticket_id'] = id
                    data['spent_time_diff'] = spent_time_diff 
                    data['log_description'] = log_description
                    data['spent_time_created_at'] = spent_time_created_at
                    log_payload_array.append(data)

        if log_payload_array:
            add_card_history_log(log_payload_array)

        data = modify_card_detail(conn, update_fields, project_id, id, username)

        if 'assigned_to' in payload:
            task.send_task(task.GENERATE_ADMIN_APP_NOTIFICATION_TASK, args=(store['id'], AdminAppNotification.TICKET_ASSIGNED, id))
            task.send_task(task.SEND_PROJECT_NOTIFICATION_TASK, args=(store['id'], ProjectNotification.TICKET_ASSIGNED, id))
        
        if data:
            response['status'] = 200
            response['message'] = "Card updated successfully."

            task.send_task(task.GENERATE_ADMIN_APP_NOTIFICATION_TASK, args=(store['id'], AdminAppNotification.TICKET_UPDATED, id))
            
            if 'current_column_id' in payload:
                data = {}
                data['project_id'] = project_id
                data['ticket_id'] = id
                data['current_column_id'] = payload['current_column_id']
                data['updated_by'] = username
            
                task.send_task(task.UPDATE_RESOURCES_TASK, (store['id'], data))
        else:
            response['status'] = 400
            response['message'] = "Card updation failed."

    except IntegrityError as e:
        logger.error(traceback.format_exc())
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 409
            response['message'] = "title: This card already exists in the rules."
        else:
            response['status'] = 500
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response

def delete_card(project_id, card_ids):
    response = {
        'status': 400
    }
    conn = pg_db.get_connection()
    deleted_ids = []
    not_found_ids = []
    try:
        for card_id in card_ids:
            # Step 1: Delete related records from agile_card_comments
            delete_comments_query = text(f"""DELETE FROM agile_card_comments
                                            WHERE card_id = :id;""")
            conn.execute(delete_comments_query.params(id=card_id))
            conn.commit()

            # Step 2: Delete related records from agile_customfield_value
            delete_customfield_value_query = text(f"""DELETE FROM agile_customfield_value
                                                    WHERE card_id = :id;""")
            conn.execute(delete_customfield_value_query.params(id=card_id))
            conn.commit()

            # Step 3: Delete related records from agile_project_cards_logs
            delete_logs_query = text(f"""DELETE FROM agile_project_cards_logs WHERE card_id = :id;""")
            conn.execute(delete_logs_query.params(id=card_id))
            conn.commit()

            # Step 4: Delete related records from agile_project_card_attachments
            get_attachments_query = text(f"""SELECT file_name, relative_path FROM agile_project_card_attachments WHERE project_id = :project_id AND card_id = :id;""")
            attachments = conn.execute(get_attachments_query.params(id=card_id, project_id=project_id)).fetchall()

            if attachments:
                for attachment in attachments:
                    relative_path = attachment[1]                
                    file_path = os.path.join('/app/images/', str(relative_path))
                    if os.path.exists(file_path):
                        os.remove(file_path)


            delete_attachments_query = text(f"""DELETE FROM agile_project_card_attachments WHERE project_id = :project_id AND card_id = :id;""")
            conn.execute(delete_attachments_query.params(id=card_id, project_id=project_id))
            conn.commit()

            # Step 5: Delete the card from project_cards
            delete_card_query = text(f"""DELETE FROM {pg_db.project_cards}
                                        WHERE id = :id
                                        AND project_id = :project_id;""")
            delete_card_query = delete_card_query.params(id=card_id, project_id=project_id)
            result = conn.execute(delete_card_query)

            conn.commit()

            if result.rowcount > 0:
                deleted_ids.append(card_id)
            else:
                not_found_ids.append(card_id)

        if deleted_ids and not not_found_ids:
            response['status'] = 200
            response['message'] = f"All {len(deleted_ids)} card(s) deleted successfully."
        elif deleted_ids and not_found_ids:
            response['status'] = 207  # Multi-Status (Partial Success)
            response['message'] = (
                f"{len(deleted_ids)} card(s) deleted successfully. "
                f"{len(not_found_ids)} card(s) not found."
            )
        elif not deleted_ids and not_found_ids:
            response['status'] = 404
            response['message'] = f"No cards deleted."
        else:
            response['status'] = 400
            response['message'] = "No valid card IDs provided."
        
    except Exception as e:
        logger.error(traceback.format_exc())
        response['status'] = 500
        response['message'] = "Something went wrong."
    finally:
        conn.commit()
        conn.close()

    return response

def remove_card_relation_links(store, req_body, project_id, id):
    response = {
        'status': 400
    }
    conn = pg_db.get_connection()
    try:
        if not id or not project_id:
            response['status'] = 500
            response['message'] = "Invalid payload, 'id' or 'project Id'."
            return response
        child_card_id = req_body.get('child_card_id', None)
        parent_card_id = req_body.get('parent_card_id', None)
        
        if parent_card_id:
            query = text(
                f"""Update {pg_db.project_cards} set parent_card_id = NULL where id = :card_id and project_id = :project_id"""
            )
            query = query.params(card_id=id, project_id=project_id)
            result = conn.execute(query)
        
        if child_card_id:
            query = text(
                f"""Update {pg_db.project_cards} set parent_card_id = NULL where id = :card_id and project_id = :project_id"""
            )
            query = query.params(card_id=child_card_id, project_id=project_id)
            result = conn.execute(query)

        response['status'] = 200
        response['message'] = "Card link removed successfully."
        
    except Exception as e:
        logger.error(traceback.format_exc())
        response['status'] = 500
        response['message'] = "Something went wrong."
    finally:
        conn.commit()
        conn.close()

    return response



def get_card_comment(store_id, card_id, username):
    response = {
        'status': 400
    }
    conn = pg_db.get_connection()
    try:       
        user = store_admin_db.fetch_user_by_username(store_id, username) 
        if user:
            user_name = user['name']
        else:
            user_name = ''
        comments_query = text(
            f"""SELECT * FROM agile_card_comments WHERE card_id = :card_id ORDER BY created_at DESC"""
        )
        comments_query = comments_query.bindparams(card_id=card_id)
        comments_result = conn.execute(comments_query)        
        comments_data = comments_result.fetchall()        
                                               
        if comments_data:                
            comment_array = []
            for c_data in comments_data:
                comment = {}
                comment['id'] = c_data[0]
                comment['card_id'] = c_data[1]
                comment['comment'] = c_data[2]
                comment['created_by'] = c_data[3]
                comment['created_at'] = convert_to_timestamp(c_data[4])
                comment['updated_at'] = convert_to_timestamp(c_data[5])
                comment['is_editable'] = True if comment['created_by'] == user_name else False
                comment['is_updated'] = True if c_data[5] else False
                comment_array.append(comment)            
                
            response['status'] = 200
            response['data'] = comment_array 
        else:
            response['status'] = 200
            response['data'] = [] 
    finally:        
        conn.close()
    return response   


def add_card_comment(comment, username, id, store, to_email_text):
    response = {
        "status": 400        
    }
    conn = pg_db.get_connection()
    try:
        user = user_db.fetch_user_by_username(username)
        created_by = ''
        if user:
                created_by = user['name']
        else:
            response['status'] = 400 
            response['message'] = "Invalid email id."
            return response
        
        query = text(
        f"""INSERT INTO agile_card_comments (card_id, comment, created_by, created_at)
            VALUES (:card_id, :comment, :created_by, CURRENT_TIMESTAMP)
            RETURNING id;
        """
         )         
        query = query.params(card_id=id, comment=comment, created_by=created_by)        
        result = conn.execute(query)
        comment_id = result.fetchone()[0]

        # Add comment history log    
        log_payload_array = []
        data = {}            
        data['field_name'] = 'comment'
        data['old_value'] = ''
        data['new_value'] = comment
        data['created_by'] = username
        data['ticket_id'] = id
        log_payload_array.append(data)

        if log_payload_array:
            add_card_history_log(log_payload_array)

        if to_email_text is not None:
            to_email = common.extract_email(to_email_text)
            send_alert_email_to_user(store, to_email, id, comment)
            response['status'] = 200
            response['message'] = 'Comment added successfully and notification sent to user.'
            return response
        
        task.send_task(task.SEND_PROJECT_NOTIFICATION_TASK, args=(store['id'], ProjectNotification.COMMENT_ADDED, comment_id))
        response['status'] = 200
        response['message'] = 'Comment added successfully.'

    except IntegrityError as e:
        logger.error(traceback.format_exc())
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 409
            response['message'] = "Duplicate key violation: This card already exists in the rules."
        else:
            error_message = str(e)
            response['status'] = 500
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response

def send_alert_email_to_user(store, to_email, card_id, comment):
    email_util.send_comment_alert_email(str(store["id"]), to_email, card_id, comment)

def update_card_comment(store_id, comment, card_id, id, username):
    response = {
        "status": 400        
    }
    conn = pg_db.get_connection()
    try:    
        query = text(
            f"""SELECT * FROM agile_card_comments WHERE id = :id AND card_id = :card_id"""
        )       
        query = query.bindparams(id=id, card_id=card_id)
        result = conn.execute(query)
        old_comment = result.fetchone()
        if not old_comment:
            response['status'] = 404
            response['message'] = "Comment not found."
            return response    
        query = text(
        f"""UPDATE agile_card_comments
            SET
                comment = :comment,                
                updated_at = CURRENT_TIMESTAMP
            WHERE 
                id = :id AND
                card_id = :card_id;"""
    )
        query = query.params(id=id, card_id=card_id, comment=comment)        
        result = conn.execute(query)
        data = result.rowcount > 0
        if data:
            # Add comment history log    
            log_payload_array = []
            log = {}            
            log['field_name'] = 'comment'
            log['old_value'] = old_comment[2] if old_comment else ''
            log['new_value'] = comment
            log['created_by'] = username
            log['ticket_id'] = card_id
            log_payload_array.append(log)
            
            if log_payload_array:
                add_card_history_log(log_payload_array)

            task.send_task(task.SEND_PROJECT_NOTIFICATION_TASK, args=(store_id, ProjectNotification.COMMENT_ADDED, id))

            response['status'] = 200
            response['message'] = 'Comment updated successfully.'
        else:
            response['status'] = 400
            response['message'] = "Comment updation failed."
        
    except IntegrityError as e:            
        error_message = str(e)
        response['status'] = 500
        response['message'] = f"Something went wrong. {error_message}"
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response

def delete_card_comment(card_id, id, username):
    response = {
        "status": 400        
    }
    conn = pg_db.get_connection()
    try:        
        query = text(
            f"""SELECT * FROM agile_card_comments WHERE id = :id AND card_id = :card_id"""
        )       
        query = query.bindparams(id=id, card_id=card_id)
        result = conn.execute(query)
        old_comment = result.fetchone()               
        query = text(
        f"""DELETE FROM agile_card_comments
            WHERE id = :id 
            AND card_id = :card_id;
        """
        )
        query = query.params(id=id, card_id=card_id)        
        result = conn.execute(query)
        data = result.rowcount > 0
        if data:
            # Add comment history log    
            log_payload_array = []
            log = {}            
            log['field_name'] = 'comment'
            log['old_value'] = old_comment[2] if old_comment else ''
            log['new_value'] = ''
            log['created_by'] = username
            log['ticket_id'] = card_id
            log_payload_array.append(log)

            if log_payload_array:
                add_card_history_log(log_payload_array)
            response['status'] = 200
            response['message'] = "Comment deleted successfully."
        else:
            response['status'] = 400
            response['message'] = "Comment deletion failed."

    except IntegrityError as e:               
        error_message = str(e)
        response['status'] = 500
        response['message'] = f"Something went wrong. {error_message}"
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response

def add_card_history_log(payload):
    conn = pg_db.get_connection()
    try:
        if payload:
            for data in payload:
                card_id = data['ticket_id']
                field_name = data['field_name']
                old_value = data['old_value']
                new_value = data['new_value']
                created_by = data['created_by']
                spent_time_diff = data.get('spent_time_diff', None)
                log_description = data.get('log_description', None)
                spent_time_created_at = data.get('spent_time_created_at', None)
                custom_field_id = data.get('custom_field_id', None)
                
                query = text(
                f"""INSERT INTO agile_project_cards_logs (card_id, field_name, old_value, new_value, created_by, created_at, custom_field_id, spent_time_diff, log_description, spent_time_created_at)
                    VALUES (:card_id, :field_name, :old_value, :new_value, :created_by, CURRENT_TIMESTAMP, :custom_field_id, :spent_time_diff, :log_description, :spent_time_created_at);
                """
                )         
                query = query.params(card_id=card_id, field_name=field_name, old_value=old_value, new_value=new_value, created_by=created_by, custom_field_id=custom_field_id, spent_time_diff=spent_time_diff, log_description=log_description, spent_time_created_at=spent_time_created_at)        
                conn.execute(query)

    except IntegrityError as e:
        logger.error(traceback.format_exc())
    finally:
        if conn:
            conn.commit()
            conn.close()

def get_card_history_logs(store_id, card_id, page, limit, username):
    response = {
        'status': 400
    }
    conn = pg_db.get_connection()
    try:    
        page = int(page)
        limit = int(limit)
        offset = (page - 1) * limit
        count_query = text(
            f"""SELECT COUNT(*) FROM agile_project_cards_logs WHERE card_id = :card_id"""
        )   
        count_query = count_query.bindparams(card_id=card_id)
        count_rs = conn.execute(count_query)
        total_count = int(count_rs.scalar())

        log_query = text(
            f"""SELECT id, card_id, field_name, old_value, new_value, created_by, created_at, custom_field_id FROM agile_project_cards_logs WHERE card_id = :card_id ORDER BY created_at DESC OFFSET :offset LIMIT :limit"""
        )
        log_query = log_query.bindparams(card_id=card_id, offset=offset, limit=limit)
        logs_result = conn.execute(log_query)        
        logs_data = logs_result.fetchall()                    
        if logs_data:                
            log_array = []
            for l_data in logs_data:
                user = None
                query = ''
                field_label = _format_field_label(l_data[2])
                if l_data[5] is not None:
                    user = store_admin_db.fetch_user_by_username(store_id, l_data[5])

                old_value = l_data[3]
                new_value = l_data[4]

                if l_data[2] == 'status':
                    query = text(f"""SELECT label FROM agile_card_status WHERE id = :id""")
                    field_label = 'Status'
                elif l_data[2] == 'priority':    
                    query = text(f"""SELECT label FROM agile_card_priorities WHERE id = :id""")
                    field_label = 'Priority'
                elif l_data[2] == 'module_id':
                    query = text(f"""SELECT name FROM agile_project_modules WHERE id = :id""")
                    field_label = 'Module'
                elif l_data[2] == 'current_column_id':
                    query = text(f"""SELECT name FROM agile_project_columns WHERE id = :id""")
                    field_label = 'Kanban Stage'
                elif l_data[2] == 'custom_field':
                    c_query = text(f"""SELECT name FROM agile_project_customfield WHERE id = :id""")
                    c_query = c_query.bindparams(id=l_data[7])
                    c_result = conn.execute(c_query).fetchone()
                    field_label = c_result[0] if c_result else ''
                elif l_data[2] == 'assigned_to':
                    if old_value:
                        old_user_data = user_db.fetch_user_by_username(old_value)
                        old_value = old_user_data['name'] if old_user_data else ''
                    if new_value:
                        new_user_data = user_db.fetch_user_by_username(new_value)
                        new_value = new_user_data['name'] if new_user_data else ''
                elif l_data[2] == 'start_date' or l_data[2] == 'due_date':
                    if old_value and old_value != 'None':    
                        # old_value_date = datetime.strptime(old_value, "%Y-%m-%d")
                        # old_value = old_value_date.strftime("%d %b %Y")
                        old_value = convert_to_timestamp(old_value)
                    if new_value and new_value != 'None':
                        # new_value_date = datetime.strptime(new_value, "%Y-%m-%d")
                        # new_value = new_value_date.strftime("%d %b %Y")
                        new_value = convert_to_timestamp(new_value)
                
                if query != '':
                    query1 = query.bindparams(id=old_value)
                    query2 = query.bindparams(id=new_value)
                    
                    result = conn.execute(query1).fetchone()
                    result1 = conn.execute(query2).fetchone()
                    
                    old_value = result[0] if result else ''
                    new_value = result1[0] if result1 else ''

                    old_value = old_value.split('_')[0] if '_' in old_value else old_value
                    new_value = new_value.split('_')[0] if '_' in new_value else new_value

                log = {}
                log['id'] = l_data[0]
                log['card_id'] = l_data[1]
                log['field_name'] = l_data[2]
                log['field_lable'] = field_label
                log['old_value'] = old_value
                log['new_value'] = new_value
                log['created_by'] = l_data[5]
                log['created_at'] = convert_to_timestamp(l_data[6])
                log['created_by_name'] = user['name'] if user else ''    
                log_array.append(log)            

            final_result = {
                "data": log_array,
                "meta": {
                    "current_page": page,
                    "next_page": page + 1 if offset + limit < total_count else None,
                    "total_count": total_count
                }
            }    
            response['status'] = 200
            response['data'] = final_result 
        else:
            response['status'] = 200
            response['data'] = {
                "data": [],
                "meta": {
                    "current_page": page,
                    "next_page": page + 1 if offset + limit < total_count else None,
                    "total_count": total_count
                }
            }
    finally:        
        conn.close()
    return response   

def _format_field_label(input_string):
    words = input_string.split('_')
    capitalized_words = [word.capitalize() for word in words]
    return ' '.join(capitalized_words)


def get_time_logs(card_id, page=None, limit=None, time_log_id=None):
    response = {
        'status': 400
    }
    conn = pg_db.get_connection()
    try:
        if time_log_id:
            # Fetch specific time log with time_log_id
            query = text("""
                SELECT spent_time_diff
                FROM agile_project_cards_logs
                WHERE card_id = :card_id AND id = :time_log_id
            """)
            query = query.params(card_id=card_id, time_log_id=time_log_id)
            result = conn.execute(query).fetchone()
            
            if result:
                response['status'] = 200
                response['data'] = result[0]
            else:
                response['status'] = 200
                response['data'] = []
        else:
            offset = (page - 1) * limit

            count_query = text("""
                SELECT COUNT(*)
                FROM agile_project_cards_logs
                WHERE card_id = :card_id AND field_name = 'spent_time' AND spent_time_diff IS NOT NULL
            """)
            count_query = count_query.params(card_id=card_id)
            count_result = conn.execute(count_query).fetchone()
            total_count = count_result[0]

            # Fetch all time logs for the given card_id
            query = text("""
                SELECT id, card_id, spent_time_diff, spent_time_created_at, log_description, created_by, spent_time_updated_at, created_at
                FROM agile_project_cards_logs
                WHERE card_id = :card_id AND field_name = 'spent_time' AND spent_time_diff IS NOT NULL
                ORDER BY spent_time_created_at DESC
                OFFSET :offset LIMIT :limit
            """)
            query = query.params(card_id=card_id, offset=offset, limit=limit)
            results = conn.execute(query).fetchall()
            time_log_array = []

            if results:
                for result in results:
                    user_data = user_db.fetch_user_by_username(result[5])
                    name = user_data['name'] if user_data else ''
                    time_log = {
                        'id': result[0],
                        'card_id': result[1],
                        'spent_time_diff': convert_time_format(result[2]),
                        'spent_time_created_at': convert_to_timestamp(result[3]),
                        'log_description': result[4],
                        'created_by': result[5],
                        'created_by_name': name,
                        'spent_time_updated_at': convert_to_timestamp(result[6]),
                        'is_updated': True if result[6] is not None else False,
                        'time_log_created_at': convert_to_timestamp(result[7])
                    }
                    time_log_array.append(time_log)

                paginated_data = calculatePaginationData(time_log_array, page, limit, total_count)

                response['status'] = 200
                response['data'] = paginated_data
            else:
                response['status'] = 200
                response['data'] = []
    finally:
        conn.close()

    return response



def get_card_spent_time(card_id):
    response = {
        'status': 400
    }
    conn = pg_db.get_connection()
    try:
        query = text("""
            SELECT spent_time
            FROM agile_project_cards
            WHERE id = :card_id
        """)
        query = query.params(card_id=card_id)
        result = conn.execute(query).fetchone()
        
        if result:
            response['status'] = 200
            response['data'] = result[0]
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        conn.close()
    return response


def update_time_log(username, payload, card_id, time_log_id):
    response = {
        'status': 400
    }
    conn = pg_db.get_connection()
    try:
        if not card_id or not time_log_id:
            response['status'] = 400
            response['message'] = "Invalid payload, 'card Id' or 'time log Id'."
            return response
        
        # Fetch existing time log
        existing_time_log = get_time_logs(card_id, time_log_id=time_log_id)
        if not existing_time_log:
            response['status'] = 404
            response['message'] = "Time log not found."
            return response
        
        old_spent_time = get_card_spent_time(card_id)
        if old_spent_time['data'] is not None:
            old_value = old_spent_time['data']
            old_value = convert_timedelta_to_hhmmss(old_value)
        else:
            old_value = None

        # Initialize fields for update
        update_fields = {}
        for field in ['spent_time_diff', 'log_description', 'spent_time_created_at']:
            if field in payload:
                update_fields[field] = payload[field]

        # Compare spent_time_diff if provided in payload
        if 'spent_time_diff' in payload:
            old_spent_time_diff = existing_time_log['data']
            new_spent_time_diff = parse_time_format(payload['spent_time_diff'])
            
            if new_spent_time_diff > old_spent_time_diff:
                difference = new_spent_time_diff - old_spent_time_diff
                # Add difference to agile_projects_cards.spent_time
                conn.execute(text("""
                    UPDATE agile_project_cards 
                    SET spent_time = spent_time + :difference 
                    WHERE id = :card_id
                """), {'difference': difference, 'card_id': card_id})
            
            elif new_spent_time_diff < old_spent_time_diff:
                difference = old_spent_time_diff - new_spent_time_diff
                # Subtract difference from agile_projects_cards.spent_time
                conn.execute(text("""
                    UPDATE agile_project_cards 
                    SET spent_time = spent_time - :difference 
                    WHERE id = :card_id
                """), {'difference': difference, 'card_id': card_id})

        conn.commit()
        # Prepare the update query for agile_project_cards_logs
        set_clause = ", ".join([f"{field} = :{field}" for field in update_fields])
        query = text(f"""
            UPDATE agile_project_cards_logs 
            SET {set_clause},
            spent_time_updated_at = CURRENT_TIMESTAMP
            WHERE id = :id AND card_id = :card_id
        """)
        conn.commit()
        
        # Add parameters for the query
        query = query.params(id=time_log_id, card_id=card_id, **update_fields)

        # Execute the update query
        time_log_update = conn.execute(query)

        log_payload_array = []
        if 'spent_time_diff' in payload:
            new_value = get_card_spent_time(card_id)
            if new_value:
                new_value = convert_timedelta_to_hhmmss(new_value['data'])
            else:
                new_value = None
            if old_value != new_value:
                data = {}
                data['field_name'] = 'spent_time'
                data['old_value'] = convert_time_format(old_value) if old_value is not None else None
                data['new_value'] = convert_time_format(new_value) if new_value is not None else None
                data['created_by'] = username
                data['ticket_id'] = card_id
                data['spent_time_diff'] = None 
                data['log_description'] = None
                data['spent_time_created_at'] = None
                log_payload_array.append(data)

        if log_payload_array != []:
            add_card_history_log(log_payload_array)
        
        if time_log_update.rowcount > 0:
            response['status'] = 200
            response['message'] = "Data updated successfully."
        else:
            response['status'] = 400
            response['message'] = "Data updation failed."

    finally:
        conn.commit()
        conn.close()

    return response

def delete_time_log(card_id, time_log_id, username):
    response = {
        'status': 400
    }
    conn = pg_db.get_connection()
    try:
        # Fetch existing time log
        existing_time_log = get_time_logs(card_id, time_log_id=time_log_id)
        if not existing_time_log:
            response['status'] = 404
            response['message'] = "Time log not found."
            return response
        else:
            existing_time_log = convert_timedelta_to_hhmmss(existing_time_log['data'])
        
        old_spent_time = get_card_spent_time(card_id)
        if old_spent_time['data'] is not None:
            old_value = old_spent_time['data']
            old_value = convert_timedelta_to_hhmmss(old_value)
        else:
            old_value = None
        
        query = text("""
                    UPDATE agile_project_cards_logs SET spent_time_diff = NULL, spent_time_created_at = NULL, log_description = NULL, spent_time_updated_at = NULL WHERE card_id = :card_id AND id = :time_log_id
                     """)
        query = query.params(card_id=card_id, time_log_id=time_log_id)
        result = conn.execute(query)
        conn.commit()
        
        # Subtract time log entry from agile_projects_cards.spent_time
        conn.execute(text("""
                    UPDATE agile_project_cards 
                    SET spent_time = spent_time - :difference 
                    WHERE id = :card_id
                """), {'difference': existing_time_log, 'card_id': card_id})
        conn.commit()

        log_payload_array = []
        new_value = get_card_spent_time(card_id)
        
        if new_value['data'] is None:
            conn.execute(text("""
                    UPDATE agile_project_cards 
                    SET spent_time = '00:00:00'
                    WHERE id = :card_id
                """), {'card_id': card_id})
        conn.commit()

        if new_value['data'] is not None:
            new_value = convert_timedelta_to_hhmmss(new_value['data'])
        else:
            new_value = None
        
        if old_value != new_value:
            data = {}
            data['field_name'] = 'spent_time'
            data['old_value'] = convert_time_format(old_value) if old_value is not None else None
            data['new_value'] = convert_time_format(new_value) if new_value is not None else None
            data['created_by'] = username
            data['ticket_id'] = card_id
            data['spent_time_diff'] = None 
            data['log_description'] = None
            data['spent_time_created_at'] = None
            log_payload_array.append(data)

        if log_payload_array != []:
            add_card_history_log(log_payload_array)
        
        if result.rowcount > 0:
            response['status'] = 200
            response['message'] = "Data deleted successfully."
        else:
            response['status'] = 400
            response['message'] = "Data deletion failed."

    finally:
        conn.commit()
        conn.close()
    return response

def fetch_parent_and_child_cards_by_card_id(project_id, card_id, parent_cart_id):
    response = {'status': 400}
    
    query = """SELECT pc.id, pc.project_id, pc.module_id, pc.current_column_id, pc.status, 
                    pc.priority, pc.title, pc.card_identifier, pc.assigned_to, pc.is_archived,
                    pm.name AS module_name, bc.name AS column_name, cs.label as status_label, 
                    cp.label as priority_label, pc.due_date, bc.is_resolved, pc.parent_card_id
                FROM agile_project_cards pc
                JOIN agile_project_modules pm ON pc.module_id = pm.id
                JOIN agile_project_columns bc ON pc.current_column_id = bc.id
                LEFT JOIN agile_card_status cs ON pc.status = cs.id
                LEFT JOIN agile_card_priorities cp ON pc.priority = cp.id
                WHERE pc.project_id = :project_id AND pm.is_visible = true AND bc.is_visible = true
                AND (pc.parent_card_id = :card_id OR pc.id = :parent_cart_id) ORDER BY pc.sort_id"""

    # Fetch the card data for both parent and child in a single query
    try:
        with pg_db.get_connection() as conn:
            result = conn.execute(text(query), {"project_id": project_id, "card_id": card_id, "parent_cart_id": parent_cart_id})
            rows = result.fetchall()
            # Prepare the list of card details
            card_data = {
                'child_cards': [],
                'parent_cards': []
            }
            
            # Process the rows and separate parent and child cards
            for row in rows:
                card = {
                    'tickedID': row[0],
                    'projectID': row[1],
                    'moduleID': row[2],
                    'moduleName': row[10],
                    'columnID': row[3],
                    'columnName': row[11],
                    'status': row[4],
                    'statusLabel': row[12],
                    'priority': row[5],
                    'priorityLabel': row[13],
                    'title': row[6],
                    'card_identifier': row[7],
                    'assignee': row[8],
                    'is_archived': row[9],
                    'created_by': row[10],
                    'due_date': convert_to_timestamp(row[14]),
                    'is_resolved': row[15]
                }
                
                if row[16] and int(row[16]) == int(card_id):
                    card_data['child_cards'].append(card)
                elif int(row[0]) == int(parent_cart_id):
                    card_data['parent_cards'].append(card)

            response['data'] = card_data
            response['status'] = 200
            
    except Exception as e:
        response['message'] = str(e)
    finally:
        conn.close()

    return response

def upload_attachments(store_id, username, files, project_id, card_id):
    response = {
        "status": 200,
        "files": [],
        "message": ""
    }
    MAX_CARD_SIZE_MB = 10
    MAX_CARD_SIZE_BYTES = MAX_CARD_SIZE_MB * 1024 * 1024
    failed_count = 0

    # Use absolute Windows path
    base_dir = r'/app/images'
    monthly_folder = os.path.join(base_dir, 'project', project_id, card_id)

    if not os.path.exists(monthly_folder):
        os.makedirs(monthly_folder)
    
    # Calculate current size of files in card folder
    current_total_size = get_directory_size(monthly_folder)
    if current_total_size >= MAX_CARD_SIZE_BYTES:
        response['message'] = "Upload limit of 10MB exceeded for this card."
        response['status'] = 400
    
    conn = pg_db.get_connection()
    try:
        log_payload_array = []
        for index, file in enumerate(files):
            if file.filename == '':
                failed_count += 1
                response['message'] = "No file selected for uploading"
                continue

            if file and allowed_file(file.filename):
                file.seek(0, os.SEEK_END)
                file_size = file.tell()  # Size in bytes
                file.seek(0)  # Reset pointer after checking size

                if current_total_size + file_size > MAX_CARD_SIZE_BYTES:
                    response['message'] = "Upload limit of 10MB exceeded for this card."
                    response['status'] = 400
                    break

                original_file_name = file.filename
                newName = change_file_name(file.filename, index)
                file_path = os.path.join(monthly_folder, newName)
                relative_path = os.path.relpath(file_path, base_dir)
                response['files'].append(relative_path.replace('\\', '/'))

                insert_query = text("""
                        INSERT INTO agile_project_card_attachments (
                            project_id, card_id, file_name, file_size, 
                            relative_path, file_type, created_by
                        ) VALUES (
                            :project_id, :card_id, :file_name, :file_size,
                            :relative_path, :file_type, :created_by
                        )
                        RETURNING id;
                    """)
                
                file_type = original_file_name.rsplit('.', 1)[1].lower() if '.' in original_file_name else None

                result = conn.execute(insert_query, {
                        'project_id': project_id,
                        'card_id': card_id,
                        'file_name': original_file_name,
                        'file_size': file_size,
                        'relative_path': relative_path,
                        'file_type': file_type,
                        'created_by': username
                    })
                conn.commit()
                
                file.save(file_path)
                current_total_size += file_size

                # Log history
                log_payload = {
                    'field_name': 'attachment',
                    'old_value': '',
                    'new_value': original_file_name,
                    'created_by': username,
                    'ticket_id': card_id,
                    'created_at': datetime.now(timezone.utc).isoformat()
                }
                log_payload_array.append(log_payload)
        add_card_history_log(log_payload_array)

        if failed_count > 0:
            response['message'] = f"{failed_count} files failed to upload."

        if len(response['files']) == 0:
            response['status'] = 500

    finally:
        if conn:
            conn.close()

    return response

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower()


def change_file_name(filename, index):
    random = str(round(datetime.utcnow().timestamp()))
    if '.' in filename:
        ext = filename.rsplit('.', 1)[1].lower()
        fname_without_ext = filename.rsplit('.', 1)[0]  # Extract filename without extension
        new_filename = f"{random}_{index}.{ext}"  # Append original name
        return new_filename

def get_directory_size(directory):
    """Returns total size (in bytes) of all files in the directory."""
    total = 0
    for dirpath, dirnames, filenames in os.walk(directory):
        for f in filenames:
            fp = os.path.join(dirpath, f)
            if os.path.isfile(fp):
                total += os.path.getsize(fp)
    return total
    
def get_video_thumbnail(video_path):
     """
     Extracts the first frame from a video and returns it as a PNG thumbnail.
     """
     try:
          cap = cv2.VideoCapture(video_path)
          success, frame = cap.read()
          cap.release()

          if success:
               # Convert frame to PIL image
               img = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
               img.thumbnail((100, 100))  # Resize thumbnail
               
               img_byte_arr = BytesIO()
               img.save(img_byte_arr, format='PNG')
               img_byte_arr.seek(0)
               return img_byte_arr

     except Exception as e:
          logger.error(f"Error extracting video thumbnail: {e}")
          return None

def get_card_attachment(store_id, card_id, username):
    response = {
        'status': 400
    }

    # File type categories
    attachment_types = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg', 'ico', 'mp4', 'webm', 'ogv', 'ogg', 'avi', 'mov', 'mkv']
    document_types = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'csv', 'xml', 'json', 'zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'exe']

    conn = pg_db.get_connection()
    try:
        user = store_admin_db.fetch_user_by_username(store_id, username) 
        if not user:
            return {'status': 401, 'message': 'Unauthorized'}

        query = text("""
            SELECT id, project_id, card_id, file_name, file_size, relative_path, file_type, created_by, created_at
            FROM agile_project_card_attachments
            WHERE card_id = :card_id
            ORDER BY created_at DESC
        """)
        result = conn.execute(query.bindparams(card_id=card_id))
        attachment_data = result.fetchall()

        attachment_array = []
        if attachment_data:
            for row in attachment_data:
                raw_file_type = (row[6] or '').lower()

                # Determine simplified file type
                if raw_file_type in attachment_types:
                    simplified_type = 'attachment'
                elif raw_file_type in document_types:
                    simplified_type = 'file'
                else:
                    simplified_type = 'unknown'

                attachment = {
                    'id': row[0],
                    'project_id': row[1],
                    'card_id': row[2],
                    'file_name': row[3],
                    'file_size': row[4],
                    'relative_path': row[5],
                    'file_type': simplified_type,
                    'created_by': row[7],
                    'created_at': convert_to_timestamp(row[8])
                }
                attachment_array.append(attachment)

        response['status'] = 200
        response['data'] = attachment_array

    finally:
        conn.close()

    return response

def delete_card_attachment(username, file_name_array, project_id, card_id):
    response = {
        'status': 400,
        'message': 'No file names provided'
    }

    if not file_name_array:
        return response

    conn = pg_db.get_connection()
    try:
        # Build dynamic ILIKE conditions for each file
        ilike_conditions = []
        params = {
            'project_id': int(project_id),
            'card_id': int(card_id)
        }
        
        for i, file_name in enumerate(file_name_array):
            param_name = f'file_path_{i}'
            ilike_conditions.append(f"relative_path ILIKE :{param_name}")
            params[param_name] = f'%{file_name}%'

        # Construct the query with dynamic ILIKE conditions
        fetch_query = text(f"""
            SELECT file_name, relative_path
            FROM agile_project_card_attachments
            WHERE project_id = :project_id 
            AND card_id = :card_id
            AND ({' OR '.join(ilike_conditions)})
        """)
        
        fetch_result = conn.execute(fetch_query, params).fetchall()

        if not fetch_result:
            response['message'] = 'No matching attachments found'
            response['status'] = 404
            return response

        # Track files to delete from filesystem
        files_to_delete = []
        deleted_files = []
        
        for file_name, relative_path in fetch_result:
            # Construct file path
            file_path = os.path.join('/app/images/', str(relative_path))
            files_to_delete.append((file_path, file_name))
            deleted_files.append(file_name)

        # Delete records from database using the same conditions
        delete_query = text(f"""
            DELETE FROM agile_project_card_attachments
            WHERE project_id = :project_id 
            AND card_id = :card_id
            AND ({' OR '.join(ilike_conditions)})
            RETURNING file_name
        """)
        
        result = conn.execute(delete_query, params)
        deleted_records = result.fetchall()
        conn.commit()

        if deleted_records:
            # Delete physical files
            for file_path, _ in files_to_delete:
                if os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                    except OSError as e:
                        logger.warning(f"Failed to delete file {file_path}: {str(e)}")

            # Log history for all deleted files
            log_payload_array = [{
                'field_name': 'attachment',
                'old_value': file_name,
                'new_value': '',
                'created_by': username,
                'ticket_id': card_id,
                'created_at': datetime.now(timezone.utc).isoformat()
            } for file_name in deleted_files]
            
            add_card_history_log(log_payload_array)

            response['status'] = 200
            response['message'] = f'Successfully deleted {len(deleted_records)} attachment(s)'
            response['data'] = {
                'deleted_files': deleted_files
            }
        else:
            response['message'] = 'No attachments were deleted'

    except Exception as e:
        logger.error(f"Error deleting card attachments: {str(e)}")
        response['message'] = str(e)
        response['status'] = 500
        if conn:
            conn.rollback()

    finally:
        if conn:
            conn.close()

    return response