from graphql import bulk_query
from sqlalchemy import text
import pg_db
from mongo_db import user_db
from datetime import datetime
from utils import bc
from utils.common import calculatePaginationData, convert_to_timestamp
from plugin import bc_products
import logging
from new_pgdb.analytics_db import AnalyticsDB
import task
from new_mongodb import AdminAppNotification

logger = logging.getLogger()


def get_distribution_details_old(store, bop_id):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        query = text(f"""select d.id, d.bop_id, d.start_date_time, d.end_date_time, d.is_qty_locked, d.is_active, d.is_published, d.created_at, d.created_by, d.updated_at, d.updated_by, d.orders, d.salse_rep_count, bop.bc_name, bop.case_qty, bop.display_qty, bop.name, bop.is_po_locked, d.updated_at, bop.min_market_price, bop.is_marketing_product from bo_distributions d join bo_bulk_order_products bop on bop.bop_id = d.bop_id where d.bop_id = :bop_id and d.end_date_time is null and d.is_published = true;""")
        query = query.params(bop_id=bop_id)
        result = conn.execute(query)
        distribution_data = result.fetchone()
        final_data = {}
        p_id = 0  
        customer_id_array = {}
        product_ids=[]
        variant_id_array = []  
        variant_array = {}
        marketing_product_skus = []
        last_modified_at = ''
        is_distribution_found = False
        if distribution_data:
            is_distribution_found = True
            distribution_id = distribution_data[0]    
            last_modified_at = distribution_data[18]    
            query = text(f"""select dl.id, dl.distribution_id, dl.bc_product_id, dl.bc_sku, dl.variant_id, dl.bc_variant_id, dl.bo_upc, dl.bc_upc, dl.option, dl.po_option, dl.available_qty, dl.requested_qty, dl.locked_qty, dl.distributed_qty, dl.customer_id, dl.customer_name, dl.customer_rep_id, dl.customer_rep_name, dl.created_at, dl.created_by, dl.previously_distributed_qty, dl.customer_rep_email, c.email, c.company  from bo_distribution_lineitems as dl join customers as c on c.customer_id = dl.customer_id where distribution_id = :distribution_id and requested_qty > 0 order by id;""")
            query = query.params(distribution_id=distribution_id)
            result = conn.execute(query)
            rows = result.fetchall()
            
            for row in rows:                           
                product_id = str(row[2])
                customer_id = str(row[14])
                p_id = product_id
                if product_id not in final_data:
                    final_data[product_id] = {
                        'distribution_id': distribution_data[0],
                        'bop_id': distribution_data[1],
                        'bc_product_id': row[2],
                        'bc_name': distribution_data[13],
                        'bc_sku': row[3],
                        'is_qty_locked': distribution_data[4],
                        'is_active': distribution_data[5],
                        'is_published': distribution_data[6],
                        'case_qty': distribution_data[14],
                        'display_qty': distribution_data[15],
                        'name': distribution_data[16],
                        'is_po_locked': distribution_data[17],
                        'min_market_price': distribution_data[19],
                        'variants': []
                    }
                    product_ids.append(str(product_id))
                    if distribution_data[20]:
                        marketing_product_skus.append(row[3])
                
                if row[4] not in variant_array:
                    if row[5] is not None:
                       variant_id_array.append(int(row[5]))
                    variant_array[row[4]] = {                        
                        'bc_upc': row[7],
                        'bo_upc': row[6],                        
                        'option': row[8],
                        'po_option': row[9],
                        'variant_id': row[4],
                        'bc_variant_id': row[5],
                        'available_qty': None, 
                        'customer_array': [
                            {
                                'customer_id': row[14],
                                'customer_name': row[15],
                                'customer_rep_id': row[16],
                                'customer_rep_name': row[17],
                                'requested_qty': row[11],                               
                                'locked_qty': row[12],
                                'distributed_qty': row[13],
                                'previously_distributed_qty': row[20],
                                'customer_email': row[22],
                                'customer_company': row[23],
                                'customer_rep_email': row[21]
                            }
                        ]
                    }    
                else:
                    variant_array[row[4]]['customer_array'].append({
                        'customer_id': row[14],
                        'customer_name': row[15],
                        'customer_rep_id': row[16],
                        'customer_rep_name': row[17],
                        'requested_qty': row[11],                        
                        'locked_qty': row[12],
                        'distributed_qty': row[13],
                        'previously_distributed_qty': row[20],
                        'customer_email': row[22],
                        'customer_company': row[23],
                        'customer_rep_email': row[21]
                    })
                if customer_id not in customer_id_array:
                            customer_id_array[customer_id] = {                            
                                "customer_id": row[14],
                                "customer_name": row[15],
                                "customer_rep_id": row[16],
                                "customer_rep_name": row[17],
                                "requested_qty": None,                                
                                'locked_qty': '-',
                                'distributed_qty': None,
                                'previously_distributed_qty': 0,
                                'customer_email': row[22],
                                'customer_company': row[23],
                                'customer_rep_email': row[21]
                            }  
        append_query = ''
        if last_modified_at != '':
            append_query = ' and po.created_at > :last_modified_at '
        query = text(f"""SELECT max(pol.bop_id), max(pol.bo_upc), pol.bc_upc, pol.option, pol.po_option, pol.variant_id, pol.bc_variant_id, SUM(pol.requested_qty) AS total_requested_qty, po.customer_id, max(po.customer_name), max(po.customer_rep_id), max(po.customer_rep_name), max(bop.bc_product_id), max(bop.bc_name), max(bop.bc_sku), max(bop.case_qty), max(bop.display_qty), max(bop.name), bop.is_po_locked, SUM(pol.fullfilled_qty) as total_fullfilled_qty, bop.min_market_price, max(po.customer_rep_email), c.company, c.email, bop.is_marketing_product
                    FROM bo_purchase_order_lineitems AS pol JOIN bo_purchase_orders AS po ON po.po_id = pol.po_id JOIN bo_bulk_order_products AS bop ON bop.bop_id = pol.bop_id JOIN customers AS c ON c.customer_id = po.customer_id
                    WHERE pol.bop_id = :bop_id and po.status NOT IN ('deleted', 'completed') {append_query}  GROUP BY po.customer_id, pol.option, pol.bc_upc, pol.po_option, pol.variant_id, pol.bc_variant_id, po.customer_name, bop.is_po_locked, bop.min_market_price, c.company, c.email, bop.is_marketing_product ORDER BY po.customer_name """)

        if append_query == '':    
            query = query.params(bop_id=bop_id)
        else: 
            query = query.params(bop_id=bop_id, last_modified_at=last_modified_at)
        result = conn.execute(query)
        rows = result.fetchall() 

        for row in rows:       
            product_id = str(row[12])
            p_id = product_id            
            customer_id = str(row[8])
            if product_id not in final_data:
                
                final_data[product_id] = {    
                        'distribution_id': None,                
                        'bop_id': row[0],
                        'bc_product_id': row[12],
                        'bc_name': row[13],
                        'bc_sku': row[14],
                        'is_qty_locked': False,
                        'is_active': True,
                        'is_published': True,
                        'is_po_locked': row[18],
                        'case_qty': row[15],
                        'display_qty': row[16],
                        'name': row[17],
                        'min_market_price': row[20],
                        'variants': []
                    }
                product_ids.append(str(product_id))
                if row[24]:
                    marketing_product_skus.append(row[14])


            if row[5] not in variant_array:
                if row[6] is not None:
                    variant_id_array.append(int(row[6]))
                variant_array[row[5]] = {                        
                    'bc_upc': row[2],
                    'bo_upc': row[1],                        
                    'option': row[3],
                    'po_option': row[4],
                    'variant_id': row[5],
                    'bc_variant_id': row[6],
                    'available_qty': None, 
                    'customer_array': [
                        {
                            'customer_id': row[8],
                            'customer_name': row[9],
                            'customer_rep_id': row[10],
                            'customer_rep_name': row[11],
                            'requested_qty': (row[7] - row[19] if (row[7] - row[19]) > 0 else None) if not is_distribution_found else row[7],                               
                            'locked_qty': 0,
                            'distributed_qty': 0,
                            'previously_distributed_qty': 0,
                            'customer_email': row[23],
                            'customer_company': row[22],
                            'customer_rep_email': row[21]
                        }
                    ]
                }                 
            else:
                customer_found = False 
                for customer in variant_array[row[5]]['customer_array']:
                    if int(customer_id) == int(customer['customer_id']):   
                        customer['requested_qty'] = (customer['requested_qty'] + row[7])                     
                        customer_found = True
                        break   
                if not customer_found:
                    variant_array[row[5]]['customer_array'].append({
                        'customer_id': row[8],
                        'customer_name': row[9],
                        'customer_rep_id': row[10],
                        'customer_rep_name': row[11],
                        'requested_qty': (row[7] - row[19] if (row[7] - row[19]) > 0 else None) if not is_distribution_found else row[7],                        
                        'locked_qty': 0,
                        'distributed_qty': 0,
                        'previously_distributed_qty': 0,
                        'customer_email': row[23],
                        'customer_company': row[22],
                        'customer_rep_email': row[21]
                    })

            if customer_id not in customer_id_array:
                    customer_id_array[customer_id] = {                            
                        "customer_id": row[8],
                        "customer_name": row[9],
                        "customer_rep_id": row[10],
                        "customer_rep_name": row[11],
                        "requested_qty": None,                                
                        'locked_qty': '-',
                        'distributed_qty': None,
                        'previously_distributed_qty': 0,
                        'customer_email': row[23],
                        'customer_company': row[22],
                        'customer_rep_email': row[21]
                    }
        # if variant_array:
        for variant_id in variant_array:
            final_data[product_id]['variants'].append(variant_array[variant_id])

        for key, value in customer_id_array.items():            
            for varinat in final_data[p_id]['variants']:
                customer_found = False                
                for customer in varinat['customer_array']:
                    if str(customer['customer_id']) == str(key):
                        customer_found = True
                        break                               
                if not customer_found:                                        
                    varinat['customer_array'].append(value)         

        if p_id in final_data:
            for variant in final_data[p_id]['variants']:
                variant['customer_array'] = sorted(variant['customer_array'], key=lambda x: x['customer_name'])   

        product_inventory_data = bc_products.fetch_products_by_ids(store, product_ids)            
        if product_inventory_data:   
            graphql_inventory = {}
            for product in product_inventory_data:
                for variant in product['variants']:
                    variant_id = variant['id']
                    product_id = product['id']
                    available_to_sell = variant['inventory_level']
                    graphql_inventory[(product_id, variant_id)] = available_to_sell
            if p_id in final_data:
                for variant in final_data[p_id]['variants']:
                    variant_id = variant['bc_variant_id']
                    inventory_level = graphql_inventory.get((int(p_id), variant_id))
                    variant['available_qty'] = inventory_level

        if len(marketing_product_skus):
                data = bc_products.fetch_bc_product_by_sku(store, marketing_product_skus)
                if data:
                    if 'data' in data:
                        for i in data['data']:
                            if i['sku'] in marketing_product_skus:   
                                final_data[p_id]['variants'][0]['available_qty'] = i['inventory_level']                                                     
                                

        response['data'] = final_data[p_id] if p_id in final_data  else {}
        response['status'] = 200 


    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response



def get_distribution_details(store, bop_id):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        final_data = {}
        p_id = 0
        product_id = 0
        customer_id_array = {}
        product_ids=[]
        variant_id_array = []
        variant_array = {}
        marketing_product_skus = []
        query = text(f"""select max(bpv.bop_id) as product_id, bpv.bo_upc, bpv.bc_upc, bpv.option, bpv.po_option, bpv.id as bpv_id, max(bpv.bc_variant_id), sum(pol.requested_qty) as total_requested_qty, po.customer_id, max(po.customer_name) as customer_name,
                        max(po.customer_rep_id), max(po.customer_rep_name) as customer_rep_name,
                        max(bop.bc_product_id), max(bop.bc_name), max(bop.bc_sku), max(bop.case_qty), max(bop.display_qty), max(bop.name), bop.is_po_locked,
                        SUM(pol.fullfilled_qty) as total_fullfilled_qty, bop.min_market_price, max(po.customer_rep_email), c.company, c.email, bop.is_marketing_product,
                        sum(pol.remaining_qty) as remaining_qty, sum(pol.approved_qty) as approved_qty, ca.state, rv.total_sold_30, rv.total_sold_60
                        from bo_product_variants bpv left join bo_purchase_order_lineitems pol on bpv.id=pol.variant_id
                        left join bo_purchase_orders po on pol.po_id=po.po_id
                        left join bo_bulk_order_products bop on bop.bop_id=bpv.bop_id
                        left join customers AS c ON c.customer_id = po.customer_id
                        left join (SELECT DISTINCT ON (customer_id) customer_id, state FROM customer_addresses
						ORDER BY customer_id, customer_address_id desc) AS ca ON ca.customer_id = po.customer_id
                        left join {AnalyticsDB.get_replenishment_variants_table()} rv
                        ON (bpv.bc_sku IS NOT NULL AND bpv.bc_sku = rv.sku) 
                        OR (bpv.bc_sku IS NULL AND bpv.bc_variant_id = rv.variant_id)
                        where bpv.bop_id= :bop_id and po.status NOT IN ('deleted', 'completed') and pol.remaining_qty > 0 and (pol.status IS NULL or pol.status NOT IN ('cancelled', 'completed'))
                        group by bpv.id, bpv.bop_id, bpv.bc_sku, bpv.bc_upc, bpv.bc_variant_id, bpv.option, bpv.po_option, po.customer_id, bop.is_po_locked, bop.min_market_price, c.company, c.email, ca.state, bop.is_marketing_product, rv.total_sold_30, rv.total_sold_60;""")
        query = query.params(bop_id=bop_id)
        result = conn.execute(query)
        rows = result.fetchall()
        for row in rows:
            product_id = str(row[12])
            p_id = product_id
            customer_id = str(row[8])
            if product_id not in final_data:
                final_data[product_id] = {
                        'distribution_id': None,
                        'bop_id': row[0],
                        'bc_product_id': row[12],
                        'bc_name': row[13],
                        'bc_sku': row[14],
                        'is_qty_locked': False,
                        'is_active': True,
                        'is_published': True,
                        'is_po_locked': row[18],
                        'case_qty': row[15],
                        'display_qty': row[16],
                        'name': row[17],
                        'min_market_price': row[20],
                        'variants': []
                    }
                product_ids.append(str(product_id))
                if row[24]:
                    marketing_product_skus.append(row[14])
            if row[5] not in variant_array:
                if row[6] is not None:
                    variant_id_array.append(int(row[6]))
                variant_array[row[5]] = {
                    'bc_upc': row[2],
                    'bo_upc': row[1],
                    'option': row[3],
                    'po_option': row[4],
                    'variant_id': row[5],
                    'bc_variant_id': row[6],
                    'available_qty': None,
                    # 'total_sold_30_actual_qty': row[28],
                    # 'total_sold_60_actual_qty': row[29],
                    # 'total_sold_30': row[28] / row[15] if row[15] != 0 else row[28],
                    # 'total_sold_60': row[29] / row[15] if row[15] != 0 else row[29],
                    'total_sold_30_actual_qty': row[28] if row[28] is not None else 0,
                    'total_sold_60_actual_qty': row[29] if row[29] is not None else 0,
                    'total_sold_30': (row[28] / row[15] if row[28] is not None and row[15] and row[15] != 0 else (row[28] if row[28] is not None else 0)),
                    'total_sold_60': (row[29] / row[15] if row[29] is not None and row[15] and row[15] != 0 else (row[29] if row[29] is not None else 0)),
                    'customer_array': [
                        {
                            'customer_id': row[8],
                            'customer_name': row[9],
                            'customer_rep_id': row[10],
                            'customer_rep_name': row[11],
                            'requested_qty': (row[7] - row[19] if (row[7] - row[19]) > 0 else None),
                            'locked_qty': 0,
                            'distributed_qty': 0,
                            'previously_distributed_qty': row[19],
                            'customer_email': row[23],
                            'customer_company': row[22],
                            'customer_rep_email': row[21],
                            'customer_state': row[27]
                        }
                    ]
                }
            else:
                customer_found = False
                for customer in variant_array[row[5]]['customer_array']:
                    if int(customer_id) == int(customer['customer_id']):
                        customer['requested_qty'] = (customer['requested_qty'] + row[7])
                        customer['previously_distributed_qty'] = (customer['previously_distributed_qty'] + row[19])
                        customer_found = True
                        break
                if not customer_found:
                    variant_array[row[5]]['customer_array'].append({
                        'customer_id': row[8],
                        'customer_name': row[9],
                        'customer_rep_id': row[10],
                        'customer_rep_name': row[11],
                        'requested_qty': (row[7] - row[19] if (row[7] - row[19]) > 0 else None),
                        'locked_qty': 0,
                        'distributed_qty': 0,
                        'previously_distributed_qty': row[19],
                        'customer_email': row[23],
                        'customer_company': row[22],
                        'customer_rep_email': row[21],
                        'customer_state': row[27]
                    })
            if customer_id not in customer_id_array:
                    customer_id_array[customer_id] = {
                        "customer_id": row[8],
                        "customer_name": row[9],
                        "customer_rep_id": row[10],
                        "customer_rep_name": row[11],
                        "requested_qty": None,
                        'locked_qty': '-',
                        'distributed_qty': None,
                        'previously_distributed_qty': 0,
                        'customer_email': row[23],
                        'customer_company': row[22],
                        'customer_rep_email': row[21],
                        'customer_state': row[27]
                    }
        # if variant_array:
        for variant_id in variant_array:
            final_data[product_id]['variants'].append(variant_array[variant_id])
        for key, value in customer_id_array.items():
            for varinat in final_data[p_id]['variants']:
                customer_found = False
                for customer in varinat['customer_array']:
                    if str(customer['customer_id']) == str(key):
                        customer_found = True
                        break
                if not customer_found:
                    varinat['customer_array'].append(value)
        if final_data:
            query = text(f"""select d.id, d.bop_id, d.start_date_time, d.end_date_time, d.is_qty_locked, d.is_active, d.is_published,  d.orders, d.salse_rep_count from bo_distributions d join bo_bulk_order_products bop on bop.bop_id = d.bop_id where d.bop_id = :bop_id and d.end_date_time is null and d.is_published = true;""")
            query = query.params(bop_id=bop_id)
            result = conn.execute(query)
            distribution_data = result.fetchone()
            if distribution_data:
                final_data[product_id]['distribution_id'] = distribution_data[0]
                final_data[product_id]['is_qty_locked'] = distribution_data[4]
                final_data[product_id]['is_active'] = distribution_data[5]
                final_data[product_id]['is_published'] = distribution_data[6]
                distribution_id = distribution_data[0]
                query = text(f"""select dl.id, dl.distribution_id, dl.bc_product_id, dl.bc_sku, dl.variant_id, dl.bc_variant_id, dl.bo_upc, dl.bc_upc,  dl.requested_qty, dl.locked_qty, dl.distributed_qty, dl.customer_id from bo_distribution_lineitems as dl where distribution_id = :distribution_id and requested_qty > 0 order by id;""")
                query = query.params(distribution_id=distribution_id)
                result = conn.execute(query)
                rows = result.fetchall()
                for row in rows:
                    d_variant_id = row[4]
                    customer_id = row[11]
                    for variant in final_data[product_id]['variants']:
                        v_id = variant['variant_id']
                        if int(v_id) == int(d_variant_id):
                            for customer in variant['customer_array']:
                                if int(customer['customer_id']) == int(customer_id):
                                    customer['locked_qty'] = row[9] if row[9] > 0 else 0
                                    customer['distributed_qty'] = row[10] if row[10] > 0 else 0
        if p_id in final_data:
            for variant in final_data[p_id]['variants']:
                variant['customer_array'] = sorted(variant['customer_array'], key=lambda x: x['customer_name'])
        # logger.error("Dhaval...")
        # logger.error(store)
        # logger.error(product_ids)
        product_inventory_data = bc_products.fetch_products_by_ids(store, product_ids)
        # logger.error(product_inventory_data)
        if product_inventory_data:
            graphql_inventory = {}
            for product in product_inventory_data:
                for variant in product['variants']:
                    variant_id = variant['id']
                    product_id = product['id']
                    available_to_sell = variant['inventory_level']
                    graphql_inventory[(product_id, variant_id)] = available_to_sell
            if p_id in final_data:
                for variant in final_data[p_id]['variants']:
                    variant_id = variant['bc_variant_id']
                    inventory_level = graphql_inventory.get((int(p_id), variant_id))
                    variant['available_qty'] = inventory_level
        if len(marketing_product_skus):
                data = bc_products.fetch_bc_product_by_sku(store, marketing_product_skus)
                if data:
                    if 'data' in data:
                        for i in data['data']:
                            if i['sku'] in marketing_product_skus:
                                final_data[p_id]['variants'][0]['available_qty'] = i['inventory_level']
        response['data'] = final_data[p_id] if p_id in final_data  else {}
        response['status'] = 200
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response

def store_distribution_details(store, bop_id, bc_product_id, bc_name, bc_sku, is_qty_locked, is_active, is_published, variants, username, distribution_id, is_po_locked):
    response = {
        "status": 400,
        "message": "Data insertion failed."
    }
    conn = pg_db.get_connection()
    try:
        with conn.begin():
            if bop_id:
                start_date_time = datetime.now()            
                query = text(f"""update bo_distributions set end_date_time = :end_date_time, is_qty_locked = false, is_active = false, is_published = false where bop_id = :bop_id and is_active = true and is_published = true;""")
                query = query.params(bop_id=bop_id, end_date_time = start_date_time)
                result = conn.execute(query)

                query = text(f"""INSERT INTO bo_distributions (bop_id, start_date_time, end_date_time, is_qty_locked, is_active, is_published, created_at, created_by, updated_at, updated_by, orders, salse_rep_count, is_po_locked) VALUES (:bop_id, :start_date_time, :end_date_time, :is_qty_locked, :is_active, :is_published, :created_at, :created_by, :updated_at, :updated_by, :orders, :salse_rep_count, :is_po_locked);""")
                query = query.params(bop_id=bop_id, start_date_time=start_date_time, end_date_time=None, is_qty_locked=is_qty_locked, is_active=is_active, is_published=is_published, created_at=start_date_time, created_by=username, updated_at=start_date_time, updated_by=username, orders=0, salse_rep_count=0, is_po_locked=is_po_locked)            
                result = conn.execute(query)
               
                query = text(f"""update bo_bulk_order_products set is_qty_locked = :is_qty_locked, is_po_locked = :is_po_locked where bop_id = :bop_id;""")
                query = query.params(bop_id=bop_id, is_qty_locked = is_qty_locked, is_po_locked = is_po_locked)
                result = conn.execute(query)

                query = text(f"""select max(id) from bo_distributions where bop_id = :bop_id and end_date_time is null and is_active = true and is_published = true;""")
                query = query.params(bop_id=bop_id)
                result = conn.execute(query)
                new_row_id = result.fetchone()[0]

                if new_row_id:                                                              
                    for variant in variants:                        
                            store_distribution_details_variant(conn, new_row_id, bc_product_id, bc_name, bc_sku, variant, username, distribution_id)

                    response['status'] = 200
                    response['message'] = "Success"
                else:                
                    response['status'] = 400
                    response['message'] = "Data insertion failed."
            
    except Exception as e:
        conn.rollback()
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response

def get_distribution_logs(store, bop_id):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        query = text(f"""
                    SELECT
                        d.id,
                        bop.bc_name,
                        d.created_at,
                        d.updated_by,
                        d.orders,
                        d.salse_rep_count,
                        d.is_qty_locked, 
                        d.is_active,
                        d.is_published,
                        pdl.bc_upc,
                        pdl.bo_upc,
                        pdl.option,
                        pdl.po_option,
                        pdl.variant_id,
                        pdl.bc_variant_id,
                        pdl.available_qty,
                        pdl.requested_qty,
                        pdl.locked_qty,
                        pdl.customer_id,
                        pdl.customer_name,
                        pdl.customer_rep_id,
                        pdl.customer_rep_name,
                        bop.case_qty,
                        bop.display_qty,
                        bop.name   
                    FROM
                        bo_distributions AS d
                    LEFT JOIN
                        bo_published_distribution_logs AS pdl ON d.id = pdl.distribution_id
                    LEFT JOIN
                        bo_bulk_order_products AS bop ON d.bop_id = bop.bop_id
                    WHERE
                        d.bop_id = :bop_id;
                    """)
        query = query.params(bop_id=bop_id)
        result = conn.execute(query)
        rows = result.fetchall()        
        customer_id_array = {}
        
        final_data_list={}
        data_arr=[]
        log_array = []
        for row in rows:
            dist_id = str(row[0])
            customer_id = str(row[18])
            if dist_id not in final_data_list:
                final_data = {
                    'distribution_id': row[0],
                    'bc_name': row[1],
                    'created_at': convert_to_timestamp(row[2]),
                    'updated_by': row[3],
                    'orders': row[4],
                    'salse_rep_count': row[5],
                    'is_qty_locked': row[6],
                    'is_active': row[7],
                    'is_published':row[8],
                    'case_qty': row[22],
                    'display_qty': row[23],
                    'name': row[24],
                    'variants': []
                }
                final_data_list[dist_id] = final_data
            
            variant_found = False
            for variant in final_data_list[dist_id]['variants']:
                if variant["variant_id"] == row[13]:
                    if customer_id not in customer_id_array:
                        customer_id_array[customer_id] = {
                            # "po_id" : 0,
                            "customer_id": row[18],
                            "customer_name": row[19],
                            "customer_rep_id": row[20],
                            "customer_rep_name": row[21],
                            "requested_qty": '-',
                            "locked_qty": '-'
                        }

                    variant['customer_array'].append({
                        # "po_id": row[0],
                        "customer_id": row[18],
                        "customer_name": row[19],
                        "customer_rep_id": row[20],
                        "customer_rep_name": row[21],
                        "requested_qty": row[16],
                        "locked_qty": row[17]
                    })
                    variant_found = True
                    break
            
            if not variant_found:
                if customer_id not in customer_id_array:
                    customer_id_array[customer_id] = {
                        # "po_id" : 0,
                        "customer_id": row[18],
                        "customer_name": row[19],
                        "customer_rep_id": row[20],
                        "customer_rep_name": row[21],
                        "requested_qty": '-',
                        "locked_qty": '-'
                    }              
                final_data_list[dist_id]['variants'].append({
                    "bc_upc": row[9],
                    "bo_upc": row[10],
                    "option": row[11],
                    "po_option": row[12],
                    "variant_id": row[13],
                    "bc_variant_id": row[14],
                    "available_qty": row[15],
                    "customer_array": [{
                        # "po_id": row[0],
                        "customer_id": row[18],
                        "customer_name": row[19],
                        "customer_rep_id": row[20],
                        "customer_rep_name": row[21],
                        "requested_qty": row[16],
                        "locked_qty": row[17]
                    }]
                })
        for key, value in customer_id_array.items(): 
          for i, data in final_data_list.items():     
            for varinat in data['variants']:
                customer_found = False                
                for customer in varinat['customer_array']:
                    if str(customer['customer_id']) == str(key):
                        customer_found = True
                        break                               
                if not customer_found:                                        
                    varinat['customer_array'].append(value)         

        if final_data_list:
            for key, data in final_data_list.items():
                for variant in data['variants']:
                    variant['customer_array'] = sorted(variant['customer_array'], key=lambda x: x['customer_name'])   
                log_array.append(data)

        response['status'] = 200
        response['data'] = log_array

    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response
    

def store_distribution_details_variant(conn, new_row_id, bc_product_id, bc_name, bc_sku, variant, username, distribution_id):
    bc_upc = variant.get('bc_upc', '')
    bo_upc = variant.get('bo_upc', '')
    option = variant.get('option', '')
    po_option = variant.get('po_option', '')
    variant_id = variant.get('variant_id', 0)
    bc_variant_id = variant.get('bc_variant_id', 0)
    available_qty = variant.get('available_qty', 0)
    variant_items = variant.get('customer_array', [])
    for variant_item in variant_items:
        customer_id = variant_item.get('customer_id', 0)
        customer_name = variant_item.get('customer_name', '')
        customer_rep_id = variant_item.get('customer_rep_id', 0)
        customer_rep_name = variant_item.get('customer_rep_name', '')
        customer_rep_email = variant_item.get('customer_rep_email', '')
        requested_qty = variant_item.get('requested_qty', 0)    
        requested_qty = None if requested_qty == '-' else requested_qty        
        locked_qty = variant_item.get('locked_qty', 0)
        locked_qty = 0 if locked_qty == '-' else locked_qty
        distributed_qty = variant_item.get('distributed_qty', 0)
        distributed_qty = 0 if distributed_qty == '-' else distributed_qty   
        previously_distributed_qty = 0

        current_locked_qty = 0
        if distributed_qty and distributed_qty > 0:
            distributed_qty = distributed_qty if distributed_qty < locked_qty else 0
        if distribution_id:
            current_locked_qty_query = text("""SELECT locked_qty, previously_distributed_qty FROM bo_distribution_lineitems WHERE distribution_id = :distribution_id and variant_id = :variant_id and customer_id = :customer_id;""")
            current_locked_qty_query = current_locked_qty_query.params(distribution_id=distribution_id, variant_id=variant_id, customer_id=customer_id)
            current_locked_qty_result = conn.execute(current_locked_qty_query)
            current_locked_qty_row = current_locked_qty_result.fetchone()   

            if current_locked_qty_row:
                current_locked_qty = current_locked_qty_row[0] 
                previously_distributed_qty = current_locked_qty_row[1]  

            # if distributed_qty and distributed_qty > 0:
                # previously_distributed_qty = previously_distributed_qty + distributed_qty
                # if requested_qty:
                    # requested_qty = requested_qty - distributed_qty if requested_qty != 0 else requested_qty
                    # locked_qty = requested_qty if locked_qty > requested_qty else locked_qty
                # distributed_qty = 0
            # else:
            #     requested_qty = requested_qty

        dl_query = text(f"""INSERT INTO bo_distribution_lineitems (distribution_id, bc_product_id, bc_sku, variant_id, bc_variant_id, bo_upc, bc_upc, option, po_option, available_qty, requested_qty, locked_qty, distributed_qty, customer_id, customer_name, customer_rep_id, customer_rep_name, created_at, created_by, previously_distributed_qty, customer_rep_email) VALUES 
                     (:distribution_id, :bc_product_id, :bc_sku, :variant_id, :bc_variant_id, :bo_upc, :bc_upc, :option, :po_option, :available_qty, :requested_qty, :locked_qty, :distributed_qty, :customer_id, :customer_name, :customer_rep_id, :customer_rep_name, :created_at, :created_by, :previously_distributed_qty, :customer_rep_email);""")
        dl_query = dl_query.params(distribution_id = new_row_id, bc_product_id=bc_product_id, bc_sku=bc_sku, variant_id=variant_id, bc_variant_id=bc_variant_id, bo_upc=bo_upc, bc_upc=bc_upc, option=option, po_option=po_option, available_qty=available_qty, requested_qty=requested_qty, locked_qty=locked_qty, distributed_qty=distributed_qty, customer_id=customer_id, customer_name=customer_name, customer_rep_id=customer_rep_id, customer_rep_name=customer_rep_name, created_at=datetime.now(), created_by=username, previously_distributed_qty=previously_distributed_qty, customer_rep_email=customer_rep_email)
        result = conn.execute(dl_query)
       

        pdl_query = text(f"""INSERT INTO bo_published_distribution_logs (distribution_id, bc_product_id, bc_sku, variant_id, bc_variant_id, bo_upc, bc_upc, option, po_option, available_qty, requested_qty, locked_qty, customer_id, customer_name, customer_rep_id, customer_rep_name, created_at, created_by) VALUES 
                     (:distribution_id, :bc_product_id, :bc_sku, :variant_id, :bc_variant_id, :bo_upc, :bc_upc, :option, :po_option, :available_qty, :requested_qty, :locked_qty, :customer_id, :customer_name, :customer_rep_id, :customer_rep_name, :created_at, :created_by);""")
        pdl_query = pdl_query.params(distribution_id = new_row_id, bc_product_id=bc_product_id, bc_sku=bc_sku, variant_id=variant_id, bc_variant_id=bc_variant_id, bo_upc=bo_upc, bc_upc=bc_upc, option=option, po_option=po_option, available_qty=available_qty, requested_qty=requested_qty, locked_qty=locked_qty, customer_id=customer_id, customer_name=customer_name, customer_rep_id=customer_rep_id, customer_rep_name=customer_rep_name, created_at=datetime.now(), created_by=username)
        result = conn.execute(pdl_query)

def update_distribution_details(distribution_id, variants, is_qty_locked, bop_id, username, bc_product_id, bc_sku, store_id, payload):
    response = {
        "status": 400,
        "message": "Data insertion failed."
    }
    conn = pg_db.get_connection()
    try:
        with conn.begin():
            if distribution_id:                                                                                                    
                update_date_time = datetime.now() 
                if len(variants) > 0:         
                    for variant in variants:
                            edit_distribution_details(conn, distribution_id, variant, username, bc_product_id, bc_sku)
                
                bo_query = text(f"""update bo_bulk_order_products set is_qty_locked = :is_qty_locked where bop_id = :bop_id;""")
                bo_query = bo_query.params(bop_id=bop_id, is_qty_locked = is_qty_locked)
                bo_result = conn.execute(bo_query)            

                query = text(f"""update bo_distributions set updated_at = :updated_at, updated_by = :updated_by, is_qty_locked = :is_qty_locked where id = :distribution_id;""")
                query = query.params(distribution_id=distribution_id, updated_at = update_date_time, updated_by = username, is_qty_locked = is_qty_locked)
                result = conn.execute(query)

                task.send_task(task.SEND_BULK_ORDER_NOTIFICATION_TASK, args=(store_id, payload))

                response['status'] = 200
                response['message'] = "Success"            
            
    except Exception as e:
        conn.rollback()
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response

def edit_distribution_details(conn, distribution_id, variant, username, bc_product_id, bc_sku):                  
    bc_upc = variant.get('bc_upc', '')
    bo_upc = variant.get('bo_upc', '')
    option = variant.get('option', '')
    po_option = variant.get('po_option', '')
    variant_id = variant.get('variant_id', 0)
    bc_variant_id = variant.get('bc_variant_id', 0)
    available_qty = variant.get('available_qty', 0)
    variant_items = variant.get('customer_array', [])
    for variant_item in variant_items:
        customer_id = variant_item.get('customer_id', 0)
        customer_name = variant_item.get('customer_name', '')
        customer_rep_id = variant_item.get('customer_rep_id', 0)
        customer_rep_name = variant_item.get('customer_rep_name', '')
        customer_rep_email = variant_item.get('customer_rep_email', '')
        requested_qty = variant_item.get('requested_qty', 0)   
        requested_qty = None if requested_qty == '-'  else requested_qty      
        locked_qty = variant_item.get('locked_qty', 0)
        locked_qty = 0 if locked_qty == '-' else locked_qty
        distributed_qty = variant_item.get('distributed_qty', 0)
        distributed_qty = 0 if distributed_qty == '-' else distributed_qty 
        previously_distributed_qty = 0

        if distributed_qty and distributed_qty > 0:
            distributed_qty = distributed_qty if distributed_qty < locked_qty else 0

        # Check if the row exists in bo_distribution_lineitems
        check_dl_query = text(f"""
            SELECT 1 FROM bo_distribution_lineitems WHERE distribution_id = :distribution_id AND variant_id = :variant_id AND customer_id = :customer_id
        """)
        check_dl_query = check_dl_query.params(distribution_id=distribution_id, variant_id=variant_id, customer_id=customer_id)
        exists = conn.execute(check_dl_query).fetchone()
        
        if exists:
            dl_query = text(f"""UPDATE bo_distribution_lineitems SET available_qty = :available_qty, requested_qty = :requested_qty, locked_qty = :locked_qty, distributed_qty = :distributed_qty WHERE distribution_id = :distribution_id and variant_id = :variant_id and customer_id = :customer_id;""")
            dl_query = dl_query.params(distribution_id = distribution_id, variant_id = variant_id, customer_id = customer_id, available_qty = available_qty, requested_qty = requested_qty, locked_qty = locked_qty, distributed_qty = distributed_qty)
            result = conn.execute(dl_query)
        else:
            # Insert new row
            dl_query = text(f"""INSERT INTO bo_distribution_lineitems (distribution_id, bc_product_id, bc_sku, variant_id, bc_variant_id, bo_upc, bc_upc, option, po_option, available_qty, requested_qty, locked_qty, distributed_qty, customer_id, customer_name, customer_rep_id, customer_rep_name, created_at, created_by, previously_distributed_qty, customer_rep_email) VALUES 
                     (:distribution_id, :bc_product_id, :bc_sku, :variant_id, :bc_variant_id, :bo_upc, :bc_upc, :option, :po_option, :available_qty, :requested_qty, :locked_qty, :distributed_qty, :customer_id, :customer_name, :customer_rep_id, :customer_rep_name, :created_at, :created_by, :previously_distributed_qty, :customer_rep_email);""")
            dl_query = dl_query.params(distribution_id = distribution_id, bc_product_id=bc_product_id, bc_sku=bc_sku, variant_id=variant_id, bc_variant_id=bc_variant_id, bo_upc=bo_upc, bc_upc=bc_upc, option=option, po_option=po_option, available_qty=available_qty, requested_qty=requested_qty, locked_qty=locked_qty, distributed_qty=distributed_qty, customer_id=customer_id, customer_name=customer_name, customer_rep_id=customer_rep_id, customer_rep_name=customer_rep_name, created_at=datetime.now(), created_by=username, previously_distributed_qty=previously_distributed_qty, customer_rep_email=customer_rep_email)
            result = conn.execute(dl_query)
        
        # Check if the row exists in bo_published_distribution_logs
        check_pdl_query = text(f"""
            SELECT 1 FROM bo_published_distribution_logs WHERE distribution_id = :distribution_id AND variant_id = :variant_id AND customer_id = :customer_id
        """)
        check_pdl_query = check_pdl_query.params(distribution_id=distribution_id, variant_id=variant_id, customer_id=customer_id)
        pdl_exists = conn.execute(check_pdl_query).fetchone()
        
        if pdl_exists:
            pdl_query = text(f"""UPDATE bo_published_distribution_logs SET available_qty = :available_qty, requested_qty = :requested_qty, locked_qty = :locked_qty WHERE distribution_id = :distribution_id and variant_id = :variant_id and customer_id = :customer_id;""")
            pdl_query = pdl_query.params(distribution_id = distribution_id, variant_id = variant_id, customer_id = customer_id, available_qty = available_qty, requested_qty = requested_qty, locked_qty = locked_qty)
            result = conn.execute(pdl_query)     
        else:
            pdl_query = text(f"""INSERT INTO bo_published_distribution_logs (distribution_id, bc_product_id, bc_sku, variant_id, bc_variant_id, bo_upc, bc_upc, option, po_option, available_qty, requested_qty, locked_qty, customer_id, customer_name, customer_rep_id, customer_rep_name, created_at, created_by) VALUES 
                     (:distribution_id, :bc_product_id, :bc_sku, :variant_id, :bc_variant_id, :bo_upc, :bc_upc, :option, :po_option, :available_qty, :requested_qty, :locked_qty, :customer_id, :customer_name, :customer_rep_id, :customer_rep_name, :created_at, :created_by);""")
            pdl_query = pdl_query.params(distribution_id = distribution_id, bc_product_id=bc_product_id, bc_sku=bc_sku, variant_id=variant_id, bc_variant_id=bc_variant_id, bo_upc=bo_upc, bc_upc=bc_upc, option=option, po_option=po_option, available_qty=available_qty, requested_qty=requested_qty, locked_qty=locked_qty, customer_id=customer_id, customer_name=customer_name, customer_rep_id=customer_rep_id, customer_rep_name=customer_rep_name, created_at=datetime.now(), created_by=username)
            result = conn.execute(pdl_query)

def update_distribution_status(distribution_id, bop_id, payload, username, store_id):
    response = {
        "status": 400,
        "message": "Status updation failed."
    }
    conn = pg_db.get_connection()
    try:  
        update_distribution_details = {} 
        for field in ['is_po_locked']:
            if field in payload:
                update_distribution_details[field] = payload[field]  

        if update_distribution_details:
            set_clause = ", ".join([f"{field} = :{field}" for field in update_distribution_details])
            set_clause = f"SET {set_clause},"
        else:
            set_clause = "SET "  

        if distribution_id:                  
            query = text(f"""SELECT * FROM bo_distributions WHERE id = :distribution_id and bop_id = :bop_id;""")                
            query = query.params(distribution_id=distribution_id, bop_id=bop_id)        
            result = conn.execute(query).fetchone()                           

            query = text(
                    f"""UPDATE bo_distributions 
                            {set_clause}
                            updated_by = :updated_by,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE 
                            id = :distribution_id and bop_id = :bop_id;"""
                )          
            params = update_distribution_details.copy()
            params.update({'updated_by': username, 'distribution_id': distribution_id, 'bop_id': bop_id})
            result = conn.execute(query, params)   

            if result.rowcount > 0:
                response['status'] = 200
                response['message'] = "Status updated successfully."

        new_set_clause = set_clause
        new_set_clause = new_set_clause.rstrip(',')
        bo_query = text(f"""update bo_bulk_order_products {new_set_clause} where bop_id = :bop_id;""")
        bo_params = update_distribution_details.copy()
        bo_params.update({'bop_id': bop_id})
        bo_result = conn.execute(bo_query, bo_params) 

        if bo_result.rowcount > 0:
            response['status'] = 200
            response['message'] = "Status updated successfully."   
            if not payload["is_po_locked"]:
                task.send_task(task.GENERATE_ADMIN_APP_NOTIFICATION_TASK, args=(store_id, AdminAppNotification.UNLOCK_PRODUCT, bop_id))                                                                                                 
            
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response