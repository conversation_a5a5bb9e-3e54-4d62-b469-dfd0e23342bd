from datetime import datetime
import logging
import os

from bson import ObjectId
from flask import make_response, send_file
import requests
from mongo_db import user_db
from new_mongodb import StoreDBCollections, cms_db, get_store_db_client_for_store_id, process_data
import new_mongodb
import new_utils
from fields.dynamic_pages_fields import page_fields
from werkzeug.utils import secure_filename
from PIL import Image
from bs4 import BeautifulSoup
from pymongo import MongoClient

from plugin import bc_products

logger = logging.getLogger()

ALLOWED_EXTENSIONS = set(['png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov', 'wmv', 'mkv'])


def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def change_file_name(filename):
    rendom = str(round(datetime.utcnow().timestamp()))
    if ('.' in filename):
        ext = filename.rsplit('.', 1)[1].lower()
        fname = rendom + '.' + ext
        return fname

def get_web_pages(store, body, cdn_baseurl):        
        result = []        
        if 'webpage_id' in body:
            res = {}
            webpage_id = body['webpage_id']
            webPage = cms_db.get_page_by_id(store, webpage_id)
            webPage = process_data(webPage)
            if 'preview_state' in webPage and webPage['preview_state']:               
                preview_state = webPage['preview_state']
            else:
                preview_state = webPage['default_layout']

            preview_state =  addCdnToImageUrl(cdn_baseurl, preview_state)

            res['id'] = webPage['id']
            res['name'] = webPage['name']
            res['created_at'] = webPage['created_at']
            res['updated_at'] = webPage['updated_at']
            res['url'] = webPage['url']
            res['load_iframe'] = webPage['load_iframe']
            res['is_customers_only'] = webPage['is_customers_only']
            res['versions'] = preview_state

            result.append(res)
            return result
        else:            
            body['filterBy'] = ['name']            
            pages, total_data_length, paginationPage, limit = cms_db.get_paginated_records_updated(store, body, page_fields,'')
            webpages = cms_db.get_all_webpages(store)
            nameResult = []
            for page in webpages:
                test = {}
                test['Name'] = page['name']
                test['URL'] = page['url']
                nameResult.append(test)

            for page in pages:
                res = {}
                versions = page['versions']
                if (len(versions) == 0):
                    activeVersion = page['default_layout']
                else:
                    for version in versions:
                        if (version['status'] == 'active'):
                            activeVersion = version
                            break

                res['id'] = page['id']
                res['name'] = page['name']
                res['created_at'] = page['created_at']
                res['updated_at'] = page['updated_at']
                res['url'] = page['url']
                res['status'] = page.get('status', 'active')
                res['load_iframe'] = page['load_iframe']
                res['is_customers_only'] = page.get('is_customers_only', False)
                res['versions'] = activeVersion

                result.append(res)
            data = new_utils.calculate_pagination(
                result, paginationPage, limit, total_data_length)
            data['meta']['name_info'] = nameResult
            return data  

def get_pages_storefront(store, body, cdn_baseurl):
    result = []                        
    body['filterBy'] = ['name']            

    db_cllient = get_store_db_client_for_store_id(store['id'])
    pages, total_data_length, paginationPage, limit = new_utils.get_paginated_records_updated(db_client=db_cllient, \
                                                        collection_name=StoreDBCollections.WEBPAGES_COLLECTION, \
                                                        payload=body, fields=page_fields, additional_query='')

    webpages = cms_db.get_all_webpages(store)
    nameResult = []
    for page in webpages:
        test = {}
        test['Name'] = page['name']
        test['URL'] = page['url']
        nameResult.append(test)

    for page in pages:
        res = {}
        versions = page['versions']
        if (len(versions) == 0):
            activeVersion = page['default_layout']
        else:
            for version in versions:
                if (version['status'] == 'active'):
                    activeVersion = version
                    break
        activeVersion =  addCdnToImageUrl(cdn_baseurl, activeVersion)
                                                                                                                                                                

        res['id'] = page['id']
        res['name'] = page['name']
        res['created_at'] = page['created_at']
        res['updated_at'] = page['updated_at']
        res['url'] = page['url']
        res['status'] = page.get('status', 'active')
        res['load_iframe'] = page['load_iframe']
        res['is_customers_only'] = page.get('is_customers_only', False)
        res['versions'] = activeVersion

        result.append(res)
    data = new_utils.calculate_pagination(
        result, paginationPage, limit, total_data_length)
    data['meta']['name_info'] = nameResult
    return data 

def create_page(store, body):
    response = {
        "status": 400
    }
    url = body['name']
    # url = '/pages/' + url.replace(" ", "_").lower()
    url = url.replace(" ", "_").lower()
    isUniqueName = checkForUniquePageName(store, body['name'])
    if isUniqueName:
        body["created_at"] = int(datetime.utcnow().timestamp())
        body["updated_at"] = ""
        body["url"] = url
        body["default_layout"] = {}
        body["versions"] = []
        body["preview_state"] = {}
        body['load_iframe'] = False
        body['is_customers_only'] = False
        body['status'] = "active"
        
        id = cms_db.create_webpage(store, body)
        response['message'] = "Page created successfully"
        response['status'] = 200
    else:
        response['message'] = "name: The provided page Name has already been matched with other pages. Please provide a different page Name."
        response['status'] = 409

    return response

def update_page(store, body, webpage_id=None):
    response = {
        "status": 400
    }
    url = body['name']
    # url = '/pages/' + url.replace(" ", "_").lower()
    url = url.replace(" ", "_").lower()

    current_record = cms_db.get_page_by_id(store, webpage_id)

    if current_record and current_record.get('name') == body['name']:
        # If the name is the same, skip the uniqueness check
        isUniqueName = True
    elif body['status_update'] == 'false':
        # Otherwise, perform the uniqueness check
        isUniqueName = checkForUniquePageName(store, body['name'])
    else:
        isUniqueName = True


    if isUniqueName:
        update_obj={
                    "name": body['name'],
                    "status": body['status'],
                    "url": url,
                    "updated_at":  int(datetime.utcnow().timestamp())
                }
        cms_db.update_webpage(store, {"_id": ObjectId(str(webpage_id))}, {"$set": update_obj})
        response['message'] = "Page Updated successfully"
        response['status'] = 200
    else:
        response['message'] = "name: The provided page Name has already been matched with other pages. Please provide a different page Name."
        response['status'] = 409

    return response

def update_customer_only_flag(store, body, webpage_id=None):
        response = {
            "status": 400
        }
        if webpage_id:
            update_obj={                                                                             
                        "is_customers_only": body['is_customers_only'],                                                                             
                        "updated_at":  int(datetime.utcnow().timestamp())
                    }
            cms_db.update_webpage(store, {"_id": ObjectId(str(webpage_id))}, {"$set": update_obj})
            # id = super().update_one({"_id": ObjectId(str(webpage_id))}, {"$set":
            #                                                              {                                                                             
            #                                                                  "is_customers_only": body['is_customers_only'],                                                                             
            #                                                                  "updated_at":  int(datetime.utcnow().timestamp())
            #                                                              }
            #                                                              })
            response['message'] = "Page Updated successfully"
            response['status'] = 200        

        return response

def set_version(store, body, webpage_id=None):
        response = {
            "status": 400
        }
        created_by = {}
        if 'created_by' in body:
            user = user_db.fetch_user_by_username(body['created_by'])        
            created_by = {
                "user_id": str(user['_id']),
                "user_name": user['name']
            }
        new_page_url = body["seo_details"]["page_url"]
        isUrlUnique = checkForUniqueUrl(store, new_page_url, webpage_id)
        if isUrlUnique:
            
            webPage = cms_db.get_page_by_id(store, webpage_id)
            # webPage = super().find_one({"_id": ObjectId(str(webpage_id))})
            totalVersions = len(webPage['versions'])
            versions = webPage['versions']

            if totalVersions > 0:
                lastVersion = webPage['versions'][-1]['version']
                for version in versions:
                    version['status'] = 'inactive'
                    seo_details = version["seo_details"]
                    seo_details["page_url"] = new_page_url
                    # for seo_detail in seo_details:
                        # Update the desired key-value pair
                        # seo_detail["page_url"] = new_page_url
            else:
                lastVersion = 0

            if (totalVersions >= 10):
                webPage['versions'].pop(0)

            body['created_by'] = created_by
            body['created_at'] = int(datetime.utcnow().timestamp())
            body['version'] = lastVersion + 1
            versions.append(body)

            update_obj={
                        "url": new_page_url,
                        "versions": webPage['versions'],
                        "updated_at":  int(datetime.utcnow().timestamp())
                    }
            cms_db.update_webpage(store, {"_id": ObjectId(str(webpage_id))}, {"$set": update_obj})

            # id = super().update_one({"_id": ObjectId(str(webpage_id))}, {"$set":
            #                                                              {
            #                                                                  "url": new_page_url,
            #                                                                  "versions": webPage['versions'],
            #                                                                  "updated_at":  int(datetime.utcnow().timestamp())
            #                                                              }})
            response['message'] = "changes saved sucessfuly"
            response['status'] = 200
        else:
            response['message'] = "The provided page URL has already been matched with other pages. Please provide a different page URL."
            response['status'] = 409
        return response

def get_webpage(store, webpage_id=None):
        result = {}
        webPage = cms_db.get_page_by_id(store, webpage_id)
        # webPage = super().find_one({"_id": ObjectId(str(webpage_id))})
        if (len(webPage['versions']) == 0):
            activeVersion = webPage['default_layout']
        else:
            versions = webPage['versions']
            for version in versions:
                if (version['status'] == 'active'):
                    activeVersion = version
                    break
        new_utils._process_document(webPage)
        result['id'] = webPage['id']
        result['name'] = webPage['name']
        result['created_at'] = webPage['created_at']
        result['updated_at'] = webPage['updated_at']
        result['url'] = webPage['url']
        result['is_customers_only'] = webPage['is_customers_only']
        result['versions'] = activeVersion

        return result

def set_page_preview(store, body, webpage_id=None):
        response = {
            "status": 400
        }
        webPage = cms_db.get_page_by_id(store, webpage_id)
        # webPage = super().find_one({"_id": ObjectId(str(webpage_id))})

        created_by = {}
        if 'created_by' in body:
            user = user_db.fetch_user_by_username(body['created_by'])        
            created_by = {
                "user_id": str(user['_id']),
                "user_name": user['name']
            }

        body['created_by'] = created_by
        body['created_at'] = int(datetime.utcnow().timestamp())

        webPage['preview_state'] = body

        update_obj={
                    "preview_state": webPage['preview_state'],
                    "updated_at":  int(datetime.utcnow().timestamp())
                }
        cms_db.update_webpage(store, {"_id": ObjectId(str(webpage_id))}, {"$set": update_obj})
        # id = super().update_one({"_id": ObjectId(str(webpage_id))}, {"$set":
        #                                                              {
        #                                                                  "preview_state": webPage['preview_state'],
        #                                                                  "updated_at":  int(datetime.utcnow().timestamp())
        #                                                              }})
        response['message'] = "changes saved sucessfuly"
        response['status'] = 200
        return response

def get_webpage_versions(store, webpage_id=None):
        webPage = cms_db.get_page_by_id(store, webpage_id)
        # webPage = super().find_one({"_id": ObjectId(str(webpage_id))})
        result = {}
        versionArray = []
        if (len(webPage['versions']) > 0):

            versions = webPage['versions']
            for version in versions:
                data = {}
                data['id'] = int(version['version'])
                if 'created_by' in version:
                    user_name = version['created_by'].get('user_name', '')
                    if user_name:
                        data['title'] = f"{version['version']} ({user_name})"
                    else:
                        data['title'] = str(version['version'])
                else:
                    data['title'] = str(version['version'])
                versionArray.append(data)

            result['webpageVersions'] = versionArray

        return result

def set_active_version(store, body, webpage_id=None):
        response = {
            "status": 400
        }
        webPage = cms_db.get_page_by_id(store, webpage_id)
        # webPage = super().find_one({"_id": ObjectId(str(webpage_id))})

        if (len(webPage['versions']) > 0):
            versions = webPage['versions']
            for version in versions:
                if (version['version'] == int(body['version'])):
                    version['status'] = 'active'
                else:
                    version['status'] = 'inactive'
        
        update_obj={
                    "versions": webPage['versions'],
                    "updated_at":  int(datetime.utcnow().timestamp())
                }
        cms_db.update_webpage(store, {"_id": ObjectId(str(webpage_id))}, {"$set": update_obj})
        # id = super().update_one({"_id": ObjectId(str(webpage_id))}, {"$set":
        #                                                              {
        #                                                                  "versions": webPage['versions'],
        #                                                                  "updated_at":  int(datetime.utcnow().timestamp())
        #                                                              }})

        if id:
            response['message'] = "version active status changed successfully!"
            response['status'] = 200
        return response

def get_webpage_versionData(store, body, webpage_id=None):
        result = {}
        webPage = cms_db.get_page_by_id(store, webpage_id)
        # webPage = super().find_one({"_id": ObjectId(str(webpage_id))})
        if (len(webPage['versions']) == 0):
            activeVersion = webPage['default_layout']
        else:
            versions = webPage['versions']
            for version in versions:
                if (version['version'] == int(body['version'])):
                    activeVersion = version
                    break
        new_utils._process_document(webPage)
        result['id'] = webPage['id']
        result['name'] = webPage['name']
        result['created_at'] = webPage['created_at']
        result['updated_at'] = webPage['updated_at']
        result['url'] = webPage['url']
        result['is_customers_only'] = webPage['is_customers_only']        
        result['versions'] = activeVersion

        return result

def delete_webpage_by_id(store, webpage_id):
        return cms_db.delete_webpage(store, webpage_id)
        # return super().delete({"_id": ObjectId(str(webpage_id))})

def setImage(body):
        response = {
            "status": 400
        }

        file = body.get('image')
        type = body.get('type')
        UPLOAD_FOLDER = os.path.join('images')

        if (file.filename == ''):
            response['message'] = "No image selected for uploading"
            response['status'] = 500

        if (file and allowed_file(file.filename)):
            newName = change_file_name(file.filename)
            fname = secure_filename(newName)

            if not os.path.exists(UPLOAD_FOLDER):
                os.makedirs(UPLOAD_FOLDER)

            file_path = os.path.join(UPLOAD_FOLDER, fname)

            file.save(file_path)

            # Check the file extension
            _, file_extension = os.path.splitext(file_path)
            file_extension = file_extension.lower()

            if file_extension in ['.png', '.jpg', '.jpeg', '.webp']:
                image = Image.open(file_path)
            
                original_width, original_height = image.size
                
                # Specify the desired width for each type
                desired_width = {
                    'top_ten_products': 200,
                    'full_width_banner': 1656,
                    'five_column_banner': 312,
                    'four_column_banner': 396,
                    'four_column_text_banner': 396
                }

                # Check if the type is in the desired_width dictionary
                if type in desired_width:
                    # Calculate the new height to maintain the aspect ratio
                    new_height = int((desired_width[type] / original_width) * original_height)
                    
                    # Resize the image
                    image = image.resize((desired_width[type], new_height))
                    image.save(file_path)
                else:
                    # If the type is not in the dictionary, save the image without resizing
                    image.save(file_path)

                base_path = os.path.join(os.path.abspath(os.getcwd()), UPLOAD_FOLDER, newName)
                
                if '/app/images' in base_path:
                    base_path = base_path.replace('/app/images', '')  
                response['message'] = base_path
                response['status'] = 200
            else:
                base_path = os.path.join(os.path.abspath(
                    os.getcwd()), UPLOAD_FOLDER, newName)
            
                if '/app/images' in base_path:
                    base_path = base_path.replace('/app/images', '')  
                
                response['message'] = base_path
                response['status'] = 200
        else:
            response['message'] = "Allowed image types are -> png, jpg, jpeg, gif, mp4, avi, mov, wmv, mkv"
            response['status'] = 500

        return response

def getImage(body):
    url = './images/' + body['image']
    if not os.path.exists(url):
        return make_response({'error': 'Image not found'}, 404)

    return send_file(url, as_attachment=True)

def get_bc_page_data(body):
        store_hash = body['store_hash']
        webpage_id = body['webpage_id']
        access_token = body['access_token']
        api_url = f"https://api.bigcommerce.com/stores/{store_hash}/v3/content/pages/{webpage_id}"
        headers = {
            "X-Auth-Token": access_token,
            "Accept": "application/json"

        }
        response = requests.get(api_url, headers=headers)
        temp_body = {}
        component_body = []

        if response.status_code == 200:
            webpage_data = response.json()
            variant_schema = {}

            variant_schema['id'] = '1'
            variant_schema['name'] = 'HTML Block'
            variant_schema['admin_layout'] = 'style1'
            variant_schema['class'] = [
                'test_globle_class', 'test_globle_class2']            
            variant_schema['config'] = {
                'data': webpage_data
            }
            component_body.append({
                'name': 'HTML Block',
                'code': 'html_block',
                'variant': variant_schema
            })

            temp_body['name'] = body['page_name']
            temp_body['status'] = body['status']
            temp_body['class'] = ''            
            temp_body['components'] = component_body

            return "Webpage data retrieved successfully"

        else:
            return f"Failed to retrieve webpage data. Status code: {response.status_code}"
        
def get_bc_data(store, body):
        result = {}
        products=bc_products.fetch_bc_product_by_sku(store,body['sku'])
        if products['data']:
            product=products['data'][0]
            result['id']=product['id']
            result['name']=product['name']
            result['sku']=product['sku']
            result['is_visible']=product['is_visible']
            result['page_title']=product['page_title']
            result['custom_url']=product['custom_url']
            result['thumbnail_image']=""
    
            for images in product['images']:
               if images['is_thumbnail']:
                  result['thumbnail_image']=images['url_thumbnail']
            
        return result

def create_bc_page(page, req_body,store):
        
        created_by = {}
        if 'created_by' in req_body:
            user = user_db.fetch_user_by_username(req_body['created_by'])        
            created_by = {
                "user_id": str(user['_id']),
                "user_name": user['name']
            }
        # Find in DB if available.
        web_page = cms_db.get_page_by_bc_id(store, page['id'])
        # web_page = super().find_one({"bc_id": page['id']})
        page_content = page['body']
        
        page_content = _parse_description(page_content)

        load_iframe = False
        if '<script type="text/javascript">' in page_content:
                load_iframe = True
   # If webpage not exists then create
        if not web_page:            
            generate_page(store, page, load_iframe,page_content,created_by)

        # If exists then check if it's modified or not if not then replace content
        if web_page:                   
            update_webpage(store, page, load_iframe,web_page['updated_at'],web_page,page_content,created_by)
            getNavigationForWebpages(page,store) 

def getNavigationForWebpages(page,store):
        navigations=cms_db.get_sub_nav(store)
        navigations=new_utils._process_list(navigations)
        for navigation in navigations:
            navigation['navigation_id']=ObjectId(navigation['navigation_id'])
            is_updated=False
            for data in navigation['navigations']:
                if data['type'] == 'Web Pages' and data['id'] == 'Web Pages'+'_'+str(page['id']):
                    is_updated=True
                    data['url']=page['url']
            if is_updated:
                cms_db.update_sub_nav_by_id(store,navigation)
        return
        
        
def generate_page(store, page, load_iframe,page_content,created_by):
        obj = {}
        obj['name'] = page['name']
        obj['bc_id'] = page['id']
        obj['is_visible'] = page['is_visible']
        obj["created_at"] = int(datetime.utcnow().timestamp())
        obj["updated_at"] = ""
        obj["url"] = page['url']
        obj["default_layout"] = {}
        obj["versions"] = [{
            'name': 'Current Bigcommerce Page',
            'status': 'active',
            'class': '',                
            'components': [{
                "name": 'HTML Block',
                'code': 'html_block',
                'variant': {
                    "id": '',
                    'name': 'HTML Block',
                    'admin_layout': 'style1',
                    'class': [],                        
                    'config': {
                        "data": page_content
                    }
                }
            }],
            'seo_details': {
                'page_name': page['name'],
                'page_url': page['url'],
                'meta_title': page['meta_title'],
                'meta_description': page['meta_description']
            },
            'created_by': created_by,
            'created_at': int(datetime.utcnow().timestamp()),
            'updated_at': '',
            'version': 1
        }]
        obj['load_iframe'] = load_iframe
        obj['is_customers_only'] = page['is_customers_only']
        obj["preview_state"] = {}

        return cms_db.create_webpage(store, obj)

def update_webpage(store, page, load_iframe,updated_at,web_page,page_content,created_by):
        web_page['updated_at'] == ''
        if updated_at=='':
            update_obj = {
            'name': page['name'],
            'is_visible': page['is_visible'],
            "url": page['url'],
            "load_iframe": load_iframe,
            "is_customers_only": page['is_customers_only'],
            "versions": [{
                'name': 'Current Bigcommerce Page',
                'status': 'active',
                'class': '',                    
                'components': [{
                        "name": 'HTML Block',
                        'code': 'html_block',
                        'variant': {
                            "id": '',
                            'name': 'HTML Block',
                            'admin_layout': 'style1',
                            'class': [],                                
                            'config': {
                                "data": page_content
                            }
                        }
                }],
                'seo_details': {
                    'page_name': page['name'],
                    'page_url': page['url'],
                    'meta_title': page['meta_title'],
                    'meta_description': page['meta_description']
                },
                'created_by': created_by,
                'created_at': int(datetime.utcnow().timestamp()),
                'updated_at': int(datetime.utcnow().timestamp()),
                'version': 1
            }]
            }
        else:
            for version in web_page['versions']:
                if version['status'] == 'active':
                    seo_details = version["seo_details"]
                    seo_details["page_url"] = page['url']
                    seo_details["page_name"]=page['name']
                    
            update_obj= {
                "url": page['url'],
                "name":page['name'],
                "versions": web_page['versions'],
                "updated_at":  int(datetime.utcnow().timestamp())
                    }
            
        return cms_db.update_webpage(store, {"bc_id": page['id']}, {"$set": update_obj})

def checkForUniquePageName(store, name):
        url = name
        # url = '/pages/' + url.replace(" ", "_").lower()
        url = url.replace(" ", "_").lower()
        pages = cms_db.get_all_webpages(store)
        # pages = super().find_all()
        # pages = parse_json(self.processList(pages))
        # pages = new_utils.parse_json(new_utils._process_list(pages))
        for page in pages:
            if page['name'] == name or page['url'] == url:
                return False
        return True 



def checkForUniqueUrl(store, url, webpage_id):
        pages = cms_db.get_all_webpages(store)
        pages = new_utils.parse_json(new_utils._process_list(pages))
        for page in pages:
            if not page['id'] == webpage_id:
                if page['url'] == url:
                    return False
        return True

def addCdnToImageUrl(cdn_baseurl, activeVersion): 
        if 'components' in activeVersion:   
            for component in activeVersion['components']:
                if 'variant' in component:
                    variant = component['variant']
                    if 'config' in variant:
                        config = variant['config']
                        if 'image_url' in config:
                            if config['image_url'] != '':
                                config['image_url'] = cdn_baseurl + '/banner' + config['image_url']
                        if 'mobile_image_url' in config:
                            if config['mobile_image_url'] != '':
                                config['mobile_image_url'] = cdn_baseurl + '/banner' + config['mobile_image_url']  

                        elif 'slider' in  config and 'side_images' in config:                            
                            sliders = config['slider']
                            side_images = config['side_images']
                            for slider in sliders:
                                if 'image_url' in slider:
                                    if slider['image_url'] != '':
                                        slider['image_url'] = cdn_baseurl + '/banner' + slider['image_url']

                            for side_image in side_images:
                                if 'image_url' in side_image:
                                    if side_image['image_url'] != '':
                                        side_image['image_url'] = cdn_baseurl + '/banner' + side_image['image_url']
                        elif 'banners' in  config:
                            banners = config['banners']
                            for banner in banners:
                                if 'image_url' in banner:
                                    if banner['image_url'] != '':
                                        banner['image_url'] = cdn_baseurl + '/banner' + banner['image_url'] 
                                if 'mobile_image_url' in banner:
                                    if banner['mobile_image_url'] != '':
                                        banner['mobile_image_url'] = cdn_baseurl + '/banner' + banner['mobile_image_url']                          
                        elif 'logos' in config:
                            logos = config['logos'] 
                            for logo in logos:
                                if 'image_url' in logo:
                                    if logo['image_url'] != '':
                                        logo['image_url'] = cdn_baseurl + '/banner' + logo['image_url']

        return activeVersion  

def _parse_description(description):
    soup = BeautifulSoup(description, 'html.parser')  
        
    # Define the replacements based on parent tags
    replacements = {
        'a': {
            '%%GLOBAL_ShopPathSSL%%': '',
            '%%GLOBAL_CdnStorePath%%': ''
        },
        'img': {
            '%%GLOBAL_ShopPathSSL%%': 'https://cdn11.bigcommerce.com/s-964anr',
            '%%GLOBAL_CdnStorePath%%': 'https://cdn11.bigcommerce.com/s-964anr'
        },
        'video': {
            '%%GLOBAL_ShopPathSSL%%': 'https://cdn11.bigcommerce.com/s-964anr',
            '%%GLOBAL_CdnStorePath%%': 'https://cdn11.bigcommerce.com/s-964anr'
        }
    }       

    for placeholder in ['%%GLOBAL_ShopPathSSL%%', '%%GLOBAL_CdnStorePath%%']:
        elements = soup.find_all(string=lambda text: text and placeholder in text)
        for elem in elements:
            parent = elem.find_parent()
            if parent and parent.name in replacements and placeholder in replacements[parent.name]:
                new_value = replacements[parent.name][placeholder]
                elem.replace_with(elem.replace(placeholder, new_value))

    description = soup.prettify()

    description = description.replace('<a href="%%GLOBAL_ShopPathSSL%%', '<a href="') 
    description = description.replace('<a href="%%GLOBAL_CdnStorePath%%', '<a href="') 
    description = description.replace('<a href="https://cdn11.bigcommerce.com/s-964anr', '<a href="') 

    return description

def add_url_type_to_images(store_id):
    db = new_mongodb.get_admin_db_client_for_store_id(store_id)
    result= db['component'].find({})

    for doc in result:
        updated = False
      
        for key in ['variants']:
            if key in doc:
                # versions is a list, others are dicts
                variants = doc[key]
                for variant in variants:
                    if 'config' in variant:
                        config = variant['config']    
                        # Direct image_url
                        if 'url' in config:
                            config['url_type'] = ''
                            updated = True
                        # slider and side_images
                        if 'slider' in config and isinstance(config['slider'], list):
                            for slider in config['slider']:
                                if 'url' in slider:
                                    slider['url_type'] = ''
                                    updated = True
                        if 'side_images' in config and isinstance(config['side_images'], list):
                            for side_image in config['side_images']:
                                if 'url' in side_image:
                                    side_image['url_type'] = ''
                                    updated = True
                        # banners
                        if 'banners' in config and isinstance(config['banners'], list):
                            for banner in config['banners']:
                                if 'url' in banner:
                                    banner['url_type'] = ''
                                    updated = True
                        # logos
                        if 'logos' in config and isinstance(config['logos'], list):
                            for logo in config['logos']:
                                if 'url' in logo:
                                    logo['url_type'] = ''
                                    updated = True
        # Save back if updated
        if updated:
            db['component'].update_one({"_id": doc['_id']}, {"$set": {"variants": variants}})
    print("Update complete.")

def add_url_type_to_config_urls_v2(store_id):
    db = new_mongodb.get_store_db_client_for_store_id(store_id)
    collection = db['pages']  # Change to your actual collection name if different

    for doc in collection.find({}):
        updated = False

        # Helper function to add url_type next to any 'url' key in a dict
        def add_url_type(obj):
            nonlocal updated
            if isinstance(obj, dict):
                if 'url' in obj and 'url_type' not in obj:
                    obj['url_type'] = ''
                    updated = True
            return obj

        # Traverse all versions, preview_state, and default_layout
        for key in ['versions', 'preview_state', 'default_layout']:
            if key in doc:
                items = doc[key] if isinstance(doc[key], list) else [doc[key]]
                for item in items:
                    if not item or 'components' not in item:
                        continue
                    for component in item['components']:
                        if 'variant' in component and 'config' in component['variant']:
                            config = component['variant']['config']
                            # Direct url
                            add_url_type(config)
                            # slider
                            if 'slider' in config and isinstance(config['slider'], list):
                                for slider in config['slider']:
                                    add_url_type(slider)
                            # side_images
                            if 'side_images' in config and isinstance(config['side_images'], list):
                                for side_image in config['side_images']:
                                    add_url_type(side_image)
                            # banners
                            if 'banners' in config and isinstance(config['banners'], list):
                                for banner in config['banners']:
                                    add_url_type(banner)
                            # logos
                            if 'logos' in config and isinstance(config['logos'], list):
                                for logo in config['logos']:
                                    add_url_type(logo)
        if updated:
            print(str(doc['_id']), "updated")
            collection.replace_one({'_id': doc['_id']}, doc)
    print("Update complete.")

    




                              
