from new_mongodb import fetch_one_document_from_storefront_collection
from plugin import bc_products
from sqlalchemy import text
import pg_db
import task
from utils.common import calculatePaginationData
import logging
from utils.common import convert_to_timestamp
from new_pgdb.analytics_db import AnalyticsDB

logger = logging.getLogger()


def get_bulk_order_customer_report(store, page, limit, search, customer_rep, tags, start_date, end_date, sort_array):
    response = {
        "status": 400,
    }
    conn = pg_db.get_connection()
    try:
        rep_name_condition = ""
        if customer_rep:
            rep_name_condition = f"AND scr.rep_email = '{customer_rep}'"

        search_condition = ""            
        if search and search != '':
            search = search.strip()
            if not search.isnumeric():
                search = search.replace(" ", "")
                search_condition += "AND (cu.first_name ILIKE '%" + search + "%' or cu.last_name ILIKE '%" + search + "%' or REPLACE(CONCAT(cu.first_name,' ',cu.last_name), ' ', '') ILIKE '%" + search + "%' or cu.email ILIKE '%" + search + "%')"

        tags_condition = ""
        if tags and tags != '':
            tags = tags.split(',')
            tags_list = [value.strip() for value in tags]  # Trim each value
            tags_list_str = "','".join(tags_list)
            tags_list_str = f"'{tags_list_str}'"
            tags_condition = f"AND gtm.tag_id IN ({tags_list_str})"
        
        date_condition = ""
        if start_date and end_date:
            date_condition = f"WHERE pbo.created_at >= '{start_date}' AND pbo.created_at <= '{end_date}'"
        
        count_query = f"""SELECT COUNT(*) FROM (WITH aggregated_orders AS (SELECT po.customer_id, COUNT(DISTINCT pbo.bc_order_id) AS order_count, COALESCE(SUM(pbo.order_total), 0) AS order_total,
                            MAX(pbo.created_at) AS last_order_date
                            FROM bo_purchase_orders po LEFT JOIN bo_purchase_order_bc_order_mapping pbo ON po.po_id = pbo.po_id
                            {date_condition}  GROUP BY po.customer_id),
                            lifetime_orders AS (
                                SELECT o.customer_id, COUNT(DISTINCT o.order_id) AS lifetime_order_count, COALESCE(SUM(o.total_including_tax), 0) AS lifetime_order_total
                                FROM orders o LEFT JOIN customers c ON o.customer_id = c.customer_id WHERE o.order_status_id NOT IN (0, 4, 5, 6, 7, 13) GROUP BY o.customer_id
                            )
                        SELECT cu.customer_id, cu.email, cu.first_name, cu.last_name, CONCAT(cu.first_name, ' ', cu.last_name) AS name, scr.rep_name, scr.rep_email,
                        STRING_AGG(DISTINCT gtm.tag_label, ', ') AS tag_labels,
                        COALESCE(ao.order_count, 0) AS order_count,
                        COALESCE(ao.order_total, 0) AS order_total, ao.last_order_date,
                        lo.lifetime_order_count, lo.lifetime_order_total
                        FROM customers cu
                        LEFT JOIN bo_generic_tags_mapping gtm ON gtm.entity_int_id = cu.customer_id AND gtm.entity_type = 'customer'
                        LEFT JOIN salesforce_customer_rep scr ON cu.customer_id = scr.customer_id
                        LEFT JOIN aggregated_orders ao ON cu.customer_id = ao.customer_id
                        LEFT JOIN lifetime_orders lo ON cu.customer_id = lo.customer_id
                        WHERE gtm.entity_type = 'customer' {search_condition} {rep_name_condition} {tags_condition}
                        GROUP BY cu.customer_id, cu.email, cu.first_name, cu.last_name, scr.rep_name, scr.rep_email, ao.order_count, ao.order_total, ao.last_order_date, lo.lifetime_order_count, lo.lifetime_order_total) AS subquery"""
        count_res = conn.execute(text(count_query))
        total_count = int(count_res.scalar())

        base_query = f"""WITH aggregated_orders AS (SELECT po.customer_id, COUNT(DISTINCT pbo.bc_order_id) AS order_count, COALESCE(SUM(pbo.order_total), 0) AS order_total,
                            MAX(pbo.created_at) AS last_order_date
                            FROM bo_purchase_orders po LEFT JOIN bo_purchase_order_bc_order_mapping pbo ON po.po_id = pbo.po_id
                            {date_condition}  GROUP BY po.customer_id
                        ),
                        lifetime_orders AS (
                            SELECT o.customer_id, COUNT(DISTINCT o.order_id) AS lifetime_order_count, COALESCE(SUM(o.total_including_tax), 0) AS lifetime_order_total
                            FROM orders o LEFT JOIN customers c ON o.customer_id = c.customer_id WHERE o.order_status_id NOT IN (0, 4, 5, 6, 7, 13) GROUP BY o.customer_id
                        )
                        SELECT cu.customer_id, cu.email, cu.first_name, cu.last_name, CONCAT(cu.first_name, ' ', cu.last_name) AS name, scr.rep_name, scr.rep_email,
                        STRING_AGG(DISTINCT gtm.tag_label, ', ') AS tag_labels,
                        COALESCE(ao.order_count, 0) AS order_count,
                        COALESCE(ao.order_total, 0) AS order_total, ao.last_order_date,
                        lo.lifetime_order_count, lo.lifetime_order_total
                        FROM customers cu
                        LEFT JOIN bo_generic_tags_mapping gtm ON gtm.entity_int_id = cu.customer_id AND gtm.entity_type = 'customer'
                        LEFT JOIN salesforce_customer_rep scr ON cu.customer_id = scr.customer_id
                        LEFT JOIN aggregated_orders ao ON cu.customer_id = ao.customer_id
                        LEFT JOIN lifetime_orders lo ON cu.customer_id = lo.customer_id
                        WHERE gtm.entity_type = 'customer' {search_condition} {rep_name_condition} {tags_condition}
                        GROUP BY cu.customer_id, cu.email, cu.first_name, cu.last_name, scr.rep_name, scr.rep_email, ao.order_count, ao.order_total, ao.last_order_date, lo.lifetime_order_count, lo.lifetime_order_total
                    """
        
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ['name', 'rep_name', 'order_count', 'order_total', 'last_order_date', 'lifetime_order_count', 'lifetime_order_total']:                
                if sort_direction == 'ASC':
                    base_query += f" ORDER BY {sort_array[0]} {sort_direction} NULLS FIRST"
                else:
                    base_query += f" ORDER BY {sort_array[0]} {sort_direction} NULLS LAST"

        offset = (page - 1) * limit
        if page and limit:
            base_query += f" LIMIT {limit} OFFSET {offset}"

        result = conn.execute(text(base_query))

        data = []
        for row in result.fetchall():
            products_data = {
                'customer_id': row[0],
                'email': row[1],
                'first_name': row[2],
                'last_name': row[3],
                'name': row[4],
                'rep_name': row[5],
                'rep_email': row[6],
                'tag_labels': row[7],
                'order_count': row[8],
                'order_total': row[9],
                'last_order_date': convert_to_timestamp(row[10]),
                "lifetime_order_count": row[11],
                "lifetime_order_total": round(row[12], 2) if row[12] else 0,
            }
            data.append(products_data)

        data = calculatePaginationData(data, page, limit, total_count)
        response['data'] = data
        response['status'] = 200
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response

def get_bulk_order_sales_rep_report(store, page, limit, search, sort_array):
    response = {
        "status": 400,
    }
    conn = pg_db.get_connection()
    try:
        search_condition = ""            
        if search and search != '':
            search = search.strip()
            search_condition = search_condition + "AND (scr.rep_name ILIKE '%" + search + "%')"
        
        count_query = f"""SELECT COUNT(*) FROM (WITH aggregated_orders AS (SELECT po.customer_id, COUNT(DISTINCT pbo.bc_order_id) AS order_count, SUM(pbo.order_total) AS order_total, MAX(pbo.created_at) AS last_order_date
							FROM bo_purchase_orders po
							LEFT JOIN bo_purchase_order_bc_order_mapping pbo ON po.po_id = pbo.po_id
							LEFT JOIN (SELECT DISTINCT customer_id FROM customers) c ON c.customer_id = po.customer_id
							LEFT JOIN (SELECT DISTINCT customer_id FROM orders WHERE order_status_id NOT IN (0, 4, 5, 6, 7, 13)) o ON o.customer_id = c.customer_id
							GROUP BY po.customer_id
                            ) 
                            SELECT scr.rep_name, scr.rep_email,
                            COUNT(DISTINCT ao.customer_id) AS total_customers, COALESCE(SUM(ao.order_count), 0) AS total_orders, 
                            COALESCE(SUM(ao.order_total), 0) AS total_order_value, MAX(ao.last_order_date) AS last_order_date
                            FROM salesforce_customer_rep scr 
                            INNER JOIN customers cu ON scr.customer_id = cu.customer_id
                            LEFT JOIN aggregated_orders ao ON cu.customer_id = ao.customer_id
                            WHERE scr.rep_name IS NOT NULL AND scr.rep_name != '' AND scr.rep_email IS NOT NULL AND scr.rep_email != ''
                            {search_condition}
                            GROUP BY scr.rep_name, scr.rep_email) AS subquery"""
        count_res = conn.execute(text(count_query))
        total_count = int(count_res.scalar())
    
        base_query = f"""WITH aggregated_orders AS (SELECT po.customer_id, COUNT(DISTINCT pbo.bc_order_id) AS order_count, SUM(pbo.order_total) AS order_total, MAX(pbo.created_at) AS last_order_date
							FROM bo_purchase_orders po
							LEFT JOIN bo_purchase_order_bc_order_mapping pbo ON po.po_id = pbo.po_id
							LEFT JOIN (SELECT DISTINCT customer_id FROM customers) c ON c.customer_id = po.customer_id
							LEFT JOIN (SELECT DISTINCT customer_id FROM orders WHERE order_status_id NOT IN (0, 4, 5, 6, 7, 13)) o ON o.customer_id = c.customer_id
							GROUP BY po.customer_id
                        ) 
                        SELECT scr.rep_name, scr.rep_email,
                        COUNT(DISTINCT ao.customer_id) AS total_customers, COALESCE(SUM(ao.order_count), 0) AS total_orders, 
                        COALESCE(SUM(ao.order_total), 0) AS total_order_value, MAX(ao.last_order_date) AS last_order_date
                        FROM salesforce_customer_rep scr 
                        INNER JOIN customers cu ON scr.customer_id = cu.customer_id
                        LEFT JOIN aggregated_orders ao ON cu.customer_id = ao.customer_id
                        WHERE scr.rep_name IS NOT NULL AND scr.rep_name != '' AND scr.rep_email IS NOT NULL AND scr.rep_email != ''
                        {search_condition}
                        GROUP BY scr.rep_name, scr.rep_email"""
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ['rep_name', 'total_customers', 'total_orders', 'total_order_value', 'last_order_date']:                
                if sort_direction == 'ASC':
                    base_query += f" ORDER BY {sort_array[0]} {sort_direction} NULLS FIRST"
                else:
                    base_query += f" ORDER BY {sort_array[0]} {sort_direction} NULLS LAST"

        offset = (page - 1) * limit
        if page and limit:
            base_query += f" LIMIT {limit} OFFSET {offset}"

        result = conn.execute(text(base_query))
        data = []
        for row in result.fetchall():
            report_data = {
                'rep_name': row[0],
                'rep_email': row[1],
                'total_customers': row[2],
                'total_orders': int(row[3]),
                'total_order_value': row[4],
                'last_order_date': convert_to_timestamp(row[5])
            }
            data.append(report_data)
        
        data = calculatePaginationData(data, page, limit, total_count)
        response['data'] = data
        response['status'] = 200
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response

def get_bulk_order_sales_rep_customers(page, limit, rep_email):
    response = {
        "status": 400,
    }
    conn = pg_db.get_connection()
    try:
        count_query = f"""SELECT COUNT(*) FROM (SELECT cu.customer_id, cu.email, CONCAT(cu.first_name, ' ', cu.last_name) AS name, STRING_AGG(DISTINCT gtm.tag_label, ', ') AS tag_labels
                        FROM customers cu
                        LEFT JOIN salesforce_customer_rep scr ON scr.customer_id = cu.customer_id
                        LEFT JOIN bo_generic_tags_mapping gtm ON gtm.entity_int_id = cu.customer_id AND gtm.entity_type = 'customer'
                        WHERE scr.rep_email = '{rep_email}'
                        AND cu.customer_id IN (
                            SELECT po.customer_id FROM bo_purchase_orders po
                            LEFT JOIN bo_purchase_order_bc_order_mapping pbo ON po.po_id = pbo.po_id GROUP BY po.customer_id
                        )
                        GROUP BY cu.customer_id, cu.email, cu.first_name, cu.last_name ORDER BY name) AS subquery"""
        count_res = conn.execute(text(count_query))
        total_count = int(count_res.scalar())
    
        base_query = f"""SELECT cu.customer_id, cu.email, CONCAT(cu.first_name, ' ', cu.last_name) AS name, STRING_AGG(DISTINCT gtm.tag_label, ', ') AS tag_labels
                        FROM customers cu
                        LEFT JOIN salesforce_customer_rep scr ON scr.customer_id = cu.customer_id
                        LEFT JOIN bo_generic_tags_mapping gtm ON gtm.entity_int_id = cu.customer_id AND gtm.entity_type = 'customer'
                        WHERE scr.rep_email = '{rep_email}'
                        AND cu.customer_id IN (
                            SELECT po.customer_id FROM bo_purchase_orders po
                            LEFT JOIN bo_purchase_order_bc_order_mapping pbo ON po.po_id = pbo.po_id GROUP BY po.customer_id
                        )
                        GROUP BY cu.customer_id, cu.email, cu.first_name, cu.last_name ORDER BY name"""

        offset = (page - 1) * limit
        if page and limit:
            base_query += f" LIMIT {limit} OFFSET {offset}"

        result = conn.execute(text(base_query))
        data = []
        for row in result.fetchall():
            customer_data = {
                'customer_id': row[0],
                'email': row[1],
                'name': row[2],
                'tag_labels': row[3]
            }
            data.append(customer_data)
        
        final_response = {
            "data": data,
            "meta": {
                "current_page": page,
                "next_page": page + 1 if offset + limit < total_count else None,
                "total_count": total_count
            }
        }
        response['data'] = final_response
        response['status'] = 200
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response

def get_bulk_order_by_product_report(store, page, limit, search, sort_array):
    response = {
        "status": 400,
    }
    conn = pg_db.get_connection()
    try:
        search_condition = ""            
        if search and search != '':
            search_condition = search_condition + "AND (bop.bc_name ILIKE '%" + search + "%' or bop.bc_sku ILIKE '%" + search + "%')"
        
        count_query = f"""SELECT COUNT(*) FROM (SELECT bop.bop_id, bop.bc_sku, bop.bc_product_id, bop.bc_name AS product_name, bop.product_image, bop.status, bop.type, bop.is_marketing_product,
                        COUNT(DISTINCT CASE WHEN NOT EXISTS (
                                SELECT 1 FROM bo_purchase_order_bc_order_mapping pbo_inner JOIN orders o_inner ON pbo_inner.bc_order_id::INT = o_inner.order_id 
                                WHERE pbo_inner.po_id = po.po_id AND o_inner.order_status_id NOT IN (0, 4, 5, 6, 7, 13)
                            ) THEN NULL
                            ELSE po.customer_id 
                        END) AS unique_customers, 
                        COUNT(DISTINCT CASE WHEN o.order_status_id NOT IN (0, 4, 5, 6, 7, 13) THEN pbo.bc_order_id END) AS total_order_count,
                        (SELECT SUM(pbo_inner.order_total) FROM bo_purchase_order_bc_order_mapping pbo_inner 
                            JOIN orders o_inner ON pbo_inner.bc_order_id::INT = o_inner.order_id 
                            WHERE pbo_inner.po_id IN (SELECT DISTINCT pol_inner.po_id FROM bo_purchase_order_lineitems pol_inner WHERE pol_inner.bop_id = bop.bop_id)
                            AND o_inner.order_status_id NOT IN (0, 4, 5, 6, 7, 13)
                        ) AS order_total, 
                        MAX(CASE WHEN o.order_status_id NOT IN (0, 4, 5, 6, 7, 13) THEN po.created_at END) AS last_order_date
                        FROM bo_bulk_order_products bop
                        LEFT JOIN bo_purchase_order_lineitems pol ON bop.bop_id = pol.bop_id
                        LEFT JOIN bo_purchase_orders po ON pol.po_id = po.po_id
                        LEFT JOIN bo_purchase_order_bc_order_mapping pbo ON po.po_id = pbo.po_id
                        LEFT JOIN orders o ON pbo.bc_order_id::INT = o.order_id
                        WHERE bop.status IN ('inactive', 'active', 'draft') AND bop.type IN ('bulkorder', 'preorder') {search_condition}
                        GROUP BY bop.bop_id, bop.bc_sku, bop.bc_product_id, bop.bc_name, bop.product_image, bop.status, bop.type, bop.is_marketing_product) AS subquery;"""
        count_res = conn.execute(text(count_query))
        total_count = int(count_res.scalar())
        
        base_query = f"""SELECT bop.bop_id, bop.bc_sku, bop.bc_product_id, bop.bc_name AS product_name, bop.product_image, bop.status, bop.type, bop.is_marketing_product,
                        COUNT(DISTINCT CASE WHEN NOT EXISTS (
                                SELECT 1 FROM bo_purchase_order_bc_order_mapping pbo_inner JOIN orders o_inner ON pbo_inner.bc_order_id::INT = o_inner.order_id 
                                WHERE pbo_inner.po_id = po.po_id AND o_inner.order_status_id NOT IN (0, 4, 5, 6, 7, 13)
                            ) THEN NULL
                            ELSE po.customer_id 
                        END) AS unique_customers, 
                        COUNT(DISTINCT CASE WHEN o.order_status_id NOT IN (0, 4, 5, 6, 7, 13) THEN pbo.bc_order_id END) AS total_order_count,
                        (SELECT SUM(pbo_inner.order_total) FROM bo_purchase_order_bc_order_mapping pbo_inner 
                            JOIN orders o_inner ON pbo_inner.bc_order_id::INT = o_inner.order_id 
                            WHERE pbo_inner.po_id IN (SELECT DISTINCT pol_inner.po_id FROM bo_purchase_order_lineitems pol_inner WHERE pol_inner.bop_id = bop.bop_id)
                            AND o_inner.order_status_id NOT IN (0, 4, 5, 6, 7, 13)
                        ) AS order_total, 
                        MAX(CASE WHEN o.order_status_id NOT IN (0, 4, 5, 6, 7, 13) THEN po.created_at END) AS last_order_date
                        FROM bo_bulk_order_products bop
                        LEFT JOIN bo_purchase_order_lineitems pol ON bop.bop_id = pol.bop_id
                        LEFT JOIN bo_purchase_orders po ON pol.po_id = po.po_id
                        LEFT JOIN bo_purchase_order_bc_order_mapping pbo ON po.po_id = pbo.po_id
                        LEFT JOIN orders o ON pbo.bc_order_id::INT = o.order_id
                        WHERE bop.status IN ('inactive', 'active', 'draft') AND bop.type IN ('bulkorder', 'preorder') {search_condition}
                        GROUP BY bop.bop_id, bop.bc_sku, bop.bc_product_id, bop.bc_name, bop.product_image, bop.status, bop.type, bop.is_marketing_product"""
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ['product_name', 'bc_sku', 'unique_customers', 'total_order_count', 'order_total', 'last_order_date']:                
                if sort_direction == 'ASC':
                    base_query += f" ORDER BY {sort_array[0]} {sort_direction} NULLS FIRST"
                else:
                    base_query += f" ORDER BY {sort_array[0]} {sort_direction} NULLS LAST"

        offset = (page - 1) * limit
        if page and limit:
            base_query += f" LIMIT {limit} OFFSET {offset}"
        
        result = conn.execute(text(base_query))
        data = []
        for row in result.fetchall():
            image_url = row[4]
            if row[6] == 'bulkorder':
                query = {
                    "id": int(row[2]),   
                }
                projection = {
                    "images": {
                        "$elemMatch": {
                            "is_thumbnail": True
                        }
                    }
                }
                res = fetch_one_document_from_storefront_collection(store['id'], 'products', query, projection)
                if res:
                    images = res.get('images', [])
                    if len(images) > 0:
                        image = images[0].get('url_thumbnail', '')
                        if image != '':
                            image_url = image
            report_data = {
                "bop_id": row[0],
                "bc_sku": row[1],
                "bc_product_id": row[2],
                "product_name": row[3],
                "product_image": image_url,
                "status": row[5],
                "type": row[6],
                "is_marketing_product": row[7],
                "unique_customers": row[8],
                "total_order_count": row[9],
                "order_total": row[10],
                "last_order_date": convert_to_timestamp(row[11])
            }
            data.append(report_data)
        
        data = calculatePaginationData(data, page, limit, total_count)
        response['data'] = data
        response['status'] = 200
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response

def get_bulk_product_customer_tracking_report(store, bop_id, page, limit, search, customer_rep, tags, sort_array, state):
    response = {
        "status": 400,
    }
    conn = pg_db.get_connection()
    try:
        # get all the variants of product
        total_variant_query = f"""SELECT COUNT(DISTINCT v.variants_id) AS total_variants FROM variants v
                                INNER JOIN bo_bulk_order_products bop ON v.product_id = bop.bc_product_id WHERE bop.bop_id = {bop_id}"""
        total_variant_res = conn.execute(text(total_variant_query)).scalar()
        total_variant_count = str(total_variant_res) if total_variant_res else "0"

        # Conditions to filter the query
        conditions = []

        if customer_rep:
            conditions.append(f"scr.rep_email = '{customer_rep}'")

        if search:
            search = search.strip().replace(" ", "")
            conditions.append(f"(cu.first_name ILIKE '%{search}%' OR cu.last_name ILIKE '%{search}%' OR REPLACE(CONCAT(cu.first_name, ' ', cu.last_name), ' ', '') ILIKE '%{search}%' OR cu.email ILIKE '%{search}%')")

        if tags:
            tags_list = [value.strip() for value in tags.split(',')]
            tags_str = "','".join(tags_list)
            conditions.append(f"gtm.tag_id IN ('{tags_str}')")
        
        if state:
            state = state.strip()
            conditions.append(f"ca.state = '{state}'")

        where_clause = f"AND {' AND '.join(conditions)}" if conditions else ""
                
        
        # Optimized count query with the necessary aggregations
        count_query = f"""WITH UniqueOrders AS (
                            SELECT DISTINCT pbo.bc_order_id, po.customer_id
                            FROM bo_purchase_order_bc_order_mapping pbo
                            INNER JOIN bo_purchase_orders po ON po.po_id = pbo.po_id
                            INNER JOIN bo_purchase_order_lineitems pol ON pol.po_id = po.po_id
                            INNER JOIN order_line_items ol ON pbo.bc_order_id::INT = ol.order_id
                            INNER JOIN bo_bulk_order_products bop ON ol.product_id = bop.bc_product_id
                            WHERE bop.bop_id = {bop_id} AND pol.fullfilled_qty > 0 
                            AND (pol.status = 'completed' OR (pol.status = 'cancelled' AND pol.fullfilled_qty > 0))
                        ),
                        open_orders AS (
                            SELECT po.customer_id
                            FROM bo_purchase_orders po
                            INNER JOIN bo_purchase_order_lineitems pol ON pol.po_id = po.po_id
                            WHERE pol.bop_id = {bop_id} AND po.status NOT IN ('deleted', 'completed') AND (pol.status IS NULL OR pol.status NOT IN ('cancelled', 'completed'))
                            GROUP BY po.customer_id
                        ),
                        aggregated_orders AS (
                            SELECT uo.customer_id FROM UniqueOrders uo GROUP BY uo.customer_id
                        ),
                        bulk_total_revenue AS (
                            SELECT uo.customer_id
                            FROM UniqueOrders uo
                            INNER JOIN orders o ON uo.bc_order_id::INT = o.order_id
                            INNER JOIN order_line_items ol ON o.order_id = ol.order_id
                            INNER JOIN bo_bulk_order_products bop ON ol.product_id = bop.bc_product_id
                            WHERE bop.bop_id = {bop_id} GROUP BY uo.customer_id
                        ),
                        lifetime_product_orders AS (
                            SELECT o.customer_id
                            FROM orders o
                            INNER JOIN order_line_items ol ON o.order_id = ol.order_id
                            INNER JOIN bo_bulk_order_products bop ON ol.product_id = bop.bc_product_id
                            WHERE bop.bop_id = {bop_id} AND o.order_status_id NOT IN (0, 4, 5, 6, 7, 13) GROUP BY o.customer_id
                        ),
                        lifetime_total_orders AS (
                            SELECT o.customer_id FROM orders o GROUP BY o.customer_id
                        ),
                        customer_addresses AS (
                            SELECT DISTINCT ON (customer_id) customer_id, state
                            FROM customer_addresses
                            ORDER BY customer_id, customer_address_id DESC
                        )
                        SELECT COUNT(*)
                        FROM customers cu
                        LEFT JOIN customer_addresses ca ON ca.customer_id = cu.customer_id
                        LEFT JOIN bo_generic_tags_mapping gtm ON gtm.entity_int_id = cu.customer_id AND gtm.entity_type = 'customer'
                        LEFT JOIN salesforce_customer_rep scr ON cu.customer_id = scr.customer_id
                        LEFT JOIN aggregated_orders ao ON cu.customer_id = ao.customer_id
                        LEFT JOIN lifetime_product_orders lpo ON cu.customer_id = lpo.customer_id
                        LEFT JOIN lifetime_total_orders lto ON cu.customer_id = lto.customer_id
                        LEFT JOIN open_orders oo ON cu.customer_id = oo.customer_id
                        LEFT JOIN bulk_total_revenue btr ON cu.customer_id = btr.customer_id
                        WHERE gtm.entity_type = 'customer' {where_clause} """
        count_res = conn.execute(text(count_query)).scalar()
        total_count = int(count_res) if count_res else 0
        
        base_query = f"""WITH UniqueOrders AS (
                            SELECT DISTINCT pbo.bc_order_id, pbo.created_at, po.customer_id
                            FROM bo_purchase_order_bc_order_mapping pbo
                            JOIN bo_purchase_orders po ON po.po_id = pbo.po_id
                            JOIN bo_purchase_order_lineitems pol ON pol.po_id = po.po_id
                            JOIN order_line_items ol ON pbo.bc_order_id::INT = ol.order_id
                            JOIN bo_bulk_order_products bop ON ol.product_id = bop.bc_product_id
                            WHERE bop.bop_id = {bop_id} AND pol.fullfilled_qty > 0 
                            AND (pol.status = 'completed' OR (pol.status = 'cancelled' AND pol.fullfilled_qty > 0))
                        ),
                        open_orders AS (
                            SELECT po.customer_id, COUNT(DISTINCT po.po_id) AS open_orders
                            FROM bo_purchase_orders po
                            JOIN bo_purchase_order_lineitems pol ON pol.po_id = po.po_id
                            WHERE pol.bop_id = {bop_id} AND po.status NOT IN ('deleted', 'completed') 
                            AND (pol.status IS NULL OR pol.status NOT IN ('cancelled', 'completed'))
                            GROUP BY po.customer_id
                        ),
                        aggregated_orders AS (
                            SELECT customer_id, COUNT(bc_order_id) AS order_count, MAX(created_at) AS last_order_date
                            FROM UniqueOrders GROUP BY customer_id
                        ),
                        bulk_total_revenue AS (
                            SELECT uo.customer_id, COALESCE(SUM(ol.total_inc_tax), 0) AS order_total
                            FROM UniqueOrders uo
                            JOIN orders o ON uo.bc_order_id::INT = o.order_id
                            JOIN order_line_items ol ON o.order_id = ol.order_id
                            JOIN bo_bulk_order_products bop ON ol.product_id = bop.bc_product_id
                            WHERE bop.bop_id = {bop_id} GROUP BY uo.customer_id
                        ),
                        lifetime_product_orders AS (
                            SELECT o.customer_id, COUNT(DISTINCT o.order_id) AS product_order_count, COALESCE(SUM(ol.total_inc_tax), 0) AS product_order_total, 
                                MAX(o.order_created_date_time) AS product_last_order_date, COUNT(DISTINCT ol.variant_id) AS purchased_flavours
                            FROM orders o
                            JOIN order_line_items ol ON o.order_id = ol.order_id
                            JOIN bo_bulk_order_products bop ON ol.product_id = bop.bc_product_id
                            WHERE bop.bop_id = {bop_id} AND o.order_status_id NOT IN (0, 4, 5, 6, 7, 13) GROUP BY o.customer_id
                        ),
                        lifetime_total_orders AS (
                            SELECT customer_id, COUNT(DISTINCT o.order_id) AS lifetime_order_count, COALESCE(SUM(o.total_including_tax), 0) AS lifetime_order_total
                            FROM orders o GROUP BY customer_id
                        ),
                        customer_addresses AS (
                            SELECT DISTINCT ON (customer_id) customer_id, state 
                            FROM customer_addresses 
                            ORDER BY customer_id, customer_address_id DESC
                        )
                        SELECT 
                            cu.customer_id, cu.email, cu.first_name, cu.last_name, CONCAT(cu.first_name, ' ', cu.last_name) AS name, 
                            scr.rep_name, scr.rep_email, STRING_AGG(DISTINCT gtm.tag_label, ', ') AS tag_labels, 
                            COALESCE(ao.order_count, 0) AS order_count, COALESCE(btr.order_total, 0) AS order_total, 
                            ca.state, COALESCE(lpo.product_order_count, 0) AS product_order_count, COALESCE(lpo.product_order_total, 0) AS product_order_total, 
                            lpo.product_last_order_date, COALESCE(oo.open_orders, 0) AS open_orders, 
                            COALESCE(lto.lifetime_order_count, 0) AS lifetime_order_count, 
                            COALESCE(lto.lifetime_order_total, 0) AS lifetime_order_total, 
                            COALESCE(lpo.purchased_flavours, 0) AS purchased_flavours,
                            cu.company AS company_name
                        FROM customers cu
                        LEFT JOIN customer_addresses ca ON cu.customer_id = ca.customer_id
                        LEFT JOIN bo_generic_tags_mapping gtm ON gtm.entity_int_id = cu.customer_id AND gtm.entity_type = 'customer'
                        LEFT JOIN salesforce_customer_rep scr ON cu.customer_id = scr.customer_id
                        LEFT JOIN aggregated_orders ao ON cu.customer_id = ao.customer_id
                        LEFT JOIN lifetime_product_orders lpo ON cu.customer_id = lpo.customer_id
                        LEFT JOIN lifetime_total_orders lto ON cu.customer_id = lto.customer_id
                        LEFT JOIN open_orders oo ON cu.customer_id = oo.customer_id
                        LEFT JOIN bulk_total_revenue btr ON cu.customer_id = btr.customer_id
                        WHERE gtm.entity_type = 'customer' {where_clause}
                        GROUP BY cu.customer_id, cu.email, cu.first_name, cu.last_name, ca.state, scr.rep_name, scr.rep_email, ao.order_count, btr.order_total, 
                            lpo.product_order_count, lpo.product_order_total, lpo.product_last_order_date, oo.open_orders, lto.lifetime_order_count, lto.lifetime_order_total, lpo.purchased_flavours """
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ['name', 'rep_name', 'order_count', 'product_last_order_date', 'tag_labels', 'order_total', 'state', 'lifetime_order_count', 'lifetime_order_total', 'open_orders', 'product_order_count', 'product_order_total', 'purchased_flavours', 'company_name']:                
                if sort_direction == 'ASC':
                    base_query += f" ORDER BY {sort_array[0]} {sort_direction} NULLS FIRST"
                else:
                    base_query += f" ORDER BY {sort_array[0]} {sort_direction} NULLS LAST"

        offset = (page - 1) * limit
        if page and limit:
            base_query += f" LIMIT {limit} OFFSET {offset}"

        result = conn.execute(text(base_query))

        data = []
        for row in result.fetchall():         
            products_data = {
                'customer_id': row[0],
                "email": row[1],
                "first_name": row[2],
                "last_name": row[3],
                "name": row[4],
                "rep_name": row[5],
                "rep_email": row[6],
                "tag_labels": row[7],
                "order_count": row[8],
                "order_total": round(row[9], 2) if row[9] else 0,
                "state": row[10],
                "product_order_count": row[11],
                "product_order_total": round(row[12], 2) if row[12] else 0,
                "product_last_order_date": convert_to_timestamp(row[13]),
                "open_orders": row[14],
                "lifetime_order_count": row[15],
                "lifetime_order_total": round(row[16], 2) if row[16] else 0,
                "purchased_flavours": (str(row[17]) if row[17] else "0") + '/' + str(total_variant_count),   
                "company_name": row[18]            
            }
            data.append(products_data) 

        data = calculatePaginationData(data, page, limit, total_count)
        response['data'] = data
        response['status'] = 200
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response

def product_customer_tracking_csv(store, query_params):
    response = {
        "status": 400,
    }
    if query_params:
        task_id = task.send_task(task.PRODUCT_CUSTOMER_TRACKING_CSV_MAIL_TASK, args=(store['id'], query_params))
        if task_id:
            response['status'] = 200
            response['message'] = f"We are generating the data you requested for download as a CSV file. This process may take a few minutes. Once complete, the CSV will be sent to your email: {query_params['username']}."
        else:
            response['status'] = 400
            response['message'] = 'Something went wrong to generate the CSV file.'
    else:
        response['status'] = 400
        response['message'] = 'query params are required'

    return response

def get_suggested_customer_report(store, page, limit, search, customer_rep, sort_array):
    response = {
        "status": 400,
    }
    conn = pg_db.get_connection()
    try:
        rep_name_condition = ""
        if customer_rep:
            rep_name_condition = f"AND scr.rep_email = '{customer_rep}'"

        search_condition = ""            
        if search and search != '':
            search = search.strip()
            if not search.isnumeric():
                search = search.replace(" ", "")
                search_condition += "AND (c.first_name ILIKE '%" + search + "%' or c.last_name ILIKE '%" + search + "%' or REPLACE(CONCAT(c.first_name,' ',c.last_name), ' ', '') ILIKE '%" + search + "%' or c.email ILIKE '%" + search + "%')"

        
        count_query = f"""SELECT COUNT(*) FROM (WITH lifetime_orders AS (
                            SELECT o.customer_id, COUNT(DISTINCT o.order_id) AS lifetime_order_count, COALESCE(SUM(o.total_including_tax), 0) AS lifetime_order_total, MAX(o.order_created_date_time) AS last_order_date
                            FROM orders o LEFT JOIN customers c ON o.customer_id = c.customer_id WHERE o.order_status_id NOT IN (0, 4, 5, 6, 7, 13) GROUP BY o.customer_id
                            )
                            SELECT DISTINCT c.customer_id, c.first_name, c.last_name, c.email, CONCAT(c.first_name, ' ', c.last_name) AS name, c.company, scr.rep_name, scr.rep_email, 
                            lo.last_order_date, lo.lifetime_order_count, lo.lifetime_order_total, ca.state
                            FROM customers c
                            LEFT JOIN (
                                SELECT DISTINCT ON (customer_id) customer_id, state 
                                FROM customer_addresses ORDER BY customer_id, customer_address_id DESC
                            ) AS ca ON ca.customer_id = c.customer_id
                            LEFT JOIN salesforce_customer_rep scr ON c.customer_id = scr.customer_id
                            LEFT JOIN lifetime_orders lo ON c.customer_id = lo.customer_id
                            WHERE c.customer_id in (
                                SELECT distinct(an.customer_id) AS customer_id FROM {AnalyticsDB.get_products_trend_table()} an
								JOIN bo_bulk_order_products bo ON an.product_id = bo.bc_product_id
								WHERE an.order_date_time >= now() - INTERVAL '90 DAYS'
								GROUP BY an.order_id, an.product_id, an.customer_id
								HAVING SUM(an.total) >= 10000
                                Union
                                SELECT distinct(scr.customer_id) AS customer_id FROM salesforce_customer_rep scr WHERE scr.type = 'Distributor'
                                Union
								SELECT distinct(an.customer_id) AS customer_id FROM {AnalyticsDB.get_products_trend_table()} an GROUP BY an.customer_id HAVING SUM(an.total) >= 5000
                            )
                            AND c.customer_id not in (SELECT distinct(entity_int_id) from bo_generic_tags_mapping where entity_type = 'customer') {search_condition} {rep_name_condition}) AS subquery"""
        count_res = conn.execute(text(count_query))
        total_count = int(count_res.scalar())

        base_query = f"""WITH lifetime_orders AS (
                            SELECT o.customer_id, COUNT(DISTINCT o.order_id) AS lifetime_order_count, COALESCE(SUM(o.total_including_tax), 0) AS lifetime_order_total, MAX(o.order_created_date_time) AS last_order_date
                            FROM orders o LEFT JOIN customers c ON o.customer_id = c.customer_id WHERE o.order_status_id NOT IN (0, 4, 5, 6, 7, 13) GROUP BY o.customer_id
                            )
                            SELECT DISTINCT c.customer_id, c.first_name, c.last_name, c.email, CONCAT(c.first_name, ' ', c.last_name) AS name, c.company, scr.rep_name, scr.rep_email, 
                            lo.last_order_date, lo.lifetime_order_count, lo.lifetime_order_total, ca.state
                            FROM customers c
                            LEFT JOIN (
                                SELECT DISTINCT ON (customer_id) customer_id, state 
                                FROM customer_addresses ORDER BY customer_id, customer_address_id DESC
                            ) AS ca ON ca.customer_id = c.customer_id
                            LEFT JOIN salesforce_customer_rep scr ON c.customer_id = scr.customer_id
                            LEFT JOIN lifetime_orders lo ON c.customer_id = lo.customer_id
                            WHERE c.customer_id in (
                                SELECT distinct(an.customer_id) AS customer_id FROM {AnalyticsDB.get_products_trend_table()} an
								JOIN bo_bulk_order_products bo ON an.product_id = bo.bc_product_id
								WHERE an.order_date_time >= now() - INTERVAL '90 DAYS'
								GROUP BY an.order_id, an.product_id, an.customer_id
								HAVING SUM(an.total) >= 10000
                                Union
                                SELECT distinct(scr.customer_id) AS customer_id FROM salesforce_customer_rep scr WHERE scr.type = 'Distributor'
                                Union
								SELECT distinct(an.customer_id) AS customer_id FROM {AnalyticsDB.get_products_trend_table()} an GROUP BY an.customer_id HAVING SUM(an.total) >= 5000
                            )
                            AND c.customer_id not in (SELECT distinct(entity_int_id) from bo_generic_tags_mapping where entity_type = 'customer') {search_condition} {rep_name_condition}"""
        
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ['name', 'rep_name', 'last_order_date', 'lifetime_order_count', 'lifetime_order_total', 'state']:                
                if sort_direction == 'ASC':
                    base_query += f" ORDER BY {sort_array[0]} {sort_direction} NULLS FIRST"
                else:
                    base_query += f" ORDER BY {sort_array[0]} {sort_direction} NULLS LAST"

        offset = (page - 1) * limit
        if page and limit:
            base_query += f" LIMIT {limit} OFFSET {offset}"

        result = conn.execute(text(base_query))

        data = []
        for row in result.fetchall():
            products_data = {
                'customer_id': row[0],
                'first_name': row[1],
                'last_name': row[2],
                'email': row[3],
                'name': row[4],
                'company': row[5],
                'rep_name': row[6],
                'rep_email': row[7],
                'last_order_date': convert_to_timestamp(row[8]),
                'lifetime_order_count': row[9],
                'lifetime_order_total': round(row[10], 2) if row[10] else 0,
                'state': row[11]
            }
            data.append(products_data)

        data = calculatePaginationData(data, page, limit, total_count)
        response['data'] = data
        response['status'] = 200
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response

def get_customers_purchased_flavours(store, bop_id, customer_id):
    response = {
        "status": 400,
    }
    conn = pg_db.get_connection()
    try:
        
        # get all the variants of product
        total_variant_query = f"""SELECT DISTINCT v.variant_options, v.variants_id AS purchased_flavours, v.variants_sku, bop.bc_sku, bop.case_qty FROM variants v
                                    INNER JOIN bo_bulk_order_products bop ON v.product_id = bop.bc_product_id
                                    WHERE bop.bop_id = {bop_id}"""
        total_variant_res = conn.execute(text(total_variant_query)).fetchall()
        
        current_inventory_level = {}
        bc_sku = total_variant_res[0][3]
        case_qty = total_variant_res[0][4] if total_variant_res[0][4] else 0
        if bc_sku:
            product_data = bc_products.fetch_bc_product_by_sku(store, [bc_sku])
            if product_data:
                if 'data' in product_data:
                    product = product_data['data'][0]
                    for variant in product['variants']:
                        available_to_sell = variant['inventory_level']
                        current_inventory_level[variant['id']] = round(available_to_sell / case_qty, 2) if available_to_sell and case_qty > 0 else available_to_sell / case_qty if available_to_sell and case_qty > 0 else 0


        #get list of purchased flavour for customer
        base_query = f"""WITH purchased_varints_count AS (
                            SELECT DISTINCT ol.variant_id AS variant_id, SUM(ol.quantity) AS total_purchased_qty
                            FROM orders o
                            INNER JOIN order_line_items ol ON o.order_id = ol.order_id
                            WHERE o.customer_id = {customer_id} AND o.order_status_id NOT IN (0, 4, 5, 6, 7, 13)  GROUP BY ol.variant_id
                        )
                        SELECT distinct v.variant_options, v.variants_id, pvc.total_purchased_qty from variants v 
                        LEFT JOIN purchased_varints_count pvc ON pvc.variant_id = v.variants_id
                        WHERE variants_id IN (
                        SELECT ol.variant_id AS purchased_flavours 
                        FROM orders o
                        INNER JOIN order_line_items ol ON o.order_id = ol.order_id
                        INNER JOIN bo_bulk_order_products bop ON ol.product_id = bop.bc_product_id
                        INNER JOIN variants v ON ol.product_id = v.product_id
                        WHERE bop.bop_id = {bop_id} AND o.customer_id = {customer_id}) ORDER BY variant_options ASC"""
        purchased_qty_res  = conn.execute(text(base_query)).fetchall()

        # Create a dictionary for easy lookup of purchased quantities by variant_id
        purchased_qty_dict = {row[1]: row[2] if row[2] is not None else 0 for row in purchased_qty_res}

        # Prepare final result by matching `variant_id` and adding `total_purchased_qty`
        final_result = []
        for row in total_variant_res:
            variant_id = row[1]
            variant_options = row[0]
            total_purchased_qty = purchased_qty_dict.get(variant_id, 0)  # Default to 0 if not found
            final_result.append({
                'variant_id': variant_id,
                'variant_options': variant_options,
                'total_purchased_qty': total_purchased_qty,
                'variants_sku': row[2],
                'current_inventory': current_inventory_level.get(variant_id, 0)
            })
        
        response["status"] = 200
        response["data"] = final_result

    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response

def get_product_sold_customer_states(store, bop_id):
    response = {
        "status": 400,
    }
    states = []
    conn = pg_db.get_connection()
    try:
        query = f"""SELECT DISTINCT sh.state FROM order_shipping_addresses sh 
                INNER JOIN {AnalyticsDB.get_products_trend_table()} ap ON ap.order_id = sh.order_id
                INNER JOIN bo_bulk_order_products bop ON ap.product_id = bop.bc_product_id
                WHERE bop.bop_id = {bop_id}"""
       
        results = conn.execute(text(query)).fetchall()
        states = [result[0] for result in results if result[0] is not None and result[0] != '']

        response["status"] = 200
        response["data"] = states
    finally:
        if conn:
            conn.close()

    return response

def get_bulk_products_gloabl_report(store, page, limit, search, customer_rep, bop_ids, sort_array, state, sorting_bop_id):
    response = {
        "status": 400,
    }
    conn = pg_db.get_connection()
    try:
        customer_data = []
        product_map = {}
        all_products = []
       
        # Conditions to filter the query
        conditions = []
        if customer_rep:
            conditions.append(f"scr.rep_email = '{customer_rep}'")
        if search:
            search = search.strip().replace(" ", "")
            conditions.append(f"(cu.first_name ILIKE '%{search}%' OR cu.last_name ILIKE '%{search}%' OR REPLACE(CONCAT(cu.first_name, ' ', cu.last_name), ' ', '') ILIKE '%{search}%' OR cu.email ILIKE '%{search}%')")
        if state:
            state = state.strip()
            conditions.append(f"ca.state = '{state}'")

        bop_ids_list = None
        if bop_ids:
            bop_ids_list = [int(value.strip()) for value in bop_ids.split(';')]

        where_clause = f"AND {' AND '.join(conditions)}" if conditions else ""

        # conditional append queries for sorting based on product fields
        append_columns = ""
        conditional_query = ""
        conditional_join = ""
        if sorting_bop_id:
            conditional_query = f""", lifetime_product_orders AS (
                            SELECT o.customer_id, COUNT(DISTINCT o.order_id) AS product_order_count, COALESCE(SUM(ol.total_inc_tax), 0) AS product_order_total, 
                                MAX(o.order_created_date_time) AS product_last_order_date, COUNT(DISTINCT ol.variant_id) AS purchased_flavours
                            FROM orders o
                            JOIN order_line_items ol ON o.order_id = ol.order_id
                            JOIN bo_bulk_order_products bop ON ol.product_id = bop.bc_product_id
                            WHERE bop.bop_id = {sorting_bop_id} AND o.order_status_id NOT IN (0, 4, 5, 6, 7, 13) GROUP BY o.customer_id
                        )"""
            append_columns = f""", COALESCE(lpo.product_order_count, 0) AS product_order_count, COALESCE(lpo.product_order_total, 0) AS product_order_total, lpo.product_last_order_date,  COALESCE(lpo.purchased_flavours, 0) AS purchased_flavours""" 
            conditional_join = f"""LEFT JOIN lifetime_product_orders lpo ON cu.customer_id = lpo.customer_id"""

        
        # Optimized count query with the necessary aggregations
        count_query = f"""WITH customer_addresses AS (
                            SELECT DISTINCT ON (customer_id) customer_id, state FROM customer_addresses ORDER BY customer_id, customer_address_id DESC
                        ),
                        lifetime_total_orders AS (
                            SELECT o.customer_id FROM orders o GROUP BY o.customer_id
                        )
                        SELECT COUNT(*)
                        FROM customers cu
                        LEFT JOIN customer_addresses ca ON cu.customer_id = ca.customer_id
                        LEFT JOIN bo_generic_tags_mapping gtm ON gtm.entity_int_id = cu.customer_id AND gtm.entity_type = 'customer'
                        LEFT JOIN salesforce_customer_rep scr ON cu.customer_id = scr.customer_id
                        LEFT JOIN lifetime_total_orders lto ON cu.customer_id = lto.customer_id
                        WHERE gtm.entity_type = 'customer' {where_clause} """
        count_res = conn.execute(text(count_query)).scalar()
        total_count = int(count_res) if count_res else 0
        
        # Customers query
        customer_query = f"""WITH customer_addresses AS (
                            SELECT DISTINCT ON (customer_id) customer_id, state FROM customer_addresses ORDER BY customer_id, customer_address_id DESC
                        ),
                        lifetime_total_orders AS (
                            SELECT customer_id, COUNT(DISTINCT o.order_id) AS lifetime_order_count, COALESCE(SUM(o.total_including_tax), 0) AS lifetime_order_total
                            FROM orders o GROUP BY customer_id
                        )
                        {conditional_query}
                        SELECT 
                            cu.customer_id, cu.email, cu.first_name, cu.last_name, CONCAT(cu.first_name, ' ', cu.last_name) AS name, 
                            scr.rep_name, scr.rep_email, ca.state, cu.company AS company_name,
                            COALESCE(lto.lifetime_order_count, 0) AS lifetime_order_count, 
                            COALESCE(lto.lifetime_order_total, 0) AS lifetime_order_total
                            {append_columns}
                        FROM customers cu
                        LEFT JOIN customer_addresses ca ON cu.customer_id = ca.customer_id
                        LEFT JOIN bo_generic_tags_mapping gtm ON gtm.entity_int_id = cu.customer_id AND gtm.entity_type = 'customer'
                        LEFT JOIN salesforce_customer_rep scr ON cu.customer_id = scr.customer_id
                        LEFT JOIN lifetime_total_orders lto ON cu.customer_id = lto.customer_id
                        {conditional_join}
                        WHERE gtm.entity_type = 'customer' {where_clause}"""
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ['name', 'rep_name', 'state', 'company_name', 'lifetime_order_count', 'lifetime_order_total', 'product_last_order_date', 'product_order_count', 'product_order_total', 'purchased_flavours']:                
                if sort_direction == 'ASC':
                    customer_query += f" ORDER BY {sort_array[0]} {sort_direction} NULLS FIRST"
                else:
                    customer_query += f" ORDER BY {sort_array[0]} {sort_direction} NULLS LAST"

        offset = (page - 1) * limit
        if page and limit:
            customer_query += f" LIMIT {limit} OFFSET {offset}"

        customer_result = conn.execute(text(customer_query)).fetchall()

        # fetch products data for given bop ids
        if bop_ids_list:
            total_variant_counts = {}
            # get all the variants of product
            total_variant_query = f"""SELECT bop.bop_id, COUNT(DISTINCT v.variants_id) AS total_variants FROM variants v
                                    INNER JOIN bo_bulk_order_products bop ON v.product_id = bop.bc_product_id WHERE (:bop_ids IS NULL OR bop.bop_id = ANY(:bop_ids)) GROUP BY bop.bop_id"""
            total_variant_res = conn.execute(text(total_variant_query), {"bop_ids": bop_ids_list}).fetchall()
            if total_variant_res:
                for row in total_variant_res:
                    total_variant_counts[str(row[0])] = str(row[1]) if row[1] else "0"

            # Fetch all unique products
            all_products_query = f"""SELECT bop.bop_id, bop.bc_name 
                                    FROM bo_bulk_order_products bop
                                    WHERE (:bop_ids IS NULL OR bop.bop_id = ANY(:bop_ids))"""
            all_products_result = conn.execute(text(all_products_query), {"bop_ids": bop_ids_list}).fetchall()
            all_products = [{
                "bop_id": row[0],
                "product_name": row[1],
                "product_order_count": 0,
                "product_order_total": 0,
                "product_last_order_date": None,
                "purchased_flavours": "0" + '/' + str(total_variant_counts[str(row[0])]),   
            } for row in all_products_result]
            # Sort the products by the order of bop_ids in bop_ids_list
            bop_id_order = {bop_id: index for index, bop_id in enumerate(bop_ids_list)}
            all_products.sort(key=lambda x: bop_id_order.get(x["bop_id"], float('inf')))

            # Product query
            product_query = f"""WITH lifetime_product_orders AS (
                                    SELECT o.customer_id, bop.bop_id,  o.order_id, COALESCE(SUM(ol.total_inc_tax), 0) AS product_order_total,
                                        MAX(o.order_created_date_time) AS product_last_order_date, COUNT(DISTINCT ol.variant_id) AS purchased_flavours
                                    FROM orders o
                                    JOIN order_line_items ol ON o.order_id = ol.order_id
                                    JOIN bo_bulk_order_products bop ON ol.product_id = bop.bc_product_id
                                    WHERE (:bop_ids IS NULL OR bop.bop_id = ANY(:bop_ids)) AND o.order_status_id NOT IN (0, 4, 5, 6, 7, 13) GROUP BY o.customer_id, bop.bop_id, o.order_id
                                ),
                                total_purchased_flavours AS (
                                    SELECT o.customer_id, bop.bop_id, COUNT(DISTINCT ol.variant_id) AS purchased_flavours
                                    FROM orders o
                                    JOIN order_line_items ol ON o.order_id = ol.order_id
                                    JOIN bo_bulk_order_products bop ON ol.product_id = bop.bc_product_id
                                    WHERE (:bop_ids IS NULL OR bop.bop_id = ANY(:bop_ids)) AND o.order_status_id NOT IN (0, 4, 5, 6, 7, 13) GROUP BY o.customer_id, bop.bop_id
                                )
                                SELECT
                                    lpo.customer_id, bop.bop_id, bop.bc_name, COUNT(DISTINCT lpo.order_id) AS product_order_count,
                                    SUM(lpo.product_order_total) as order_total, MAX(lpo.product_last_order_date) as last_order_date,  COALESCE(tpf.purchased_flavours, 0) AS purchased_flavours
                                FROM lifetime_product_orders lpo
                                JOIN bo_bulk_order_products bop ON bop.bop_id = lpo.bop_id
                                JOIN total_purchased_flavours tpf ON tpf.customer_id = lpo.customer_id AND tpf.bop_id = lpo.bop_id
                                WHERE (:bop_ids IS NULL OR bop.bop_id = ANY(:bop_ids))
                                GROUP BY lpo.customer_id, bop.bop_id, bop.bc_name, tpf.purchased_flavours"""
            
            product_result = conn.execute(text(product_query), {"bop_ids": bop_ids_list}).fetchall()

            for row in product_result:
                customer_id = row[0]
                bop_id = row[1]
                if customer_id not in product_map:
                    product_map[customer_id] = []
                product_map[customer_id].append({
                    "bop_id": bop_id,
                    "product_name": row[2],
                    "product_order_count": row[3],
                    "product_order_total": round(row[4], 2) if row[4] else 0,
                    "product_last_order_date": convert_to_timestamp(row[5]),
                    "purchased_flavours": (str(row[6]) if row[6] else "0") + '/' + str(total_variant_counts[str(bop_id)]),   
                })

        # Fill missing products with dummy data
        if bop_ids_list:
            for customer_id in product_map:
                existing_bop_ids = {product["bop_id"] for product in product_map[customer_id]}
                missing_products = [product for product in all_products if product["bop_id"] not in existing_bop_ids]
                product_map[customer_id].extend(missing_products)
                # Sort the products by the order of bop_ids in bop_ids_list
                bop_id_order = {bop_id: index for index, bop_id in enumerate(bop_ids_list)}
                product_map[customer_id].sort(key=lambda x: bop_id_order.get(x["bop_id"], float('inf')))

        for row in customer_result:     
            customer_id = row[0]    
            customer_data.append({
                'customer_id': row[0],
                "email": row[1],
                "first_name": row[2],
                "last_name": row[3],
                "name": row[4],
                "rep_name": row[5],
                "rep_email": row[6],
                "state": row[7],  
                "company_name": row[8],
                "lifetime_order_total": round(row[10], 2) if row[10] else 0,
                "products": product_map.get(customer_id, all_products)         
            })
        data = calculatePaginationData(customer_data, page, limit, total_count)
        response['data'] = data
        response['status'] = 200
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response

def bulk_products_global_report_csv(store, query_params):
    response = {
        "status": 400,
    }
    if query_params:
        task_id = task.send_task(task.BULK_PRODUCTS_GLOBAL_REPORT_MAIL_TASK, args=(store['id'], query_params))
        if task_id:
            response['status'] = 200
            response['message'] = f"We are generating the data you requested for download as a CSV file. This process may take a few minutes. Once complete, the CSV will be sent to your email: {query_params['username']}."
        else:
            response['status'] = 400
            response['message'] = 'Something went wrong to generate the CSV file.'
    else:
        response['status'] = 400
        response['message'] = 'query params are required'

    return response