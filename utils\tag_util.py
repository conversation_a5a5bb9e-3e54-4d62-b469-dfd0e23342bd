import new_pgdb as db
from new_pgdb import tag_db
from utils.common import parse_json
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError
import new_pgdb
import new_utils
from datetime import datetime

def add_tag(tag):
    return tag_db.Tags.add_tag(tag)

def add_tags(tags, store):
    return tag_db.Tags.add_tags(tags, store)

#########################
#### {"product-sku-1": ["tag1", "tag2"], "product-sku-2": ["tag1", "tag2"]}
#########################
def add_product_tags(product_tag_mapping, store, session=None):
    # Prepare the new payload format
    req_body = {"data": []}

    for product_sku, tags in product_tag_mapping.items():
        # Transform the tags by stripping whitespace and normalizing case
        req_body["data"].append({
            "sku": product_sku,
            "tags": tags
        })

    # Call the add_products function with the transformed payload
    response = add_products(req_body, store)

    return response

#########################
#### {"product-sku-1": ["tag1", "tag2"], "product-sku-2": ["tag1", "tag2"]}
#########################
def update_product_tags(product_tag_mapping, store):
    # Prepare the new payload format
    req_body = {"data": []}

    for product_sku, tags in product_tag_mapping.items():
        # Transform the tags by stripping whitespace and normalizing case
        req_body["data"].append({
            "sku": product_sku,
            "tags": tags
        })

    # Call the add_products function with the transformed payload
    response = add_products(req_body, store)

    return response


def get_all_tags(store, sort_by=None):
    result = []
    tags = tag_db.Tags.get_all_tags(store)
    
    if tags and len(tags) > 0:
        tag_ids = [t.id for t in tags]  # Extract all tag ids to use in the query
        
        if tag_ids:  # Proceed only if there are tag_ids to query
            conn = db.get_connection(store['id'])
            try:
                # Count occurrences of each tag_id in the product_tags table in a single query
                count_query = """
                    SELECT tag_id, COUNT(*) as count 
                    FROM product_tags 
                    WHERE tag_id IN :tag_ids
                    GROUP BY tag_id
                """
                # Execute the query and fetch counts for all tag_ids
                count_results = conn.execute(text(count_query), {'tag_ids': tuple(tag_ids)})
                
                # Use tuple indices instead of dictionary keys
                tag_count_map = {row[0]: row[1] for row in count_results}

                # Iterate through the tags and attach the count (default to 0 if not found)
                for t in tags:
                    result.append({
                        "id": t.id,
                        "tag": t.tag,
                        "created_at": t.created_at.isoformat() if t.created_at else '',  # Assuming Tags model has created_at field
                        "count": tag_count_map.get(t.id, 0)  # Default to 0 if tag_id not found
                    })
                
                # Sort the result based on sort_by parameter
                if sort_by:
                    field, order = sort_by.split('/')  # Split the field and order, e.g., 'count/1'
                    order = int(order)

                    # Define the key function for sorting
                    if field == "count":
                        result.sort(key=lambda x: x["count"], reverse=(order == -1))
                    elif field == "created_at":
                        result.sort(key=lambda x: x["created_at"], reverse=(order == -1))
                    elif field == "tag":
                        result.sort(key=lambda x: x["tag"], reverse=(order == -1))
                    
            except SQLAlchemyError as e:
                # Handle exceptions if needed
                print(f"Error occurred: {e}")
            finally:
                # Close the connection manually
                if conn:
                    conn.close()

    return result


def get_product_tags(product_sku, store):
    # Connect to the database
    conn = db.get_connection(store['id'])
    
    # Define dictionary to store results with either variant_sku or sku as the key
    sku_tags_dict = {}
    result_sku = None  # To store which SKU (variant_sku or sku) to display in the result
    
    try:
        # First search in the variant_sku field
        query = """
            SELECT variant_sku, tag_id FROM product_tags
            WHERE variant_sku = :variant_sku
        """
        records = conn.execute(text(query), {"variant_sku": product_sku}).fetchall()
        
        # If records are found in variant_sku, use variant_sku in the output
        if records:
            result_sku = product_sku  # Set variant_sku as the key in the output
            for record in records:
                tag_id = record[1]
                # Group tag_ids by variant_sku
                if result_sku not in sku_tags_dict:
                    sku_tags_dict[result_sku] = []
                sku_tags_dict[result_sku].append(tag_id)
        else:
            # If no records are found in variant_sku, search in sku where variant_sku is NULL
            query = """
                SELECT sku, tag_id FROM product_tags
                WHERE sku = :sku AND variant_sku IS NULL
            """
            records = conn.execute(text(query), {"sku": product_sku}).fetchall()
            
            # Set sku as the key if variant_sku is not found
            if records:
                result_sku = product_sku
                for record in records:
                    tag_id = record[1]
                    # Group tag_ids by sku
                    if result_sku not in sku_tags_dict:
                        sku_tags_dict[result_sku] = []
                    sku_tags_dict[result_sku].append(tag_id)
        
        # Get unique tag_ids to fetch tag names from the tags table
        all_tag_ids = {tag_id for tags in sku_tags_dict.values() for tag_id in tags}


        if all_tag_ids:
            # Query to fetch tag names based on tag_ids
            tag_query = """
                SELECT id, tag FROM tags
                WHERE id IN :tag_ids
            """
            tag_records = conn.execute(text(tag_query), {"tag_ids": tuple(all_tag_ids)}).fetchall()
            
            # Map tag_id to tag name for easy lookup
            tag_map = {record[0]: record[1] for record in tag_records}
            
            # Replace tag_ids with tag names in the final output
            sku_tags = [{"sku": result_sku, "tags": [tag_map[tag_id] for tag_id in tags]}
                        for result_sku, tags in sku_tags_dict.items()]
        else:
            sku_tags = []
    
    finally:
        # Ensure the database connection is closed
        conn.close()

    return {"data": sku_tags}




def clear_product_tags(req_body, store):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        # Loop through each SKU and variant_sku combination in the payload
        for record in req_body.get('data', []):
            sku = record.get('sku')
            variant_sku = record.get('variant_sku')

            # Ensure SKU is provided (since it's required for deletion)
            if not sku:
                continue  # Skip this record if SKU is not provided

            # Base delete query
            delete_query = """
                DELETE FROM product_tags 
                WHERE sku = :sku 
            """

            # Add condition for variant_sku: if it's empty, delete where variant_sku is NULL or empty
            if variant_sku == "" or variant_sku is None:
                delete_query += " AND (variant_sku = '' OR variant_sku IS NULL)"
            else:
                delete_query += " AND variant_sku = :variant_sku"

            # Prepare query parameters
            query_params = {'sku': sku}
            if variant_sku != "" or variant_sku is None:
                query_params['variant_sku'] = variant_sku

            # Execute the delete query
            conn.execute(text(delete_query), query_params)
            conn.commit()

        response['status'] = 200
        response['message'] = "Records deleted successfully."

    except Exception as e:
        response['status'] = 422
        response['message'] = str(e)
        raise e
    finally:
        if conn:
            conn.close()

    return response

    # tag_db.ProductTags.delete_product_tags(skus, store)

def clear_tag(tagId, store):
    tag_db.Tags.delete_tags(tagId, store)

def edit_tag(tag_mapping, store):
    new_tags = []
    old_tag_ids = []
    for old_tag_id, tags in tag_mapping.items():
        old_tag_ids.append(old_tag_id)       
        # for tag in tags:
        #     new_tags.append(tag)        
        new_tags = tags    
    return tag_db.Tags.update_tags(old_tag_ids, new_tags, store)

def get_all_products_with_tag(store, filter=None, page=1, limit=10, sort_by='created_at/-1'):
    response = {"status": 404, "message": "Data not found", "data": []}  # Default empty data
    session = db.get_session(store['id'])

    try:
        with db.get_connection(store['id']) as conn:
            # Fetch data from product_tags and related tables
            query = """
                SELECT pt.sku, pt.variant_sku, pt.created_at, 
                       p.product_name, 
                       v.variant_options, 
                       t.tag 
                FROM product_tags pt
                LEFT JOIN products p ON pt.sku = p.sku
                LEFT JOIN variants v ON pt.variant_sku = v.variants_sku
                LEFT JOIN tags t ON pt.tag_id = t.id
            """

            # Execute the query and fetch the result set
            data = conn.execute(text(query)).fetchall()

            # Create a dictionary to hold unique entries
            grouped_products = {}

            # Process and format the results
            for row in data:
                sku = row.sku
                variant_sku = row.variant_sku

                # Create a unique key for grouping
                key = (sku, variant_sku) if variant_sku else sku

                # Initialize if not already in the dictionary
                if key not in grouped_products:
                    grouped_products[key] = {
                        "sku": sku,
                        "variant_sku": variant_sku,
                        "created_at": row.created_at.isoformat() if row.created_at else '',
                        "product_name": row.product_name,
                        "variant_options": row.variant_options,
                        "tags": []
                    }

                # Append the tag to the tag list (ensure no duplicates)
                tag = row.tag
                if tag and tag not in grouped_products[key]["tags"]:
                    grouped_products[key]["tags"].append(tag)

            # Convert the grouped data into a list
            result = list(grouped_products.values())

            # Apply filtering if provided
            if filter:
                filter = filter.lower()
                result = [
                    item for item in result
                    if (item['product_name'] or '').lower().find(filter) != -1 or 
                    (item['sku'] or '').lower().find(filter) != -1 or 
                    (item['variant_sku'] or '').lower().find(filter) != -1 or 
                    any((tag or '').lower().find(filter) != -1 for tag in item['tags'])
                ]

            # Apply sorting based on the sort_by parameter
            if sort_by:
                field, order = sort_by.split('/')
                reverse = int(order) == -1

                # Perform the sorting
                result = sorted(result, key=lambda x: x.get(field, '').lower() if isinstance(x.get(field), str) else x.get(field), reverse=reverse)

            if page == 0 and limit == 0:
                # Return all data without pagination
                response['status'] = 200
                response['data'] = {"data": result}
            else:
                # Calculate pagination details
                total_records = len(result)
                start_index = (page - 1) * limit
                end_index = start_index + limit

                # Paginate the results
                paginated_data = result[start_index:end_index]

                # Calculate total pages and pagination info
                pagination = new_utils.calculate_pagination(paginated_data, page, limit, total_records)

                response['status'] = 200
                response['data'] = pagination

    except SQLAlchemyError as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
    finally:
        conn.close()
    if not result:
        response['status'] = 200
        response['data'] = {"data": []}
    return response


def validate_parent_sku(sku, store):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store['id'])
    
    try:
        # Check if SKU exists in the product_tags table's variant_sku
        query_variant_sku_in_product_tags = text(
            """SELECT EXISTS(
                    SELECT 1
                    FROM product_tags
                    WHERE variant_sku = :sku
                ) AS variant_sku_exists;
            """
        )
        result_variant_sku_in_product_tags = conn.execute(query_variant_sku_in_product_tags.params(sku=sku))
        variant_sku_exists_in_tags = result_variant_sku_in_product_tags.scalar()

        if variant_sku_exists_in_tags:
            # If SKU exists in the variant_sku of the product_tags table, return a conflict message
            response['status'] = 409  # Conflict status code
            response['message'] = f"sku: SKU already exists in the system."
            return response

        # Check if SKU exists in the product_tags table where variant_sku is NULL
        query_sku_with_null_variant_sku = text(
            """SELECT EXISTS(
                    SELECT 1
                    FROM product_tags
                    WHERE sku = :sku AND variant_sku IS NULL
                ) AS sku_with_null_variant_exists;
            """
        )
        result_sku_with_null_variant_sku = conn.execute(query_sku_with_null_variant_sku.params(sku=sku))
        sku_with_null_variant_exists = result_sku_with_null_variant_sku.scalar()

        if sku_with_null_variant_exists:
            # If SKU exists in product_tags with variant_sku NULL, return a conflict message
            response['status'] = 409  # Conflict status code
            response['message'] = f"sku: SKU already exists in the system."
            return response

        # Check if SKU exists in the products table
        query_products = text(
            """SELECT EXISTS(
                    SELECT 1
                    FROM products
                    WHERE sku = :sku
                ) AS sku_exists;
            """
        )
        result_products = conn.execute(query_products.params(sku=sku))
        sku_exists_in_products = result_products.scalar()

        # Check if SKU exists in the variants table
        query_variant_exists = text(
            """SELECT EXISTS(
                    SELECT 1
                    FROM variants
                    WHERE variants_sku = :sku
                ) AS sku_exists;
            """
        )
        result_variant_exists = conn.execute(query_variant_exists.params(sku=sku))
        sku_exists_in_variants = result_variant_exists.scalar()

        if sku_exists_in_variants:
            # Check if parent_sku is null or empty for the variant
            query_parent_sku_check = text(
                """SELECT parent_sku
                   FROM variants
                   WHERE variants_sku = :sku;
                """
            )
            parent_sku_result = conn.execute(query_parent_sku_check.params(sku=sku)).fetchone()

            if parent_sku_result and (parent_sku_result[0] is None or parent_sku_result[0].strip() == ''):
                # If parent_sku is missing (null or empty), return specific error message
                response['status'] = 400
                response['message'] = f"sku: Parent SKU is missing for the variant SKU '{sku}'."
                return response

        # Proceed with normal SKU validation if the SKU exists in products or is a valid variant with a parent SKU
        if sku_exists_in_products or (sku_exists_in_variants and parent_sku_result):
            response['status'] = 200
            response['message'] = 'Valid SKU'
        else:
            response['status'] = 404
            response['message'] = 'sku: SKU is not valid, please try again.'

    finally:
        if conn:
            conn.close()

    return response


def add_products(req_body, store):
    response = {
        "status": 400,
        "errors": [],
        "message": "",
        "records_processed": 0
    }
    conn = new_pgdb.get_connection(store['id'])
    
    product_tag_entries = []
    tag_entries = set()
    variant_skus_to_delete = []
    skus_to_delete = []

    try:
        skus = [product.get('sku') for product in req_body.get('data', []) if product.get('sku')]
        if not skus:
            response["message"] = "No valid SKUs provided."
            return response

        # Fetch all variant and product info in one shot
        sku_placeholders = ', '.join(f"'{sku}'" for sku in skus)  # Prepare for SQL IN clause

        variant_info_query = text(f"""
            SELECT variants_sku, parent_sku 
            FROM variants 
            WHERE variants_sku IN ({sku_placeholders})
        """)
        variant_info_result = conn.execute(variant_info_query).fetchall()

        product_exists_query = text(f"""
            SELECT sku FROM products WHERE sku IN ({sku_placeholders})
        """)
        product_exists_result = conn.execute(product_exists_query).fetchall()

        # Convert query results into dictionaries for quick lookup
        variant_info_dict = {row[0]: row[1] for row in variant_info_result}  # {variant_sku: parent_sku}
        product_exists_set = {row[0] for row in product_exists_result}  # {sku}

        for product in req_body.get('data', []):
            sku = product.get('sku')
            tags = product.get('tags', [])

            if not sku or not tags:
                response['errors'].append(f"Invalid product data for SKU: '{sku}'. Ensure SKU and tags are provided.")
                continue

            unique_tags = set(tag.strip().replace(" ", "").lower() for tag in tags)

            if sku in variant_info_dict:
                parent_sku = variant_info_dict[sku]
                if not parent_sku:
                    response['errors'].append(f"sku: '{sku}' has no valid parent SKU.")
                    continue

                variant_skus_to_delete.append(sku)
                for tag in unique_tags:
                    tag_entries.add((tag, tag))
                    product_tag_entries.append((parent_sku, sku, tag))
            
            elif sku in product_exists_set:
                skus_to_delete.append(sku)
                for tag in unique_tags:
                    tag_entries.add((tag, tag))
                    product_tag_entries.append((sku, None, tag))

            else:
                response['errors'].append(f"sku: '{sku}' is not valid, please try again.")
        
        # Perform bulk deletion
        if variant_skus_to_delete:
            conn.execute(text("""
                DELETE FROM product_tags WHERE variant_sku IN :variant_skus
            """), {"variant_skus": tuple(variant_skus_to_delete)})
            conn.commit()
        
        if skus_to_delete:
            conn.execute(text("""
                DELETE FROM product_tags WHERE sku IN :skus AND variant_sku IS NULL
            """), {"skus": tuple(skus_to_delete)})
            conn.commit()

        # Bulk insert tags
        if tag_entries:
            conn.execute(text("""
                INSERT INTO tags (id, tag, created_at)
                VALUES (:id, :tag, CURRENT_TIMESTAMP)
                ON CONFLICT (id) DO UPDATE 
                SET tag = EXCLUDED.tag, created_at = CURRENT_TIMESTAMP;
            """), [{"id": id_, "tag": tag} for id_, tag in tag_entries])
            conn.commit()

        # Bulk insert product_tags
        if product_tag_entries:
            conn.execute(text("""
                INSERT INTO product_tags (sku, variant_sku, tag_id, created_at)
                VALUES (:sku, :variant_sku, :tag_id, CURRENT_TIMESTAMP);
            """), [{"sku": sku, "variant_sku": variant_sku, "tag_id": tag_id} for sku, variant_sku, tag_id in product_tag_entries])
            conn.commit()

        response['records_processed'] = len(product_tag_entries)
        response['status'] = 200 if not response['errors'] else 409
        response['message'] = "Product assigned to tag(s) successfully." if not response['errors'] else response['errors']

    finally:
        if conn:
            conn.close()

    return response


def store_generic_tags(store, payload, username, customer_id):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        entity_type = payload["entity_type"]
        # Extract and process tags
        tags = payload.get("tags", "")

        tag_keys = [tag.strip().replace(" ", "_") for tag in tags.split(",")]

        # Insert into bo_generic_tags table if tag_key does not exist
        for tag_key, tag_label in zip(tag_keys, tags.split(",")):
            tag_label = tag_label.strip()
            query_check = "SELECT COUNT(*) FROM bo_generic_tags WHERE tag_key = :tag_key"
            result = conn.execute(text(query_check), {"tag_key": tag_key})
            exists = result.fetchone()[0] > 0

            if not exists and tags != "":
                query_insert_tag = (
                    "INSERT INTO bo_generic_tags (tag_key, label, created_at, created_by, entity_type) "
                    "VALUES (:tag_key, :tag_label, CURRENT_TIMESTAMP, :username, :entity_type)"
                )
                conn.execute(text(query_insert_tag), {"tag_key": tag_key, "tag_label": tag_label, "username": username, "entity_type": entity_type})
                conn.commit()

        # Delete from bo_generic_tags_mapping table based on entity_type and id
        entity_id = customer_id
        if isinstance(entity_id, int):
            delete_query = (
                "DELETE FROM bo_generic_tags_mapping WHERE entity_type = :entity_type AND entity_int_id = :entity_id"
            )
        else:
            delete_query = (
                "DELETE FROM bo_generic_tags_mapping WHERE entity_type = :entity_type AND entity_str_id = :entity_id"
            )

        conn.execute(text(delete_query), {"entity_type": entity_type, "entity_id": entity_id})
        conn.commit()

        if tags != "":
            # Insert new entries into bo_generic_tags_mapping table
            for tag_key, tag_label in zip(tag_keys, tags.split(",")):
                tag_label = tag_label.strip()
                if isinstance(entity_id, int):
                    insert_query = (
                        "INSERT INTO bo_generic_tags_mapping (entity_type, entity_int_id, tag_id, tag_label, created_by, created_at) "
                        "VALUES (:entity_type, :entity_id, :tag_id, :tag_label, :username, CURRENT_TIMESTAMP)"
                    )
                else:
                    insert_query = (
                        "INSERT INTO bo_generic_tags_mapping (entity_type, entity_str_id, tag_id, tag_label, created_by, created_at) "
                        "VALUES (:entity_type, :entity_id, :tag_id, :tag_label, :username, CURRENT_TIMESTAMP)"
                    )
                res = conn.execute(text(insert_query), {"entity_type": entity_type, "entity_id": entity_id, "tag_id": tag_key, "tag_label": tag_label, "username": username})
        
            conn.commit()
            if res.rowcount > 0:
                response["status"] = 200
                response["message"] = "Tags assigned successfully."
            else:
                response["status"] = 400
                response["message"] = "Failed to assign tags."
        else:
            response["status"] = 200
            response["message"] = "Tags deleted successfully."
    finally:
        conn.commit()
        conn.close()

    return response


def get_generic_tags(store, entity_type, id):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        if isinstance(id, int):
            query = "SELECT tag_label, tag_id, entity_type, entity_int_id FROM bo_generic_tags_mapping WHERE entity_type = :entity_type AND entity_int_id = :entity_id"
        else:
            query = "SELECT tag_label, tag_id, entity_type, entity_str_id FROM bo_generic_tags_mapping WHERE entity_type = :entity_type AND entity_str_id = :entity_id"
        res = conn.execute(text(query), {"entity_type": entity_type, "entity_id": id})
        tags = res.fetchall()
        if tags:
            tag_data = []
            for tag in tags:
                tag_data.append({
                    "tag_label": tag[0],
                    "tag_id": tag[1],
                    "entity_type": tag[2],
                    "id": tag[3]
                })
            response["status"] = 200
            response["data"] = tag_data
        else:
            response["status"] = 200
            response["data"] = []
    finally:
        conn.close()
    return response

def get_all_generic_tags(store, entity_type):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        query = "SELECT label, tag_key FROM bo_generic_tags WHERE entity_type = :entity_type ORDER BY label ASC"
        res = conn.execute(text(query), {"entity_type": entity_type})
        tags = res.fetchall()
        if tags:
            tag_data = []
            for tag in tags:
                tag_data.append({
                    "tag_label": tag[0],
                    "tag_id": tag[1]
                })
            response["status"] = 200
            response["data"] = tag_data
        else:
            response["status"] = 200
            response["data"] = []
    finally:
        conn.close()
    return response

