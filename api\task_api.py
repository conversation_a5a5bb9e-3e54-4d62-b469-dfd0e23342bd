import datetime
from flask import request, make_response, json
from flask_restful import Api, Resource
import logging
import traceback
from api import APIResource
import task

logger = logging.getLogger()

class TaskRunner(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering TaskRunner GET")
        try:
            tasks = task.get_scheduled_tasks()
            current_time = datetime.datetime.now().isoformat()
            return {
                "current_time": current_time,
                "scheduled_tasks": tasks
            }, 200
        finally:
            logger.debug("Exiting TaskRunner GET")

    def delete_executor(self, request, token_payload, store):
        logger.debug("Entering TaskRunner DELETE")
        try:
            tasks = task.cancel_duplicate_tasks()
            current_time = datetime.datetime.now().isoformat()
            return {
                "current_time": current_time,
                "scheduled_tasks": tasks
            }, 200
        finally:
            logger.debug("Exiting TaskRunner DELETE")

    def post_executor(self, request, token_payload, store):
        logger.debug("Entering TaskRunner POST")
        try:
            req_payload = request.get_json(force=True)
            if req_payload and "task" in req_payload:
                task_id = task.send_task(req_payload["task"], (store["id"],))
                return {"id": str(task_id)}, 200
            else:
                return {"error": "Invalid task name"}, 409
        finally:
            logger.debug("Exiting TaskRunner POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def delete(self):
        return self.execute_store_request(request, self.delete_executor)