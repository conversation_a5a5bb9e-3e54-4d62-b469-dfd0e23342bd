from datetime import datetime, timedelta

month_array = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

def convert_to_cst(input_timestamp):
  # Convert string to datetime object
  input_datetime = datetime.strptime(input_timestamp, "%Y-%m-%dT%H:%M:%S.%f")

  # Define time difference for CST (UTC-6)
  cst_offset = timedelta(hours=-6)

  # Apply the offset to the input datetime to get CST datetime
  cst_datetime = input_datetime + cst_offset

  # Format the CST datetime as a string
  cst_datetime_str = cst_datetime.strftime("%Y-%m-%d %H:%M:%S CST")
  
  return cst_datetime_str