from sqlalchemy import text
import pg_db
from mongo_db import user_db
import logging
from utils.common import calculatePaginationData, convert_time_format
from new_mongodb import get_admin_db_client_for_store_id
from projects.project_task_report import get_filters

logger = logging.getLogger()

def get_members_report(store_id, project_id, username, page, limit, report_type, sort_array):
    response = {
        "status": 400
    }
    db = get_admin_db_client_for_store_id(store_id)
    conn = pg_db.get_connection()
    try:
        if not project_id:
            response['status'] = 200
            response['data'] = []
            return response
        
        # Fetch user data
        user_data = user_db.fetch_user_by_username(username)
        user_id = str(user_data['_id'])

        # Fetch user preferences from MongoDB
        user_pref = db["user_preference"].find_one({"user_id": user_id, "type": report_type})

        project_ids = list(map(int, project_id.split(","))) if isinstance(project_id, str) else project_id

        module_ids = []
        assignees = []
        current_column_ids = []
        priorities = []
        ticket_statuses = []

        if user_pref:
            columns = user_pref.get("columns", {})
            module_ids = columns.get("modules", []) if len(project_ids) == 1 else []
            assignees = columns.get("assignee", [])
            current_column_ids = columns.get("stages", []) if len(project_ids) == 1 else []
            priorities = columns.get("priority", [])
            ticket_statuses = columns.get("ticket_status", [])

            if "unassigned" in assignees:
                assignees.append("")

        # Count query to get the total records
        count_query = """
            SELECT COUNT(DISTINCT assigned_to) AS total_count
            FROM agile_project_cards apc
            WHERE apc.project_id = ANY(:project_ids)
        """

        # Main query to fetch the members report
        query = """
            SELECT 
                assigned_to AS username,
                COUNT(id) AS total_cards,
                COALESCE(
                    TO_CHAR(
                        MAKE_INTERVAL(secs => SUM(EXTRACT(EPOCH FROM estimation))),
                        'HH24:MI:SS'
                    ), 
                    '00:00:00'
                ) AS total_estimation,
                COALESCE(
                    TO_CHAR(
                        MAKE_INTERVAL(secs => SUM(EXTRACT(EPOCH FROM spent_time))),
                        'HH24:MI:SS'
                    ), 
                    '00:00:00'
                ) AS total_spent_time,
                COALESCE(
                    TO_CHAR(
                        MAKE_INTERVAL(secs => GREATEST(0, SUM(EXTRACT(EPOCH FROM estimation)) - SUM(EXTRACT(EPOCH FROM spent_time)))),
                        'HH24:MI:SS'
                    ),
                    '00:00:00'
                ) AS total_remaining_time,
                CASE
                    WHEN SUM(EXTRACT(EPOCH FROM estimation)) = 0 AND SUM(EXTRACT(EPOCH FROM spent_time)) > 0
                    THEN 100 
                    WHEN SUM(EXTRACT(EPOCH FROM estimation)) > 0 THEN LEAST(
                        ROUND(SUM(EXTRACT(EPOCH FROM spent_time)) / SUM(EXTRACT(EPOCH FROM estimation)) * 100, 2)::INTEGER,
                        100
                    )
                    ELSE 0
                END AS progress_percentage,
                SUM(EXTRACT(EPOCH FROM estimation)) AS total_estimation_seconds,
                SUM(EXTRACT(EPOCH FROM spent_time)) AS total_spent_time_seconds,
                GREATEST(0, COALESCE(SUM(EXTRACT(EPOCH FROM estimation)), 0) - COALESCE(SUM(EXTRACT(EPOCH FROM spent_time)), 0)) AS total_remaining_seconds
            FROM agile_project_cards apc
            WHERE apc.project_id = ANY(:project_ids)
        """

        # Add filters based on user preferences
        if module_ids:
            query += " AND apc.module_id = ANY(:module_ids)"
            count_query += " AND apc.module_id = ANY(:module_ids)"
        if assignees:
            query += " AND apc.assigned_to = ANY(:assignees)"
            count_query += " AND apc.assigned_to = ANY(:assignees)"
        if current_column_ids:
            query += " AND apc.current_column_id = ANY(:current_column_ids)"
            count_query += " AND apc.current_column_id = ANY(:current_column_ids)"
        if priorities:
            query += " AND apc.priority = ANY(:priorities)"
            count_query += " AND apc.priority = ANY(:priorities)"
        if ticket_statuses:
            query += " AND apc.status = ANY(:ticket_statuses)"
            count_query += " AND apc.status = ANY(:ticket_statuses)"

        query += " GROUP BY apc.assigned_to"

        # Add sorting if specified
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ["assignee"]:                
                query += f" ORDER BY assigned_to {sort_direction}"
            elif sort_array[0] in ["total_estimation"]:
                query += f" ORDER BY total_estimation_seconds {sort_direction}"
            elif sort_array[0] in ["total_spent_time"]:
                query += f" ORDER BY total_spent_time_seconds {sort_direction}"
            elif sort_array[0] in ["total_remaining_time"]:
                query += f" ORDER BY total_remaining_seconds {sort_direction}"
            elif sort_array[0] in ["total_cards", "progress_percentage"]:                
                query += f" ORDER BY {sort_array[0]} {sort_direction}"

        # Add pagination if specified
        if page and limit:
            offset = (page - 1) * limit
            query += " LIMIT :limit OFFSET :offset"

        filters_response = get_filters(project_ids, user_pref)

        # Execute count query
        count_query = text(count_query).params(
            project_ids=project_ids,
            module_ids=module_ids,
            assignees=assignees,
            current_column_ids=current_column_ids,
            priorities=priorities,
            ticket_statuses=ticket_statuses
        )
        
        count_result = conn.execute(count_query)
        total_records = count_result.fetchone()[0]

        # Execute main query
        query = text(query).params(
            project_ids=project_ids, 
            page=page, 
            limit=limit, 
            offset=offset,
            module_ids=module_ids,
            assignees=assignees,
            current_column_ids=current_column_ids,
            priorities=priorities,
            ticket_statuses=ticket_statuses
        )
        result = conn.execute(query)

        members_report = []
        for row in result:
            user_data = user_db.fetch_user_by_username(row[0])
            if user_data:
                name = user_data.get('name', '')
            else:
                name = 'Unassigned'
                
            member_data = {
                "username": row[0],
                "assignee": name,
                "total_cards": row[1],
                "total_estimation": convert_time_format(row[2], call_from_report=True),
                "total_spent_time": convert_time_format(row[3], call_from_report=True),
                "total_remaining_time": convert_time_format(row[4], call_from_report=True),
                "progress_percentage": f"{row[5]}%"
            }
            members_report.append(member_data)

        paginated_data = calculatePaginationData(members_report, page, limit, total_records)

        response['status'] = 200
        response['data'] = {
            "members_report": paginated_data,
            "filters": filters_response["filters"]
        }

    finally:
        conn.close()
    return response
