{"status": "active", "permissions": {"orders": {"name": "orders", "api": "/orders", "operations": ["read"], "children": {}}, "products": {"name": "products", "api": "/products", "operations": ["read"], "children": {}}, "customers": {"name": "customers", "api": "/customers", "operations": ["read"], "children": {}}, "storefronts": {"name": "storefronts", "api": "/storefronts", "operations": ["read", "write"], "children": {"navigation": {"name": "navigation", "api": "/storefronts/navigation", "operations": ["read", "write"], "children": {}}, "storeinformation": {"name": "storeinformation", "api": "/store-info", "operations": ["read", "write"], "children": {}}, "themes": {"name": "themes", "api": "/storefronts/themes", "operations": ["read", "write"], "children": {}}}}, "cms": {"name": "cms", "api": "/cms", "operations": ["read", "write"], "children": {"webpages": {"name": "webpages", "api": "/cms/webpages", "operations": ["read", "write"], "children": {}}, "components": {"name": "components", "api": "/cms/components", "operations": ["read", "write"], "children": {}}}}, "utility": {"name": "utility", "api": "/utils", "operations": ["read", "write"], "children": {"sheets": {"name": "sheets", "api": "/utils/sheets", "operations": ["read", "write"], "children": {}}}}, "analytics": {"name": "analytics", "api": "/analytics", "operations": ["read"], "children": {}}, "settings": {"name": "settings", "api": "/settings", "operations": ["read", "write"], "children": {"users": {"name": "users", "api": "/settings/users", "operations": ["read", "write"], "children": {}}, "api token": {"name": "api token", "api": "/settings/api-token", "operations": ["read", "write"], "children": {}}}}}}