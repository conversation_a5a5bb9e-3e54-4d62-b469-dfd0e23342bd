class AnalyticsDB:
    schema_name = "analytics"
    products_trend_table = "analytics_products_trend"
    variants_trend_table = "analytics_variants_trend"
    replenishment_products_table = "replenishment_products"
    replenishment_variants_table = "replenishment_variants"
    profitability_customers_trend_table = "profitability_customers_trend"
    profitability_products_revenue_table = "profitability_products_revenue"
    profitability_customers_shipping_cost_table = "profitability_customers_shipping_cost"
    order_line_items_analytics_table = "bc_order_line_items"
    order_analytics_table = "bc_orders"
    current_month_daily_sales_table = "current_month_daily_sales"
    current_month_daily_sales_variants_table = "current_month_daily_sales_variants"

    @classmethod
    def get_products_trend_table(cls):
        return cls.schema_name + "." + cls.products_trend_table
    @classmethod
    def get_variants_trend_table(cls):
        return cls.schema_name + "." + cls.variants_trend_table
    @classmethod
    def get_replenishment_products_table(cls):
        return cls.schema_name + "." + cls.replenishment_products_table
    @classmethod
    def get_replenishment_variants_table(cls):
        return cls.schema_name + "." + cls.replenishment_variants_table
    @classmethod
    def get_customers_trend_table(cls):
        return cls.schema_name + "." + cls.profitability_customers_trend_table
    @classmethod
    def get_products_revenue_table(cls):
        return cls.schema_name + "." + cls.profitability_products_revenue_table
    @classmethod
    def get_customers_trend_shipping_cost_table(cls):
        return cls.schema_name + "." + cls.profitability_customers_shipping_cost_table
    @classmethod
    def get_order_line_items_analytics_table(cls):
        return cls.schema_name + "." + cls.order_line_items_analytics_table
    @classmethod
    def get_order_analytics_table(cls):
        return cls.schema_name + "." + cls.order_analytics_table
    
    @classmethod
    def get_current_month_daily_sales_table(cls):
        return cls.schema_name + "." + cls.current_month_daily_sales_table
    @classmethod
    def get_current_month_daily_sales_variants_table(cls):
        return cls.schema_name + "." + cls.current_month_daily_sales_variants_table
