from flask import request
import logging
from api import APIResource
import traceback
from utils import bc

logger = logging.getLogger()

class BCApi(APIResource):
    def post_executor(self, request, token_payload, store):
        try:
            customer_id = None
            req_body = request.get_json(force=True)
            status, res_body = bc.process_bc_api_request(store, req_body, customer_id)
            return res_body, status
        finally:
            logger.debug("Exiting LoginAPI POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)
