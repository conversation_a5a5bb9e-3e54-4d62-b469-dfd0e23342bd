import requests
import time
from analytics import calculate_pagination
from mongo_db import catalog_db, customer_db
from new_pgdb import order_consignment_db
import new_pgdb
from utils import customer_util, redis_util, store_util,order_util
from utils.common import calculatePaginationData, parse_json, convert_to_timestamp
import logging
import traceback
import json
from sqlalchemy import text
from datetime import datetime
from orders.view_orders import orders_list
from customers.all_customer import customer_tags
from orders.view_orders.orders_list import _get_customer_price_list_and_group, check_line_item_availability
import new_mongodb

logger = logging.getLogger()

# from pg_db import get_connection, run_query

def get_bc_orders(store, query_params):    
    req_body = {}
    
    if 'status_id' in query_params:
        if 'channel_id' in query_params:
            req_body = {
            'method': 'get',
            'url': "v2/orders",
            'query_params': { 
                'channel_id':query_params['channel_id'],
                'status_id': query_params['status_id'],
                'sort': query_params['sort'],
                'page':query_params['page'],
                'limit':query_params['limit'],
                
            }
        } 
        else:
            req_body = {
            'method': 'get',
            'url': "v2/orders",
            'query_params': { 
                'status_id': query_params['status_id'],
                'sort': query_params['sort'],
                'page':query_params['page'],
                'limit':query_params['limit']
            }
        }
    else:
        if 'channel_id' in query_params:
            req_body = {
            'method': 'get',
            'url': "v2/orders",
            'query_params': { 
                'channel_id':query_params['channel_id'],
                'sort': query_params['sort'],
                'page':query_params['page'],
                'limit':query_params['limit'],
                
            }
        } 
        else: 
            req_body = {
            'method': 'get',
            'url': "v2/orders",  
            'query_params': {                 
                'sort': query_params['sort'],
                'page':query_params['page'],
                'limit':query_params['limit']
            }          
        }

    bc_api = store_util.get_bc_api_creds(store)
    res = fetch_all_orders(bc_api, req_body) 

    return res

def get_bc_order_by_id(store, filter):
    result= []
    req_body = {
        'method': 'get',
        'url': f"v2/orders/{filter}", 
    }
    api_data = store_util.get_bc_api_creds(store)
    res = call_api(api_data, req_body['method'], req_body['url'] , {}, {})
    if res and res.status_code == 200:
        result = [res.json()]
        
    return result

def get_bc_cart_products(api_data, cart_id):
    res = call_api(api_data, "GET", "v3/carts/" + cart_id, {}, {})

    cart_products = None
    if res and res.status_code == 200:
        cart_products = res.json()

    return cart_products

def get_bc_order_products(api_data, order_id, store, price_list_id, user, new_products):
    url = "v2/orders/" + order_id + "/products"
    req_body = {
        'method': 'get',
        'url': url
    }
    if new_products:
        # # Convert to list of SKUs if it's a string
        if isinstance(new_products, str):
            new_products = [sku.strip() for sku in new_products.split(',') if sku.strip()]
        elif not isinstance(new_products, list):
            new_products = [str(new_products).strip()] if new_products else []
        product_details_list = check_line_item_availability(store, order_id, new_products, price_list_id)
    
    order_products = fetch_all(api_data, req_body)
    
    # Append all product details to order_products
    if new_products and product_details_list['status'] == 200:
        # Rename fields in product details list
        for product in product_details_list['data']:
            product['base_price'] = product.pop('unit_price')
            product['base_total'] = product.pop('total')
            product['is_new_item'] = True
        
        # Extend order_products with all product details
        order_products.extend(product_details_list['data'])

    # print(order_products, "order_products")
    variant_ids = []
    product_ids = []
    skus = []
    if order_products:
        for product in order_products:
            variant_ids.append(product['variant_id'])
            product_ids.append(product['product_id'])
            skus.append(product['sku'])

        customer_id = order_util.fetch_customer_id_from_order(store['id'], order_id)

        products = catalog_db.get_productsList_by_id(store, [p['product_id'] for p in order_products])
        result = order_consignment_db.OrderConsignmentSchema.get_order(store, order_id)
        order_type = order_consignment_db.OrderConsignmentSchema.get_order_type(store, order_id) if result > 0 else ""

        # Fetch variant prices based on variant_ids and price_list_id
        if price_list_id:
            variant_prices = order_util.get_variant_prices(store['id'], variant_ids, price_list_id, customer_id)
        
        price_list_ids = _get_price_list_ids_to_fetch_accepted_prices(store, customer_id)
        
        accepted_prices = order_util.get_accepted_prices(store['id'], variant_ids, price_list_ids, customer_id) if price_list_ids != [] else {}

        cost_map = order_util.get_cost_of_line_items(store['id'], variant_ids, order_id)

        classifications_map = order_util.get_classifications(store['id'], skus)

        for product in order_products:
            product['order_type'] = order_type
            options = product.get('product_options', [])
            if 'variant_name' not in product:
                product['variant_name'] = ''

            if options:
                product['variant_name'] = ' - '.join(option['display_value'] for option in options)

            # Map each product with the correct image and price
            for item in products:
                if product.get("product_id") == item.get("id"):
                    images = item.get("images", [])
                    if images and len(images) > 0:
                        product["images"] = images[0].get("url_thumbnail", "")
                    else:
                        product["images"] = ""

                    # Set the purchasing_disabled flag from the variant
                    variant_id = product.get("variant_id")
                    product["purchasing_disabled"] = item["variants"].get(variant_id, {}).get("purchasing_disabled", False)
                    break  # Stop searching once data is set
            
            # Set the price from the price list if available
            product['price_from_price_list'] = variant_prices.get(product['variant_id'], {}).get('price') if price_list_id else None
            product['pricing_group'] = variant_prices.get(product['variant_id'], {}).get('pricing_group') if price_list_id else None
            product['cost'] = cost_map.get(product['variant_id'], {}) if cost_map else None
            product['classification'] = classifications_map.get(product['sku'], None)
            product['accepted_prices'] = accepted_prices.get(product['variant_id'], {}).get('accepted_prices', [])

    return order_products


def get_bc_order_products_new(api_data, order_id, store):
    url = "v2/orders/" + order_id + "/products"
    req_body = {
        'method': 'get',
        'url': url
    }
    order_products = None
    order_products = fetch_all(api_data, req_body)

    ids = []
    if order_products:
        for obj in order_products:
                ids.append(obj['product_id'])

        products = catalog_db.get_productsList_by_id(store, ids)
        
        for product in order_products:
        
            for item in products:
                image=item["images"][0]
                if product is not None and product.get("product_id") == item.get("id"):
                    product["images"]=image["url_thumbnail"]
    return order_products


def get_order_products(api_data, order_id):
    url = "v2/orders/" + order_id + "/products"
    order_products = call_api(api_data, "GET", url)
    if order_products.status_code == 200:
        return order_products.json()
    else:
        return []


def get_bc_order_shipping_address(api_data, order_id, query_params):
    res = call_api(api_data, "GET", "v2/orders/" + order_id +
                   "/shipping_addresses", query_params, {})
    order_shipping_address_details = None

    if res and res.status_code == 200:
        order_shipping_address_details = res.json()

    return order_shipping_address_details


def get_bc_customers(api_data, id,store):
    res = call_api(api_data, "GET", "v2/customers/" + id, {}, {})
    orders_len,count=get_customer_orders_total_count(api_data,id,{})
    customers = None

    if res and res.status_code == 200:
        customers = res.json()
    if 'date_created' in customers:
        customers['date_created'] = convert_to_timestamp(customers['date_created'])
    if 'date_modified' in customers:
        customers['date_modified'] = convert_to_timestamp(customers['date_modified'])

    customers['order_count']=orders_len
    customers['total_count']=count

    db = new_mongodb.get_store_db_client_for_store_id(store['id'])

    fields = [
        'Website', 'Status', 'Owner', 'Last_Activity', 'Average_Order_Value__c',
        'Total_Spent_Numeric__c', 'AnnualRevenue', 'Credit_Limit__c',
        'credit_used', 'outstanding_balance'
    ]

    projection = {f"salesforce_data.{field}": 1 for field in fields}

    mongo_customer = db.customers.find_one({"id": int(id)}, projection)

    if mongo_customer and 'salesforce_data' in mongo_customer:
        sf_data = mongo_customer['salesforce_data']
        customers.update({
            'website': sf_data.get('Website', None),
            'status': sf_data.get('Status', None),
            'owner': sf_data.get('Owner', None),
            'last_activity': sf_data.get('Last_Activity', None),
            'average_order_value': sf_data.get('Average_Order_Value__c', None),
            'total_spent': sf_data.get('Total_Spent_Numeric__c', None),
            'annual_revenue': sf_data.get('AnnualRevenue', None),
            'credit_limit': sf_data.get('Credit_Limit__c', None),
            'credit_used': sf_data.get('credit_used', None),
            'outstanding_balance': sf_data.get('outstanding_balance', None),
        })

    return customers

def get_customer_consignment_orders(store, customer_id, query_params):
    response = {
        "status" :400
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        count_query = f"""SELECT COUNT(DISTINCT soc.order_id) FROM salesforce_order_consignment AS soc
                    WHERE soc.customer_id = {customer_id} AND soc.order_status IN ('Unpaid', 'Partial')"""
        
        res = conn.execute(text(count_query))
        total_count_row = res.fetchone()
        total_count = total_count_row[0] if total_count_row else 0

        query =  f"""SELECT soc.customer_id, soc.order_id, soc.total_amount as order_total, soc.due_amount, soc.paid_amount,
                    o.order_created_date_time, o.order_status, o.order_status_id, o.total_including_tax as bc_order_total, o.total_excluding_tax as bc_order_total_excluding_tax
                    FROM salesforce_order_consignment soc
					JOIN 
                        orders o on soc.order_id = o.order_id
                    WHERE soc.customer_id = {customer_id} AND soc.order_status IN ('Unpaid', 'Partial')
                    GROUP BY
                        soc.customer_id, soc.order_id, soc.order_status, soc.total_amount, soc.due_amount, soc.paid_amount, o.order_created_date_time, o.order_status, o.order_status_id, o.total_including_tax, o.total_excluding_tax
                    ORDER BY o.order_created_date_time desc"""
                      
        page = int(query_params.get('page', 1))
        limit = int(query_params.get('limit', 10))
        offset = (page - 1) * limit
        query += f" LIMIT {limit} OFFSET {offset}"
        results = conn.execute(text(query))

        orders = []
        for row in results.fetchall():
            due_value = "{:,.0f}".format(row[3]) if row[3] else 0
            paid_value = "{:,.0f}".format(row[4]) if row[3] else 0
            reward_value = customer_db.get_order_reward_points(store,row[1])
            row_data = {
                'customer_id': row[0],
                'order_id': row[1],
                'sf_total_amount': row[2],
                'sf_due_amount': due_value,
                'sf_paid_amount': paid_value,
                'order_created_date_time': convert_to_timestamp(row[5]),
                'order_status': row[6] if row[6] else orders_list.get_order_status_name(row[7]),
                'order_status_id': row[7],
                'bc_total_including_tax': row[8],
                'bc_total_excluding_tax': row[9],
                'rewards': reward_value or 0
            }
            orders.append(row_data)

        data = calculate_pagination(orders, page, limit, total_count)

        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 404
            response['message'] = 'No data found.'
    finally:
        if conn:
            conn.close()
    return response

def get_customer_orders_total_count(api_data, id,query_params):
   
    req_body = {
        'method': 'get',
        'url': "v2/orders",
        'query_params': {
            'customer_id': id,
        }
    }
    res_all = fetch_all(api_data, req_body)
    if res_all:
        orders_len=len(res_all)
        count=sum(float(order['total_inc_tax']) for order in res_all)
    else:
        orders_len=0
        count=0
    return orders_len,count

def get_customer_orders_count(api_data, id,query_params):
    req_body = {
        'method': 'get',
        'url': "v2/orders",
        'query_params': {
            'customer_id': id,
        }
    }
    res_all = fetch_all(api_data, req_body)
    if res_all:
        res_len=len(res_all)
    else:
        res_len=0
    return res_len

def get_customer_addresses(api_data, id,query_params):
   
    req_body = {
        'query_params': {
            'customer_id:in': id,
        }
    }
    res = call_api(api_data, "GET", "v3/customers/addresses", req_body['query_params'], {})
    addresses={}
    if res:
        addresses = res.json()
    return addresses

    

def get_customer_order_history(api_data, id,query_params,store):
    # sort = 'date_created:desc'
    sort_by = query_params['sort_by']
    sort_order = query_params['sort_order']
    sort = f"{sort_by}:{sort_order}"
    if id:
        req_body = {
        'query_params': {
            'customer_id': id,
            'page':query_params['page'],
            'limit':query_params['limit'],
            'sort':sort
            
        }
    }
    res = call_api(api_data, "GET", "v2/orders", req_body['query_params'], {})
    orders = {}
    if res and res.status_code == 200:
        orders = res.json()

    order_ids=customer_db.get_customer_from_loyalty_history_using_id(store,id)
    orderData=[]
    for order in orders:
        
        orderList={}
        orderList['id']=order['id']
        orderList['status']=order['status']
        orderList['total']=order['total_inc_tax']
        orderList['date_created']=order['date_created']
        orderList['comment']=order['customer_message']
        result=order_consignment_db.OrderConsignmentSchema.get_order(store, order['id']) 
        
        if result>0:
            order_type=order_consignment_db.OrderConsignmentSchema.get_order_type(store, order['id'])
            orderList['type']=order_type
        else:
            orderList['type']=""
        
        orderData.append(orderList)
    orders_len=get_customer_orders_count(api_data,id,{})
    if orderData:
        data = calculatePaginationData(orderData, query_params['page'], query_params['limit'], orders_len)
    else:
        data={}

    return data

def  get_customer_orders(api_data, id,query_params,store):
    sort = 'date_created:desc' if not query_params.get('sort_by') else query_params.get('sort_by')
    if id:
        req_body = {
        'query_params': {
            'customer_id': id,
            'page':query_params.get('page', 1),
            'limit':query_params.get('limit', 10),
            'sort':sort
            
        }
    }
    res = call_api(api_data, "GET", "v2/orders", req_body['query_params'], {})
    orders = {}
    if res and res.status_code == 200:
        orders = res.json()

    order_ids=customer_db.get_customer_from_loyalty_history_using_id(store,id)
    orderData=[]
    for order in orders:
        
        orderList={}
        orderList['id']=order['id']
        orderList['status']=order['status']
        orderList['total']=order['total_inc_tax']
        orderList['rewards']=0
        orderList['date_created']=order['date_created']
        orderList['payment_status']=order['payment_status']
        orderList['payment_method']=order['payment_method']
        orderList['items_total']=order['items_total']
        orderList['channel_id']=order['channel_id']
        orderList['billing_address']=order['billing_address']
        if order['id'] in order_ids:
           reward_value = customer_db.get_order_reward_points(store,order['id'])
           orderList['rewards'] = reward_value
        orderData.append(orderList)

    return orderData

def get_bc_customer_group_by_id(api_data, customer_group_id, query_params):
    res = call_api(api_data, "GET", "v2/customer_groups/" +
                   customer_group_id, query_params, {})
    customer_group = None

    if res and res.status_code == 200:
        customer_group = res.json()

    return customer_group


def get_bc_customer_shipping_address_details(api_data, customer_id, query_params, store):
    res = call_api(api_data, "GET", "v2/customers/" + customer_id + "/addresses", query_params, {})
    shipping_address = None

    if res and res.status_code == 200:
        shipping_address = res.json()

        # db = new_mongodb.get_store_db_client_for_store_id(store['id'])
        # customer = db.customers.find_one({"id": int(customer_id)})

        # billing = customer.get("salesforce_data", {}).get("BillingAddress") if customer else None
        # shipping = customer.get("salesforce_data", {}).get("ShippingAddress") if customer else None

        # def normalize(value):
        #     return (value or '').strip().lower()

        # def is_match(bc_addr, mongo_addr):
        #     if not bc_addr or not mongo_addr:
        #         return False

        #     match = (
        #         normalize(bc_addr.get("street_1")) == normalize(mongo_addr.get("street")) and
        #         normalize(bc_addr.get("city")) == normalize(mongo_addr.get("city")) and
        #         normalize(bc_addr.get("state")) == normalize(mongo_addr.get("state")) and
        #         normalize(bc_addr.get("zip")) == normalize(mongo_addr.get("postalCode")) and
        #         normalize(bc_addr.get("country")) == normalize(mongo_addr.get("country"))
        #     )
        #     return match

        # for addr in shipping_address:
        #     addr["is_billing"] = is_match(addr, billing)
        #     addr["is_shipping"] = is_match(addr, shipping)

    return shipping_address


def get_bc_customer_groups(api_data, query_params):
    res = call_api(api_data, "GET", "v2/customer_groups", query_params, {})
    customer_groups = None

    if res and res.status_code == 200:
        customer_groups = res.json()

    return customer_groups

def update_products_visibility(api_data, product_ids, visible):
    try:
        req_body = []
        
        for id in product_ids:
            req_body.append({
                'id': id,
                'is_visible': bool(visible) 
            })

        res = call_api(api_data, "PUT", "v3/catalog/products", {}, req_body)

        return res.json()
    except Exception as e:
        logger.error(traceback.format_exc())


def get_bc_webpages(api_data):
    req_body = {
        'method': 'get',
        'url': "v3/content/pages",
        'query_params': {
            'include': 'body',
        }
    }
    res = fetch_all(api_data, req_body)

    return res


def get_bc_categories(api_data):
    req_body = {
        'method': 'get',
        'url': "v3/catalog/trees/categories",
        'query_params': {           
        }
    }
    res = fetch_all(api_data, req_body)

    return res

def get_bc_brands(api_data):
    req_body = {
        'method': 'get',
        'url': "v3/catalog/brands",
        'query_params': {           
        }
    }
    res = fetch_all(api_data, req_body)

    return res

def get_bc_promotions(api_data):
    req_body = {
        'method': 'get',
        'url': "v3/promotions",
        'query_params': {   
            "status" : "ENABLED",
            "sort" : "name",
            "direction" : "asc"        
        }
    }
    res = fetch_all(api_data, req_body)

    return res

def get_bc_blogs(api_data):
    req_body = {
        'method': 'get',
        'url': "v2/blog/posts",
        'query_params': {
            'include': 'body',
        }
    }
    res = fetch_all(api_data, req_body)

    return res

def fetch_all_orders(api_data, req_body):
    resources = []
    # print(req_body,"reqbod")
    page = 1
    limit = 250
    query_params = {}
    if "query_params" in req_body and req_body["query_params"]:
        query_params = req_body["query_params"]

    # done = False
    # while not done:
        # query_params["page"] = req_body['page']
        # query_params["limit"] = req_body['limit'];
    req_body["query_params"] = query_params
    # print(req_body,"body")
    response = process_api(api_data, req_body)
    code = response["status_code"]
    data = response["data"]
        # if response has no content.
    # if code == 204:
    #     done = True

    # if code == 200:
    #         # condition for checking is current page is last page or not.
    #         if isinstance(data, list) and len(data) == 0:
    #             done = True

    #         # iterate through each elements and append in resources list.
    #         if isinstance(data, list):
    #             for item in data:
    #                 resources.append(item)

    #         page += 1
    
    return data

def fetch_all(api_data, req_body):
    resources = []
    page = 1
    limit = 250
    query_params = {}
    if "query_params" in req_body and req_body["query_params"]:
        query_params = req_body["query_params"]

    done = False
    while not done:
        query_params["page"] = page
        query_params["limit"] = limit
        req_body["query_params"] = query_params

        response = process_api(api_data, req_body)
        code = response["status_code"]
        data = response["data"]

        # if response has no content.
        if code == 204:
            done = True

        if code == 200:
            # condition for checking is current page is last page or not.
            if isinstance(data, list) and len(data) == 0:
                done = True

            # iterate through each elements and append in resources list.
            if isinstance(data, list):
                for item in data:
                    resources.append(item)

            page += 1

    return resources


def process_api(api_data, req_body, exclude_meta=True, customer_id=None):
    query_params = {}
    if "query_params" in req_body:
        query_params = req_body["query_params"]

    body = {}
    if "body" in req_body:
        body = req_body["body"]

    res = call_api(
        api_data, req_body["method"], req_body["url"], query_params, body, customer_id)

    response_body = {}
    retry_count = 0
    sleep_time = 1
    while retry_count < 5:
        try:
            # While no content ...
            if res.status_code == 204:
                retry_count = 5

            response_body = res.json()
            if res.status_code >= 200 and res.status_code <= 299:
                retry_count = 5
        except Exception as e:
            logger.error(traceback.format_exc())
        if retry_count < 4:
            time.sleep(sleep_time)
        sleep_time *= 2
        retry_count += 1
    if exclude_meta and "meta" in response_body:
        return {"status_code": res.status_code, "data": response_body["data"]}
    else:
        return {"status_code": res.status_code, "data": response_body}
    
def process_api_for_listing(api_data, req_body, exclude_meta=True, customer_id=None):
    query_params = {}
    if "query_params" in req_body:
        query_params = req_body["query_params"]

    body = {}
    if "body" in req_body:
        body = req_body["body"]

    res = call_api_for_listing(
        api_data, req_body["method"], req_body["url"], query_params, body, customer_id)

    response_body = {}
    retry_count = 0
    sleep_time = 1
    while retry_count < 5:
        try:
            # While no content ...
            if res.status_code == 204:
                retry_count = 5

            response_body = res.json()
            if res.status_code >= 200 and res.status_code <= 299:
                retry_count = 5
        except Exception as e:
            logger.error(traceback.format_exc())
        if retry_count < 4:
            time.sleep(sleep_time)
        sleep_time *= 2
        retry_count += 1
    if exclude_meta and "meta" in response_body:
        return {"status_code": res.status_code, "data": response_body["data"]}
    else:
        return {"status_code": res.status_code, "data": response_body}
    

def process_zohoApi_for_listing(api_data, req_body, exclude_meta=True, customer_id=None):
   
    res = call_zohoApi_for_listing(
        api_data, req_body["Authorization"], req_body["appName"],req_body["form"],req_body["method"],req_body["body"])

    response_body = {}
    retry_count = 0
    sleep_time = 1
    while retry_count < 5:
        try:
            # While no content ...
            if res.status_code == 204:
                retry_count = 5

            response_body = res.json()
            if res.status_code >= 200 and res.status_code <= 299:
                retry_count = 5
        except Exception as e:
            logger.error(traceback.format_exc())
        if retry_count < 4:
            time.sleep(sleep_time)
        sleep_time *= 2
        retry_count += 1
    if exclude_meta and "meta" in response_body:
        return {"status_code": res.status_code, "data": response_body["data"]}
    else:
        return {"status_code": res.status_code, "data": response_body}


def process_bc_api_request(store, req_body, customer_id=None):
    bc_api = store_util.get_bc_api_creds(store)
    res = process_api(api_data=bc_api, req_body=req_body, customer_id=customer_id)
    return res['status_code'], res['data']

def bc_api_request(store, req_body, customer_id=None):
    bc_api = store_util.get_bc_api_creds(store)
    res = process_api_for_listing(api_data=bc_api, req_body=req_body, customer_id=customer_id)
    result=res['data']
    key_list = list(result[0].keys())
    return res['status_code'], key_list

def zoho_api_request(store, req_body, customer_id=None):
    bc_api = store_util.get_bc_api_creds(store)
    res = process_zohoApi_for_listing(api_data=bc_api, req_body=req_body, customer_id=customer_id)
    result=res['data']
    # key_list = list(result[0].keys())
    return res['status_code'], result
# BC "Get All Orders" api not providing total number of orders count...
# BC has a separate APi to get that...


def get_bc_total_orders_count(api_data,body):
    res = call_api(api_data, "GET", "v2/orders/count", body, {})
    total_orders_count = None
    if res and res.status_code == 200:
        res = res.json()
        total_orders_count = res['count']

    return total_orders_count


def call_api(api_data, method, url, query_params=None, req_body=None, customer_id=None):
    headers = {}
    if customer_id:
        headers["X-Bc-Customer-Id"] = str(customer_id)
    headers["X-Auth-Client"] = api_data["client_id"]
    headers["X-Auth-Token"] = api_data["access_token"]
    headers["Accept"] = "application/json"
    api_url = "https://api.bigcommerce.com/stores/" + \
        api_data["store_hash"] + "/" + url
    method = method.upper()
    res = None
    if "GET" == method:
        res = requests.get(url=api_url, params=query_params, headers=headers)
    elif "POST" == method or "PUT" == method:
        if "POST" == method:
            res = requests.post(url=api_url, params=query_params,
                                json=req_body, headers=headers)
        else:
            res = requests.put(url=api_url, params=query_params,
                               json=req_body, headers=headers)
    elif "DELETE" == method:
        if query_params is not None:
            res = requests.delete(url=api_url, params=query_params, headers=headers)
        else:
            res = requests.delete(url=api_url, headers=headers)

    return res


def call_api_skuvault(method, url, query_params=None, req_body=None, customer_id=None):
    headers = {}
    headers["Accept"] = "application/json"
    headers["Content-Type"] = "application/json"
    api_url = "https://app.skuvault.com/api/" + url
    method = method.upper()
    res = None
    if "GET" == method:
        res = requests.get(url=api_url, params=query_params, headers=headers)
    elif "POST" == method or "PUT" == method:
        if "POST" == method:
            res = requests.post(url=api_url, params=query_params,
                                json=req_body, headers=headers)
        else:
            res = requests.put(url=api_url, params=query_params,
                               json=req_body, headers=headers)
    elif "DELETE" == method:
        if query_params is not None:
            res = requests.delete(url=api_url, params=query_params, headers=headers)
        else:
            res = requests.delete(url=api_url, headers=headers)

    return res

def call_api_for_listing(api_data, method, url, query_params=None, req_body=None, customer_id=None):
    headers = {}
    if customer_id:
        headers["X-Bc-Customer-Id"] = str(customer_id)
    headers["X-Auth-Client"] = api_data["client_id"]
    headers["X-Auth-Token"] = api_data["access_token"]
    headers["Accept"] = "application/json"
    api_url = "https://api.bigcommerce.com/stores/" + \
        api_data["store_hash"] + "/" + url
    method = method.upper()
    res = None
    if "GET" == method or "POST" == method or "PUT" == method:
        res = requests.get(url=api_url, params=query_params, headers=headers)
    return res

def call_zohoApi_for_listing(api_data, auth,appName, form,method,body, query_params=None, req_body=None, customer_id=None):
    headers = {}
    if auth:
         headers["Authorization"] = auth
    if "GET" == method:
        api_url = "https://creator.zoho.in/api/v2/disha.adhikari_atlantixdigital/" + \
        appName + "/report/" + form  
        res = None
        res = requests.get(url=api_url, headers=headers)
    elif "POST" == method:
        api_url = "https://creator.zoho.in/api/v2/disha.adhikari_atlantixdigital/" + \
        appName + "/form/" + form  
        res = None
        res = requests.post(url=api_url, headers=headers, json=body)
    return res

def _process_bc_graphql_request_internal(bc_store_url, token, query, customer_id=None):
    headers = {}
    headers["Authorization"] = "Bearer " + token
    headers["Accept"] = "application/json"
    if customer_id:
        headers["X-Bc-Customer-Id"] = str(customer_id)
    if not bc_store_url.endswith("/"):
        bc_store_url = bc_store_url + "/"
    api_url = bc_store_url + "graphql"
    res = requests.post(url=api_url, json={"query": query}, headers=headers)
    return res

def process_bc_graphql_request(store, query, customer_id=None):
    retry_count = 0
    status_code = 500
    content = None
    bc_api = store_util.get_bc_api_creds(store)
    while retry_count < 2:
        try:
            token = store_util.get_graphql_token(store)
            res = _process_bc_graphql_request_internal(bc_api['store_url'], token, query, customer_id)
            status_code = res.status_code
            if res.status_code == 401:
                token = store_util.reset_graphql_token(store)
            elif res.status_code < 500:
                content = res.json()
                retry_count = 2
            else:
                content = res.content
                retry_count = 2
        except Exception as e:
            logger.error(f"process_bc_graphql_request: Exception caught: {str(traceback.format_exc())}")
        retry_count += 1
    return status_code, content

def get_bc_graphql_token(api_data, expires_at, domain=None):
    req_body = {"channel_id": 1, "expires_at": int(expires_at)}
    # if domain:
    #     req_body["allowed_cors_origins"] = [domain]
    res = call_api(
        api_data, "POST", "v3/storefront/api-token-customer-impersonation", {}, req_body
    )
    result = None
    if res and res.status_code == 200:
        res = res.json()
        result = res["data"]["token"]
    return result

def get_order_products_for_audit_report(store, order_id):
    url = "v2/orders/" + order_id + "/products"
    req_body = {
        'method': 'get',
        'url': url
    }
    api = store_util.get_bc_api_creds(store)
    order_products = fetch_all(api, req_body)

    variant_ids = []
    if order_products:
        for product in order_products:
            variant_ids.append(product['variant_id'])

        customer_id = order_util.fetch_customer_id_from_order(store['id'], order_id)
        price_list_ids = _get_price_list_ids_to_fetch_accepted_prices(store, customer_id)

        accepted_prices = order_util.get_accepted_prices(store['id'], variant_ids, price_list_ids, customer_id) if price_list_ids != [] else {}

    return accepted_prices, order_products

def _get_price_list_ids_to_fetch_accepted_prices(store, customer_id):
    price_list_ids = []
    customer_price_list_and_group = _get_customer_price_list_and_group(store['id'], customer_id)

    if customer_price_list_and_group['status'] == 200:
        customer_data = customer_price_list_and_group['data']
        price_list = customer_data.get('price_list')
        customer_group_id = customer_data.get('customer_group_id')

        # Normalize keys to lowercase for case-insensitive matching
        price_list_mapping = {
            'vip': 52,
            'tcd': 14,
            'mvd': 15,
            'tier pro': 8,
            'tiered 2': 4,
            'tiered 1': 1
        }

        def normalize_price_list(value):
            if not value:
                return ''
            value = value.strip().lower()
            # Handle known typos/alternatives
            if value in ['tired pro', 'tier pro']:
                return 'tier pro'
            elif value in ['tiered 2', 'tier 2']:
                return 'tiered 2'
            elif value in ['tiered 1', 'tier 1']:
                return 'tiered 1'
            return value

        if price_list:
            normalized_price_list = normalize_price_list(price_list)
            if normalized_price_list in price_list_mapping:
                mapped_id = price_list_mapping[normalized_price_list]
                price_list_ids.append(mapped_id)

                # Special case: If VIP (52), also add 13
                if mapped_id == 52:
                    price_list_ids.append(13)
                if mapped_id == 14:
                    price_list_ids.extend([52,13])
                if mapped_id == 15:
                    price_list_ids.extend([52,13])
            else:
                if customer_group_id in [98, 103, 28]:
                    price_list_ids.extend([13, 8])
        else:
            if customer_group_id in [98, 103, 28]:
                price_list_ids.extend([13, 8])

    return price_list_ids
