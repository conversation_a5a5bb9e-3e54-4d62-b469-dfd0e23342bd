from datetime import datetime, timezone
import os
from mongo_db import  product_db, user_db
import new_mongodb
from cms import process_cdn_image_url
from new_mongodb import cms_db, insert_document_in_admin_collection, update_document_in_admin_collection
import new_utils
from utils import store_util
from bson import ObjectId
from pymongo.collation import Collation
from fields.cms_fields import cms_brand_fields
from utils.common import parse_json, processList, processListCategory
from werkzeug.utils import secure_filename
from flask import send_file, make_response
import logging

logger = logging.getLogger()

ALLOWED_EXTENSIONS = set(['png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov', 'wmv', 'mkv'])

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def change_file_name(filename):
    rendom = str(round(datetime.now(timezone.utc).timestamp()))
    if ('.' in filename):
        ext = filename.rsplit('.', 1)[1].lower()
        fname = rendom + '.' + ext
        return fname

def _create_reg_ex_query(payload, additionalQuery):
    query = {
        "$or": [],
    }    

    for i in payload["filterBy"]:
        query['$or'].append({i: {"$regex": payload['filter'], "$options": "i"}},)

    if "type" not in query and "type" in payload:
            query["type"] = payload["type"]

    if "status" in payload:
        if payload["status"] == "active":
            query["is_visible"] = True
        elif payload["status"] == "inactive":
            query["is_visible"] = False
        elif payload["status"] == "out_of_stock":
            query["inventory_level"] = 0              
    query.update(additionalQuery)
    return query

def _get_paginated_records_brands(store, payload, fields, additionalQuery):
    sort = {
        'sort_by': payload['sort_by'] or 'date_created'
    }

    if payload['sort_order'] == 'asc':
        sort['sort_order'] = 1
    else:
        sort['sort_order'] = -1

    limit = int(payload["limit"]) if payload.__contains__("limit") else 10
    page = int(payload["page"]) if payload.__contains__("page") else 1
    skips = payload['skips'] if payload.__contains__('skips') else 0

    query = _create_reg_ex_query(payload, additionalQuery) if len(payload["filterBy"]) else {}
    
    skips = limit * (page - 1)
    collation = Collation(locale='en', strength=2)
    cms_brand_collection = new_mongodb.StoreAdminDBCollections.CMS_BRANDS_COLLECTION
    data = new_mongodb.fetchall_documents_from_admin_collection(store['id'], cms_brand_collection, query, fields) \
            .collation(collation).sort(sort['sort_by'], sort['sort_order']).skip(skips).limit(limit)
    
    data = processListCategory(data)
    
    document_length = new_mongodb.count_documents_admin_collection(store['id'], cms_brand_collection, query)

    return parse_json(data), document_length, page, limit

def get_brands_data(store, body):    
    cdn_baseurl = store_util.get_cdn_base_url(store)    
    result = []        
    if 'brand_id' in body:
        res = {}
        brand_id = body['brand_id']
        query = {"_id": ObjectId(str(brand_id))}
        brand = new_mongodb.fetch_one_document_from_admin_collection(store['id'], \
                    new_mongodb.StoreAdminDBCollections.CMS_BRANDS_COLLECTION, query)
        
        if 'preview_state' in brand and brand['preview_state']:               
            preview_state = brand['preview_state']
        else:
            preview_state = brand['default_layout']

        preview_state = process_cdn_image_url(cdn_baseurl, preview_state)

        res['id'] = brand['id']
        res['name'] = brand['name']
        res['created_at'] = brand['created_at']
        res['updated_at'] = brand['updated_at']
        res['url'] = brand['url']
        res['is_customers_only'] = brand['is_customers_only']
        res['versions'] = preview_state
        result.append(res)
    else:            
        body['filterBy'] = ['name']            
        cms_brand_collection = new_mongodb.StoreAdminDBCollections.CMS_BRANDS_COLLECTION
        brands, total_data_length, paginationPage, limit = _get_paginated_records_brands(store, body, cms_brand_fields,'')
        brands_db = cms_db.get_all_brands(store)
        # brands = new_mongodb.fetchall_documents_from_admin_collection(store['id'], cms_brand_collection)
        nameResult = []
        for brand in brands_db:
            test = {}
            test['Name'] = brand['name']
            test['URL'] = brand['url']
            nameResult.append(test)

        for brand in brands:
            res = {}
            
            if brand['url'] is None or brand['url'] == "":
                continue
            product_count=product_db.get_product_count_by_brand_id(store,brand['bc_id'])
           
            # product_count = new_mongodb.fetchall_documents_from_storefront_collection(store['id'], new_mongodb.StoreDBCollections.PRODUCTS, {"brand_id": page['bc_id']})
            versions = brand['versions']
            if (len(versions) == 0):
                activeVersion = brand['default_layout']
            else:
                for version in versions:
                    if (version['status'] == 'active'):
                        activeVersion = version
                        break
            
            activeVersion = process_cdn_image_url(cdn_baseurl, activeVersion)
            
            res['id'] = brand['id']
            res['name'] = brand['name']
            res['created_at'] = brand['created_at']
            res['updated_at'] = brand['updated_at']
            res['url'] = brand['url']
            res['status'] = brand['status']
            res["image_url"]=brand['image_url']
            res["product_count"]=product_count
            res["type"] = brand["type"]
            res['versions'] = activeVersion

            result.append(res)
        result = new_utils.calculate_pagination(result, paginationPage, limit, total_data_length)
        result['meta']['name_info'] = nameResult
    return result  

def set_version(store, payload, id=None):
        response = {
            "status": 400            
        }
        created_by = {}
        if 'created_by' in payload:
            user = user_db.fetch_user_by_username(payload['created_by'])        
            created_by = {
                "user_id": str(user['_id']),
                "user_name": user['name']
            } 
        brand = cms_db.get_brand_by_bc_id(store, id)
        
        if brand:      
            brand_id = brand['_id']                                                                                     
            totalVersions = len(brand['versions'])
            versions = brand['versions']

            if totalVersions > 0:
                lastVersion = brand['versions'][-1]['version']
                for version in versions:
                    version['status'] = 'inactive'                    
            else:
                lastVersion = 0

            if (totalVersions >= 10):
                brand['versions'].pop(0)

            payload['created_by'] = created_by
            payload['created_at'] = int(datetime.now(timezone.utc).timestamp())
            payload['version'] = lastVersion + 1
            versions.append(payload)

            update_obj={                                                                            
                          "versions": brand['versions'],
                          "updated_at":  int(datetime.now(timezone.utc).timestamp()),
                          "image_url":payload['image_url']
                      }
            cms_db.update_brands(store, {"_id": ObjectId(brand_id)}, {"$set": update_obj})
                   
            response['message'] = "changes saved sucessfuly"
            response['status'] = 200
        else:
            response['message'] = "Brand not found."
            response['status'] = 404
        return response

def update_customer_only_flag(store, body, id=None):
        response = {
            "status": 400
        }
        if id:
            update_obj={                                                                             
                          "is_customers_only": body['is_customers_only'],                                                                             
                          "updated_at":  int(datetime.utcnow().timestamp())
                      }
            cms_db.update_brands(store, {"id": int(id)}, {"$set": update_obj})
            response['message'] = "Brand Updated successfully"
            response['status'] = 200        

        return response

def get_brand(store, brand_id=None):
        result = {}
        brand = cms_db.get_brand_by_id(store, brand_id)
        # brand = super().find_one({"_id": ObjectId(str(category_id))})
        if (len(brand['versions']) == 0):
            activeVersion = brand['default_layout']
        else:
            versions = brand['versions']
            for version in versions:
                if (version['status'] == 'active'):
                    activeVersion = version
                    break

        result['id'] = brand['id']
        result['name'] = brand['name']
        result['created_at'] = brand['created_at']
        result['updated_at'] = brand['updated_at']
        result['url'] = brand['url']
        result['image_url']=brand['image_url']
        # result['is_customers_only'] = category['is_customers_only']
        result['versions'] = activeVersion

        return result

def get_cms_versions(store, id=None):
        result = cms_db.get_brand_by_id(store, id)
        list = {}
        versionArray = []
        if result is not None:
            if (len(result['versions']) > 0):
               versions = result['versions']
               for version in versions:
                  versionArray.append(version['version'])

               list['webpageVersions'] = versionArray

        return list

def get_cms_versionData(store, body, id=None):
        list = {}
        result = cms_db.get_brand_by_id(store, id)
        if result is not None:
           if (len(result['versions']) == 0):
               activeVersion = result['default_layout']
           else:
               versions = result['versions']
               for version in versions:
                   if (version['version'] == int(body['version'])):
                      activeVersion = version
                      break

           list['id'] = result['id']
           list['name'] = result['name']
           list['created_at'] = result['created_at']
           list['updated_at'] = result['updated_at']
           list['url'] = result['url']
           list['image_url']=result['image_url']
          #  list['is_customers_only'] = result['is_customers_only'] 
           list['versions'] = activeVersion

        return list

def setImage(body):
        response = {
            "status": 400
        }

        if not os.path.exists('images/brands/images'):
            os.makedirs('images/brands/images')

        file = body['image']
        UPLOAD_FOLDER = os.path.join('images/brands/images')

        if (file.filename == ''):
            response['message'] = "No image selected for uploading"
            response['status'] = 500

        if (file and allowed_file(file.filename)):
            newName = change_file_name(file.filename)
            fname = secure_filename(newName)
            file.save(os.path.join(UPLOAD_FOLDER, fname))
            base_path = os.path.join(os.path.abspath(
                os.getcwd()), UPLOAD_FOLDER, newName)
            
            if '/app/images' in base_path:
                base_path = base_path.replace('/app/images', '')             
            response['message'] = base_path
            response['status'] = 200
        else:
            response['message'] = "Allowed image types are -> png, jpg, jpeg, gif, mp4, avi, mov, wmv, mkv"
            response['status'] = 500

        return response

def getImage(body):
    url = './images/brands/images/' + body['image']
    if not os.path.exists(url):
        return make_response({'error': 'Image not found'}, 404)
    return send_file(url, as_attachment=True)

def create_bc_brands(brand, req_body,store):
        created_by = {}
        if 'created_by' in req_body:
            user = user_db.fetch_user_by_username(req_body['created_by'])        
            created_by = {
                "user_id": str(user['_id']),
                "user_name": user['name']
            }           
        # Find in DB if available.x
        db_brand = cms_db.get_brand_by_bc_id(store, brand['id'])
          
        # description = brand["description"] 
        # if '%%GLOBAL_ShopPathSSL%%' in description or '%%GLOBAL_CdnStorePath%%' in description or '<a href="%%GLOBAL_ShopPathSSL%%' in description or '<a href="%%GLOBAL_CdnStorePath%%' in description:                                        
        #     print(category["id"], '  ||  ', category["name"], '  ||  ', category["custom_url"]["url"])

        # if '/searchresults.html?search_query=' in description:
        #     print(category["id"], '  ||  ', category["name"], '  ||  ', category["custom_url"]["url"])


        # description = description.replace('<a href="%%GLOBAL_ShopPathSSL%%', '<a href="') 
        # description = description.replace('<a href="%%GLOBAL_CdnStorePath%%', '<a href="')
        # description = description.replace("%%GLOBAL_ShopPathSSL%%", "https://cdn11.bigcommerce.com/s-964anr")
        # description = description.replace("%%GLOBAL_CdnStorePath%%", "https://cdn11.bigcommerce.com/s-964anr")   
        # description = description.replace("/searchresults.html?search_query=", "/search/?q=")             
        
           # If webpage not exists then create
        if not db_brand:            
            generate_brand(store['id'], brand, created_by)

        # If exists then check if it's modified or not if not then replace content
        if db_brand:                  
            update_brand(store['id'], brand, created_by) 
            getNavigationForBrands(brand,store)   

def getNavigationForBrands(brand,store):
            navigations=cms_db.get_sub_nav(store)
            navigations=processList(navigations)
            for navigation in navigations:
                navigation['navigation_id']=ObjectId(navigation['navigation_id'])
                is_updated=False
                for data in navigation['navigations']:
                    if data['type'] == 'Brands' and data['id'] == 'Brands'+'_'+str(brand['id']) :
                        is_updated=True
                        data['url']=brand['custom_url']['url']
                if is_updated:
                   cms_db.update_sub_nav_by_id(store,navigation)
            return

def check_all_brands(brands_arr, store):
        db_brands= cms_db.get_all_brands(store)
        for brand in db_brands:

            if int(brand['id']) not in brands_arr:
                cms_db.delete_brand_by_id(store, brand['id']) 

def generate_brand(store_id, brand, created_by):
            new_list={} 
            seo_details={}
            seo_details["page_name"]=brand["name"]
            seo_details["page_url"]=brand["custom_url"]["url"]
            seo_details["meta_title"]=brand["page_title"]
            seo_details["meta_description"]=brand["meta_description"]           
            
            new_list["id"]=brand["id"]
            new_list["name"]=brand["name"]
            new_list["type"]="brand"            
            new_list["url"]=brand["custom_url"]["url"]                                         
            new_list["default_layout"] = {}
            new_list["versions"]= [
                { 
                "name":brand["name"],
                "status":"active", 
                "class":"",
                "type": "brand",
                "seo_details":seo_details,
                "components":[
                    {
                    "id": 1,
                    "name": "HTML Block",
                    "code": "html_block",  
                    "variant": {
                        "id": "1",
                        "name": "HTML Block",
                        "admin_layout": "style1",
                        "class": [],                          
                        "config": {
                            "data": ""   
                                }
                    },  
                    "children": []
                    }
                ],
                "created_at":int(datetime.now(timezone.utc).timestamp()),
                "created_by": created_by,                                                                              
                "version":0,
            } ]
            new_list["preview_state"] = {} 
            new_list["updated_at"]=""
            new_list["created_at"]=int(datetime.now(timezone.utc).timestamp())
            new_list["status"] = "active"
            new_list["image_url"]=brand['image_url']

            return insert_document_in_admin_collection(store_id, "cms_brands", new_list)

def update_brand(store_id, brand, created_by):  
            # print(brand,"brand")         
            update_obj = {
                'name': brand['name'],
                'type': 'brand',
                'url': brand["custom_url"]["url"],                            
                'versions': [{
                    'name': brand['name'],
                    'status': 'active',
                    'class': '',  
                    'type': 'brand',                  
                    'components': [{
                            'id': 1,
                            'name': 'HTML Block',                            
                            'code': 'html_block',
                            'variant': {
                                "id": '1',
                                'name': 'HTML Block',
                                'admin_layout': 'style1',
                                'class': [],                                
                                'config': {
                                    'data': ""    
                                }
                            },
                            'children': []
                    }],
                    'seo_details': {
                        'page_name': brand['name'],
                        'page_url': brand["custom_url"]["url"],
                        'meta_title': brand['page_title'],
                        'meta_description': brand['meta_description']
                    },
                    'created_by': created_by,
                    'created_at': int(datetime.now(timezone.utc).timestamp()),
                    'updated_at': int(datetime.now(timezone.utc).timestamp()),
                    'version': 1,
                    
                }],
                'image_url':brand['image_url']
            }                               
            return update_document_in_admin_collection(store_id, "cms_brands", {"id": brand['id']}, {"$set": update_obj})
