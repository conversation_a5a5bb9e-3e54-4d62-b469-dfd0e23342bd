from datetime import datetime
from sqlalchemy import text
import new_mongodb
import new_pgdb
from new_pgdb import order_consignment_db, blocked_orders_db
import pg_db
from utils import redis_util, store_util, bc, order_util
from new_mongodb import customer_db, callback_db
import new_utils
from utils.common import calculatePaginationData, get_product_id_by_sku_from_mongo
from utils import order_util
from plugin import bc_order
from graphql import products_variant_query
from plugin import bc_price_list
from utils.common import convert_to_timestamp, fetch_static_price_lists, get_paginated_records_updated
import task
import logging
import traceback
from fields.products import products_suggestion_fields
from plugin import bc_products

logger = logging.getLogger()

def get_order_status_name(status_id):
    status_mapping = {
        0: "Incomplete",
        1: "Pending",
        2: "Shipped",
        3: "Partially Shipped",
        4: "Refunded",
        5: "Cancelled",
        6: "Declined",
        7: "Awaiting Payment",
        8: "Awaiting Pickup",
        9: "Awaiting Shipment",
        10: "Completed",
        11: "Awaiting Fulfillment",
        12: "Manual Verification Required",
        13: "Disputed",
        14: "Partially Refunded"
    }
    
    if status_id in status_mapping:
        return status_mapping[status_id]
    else:
        return ""
    

def _get_all_orders(store, payload):
    response = {'status': 400}
    conn = pg_db.get_connection()
    try:
        status_id = payload.get('status_id')
        status_id = int(status_id) if status_id not in ('', None) else None
        sort_by = str(payload.get('sort_by',''))
        sort_order = str(payload.get('sort_order', ''))
        page = int(payload.get('page', 1))
        limit = int(payload.get('limit', 10))
        filter = payload.get('filter', '')
        is_express_orders = payload.get('is_express_orders', 'false')
        rep_name = payload.get('rep_name', '')
        
        condition = ""
        status_id_condition = ""
        if status_id or status_id == 0:
            status_id_condition = f" WHERE o.order_status_id = {status_id}"

        rep_name_condition = ""
        if rep_name:
            rep_name_condition = f"scr.rep_name = '{rep_name}'"

        search_condition = ""
        if filter.isdigit():
            if is_express_orders.lower() == 'true':
                search_condition = f"CAST(eo.order_id AS TEXT) ILIKE '%{filter}%'"
            else:
                search_condition = f"CAST(o.order_id AS TEXT) ILIKE '%{filter}%'"
        else:
            search_condition = f"((CONCAT(cs.first_name, ' ', cs.last_name) ILIKE '%{filter}%') OR (cs.email ILIKE '%{filter}%'))"

        if status_id_condition and search_condition:
            condition = f"{status_id_condition} AND {search_condition}"
        elif status_id_condition:
            condition = status_id_condition
        elif search_condition:
            condition = f"WHERE {search_condition}"

        if rep_name_condition:
            if condition:
                condition += f" AND {rep_name_condition}"
            else:
                condition = f"WHERE {rep_name_condition}"

        
        order_express = False
        if is_express_orders.lower() == 'true':
            order_express = True                
            total_count_query = f"""SELECT COUNT(*) FROM (SELECT o.order_created_date_time AS date_created, eo.order_id, cs.customer_id, STRING_AGG(DISTINCT oc.order_type, ',') AS order_type
                            FROM express_orders AS eo LEFT JOIN orders AS o ON eo.order_id = o.order_id
                            LEFT JOIN customers AS cs ON eo.customer_id = cs.customer_id
                            LEFT JOIN order_consignment as oc ON o.order_id = oc.order_id
                            LEFT JOIN salesforce_customer_rep AS scr ON o.customer_id = scr.customer_id
                            {condition} GROUP BY o.order_created_date_time, eo.order_id, cs.customer_id) AS subquery""" 
            
            result_count = conn.execute(text(total_count_query))
            total_count = int(result_count.scalar())                               

            query = text(f"""
                            SELECT 
                                o.order_created_date_time AS date_created, 
                                eo.order_id AS id, 
                                o.order_status AS status, 
                                cs.first_name, 
                                cs.last_name, 
                                cs.customer_id, 
                                o.total_including_tax, 
                                o.order_status_id,
                                STRING_AGG(DISTINCT oc.order_type, ',') AS order_type,
                                scr.rep_name,
                                cs.email,
                                bo.id AS blocked_order_id,
                                o.updated_by,
                                scr.payment_term,
                                COALESCE((SELECT SUM(sod.amount_due) FROM salesforce_order_details sod WHERE sod.order_id = eo.order_id), 0) AS total_amount_due,
                                scr.credit_limit
                            FROM express_orders AS eo
                            LEFT JOIN orders AS o ON eo.order_id = o.order_id
                            LEFT JOIN customers AS cs ON eo.customer_id = cs.customer_id
                            LEFT JOIN order_consignment as oc ON o.order_id = oc.order_id
                            LEFT JOIN salesforce_customer_rep AS scr ON o.customer_id = scr.customer_id
                            LEFT JOIN blocked_orders AS bo ON o.order_id = bo.order_id
                            {condition}
                            GROUP BY o.order_created_date_time, eo.order_id,
                            o.order_status, cs.first_name, cs.last_name, cs.customer_id, o.total_including_tax, o.order_status_id, scr.rep_name, bo.id, o.updated_by, scr.payment_term, scr.credit_limit
                        """)
            
        else:
            order_express = False
            total_count_query = f"""SELECT COUNT(*) FROM (SELECT o.order_created_date_time AS date_created, o.order_id, cs.customer_id, STRING_AGG(DISTINCT oc.order_type, ',') AS order_type
                            FROM orders AS o LEFT JOIN customers AS cs ON o.customer_id = cs.customer_id
                            LEFT JOIN order_consignment AS oc ON o.order_id = oc.order_id
                            LEFT JOIN salesforce_customer_rep AS scr ON o.customer_id = scr.customer_id
                            {condition} GROUP BY o.order_created_date_time, o.order_id, cs.customer_id) AS subquery"""               
                            
            result_count = conn.execute(text(total_count_query))
            total_count = int(result_count.scalar())                                    

            query = text(f"""SELECT 
                                o.order_created_date_time AS date_created, 
                                o.order_id AS id, 
                                o.order_status AS status, 
                                cs.first_name, 
                                cs.last_name, 
                                cs.customer_id, 
                                o.total_including_tax, 
                                o.order_status_id,
                                STRING_AGG(DISTINCT oc.order_type, ',') AS order_type,
                                scr.rep_name,
                                cs.email,
                                bo.id AS blocked_order_id,
                                o.updated_by,
                                scr.payment_term,
                                COALESCE((SELECT SUM(sod.amount_due) FROM salesforce_order_details sod WHERE sod.order_id = o.order_id), 0) AS total_amount_due,
                                scr.credit_limit
                            FROM orders AS o
                            LEFT JOIN customers AS cs ON o.customer_id = cs.customer_id
                            LEFT JOIN order_consignment AS oc ON o.order_id = oc.order_id
                            LEFT JOIN salesforce_customer_rep AS scr ON o.customer_id = scr.customer_id
                            LEFT JOIN blocked_orders AS bo ON o.order_id = bo.order_id
                            {condition}
                            GROUP BY o.order_created_date_time, o.order_id, 
                            o.order_status, cs.first_name, cs.last_name, cs.customer_id, o.total_including_tax, o.order_status_id, scr.rep_name, bo.id, o.updated_by, scr.payment_term, scr.credit_limit
                            """)
        
        if sort_by and sort_order:
            nulls_order = "NULLS FIRST" if sort_order.lower() == "asc" else "NULLS LAST"
            query = text(str(query) + f" ORDER BY {sort_by} {sort_order} {nulls_order}")

        offset = (page - 1) * limit
        query = text(str(query) + f" LIMIT {limit} OFFSET {offset}")
        result = conn.execute(query)           
        res = result.fetchall()

        orders = []
        if res:
            for row in res:
                row_data = {
                    'date_created': convert_to_timestamp(row[0]),
                    'id': row[1],
                    'status': row[2] if row[2] else get_order_status_name(row[7]),
                    'first_name': row[3],
                    'last_name': row[4],
                    'customer_id': row[5],
                    'total_inc_tax': row[6],
                    'status_id': row[7],
                    'order_type': row[8] if row[11] == None else 'Block Order',
                    'is_express_orders': order_express,
                    'customer_rep_name': row[9],
                    'email': row[10],
                    'updated_by': row[12] if row[12] else '-',
                    'payment_term': row[13],
                    'total_amount_due': round(row[14], 2) if row[14] else 0,
                    'credit_limit': row[15]
                }
                orders.append(row_data)
        
        if orders:
            for order in orders:
                rewards=customer_db.get_order_reward_points(store,order['id'])
                if rewards:
                    order['rewards']=rewards
                else:
                    order['rewards']=0
            
                                
            limit = int(payload["limit"]) if payload.__contains__("limit") else 1
            page = int(payload["page"]) if payload.__contains__("page") else 1

            data = new_utils.calculate_pagination(orders, page, limit, total_count)

            response['data'] = data
            response['status'] = 200
        else:
            response['data'] = orders
            response['status'] = 200
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()       
    return response

def get_bc_order_details(store, order_id, payload, username=None):
    req_body={}

    if payload:
        if 'type' in payload and payload['type'] == '':
            return blocked_orders_db.remove_blocked_order(store, order_id)

        # when type is block-order, do not include consignment line items 
        if 'type' in payload and payload['type'] == 'block-order':
            return order_util.update_product_type(store, order_id, username)
        
        if 'type' in payload and (payload['type'] =='consignment' or payload['type']=='on-hold'):
                req_body = {
                'method': 'get',
                'url': "v2/orders",  
                'query_params': { 
                    # 'order_id':order_id,
                    'include':'consignments.line_items'
                    }}
    query_params={}
    if "query_params" in req_body and req_body["query_params"]:
        query_params = req_body["query_params"]
    api_data = store_util.get_bc_api_creds(store)
    res = bc.call_api(api_data, "GET", "v2/orders/"+order_id , query_params, {})
    order_details = None
    if res and res.status_code == 200:
        order_details = res.json()
        order_details['date_created'] = convert_to_timestamp(order_details['date_created'])
        order_details['date_modified'] = convert_to_timestamp(order_details['date_modified'])
        order_details['date_shipped'] = convert_to_timestamp(order_details['date_shipped'])

        # Fetch coupon details
        coupon_res = bc.call_api(api_data, "GET", f"v2/orders/{order_id}/coupons", {}, {})
        if coupon_res and coupon_res.status_code == 200:
            coupon_data = coupon_res.json()
            order_details['coupon_code'] = coupon_data[0]['code']
        else:
            order_details['coupon_code'] = ''
            
        if payload and 'type' in payload :
            order_util.add_product_details(store,payload,order_details,order_id)
            response = {"status": 200, }
            return response
        result=order_consignment_db.OrderConsignmentSchema.get_order(store, order_id)
        if result>0:
            order_type=order_consignment_db.OrderConsignmentSchema.get_order_type(store, order_id)
            order_details['type']=order_type
        else:
            # check for block order type ...
            order_type=blocked_orders_db.BlockedOrdersLogsSchema.is_order_exists(store, order_id)

            if order_type:
                order_details['type']="block-order"
            else:
                order_details['type']=""
                
        customer = redis_util.get_salesforce_customer(store['id'], order_details['customer_id'])
        if customer:
            rep = customer['rep']
            if rep:
                order_details['customer_rep_name'] = rep['Name']
                order_details['customer_rep_email'] = rep['Email']
        else:
            order_details['customer_rep_name']=""
            order_details['customer_rep_email']=""
        price_list = _get_customer_price_list_and_group(store['id'], order_details['customer_id'])
        
        if price_list:
            order_details['customer_price_list'] = price_list['data']['price_list']
        
        customer=customer_db.fetch_customer_by_id(store,order_details['customer_id'])
        #get loyalty points
        order_details['reward_points']= customer_db.fetch_loyalty_points_for_customer(store,order_details['customer_id']) or 0
        #get customer group name
        if customer:
            customer_group_name = customer_db.fetch_customer_group_by_id(store, customer['customer_group_id'])
            if customer_group_name is not  None:
                order_details["customer_group_name"]=customer_group_name
            else:
                order_details["customer_group_name"]=""
        else:
            order_details["customer_group_name"]="" 
        
        # add restock_qty and threshold_qty
        rs = blocked_orders_db.BlockedOrdersSchema.get_threshold_and_restock_qty(store, order_id)
        
        if rs:
            order_details['restock_threshold'] = rs['restock_threshold']
            order_details['restock_qty'] = rs['restock_qty']
        order_details["order_payments"] = callback_db.get_order_payments(store, [order_id])
    return order_details

def _get_customer_price_list_and_group(store_id, customer_id):
    response = {'status': 400}
    conn = new_pgdb.get_connection(store_id)
    try:
        query = text("""
            SELECT scr.price_list, c.customer_group_id, c.customer_group_name
            FROM salesforce_customer_rep scr
            LEFT JOIN customers c ON scr.customer_id = c.customer_id
            WHERE scr.customer_id = :customer_id
        """)
        result = conn.execute(query, {"customer_id": customer_id}).fetchone()
        
        if result:
            response['status'] = 200
            response['data'] = {
                'price_list': result[0],
                'customer_group_id': result[1],
                'customer_group_name': result[2]
            }
        else:
            response['status'] = 404
            response['data'] = {
                'price_list': None,
                'customer_group_id': None,
                'customer_group_name': None
            }
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        if conn:
            conn.close()
    return response


def get_order(store, order_id):
    res = get_bc_order_details(store, order_id, None)
    return new_utils.parse_json(res)

def get_all_customer_orders(store, customer_id, payload):
        response = {'status': 400}
        conn = new_pgdb.get_connection(store['id'])
        try:
            sort_by = str(payload.get('sort_by',''))
            sort_order = str(payload.get('sort_order', ''))
            page = int(payload.get('page', 1))
            limit = int(payload.get('limit', 10))              
            total_count_query = text(f"""SELECT COUNT(*) FROM orders WHERE customer_id= :customer_id""")
            
            result_count = conn.execute(total_count_query.params(customer_id=customer_id))
            total_count = int(result_count.scalar())                               

            query = f"""SELECT 
                                o.order_created_date_time AS date_created, 
                                o.order_id AS id, 
                                oc.order_type AS order_type,
                                o.order_status AS status, 
                                o.total_including_tax, 
                                o.order_status_id
                                
                            FROM orders AS o
                            LEFT JOIN order_consignment AS oc ON o.order_id = oc.order_id
                            WHERE o.customer_id = {customer_id}
                            GROUP BY o.order_created_date_time, o.order_id, 
                            o.order_status, o.total_including_tax, o.order_status_id, oc.order_type  
                            """
            if sort_by and sort_order:
                query = text(str(query) + f" ORDER BY {sort_by} {sort_order}")

            offset = (page - 1) * limit
            query = text(str(query) + f" LIMIT {limit} OFFSET {offset}")
            result = conn.execute(query)           
            res = result.fetchall()

            orders = []
            if res:
                for row in res:
                    row_data = {
                        'date_created': convert_to_timestamp(row[0]),
                        'id': row[1],
                        'type': row[2],
                        'status': row[3] if row[3] else get_order_status_name(row[5]),
                        'total': row[4]
                    }
                    bc_order = bc.get_bc_order_by_id(store, row_data['id'])
                    if bc_order:
                       row_data['comment'] = bc_order[0]['customer_message']
                    else:
                        row_data['comment'] = None
                    orders.append(row_data)
            
            if orders:                
                limit = int(payload["limit"]) if payload.__contains__("limit") else 1
                page = int(payload["page"]) if payload.__contains__("page") else 1

                data = new_utils.calculate_pagination(orders, page, limit, total_count)

                response['data'] = data
                response['status'] = 200
            else:
                response['data'] = orders
                response['status'] = 200
        except Exception as e:
            error_message = str(e)
            response['status'] = 422
            response['message'] = error_message
            raise e
        finally:
            if conn:
                conn.close()       
        return response


def update_unit_prices(store, order_id, payload, name):
    conn = new_pgdb.get_connection(store['id'])
    try:
        # Call the update_order function with the prepared payload and order_id
        response = bc_order.update_order(store, payload, order_id)

        # Check the response and handle success or failure
        if response.status_code == 200:
            order_res = bc_order.fetch_order(store, order_id)
            if order_res.status_code < 299:
                order_data = order_res.json()
                new_total_including_tax = order_data['total_inc_tax']
                query = text(f"""UPDATE orders SET total_including_tax = :total_including_tax, updated_by = :name WHERE order_id = :order_id""")
                results = conn.execute(query.params(total_including_tax=new_total_including_tax, name=name, order_id=order_id))
                conn.commit()
            task.send_task(task.GENERATE_ADMIN_APP_NOTIFICATION_TASK, (store['id'], "order_updated", order_id))
            return {"message": "Order updated successfully", "status": 200}
        else:
            return {"message": response.json(), "status": response.status_code}
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close() 
    

def get_order_products_processed_data(store, res):
    if (len(res)):
        variant_ids = list(set([item['variant_id'] for item in res if item['quantity']]))
        product_ids = list(set([item['product_id'] for item in res if item['quantity']]))

        # check available quantity after removing sku from order ...
        graphql_query = products_variant_query.get_query(product_ids, variant_ids)
        status, gql_res = bc.process_bc_graphql_request(store, graphql_query)
        
        variants_inventory = {}
        min_max_rules = {}

        if status == 200 and 'data' in gql_res and len(gql_res['data']['site']['products']['edges']):
            products = gql_res['data']['site']['products']['edges']
            
            for product in products:
                min_purchase_quantity = product['node']['minPurchaseQuantity']
                max_purchase_quantity = product['node']['maxPurchaseQuantity']
                min_max_rules[product['node']['entityId']] = {
                    'min_purchase_quantity': min_purchase_quantity,
                    'max_purchase_quantity': max_purchase_quantity
                }

                variants = product['node']['variants']['edges']
                for variant in variants:
                    variants_inventory[variant['node']['entityId']] = variant['node']['inventory']['aggregated']['availableToSell']

        for item in res:
            item['quantity_available'] = variants_inventory.get(item['variant_id'], 0)
            item['min_purchase_quantity'] = min_max_rules.get(item['product_id'], {}).get('min_purchase_quantity', 0)
            item['max_purchase_quantity'] = min_max_rules.get(item['product_id'], {}).get('max_purchase_quantity', 0)

        data = [{
            'id': item.get('id', None), 
            'order_id': item.get('order_id'), 
            'product_id': item.get('product_id'), 
            'variant_id': item.get('variant_id'),
            'classification': item.get('classification', None),
            'name': item.get('name'),
            'product_image': item.get('images', None),
            'sku': item.get('sku'),
            'quantity': item.get('quantity'),
            'unit_price': item.get('base_price'),
            'total': item.get('base_total'),
            'available_qty': item.get('quantity_available'),
            'min_purchase_quantity': item.get('min_purchase_quantity'),
            'max_purchase_quantity': item.get('max_purchase_quantity'),
            'variant_name': item.get('variant_name', ''),
            'price_from_price_list': item.get('price_from_price_list', None),
            'pricing_group': item.get('pricing_group', None),
            'cost': item.get('cost', None),
            'total_cost': (item.get('cost') or 0) * item.get('quantity', 0),
            'purchasing_disabled': item.get('purchasing_disabled', False),
            'accepted_prices': item.get('accepted_prices', []),
            'isNewItem': item.get('is_new_item', False)
            } for item in res if item['quantity']]
    
        
        return {'status': 200, 'data': data}

    else:
      return {'status': 404, 'message': 'Order not found'}
    

def get_price_list(store):
    static_price_lists = fetch_static_price_lists(store['id'])
    price_lists, status = bc_price_list.fetch_price_lists(store)

    for static_price_list in static_price_lists:
        price_lists['data'].append({
            'id': static_price_list['id'],
            'name': static_price_list['name'],
            'date_created': None,
            'date_modified': None,
            'active': static_price_list['active']
        })
    
    extracted_data = []
    if 'data' in price_lists:
        for item in price_lists['data']:
            if item['active']:
                extracted_item = {
                    'id': item['id'],
                    'name': item['name']
                }
                extracted_data.append(extracted_item)
    else:
        extracted_data = []
    
    return extracted_data, status
        
def get_products_suggestion(store_id, search, page, limit):
    response = {
        "status": 400       
    }
    db = new_mongodb.get_store_db_client_for_store_id(store_id)
    try:
        payload = {
            "page": page,
            "limit": limit,
            "filterBy": ["name", "sku"],
            "filter": search
        }

        products, total_data_length, page, limit = get_paginated_records_updated(
        db, new_mongodb.StoreDBCollections.PRODUCTS, payload, products_suggestion_fields, {"is_visible": True}
        )
        if products:
            final_result = {
                "data": products,
                "meta": {
                    "current_page": page,
                    "next_page": (page + 1 if page and limit and (page * limit) < total_data_length else None),
                    "total_count": total_data_length
                }
            }
            response['data'] = final_result
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = {
                "data": [],
                "meta": {
                    "current_page": page,
                    "next_page": None,
                    "total_count": total_data_length
                }
            }

    except Exception as e:
        logger.error(traceback.format_exc())

    return response

def get_product_variants(store_id, parent_sku, page, limit):
    response = {
        "status": 400       
    }
    try:
        product_data = new_mongodb.fetch_one_document_from_storefront_collection(store_id, new_mongodb.StoreDBCollections.PRODUCTS, {"sku": parent_sku})

        product_variants = []
        for variant in product_data.get("variants", []):  # Ensure we handle nested variants
            variant_name = " - ".join(option['label'] for option in variant.get('option_values', [])) or "Parent Product"
            variant_data = {
                "variant_id": variant["id"],
                "inventory_level": variant["inventory_level"] or 0,
                "sku": variant["sku"] or "",
                "variant_name": variant_name
            }
            product_variants.append(variant_data)

        page = int(page) if page else 1
        limit = int(limit) if limit else 10
        total_variants = len(product_variants)
        start_index = (page - 1) * limit
        end_index = start_index + limit
        paginated_variants = product_variants[start_index:end_index]

        final_result = {
            "data": paginated_variants,
            "meta": {
                "current_page": page,
                "next_page": (page + 1 if (page * limit) < total_variants else None),
                "total_count": total_variants
            }
        }
        response['data'] = final_result
        response['status'] = 200

    except Exception as e:
        logger.error(traceback.format_exc())

    return response

def check_line_item_availability(store, order_id, skus, price_list_id=None):
    response = {
        "status": 400,
        "data": []      
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        variant_ids = []
        product_ids = []
        sku_to_ids_map = {}

        
        # Get customer information from the order
        customer_id = order_util.fetch_customer_id_from_order(store['id'], order_id)
        if not customer_id:
            return {'status': 404, 'message': 'Order not found or invalid order ID.'}
        
        
        for sku in skus:
            # Validate product exists first
            products_data = bc_products.get_products(store, sku)
            products_data = products_data.json()

            if products_data['data'] != []:
                # Extract the main product SKU
                product_sku = products_data['data'][0]['sku']
                
                # Extract the first variant SKU (if variants exist)
                if 'variants' in products_data['data'][0] and products_data['data'][0]['variants']:
                    variant_sku = products_data['data'][0]['variants'][0]['sku']
                else:
                    variant_sku = None

                # Compare the product SKU and variant SKU
                if product_sku != variant_sku:
                    return {'status': 409, 'message': 'Product is not available for purchase.'}

            product_id, variant_id = get_product_id_by_sku_from_mongo(store, sku)
            if product_id == 0 and variant_id == 0:
                return {'status': 409, 'message': 'Invalid Product'}
                
            variant_ids.append(variant_id)
            product_ids.append(product_id) 
            sku_to_ids_map[sku] = {'product_id': product_id, 'variant_id': variant_id}

        if price_list_id:
            variant_prices = order_util.get_variant_prices(store['id'], variant_ids, price_list_id, customer_id)

        # Get product GraphQL data for inventory and min/max quantities
        graphql_query = products_variant_query.get_query(product_ids, variant_ids)
        gql_status, gql_res = bc.process_bc_graphql_request(store, graphql_query)

        variants_inventory = {}
        min_max_rules = {}
        variants_options = {}
        product_names = {}
        product_prices = {}
        product_availability = {}

        if gql_status == 200 and 'data' in gql_res and len(gql_res['data']['site']['products']['edges']):
            products = gql_res['data']['site']['products']['edges']
            
            for product in products:
                product_id = product['node']['entityId']
                min_purchase_quantity = product['node']['minPurchaseQuantity']
                max_purchase_quantity = product['node']['maxPurchaseQuantity']
                product_names[product_id] = product['node']['name']
                product_availability[product_id] = product['node']['availabilityV2']['status'] == 'Available'
                
                min_max_rules[product_id] = {
                    'min_purchase_quantity': min_purchase_quantity,
                    'max_purchase_quantity': max_purchase_quantity
                }

                variants = product['node']['variants']['edges']

                for variant in variants:
                    variant_id = variant['node']['entityId']
                    variants_inventory[variant_id] = variant['node']['inventory']['aggregated']['availableToSell']
                    
                    # Get variant price
                    if variant['node']['prices']['salePrice']:
                        product_prices[variant_id] = variant['node']['prices']['salePrice']['value']
                    else:
                        product_prices[variant_id] = variant['node']['prices']['price']['value']
                    
                    # Process variant options for variant name
                    option_values = []
                    for option in variant['node']['options']['edges']:
                        for value in option['node']['values']['edges']:
                            option_values.append(value['node']['label'])
                    variants_options[variant_id] = ' - '.join(option_values) if option_values else 'Parent Product'
        
        # If no GraphQL data is available, check if we have any inventory data
        if gql_status != 200 or not gql_res or 'data' not in gql_res or len(gql_res['data']['site']['products']['edges']) == 0:
            return {'status': 400, 'message': 'Product out of stock or disabled.'}

        # Get product data from MongoDB for images and other details
        from mongo_db import catalog_db
        products_catalog = catalog_db.get_productsList_by_id(store, product_ids)
        
        # Get classifications
        classifications_map = order_util.get_classifications(store['id'], skus)
        
        # Get accepted prices for the customer
        price_list_ids = bc._get_price_list_ids_to_fetch_accepted_prices(store, customer_id)
        accepted_prices = order_util.get_accepted_prices(store['id'], variant_ids, price_list_ids, customer_id) if price_list_ids != [] else {}

        # Build response for each SKU
        for sku in skus:
            if sku not in sku_to_ids_map:
                continue
                
            product_id = sku_to_ids_map[sku]['product_id']
            variant_id = sku_to_ids_map[sku]['variant_id']
            
            # Check if product is available
            available_qty = variants_inventory.get(variant_id, 0)
            if available_qty == 0:
                return {'status': 400, 'message': f'{sku} is out of stock.'}

            # Check if product is available for purchase
            is_available = product_availability.get(product_id, False)
            if not is_available:
                return {'status': 400, 'message': f'{sku} is not available for purchase.'}

            # Get min/max purchase quantities
            min_purchase_qty = min_max_rules.get(product_id, {}).get('min_purchase_quantity')
            max_purchase_qty = min_max_rules.get(product_id, {}).get('max_purchase_quantity')

            # Get product details
            # product_name = product_names.get(product_id, "")

            # Get product image and purchasing_disabled flag
            product_image = ""
            product_name = ""
            purchasing_disabled = False
            for catalog_product in products_catalog:
                if catalog_product.get("id") == product_id:
                    images = catalog_product.get("images", [])
                    product_name = catalog_product.get("name", "")
                    if images and len(images) > 0:
                        product_image = images[0].get("url_thumbnail", "")
                    
                    # Get purchasing_disabled flag
                    variant_info = catalog_product.get("variants", {}).get(variant_id, {})
                    unit_price = variant_info.get("calculated_price", 0)
                    purchasing_disabled = variant_info.get("purchasing_disabled", False)
                    break

            # Calculate quantity (use min_purchase_qty if > 1, otherwise 1)
            quantity = min_purchase_qty if min_purchase_qty and min_purchase_qty > 1 else 1
            
            # Get accepted prices for this variant
            variant_accepted_prices = accepted_prices.get(variant_id, {}).get('accepted_prices', [])
            unit_price = float(variant_accepted_prices[0]) if variant_accepted_prices else product_prices.get(variant_id, 0)

            
            line_item = {
                "id": f"{order_id}_{variant_id}",
                "order_id": order_id,
                "product_id": product_id,
                "variant_id": variant_id,
                "classification": classifications_map.get(sku),
                "name": product_name,
                "product_image": product_image,
                "sku": sku,
                "quantity": quantity,
                "available_qty": available_qty,
                "unit_price": f"{unit_price:.4f}",
                "total": f"{unit_price * quantity:.4f}",
                "min_purchase_quantity": min_purchase_qty,
                "max_purchase_quantity": max_purchase_qty,
                "variant_name": variants_options.get(variant_id, ""),
                "price_from_price_list": variant_prices.get(variant_id, {}).get('price') if price_list_id else None,
                "pricing_group": variant_prices.get(variant_id, {}).get('pricing_group') if price_list_id else None,
                "cost": None,
                "total_cost": None,
                "purchasing_disabled": purchasing_disabled,
                "accepted_prices": variant_accepted_prices
            }
            response['data'].append(line_item)

        response['status'] = 200

    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        conn.close()
    return response