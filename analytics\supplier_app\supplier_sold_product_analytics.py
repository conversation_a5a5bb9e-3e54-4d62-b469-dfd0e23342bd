from new_mongodb import store_admin_db
from sqlalchemy import text
import new_pgdb
import utils
from utils import common
from datetime import datetime, timedelta
import datetime
import copy
import logging
from new_pgdb.analytics_db import AnalyticsDB
from dateutil.relativedelta import relativedelta
from utils.common import calculatePaginationData, convert_to_timestamp

logger = logging.getLogger()

# common functions
def _get_months_in_range(start_date, end_date):
    range_months = []
    current_month = start_date
    while current_month <= end_date:
        range_months.append(current_month.month)
        current_month += relativedelta(months=1)

    return range_months

def _check_product_access(conn, store_id, product_id, email_id):

    user = store_admin_db.fetch_user_by_username(store_id, email_id) 
    if not user:
        return False
    user_type = user['type']
    user_role = user['role']
    role_id = user['role_id']
    condition_query = ''
    if user_type == 'admin_app_default_user':
        if user_role in ['The Owner', 'Atlantix User']:
            return True
        else:
            return False
    elif user_type == 'admin_app_supplier_user':
        # if role_id != '68198b98d191f4902c39bcbd' and user_role not in ['Only Supplier Portal']:
        if user_role not in ['Only Supplier Portal']:
            return False
        condition_query = " AND usm.product_id = :product_id AND usm.email_id = :email_id"

    # First check if the product is associated with the user's supplier
    check_query = f"""
        WITH product_supplier_check AS (
            SELECT EXISTS (
                SELECT 1 
                FROM {new_pgdb.DBTables.supplier_app_user_supplier_mapping} usm
                WHERE 1=1 {condition_query}
            ) as has_access
        )
        SELECT has_access FROM product_supplier_check
    """
    check_params = {}
    if condition_query:
        check_params = {
            "product_id": product_id,
            "email_id": email_id.strip() if email_id else None
        }

    check_result = conn.execute(text(check_query).params(**check_params))
    has_access = check_result.scalar()

    if not has_access:
        return False
    return True
        
def _fetch_product_sell_by_state_supplier(conn, product_id, start_date, end_date):
    query = text(
        f"""SELECT
                sh.state,
                SUM(pt.quantity) AS total_quantity
            FROM
                {AnalyticsDB.get_products_trend_table()} pt
            JOIN
                {new_pgdb.DBTables.order_shipping_addresses_table} sh ON pt.order_id = sh.order_id
            WHERE
                pt.product_id = :product_id
                AND pt.order_date_time BETWEEN :start_date AND :end_date
            GROUP BY
                sh.state;"""
    )

    result = conn.execute(query.params(product_id=product_id, start_date=start_date, end_date=end_date))
    data = {}
    total_quantity = 0
    for row in result.fetchall():
        state = str(row[0])
        quantity = int(row[1])

        if state not in data:
            data[state] = {
                "state": state,
                "quantity": 0
            }
        data[state]["quantity"] += quantity
        total_quantity += quantity

    return {
        'data': data,
        'total_quantity': total_quantity
    }


def _fetch_monthly_product_sell_by_state_supplier(conn, product_id, start_date, end_date):
    query = text (
        f"""SELECT
                sh.state,
                pt.order_month,
                SUM(pt.quantity) AS total_quantity_monthly
            FROM
                {AnalyticsDB.get_products_trend_table()} pt
            JOIN
                {new_pgdb.DBTables.order_shipping_addresses_table} sh ON pt.order_id = sh.order_id
            WHERE
                pt.product_id = :product_id
                AND pt.order_date_time BETWEEN :start_date AND :end_date
            GROUP BY
                sh.state,
                pt.order_month;"""
        )
    result = conn.execute(query.params(product_id=product_id, start_date=start_date, end_date=end_date))
    data = {}
    for row in result.fetchall():
        state_data = data.get(str(row[0]), {})
        state_data[str(row[1])] = int(row[2])
        data[str(row[0])] = state_data

    return data

def _fetch_product_quantity_by_price_suppliers(conn, product_id, start_date, end_date):
    query = text(
                f"""SELECT
                        price_ex_tax,
                        sum(quantity) as quantity
                    FROM
                        {AnalyticsDB.get_variants_trend_table()}
                    WHERE
                        product_id = :product_id AND
                        order_date_time BETWEEN :start_date AND :end_date
                    GROUP BY
                        price_ex_tax
                   ;"""
            )

    query = query.params(product_id=product_id, start_date=start_date, end_date=end_date)

    result = conn.execute(query)
    data = {}
    total_quantity = 0
    for row in result.fetchall():
        price = row[0]
        data[price] = row[1]
        total_quantity += row[1]

    return {
        "total_quantity": total_quantity,
        "prices": data
    }

def _fetch_product_quantity_by_price_month_suppliers(conn, product_id, start_date, end_date):
    query = text(
                f"""SELECT
                        price_ex_tax,
                        order_month,
                        sum(quantity) as quantity
                    FROM
                        {AnalyticsDB.get_variants_trend_table()}
                    WHERE
                        product_id = :product_id AND
                        order_date_time BETWEEN :start_date AND :end_date
                    GROUP BY
                        price_ex_tax,
                        order_month
                   ;"""
            )

    query = query.params(product_id=product_id, start_date=start_date, end_date=end_date)

    result = conn.execute(query)
    data = {}
    for row in result.fetchall():
        price = row[0]
        month = row[1]
        quantity = row[2]

        monthly_data = data.get(price, {})
        monthly_data[month] = quantity

        data[price] = monthly_data

    return data

def _fetch_variant_sales_by_customers_suppliers(conn, product_id, start_date, end_date, state_join_clause_query,
                                            state_condition, state=None):
    query = text(
        f"""SELECT
                ap.customer_id,
                MAX(c.company) AS company,
                MAX(c.customer_group_name) AS customer_group,
                CAST(SUM(ap.quantity) AS INTEGER) AS total_quantity,
                sh.state,
                scr.type
            FROM
                {AnalyticsDB.get_products_trend_table()} ap
            JOIN
                {new_pgdb.DBTables.customers_table} c ON c.customer_id = ap.customer_id
            JOIN
                {new_pgdb.DBTables.salesforce_customer_rep} scr ON scr.customer_id = ap.customer_id
                {state_join_clause_query}
            WHERE
                ap.product_id = :product_id
                AND ap.order_date_time BETWEEN :start_date AND :end_date
                {state_condition}
            GROUP BY
                ap.customer_id,
                sh.state,
                scr.type
            ORDER BY
                total_quantity DESC;"""
        )

    if state:
        query = query.params(product_id=product_id, start_date=start_date, end_date=end_date, state=state)
    else:
        query = query.params(product_id=product_id, start_date=start_date, end_date=end_date)

    result = conn.execute(query)
    data = {}
    total_quantity = 0
    for row in result.fetchall():
        data[int(row[0])] = {
            'company': str(row[1]),
            'customer_group': str(row[2]),
            'quantity': int(row[3]),
            'state': row[4],
            'customer_type': row[5]
        }
        total_quantity += int(row[3])
    return {
        'data': data,
        'total_quantity': total_quantity
    }

def _fetch_variant_monthly_sales_by_customers_suppliers(conn, product_id, start_date, end_date, state_join_clause_query,
                                            state_condition,  state=None):
    query = text(
                f"""SELECT
                        ap.customer_id,
                        ap.order_month,
                        SUM(ap.quantity) AS total_quantity_monthly,
                        sh.state,
                        scr.type
                    FROM
                        {AnalyticsDB.get_products_trend_table()} ap
                    JOIN
                        {new_pgdb.DBTables.salesforce_customer_rep} scr ON scr.customer_id = ap.customer_id
                        {state_join_clause_query}
                    WHERE
                        ap.product_id = :product_id
                        AND ap.order_date_time BETWEEN :start_date AND :end_date
                        {state_condition}
                    GROUP BY
                        ap.customer_id,
                        ap.order_month,
                        sh.state,
                        scr.type
                   ;"""
            )

    if state:
        query = query.params(product_id=product_id, start_date=start_date, end_date=end_date, state=state)
    else:
        query = query.params(product_id=product_id, start_date=start_date, end_date=end_date)

    result = conn.execute(query)
    data = {}
    for row in result.fetchall():
        customer_id = int(row[0])
        customer = data.get(customer_id, {})
        customer[str(row[1])] = int(row[2])
        customer['state'] = row[3]
        customer['customer_type'] = row[4]
        data[customer_id] = customer
    return data

def _fetch_variants_range_data_supplier(conn, product_id, start_date, end_date, state_join_clause_query,
                                     state_condition, state):

    state_select = ", sh.state" if state else ""
    state_group_by = ", sh.state" if state else ""

    sum_query = text(
    f"""SELECT
            av.variant_id,
            SUM(av.quantity) AS total_quantity
            {state_select}
        FROM
            {AnalyticsDB.get_variants_trend_table()} av
            {state_join_clause_query}
        WHERE
            av.product_id = :product_id
            AND av.order_date_time BETWEEN :start_date AND :end_date
            {state_condition}
        GROUP BY
            av.variant_id
            {state_group_by}
            ;
        """
    )
    if state:
        sum_query = sum_query.params(product_id=product_id, start_date=start_date, end_date=end_date,
                                        state=state)
    else:
        sum_query = sum_query.params(product_id=product_id, start_date=start_date, end_date=end_date)

    sum_data = conn.execute(sum_query)
    sum_results = sum_data.fetchall()
    variants = {}
    total_quantity = 0
    fetched_state = None
    for row in sum_results:
        variants[row[0]] = row[1]
        total_quantity += row[1]
        if state:  # Fetch state only if it was included in the query
            fetched_state = row[2]

    variant_count = len(variants)

    result = {
        "data": variants,
        "total_quantity": total_quantity,
        "variant_count": variant_count,
    }

    if state:
        result["state"] = fetched_state

    return result

def _fetch_variants_monthly_data_supplier(conn, product_id, start_date, end_date, state_join_clause_query,
                                       state_condition, state):
    state_select = ", sh.state" if state else ""
    state_group_by = ", sh.state" if state else ""
    query = text (
            f"""SELECT
                av.variant_id,
                av.order_month,
                SUM(av.quantity) AS total_quantity_monthly
                {state_select}
            FROM
                {AnalyticsDB.get_variants_trend_table()} av
                {state_join_clause_query}
            WHERE
                av.product_id = :product_id
                AND av.order_date_time BETWEEN :start_date AND :end_date
                {state_condition}
            GROUP BY
                av.variant_id,
                av.order_month
                {state_group_by}
            ;"""
        )

    if state:
        query = query.params(product_id=product_id, start_date=start_date, end_date=end_date, state=state)
    else:
        query = query.params(product_id=product_id, start_date=start_date, end_date=end_date)

    data = conn.execute(query)
    result = data.fetchall()
    variants = {}
    for row in result:
        variant = variants.get(row[0], {})
        variant[row[1]] = row[2]

        variants[row[0]] = variant
        if state:
            variants['state'] = row[3]
    return variants

def _fetch_product_variants_supplier(conn, product_id):
    query = text (
            f"select variants_id, variants_sku, variant_options from variants where product_id = {product_id};"
        )

    data = conn.execute(query)
    result = data.fetchall()
    variants = {}
    for row in result:
        if row[2] and row[2] != "":
            variants[row[0]] = {
                "sku": row[1],
                "option": row[2]
            }
        else:
             variants[row[0]] = {
                "sku": row[1],
                "option": row[1]
            }
    return variants


def get_sold_product_analytics_summary_suppliers(store, product_id, start_date, end_date, username):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store['id'])
    total_data = {}

    try:
        has_access = _check_product_access(conn, store['id'], product_id, username)
        if not has_access:
            response['message'] = "You don't have permission to access this product's data"
            response['status'] = 403
            return response

        # If user has access, proceed with the original query
        base_query = f"""
            SELECT
                COUNT(DISTINCT apt.order_id) AS order_count,
                SUM(apt.quantity) AS sold_quantity,
                COUNT(DISTINCT sh.state) AS total_state,
                MAX(p.inventory_level) AS available_quantity
            FROM
                {AnalyticsDB.get_products_trend_table()} apt
            LEFT JOIN order_shipping_addresses sh ON apt.order_id = sh.order_id
            LEFT JOIN products p ON apt.product_id = p.product_id
            WHERE
                apt.product_id = :product_id
                AND apt.order_date_time BETWEEN :start_date AND :end_date
        """

        params = {
            "product_id": product_id,
            "start_date": start_date,
            "end_date": end_date
        }

        # base_query += """
        #     GROUP BY
        #         sc.type
        #     ORDER BY
        #         sold_quantity DESC
        # """

        query = text(base_query)
        data = conn.execute(query.params(**params))
        results = data.fetchone()
    
        # for result in results:
        if results:
            total_data = {
                'order_count': int(results[0]) if results[0] else 0,
                'sold_quantity': int(results[1]) if results[1] else 0,
                'total_state': int(results[2]) if results[2] else 0,
                'available_quantity': int(results[3]) if results[3] else 0,
                'cost': 0
            }
            
        response['status'] = 200
        response['data'] = total_data

    finally:
        if conn:
            conn.close()
    return response

def get_variant_sales_by_states_suppliers(store, product_id, start_date, end_date, username, sort_array=[]):
    response = {
        "status": 400
    }
    try:
        result_data = {}
        conn = new_pgdb.get_connection(store['id'])
        has_access = _check_product_access(conn, store['id'], product_id, username)
        if not has_access:
            response['message'] = "You don't have permission to access this product's data"
            response['status'] = 403
            return response
        start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d")
        end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d")
        #offset = (int(page) - 1) * int(limit)

        current_date = datetime.datetime.now()
        month_start_date = current_date - datetime.timedelta(days=current_date.day - 1) - datetime.timedelta(weeks=12)

        month_start_date = month_start_date.replace(day=1).date()
        month_end_date = current_date.date()

        _months = _get_months_in_range(copy.deepcopy(month_start_date), copy.deepcopy(month_end_date))
        _months.reverse()

        tasks = []
        tasks.append({
            "func": _fetch_product_sell_by_state_supplier,
            "args": [product_id, start_date, end_date]
        })
        tasks.append({
            "func": _fetch_monthly_product_sell_by_state_supplier,
            "args": [product_id, month_start_date, month_end_date]
        })

        range_data, monthly_data = utils.concurrent_db_execution(store, tasks)

        MONTH_NAMES = common.get_month_names()

        range_state_data = range_data['data']
        total_range_quantity = range_data['total_quantity']

        states = set().union(range_state_data, monthly_data.keys())

        current_month = datetime.date.today().month
        current_year = datetime.date.today().year
        meta_info = {'month_' + str(i): '' for i in range(1, 5)}

        for i in range(1, 5):
            month_key = 'month_' + str(i)
            month_value = current_month - (i - 1)
            year_value = current_year if month_value > 0 else current_year -1
            month_value = month_value if month_value > 0 else month_value + 12
            month_name = MONTH_NAMES[month_value]
            meta_info[month_key] = f"{month_name} {year_value}"

        if not states:
            result_data['message'] = "No variant data available for the specified product ID."
            result_data['meta'] = {'month_rows':[meta_info]}
            response['status'] = 200
            response['data'] = result_data
            return response

        state_data = []
        month_keys = {}

        for state in states:
            range_data = range_state_data.get(state, None)
            percentage = 0
            range_quantity = 0
            if range_data:
                range_quantity = range_data.get('quantity', 0)
                if total_range_quantity > 0:
                    percentage = round((range_quantity / total_range_quantity) * 100, 2)

            _state_data = {
                "state": state,
                "total_quantity_by_range": range_quantity,
                "percentage": percentage
            }

            monthly_state_data = monthly_data.get(state, {})
            for idx, m in enumerate(_months):
                month_str = MONTH_NAMES.get(m)
                month_key = f"month_{idx+1}"
                _state_data[month_key] = monthly_state_data.get(str(m), 0)
                month_keys[month_key] = month_str

            state_data.append(_state_data)

        if len(sort_array) > 0:
            sort_direction = False if sort_array[1] == '1' else True
            state_data = sorted(state_data, key=lambda x: x[sort_array[0]], reverse=sort_direction)
        else:
            state_data = sorted(state_data, key=lambda x: x['percentage'], reverse=True)

        result_data['data'] = state_data
        result_data['meta'] = {
            "month_rows": [meta_info]
        }
        response['status'] = 200
        response['data'] = result_data
    finally:
        if conn:
            conn.close()

    return response

def get_product_price_report_suppliers(store, product_id, start_date, end_date, sort="desc"):
    result_data = {}

    current_date = datetime.datetime.now()
    month_start_date = current_date - datetime.timedelta(days=current_date.day - 1) - datetime.timedelta(weeks=12)

    month_start_date = month_start_date.replace(day=1).date()
    month_end_date = current_date.date()

    _months = _get_months_in_range(copy.deepcopy(month_start_date), copy.deepcopy(month_end_date))
    _months.reverse()

    tasks = []
    tasks.append({
        "func": _fetch_product_quantity_by_price_suppliers,
        "args": [product_id, start_date, end_date]
    })
    tasks.append({
        "func": _fetch_product_quantity_by_price_month_suppliers,
        "args": [product_id, month_start_date, month_end_date]
    })

    range_data, monthly_data = utils.concurrent_db_execution(store, tasks)
    total_quantity = range_data['total_quantity']
    range_sell_data = range_data.get('prices', {})

    MONTH_NAMES = common.get_month_names()
    current_month = datetime.date.today().month
    meta_info = {'month_' + str(i): '' for i in range(1, 5)}

    for i in range(1, 5):
        month_key = 'month_' + str(i)
        month_value = current_month - (i - 1)
        month_value = month_value if month_value > 0 else month_value + 12
        month_name = MONTH_NAMES[month_value]
        meta_info[month_key] = month_name

    price_data_list = []
    month_keys = {}
    for price, _data in monthly_data.items():
        range_quantity = range_sell_data.get(price, 0)
        quantity_percentage = 0
        if total_quantity > 0:
            quantity_percentage = round((range_quantity / total_quantity) * 100, 2)

        price_data = {
            "price": price,
            "quantity": range_quantity,
            "percentage": quantity_percentage
        }

        for idx, m in enumerate(_months):
            month_str = MONTH_NAMES.get(m)
            month_key = f"month_{idx+1}"
            price_data[month_key] = _data.get(month_str, 0)
            month_keys[month_key] = month_str

        price_data_list.append(price_data)

    if sort.lower() == 'asc':
        price_data_list = sorted(price_data_list, key=lambda x: x['percentage'])
    else:
        price_data_list = sorted(price_data_list, key=lambda x: x['percentage'], reverse=True)

    result_data['data'] = price_data_list
    result_data['meta'] = {
        "month_rows": [month_keys]
    }

    return result_data

def get_variant_sales_by_customers_suppliers(store, product_id, start_date, end_date, sort="desc", state=None):
    result_data = {}
    state_condition = ""

    # Check if 'state' is provided and not None
    if state:
        state_condition = "AND sh.state = :state"

    state_join_clause_query = f"JOIN {new_pgdb.DBTables.order_shipping_addresses_table} sh ON ap.order_id = sh.order_id" #if state else ""

    current_date = datetime.datetime.now()
    month_start_date = current_date - datetime.timedelta(days=current_date.day - 1) - datetime.timedelta(weeks=12)

    month_start_date = month_start_date.replace(day=1).date()
    month_end_date = current_date.date()

    _months = _get_months_in_range(copy.deepcopy(month_start_date), copy.deepcopy(month_end_date))
    _months.reverse()

    tasks = []
    tasks.append({
        "func": _fetch_variant_sales_by_customers_suppliers,
        "args": [product_id, start_date, end_date,state_join_clause_query, state_condition, state]
    })
    tasks.append({
        "func": _fetch_variant_monthly_sales_by_customers_suppliers,
        "args": [product_id, month_start_date,month_end_date, state_join_clause_query, state_condition, state]
    })

    range_data, monthly_data = utils.concurrent_db_execution(store, tasks)

    total_quantity_sold = range_data.get("total_quantity", 0)

    MONTH_NAMES = common.get_month_names()

    month_keys = {}
    customers_data = []
    range_sell_data = range_data.get('data', {})

    current_month = datetime.date.today().month
    meta_info = {'month_' + str(i): '' for i in range(1, 5)}

    for i in range(1, 5):
        month_key = 'month_' + str(i)
        month_value = current_month - (i - 1)
        month_value = month_value if month_value > 0 else month_value + 12
        month_name = MONTH_NAMES[month_value]
        meta_info[month_key] = month_name

    if not range_sell_data:
        result_data['message'] = "No variant data available for the specified product ID."
        result_data['meta'] = {'month_rows':[meta_info]}
        return result_data

    for customer_id, customer_data in range_sell_data.items():
        range_quantity = customer_data.get("quantity", 0)
        quantity_percentage = round((range_quantity / total_quantity_sold) * 100, 2)

        state_abbreviation = common.get_state_abbreviation(customer_data['state'])
        customer = {
            'company': customer_data['company'],
            'customer_group': customer_data['customer_group'],
            'state': state_abbreviation,
            'customer_type': customer_data['customer_type'],
            'total_quantity_by_range': range_quantity,
            'percentage': quantity_percentage
        }

        monthly_sell_data = monthly_data.get(customer_id, {})
        for idx, m in enumerate(_months):
            month_str = MONTH_NAMES.get(m)
            month_key = f"month_{idx+1}"
            customer[month_key] = monthly_sell_data.get(str(m), 0)
            month_keys[month_key] = month_str

        customers_data.append(customer)

    if sort.lower() == 'asc':
            customers_data = sorted(customers_data, key=lambda x: x['percentage'])
    else:
        customers_data = sorted(customers_data, key=lambda x: x['percentage'], reverse=True)

    result_data['data'] = customers_data
    result_data['meta'] = {
        "month_rows": [month_keys]
    }

    return result_data

def get_sku_report_supplier(store, product_id, start_date, end_date, username, sort_array=[], state=None):
    response = {
        "status": 400
    }
    try:
        result_data = {}
        conn = new_pgdb.get_connection(store['id'])
        has_access = _check_product_access(conn, store['id'], product_id, username)
        if not has_access:
            response['message'] = "You don't have permission to access this product's data"
            response['status'] = 403
            return response
        # Check if 'state' is provided and not None
        state_join_clause_query = ""
        state_condition = ""

        if state:
            state_condition = "AND sh.state = :state"
            state_join_clause_query = f"JOIN {new_pgdb.DBTables.order_shipping_addresses_table} sh ON av.order_id = sh.order_id"

        current_date = datetime.datetime.now()
        month_start_date = current_date - datetime.timedelta(days=current_date.day - 1) - datetime.timedelta(weeks=12)

        month_start_date = month_start_date.replace(day=1).date()
        month_end_date = current_date.date()
        _months = _get_months_in_range(copy.deepcopy(month_start_date), copy.deepcopy(month_end_date))
        _months.reverse()

        tasks = []
        tasks.append({
            "func": _fetch_product_variants_supplier,
            "args": [product_id]
        })
        tasks.append({
            "func": _fetch_variants_range_data_supplier,
            "args": [product_id, start_date, end_date, state_join_clause_query, state_condition, state]
        })
        tasks.append({
            "func": _fetch_variants_monthly_data_supplier,
            "args": [product_id, str(month_start_date), str(month_end_date), state_join_clause_query,
                    state_condition, state]
        })

        variants, range_data, monthly_data = utils.concurrent_db_execution(store, tasks)

        sku_report_data = []
        sku_distribution_data = []
        month_keys = {}

        total_range_quantity = range_data.get('total_quantity', 0)
        variant_range_data = range_data.get('data')
        total_variant_count = range_data.get('variant_count')

        MONTH_NAMES = common.get_month_names()
        current_month = datetime.date.today().month
        current_year = datetime.date.today().year
        meta_info = {'month_' + str(i): '' for i in range(1, 5)}

        for i in range(1, 5):
            month_key = 'month_' + str(i)
            month_value = current_month - (i - 1)
            year_value = current_year if month_value > 0 else current_year - 1
            month_value = month_value if month_value > 0 else month_value + 12
            month_name = MONTH_NAMES[month_value]
            meta_info[month_key] = f"{month_name} {year_value}"


        if not variant_range_data:
            result_data['message'] = "No variant data available for the specified product ID."
            result_data['meta'] = {'month_rows':[meta_info]}
            response['status'] = 200
            response['data'] = result_data
            return response

        for variant_id, variant in variants.items():
            range_quantity = variant_range_data.get(variant_id, 0)
            quantity_percentage = 0
            if total_range_quantity > 0:
                quantity_percentage = round((range_quantity / total_range_quantity) * 100, 2)

            state_abbreviation = common.get_state_abbreviation(range_data.get('state', '')) if range_data.get('state', '') else ''


            sku_data = {
                'variants': variant['option'],
                'variant_id': variant_id,
                'total_quantity_by_range': int(range_quantity),
                'percentage': quantity_percentage
            }
            if state:
                sku_data['state'] = state_abbreviation
                sku_data['state_name'] = range_data.get('state', '')

            variant_monthly_data = monthly_data.get(variant_id,{})
            for idx, m in enumerate(_months):
                month_str = MONTH_NAMES.get(m)
                month_key = f"month_{idx+1}"
                month_quantity = variant_monthly_data.get(month_str, 0)
                sku_data[month_key] = month_quantity
                month_keys[month_key] = month_str

            sku_report_data.append(sku_data)

        if len(sort_array) > 0:
            sort_direction = False if sort_array[1] == '1' else True
            sku_report_data = sorted(sku_report_data, key=lambda x: x[sort_array[0]], reverse=sort_direction)
        else:
            sku_report_data = sorted(sku_report_data, key=lambda x: x['percentage'], reverse=True)

        sku_distribution = {
            'total_quantity_sku_distribution': total_range_quantity,
            'total_variant_count': total_variant_count
        }
        sku_distribution_data.append(sku_distribution)

        result_data['data'] = sku_report_data
        result_data['sku_distribution'] = sku_distribution_data
        result_data['meta'] = {
            'month_rows': [meta_info]
        }
        response['status'] = 200
        response['data'] = result_data
    finally:
        if conn:
            conn.close()
    return response

def get_product_performance_data_suppliers(store, product_id, start_date, end_date, supplier, user):
    conn = new_pgdb.get_connection(store['id'])
    result_data = {}
    try:
        end_date = datetime.datetime.now()
        start_date = end_date - timedelta(days=120)  # 4 months = 30 days/month * 4 months

        base_query = f"""
            SELECT
                COUNT(apt.product_id) AS total_products_sold,
                EXTRACT(WEEK FROM apt.order_date_time) AS week_number
            FROM
                {AnalyticsDB.get_products_trend_table()} apt
            LEFT JOIN
                skuvault_catalog sc ON apt.parent_sku = sc.parent_sku
            LEFT JOIN
                {new_pgdb.DBTables.supplier_app_user_supplier_mapping} usm ON sc.primary_supplier = usm.suppliers
            WHERE
                apt.product_id = :product_id
                AND apt.order_date_time BETWEEN :start_date AND :end_date
        """

        params = {
            "product_id": product_id,
            "start_date": start_date,
            "end_date": end_date
        }

        if supplier:
            base_query += " AND sc.primary_supplier = :supplier"
            params["supplier"] = supplier.strip()

        if user:
            base_query += " AND usm.email_id = :user"
            params["user"] = user.strip()

        base_query += """
            GROUP BY
                week_number
        """

        chart_query = text(base_query)
        total_variants_data = conn.execute(chart_query.params(**params))
        chart_results = total_variants_data.fetchall()

        sequential_week_number = 1
        for result in chart_results:
            products_sold = result[0]
            result_data[f'week_{sequential_week_number}'] = products_sold
            sequential_week_number += 1

        for i in range(sequential_week_number, 19):
            result_data[f'week_{i}'] = 0

    finally:
        if conn:
            conn.close()

    return [result_data]

def get_product_sold_states_suppliers(store, product_id, start_date, end_date, username):
    response = {
        "status": 400
    }
    result_data = {}
    states = []
    conn = new_pgdb.get_connection(store['id'])
    try:
        has_access = _check_product_access(conn, store['id'], product_id, username)
        if not has_access:
            response['message'] = "You don't have permission to access this product's data"
            response['status'] = 403
            return response

        if not product_id or not start_date or not end_date:
            response['message'] = "Invalid product_id, start_date or end_date"
            response['status'] = 400
            return response

        base_query = f"""
            SELECT DISTINCT sh.state
            FROM {new_pgdb.DBTables.order_shipping_addresses_table} sh
            JOIN {AnalyticsDB.get_products_trend_table()} ap ON ap.order_id = sh.order_id
            WHERE ap.product_id = :product_id
              AND ap.order_date_time BETWEEN :start_date AND :end_date
        """

        params = {
            "product_id": product_id,
            "start_date": start_date,
            "end_date": end_date
        }

        query = text(base_query)
        data = conn.execute(query.params(**params))
        results = data.fetchall()

        states = [result[0] for result in results if result[0] not in (None, '')]
        total_states_count = len(states)

        result_data = {
            'states': states,
            'state_count': total_states_count
        }
        response['status'] = 200
        response['data'] = result_data

    finally:
        if conn:
            conn.close()

    return response

def get_product_sale_supplier(store, product_id, start_date, end_date, username, period="day"):

    response = {
        "status": 400
    }
    date_format = "%Y-%m-%d"
    date_start = datetime.datetime.strptime(start_date, date_format)
    date_end = datetime.datetime.strptime(end_date, date_format)
    date_diff = (date_end - date_start).days
    period = period.lower()

    query = None
    data = {}
    if period == "day":

        # if date_diff > 30:
        #     date_end = (date_start + datetime.timedelta(days=30))
        #     end_date = date_end.strftime(date_format)

        query = f"""
        select
            max(apt.product_name) as product_name,
            sum(apt.quantity) as quantity,
            sum(apt.total) / sum(apt.quantity) as price,
            sum(apt.total) as total,
            count(apt.order_id) as orders,
            apt.order_date_time,
            apt.order_year,
            COALESCE(scr.type, 'Other') AS customer_type
        from
            {AnalyticsDB.get_products_trend_table()} apt
        inner join
            salesforce_customer_rep scr on apt.customer_id = scr.customer_id
        where
            apt.product_id={product_id}
            and apt.order_date_time >= '{start_date}'
            and apt.order_date_time <= '{end_date}'
        group by
            apt.order_date_time, apt.order_year, COALESCE(scr.type, 'Other')
        order by
            apt.order_year, apt.order_date_time;
        """

    elif period == "week":

        # if date_diff > 92:
        #     date_end = (date_start + datetime.timedelta(days=91))
        #     end_date = date_end.strftime(date_format)

        query = f"""
        select
            max(apt.product_name) as product_name,
            sum(apt.quantity) as quantity,
            sum(apt.total) / sum(apt.quantity) as price,
            sum(apt.total) as total,
            count(apt.order_id) as orders,
            apt.order_week,
            apt.order_year,
            COALESCE(scr.type, 'Other') AS customer_type
        from
            {AnalyticsDB.get_products_trend_table()} apt
        left join
            salesforce_customer_rep scr on apt.customer_id = scr.customer_id
        where
            apt.product_id={product_id}
            and apt.order_date_time >= '{start_date}'
            and apt.order_date_time <= '{end_date}'
        group by
            apt.product_id, apt.order_week, apt.order_year, COALESCE(scr.type, 'Other')
        order by
            apt.order_year, apt.order_week;
        """

    elif period == "month":

        # if date_diff > 365:
        #     date_end = (date_start + datetime.timedelta(days=365))
        #     end_date = date_end.strftime(date_format)

        query = f"""
        select
            max(apt.product_name) as product_name,
            sum(apt.quantity) as quantity,
            sum(apt.total) / sum(apt.quantity) as price,
            sum(apt.total) as total,
            count(apt.order_id) as orders,
            apt.order_month,
            apt.order_year,
            COALESCE(scr.type, 'Other') AS customer_type
        from
            {AnalyticsDB.get_products_trend_table()} apt
        left join
            salesforce_customer_rep scr on apt.customer_id = scr.customer_id
        where
            apt.product_id={product_id}
            and apt.order_date_time >= '{start_date}'
            and apt.order_date_time <= '{end_date}'
        group by
            apt.product_id, apt.order_month, apt.order_year, COALESCE(scr.type, 'Other')
        order by
            apt.order_year, apt.order_month;
        """

    elif period == "year":
        # if date_diff > 3650:
        #     date_end = (date_start + datetime.timedelta(days=3650))
        #     end_date = date_end.strftime(date_format)
        date_index = date_start
        while date_index <= date_end:
            data[str(date_index.year)] = {
                "quantity": 0
            }
            date_index = date_index + datetime.timedelta(days=365)
        query = f"""
        select
            max(apt.product_name) as product_name,
            sum(apt.quantity) as quantity,
            sum(apt.total) / sum(apt.quantity) as price,
            sum(apt.total) as total,
            count(apt.order_id) as orders,
            apt.order_year,
            COALESCE(scr.type, 'Other') AS customer_type
        from
            {AnalyticsDB.get_products_trend_table()} apt
        left join
            salesforce_customer_rep scr on apt.customer_id = scr.customer_id
        where
            apt.product_id={product_id}
            and apt.order_date_time >= '{start_date}'
            and apt.order_date_time <= '{end_date}'
        group by
            apt.product_id, apt.order_year, COALESCE(scr.type, 'Other')
        order by
            apt.order_year;
        """
    result = {
        "product_name": "",
        "quantity": 0,
        "avg_price": 0,
        "total": 0,
        "orders": 0,
        "data": {}
    }

    if query:
        query=query.replace('\n', '')
        conn = new_pgdb.get_connection(store['id'])

        has_access = _check_product_access(conn, store['id'], product_id, username)
        if not has_access:
            response['message'] = "You don't have permission to access this product's data"
            response['status'] = 403
            return response
        try:
            rs = conn.execute(text(query))
            for row in rs:
                if row[1] > 0:
                    result["product_name"] = str(row[0])
                    result["quantity"] = result["quantity"] + int(row[1])
                    result["total"] = result["total"] + row[3]
                    result["orders"] = result["orders"] + int(row[4])
                    index = str(row[5])
                    if period in ['week','qtr','month']:
                        index = str(row[5]) + "_" + str(row[6])
                    if period == 'year':
                        data.setdefault(row[6], {})[index] = {
                            "quantity": int(row[1]),
                            "revenue": round(row[3], 2)
                        }
                    else:
                        data.setdefault(row[7], {})[index] = {
                            "quantity": int(row[1]),
                            "revenue": round(row[3], 2)
                        }
            # Convert inner dictionary to array format while maintaining outer structure
            for customer_type, time_periods in data.items():
                period_array = []
                for period_key, details in time_periods.items():
                    period_data = {}
                    period_data[period_key] = {
                        "quantity": details["quantity"],
                        "revenue": details["revenue"]
                    }
                    period_array.append(period_data)
                data[customer_type] = period_array

            # Get state quantity data
            exist_query=f"""
                select sum(pt.quantity) as quantity_count, sh.state from
                {AnalyticsDB.get_products_trend_table()} pt, order_shipping_addresses sh
                where pt.order_id=sh.order_id and pt.product_id={product_id} and pt.order_date_time >= '{start_date}' and pt.order_date_time <= '{end_date}'
                group by sh.state
                order by quantity_count desc
                """
            records=conn.execute(text(exist_query))
            state_quantity={}
            for record in records:
                state_quantity[record[1]]={
                    "quantity":int(record[0])
                }
            result['data'] = data
            result['state_quantity']=state_quantity
            if result['quantity'] > 0:
                result['avg_price'] = round(result['total'] / result['quantity'], 2)
                result["total"] = round(result["total"], 2)
            if result['product_name'] == "":
                name_query = """select product_name from products where product_id={product_id}""".format(product_id=product_id)
                name_record=conn.execute(text(name_query))
                for row in name_record:
                    result["product_name"] = str(row[0])
            
            response['status'] = 200
            response['data'] = result
        finally:
            conn.close()

    return response

def get_supplier_products(store, query_params):
    # Extract query params
    brands = query_params.get('brands', '')
    user = query_params.get('user', None)
    search = query_params.get('search', '').strip()
    page = int(query_params.get('page', 1))
    limit = int(query_params.get('limit', 10))
    sort_by = query_params.get('sort_by', 'total_sold_quantity/-1')
    sort_array = sort_by.split("/") if sort_by != '' else []
    # start_date = query_params.get('start_date', None)
    # end_date = query_params.get('end_date', None)

    # Get last 6 months
    sale_history_months = 6
    month_names, _ = common.get_month_array_for_meta(sale_history_months)
    month_keys = list(month_names.keys())
    today = datetime.datetime.today()
    formatted_month_labels = []
    for i in range(sale_history_months):
        month_date = today.replace(day=1) - relativedelta(months=(sale_history_months - 1 - i))
        formatted_month_labels.append(month_date.strftime('%b %Y'))
    formatted_month_labels.reverse()

    # Map month label to month_key
    month_label_to_key = {label: key for key, label in zip(month_keys, formatted_month_labels)}

    # Update Query for applying sorting
    sort_column = 'total_sold_quantity'
    sort_dir = 'DESC'
    nulls_order = ''
    if len(sort_array) == 2:
        requested_col = sort_array[0]
        requested_dir = sort_array[1]
        # If sorting by month label, map to month_key
        if requested_col in month_label_to_key:
            sort_column = month_label_to_key[requested_col]
        else:
            sort_column = requested_col
        sort_dir = 'ASC' if requested_dir == '1' else 'DESC'
        nulls_order = "NULLS FIRST" if sort_dir.lower() == "asc" else "NULLS LAST"

    valid_columns = ['product_id', 'product_name', 'sku', 'total_sold_quantity', 'total_available_quantity', 'last_out_of_stock_date', 'last_received_date'] + month_keys
    order_by_sql = ''
    if sort_column in valid_columns:
        if sort_column in month_keys:
            order_by_sql = f'ORDER BY "{sort_column}" {sort_dir} {nulls_order}'
        else:
            order_by_sql = f'ORDER BY {sort_column} {sort_dir} {nulls_order}'

    offset = (page - 1) * limit

    # Update query for getting products sold by supplier with filters like user, supplier, search
    where_clauses = []
    params = {}
    if user:
        where_clauses.append(f"p.product_id IN (SELECT DISTINCT product_id FROM {new_pgdb.DBTables.supplier_app_user_supplier_mapping} where email_id = :user)")
        params["user"] = user.strip()
    
    if brands:
        where_clauses.append(f"""p.product_id IN (SELECT DISTINCT product_id FROM {new_pgdb.DBTables.supplier_app_user_supplier_mapping} WHERE brand_id IN :brands)""")
        params['brands'] = tuple(brands.split(','))

    if search:
        where_clauses.append('(p.product_name ILIKE :search OR p.sku ILIKE :search)')
        params['search'] = f'%{search}%'
    
    # if start_date and end_date:
    #     where_clauses.append('pt.order_date_time >= :start_date and pt.order_date_time <= :end_date')
    #     params['start_date'] = start_date
    #     params['end_date'] = end_date

    where_sql = " AND ".join(where_clauses) if where_clauses else '1=1'

    # Get month keys and formatted month labels
    months_case_sql = ",\n                ".join([
        f"COALESCE(MAX(rp.{month_key}), 0) AS {month_key}" for month_key in month_keys
    ])

    # Final query to get final output for products sold by supplier and apply pagination
    query = f'''
        SELECT
            p.product_id,
            p.product_name,
            p.sku AS sku,
            SUM(pt.quantity) AS total_sold_quantity,
            MAX(COALESCE(p.inventory_level, 0)) AS total_available_quantity,
            rp.out_of_stock_date AS last_out_of_stock_date,
            rp.out_of_stock_end_date AS last_received_date,
            {months_case_sql}
        FROM {new_pgdb.DBTables.products} p
        LEFT JOIN {AnalyticsDB.get_replenishment_products_table()} rp ON p.product_id = rp.product_id
        LEFT JOIN {AnalyticsDB.get_products_trend_table()} pt ON p.product_id = pt.product_id
        WHERE {where_sql}
        GROUP BY p.product_id, p.product_name, p.sku, rp.out_of_stock_date, rp.out_of_stock_end_date
        {order_by_sql}
        LIMIT :limit OFFSET :offset
    '''
    params["limit"] = limit
    params["offset"] = offset
    response = {"status": 400}

    # Count query for total count
    count_query = f'''
        SELECT COUNT(*) FROM (
            SELECT p.product_id
            FROM {new_pgdb.DBTables.products} p
            LEFT JOIN {AnalyticsDB.get_replenishment_products_table()} rp ON p.product_id = rp.product_id
            LEFT JOIN {AnalyticsDB.get_products_trend_table()} pt ON p.product_id = pt.product_id
            WHERE {where_sql}
            GROUP BY p.product_id, p.product_name, p.sku
        ) AS subquery
    '''

    # Execute query to get results
    conn = new_pgdb.get_connection(store['id'])
    try:
        total_count = conn.execute(text(count_query), params).scalar()
        results = conn.execute(text(query), params)
        data = []
        parent_sku_str = ''
        for row in results.fetchall():
            parent_sku_str = parent_sku_str + "'" + str(row[2]) + "', "
            prod = {
                'product_id': row[0],
                'product_name': row[1],
                'sku': row[2],
                'total_sold_quantity': float(row[3] or 0),
                'total_available_quantity': float(row[4] or 0),
                'last_6_months': [
                    {'month': formatted_month_labels[i], 'quantity': float(row[7+i])} for i in range(sale_history_months)
                ],
                'last_out_of_stock_date': convert_to_timestamp(row[5]),
                'last_received_date': convert_to_timestamp(row[6]),
                'child_data': []
            }
            data.append(prod)
        paginated_data = calculatePaginationData(data, page, limit, total_count)
        parent_sku_str = parent_sku_str.rstrip(', ')
        child_data_array = get_variants_data(conn, parent_sku_str, months_case_sql, formatted_month_labels, sale_history_months)
        
        if child_data_array['status'] == 200:
            child_array = child_data_array['data']['data']
            parent_sku_list = [sku.strip().strip("'") for sku in parent_sku_str.split(',')]
            for p_sku in parent_sku_list:
                for item in paginated_data['data']:
                        if p_sku == item.get('sku'):
                            item['child_data'] = child_array.get(p_sku, {})
                            break
        response['status'] = 200
        response['data'] = paginated_data
    finally:
        if conn:
            conn.close()
    return response

def get_variants_data(conn, parent_sku_str, months_case_sql, formatted_month_labels, sale_history_months):
    # write a function to get the variants data for the given parent sku and keep all the columns same as the parent sku data
    response = {
        "status": 400,
        "data": {}
    }
    # conn = new_pgdb.get_connection(store['id'])
    try:
        # if start_date and end_date:
        #     where_clauses.append('vt.order_date_time >= :start_date and vt.order_date_time <= :end_date')
        #     params['start_date'] = start_date
        #     params['end_date'] = end_date
        query_result = None
        if parent_sku_str != '':
            base_query = f"""
                            select MAX(v.product_name), rp.parent_sku, rp.sku, v.variant_options, 
                            SUM(vt.quantity) AS total_sold_quantity,
                            MAX(COALESCE(v.variants_inventory_level, 0)) AS total_available_quantity,
                            rp.out_of_stock_end_date AS last_received_date,
                            rp.total_out_of_stock_days AS total_out_of_stock_days,
                            rp.restocked_inventory_level,
                            rp.out_of_stock_start_date, {months_case_sql}
                            FROM {AnalyticsDB.get_variants_trend_table()} vt
                            LEFT JOIN {AnalyticsDB.get_replenishment_variants_table()} rp ON vt.variant_id = rp.variant_id
                            LEFT JOIN {new_pgdb.DBTables.variants} v ON vt.variant_id = v.variants_id
                            WHERE vt.parent_sku IN ({parent_sku_str}) GROUP BY v.variant_options, rp.sku, rp.parent_sku,
                            rp.out_of_stock_end_date, rp.total_out_of_stock_days, rp.restocked_inventory_level, rp.out_of_stock_start_date
                            ORDER BY v.variant_options ASC
                            """
            
            query_result = conn.execute(text(base_query))
            query_result = list(query_result)
        formatted_data = {}
        final_result = {
            'data': [],
            'meta': {}
        }
        if query_result:
            for data in query_result:
                obj = {}
                obj["product_title"] = data[0]
                obj["parent_sku"] = data[1]
                obj["sku"] = data[2]
                obj['variant_options'] = data[3]
                obj["total_sold_quantity"] = int(data[4] or 0)
                obj["quantity_available"] = int(data[5] or 0)
                obj["last_received_date"] = convert_to_timestamp(data[6])
                obj["total_out_of_stock_days"] = int(data[7]) if data[7] != None else 0
                obj["restocked_inventory_level"] = int(data[8]) if data[8] != None else 0
                obj["out_of_stock_date"] = convert_to_timestamp(data[9])
                obj["last_6_months"] = [
                    {'month': formatted_month_labels[i], 'quantity': float(data[10+i])} for i in range(sale_history_months)
                ]
                if data[1] in formatted_data:
                    if formatted_data[data[1]] is not None:
                        formatted_data[data[1]].append(obj)
                    else:
                        formatted_data[data[1]] = [obj]
                else:
                    formatted_data[data[1]] = [obj]
            final_result['data'] = formatted_data
            response['data'] = final_result
            response['status'] = 200
    except Exception as e:
        logger.error(f"Error in get_variants_data: {e}")
        response['status'] = 500
        response['message'] = str(e)
    # finally:
    #     conn.close()
    return response