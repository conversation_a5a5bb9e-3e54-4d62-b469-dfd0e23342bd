from datetime import datetime
import logging
import os
from werkzeug.utils import secure_filename
from bson import ObjectId
from flask import make_response, send_file
from pymongo.collation import Collation
from new_mongodb import StoreDBCollections, cms_db, count_documents_storefront_collection, fetchall_documents_from_storefront_collection, process_documents
from new_mongodb import get_store_db_client_for_store_id, get_admin_db_client_for_store_id
from fields.blogs_fields import blog_fields
from fields.cms_fields import cms_blog_author_fields
import new_utils
from utils.common import get_paginated_records_updated, calculatePaginationData

logger = logging.getLogger()

ALLOWED_EXTENSIONS = set(['png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov', 'wmv', 'mkv'])

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def change_file_name(filename):
    rendom = str(round(datetime.utcnow().timestamp()))
    if ('.' in filename):
        ext = filename.rsplit('.', 1)[1].lower()
        fname = rendom + '.' + ext
        return fname

def _get_related_blogs(db_client, blog_tags, current_blog_id):
    collection = db_client[StoreDBCollections.BLOGS_COLLECTION]
    related_blogs = list(collection.find({
        'tags': {'$in': blog_tags},
        '_id': {'$ne': ObjectId(str(current_blog_id))}
    }).sort("created_at", -1).limit(3))
    related_blogs = new_utils._process_list(related_blogs)
    return new_utils.parse_json(related_blogs)

def get_blogs(store, body):
    result = []
    body['filterBy'] = ['name', 'created_at']

    db_client = get_store_db_client_for_store_id(store['id'])
    blog_author_collection = db_client[StoreDBCollections.BLOGS_AUTHOR]
    blog_category_collection = db_client[StoreDBCollections.BLOGS_CATEGORY]

    blogs, total_data_length, page, limit = new_utils.get_paginated_records_updated(db_client=db_client, \
                                            collection_name=StoreDBCollections.BLOGS_COLLECTION, payload=body, \
                                            fields=blog_fields, additional_query='')

    count = 0
    for blog in blogs:
        res = {}
        blogData = {}
        author = {}
        category = {}
        authorName = ''
        categoryName = ''
        if blog['author_details']:
            author = blog_author_collection.find_one(
                {'_id': ObjectId(str(blog['author_details']))})
        if blog['category_details']:
            category = blog_category_collection.find_one(
                {'_id': ObjectId(str(blog['category_details']))})

        if author and category:
            authorName = author['name']
            categoryName = category['name']

        blogData['image_url'] = blog['image_url'] 
        blogData['content'] = blog['content']
        blogData['short_desc'] = blog['short_desc']
        blogData['author_details'] = authorName
        blogData['category_details'] = categoryName
        blogData['seo_details'] = blog['seo_details']
        blogData['tags'] = blog['tags']

        res['id'] = blog['id']
        res['index'] = count
        res['name'] = blog['name']
        res['description'] = blog['description']
        res['created_at'] = blog['created_at']
        res['updated_at'] = blog['updated_at']
        res['url'] = blog['url']
        res['status'] = blog['status']
        res['data'] = blogData

        related_blogs = _get_related_blogs(db_client, blog['tags'], blog['id'])
        related_blogs_data = []
        for related_blog in related_blogs:
            related_blog_data = {
                'id': related_blog['id'],
                'name': related_blog['name'],
                'description': related_blog['description'],
                'content': related_blog['content'],
                'short_desc':  related_blog['short_desc'],
                'url': related_blog['url'],
                'image_url': related_blog['image_url']
            }
            related_blogs_data.append(related_blog_data)

        res['related_blogs'] = related_blogs_data
        count = count + 1
        result.append(res)

    data = new_utils.calculate_pagination(result, page, limit, total_data_length)
    return data

def create_blog(store, body):
    response = {
        "status": 400
    }

    data = {
        "data": ''
    }
    content = {
        "config": data
    }
    url = body['name'].strip()
    # url = '/pages/' + url.replace(" ", "_").lower()
    url = '/' + url.replace(" ", "-").lower()
    seo_details = {
        "blog_name": body['name'].strip(),
        "blog_url": url,
        "meta_title": "",
        "meta_keywords": "",
        "meta_description": "",
    }
    isUniqueName = check_for_unique_blog_name(store, body['name'], '')
    if isUniqueName:
        body["created_at"] = int(datetime.utcnow().timestamp())
        body["updated_at"] = ""
        body["url"] = url
        body['image_url'] = ""
        body['content'] = [content]
        body['short_desc'] = ""
        body["author_details"] = ""
        body["category_details"] = ""
        body['tags'] = []
        body['seo_details'] = [seo_details]
        body['status'] = "active"

        body['name'] = body['name'].strip()
        id = cms_db.create_blogs(store, body)
        response['message'] = "Blog created successfully"
        response['status'] = 200
    else:
        response['message'] = "name: The provided blog name has already been matched with other blogs. Please provide a different blog name."
        response['status'] = 409

    return response

def check_for_unique_blog_name(store, name, blog_id):
    url = name.strip()
    url = '/' + url.replace(" ", "-").lower()
    blogs = cms_db.get_all_blogs(store)
    blogs = new_utils.parse_json(new_utils._process_list(blogs))
    for blog in blogs:
        if not blog['id'] == blog_id:
            if blog['url'] == url:
                return False
    return True

def update_blog(store, body, blog_id=None):
    response = {
        "status": 400
    }
    old_blog = cms_db.get_blog_by_id(store, blog_id)
    # old_blog = super().find_one({"_id": ObjectId(str(blog_id))})
    seo_details = []
    if old_blog:
        seo_details = old_blog['seo_details']
    url = body['name'].strip()
    url = '/' + url.replace(" ", "-").lower()
    seo_details[0]['blog_name'] = body['name'].strip()
    if body['status_update'] == 'false':
        isUniqueName = check_for_unique_blog_name(store, body['name'], blog_id)
    else:
        isUniqueName = True

    if isUniqueName:
        update_obj={
                    "name": body['name'].strip(),
                    "description": body['description'],
                    "status": body['status'],
                    "seo_details": seo_details,
                    "updated_at":  int(datetime.utcnow().timestamp())
                }
        cms_db.update_blog(store, {"_id": ObjectId(str(blog_id))}, {"$set": update_obj})
        response['message'] = "Blog Updated successfully"
        response['status'] = 200
    else:
        response['message'] = "name: The provided blog name has already been matched with other blogs. Please provide a different blog name."
        response['status'] = 409

    return response

def delete_by_id(store, blog_id):
    return cms_db.delete_blog_by_id(store, blog_id)

def set_data(store, body, blog_id=None):
    response = {
        "status": 400
    }
    new_page_url = body["seo_details"][0]["blog_url"].strip()
    if new_page_url.startswith('/'):
        new_page_url = new_page_url[1:]

    if not new_page_url == '':
        new_page_url = '/' + new_page_url
        isUrlUnique = check_for_unique_url(store, new_page_url, blog_id)
        if isUrlUnique:
            new_name = body["seo_details"][0]["blog_name"]
            if not new_name == '':
                isNameUnique = check_for_unique_name(store,new_name.strip(), blog_id)
                if isNameUnique:
                    seo_details = body['seo_details']
                    # Update blog_url value
                    seo_details[0]["blog_url"] = new_page_url
                    blog = cms_db.get_blog_by_id(store, blog_id)
                    # blog = super().find_one(
                    #     {"_id": ObjectId(str(blog_id))})
                    update_obj={
                                "url": new_page_url,
                                "name": new_name.strip(),
                                "image_url": body['image_url'],
                                "seo_details": seo_details,
                                "author_details": body['author_details'],
                                "category_details": body['category_details'],
                                "short_desc": body['short_desc'],
                                "content": body['content'],
                                "tags": body['tags'],
                                "updated_at":  int(datetime.utcnow().timestamp())
                            }
                    cms_db.update_blog(store, {"_id": ObjectId(str(blog_id))}, {"$set": update_obj})
                    response['message'] = "changes saved sucessfuly"
                    response['status'] = 200
                else:
                    response['message'] = "The provided blog name has already been matched with other blogs. Please provide a different blog Name."
                    response['status'] = 409
            else:
                response['message'] = "Blog name is required."
                response['status'] = 409
        else:
            response['message'] = "The provided blog URL has already been matched with other blogs. Please provide a different blog URL."
            response['status'] = 409
    else:
        response['message'] = "Please provide blog url."
        response['status'] = 409

    return response

def get_blog(store, blog_id=None):
    result = {}
    blogData = {}
    blog = cms_db.get_blog_by_id(store, blog_id)
    new_utils._process_document(blog)
    # blog = super().find_one({"_id": ObjectId(str(blog_id))})

    blogData['author_details'] = blog['author_details']
    blogData['category_details'] = blog['category_details']
    blogData['seo_details'] = blog['seo_details']
    blogData['content'] = blog['content']
    blogData['short_desc'] = blog['short_desc']
    blogData['tags'] = blog['tags']
    blogData['image_url'] = blog['image_url']

    result['id'] = blog['id']
    result['name'] = blog['name']
    result['description'] = blog['description']
    result['created_at'] = blog['created_at']
    result['updated_at'] = blog['updated_at']
    result['url'] = blog['url']
    result['status'] = blog['status']
    result['data'] = blogData

    return result

def setImage(body):
    response = {
        "status": 400
    }

    if not os.path.exists('images/blogs/images'):
        os.makedirs('images/blogs/images')

    file = body['image']
    UPLOAD_FOLDER = os.path.join('images/blogs/images')

    if (file.filename == ''):
        response['message'] = "No image selected for uploading"
        response['status'] = 500

    if (file and allowed_file(file.filename)):
        newName = change_file_name(file.filename)
        fname = secure_filename(newName)
        file.save(os.path.join(UPLOAD_FOLDER, fname))
        base_path = os.path.join(os.path.abspath(os.getcwd()), UPLOAD_FOLDER, newName)
        
        if '/app/images' in base_path:
            base_path = base_path.replace('/app/images', '')  
        
        response['message'] = base_path
        response['status'] = 200
    else:
        response['message'] = "Allowed image types are -> png, jpg, jpeg, gif, mp4, avi, mov, wmv, mkv"
        response['status'] = 500

    return response

def getImage(body):
    url = './images/blogs/images/' + body['image']
    if not os.path.exists(url):
        return make_response({'error': 'Image not found'}, 404)

    return send_file(url, as_attachment=True)

def create_bc_blog(store, blog):
    authorID = ''
    db_client = get_store_db_client_for_store_id(store['id'])
    blog_author_collection = db_client[StoreDBCollections.BLOGS_AUTHOR]
    blogs_collection = db_client[StoreDBCollections.BLOGS_COLLECTION]
    if str(blog['author']) != '':
        author = blog_author_collection.find_one(
            {'name': str(blog['author'])})
        if author:
            authorID = author['_id']
        else:
            new_author = {
                "name": str(blog['author']),
                "description": "",
                "created_at": int(datetime.utcnow().timestamp()),
                "updated_at": "",
                "status": "active"
            }
            result = blog_author_collection.insert_one(new_author)
            authorID = result.inserted_id
    else:
        author = blog_author_collection.find_one({'name': 'Author'})
        authorID = author['_id']

    body = {}
    seo_details = {
        "blog_name": blog['title'],
        "blog_url": blog['url'],
        "meta_title": blog['title'],
        "meta_keywords": blog['meta_keywords'],
        "meta_description": blog['meta_description'],
    }
    data = {
        "data": blog['body']
    }
    content = {
        "config": data
    }  
    date_str = blog['published_date']['date']
    timestamp = 0  # Default to None for invalid dates
    try:
        dt = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S.%f')
        timestamp = int(dt.timestamp())
        if timestamp < 0:
            raise ValueError(f"Date is before Unix epoch: {date_str}")
    except ValueError as e:
        print(f"Invalid date format or pre-epoch date encountered: {e}")
    except Exception as e:
        print(f"Unexpected error while processing date {date_str}: {e}")

    body['name'] = blog['title']
    body['description'] = ''
    body['created_at'] = timestamp
    body['updated_at'] = ''
    body['url'] = blog['url']
    body['image_url'] = blog['thumbnail_path']
    body['short_desc'] = blog['summary']
    body['author_details'] = str(authorID)
    body['category_details'] = ''
    body['tags'] = blog['tags']
    body['seo_details'] = [seo_details]
    body['content'] = [content]
    body['status'] = "active"
    db_blog = cms_db.get_blog_by_name(store, str(blog['title']))

    # If Blog not exists then create
    if not db_blog:
        return blogs_collection.insert_one(body).inserted_id

    if db_blog and db_blog['updated_at'] == '':
        body['updated_at'] = int(datetime.utcnow().timestamp())
        return blogs_collection.update_one({"_id": ObjectId(str(db_blog['_id']))}, {"$set": body})

def check_for_unique_url(store, url, blog_id):
    blogs = cms_db.get_all_blogs(store)
    blogs = new_utils.parse_json(new_utils._process_list(blogs))
    for blog in blogs:
        if not blog['id'] == blog_id:
            if blog['url'].strip() == url.strip():
                return False
    return True

def check_for_unique_name(store, name, blog_id):
    name = name.replace(" ", "").lower()
    blogs = cms_db.get_all_blogs(store)
    blogs = new_utils.parse_json(new_utils._process_list(blogs))
    for blog in blogs:
        if not blog['id'] == blog_id:
            if blog['name'].replace(" ", "").lower().strip() == name:
                return False
    return True


#blogs authors
def get_authors(store, body):        
    result = []

    if(body) and 'author_id' in body: 
        res={}            
        author_id = body['author_id']
        author = cms_db.get_blog_author_by_id(store, str(author_id))
        # author = super().find_one({"_id": ObjectId(str(author_id))})   
                                                        
        res['id'] = str(author['_id'])
        res['name'] = author['name']
        res['description'] = author['description']
        res['created_at'] = author['created_at']
        res['updated_at'] = author['updated_at']
        res['status'] = author['status']                      

        result.append(res)
    else:
        db = get_store_db_client_for_store_id(store['id'])
        # authors = cms_db.get_all_blogs_author(store)
        payload = {
            "page": body.get('page', 1),
            "limit": body.get('limit', 10),
            "sort_by": body.get('sort_by', 'created_at'),
            "sort_order": body.get('sort_order', 'desc'),
            "filterBy": body.get('filterBy', []),
            "filter": body.get('filter', '')
        }
        authors, document_length, page, limit = get_paginated_records_updated(db, StoreDBCollections.BLOGS_AUTHOR_COLLECTION, payload, cms_blog_author_fields, {})             
        data = calculatePaginationData(authors, page, limit, document_length)
    
    return data

def create_author(store, body):
    response = {
        "status": 400
    }                              
    isUniqueName =  check_for_unique_author_name(store, body['name'].strip(), '')   
    if isUniqueName:
        body["created_at"] = int(datetime.utcnow().timestamp())
        body["updated_at"] = ""           

        body['name'] = body['name'].strip() 
        body['status'] = "active"
        id = cms_db.create_blogs_author(store, body)                                     
        # id = super().create(body)

        response['message'] = "Author created successfully"
        response['status'] = 200
    else:
        response['message'] = "name: The provided author name has already been matched with other authors. Please provide a different author name."
        response['status'] = 409   
        
    return response

def update_author(store, body, author_id=None):
    response = {
        "status": 400
    }        
    if body['status_update'] == 'false':
        isUniqueName =  check_for_unique_author_name(store, body['name'].strip(), author_id)   
    else:
        isUniqueName = True

    if isUniqueName:
        update_obj={
                "name": body['name'].strip(),
                "description": body['description'],
                "status": body['status'],                    
                "updated_at":  int(datetime.utcnow().timestamp())
            }
        cms_db.update_blog_author(store, {"_id": ObjectId(str(author_id))}, {"$set": update_obj})
        response['message'] = "Author Updated successfully"
        response['status'] = 200
    else:
        response['message'] = "name: The provided author name has already been matched with other authors. Please provide a different author name."
        response['status'] = 409

    return response

def get_author(store, author_id=None):   
    result = {}  
    author = cms_db.get_blog_author_by_id(store, str(author_id))                    
    # author = super().find_one({"_id": ObjectId(str(author_id))})                     
    new_utils._process_document(author)       
    result['id'] = author['id']
    result['name'] = author['name']
    result['description'] = author['description']
    result['created_at'] = author['created_at']
    result['updated_at'] = author['updated_at']  
    result['status'] = author['status']              
        

    return result

def delete_author_by_id(store, author_id):
    return cms_db.delete_author_by_id(store, str(author_id))

def check_for_unique_author_name(store, name, author_id):  
    name = name.replace(" ", "").lower()
    # url = '/pages/' + url.replace(" ", "_").lower() 
    authors = cms_db.get_all_blogs_author(store)                 
    # authors = super().find_all()                 
    authors = new_utils.parse_json(new_utils._process_list(authors))  
    for author in authors:  
        if not author['id'] == author_id:
            if author['name'].replace(" ", "").lower().strip() == name:
                return False
    return True

#blogs categories
def get_categories(store, body):
    result = []
    if (body):
        res = {}
        category_id = body['category_id']
        category = cms_db.get_blog_category_by_id(store, str(category_id))
        new_utils._process_document(category)
        # category = super().find_one({"_id": ObjectId(str(category_id))})

        res['id'] = category['id']
        res['name'] = category['name']
        res['description'] = category['description']
        res['created_at'] = category['created_at']
        res['updated_at'] = category['updated_at']
        res['status'] = category['status']

        result.append(res)
    else:
        categories = cms_db.get_all_blogs_category(store)
        # categories = super().find_all()
        # categories = new_utils.parse_json(new_utils._process_list(categories))
        categories = new_utils._process_list(categories)
        for category in categories:
            res = {}

            res['id'] = category['id']
            res['name'] = category['name']
            res['description'] = category['description']
            res['created_at'] = category['created_at']
            res['updated_at'] = category['updated_at']
            res['status'] = category['status']

            result.append(res)

    return result

def create_category(store, body):
    response = {
        "status": 400
    }
    isUniqueName = check_for_unique_category_name(store,body['name'].strip(), '')
    if isUniqueName:
        body["created_at"] = int(datetime.utcnow().timestamp())
        body["updated_at"] = ""

        body['name'] = body['name'].strip()
        body['status'] = "active"
        id = cms_db.create_blogs_category(store, body)
        # id = super().create(body)

        response['message'] = "Category created successfully"
        response['status'] = 200
    else:
        response['message'] = "name: The provided category name has already been matched with other categories. Please provide a different category name."
        response['status'] = 409

    return response

def update_category(store, body, category_id=None):
    response = {
        "status": 400
    }
    if body['status_update'] == 'false':
        isUniqueName = check_for_unique_category_name(store,body['name'].strip(), category_id)
    else:
        isUniqueName = True

    if isUniqueName:
        update_obj={
                    "name": body['name'].strip(),
                    "description": body['description'],
                    "status": body['status'],
                    "updated_at":  int(datetime.utcnow().timestamp())
                }
        cms_db.update_blog_category(store, {"_id": ObjectId(str(category_id))}, {"$set": update_obj})
        response['message'] = "Category Updated successfully"
        response['status'] = 200
    else:
        response['message'] = "name: The provided category name has already been matched with other categories. Please provide a different category name."
        response['status'] = 409

    return response

def get_category(store, category_id=None):
    result = {}
    category = cms_db.get_blog_category_by_id(store, str(category_id))
    # category = super().find_one({"_id": ObjectId(str(category_id))})

    result['id'] = category['id']
    result['name'] = category['name']
    result['description'] = category['description']
    result['created_at'] = category['created_at']
    result['updated_at'] = category['updated_at']
    result['status'] = category['status']

    return result

def delete_category_by_id(store, category_id):
    return cms_db.delete_blog_category_by_id(store, category_id)

def check_for_unique_category_name(store, name, category_id):
    name = name.replace(" ", "").lower()
    # url = '/pages/' + url.replace(" ", "_").()
    categories = cms_db.get_all_blogs_category(store)
    # categories = super().find_all()
    categories = new_utils.parse_json(new_utils._process_list(categories))
    for category in categories:
        if not category['id'] == category_id:
            if category['name'].replace(" ", "").lower().strip() == name:
                return False
    return True

