from flask import request
import logging
import traceback
from api import APIResource
from schemas.store_info import store_info_create
from utils import store_util
from store import store_info_service
logger = logging.getLogger()

class StoreInformation(APIResource):
 
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering StoreInfo GET")
        try:
            store_id = store['id']
            cdn_url = store_util.get_cdn_base_url(store)
            res = store_info_service.get_data(store_id, cdn_url)

            if res['status'] == 200:                
                return res['data'], 200
            else:
                return {'data': res['message']}, res['status']
        finally:
            logger.debug("Exiting StoreInfo GET")

    def put_executor(self, request, token_payload, store, token=None):
        try:
            req_body = request.get_json(force=True)
            validated_data = store_info_create.validate(req_body)
            store_id = store['id']

            res = store_info_service.set_data(store_id, validated_data)
            
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409
        finally:
            logger.debug("Exiting StoreInfo POST")
 
    def get(self):
        return self.execute_store_request(request, self.get_executor)        
            
    def put(self):
        return self.execute_store_request(request, self.put_executor)
    
# store and get images to server
class InfoImageOperation(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            query_params = request.args.to_dict()
            res = self.service.set_store_info_images().getImage(query_params)
            return res
        finally:
            logger.debug("Exiting StoreInfo GET")

    def post_executor(self, request, token_payload, store):
        try:
            req_body = request.files
            res = self.service.set_store_info_images().setImage(req_body)

            if res['status'] == 200:
                return {"status": res['message']}, 200
            else:
                return {"status": res['message']}, 500
        finally:
            logger.debug("Exiting Image Upload POST")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def post(self):
        return self.execute_store_request(request, self.post_executor)

    
