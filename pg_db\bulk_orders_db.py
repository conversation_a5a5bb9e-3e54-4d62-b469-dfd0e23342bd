from sqlalchemy.dialects.postgresql import insert
import pg_db as db
from sqlalchemy import <PERSON>olean, Column, DateTime, String, Integer, ForeignKey, Float
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import insert
import pg_db as db
from sqlalchemy import Boolean, Column, DateTime, String, Integer, ForeignKey, BigInteger
from sqlalchemy.sql import func

class BulkProductBrands(db.Base):
    __tablename__ = "bo_bulk_products_brands"    
    id = Column(Integer, primary_key=True, unique=True, nullable=False)    
    brand_name = Column(String(200), nullable=False, unique=True)
    products_count = Column(Integer, default=0)
    status = Column(String, default='active')  # options - draft, active, inactive
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

class BulkOrderProducts(db.Base):
    __tablename__ = "bo_bulk_order_products"    
    bop_id = Column(Integer, primary_key=True, unique=True, nullable=False)    
    bc_sku = Column(String, unique=True, nullable=True)
    bc_product_id = Column(Integer, nullable=False)
    bc_name = Column(String(200), nullable=True)  
    name = Column(String(200), nullable=False)    
    product_image = Column(String, nullable=True)    
    display_qty = Column(Float, nullable=False)    
    case_qty = Column(Float, nullable=False)
    status = Column(String, default='draft')  # options - draft, active, inactive, archived
    type = Column(String, default='bulkorder')  # options - bulkorder, preorder 
    is_qty_locked = Column(Boolean, default=False) # options - true, false
    orders = Column(Integer, default=0)             
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
    is_po_locked = Column(Boolean, default=False)
    brand_id = Column(Integer, ForeignKey(BulkProductBrands.__tablename__ + '.id'), nullable=False, default=0)
    min_market_price = Column(Float, default=0)
    is_marketing_product = Column(Boolean, default=False)

class BulkOrderProductVariants(db.Base):
    __tablename__ = "bo_product_variants"
    id = Column(Integer, primary_key=True, autoincrement=True)
    bop_id = Column(Integer, ForeignKey(BulkOrderProducts.__tablename__ + '.bop_id'), nullable=False)
    bc_sku = Column(String, nullable=True)
    bc_upc = Column(String, nullable=True)
    bc_variant_id = Column(Integer, nullable=True)
    bo_upc = Column(String, nullable=True)
    po_option = Column(String, nullable=True)
    option = Column(String, nullable=True)  
    current_stock = Column(Float, default=0)    
    is_active = Column(Boolean, default=True)

class PurchaseOrders(db.Base):
    __tablename__ = "bo_purchase_orders"
    
    po_id = Column(Integer, primary_key=True, unique=True, nullable=False)
    customer_id = Column(Integer, nullable=False)
    customer_name = Column(String, nullable=False)
    customer_rep_id = Column(Integer, nullable=True)
    customer_rep_name = Column(String, nullable=True)
    customer_rep_email = Column(String, nullable=True)
    status = Column(String, default='pending')  # options - pending, completed  
    type = Column(String, default='bulkorder')  # options - bulkorder, preorder    
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=True)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())   
    reason = Column(String, nullable=True)
class PurchaseOrderLineItems(db.Base):
    __tablename__ = "bo_purchase_order_lineitems"
    id = Column(Integer, primary_key=True, autoincrement=True)
    po_id = Column(Integer, ForeignKey(PurchaseOrders.__tablename__ + '.po_id'), nullable=False)
    bop_id = Column(Integer)
    variant_id = Column(Integer, ForeignKey(BulkOrderProductVariants.__tablename__ + '.id'), nullable=False)
    bc_variant_id = Column(Integer, nullable=True)
    bo_upc = Column(String, nullable=True)
    bc_upc = Column(String, nullable=True) 
    bc_sku = Column(String, nullable=True)       
    price = Column(Float)
    option = Column(String, nullable=True) 
    po_option = Column(String, nullable=True)   
    requested_qty = Column(Float, default=0)
    fullfilled_qty = Column(Float, default=0)
    remaining_qty = Column(Float, default=0)
    approved_qty = Column(Float, default=0)
    status = Column(String)     # options - backorder, completed, cancelled 
   
class POBulkOrders(db.Base):
    __tablename__ = "bo_purchase_order_bc_order_mapping"
    id = Column(Integer, primary_key=True, autoincrement=True)
    po_id = Column(Integer, ForeignKey(PurchaseOrders.__tablename__ + '.po_id'), nullable=False)
    bc_order_id = Column(String)    
    order_total = Column(Float)
    cart_id = Column(String)
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

class POBCOrderCart(db.Base):
    __tablename__ = "bo_purchase_order_cart"
    id = Column(Integer, primary_key=True, autoincrement=True)
    po_id = Column(Integer, ForeignKey(PurchaseOrders.__tablename__ + '.po_id'), nullable=False)
    cart_id = Column(String)    
    consignment_id = Column(String)
    customer_id = Column(String)
    customer_email = Column(String)
    created_by = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())


class distributions(db.Base):
    __tablename__ = "bo_distributions"
    id = Column(Integer, primary_key=True, autoincrement=True)
    bop_id = Column(Integer, ForeignKey(BulkOrderProducts.__tablename__ + '.bop_id'), nullable=False)
    start_date_time = Column(DateTime, default=func.now())
    end_date_time = Column(DateTime, default=None)
    is_qty_locked = Column(Boolean, default=False) # options - true, false
    is_active = Column(Boolean, default=False)     # options - true, false
    is_published = Column(Boolean, default=False)  # options - true, false
    created_at = Column(DateTime, server_default=func.now())  
    created_by = Column(String(100), nullable=False)
    updated_at = Column(DateTime, onupdate=func.now())  
    updated_by = Column(String(100), nullable=False)   
    orders = Column(Integer, default=0)
    salse_rep_count = Column(Integer, default=0)
    is_po_locked = Column(Boolean, default=False)
class distributions_lineitems(db.Base):
    __tablename__ = "bo_distribution_lineitems"
    id = Column(Integer, primary_key=True, autoincrement=True)
    distribution_id = Column(Integer, ForeignKey(distributions.__tablename__ + '.id'), nullable=False)
    bc_product_id = Column(Integer)    
    bc_sku = Column(String)
    variant_id = Column(Integer, ForeignKey(BulkOrderProductVariants.__tablename__ + '.id'), nullable=False)
    bc_variant_id = Column(Integer)
    bo_upc = Column(String)
    bc_upc = Column(String)
    option = Column(String, nullable=True)
    po_option = Column(String, nullable=True) 
    available_qty = Column(Float, default=0)
    requested_qty = Column(Float, default=0)
    locked_qty = Column(Float, default=0)
    distributed_qty = Column(Float, default=0)
    customer_id = Column(Integer, nullable=False)
    customer_name = Column(String(100), nullable=False) 
    customer_rep_id = Column(Integer, nullable=True)
    customer_rep_name = Column(String(100), nullable=True) 
    created_at = Column(DateTime, server_default=func.now()) 
    created_by = Column(String(100), nullable=False)
    previously_distributed_qty = Column(Float, default=0)
    customer_rep_email = Column(String, nullable=True)

class published_distribution_logs(db.Base):
    __tablename__ = "bo_published_distribution_logs"
    id = Column(Integer, primary_key=True, autoincrement=True)
    distribution_id = Column(Integer, ForeignKey(distributions.__tablename__ + '.id'), nullable=False)
    bc_product_id = Column(Integer)    
    bc_sku = Column(String)
    variant_id = Column(Integer, ForeignKey(BulkOrderProductVariants.__tablename__ + '.id'), nullable=False)
    bc_variant_id = Column(Integer)
    bo_upc = Column(String)
    bc_upc = Column(String)
    option = Column(String, nullable=True)
    po_option = Column(String, nullable=True) 
    available_qty = Column(Float, default=0)
    requested_qty = Column(Float, default=0)
    locked_qty = Column(Float, default=0)
    customer_id = Column(Integer, nullable=False)
    customer_name = Column(String(100), nullable=False) 
    customer_rep_id = Column(Integer, nullable=True)
    customer_rep_name = Column(String(100), nullable=True) 
    created_at = Column(DateTime, server_default=func.now()) 
    created_by = Column(String(100), nullable=False)

class generic_tags(db.Base):
    __tablename__ = "bo_generic_tags"
    id = Column(Integer, primary_key=True, autoincrement=True)
    tag_key = Column(String, nullable=False)
    label = Column(String, nullable=False)
    entity_type = Column(String)
    created_at = Column(DateTime, server_default=func.now())
    created_by = Column(String, nullable=False)

class generic_tags_mapping(db.Base):
    __tablename__ = "bo_generic_tags_mapping"
    id = Column(Integer, primary_key=True, autoincrement=True)
    entity_type = Column(String, nullable=False)
    entity_str_id = Column(String)
    entity_int_id = Column(BigInteger)
    tag_id = Column(String)
    tag_label = Column(String)
    created_by = Column(String, nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
    updated_by = Column(String)


