from sqlalchemy import text
import pg_db
from mongo_db import user_db
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from datetime import datetime
from projects.project_columns import update_column_attributes, get_sort_ids
import logging
import traceback
from utils.common import convert_to_timestamp

logger = logging.getLogger()  

def _fetch_default_coulmns(conn, search_value=None):
    data = []
    query = text (
        f"""SELECT * FROM {pg_db.project_columns} WHERE project_id IS NULL"""
    )
    if search_value:
        query = text(query.text + f" AND name ILIKE '%{search_value}%'")
        
    query = text(query.text + " ORDER BY sort_id;")

    result = conn.execute(query)
    for row in result.fetchall():
        row_data = {
            'id': row[0],
            'project_id':row[1],
            'name': row[2],
            'sort_id': row[3],
            'is_first_column': row[4],
            'is_last_column': row[5],
            'description': row[6],
            'is_archived': row[7],
            'created_by': row[8],
            'updated_by': row[9],
            'created_at': convert_to_timestamp(row[10]),
            'updated_at': convert_to_timestamp(row[11])
        }
        data.append(row_data)
        
    return data

def get_default_columns(search_value):
    response = {
        "status": 400 
    }
    conn = pg_db.get_connection()
    try:
        data = _fetch_default_coulmns(conn, search_value)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response

def _insert_new_default_column(conn, name, description, is_archived, username):
    try:
        max_sort_id_query = text(
            f"""SELECT MAX(sort_id) AS max_sort_id FROM {pg_db.project_columns} WHERE project_id IS NULL;"""
        )
        max_sort_id_result = conn.execute(max_sort_id_query).fetchone()
        max_sort_id = max_sort_id_result[0] or 0
        
        new_sort_id = max_sort_id + 1


        query = text(
            f"""INSERT INTO {pg_db.project_columns} (name, sort_id, is_first_column, is_last_column, description, is_archived, created_by, updated_by)
                VALUES (:name, :sort_id, :is_first_column, :is_last_column, :description, :is_archived, :created_by, :created_by);
            """
        )
        query = query.params(name=name, sort_id = new_sort_id, is_first_column=False, is_last_column=False,  description=description, is_archived=is_archived, created_by=username)
        conn.execute(query)
        min_sort_id, max_sort_id, is_first_column, sort_id, success = get_sort_ids(conn, None)
        if success:
            update_column_attributes(conn, min_sort_id, max_sort_id, sort_id, is_first_column, None, call_from_func=True)
        else:
            return False

        return True
    except Exception as e:
        logger.error(traceback.format_exc())
        return False

def add_new_default_column(name, description, is_archived, username):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:    
        data = _insert_new_default_column(conn, name, description, is_archived, username)
        if data:
            response['status'] = 200
            response['message'] = "Data inserted successfully."
        else:
            response['status'] = 409
            response['message'] = "name: Data insertion failed, Due to column already exists"
    except IntegrityError as e:
            logger.error(traceback.format_exc())
            if isinstance(e.orig, UniqueViolation):
                response['status'] = 409
                response['message'] = "name: This column already exists"
            else:
                response['status'] = 500
                response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response


def _modify_sort_id(conn, custom_field_ids):
    try:
        for index, id in enumerate(custom_field_ids, start=1):
            query = text (
                """UPDATE agile_project_columns SET sort_id = :sort_id WHERE id = :id"""
            )
            query = query.params(sort_id=index, id=id)
            conn.execute(query)
        return True
    except Exception as e:
        return False

def update_sort_id_default_columns(default_columns_ids):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        if not isinstance(default_columns_ids, list):
            default_columns_ids = [default_columns_ids]

        data = _modify_sort_id(conn, default_columns_ids)
        if data:
            min_sort_id, max_sort_id, is_first_column, sort_id, flag = get_sort_ids(conn, None)
            if flag:
                success = update_column_attributes(conn, min_sort_id, max_sort_id, sort_id, is_first_column, None, call_from_func=True)
                if success:
                    response['status'] = 200
                    response['message'] = "Data updated successfully and column attributes updated."
                else:
                    response['status'] = 400
                    response['message'] = "Data updation failed while updating column attributes."
            else:
                response['status'] = 400
                response['message'] = "Error getting sort ids."
    except IntegrityError as e:
            error_message = str(e)
            response['status'] = 500
            response['message'] = f"Something went wrong. {error_message}"
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response