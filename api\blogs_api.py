from flask import request
import logging
from api import APIResource
import traceback
from schemas.blogs import blog_schema, blog_update_schema
import json
from utils import bc, store_util
from cms.blogs import blogs_list

logger = logging.getLogger()


# get all blogs
class Blogs(APIResource):
    def get_executor(self, request, token_payload, store, tenant_id, store_id):
        try:
            query_params = request.args.to_dict()
            res =blogs_list.get_blogs(store, query_params)
            return res, 200
        finally:
            logger.debug("Exiting Blogs GET")

    def get(self, tenant_id, store_id):
        return self.execute_store_request(request, self.get_executor, tenant_id, store_id)

# create blogs


class CreateBlogs(APIResource):
    def post_executor(self, request, token_payload, store, token=None):
        try:
            req_body = request.get_json(force=True)
            validated_data = blog_schema.validate(req_body)
            res = blogs_list.create_blog(store, validated_data)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409
        finally:
            logger.debug("Exiting Blog POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)

# upadate and delete webpage


class BlogOperations(APIResource):
    def put_executor(self, request, token_payload, store, blog_id):
        try:
            req_body = request.get_json(force=True)
            validated_data = blog_update_schema.validate(req_body)
            res = blogs_list.update_blog(store, validated_data, blog_id)

            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409
        finally:
            logger.debug("Exiting Blog PUT")

    def delete_executor(self, request, token_payload, store, blog_id):
        try:
            success = blogs_list.delete_by_id(store, blog_id)
            if success:
                return {"status": "ok"}, 200
            return {"status": "failed"}, 500
        finally:
            logger.debug("Exiting Blog DELETE")

    def put(self, blog_id):
        return self.execute_store_request(request, self.put_executor, blog_id)

    def delete(self, blog_id):
        return self.execute_store_request(request, self.delete_executor, blog_id)


# operations for add blog data and get blog with it's data
class BlogData(APIResource):
    def put_executor(self, request, token_payload, store, blog_id):
        try:
            req_body = request.get_json(force=True)
            res = blogs_list.set_data(store, req_body, blog_id)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409
        finally:
            logger.debug("Exiting blog PUT")

    def get_executor(self, request, token_payload, store, blog_id):
        try:
            res = blogs_list.get_blog(store, blog_id)
            return res, 200
        finally:
            logger.debug("Exiting blog GET")

    def put(self, blog_id):
        return self.execute_store_request(request, self.put_executor, blog_id)

    def get(self, blog_id):
        return self.execute_store_request(request, self.get_executor, blog_id)

# store and get images to server


class BlogImageOperation(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            query_params = request.args.to_dict()
            res = blogs_list.getImage(query_params)
            return res
        finally:
            logger.debug("Exiting Webpage GET")

    def post_executor(self, request, token_payload, store):
        req_body = request.files
        res = blogs_list.setImage(req_body)
        if res['status'] == 200:
            return {"status": res['message']}, 200
        else:
            return {"status": res['message']}, 500

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def post(self):
        return self.execute_store_request(request, self.post_executor)


class BCBlogs(APIResource):
    def post_executor(self, request, token_payload, store, token=None):
        try:
            # STEP 1 - FETCH BC BLOGS
            res = bc.get_bc_blogs(store_util.get_bc_api_creds(store))

            # STEP 2 - ITERATE OVER BC BLOGS AND CREATE BLOGS INTO THE DB
            for blog in res:
                blogs_list.create_bc_blog(store, blog)

            return {"message": "success"}, 200
        finally:
            logger.debug("Exiting BC Blog POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)
