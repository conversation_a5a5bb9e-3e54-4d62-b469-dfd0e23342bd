2023-06-02 20:40:38,004 INFO AWS instance checking Failed
2023-06-02 20:40:38,011 INFO Azure instance checking Failed
2023-06-02 20:40:38,012 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-06-02 20:40:38,658 INFO AWS instance checking Failed
2023-06-02 20:40:38,666 INFO Azure instance checking Failed
2023-06-02 20:40:38,667 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-06-02 20:40:46,801 INFO AWS instance checking Failed
2023-06-02 20:40:46,808 INFO Azure instance checking Failed
2023-06-02 20:40:46,809 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-06-02 20:45:30,927 INFO AWS instance checking Failed
2023-06-02 20:45:30,935 INFO Azure instance checking Failed
2023-06-02 20:45:30,936 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-06-02 21:05:22,874 INFO AWS instance checking Failed
2023-06-02 21:05:22,884 INFO Azure instance checking Failed
2023-06-02 21:05:22,884 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-06-03 00:21:53,866 INFO AWS instance checking Failed
2023-06-03 00:21:53,873 INFO Azure instance checking Failed
2023-06-03 00:21:53,874 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-06-03 00:22:26,672 INFO AWS instance checking Failed
2023-06-03 00:22:26,680 INFO Azure instance checking Failed
2023-06-03 00:22:26,681 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-06-03 00:22:52,438 INFO AWS instance checking Failed
2023-06-03 00:22:52,447 INFO Azure instance checking Failed
2023-06-03 00:22:52,447 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-06-03 00:22:53,175 INFO AWS instance checking Failed
2023-06-03 00:22:53,185 INFO Azure instance checking Failed
2023-06-03 00:22:53,186 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-06-03 00:23:27,801 INFO AWS instance checking Failed
2023-06-03 00:23:27,811 INFO Azure instance checking Failed
2023-06-03 00:23:27,811 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-06-03 00:25:47,341 INFO AWS instance checking Failed
2023-06-03 00:25:47,348 INFO Azure instance checking Failed
2023-06-03 00:25:47,349 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-06-03 00:26:01,509 INFO AWS instance checking Failed
2023-06-03 00:26:01,516 INFO Azure instance checking Failed
2023-06-03 00:26:01,516 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-06-03 00:26:51,947 INFO AWS instance checking Failed
2023-06-03 00:26:51,955 INFO Azure instance checking Failed
2023-06-03 00:26:51,955 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-06-03 00:27:00,068 INFO AWS instance checking Failed
2023-06-03 00:27:00,078 INFO Azure instance checking Failed
2023-06-03 00:27:00,079 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-06-03 00:27:13,734 INFO AWS instance checking Failed
2023-06-03 00:27:13,742 INFO Azure instance checking Failed
2023-06-03 00:27:13,743 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-06-03 00:30:37,534 INFO AWS instance checking Failed
2023-06-03 00:30:37,543 INFO Azure instance checking Failed
2023-06-03 00:30:37,543 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-06-03 00:42:59,974 INFO AWS instance checking Failed
2023-06-03 00:42:59,984 INFO Azure instance checking Failed
2023-06-03 00:42:59,985 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-07-03 17:57:02,460 INFO AWS instance checking Failed
2023-07-03 17:57:02,468 INFO Azure instance checking Failed
2023-07-03 17:57:02,468 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-07-03 17:59:59,406 INFO AWS instance checking Failed
2023-07-03 17:59:59,414 INFO Azure instance checking Failed
2023-07-03 17:59:59,414 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-07-03 18:01:43,761 INFO AWS instance checking Failed
2023-07-03 18:01:43,771 INFO Azure instance checking Failed
2023-07-03 18:01:43,772 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-07-03 18:02:52,440 INFO AWS instance checking Failed
2023-07-03 18:02:52,447 INFO Azure instance checking Failed
2023-07-03 18:02:52,447 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
2023-07-03 18:03:28,752 INFO AWS instance checking Failed
2023-07-03 18:03:28,761 INFO Azure instance checking Failed
2023-07-03 18:03:28,762 ERROR agent initialization
Traceback (most recent call last):
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agentfactory.py", line 11, in get_agent
    agent_instance = initalize(options=config)
  File "/home/<USER>/admin-api-service/venv/lib/python3.10/site-packages/apminsight/agent.py", line 24, in initalize
    raise RuntimeError('Configure license key in S247_LICENSE_KEY environment')
RuntimeError: Configure license key in S247_LICENSE_KEY environment
