import logging
from utils.common import paginate_data_postgres
import new_pgdb
from sqlalchemy import text
import traceback
from new_pgdb.analytics_db import AnalyticsDB
import new_utils
import task

logger = logging.getLogger()

def get_no_sold_products_analytics_data(store, query_params):
    response = {
        "status": 400,
    }
    limit = int(query_params.get('limit', 20))
    page = int(query_params.get('page', 1))
    offset = (page - 1) * limit
    hide_discontinued = query_params.get('hide_discontinued', 'true')    
    sort_by = query_params.get('sort_by','').strip()
    sort_array = sort_by.split("/") if sort_by != '' else []
    search_value = query_params.get('searchValue', None)
    days_to_replenishment = query_params.get('days_to_replenishment', 180)
    purchaser_filter = query_params.get('purchaser', '').strip()

    column_query = ''   
    
    if days_to_replenishment:
        column_query = column_query + ', sum(rp.total_sold_' + str(days_to_replenishment) + ')'

    append_query = ''

    append_query += f" AND rp.total_sold_{days_to_replenishment} = 0"
   
    if search_value and search_value != '':
        append_query = append_query + " AND  (rp.product_title ILIKE '%" + search_value + "%' or rp.parent_sku ILIKE '%" + search_value + "%') "     

    if hide_discontinued == 'true':        
        append_query = append_query + " AND ((LOWER(rp.product_title) NOT ILIKE '%discontinued%') AND (LOWER(rp.product_title) NOT ILIKE '%discountinued%')) AND ((LOWER(sv.classification) NOT ILIKE '%discontinued%') AND (LOWER(sv.classification) NOT ILIKE '%discountinued%'))"

    if purchaser_filter:
        append_query += f" AND usm.user_name = '{purchaser_filter}'"
        
    order_by = ""
    if len(sort_array):
        sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'   
        nulls_order = "NULLS FIRST" if sort_direction.lower() == "asc" else "NULLS LAST"      
        order_by = " ORDER BY " + sort_array[0] + " " + sort_direction + " " + nulls_order

    conn = new_pgdb.get_connection(store['id'])
    try:           
        final_count_query = f"""SELECT COUNT(*) FROM (select rp.parent_sku from {AnalyticsDB.get_replenishment_products_table()} rp 
                                        left join products p ON rp.parent_sku = p.sku 
                                        left join replenishment_classifications rc ON rp.parent_sku = rc.sku 
                                        left join skuvault_catalog sv ON sv.parent_sku = rp.parent_sku 
                                        left join user_supplier_mapping usm ON sv.primary_supplier = usm.suppliers  
                                        where sv.primary_supplier <> 'Unknown' {append_query} group by rp.parent_sku, sv.primary_supplier, sv.classification) AS subquery"""                     
        
        rs = conn.execute(text(final_count_query))
        total_count = int(rs.scalar())

        base_query = f"""WITH consolidated_skuvault AS (
                            SELECT DISTINCT primary_supplier, classification, parent_sku
                            FROM skuvault_catalog GROUP BY parent_sku, primary_supplier, classification
                        )
                        select MAX(rp.product_title) AS product_title, MAX(rp.product_id) AS product_id, rp.parent_sku, MAX(rp.cost) AS cost, MAX(usm.user_name) AS purchaser_name {column_query} 
                        from {AnalyticsDB.get_replenishment_products_table()} rp 
                        left join products p ON rp.parent_sku = p.sku 
                        left join replenishment_classifications rc ON rp.parent_sku = rc.sku 
                        left join consolidated_skuvault sv ON sv.parent_sku = rp.parent_sku 
                        left join user_supplier_mapping usm ON sv.primary_supplier = usm.suppliers
                        where sv.primary_supplier <> 'Unknown' {append_query}              
                        group by rp.parent_sku, sv.primary_supplier, sv.classification
                        {order_by}""" 

        final_query = base_query +  " OFFSET " + str(offset) + " LIMIT " + str(limit)
        query_result = conn.execute(text(final_query))

        formated_data = [] 
        parent_sku_str = ''                     
        if query_result:         
            for data in query_result:
                obj = {}   
                parent_sku_str = parent_sku_str + "'" + str(data[2]) + "', " 
                   
                obj["product_title"] = data[0]
                obj["parent_sku"] = data[2]  
                obj["cost"] = float(data[3])
                obj["purchaser_name"] = data[4]
                obj["days_to_replenishment"] = days_to_replenishment
                obj['child_data'] = []

                formated_data.append(obj)                                                                                                  
                                    
            paginated_rows, current_page, total_pages, total_items = paginate_data_postgres(
                total_count, formated_data, page, limit)
            
            data = new_utils.calculate_pagination(
                paginated_rows, current_page, limit, total_items)  

            parent_sku_str = parent_sku_str.rstrip(', ')
            
            child_data_array = get_on_load_no_sold_products_child_data(conn, parent_sku_str, '', days_to_replenishment, hide_discontinued)   
            
            if child_data_array['status'] == 200:
                child_array = child_data_array['data']
                parent_sku_list = [sku.strip().strip("'") for sku in parent_sku_str.split(',')]
                
                for p_sku in parent_sku_list:                                        
                    for item in data['data']:                        
                        if p_sku == item.get('parent_sku'):    
                            item['child_data'] = child_array.get(p_sku, {})
                            break 
          
            response['message'] = 'data retrived successfully'
            response['data'] = data
            response['status'] = 200
    finally:
        conn.close()

    return response

def get_on_load_no_sold_products_child_data(conn, parent_sku_str, append_query, days_to_replenishment, hide_discontinued):
    response = {
        "status": 400,
        "data": {}
    }
    column_query = ''   
    
    if days_to_replenishment:        
        column_query = column_query + ', rp.total_sold_' + str(days_to_replenishment)      

    try:
        query_result = None
        if parent_sku_str != '':
                base_query = f"""
                            select rp.product_title, rp.sku, rp.parent_sku, rp.cost, MAX(usm.user_name) AS purchaser_name {column_query}
                            from {AnalyticsDB.get_replenishment_variants_table()} rp
                            left join variants v ON rp.sku = v.variants_sku
                            left join skuvault_catalog sv ON sv.sku = rp.sku
                            left join user_supplier_mapping usm ON sv.primary_supplier = usm.suppliers
                            where rp.parent_sku IN ({parent_sku_str}) {append_query} GROUP BY rp.product_title, rp.sku, rp.parent_sku, rp.cost {column_query}   
                            ORDER BY rp.product_title ASC                                              
                            """
                            
                query_result = conn.execute(text(base_query))
        
        formatted_data = {} 
        if query_result:
            for data in query_result:
                obj = {}                                                   
                obj["product_title"] = data[0]
                obj["sku"] = data[1]
                obj["parent_sku"] = data[2] 
                obj["cost"] = float(data[3])
                obj["purchaser_name"] = data[4]
                obj["days_to_replenishment"] = days_to_replenishment                              
                if data[2] in formatted_data:
                    if formatted_data[data[2]] is not None:
                        formatted_data[data[2]].append(obj)
                    else:
                        formatted_data[data[2]] = [obj]
                else:
                    formatted_data[data[2]] = [obj]  
            
            response['data'] = formatted_data
            response['status'] = 200           
    except Exception as e:
        logger.error(traceback.format_exc())
    
    return response

def get_no_sold_products_analytics_data_csv(store, query_params):
    response = {
        "status": 400,
    }
    if query_params:
        task_id = task.send_task(task.NO_SOLD_PRODUCTS_ANALYTICS_CSV_MAIL_TASK, args=(store['id'], query_params))
        if task_id:
            response['status'] = 200
            response['message'] = f"We are generating the data you requested for download as a CSV file. This process may take a few minutes. Once complete, the CSV will be sent to your email: {query_params['username']}."
        else:
            response['status'] = 400
            response['message'] = 'Something went wrong to generate the CSV file.'
    else:
        response['status'] = 400
        response['message'] = 'query params are required'

    return response