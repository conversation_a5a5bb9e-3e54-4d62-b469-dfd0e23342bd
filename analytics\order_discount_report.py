from new_pgdb.analytics_db import AnalyticsDB
from sqlalchemy import text
import new_pgdb
import logging
import traceback
from utils.common import calculatePaginationData
from datetime import datetime, timedelta

logger = logging.getLogger()

def _prepare_date_range_params(start_date, end_date):
    """
    Prepare date range parameters for PostgreSQL queries.
    If start_date and end_date are the same, modify end_date to include the entire day.
    """
    params = {"start_date": start_date, "end_date": end_date}
    
    if start_date == end_date:
        # If same day, modify end_date to include the entire day
        params["end_date"] = f"{end_date} 23:59:59"
    
    return params

def _get_date_range_condition(start_date, end_date, date_column="order_created_date_time"):
    """
    Generate SQL condition for date range filtering.
    If start_date and end_date are the same, use >= and <= instead of BETWEEN.
    """
    if start_date == end_date:
        return f"{date_column} >= :start_date AND {date_column} <= :end_date"
    else:
        return f"{date_column} BETWEEN :start_date AND :end_date"

def _calculate_contributed_discount_percentage(data, total_discount=None):
    if total_discount is None:
        total_discount = sum(group.get("total_discount", 0) for group in data)

    for group in data:
        group_total = group.get("total_discount", 0)
        group["contributed_discount_percentage"] = float(round(
            (group_total / total_discount * 100), 2
        )) if total_discount else 0

    return data

def get_customer_groups_order_discount_report(store_id, start_date, end_date, sort_array, customer_group_id):
    conn = new_pgdb.get_connection(store_id)
    try:
        # Prepare date parameters
        params = _prepare_date_range_params(start_date, end_date)
        date_condition = _get_date_range_condition(start_date, end_date, 'o.order_date')

        customer_group_ids = []
        if customer_group_id:
            customer_group_ids = list(map(int, customer_group_id.split(","))) if isinstance(customer_group_id, str) else []
            where_condition = "WHERE c.customer_group_id = ANY(:customer_group_ids)"
        else:
            where_condition = "WHERE c.customer_group_id IN (1, 19, 28, 32, 45, 48, 75, 90, 91, 96, 98, 103, 104, 105)"

        query = f"""
                SELECT
                    c.customer_group_name,
                    c.customer_group_id,
                    SUM(o.discount_amount) AS total_discount,
                    COUNT(DISTINCT o.order_id) AS total_order_count
                FROM
                    {new_pgdb.DBTables.customers_table} c
                LEFT JOIN
                    {AnalyticsDB.get_order_analytics_table()} o ON c.customer_id = o.customer_id
                    AND {date_condition}
                    AND o.discount_amount > 0
                {where_condition}
                GROUP BY
                    c.customer_group_id,
                    c.customer_group_name
                ORDER BY
                    total_discount DESC
                """
        if customer_group_ids:
            params["customer_group_ids"] = customer_group_ids
            result = conn.execute(text(query), params).fetchall()
        else:
            result = conn.execute(text(query), params).fetchall()
        data = []
        for row in result:
            data.append({
                "customer_group_name": row[0],
                "customer_group_id": row[1],
                "total_discount": float(round(row[2] or 0, 2)),
                "total_order_count": int(row[3] or 0)
            })
        
        total_discount = None
        if customer_group_ids:
            total_discount_query = f"""SELECT SUM(o.discount_amount) AS total_discount
                                    FROM {new_pgdb.DBTables.customers_table} c
                                LEFT JOIN
                                    {AnalyticsDB.get_order_analytics_table()} o ON c.customer_id = o.customer_id
                                    AND {date_condition}
                                    AND o.discount_amount > 0
                                WHERE
                                    c.customer_group_id IN (1, 19, 28, 32, 45, 48, 75, 90, 91, 96, 98, 103, 104, 105)"""
            result = conn.execute(text(total_discount_query), params)
            total_discount = result.scalar()
            total_discount = float(total_discount) if total_discount else None

        customer_groups = _calculate_contributed_discount_percentage(data, total_discount)

        if len(sort_array):
            sort_direction = "ASC" if sort_array[1] == "1" else "DESC"
            if sort_array[0] in ["customer_group_name", "total_discount", "contributed_discount_percentage", "total_order_count"]:
                customer_groups = sorted(customer_groups, key=lambda x: x[sort_array[0]], reverse=sort_direction == "DESC")

        return {"data": customer_groups}
    except Exception as e:
        logger.error(f"Error in get_customer_groups_coupon_discount_report: {e}")
        raise e
    finally:
        conn.close()

def get_customer_types_order_discount_report(store_id, start_date, end_date, sort_array):
    conn = new_pgdb.get_connection(store_id)
    try:
        # Prepare date parameters
        params = _prepare_date_range_params(start_date, end_date)
        date_condition = _get_date_range_condition(start_date, end_date, "o.order_date")
        
        query = f"""SELECT
                    scr.type AS customer_type,
                    SUM(o.discount_amount) AS total_discount,
                    COUNT(DISTINCT o.order_id) AS total_order_count
                FROM
                    {new_pgdb.DBTables.salesforce_customer_rep} scr
                LEFT JOIN
                    {AnalyticsDB.get_order_analytics_table()} o ON scr.customer_id = o.customer_id
                    AND {date_condition}
                    AND o.discount_amount > 0
                WHERE scr.type != '' AND scr.type IS NOT NULL
                GROUP BY
                    scr.type
                ORDER BY
                    total_discount DESC"""

        result = conn.execute(text(query), params).fetchall()
        data = []
        for row in result:
            data.append(
                {
                    "customer_type": row[0],
                    "total_discount": float(round((row[1]) or 0, 2)),
                    "total_order_count": row[2]
                }
            )

        customer_types = _calculate_contributed_discount_percentage(data)

        if len(sort_array):
            sort_direction = "ASC" if sort_array[1] == "1" else "DESC"
            if sort_array[0] in ["customer_type", "total_discount", "contributed_discount_percentage", "total_order_count"]:
                customer_types = sorted(customer_types, key=lambda x: x[sort_array[0]], reverse=sort_direction == "DESC")

        return {"data": customer_types}
    except Exception as e:
        logger.error(f"Error in get_customer_types_order_discount_report: {e}")
        raise e
    finally:
        conn.close()

def get_sales_rep_order_discount_report(store_id, page, limit, sort_array, search, start_date, end_date, sales_rep_type=''):
    conn = new_pgdb.get_connection(store_id)
    try:
        # Prepare date parameters
        params = _prepare_date_range_params(start_date, end_date)
        date_condition = _get_date_range_condition(start_date, end_date, "o.order_date")
        
        count_query = f"""SELECT
                            COUNT(DISTINCT scr.rep_email) AS total_count
                        FROM
                            {new_pgdb.DBTables.salesforce_customer_rep} scr
                        LEFT JOIN {AnalyticsDB.get_order_analytics_table()} o ON scr.customer_id = o.customer_id
                            AND {date_condition}
                            AND o.discount_amount > 0
                        WHERE scr.rep_name != '' AND scr.rep_email != '' """

        query = f"""
                SELECT
                    DISTINCT scr.rep_email,
                    scr.rep_name,
                    scr.rep_type,
                    SUM(o.discount_amount) AS total_discount,
                    COUNT(DISTINCT o.order_id) AS total_order_count
                FROM
                    {new_pgdb.DBTables.salesforce_customer_rep} scr
                LEFT JOIN
                    {AnalyticsDB.get_order_analytics_table()} o ON scr.customer_id = o.customer_id
                    AND {date_condition}
                    AND o.discount_amount > 0
                WHERE
                    scr.rep_name != '' AND scr.rep_email != ''
                """
        if search:
            query += f" AND scr.rep_name ILIKE '%{search}%'"
            count_query += f" AND scr.rep_name ILIKE '%{search}%'"
        
        sales_rep_type_array = []
        if sales_rep_type != '':
            sales_rep_type_array = list(map(str, sales_rep_type.split(","))) if isinstance(sales_rep_type, str) else []
            query  += f" AND scr.rep_type = ANY(:sales_rep_type)"
            count_query  += f" AND scr.rep_type = ANY(:sales_rep_type)"

        query += " GROUP BY scr.rep_name, scr.rep_email, scr.rep_type"

        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            nulls_order = "NULLS FIRST" if sort_direction.lower() == "asc" else "NULLS LAST"
            if sort_array[0] in ["rep_name", "rep_email", "rep_type", "total_discount", "total_order_count"]:
                query += f" ORDER BY {sort_array[0]} {sort_direction} {nulls_order}"

        if page and limit:
            page = int(page)
            limit = int(limit)
            offset = (page - 1) * limit
            query += " LIMIT :limit OFFSET :offset"
            params["limit"] = limit
            params["offset"] = offset

        if sales_rep_type_array:
            params["sales_rep_type"] = sales_rep_type_array
            total_count = conn.execute(text(count_query), params).scalar()
            result = conn.execute(text(query), params).fetchall()
        else:
            total_count = conn.execute(text(count_query), params).scalar()
            result = conn.execute(text(query), params).fetchall()
        data = []
        for row in result:
            data.append({
                "rep_email": row[0],
                "rep_name": row[1],
                "rep_type": row[2],
                "total_discount": float(round((row[3]) or 0, 2)),
                "total_order_count": row[4]
            })
        
        total_discount_query = f"""SELECT SUM(o.discount_amount) AS total_discount
                                FROM {new_pgdb.DBTables.salesforce_customer_rep} scr
                                LEFT JOIN
                                    {AnalyticsDB.get_order_analytics_table()} o ON scr.customer_id = o.customer_id
                                    AND {date_condition}
                                    AND o.discount_amount > 0
                                WHERE
                                    scr.rep_name != '' AND scr.rep_email != ''""" 
        total_discount = conn.execute(text(total_discount_query), params).scalar() or 0 
        
        sales_reps = _calculate_contributed_discount_percentage(data, float(total_discount))

        if len(sort_array):
            sort_direction = "ASC" if sort_array[1] == "1" else "DESC"
            if sort_array[0] in ["contributed_discount_percentage"]:
                sales_reps = sorted(sales_reps, key=lambda x: x[sort_array[0]], reverse=sort_direction == "DESC")

        paginated_data = calculatePaginationData(sales_reps, page, limit, total_count)
        return paginated_data
    except Exception as e:
        logger.error(f"Error in get_sales_rep_order_discount_report: {e}")
        raise e
    finally:
        conn.close()


def get_sales_rep_type_order_discount_report(store_id, start_date, end_date, sort_array):
    conn = new_pgdb.get_connection(store_id)
    try:
        # Prepare date parameters
        params = _prepare_date_range_params(start_date, end_date)
        date_condition = _get_date_range_condition(start_date, end_date, "o.order_date")
        
        query = f"""SELECT
                    DISTINCT scr.rep_type AS sales_rep_type,
                    SUM(o.discount_amount) AS total_discount,
                    COUNT(DISTINCT o.order_id) AS total_order_count
                FROM
                    {new_pgdb.DBTables.salesforce_customer_rep} scr
                LEFT JOIN {AnalyticsDB.get_order_analytics_table()} o ON scr.customer_id = o.customer_id
                    AND {date_condition}
                    AND o.discount_amount > 0
                WHERE scr.rep_type != ''
                GROUP BY
                    scr.rep_type
                ORDER BY
                    total_discount DESC"""

        result = conn.execute(text(query), params).fetchall()
        data = []
        for row in result:
            data.append({
                    "sales_rep_type": row[0],
                    "total_discount": float(round((row[1]) or 0, 2)),
                    "total_order_count": row[2]
                })

        sales_rep_types = _calculate_contributed_discount_percentage(data)

        if len(sort_array):
            sort_direction = "ASC" if sort_array[1] == "1" else "DESC"
            if sort_array[0] in ["sales_rep_type", "total_discount", "contributed_discount_percentage", "total_order_count"]:
                sales_rep_types = sorted(sales_rep_types, key=lambda x: x[sort_array[0]], reverse=sort_direction == "DESC")

        return {"data": sales_rep_types}
    except Exception as e:
        logger.error(f"Error in get_sales_rep_type_order_discount_report: {e}")
        raise e
    finally:
        conn.close()

def _get_report_type_condition(report_type, date_condition, customer_group_id, customer_type, sales_rep_email, sales_rep_type):
    if report_type == "customer_group":
        # where_condition = "WHERE c.customer_group_id = :customer_group_id"
        tables = f""" FROM {new_pgdb.DBTables.customers_table} c
                  INNER JOIN {AnalyticsDB.get_order_analytics_table()} o ON c.customer_id = o.customer_id
                  AND {date_condition} AND o.discount_amount > 0
                  WHERE c.customer_group_id = :customer_group_id """
        params = {"customer_group_id": customer_group_id}
        return tables, params
    
    elif report_type == "customer_type":
        # where_condition = "WHERE scr.type = :customer_type"
        tables = f""" FROM {new_pgdb.DBTables.salesforce_customer_rep} scr
                    INNER JOIN {AnalyticsDB.get_order_analytics_table()} o ON scr.customer_id = o.customer_id
                    AND {date_condition} AND o.discount_amount > 0
                    WHERE scr.type = :customer_type """
        params = {"customer_type": customer_type}
        return tables, params
    
    elif report_type == "sales_rep":
        # where_condition = "WHERE scr.rep_email = :sales_rep_email"
        tables = f""" FROM {new_pgdb.DBTables.salesforce_customer_rep} scr
                    INNER JOIN {AnalyticsDB.get_order_analytics_table()} o ON scr.customer_id = o.customer_id
                    AND {date_condition} AND o.discount_amount > 0
                    WHERE scr.rep_email = :sales_rep_email """
        params = {"sales_rep_email": sales_rep_email}
        return tables, params
    
    elif report_type == "sales_rep_type":
        # where_condition = "WHERE scr.rep_type = :sales_rep_type"
        tables = f""" FROM {new_pgdb.DBTables.salesforce_customer_rep} scr
                    INNER JOIN {AnalyticsDB.get_order_analytics_table()} o ON scr.customer_id = o.customer_id
                    AND {date_condition} AND o.discount_amount > 0
                    WHERE scr.rep_type = :sales_rep_type """
        params = {"sales_rep_type": sales_rep_type}
        return tables, params

def get_order_discount_charts(store_id, start_date, end_date, period, report_type, customer_group_id, customer_type, sales_rep_email, sales_rep_type):
    response = {
        "message": "",
        "status": 400
    }

    conn = new_pgdb.get_connection(store_id)
    try:        
        # Prepare date parameters
        params = _prepare_date_range_params(start_date, end_date)
        date_condition = _get_date_range_condition(start_date, end_date, "o.order_date")

        # fetch the tables and params based on the report type
        tables, conditional_param = _get_report_type_condition(report_type, date_condition, customer_group_id, customer_type, sales_rep_email, sales_rep_type)
        params.update(conditional_param)
        
        # fetch the column string and group by condition based on the period
        column_str = ''
        if period == 'day':
            column_str = 'date(o.order_date) as order_date, o.order_year'
            group_by_condition = "GROUP BY date(o.order_date), o.order_year ORDER BY o.order_year, date(o.order_date)"
        elif period == 'week':
            column_str = 'EXTRACT(WEEK FROM o.order_date) AS week_number, EXTRACT(ISOYEAR FROM o.order_date) AS iso_year'
            group_by_condition = "GROUP BY week_number, iso_year ORDER BY iso_year, week_number"
        elif period == 'month':
            column_str = 'o.order_month, o.order_year'
            group_by_condition = "GROUP BY o.order_month, o.order_year ORDER BY o.order_year, o.order_month"
        elif period == 'year':
            column_str = 'o.order_year'
            group_by_condition = "GROUP BY o.order_year ORDER BY o.order_year"
        
        # fetch the query based on the report type
        query = f"""
                SELECT
                    SUM(o.discount_amount) AS total_discount,
                    array_agg(o.order_id) FILTER (WHERE o.order_id IS NOT NULL) AS aggregated_order_ids,
                    {column_str}
                {tables}
                {group_by_condition}
                """
        result = conn.execute(text(query), params).fetchall()
        # Generate complete sequence of periods
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')

         # Create a dictionary to store all periods with zero values
        all_periods = {}
        
        if period == 'day':
            current = start
            while current <= end:
                # Keep original format for key but use datetime for sorting
                key = f"{current.strftime('%Y-%m-%d')}_{current.year}"
                sort_key = current.strftime('%Y-%m-%d')  # Used only for sorting
                all_periods[key] = {'discount': 0, 'order_count': 0, 'sort_key': sort_key}
                current += timedelta(days=1)
                
        elif period == 'week':
            # Get ISO week numbers
            current = start
            while current <= end:
                year, week, _ = current.isocalendar()
                # Keep original format for key but use datetime for sorting
                key = f"{week}_{year}"
                sort_key = f"{year:04d}-{week:02d}"  # Used only for sorting
                all_periods[key] = {'discount': 0, 'order_count': 0, 'sort_key': sort_key}
                current += timedelta(days=7)
                
        elif period == 'month':
            current = start.replace(day=1)
            while current <= end:
                # Keep original format for key but use datetime for sorting
                key = f"{str(int(current.month))}_{current.year}"
                sort_key = f"{current.year:04d}-{current.month:02d}"  # Used only for sorting
                all_periods[key] = {'discount': 0, 'order_count': 0, 'sort_key': sort_key}
                # Move to first day of next month
                if current.month == 12:
                    current = current.replace(year=current.year + 1, month=1)
                else:
                    current = current.replace(month=current.month + 1)
                    
        elif period == 'year':
            current = start.replace(month=1, day=1)
            while current <= end:
                # Keep original format for key but use datetime for sorting
                key = str(current.year)
                sort_key = f"{current.year:04d}"  # Used only for sorting
                all_periods[key] = {'discount': 0, 'order_count': 0, 'sort_key': sort_key}
                current = current.replace(year=current.year + 1)

        # Update the dictionary with actual data from query results
        for row in result:
            if period == 'day':
                key = f"{row[2].strftime('%Y-%m-%d')}_{row[3]}"
                sort_key = row[2].strftime('%Y-%m-%d')
            elif period == 'week':
                week_num = int(row[2]) if row[2] is not None else 0
                year_num = int(row[3]) if row[3] is not None else 0
                key = f"{week_num}_{year_num}"
                sort_key = f"{year_num:04d}-{week_num:02d}"
            elif period == 'month':
                month_num = int(row[2]) if row[2] is not None else 0
                year_num = int(row[3]) if row[3] is not None else 0
                key = f"{month_num}_{year_num}"
                sort_key = f"{year_num:04d}-{month_num:02d}"
            else:  # year
                year_num = int(row[2]) if row[2] is not None else 0
                key = str(year_num)
                sort_key = f"{year_num:04d}"
                
            all_periods[key] = {
                'discount': float(round(row[0] or 0, 2)),
                'order_count': len(row[1]) if row[1] else 0,
                'sort_key': sort_key
            }

        # Convert to sorted lists for chart data
        labels = []
        total_discount_data = []
        total_order_count_data = []
        
        # Sort periods chronologically using sort_key
        sorted_periods = sorted(all_periods.items(), key=lambda x: x[1]['sort_key'])
        
        for period_key, values in sorted_periods:
            labels.append(period_key)  # Keep original label format
            total_discount_data.append(values['discount'])
            total_order_count_data.append(values['order_count'])
        
        datasets = [
            {
                "label": "Total Order Discount",
                "key": "order_discount_report",
                "total_discount_data": total_discount_data,
                "total_order_count_data": total_order_count_data
            }
        ]

        data = {
            "labels": labels,
            "datasets": datasets
        }

        response["data"] = data
        response["message"] = "Order discount report charts fetched successfully"
        response["status"] = 200
    except Exception as e:
        logger.error(f"Error in get_order_discount_charts: {e}")
        response["message"] = "Error in get_order_discount_charts"
        response["status"] = 500
        raise e
    finally:
        conn.close()
    return response


