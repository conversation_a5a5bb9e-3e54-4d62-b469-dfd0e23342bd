
from utils import redis_util, product_util
import datetime
from mongo_db import cart_db, catalog_db, order_db, customer_db
from plugin import bc_cart, bc_order, bc_customers
import logging

logger = logging.getLogger()


def _parse_options(product_options=[]):
    options = []
    if len(product_options) > 0:
        for option in product_options:
            options.append({
                option["option_display_name"]: option["label"]
            })
    return options


def _get_inventory(store, product_id, skus):
    result = {}
    invetory = []
    product = catalog_db.fetch_product_by_id(store, product_id)
    # print(product)
    result["name"] = product["name"]
    result["url"] = ""
    result["image_url"] = ""
    if product["custom_url"] and product["custom_url"]["url"]:
        result["url"] = product["custom_url"]["url"]
    if product["images"] and len(product["images"]):
        for image in product["images"]:
            if image["is_thumbnail"]:
                result["image_url"] = image["url_thumbnail"]
                break

    invetory_db = {}
    if product:
        invetory_db[product['sku']] = {
            "inventory": product['inventory_level'],
            "options": []
        }
        for variant in product['variants']:
            invetory_db[variant['sku']] = {
                "options": _parse_options(variant["option_values"]),
                "inventory": variant['inventory_level']
            }

    for sku in skus:
        _inventory_level = invetory_db.get(sku, {})
        invetory.append(_inventory_level)

    result["invetory"] = invetory
    return result


def _get_current_sku_inventory(store, skus):
    inventory = redis_util.get_skus_invetory(store["id"], skus)
    result = {}
    for idx, sku in enumerate(skus):
        result[sku] = inventory[idx]
    return result


def _get_current_inventory(store, cart):
    skus = cart["line_items"].keys()
    return _get_current_sku_inventory(store, skus)


def _model_to_dto(store, cart):
    dto = {}
    dto["id"] = cart["id"]
    dto["line_items"] = []
    line_items = cart.get("line_items", {})
    current_inventory = _get_current_inventory(store, cart)

    base_amount = cart.get("base_amount", 0)
    discount_amount = cart.get("discount_amount", 0)
    cart_amount = cart.get("cart_amount", 0)
    flag = True
    total = 0
    for sku, line_item in line_items.items():
        price = line_item.get("price", 0)
        if price == 0:
            price = line_item.get("list_price", 0)
            line_item["price"] = price
        total = total + line_item["quantity"] * price
        line_item["inventory"] = current_inventory.get(sku, 0)
        dto["line_items"].append(line_item)

    dto["base_amount"] = cart.get("base_amount", total)
    dto["cart_amount"] = cart.get("cart_amount", total)
    dto["tax_included"] = cart.get("tax_included", False)
    dto["discount_amount"] = cart.get("discount_amount", 0)
    dto["coupons"] = cart.get("coupons", [])
    dto["discounts"] = cart.get("discounts", [])
    dto["currency"] = cart.get("currency", {"code": "USD"})
    dto["checkout_url"] = ""
    if "redirect_urls" in cart:
        dto["checkout_url"] = cart["redirect_urls"].get("checkout_url", "")
    return dto


def _add_sku_quantity(cart, line_items):
    cart_line_items = cart['line_items']
    cart_products = cart['products']

    for line_item in line_items:
        sku = line_item['sku']
        product_id = str(line_item['product_id'])
        cart_sku = cart_line_items.get(sku, None)
        cart_product = cart_products.get(product_id, None)
        total_product_quantity = 0

        if cart_product:
            total_product_quantity = cart_product.get('total_quantity', 0)
        else:
            cart_product = {
                "total_quantity": total_product_quantity, "sku": {}}
            cart_products[product_id] = cart_product

        total_product_quantity = total_product_quantity + line_item['quantity']

        if cart_sku:
            cart_sku["quantity"] = cart_sku["quantity"] + line_item["quantity"]
        else:
            cart_sku = line_item

        cart_product["sku"][sku] = True
        cart_product["total_quantity"] = total_product_quantity
        cart_line_items[sku] = cart_sku
        cart_products[product_id] = cart_product

    return cart


def _validate_inventory(store, cart, line_items):
    result = []
    cart_line_items = cart['line_items']
    skus = []
    product_id = None

    for line_item in line_items:
        product_id = line_item["product_id"]
        skus.append(line_item['sku'])

    product_data = _get_inventory(store, product_id, skus)
    sku_invetory = product_data["invetory"]
    for idx, line_item in enumerate(line_items):
        sku = line_item['sku']
        cart_line_item = cart_line_items[sku]
        cart_quantity = cart_line_item['quantity']
        variant_data = sku_invetory[idx]
        inventory = variant_data.get("inventory", 0)
        if cart_quantity > inventory:
            result.append({
                "sku": sku,
                "product_id": line_item["product_id"],
                "variant_id": line_item["variant_id"],
                "invetory": inventory,
                "quantity": cart_quantity
            })
        else:
            cart_line_item["options"] = variant_data["options"]
            cart_line_item["name"] = product_data["name"]
            cart_line_item["url"] = product_data["url"]
            cart_line_item["image_url"] = product_data["image_url"]

    return result


def _update_sku_price(store, customer_id, cart, line_items):
    cart_line_items = cart['line_items']
    cart_products = cart['products']

    product_id = str(line_items[0]['product_id'])
    product_total_quantity = cart_products[product_id]['total_quantity']
    sku_list = []
    for line_item in line_items:
        sku_list.append(line_item['sku'])

    sku_pricing = product_util.get_price_single_product(
        store, customer_id, product_id, product_total_quantity, sku_list)

    for line_item in line_items:
        sku = line_item['sku']
        price = sku_pricing[sku]
        cart_line_items[sku]['price'] = price

    return cart


def add_cart_line_items_sync(store, cart, line_items):
    bc_cart_id = cart.get("bc_cart_id", None)
    customer_id = cart.get("customer_id")
    res = bc_cart.add_line_items_to_bc_cart(
        store, customer_id, bc_cart_id, line_items)
    # print("add_cart_line_items_sync", res.status_code)

    sku_id = {}
    if res.status_code < 299:
        bc_cart_obj = res.json()["data"]
        for bc_cart_line_item in bc_cart_obj['line_items']['physical_items']:
            cart_line_item = cart['line_items'][bc_cart_line_item['sku']]
            if bc_cart_line_item['product_id'] == cart_line_item['product_id'] and bc_cart_line_item['variant_id'] == cart_line_item['variant_id']:
                sku_id[bc_cart_line_item['sku']] = bc_cart_line_item['id']

        cart_db.update_bc_cart(store, cart, bc_cart_obj, sku_id)


def _update_bc_cart(store, cart, line_items):
    # cart_line_items = cart['line_items']
    # cart_products = cart['products']
    # bc_cart_id = cart["bc_cart_id"]
    # new_line_items = []
    # existing_line_items = []
    # for line_item in line_items:
    #     sku = line_item['sku']
    #     cart_line_item = cart_line_items.get(sku)
    #     bc_id = cart_line_item.get("bc_id", None)
    #     if bc_id:
    #         existing_line_items.append(cart_line_item)
    #     else:
    #         new_line_items.append(cart_line_item)

    # updated_line_items = []

    # if len(new_line_items) > 0:
    #     cart = bc_cart.add_line_items(store, cart)
    #     #updated_line_items.extend(new_line_items)
    #     #cart["bc_cart_id"] = bc_cart_id

    # if len(existing_line_items) > 0:
    #     cart = bc_cart.update_line_items(store, bc_cart_id, existing_line_items)
    # updated_line_items.extend(existing_line_items)

    # for _line_item in updated_line_items:
    #     cart_line_items[_line_item['sku']] = _line_item

    import task
    # print("Adding add_cart_line_items ....................")
    task.add_cart_line_items.apply_async(args=[store, cart, line_items])

    # bc_cart_id = cart.get("bc_cart_id", None)
    # if bc_cart_id:
    #     import task
    #     print("Adding add_cart_line_items ....................")
    #     task.add_cart_line_items.apply_async(args=[store, cart, line_items])
    # else:
    #     customer_id = cart.get("customer_id")
    #     res = bc_cart.add_line_items_to_bc_cart(store, customer_id, bc_cart_id, line_items)
    #     if res.status_code < 299:
    #         cart['bc_cart_id'] = res.json()['data']['id']
    cart_db.update_cart(store, cart)
    return _model_to_dto(store, cart)


def _update_sku_quantity(cart, line_items):
    cart_line_items = cart['line_items']
    cart_products = cart['products']
    deleted_line_items = []
    for line_item in line_items:
        sku = line_item['sku']
        product_id = str(line_item['product_id'])
        quantity = line_item["quantity"]

        if quantity < 0:
            return False, deleted_line_items

        cart_sku = cart_line_items.get(sku, None)
        cart_product = cart_products.get(product_id, None)

        if cart_sku:
            existing_quantity = cart_sku["quantity"]
            diff = line_item['quantity'] - existing_quantity
            cart_product['total_quantity'] = cart_product['total_quantity'] + diff
            cart_sku["quantity"] = line_item['quantity']
            if cart_sku["quantity"] == 0:
                del cart_line_items[sku]
                del cart_product["sku"][sku]
                deleted_line_items.append(sku)
            if cart_product['total_quantity'] == 0:
                del cart_products[product_id]
        else:
            return False, deleted_line_items

    return True, deleted_line_items


def get_bc_cart(store, cart):
    result = None
    res = bc_cart.fetch_cart(store, cart['bc_cart_id'])
    if res.status_code < 299:
        data = res.json()['data']
        data = process_bc_cart(store, cart, data)
        result = _model_to_dto(store, data)
    return result


def get_customer_active_cart(store, customer_id):
    result = None
    cart = cart_db.fetch_customer_active_cart(store, customer_id)
    if cart:
        if cart['bc_cart_id']:
            result = get_bc_cart(store, cart)
        if not result:
            result = _model_to_dto(store, cart)
    else:
        cart_id = cart_db.create_new_cart(store, customer_id)
        cart = cart_db.fetch_customer_active_cart(store, customer_id)
        result = _model_to_dto(store, cart)
    return result


def get_customer_cart_summary(store, customer_id):
    return cart_db.fetch_customer_active_cart_summary(store, customer_id)


def get_customer_active_cart_id(store, customer_id):
    cart = get_customer_active_cart(store, customer_id)
    cart_id = None

    if not cart:
        cart_id = cart_db.create_new_cart(store, customer_id)
    else:
        cart_id = cart['id']

    return str(cart_id)


def add_line_items(store, customer_id, line_items):
    if line_items and len(line_items) > 0:
        result = {}
        cart = cart_db.fetch_customer_active_cart(store, customer_id)
        if cart:
            cart_id = str(cart['id'])
            cart = _add_sku_quantity(cart, line_items)
            validation_result = _validate_inventory(store, cart, line_items)
            if len(validation_result) > 0:
                result["status"] = 409
                result["data"] = validation_result
            else:
                cart = _update_sku_price(store, customer_id, cart, line_items)
                cart = _update_bc_cart(store, cart, line_items)
                result["status"] = 200
                result["data"] = cart
        else:
            result["status"] = 404
            result["data"] = "Invalid cart id"
    else:
        result["status"] = 400
        result["data"] = "Line items must not be null or empty."

    return result


def update_line_items(store, customer_id, line_items):
    if line_items and len(line_items) > 0:
        result = {}
        cart = cart_db.fetch_customer_active_cart(store, customer_id)
        if cart:
            cart_id = cart['id']
            flag, deleted_line_items = _update_sku_quantity(cart, line_items)
            if flag:
                line_items = [
                    line_item for line_item in line_items if line_item['quantity'] > 0]
                if len(line_items) > 0:
                    validation_result = _validate_inventory(
                        store, cart, line_items)
                    if len(validation_result) > 0:
                        result["status"] = 409
                        result["data"] = validation_result
                    else:
                        cart = _update_sku_price(
                            store, customer_id, cart, line_items)
                        cart = _update_bc_cart(store, cart, line_items)
                        result["status"] = 200
                        result["data"] = cart
                else:
                    cart = _update_bc_cart(store, cart, line_items)
                    result["status"] = 200
                    result["data"] = cart
            else:
                result["status"] = 400
                result["data"] = "Provide valid line item."
        else:
            result["status"] = 404
            result["data"] = "Invalid cart id"
    else:
        result["status"] = 400
        result["data"] = "Line items must not be null or empty."

    return result


def delete_all_carts(store):
    cart_db.delete_all_carts(store)


def clear_cart(store, customer_id):
    cart = cart_db.fetch_customer_active_cart(store, customer_id)
    if cart:
        cart_id = cart['id']
        updated_cart = cart_db.get_cart_model(customer_id=cart["customer_id"])
        updated_cart['id'] = cart_id
        bc_cart_id = cart.get("bc_cart_id", None)
        if bc_cart_id:
            redis_util.queue_cart_delete(store["id"], bc_cart_id)
        cart = cart_db.update_cart(store, updated_cart)
        cart = _model_to_dto(store, cart)
    return cart


def delete_all_bc_cart(store):
    carts = redis_util.dequeue_cart_delete(store["id"])
    for cart_id in carts:
        bc_cart.delete_cart(store, cart_id)


def checkout(store, customer_id):
    cart = cart_db.fetch_customer_active_cart(store, customer_id)
    result = None
    if cart:
        cart_id = cart['id']
        sku_inventory = _get_current_inventory(store, cart)
        # print(sku_inventory)
        valid_line_items = []
        invalid_line_items = []
        for sku, line_item in cart["line_items"].items():
            current_inventory = sku_inventory[sku]
            if 'bc_id' in line_item:
                del line_item['bc_id']
            if current_inventory > line_item["quantity"]:
                valid_line_items.append(line_item)
            else:
                line_item['inventory'] = current_inventory
                invalid_line_items.append(line_item)

        if len(valid_line_items) > 0:
            existing_cart_id = cart['bc_cart_id']
            if not existing_cart_id:
                res = bc_cart.create_bc_cart(store, cart)
                if res.status_code < 299:
                    new_cart = res.json()['data']
                    cart = bc_cart.process_bc_cart_data(cart, new_cart)
                    cart_db.update_cart(store, cart)
                # if existing_cart_id:
                #     redis_util.queue_cart_delete(store["id"], existing_cart_id)
                #     cart['bc_cart_id'] = None
            result = _model_to_dto(store, cart)
        if len(invalid_line_items) > 0:
            # cart['invalid_line_items'] = invalid_line_items
            result["invalid_line_items"] = invalid_line_items
    return result


def process_bc_cart(store, local_cart, bc_cart):
    local_cart['bc_cart_id'] = bc_cart['id']
    local_cart['base_amount'] = bc_cart['base_amount']
    local_cart['cart_amount'] = bc_cart['cart_amount']
    local_cart['tax_included'] = bc_cart['tax_included']
    local_cart['discount_amount'] = bc_cart['discount_amount']
    local_cart['coupons'] = bc_cart['coupons']
    local_cart['discounts'] = bc_cart['discounts']
    local_cart['discounts'] = bc_cart['discounts']
    local_cart['gift_certificates'] = bc_cart['line_items']['gift_certificates']
    local_cart['currency'] = bc_cart['currency']
    products = {}
    line_item_bc_id = {}
    line_items = {}

    for line_item in bc_cart['line_items']['physical_items']:
        line_items[line_item['sku']] = line_item
        line_item_bc_id[line_item['sku']] = line_item['id']
        product = products.get(str(line_item['product_id']), {})
        product_sku = product.get('sku', {})
        total_quantity = product.get("total_quantity", 0)
        product['total_quantity'] = total_quantity + line_item['quantity']
        product_sku[line_item['sku']] = True
        product['sku'] = product_sku
        products[str(line_item['product_id'])] = product

    local_cart['products'] = products
    local_cart['line_items'] = line_items
    local_cart['line_item_bc_id'] = line_item_bc_id
    redirect_urls = bc_cart.get("redirect_urls", None)
    if redirect_urls:
        local_cart['redirect_urls'] = redirect_urls

    return local_cart


def update_cart_line_item(store, customer_id, line_item):
    cart = cart_db.fetch_customer_active_cart(store, customer_id)
    result = {
        'payload': {},
        'status': 500
    }
    if cart:
        cart_id = str(cart['id'])
        line_item_id = cart['line_item_bc_id'].get(line_item['sku'], None)
        local_line_item = cart['line_items'].get(line_item['sku'], None)
        if local_line_item and line_item_id:
            inventory = _get_current_sku_inventory(store, [line_item['sku']])
            if inventory[line_item['sku']] > line_item['quantity']:
                existing_quantity = local_line_item['quantity']
                diff = line_item['quantity'] - existing_quantity
                local_line_item['quantity'] = line_item['quantity']
                product = cart['products'].get(
                    str(local_line_item['product_id']), None)
                product['total_quantity'] = product['total_quantity'] + diff
                line_item['id'] = line_item_id
                res = bc_cart.update_line_item(
                    store, str(cart['bc_cart_id']), line_item)
                if res.status_code < 299:
                    if line_item['quantity'] == 0 and res.status_code == 204:
                        result['status'] = 200
                        result['payload']['data'] = clear_cart(
                            store, cart['customer_id'])
                    else:
                        data = res.json()['data']
                        # print(data)
                        data = process_bc_cart(store, cart, data)
                        cart_db.update_cart(store, data)
                        data = _model_to_dto(store, data)
                        result['status'] = 200
                        result['payload']['data'] = data
                else:
                    result['status'] = res.status_code
                    result['payload']['message'] = res.json()
            else:
                result['status'] = 409
                result['payload']['message'] = "quantity is more than current inventory level"
        else:
            result['status'] = 404
            result['payload']['message'] = "Line item doesn't exist."
    else:
        result['status'] = 404
        result['payload']['message'] = "Cart doesn't exist."
    return result


def remove_coupon(store, customer_id, coupon_code):
    cart = cart_db.fetch_customer_active_cart(store, customer_id)
    result = {
        "payload": {},
        "status": 500
    }
    if cart:
        cart_id = cart['id']
        res = bc_cart.remove_coupon(store, cart['bc_cart_id'], coupon_code)
        if res.status_code < 299:
            result['payload']['data'] = get_bc_cart(store, cart)
            result['status'] = 200
        else:
            result['status'] = res.status_code
            result['payload']['message'] = res.json()
    else:
        result["status"] = 404
        result["payload"]["message"] = "Cart doesn't exist."

    return result


def add_coupon(store, customer_id, coupon_code):
    cart = cart_db.fetch_customer_active_cart(store, customer_id)
    result = {
        "payload": {},
        "status": 500
    }
    if cart:
        cart_id = cart['id']
        res = bc_cart.add_coupon(store, cart['bc_cart_id'], coupon_code)
        if res.status_code < 299:
            # remove existing coupon
            # res = bc_cart.add_coupon(store, cart['bc_cart_id'], coupon_code)
            result['payload']['data'] = get_bc_cart(store, cart)
            result['status'] = 200
        else:
            result['status'] = res.status_code
            result['payload']['message'] = res.json()
    else:
        result["status"] = 404
        result["payload"]["message"] = "Cart doesn't exist."

    return result


def on_order_created(store, payload):
    order_id = payload['data']['id']
    res = bc_order.fetch_order(store, order_id)
    if res.status_code < 299:
        order = res.json()
        customer_id = order['customer_id']
        cart_id = order['cart_id']
        cart = cart_db.fetch_cart_by_bc_cart_id_and_customer_id(
            store, customer_id, cart_id)
        order['_id'] = order['id']
        order_db.insert_order(store, order)
        process_customer_points_update(
            store, order['total_inc_tax'], customer_id)
        if 'coupons' in order:
            if 'resource' in order['coupons']:
                url_value = order['coupons']['resource']
                url_value = 'v2' + url_value                
                coupons_res = bc_order.fetch_order_coupon(store, url_value)                
                if coupons_res.status_code == 200:
                    coupons_array = coupons_res.json()                    
                    for coupon in coupons_array:
                        if 'coupon_id' in coupon:
                            coupon_id_value = coupon['coupon_id']
                            c_data = {}
                            c_data['coupon_id'] = coupon_id_value
                            c_data['customer_id'] = customer_id
                            customer_db.update_customer_coupon_status(store, c_data)

        if cart:
            current_time = int(datetime.datetime.utcnow().timestamp())
            cart["modified_at"] = current_time
            cart['order_id'] = order_id
            cart['status'] = "archieved"
            if "_id" in cart:
                del cart["_id"]
            cart_db.create_cart(store, cart)
            cart_db.delete_cart(store, cart['id'])
            cart_db.create_new_cart(store, customer_id)


def process_order_created(store, payload):
    on_order_created(store, payload)


def process_customer_points_update(store, order_amount, customer_id):
    total_amount = float(order_amount)
    total_amount = round(total_amount)
    points = total_amount * 2
    customer_res = customer_db.fetch_customer_by_id(store, customer_id)
    if customer_res:
        old_points = int(customer_res['loyalty_points']) if customer_res.__contains__("loyalty_points") else 0
        add_loyalty_reward_history(store, old_points, points, customer_id)
        customer_res['loyalty_points'] = (
            int(customer_res['loyalty_points']) if customer_res.__contains__("loyalty_points") else 0) + int(points)
        customer_db.update_customer_by_id(store, customer_res)

def add_loyalty_reward_history(store, old_points, new_points, customer_id):   
    data = {}
    data['customer_id'] = int(customer_id)
    data['balance'] = int(old_points) + int(new_points)
    data['earned/used'] = int(new_points)
    data['created_at'] = int(datetime.datetime.utcnow().timestamp())
    data['description'] =  'Earned ' + str(new_points) + ' Points with order.'
    customer_db.add_customer_loyalty_history(store, data)
