from flask import request
import logging
import traceback
from api import APIResource
from new_mongodb import store_admin_db
from orders.view_orders import orders_list
from utils import bc, store_util
from orders.view_orders import blocked_orders
from orders import order_audit_report
from new_pgdb import blocked_orders_db

logger = logging.getLogger()

class Orders(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Orders GET")
        try:
            query_params = request.args.to_dict()
            res = orders_list._get_all_orders(store, query_params)
            if res['status'] == 200:                
                return res['data'], 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Orders GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class Order(APIResource):
    def get_executor(self, request, token_payload, store, order_id):
        logger.debug("Entering Order Details GET")
        try:
            if not order_id:
                return {"message": "Please enter order_id as a query params in request"}, 400
            res = orders_list.get_order(store, order_id)
            return res, 200
        finally:
            logger.debug("Exiting Order Details GET")

    def get(self, order_id):
        return self.execute_store_request(request, self.get_executor, order_id)

class BCOrderShippingAddress(APIResource):
    def get_executor(self, request, token_payload, store, order_id):
        logger.debug("Entering BC Order Shipping Address GET")
        try:
            api = store_util.get_bc_api_creds(store)
            res = bc.get_bc_order_shipping_address(api, order_id, {})

            # Return response ...
            return res, 200
        finally:
            logger.debug("Exiting BC Order Shipping Address GET")
     
    def get(self, order_id):
        return self.execute_store_request(request, self.get_executor, order_id)

class BCOrderDetails(APIResource):
    def get_executor(self, request, token_payload, store, order_id):
        logger.debug("Entering BC Order Details GET")
        try:
            payload = request.args.to_dict()
            res = orders_list.get_bc_order_details(store, order_id, payload)

            # Return response ...
            return res, 200
        finally:
            logger.debug("Exiting BC Order Details GET")
     
    def get(self, order_id):
        return self.execute_store_request(request, self.get_executor, order_id)
    
class CustomerOrderDetails(APIResource):
    def get_executor(self, request, token_payload, store, customer_id):
        logger.debug("Entering Customer Order Details GET")
        try:
            payload = request.args.to_dict()
            res = orders_list.get_all_customer_orders(store, customer_id, payload)
            return res['data'], 200
        finally:
            logger.debug("Exiting Customer Order Details GET")
     
    def get(self, customer_id):
        return self.execute_store_request(request, self.get_executor, customer_id)


class BCOrderProducts(APIResource):
    def get_executor(self, request, token_payload, store, cart_id):
        logger.debug("Entering BC Order Product Shipping Address GET")
        try:
            api = store_util.get_bc_api_creds(store)
            res = bc.get_bc_cart_products(api, cart_id)
            return res, 200
        finally:
            logger.debug("Exiting BC Order Product GET")
     
    def get(self, cart_id):
        return self.execute_store_request(request, self.get_executor, cart_id)


class BCOrderProductsListing(APIResource):
    def get_executor(self, request, token_payload, store, order_id):
        logger.debug("Entering BC Order Products Shipping Address GET")
        try:
            query_params = request.args.to_dict()
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            if user is None:
                return {"message": "Unauthorized access."}, 401
            price_list_id = query_params.get("price_list_id", None)
            new_products = query_params.get("new_products", None)
            # new_products = None
            api = store_util.get_bc_api_creds(store)
            res = bc.get_bc_order_products(api, order_id,store, price_list_id, user, new_products)
            response = orders_list.get_order_products_processed_data(store, res)

            # Return response ...
            return response, response['status']
        finally:
            logger.debug("Exiting BC Order Products GET")
     
    def get(self, order_id):
        return self.execute_store_request(request, self.get_executor, order_id)
    
class OrderConsignment(APIResource):
    def put_executor(self, request, token_payload, store):
        logger.debug("Entering BC Order Consignment PUT")
        try:
            payload = request.get_json(force=True)
            if payload:
                username = token_payload['username']
                result = orders_list.get_bc_order_details(store, str(payload['orderId']), payload, username)
                
                return result
            return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting PUT Order Consignment")

    def put(self):
        return self.execute_store_request(request, self.put_executor)   

class OrdersPriceList(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Orders Price List GET")
        try:
            result, status = orders_list.get_price_list(store)
            
            if status == 200:
                return result, 200
                
            return {"error": "Invalid request."}, 400

        finally:
            logger.debug("Exiting Orders Price List GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor) 

class OrderPriceAudit(APIResource):
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Order Price Audit POST")
        try:
            payload = request.get_json(force=True)
            order_id = payload.get('order_id', None)
            if order_id:
                username = token_payload['username']
                if not username:
                    return {"message": "Unauthorized access."}, 401
                result = order_audit_report.update_order_price_audit(store, str(order_id), username)
                if result['status'] == 200:
                    return {"message": result['message']}, 200
                else:
                    return {"message": result['message']}, result['status']
            return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Order Price Audit POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)
            
class OrderPriceAuditReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Order Price Audit report Report GET")
        try:
            query_params = request.args.to_dict()
            search = query_params.get('search', None)
            page = query_params.get('page', None)
            limit = query_params.get('limit', None)
            sort_by = query_params.get('sort_by', None)
            sort_array = sort_by.split("/") if sort_by != '' else []
            sales_rep = query_params.get('sales_rep', None)
            is_archived = query_params.get('is_archived', '')
            username = token_payload['username']
            if not username:
                return {"message": "Unauthorized access."}, 401
            result = order_audit_report.get_order_price_audit_report(store['id'], search, page, limit, sales_rep, sort_array, is_archived)
            if result['status'] == 200:
                return result['data'], 200
        finally:
            logger.debug("Exiting Order Price Audit POST")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
            
            
class BlockedOrders(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Blocked Orders PUT")
        
        try:
            query_params = request.args.to_dict()

            limit = int(query_params.get("limit", 100)) 
            page = int(query_params.get("page", 1))

            if query_params:
                result, status = blocked_orders.get_blocked_orders(store, limit, page)
                
                if status == 200:
                    return result, 200
                
            return {"error": "Invalid request."}, 400
        
        finally:
            logger.debug("Exiting GET Order Consignment")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
   

class BlockedOrdersOperations(APIResource):
    def delete_executor(self, request, token_payload, store, order_id):
        logger.debug("Entering Blocked Orders PUT")
        try:            
            if order_id:
                result = blocked_orders_db.clean_up_deleted_blocked_order(store, order_id)

                if result['status'] == 200:
                    return result, 200
                
            return {"error": "Invalid request."}, 400
        
        finally:
            logger.debug("Exiting GET Order Consignment")

    def delete(self, order_id):
        return self.execute_store_request(request, self.delete_executor, order_id)
    

class BlockedOrdersDetailProducts(APIResource):
    def get_executor(self, request, token_payload, store, order_id):
        logger.debug("Entering Blocked Order Details GET")
        
        try:

            if order_id:
                result = blocked_orders.get_blocked_order_products(store, order_id)
                
                if result['status'] == 200:
                    return result, 200
                
            return {"error": "Invalid request."}, 400
        
        finally:
            logger.debug("Exiting GET Blocked Order Details")

    def patch_executor(self, request, token_payload, store, order_id):
        logger.debug("Entering Blocked Order Details PATCH")
        
        try:
            payload = request.get_json(force=True)

            if order_id:
                payload = request.get_json(force=True)
                username = token_payload['username']
                result = blocked_orders.update_blocked_order(store, order_id, payload, "PUT", username)
                
                if result['status'] == 200:
                    return result, 200
                else:
                    return {"message": result['message']}, result['status']
                
            return {"error": "Invalid request."}, 400
        
        finally:
            logger.debug("Exiting PATCH Blocked Order Details")

    def delete_executor(self, request, token_payload, store, order_id):
        logger.debug("Entering Blocked Order Details DELETE")
        
        try:
            
            payload = request.get_json(force=True)
            
            if order_id:
                username = token_payload['username']
                result = blocked_orders.update_blocked_order(store, order_id, payload, "DELETE", username)
                
                if result['status'] == 200:
                    return result, 200
                
            return {"error": "Invalid request."}, 400
        
        finally:
            logger.debug("Exiting DELETE Blocked Order Details")

    def post_executor(self, request, token_payload, store, order_id):
        logger.debug("Entering Blocked Order Details POST")
        
        try:
            payload = request.get_json(force=True)

            if order_id:
                payload = request.get_json(force=True)
                username = token_payload['username']
                
                result = blocked_orders.update_blocked_order(store, order_id, payload, "POST", username)
                
                if result['status'] == 200:
                    return result, 200
                else:
                    return result, 400
            return {"error": "Invalid request."}, 400
        
        finally:
            logger.debug("Exiting POST Blocked Order Details")

    def post(self, order_id):
        return self.execute_store_request(request, self.post_executor, order_id)
    
    def get(self, order_id):
        return self.execute_store_request(request, self.get_executor, order_id)
    
    def patch(self, order_id):
        return self.execute_store_request(request, self.patch_executor, order_id)
    
    def delete(self, order_id):
        return self.execute_store_request(request, self.delete_executor, order_id)

class BlockedOrderLogs(APIResource):
    def get_executor(self, request, token_payload, store, order_id):
        logger.debug("Entering Blocked Order Details GET")
        
        try:
            if order_id:
                query_params = request.args.to_dict()
                page = int(query_params.get('page', 1))
                limit = int(query_params.get('limit', 10))
                search_term = query_params.get('search_term', '')
                sort_by = query_params.get('sort_by', '').strip()
                sort_array = sort_by.split("/") if sort_by != '' else []

                result = blocked_orders.get_blocked_order_logs(store, order_id, page, limit, search_term, sort_array)    
                
                if result['status'] == 200:
                    return result['data'], 200
                
            return {"error": "Invalid request."}, 400
        
        finally:
            logger.debug("Exiting GET Blocked Order Details")

    def get(self, order_id):
        return self.execute_store_request(request, self.get_executor, order_id)


class OrderUnitPrices(APIResource):
    def post_executor(self, request, token_payload, store, order_id):
        logger.debug("Entering Order unit price POST")
        
        try:
            payload = request.get_json(force=True)

            if order_id:
                payload = request.get_json(force=True)
                username = token_payload['username']
                user = None
                if username and username != '':
                    user = store_admin_db.fetch_user_by_username(store['id'], username)
                if user is None:
                    return {"message": "Unauthorized access."}, 401
                name = user.get('name')
                
                result = blocked_orders.update_blocked_order(store, order_id, payload, "POST", username, call_from_unit_price=True, name=name)
                
                if result['status'] == 200:
                    return result, 200
                else:
                    return result, result['status']
            return {"error": "Invalid request."}, 400
        
        finally:
            logger.debug("Exiting POST Order unit price")


    def put_executor(self, request, token_payload, store, order_id):
        logger.debug("Entering Order unit price PUT")
        
        try:
            payload = request.get_json(force=True)
            if order_id:
                payload = request.get_json(force=True)
                username = token_payload['username']
                user = None
                if username and username != '':
                    user = store_admin_db.fetch_user_by_username(store['id'], username)
                if user is None:
                    return {"message": "Unauthorized access."}, 401
                name = user.get('name')
                
                result = orders_list.update_unit_prices(store, order_id, payload, name)
                
                if result['status'] == 200:
                    return result, 200
                else:
                    return result, 400
            return {"error": "Invalid request."}, 400
        
        finally:
            logger.debug("Exiting PUT Order unit price")

    def delete_executor(self, request, token_payload, store, order_id):
        logger.debug("Entering Order unit price DELETE")
        
        try:
            payload = request.get_json(force=True)

            if order_id:
                payload = request.get_json(force=True)
                username = token_payload['username']
                user = None
                if username and username != '':
                    user = store_admin_db.fetch_user_by_username(store['id'], username)
                if user is None:
                    return {"message": "Unauthorized access."}, 401
                name = user.get('name')
                
                result = blocked_orders.update_blocked_order(store, order_id, payload, "DELETE", username, call_from_unit_price=True, name=name)
                
                if result['status'] == 200:
                    return result, 200
                else:
                    return result, 400
            return {"error": "Invalid request."}, 400
        
        finally:
            logger.debug("Exiting DELETE Order unit price")

    def delete(self, order_id):
        return self.execute_store_request(request, self.delete_executor, order_id)

    def put(self, order_id):
        return self.execute_store_request(request, self.put_executor, order_id)

    def post(self, order_id):
        return self.execute_store_request(request, self.post_executor, order_id)
    
class OrderPriceAuditReportDetails(APIResource):
    def patch_executor(self, request, token_payload, store, order_id):
        logger.debug("Entering Order Price Audit Report ARCHIVE PATCH")
        try:
            username = token_payload.get('username', '')
            if not username:
                return {"message": "Unauthorized"}, 401
            payload = request.get_json(force=True)
            result = order_audit_report.update_order_audit_details(store, payload, order_id, username)
            if result['status'] == 200:
                return result['data'], 200
            else:
                return {'message': result['message']}, result['status']
        finally:
            logger.debug("Exiting Order Price Audit Report ARCHIVE PATCH")

    def patch(self, order_id):
        return self.execute_store_request(request, self.patch_executor, order_id)
    
class OrdersProductsSuggestion(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Products suggestion GET")
        try:
            search = request.args.get('search', None)
            page = request.args.get('page', 1)
            limit = request.args.get('limit', 10)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':
                if search:
                    res = orders_list.get_products_suggestion(store['id'], search, page, limit)
                    if res['status'] == 200:
                        return res['data'], res['status']
                else:
                    return {'data': []}, 200
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Products suggestion GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class OrdersProductsVariants(APIResource):
    def get_executor(self, request, token_payload, store, parent_sku):
        logger.debug("Entering Products variants GET")
        try:
            page = request.args.get('page', 1)
            limit = request.args.get('limit', 10)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':
                res = orders_list.get_product_variants(store['id'], parent_sku, page, limit)
                if res['status'] == 200:
                    return res['data'], res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Products variants GET")
    
    def get(self, parent_sku):
        return self.execute_store_request(request, self.get_executor, parent_sku)
    
class OrdersCheckLineItemAvailability(APIResource):
    def post_executor(self, request, token_payload, store, order_id):
        logger.debug("Entering Cart Lineitem Availability Check POST")
        try:
            payload = request.get_json(force=True)
            skus = payload.get('skus', None)
            res = orders_list.check_line_item_availability(store, order_id, skus)
            if res['status'] == 200:
                return {'data': res['data']}, res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Cart Lineitem Availability Check POST")
    
    def post(self, order_id):
        return self.execute_store_request(request, self.post_executor, order_id)