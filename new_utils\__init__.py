from flask import json
from bson import json_util, ObjectId
from pymongo.collation import Collation
import datetime
import new_mongodb

def format_price(price):
    if abs(price) >= 1000:
        formatted_price = "{:.1f}k".format(price / 1000)
    else:
        formatted_price = "{:.2f}".format(price)
    return formatted_price

# parse json data ...
def parse_json(data):
    return json.loads(json_util.dumps(data))

def _process_document(obj):
    if obj:
        if 'id' in obj:
            obj['bc_id']=obj['id']
        if '_id' in obj:
            obj['id'] = str(obj['_id'])
            del obj['_id']

        for key, value in obj.items():
            if isinstance(value, ObjectId):
                obj[key] = str(value)
            elif isinstance(value, datetime.datetime):
                obj[key] = int(value.timestamp())
    return obj

def _process_list(data):
    result = []
    if data:
        for _obj in data:
            result.append(_process_document(_obj))
    return result

def _create_reg_ex_query(payload, filterBy, filter, additionalQuery):
    query = {
        "$or": [],
    }

    for i in filterBy:
        query['$or'].append({i: {"$regex": filter, "$options": "i"}},)

    if "type" not in query and "type" in payload:
        query["type"] = payload["type"]

    if "status" in payload:
        status = payload.get("status", "active")
        if status == "active":
            query["is_visible"] = True
        elif status == "inactive":
            query["is_visible"] = False
        elif status == "out_of_stock":
            query["inventory_level"] = 0     

    query.update(additionalQuery)

    return query

# calculate pagination data for request ...
def calculate_pagination(data, page, limit, total_records):
    page = int(page)
    limit = int(limit)
    total_records = int(total_records)

    # Calculate total number of pages
    last_page = (total_records // limit) + \
        (1 if total_records % limit > 0 else 0)

    # Generate links to other pages (limit to max 5 links)
    base_url = "?page="
    links = []

    # Add first page link
    if page > 1:
        first_link = {
            "url": base_url + str(1),
            "label": "First",
            "active": False,
        }
        links.append(first_link)

   # Add previous page link if it exists
    if page > 1:
        prev_link = {
            "url": base_url + str(page - 1),
            "label": "Previous",
            "active": False,
        }
        links.append(prev_link)

    # Generate up to 5 page links
    if last_page <= 5:
        for p in range(1, last_page + 1):
            link = {
                "url": base_url + str(p),
                "label": str(p),
                "active": p == page,
            }
            links.append(link)
    else:
        if page <= 3:
            for p in range(1, 6):
                link = {
                    "url": base_url + str(p),
                    "label": str(p),
                    "active": p == page,
                }
                links.append(link)
        elif page > last_page - 3:
            for p in range(last_page - 4, last_page + 1):
                link = {
                    "url": base_url + str(p),
                    "label": str(p),
                    "active": p == page,
                }
                links.append(link)
        else:
            for p in range(page - 2, page + 3):
                link = {
                    "url": base_url + str(p),
                    "label": str(p),
                    "active": p == page,
                }
                links.append(link)

    # Add next page link if it exists
    if page < last_page:
        next_link = {
            "url": base_url + str(page + 1),
            "label": "Next",
            "active": False,
        }
        links.append(next_link)

    # Add first page link
    if page < last_page:
        last_link = {
            "url": base_url + str(last_page),
            "label": "Last",
            "active": False,
        }
        links.append(last_link)

    # Generate pagination object
    pagination = {
        "data": data or [],
        "meta": {
            "pagination": {} if int(last_page) == 1 else {
                "first_page_url": base_url + "1",
                "items_per_page": int(limit),
                "last_page": int(last_page),
                "first_page": int(1),
                "links": links,
                "next_page": int(page + 1) if page < last_page else None,
                "page": int(page),
                "prev_page": int(page - 1) if page > 1 else None,
                "total": int(total_records),
            }
        }
    }
    return pagination

def get_paginated_records_updated(db_client, collection_name, payload, fields, additional_query):
    sort_by = payload.get('sort_by', 'date_created')
    sort_order = payload.get('sort_order', 'asc')
    limit = int(payload.get('limit', '10'))
    page = int(payload.get('page', '1'))
    skips = int(payload.get('skips', '1'))
    filterBy = payload.get("filterBy", [])
    filter = payload.get("filter", '')

    sort = {
        'sort_by': sort_by
    }

    if sort_order == 'asc':
        sort['sort_order'] = 1
    else:
        sort['sort_order'] = -1

    query = {}
    if len(filterBy):
        query = _create_reg_ex_query(payload, filterBy, filter, additional_query)

    if collection_name == 'users':
       query['status'] = {'$in': ['active', 'inactive']}
    # Calculate number of records to skip ...
    skips = limit * (page - 1)
    collation = Collation(locale='en', strength=2)

    data = new_mongodb.fetchall_documents_from_collection(db_client, collection_name, query, fields).collation(collation).sort(sort['sort_by'], sort['sort_order']).skip(skips).limit(limit)
    # ProcessList ...
    data = _process_list(data)
    
    document_length = new_mongodb.count_documents_from_collection(db_client, collection_name, query)

    return parse_json(data), document_length, page, limit

        