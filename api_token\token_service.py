from new_mongodb import fetch_one_document_from_admin_collection, fetch_one_document_from_tenant_collection, update_document_in_admin_collection, update_document_in_tenant_collection
from bson import ObjectId
import logging
import traceback
from utils import store_util, webhook_util
from utils.webhook_util import get_webhook_listing

logger = logging.getLogger()

STORE_DB = "store"

def get_all(store_id):
  try:
    result = fetch_one_document_from_admin_collection(store_id, STORE_DB)
    extracted_data = []
    if result:
      formatted_entry = {
          "store_hash": result["bc_config"]["store_hash"],
          "channel_id": result["bc_config"]["channel_id"],
          "client_id": result["bc_config"]["api"]["client_id"],
          "access_token": result["bc_config"]["api"]["access_token"],
          "id": str(result["_id"])
      }
      extracted_data.append(formatted_entry)
    
    data = {"data": extracted_data}
    return data
  
  except Exception as e:
    return {"error": str(e)}
  
def update_data(store, payload):
    try:
        store_id = payload.get('id', None)
        admin_store = {}
        tenant_store = {}
        webhook_config = None

        if store_id:
            admin_store = fetch_one_document_from_admin_collection(store_id, STORE_DB, {"_id": ObjectId(str(store_id))})
            tenant_store = fetch_one_document_from_tenant_collection('stores', {"_id": ObjectId(str(store_id))})

        query = {"_id": ObjectId(str(store_id))}

        # Define reusable function to build the update payload
        def build_update_payload(data):
            update_payload = {"$set": {}}
            if 'bc_config' in data:
                update_payload["$set"].update({
                    "bc_config.store_hash": payload['store_hash'],
                    "bc_config.channel_id": payload['channel_id'],
                    "bc_config.api.client_id": payload['client_id'],
                    "bc_config.api.access_token": payload['access_token'],
                })
            if 'api' in data:
                update_payload["$set"].update({
                    "api.storeHash": payload['store_hash'],
                    "api.channelId": payload['channel_id'],
                    "api.bcV3Api.clientId": payload['client_id'],
                    "api.bcV3Api.accessToken": payload['access_token'],
                })
            return update_payload

        # Update admin_store
        if admin_store:
            update_payload = build_update_payload(admin_store)
            result = update_document_in_admin_collection(store_id, STORE_DB, query, update_payload)

        # Update tenant_store
        if tenant_store:
            bc_config = tenant_store.get("bc_config", {})
            webhook_config = bc_config.get("webhook_config", {})
            update_payload = build_update_payload(tenant_store)
            result = update_document_in_tenant_collection('stores', query, update_payload)
        
        # reset all the webhooks
        if webhook_config:
          # Fetch the destination and token from webhook_config
          destination = webhook_config.get("destination")
          token = webhook_config.get("token")

          store = store_util.get_store_by_id(store_id)
          webhook_data = get_webhook_listing(store)
          
          # Filter active webhooks and collect their scopes
          scopes = [
              {"id": webhook["id"], "scope": webhook["scope"]}
              for webhook in webhook_data
          ]
          webhook_payload = {
              "destination": destination,
              "token": token,
              "scopes": scopes
          }
          webhook_util.add_webhooks(store_id, webhook_payload)
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e