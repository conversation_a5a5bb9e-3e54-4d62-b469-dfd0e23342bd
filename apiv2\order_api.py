from flask import request
import logging
import traceback
from apiv2 import APIResource
from orders.view_orders import orders_list
from utils import store_util

logger = logging.getLogger()

class OrderV2(APIResource):
    def get_executor(self, request, token_payload, order_id):
        logger.debug("Entering Order Details GET")
        try:
            if not order_id:
                return {"message": "Please enter order_id as a query params in request"}, 400
            store_id = token_payload.get("store_id", None)
            if store_id:
                store = store_util.get_store_by_id(store_id)
                if store:
                    res = orders_list.get_order(store, order_id)
                    return res, 200
            return {"message": "Invalid request."}, 409
        finally:
            logger.debug("Exiting Order Details GET")

    def get(self, order_id):
        return self.execute_request(request, self.get_executor, order_id)