
from services import Service
from bson import ObjectId
from utils.common import parse_json

class Permissions(Service):
    def __init__(self, repository):
        super().__init__(repository)
    
    def get_permission_by_store_id(self, store):
        store_id = store['id']
        res = super().find_one({"store_id": store_id, "status": "active"})        

        if res == None:
            return {}
        else:
            return parse_json(res)

    def create_permissions(self, store, data):
        data['store_id'] = store['id']
        
        # check if permission is already exits in DB.
        permission = self.get_permission_by_store_id(store)

        if permission == None:
            id = super().create(data)
            if id:
                return {"status": "success" }, 201
        else:
            return {'message': '1 permission object already attched with the store name: ' + store['name']}, 400

    def update_permissions(self, id, data):
        updated_data = {
            "permissions": data['permissions']
        }
        id = super().update_one({"_id": ObjectId(id)}, {"$set": updated_data})
        return id

    def delete_by_id(self, id):
        return super().delete({"_id": ObjectId(id)})
    
    def get_all_permissions(self):
        permissions = super().find_all()        
        return super().processList(permissions)

