import new_pgdb
from sqlalchemy import text
import pg_db
from mongo_db import user_db
from datetime import datetime
from flask import request
import psycopg2.errors as errors
from utils.common import calculatePaginationData, convert_to_timestamp
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import logging
import traceback

logger = logging.getLogger()

def _fetch_project_detail(conn, id, username):    
    query = text (f"""SELECT p.id, p.name, p.bu_id, p.description, p.owner_username, p.due_date, p.is_archived, p.updated_by, p.created_at, p.updated_at, p.project_initials, p.project_type, pptm.pipeline_db_tables_id, pptm.ticket_name, pptm.default_assignee, pptm.db_table_column, pdt.table_name, pdt.title FROM {pg_db.projects} p LEFT JOIN {pg_db.pipeline_project_table_mapping} pptm ON p.id = pptm.project_id LEFT JOIN  {pg_db.pipeline_db_tables} pdt ON pptm.pipeline_db_tables_id = pdt.id WHERE p.id = :id;""")

    query = query.params(id=id)
    result = conn.execute(query).fetchone()

    column_query = text (f"""SELECT id FROM {pg_db.project_columns} pc WHERE is_resolved = true AND project_id = :id;""")
    query = column_query.params(id=id)
    column_result = conn.execute(query).fetchone()

    data = []
    if result:         
        row_data = {
            'id': result[0],
            'name': result[1],
            'bu_id': result[2],            
            'description': result[3],
            'owner_username': result[4],
            'due_date': result[5],
            'is_archived': result[6],
            'updated_by': result[7],
            'created_date': convert_to_timestamp(result[8]),
            'modified_at': convert_to_timestamp(result[9]),
            'project_initials': result[10],
            'is_owner': True if username == result[4] else False,
            'status': 'active' if not result[6] else 'archived',
            'resolved_column_id': column_result[0] if column_result else None
        }
        mapping = {
            'project_type': result[11] if result[11] else 'standard',
            'table_id': result[12],
            'ticket_name': result[13],
            'default_assignee': result[14],
            'column_key': result[15],
            'db_table_name': result[16],
            'title': result[17]
        }
        row_data['project_mapping'] = mapping
        data.append(row_data)
    return data

def get_project_detail(id, username):
    response = {
        "status": 400       
    }
    conn = pg_db.get_connection()
    try:
        data = _fetch_project_detail(conn, id, username)   
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['message'] = "No data found."
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
    finally:
        if conn:
            conn.close()
    return response

def update_project(payload, username, id):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        if not id:
            response['status'] = 400
            response['message'] = "project_id: Invalid payload, 'project_id' is missing."
            return response

        existing_project = _fetch_project_detail(conn, id, username)
        if not existing_project:
            response['status'] = 404
            response['message'] = "Project not found."
            return response

        existing_project = existing_project[0]

        if 'name' in payload:
            project_name = payload.get('name', '')
            unique_query = text (f"""SELECT * from {pg_db.projects} where LOWER(name) = LOWER(:name) and id != :id;""")
            unique_query = unique_query.params(name=project_name, id=id)
            project_result = conn.execute(unique_query)
            if project_result.rowcount > 0:
                response['status'] = 409
                response['message'] = "name: The Project name with the same name already exists."
                return response

        # Update fields if provided in payload
        update_fields = {}
        for field in ['name', 'description', 'bu_id', 'is_archived', 'project_type', 'status_module_table_id']:
            if field in payload:
                update_fields[field] = payload[field]

        # Update the project with the modified data
        data = _update_project_detail(conn, update_fields, id, username)

        if data:
            response['status'] = 200
            response['message'] = "Data updated successfully."
        else:
            response['status'] = 400
            response['message'] = "Data updation failed."
    except IntegrityError as e:
        logger.error(traceback.format_exc())
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 409
            response['message'] = "Duplicate key violation: This project already exists."
        else:
            response['status'] = 500
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response


def _update_project_detail(conn, update_fields, id, username):
    set_clause = ", ".join([f"{field} = :{field}" for field in update_fields])
    query = text(
        f"""UPDATE {pg_db.projects}
            SET 
                {set_clause},
                updated_by = :updated_by,
                updated_at = CURRENT_TIMESTAMP
            WHERE 
                id = :id;"""
    )

    params = update_fields.copy()
    params.update({'updated_by': username, 'id': id})
    result = conn.execute(query, params)    
    return result.rowcount > 0

def delete_project(store_id, username, id):
    response = {
        "status": 400
    }
    if not id:
        response['message'] = "Invalid payload, 'project_id' is missing."
        return response
    conn = new_pgdb.get_connection(store_id)
    try:
        conn.begin()
        query = text(f"SELECT 1 FROM {pg_db.projects} WHERE id = :id;")
        project_check = conn.execute(query, {'id': id}).fetchone()

        if project_check:
            # Bulk delete related data using single queries for each table
            delete_queries = [
                f"""DELETE FROM agile_card_comments acc USING {pg_db.project_cards} apc WHERE acc.card_id = apc.id AND apc.project_id = :id;""",
                f"""DELETE FROM agile_project_cards_logs apcl USING {pg_db.project_cards} apc WHERE apcl.card_id = apc.id AND apc.project_id = :id;""",
                f"""DELETE FROM agile_card_tags act USING {pg_db.project_cards} apc WHERE act.card_id = apc.id AND apc.project_id = :id;""",
                f"""DELETE FROM agile_tagged_users atu USING {pg_db.project_cards} apc WHERE atu.card_id = apc.id AND apc.project_id = :id;""",
                f"""DELETE FROM agile_customfield_value acv USING {pg_db.project_cards} apc WHERE acv.card_id = apc.id AND apc.project_id = :id AND acv.project_id = :id;""",
                f"""DELETE FROM {pg_db.project_cards} apc USING {pg_db.project_columns} pc WHERE apc.current_column_id = pc.id AND apc.project_id = :id AND pc.project_id = :id;""",
                f"""DELETE FROM {pg_db.pipeline_column_mapping} pcm USING {pg_db.pipeline_project_table_mapping} pptm WHERE pcm.project_table_mapping_id = pptm.id AND pptm.project_id = :id AND pcm.project_id = :id;""",
                f"""DELETE FROM {pg_db.pipeline_custom_field_mapping} pcm USING {pg_db.pipeline_project_table_mapping} pptm WHERE pcm.project_table_mapping_id = pptm.id AND pptm.project_id = :id AND pcm.project_id = :id;""",
                f"""DELETE FROM {pg_db.pipeline_project_table_mapping} WHERE project_id = :id;""",
                f"""DELETE FROM {pg_db.project_modules} WHERE project_id = :id;""",
                f"""DELETE FROM {pg_db.project_columns} WHERE project_id = :id;""",
                f"""DELETE FROM {pg_db.agile_project_customfield} WHERE project_id = :id;""",
                f"""DELETE FROM {pg_db.agile_customfield_meta} WHERE project_id = :id;""",
                f"""DELETE FROM agile_fevorite_projects WHERE project_id = :id;""",
                f"""DELETE FROM agile_project_access WHERE project_id = :id;""",
                f"""DELETE FROM agile_project_cardindex WHERE project_id = :id;""",
            ]
            
            # Execute all delete queries in a loop
            for delete_query in delete_queries:
                conn.execute(text(delete_query), {'id': int(id)})
            
            # Finally, delete the project
            delete_project_query = text(f"DELETE FROM {pg_db.projects} WHERE id = :id;")
            conn.execute(delete_project_query, {'id': int(id)})

            conn.commit()
            response['status'] = 200
            response['message'] = "Project and related data deleted successfully."

        else:
            conn.rollback()
            response['status'] = 404
            response['message'] = "Project not found."

    except IntegrityError as e:
        logger.error(traceback.format_exc())
        conn.rollback()
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 409
            response['message'] = "Duplicate key violation: This project already exists."
        else:
            response['status'] = 500
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.close()
    return response