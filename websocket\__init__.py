from flask import request
from flask_socketio import So<PERSON><PERSON>
from new_mongodb import Client<PERSON><PERSON>Keys
from apiv2 import validate_request_token
from utils.redis_util import update_user_websocket_sid, delete_websocket_connection, get_websocket_request_sid_for_username, get_websocket_username_for_request_sid
import logging

logger = logging.getLogger()

class WebSocketClient:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            logger.info('Creating the WebSocketClient')
            cls._instance = super(WebSocketClient, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        logger.info("Entering WebSocketClient")
        self.socketio = SocketIO()
        logger.info("Exiting WebSocketClient")

    def get_socketio(self):
        return self.socketio
    
_websocket_client = WebSocketClient()
_socketio = _websocket_client.get_socketio()

def init_app(app):
    socketio = _websocket_client.get_socketio()
    socketio.init_app(app)
    return socketio

@_socketio.on("connect", namespace='/ws')
def handle_connect():
    token_payload = validate_request_token(request)
    logger.error(f"websocket: handle_connect: request sid: {request.sid}, token: {token_payload}")
    if token_payload:
        request_sid = request.sid
        username = token_payload[ClientAppsKeys.USERNAME]
        update_user_websocket_sid(username, request_sid)

@_socketio.on("disconnect", namespace='/ws')
def handle_disconnect():
    token_payload = validate_request_token(request)
    logger.error(f"websocket: handle_disconnect: request sid: {request.sid}, token: {token_payload}")
    if token_payload:
        request_sid = request.sid
        delete_websocket_connection(request_sid)

@_socketio.on("client_message", namespace='/ws')
def handle_client_message(data):
    token_payload = validate_request_token(request)
    logger.error(f"websocket: client_message: request sid: {request.sid}, token: {token_payload}")
    if token_payload:
        username = get_websocket_username_for_request_sid(request.sid)
        logger.error(f"username: {username}, data: {data}") # process data

def send_notification(data, username=None, broadcast=True):
    logger.info(f"sending notification to {username}, broadcast: {broadcast}")
    res = False
    if broadcast:
        _socketio.emit("notification", data, namespace="/ws")
        res = True
    else:
        sid = None
        if username:
            sid = get_websocket_request_sid_for_username(username)
        if sid:
            _socketio.emit("notification", data, to=sid, namespace="/ws")
            res = True
    return res

    
    

