from flask import request
import logging
import traceback
from api import APIResource
from iam import user_service
from exceptions.common_exceptions import ResourceAlreadyExistException, InvalidInputException, ResourceNotFoundException
from utils import auth_util

logger = logging.getLogger()

class AdminUsers(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering AdminUsers GET")
        try:
            query_params = request.args.to_dict()
            users = user_service.get_all_users(store, query_params)
            return users, 200
        finally:
            logger.debug("Exiting AdminUsers GET")

    def post_executor(self, request, token_payload, store):
        logger.debug("Entering AdminUsers POST")
        try:
            user = request.get_json(force=True)
            email = ''
            if token_payload and 'username' in token_payload:
                email = token_payload['username']
            result = user_service.create_user(store, user, email)
            return result, 200
        except ResourceAlreadyExistException as e:
            return {"message": "username: User with the given username is already exist."}, 409
        except InvalidInputException as e:
            return {"message": "Provide valid input."}, 400
        except ResourceNotFoundException as e:
            return {"message": "role_id: User role doesn't exist."}, 404
        finally:
            logger.debug("Exiting AdminUsers POST")
    
    def post(self):
        return self.execute_store_request(request, self.post_executor)

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class AdminUser(APIResource):

    def get_executor(self, request, token_payload, store, user_id):
        logger.debug("Entering AdminUsers GET")
        try:
            user = user_service.get_user_by_id(store, user_id)
            return {"data":user}, 200
        except ResourceAlreadyExistException as e:
            return {"message": e.message}, 409
        except InvalidInputException as e:
            return {"message": e.message}, 400
        except ResourceNotFoundException as e:
            return {"message": e.message}, 404
        finally:
            logger.debug("Exiting AdminUsers GET")

    def put_executor(self, request, token_payload, store, user_id):
        logger.debug("Entering AdminUsers PUT")
        try:
            user = request.get_json(force=True)
            result = user_service.update_user(store, user_id, user)
            return result, 200
        except ResourceAlreadyExistException as e:
            return {"message": e.message}, 409
        except InvalidInputException as e:
            return {"message": e.message}, 400
        except ResourceNotFoundException as e:
            return {"message": e.message}, 404
        finally:
            logger.debug("Exiting AdminUsers PUT")

    def delete_executor(self, request, token_payload, store, user_id):
        logger.debug("Entering AdminUsers DELETE")
        try:
            result = user_service.delete_user(store, user_id)
            return result, 200
        except InvalidInputException as e:
            return {"message": e.message }, 400
        except ResourceNotFoundException as e:
            return {"message": e.message }, 404
        finally:
            logger.debug("Exiting AdminUsers POST")
    
    def get(self, user_id):
        return self.execute_store_request(request, self.get_executor, user_id)
    
    def delete(self, user_id):
        return self.execute_store_request(request, self.delete_executor, user_id)

    def put(self, user_id):
        return self.execute_store_request(request, self.put_executor, user_id)


class ChangeUserPassword(APIResource):

    def post_executor(self, request, token_payload, store):
        logger.debug("Entering ChangeUserPassword POST")
        try:
            payload = request.get_json(force=True, silent=True)
            if payload:
                username = payload.get("username", None)
                password = payload.get("password", None)
                if username and password:
                    user = user_service.change_password(username, password)
                    if user:
                        return {"message": "Password has been changed successfully."}, 200
                    else:
                        return {"message": "User doesn't exist"}, 404
            return {"message": "Provide valid username and password"}, 400
        finally:
            logger.debug("Exiting ChangeUserPassword POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)

class UserLogin(APIResource):
    # Create user login ...
    def post_executor(self, request, token_payload, store, token=None):
        try:
            user = request.get_json(force=True)
            _old_token = request.headers.get('X-AUTH-TOKEN', None)
            res = user_service.store_user_login(store, user['username'])
            if res['status'] == 200:
                auth_util.logout(token_payload['username'], _old_token)
                return {"data": res['data']}, res['status']
            else:
                return {"message": res['message']}, res['status']
        except Exception as e:
            logger.error("User LoginAPI POST: Exception caught: ", traceback.format_exc())
            return {"message": "Something went wrong at server!!"}, 500
        finally:
            logger.debug("Exiting User LoginAPI POST")
    def post(self):
        return self.execute_store_request(request, self.post_executor)