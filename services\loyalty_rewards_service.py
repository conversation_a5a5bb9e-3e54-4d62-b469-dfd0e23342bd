from services import Service
from utils.common import parse_json
from bson import ObjectId
from datetime import datetime
import os
import logging
import requests
# from appconfig import get_mongodb_hostname, get_mongodb_port
from fields.loyalty_app_fields import reward_fields
from fields.customers import list_fields_with_loyalty_points, list_fields
from utils.common import calculatePaginationData
from mongo_db import user_db, customer_db

logger = logging.getLogger()


class points(Service):
    def get_all(self, store, payload):   
        response = {
            "status": 400
        }          
        customers = {}  
        customers = customer_db.fetch_all_customer_with_points(store)               
        if customers:
            customer_ids = []
            for customer in customers:
                customer_ids.append(customer['customer_id'])                            
                    
            customer_data = customer_db.get_customers_using_id(store, customer_ids)  
            if customer_data:
                data_array = super().processList(customer_data)               
                limit = int(payload["limit"]) if payload.__contains__("limit") else 1
                page = int(payload["page"]) if payload.__contains__("page") else 1
                
                customers = customer_db.fetch_all_customer_with_points(store)                 
                for customer in customers:                    
                    for c_data in data_array:                                                                
                        if int(c_data['id']) == customer['customer_id']:                            
                            c_data['loyalty_points'] = customer['loyalty_points'] if 'loyalty_points' in customer else 0
                            c_data['reward_count'] = len(customer['coupons'])
                            c_data['status'] = ''

                #filter the data array
                filtered_data = data_array
                if payload['filter'] != '':               
                    filtered_data = [
                        item for item in data_array
                        if payload['filter'].lower().strip() in item["email"].lower().strip() or payload['filter'].lower().strip() in item["name"].lower().strip()
                    ]

                # added latest reward history date in data array
                filtered_data = self.get_latest_reward_history(store, filtered_data)
                
                # sort the data array after filter
                filtered_data = sorted(filtered_data, key=lambda x: x[payload['sort_by']], reverse=payload['sort_order'] == "desc")

                start_limit = ((page - 1) * limit) 
                end_limit = start_limit + limit
                converted_array = filtered_data[start_limit:end_limit]                              
                data = calculatePaginationData(converted_array, page, limit, len(filtered_data))            

                response['data'] = data
                response['status'] = 200    
            else:
                response['message'] = "No data found."
                response['status'] = 404
        else:
                response['message'] = "No data found."
                response['status'] = 404

        return response                                                                          

    def get_latest_reward_history(self, store, customer_data):
        customer_ids = []
        for customer in customer_data:
            customer_ids.append(int(customer['id']))

        latest_data = customer_db.get_customers_latest_history(store, customer_ids) 
        for customer in customer_data:
            for latest in latest_data:
                if int(latest['_id']) == int(customer['id']):
                    customer['reward_date'] = latest['latest_entry']['created_at']        

        return customer_data





class Rewards(Service):

    def get_all_rewards(self, payload):
        db = []
        rewards, total_data_length, page, limit = super(
        ).get_paginated_records(payload, reward_fields, db, 0)
        # include pagination data in response ...
        data = calculatePaginationData(
            rewards, page, limit, total_data_length)
        return data

    def create_reward(self, body):
        response = {
            "status": 400
        }
        user = self.get_user_by_username(body['created_by'])
        points = body['reward_points']
        c_value = body['coupon_value']
        if c_value:
            c_value = "$" + c_value.strip() + " Off"
        else:
            response['message'] = "Coupon value can't be empty"
            response['status'] = 204

        isUniqueReward = self.checkForUniqueReward(
            int(points.strip()), c_value, '')
        if isUniqueReward['status'] == True:
            created_by = {
                "user_id": user['id'],
                "user_name": user['name']
            }
            body["created_at"] = int(datetime.utcnow().timestamp())
            body["updated_at"] = int(datetime.utcnow().timestamp())
            body["created_by"] = created_by
            body["updated_by"] = created_by
            body['reward_points'] = int(points.strip())
            body['coupon_value'] = c_value

            id = super().create(body)
            response['message'] = "Reward created successfully"
            response['status'] = 200
        else:
            response['status'] = 409
            response['message'] = isUniqueReward['reasion']

        return response

    def update_reward(self, body, reward_id=None):
        response = {
            "status": 400
        }
        user = self.get_user_by_username(body['updated_by'])
        points = body['reward_points']
        c_value = body['coupon_value'].strip()

        isUniqueReward = self.checkForUniqueReward(
            int(points.strip()), c_value, reward_id)

        if isUniqueReward['status'] == True:
            updated_by = {
                "user_id": user['id'],
                "user_name": user['name']
            }
            id = super().update_one({"_id": ObjectId(str(reward_id))}, {"$set":
                                                                        {
                                                                            "status": body['status'],
                                                                            "updated_at":  int(datetime.utcnow().timestamp()),
                                                                            "updated_by": updated_by
                                                                        }
                                                                        })
            response['message'] = "Reward updated successfully"
            response['status'] = 200
        else:
            response['status'] = 409
            response['message'] = isUniqueReward['reasion']

        return response

    def checkForUniqueReward(self, reward_points, coupon_value, r_id):
        result = {
            "status": False
        }
        reward_points = reward_points
        coupon_value = coupon_value.strip()
        rewards = super().find_all()
        rewards = parse_json(self.processList(rewards))
        for reward in rewards:
            if reward['status'] == 'active' and reward['id'] != r_id:
                if reward['coupon_value'] == coupon_value:
                    result['status'] = False
                    result['reasion'] = 'The coupon code with the same value already exist'
                    return result
                elif reward['reward_points'] == reward_points:
                    result['status'] = False
                    result['reasion'] = 'The coupon code with the same points already exist'
                    return result
        result['status'] = True
        result['reasion'] = 'All good'
        return result

    def get_user_by_username(self, username):
        res = user_db.fetch_user_by_username(username)
        res['id'] = str(res['_id'])

        del res["yoyo"]
        del res['_id']
        del res['created_at']
        del res['updated_at']

        return res