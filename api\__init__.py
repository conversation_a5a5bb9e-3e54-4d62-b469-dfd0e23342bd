from flask_restful import Api, Resource
import traceback
import logging
import appconfig
from utils import redis_util
from iam import auth_service
from new_utils import store_util
import jwt

logger = logging.getLogger()

AUTH_TOKEN_HEADER_KEY = "X-AUTH-TOKEN"
TENANT_ID_HEADER_KEY = "X-TENANT-ID"
STORE_ID_HEADER_KEY = "X-STORE-ID"
ORIGIN_HEADER_KEY = "Origin"
HTTP_ORIGIN_HEADER_KEY = "HTTP_ORIGIN"
CLIENT_ID_HEADER_KEY = "X-CLIENT-ID"
TENANT_ID_HEADER_KEY = "X-TENANT-ID"


class APIResource(Resource):

    request_id = 1

    def __init__(self, service):
        self.service = service

    def is_request_from_valid_origin(self, request):
        # origin = request.headers.get(
        #     HTTP_ORIGIN_HEADER_KEY, None) or request.headers.get(OR<PERSON><PERSON>_HEADER_KEY, None)
        # if origin:
        #     return origin == appconfig.get_admin_panel_url()
        return True

    def execute_open_api_request(self, request, request_executor, *args):
        current_request_id = APIResource.request_id
        APIResource.request_id = APIResource.request_id + 1
        logger.debug("Entering execute_request")
        logger.info(f"{current_request_id} Serving {request.base_url}")
        try:
            if self.is_request_from_valid_origin(request):
                return request_executor(request, *args)
            return {"message": "Unauthorized, 4"}, 401
        except Exception as ex:
            logger.error("execute_store_request: " + traceback.format_exc())
            return {"message": "Server failed to execute operation. Please try again."}, 500
        finally:
            logger.info(f"{current_request_id} Finished Serving {request.base_url}")
            logger.debug("Exiting execute_request")

    def validate_token(self, request):
        payload = None
        token = request.headers.get(AUTH_TOKEN_HEADER_KEY, None)
        client_id = request.headers.get(CLIENT_ID_HEADER_KEY, None)
        tenant_id = request.headers.get(TENANT_ID_HEADER_KEY, None)
        if token:
            if client_id:
                payload = auth_service.validate_client(client_id, token)
            else:
                payload = auth_service.validate_access_token(tenant_id, token)
                if payload:
                    if appconfig.is_debug_enabled():
                        client_id = payload['iss']
                    else:
                        client_id = redis_util.get_access_token(payload['username'], token)
                        if client_id:
                            client_id = str(client_id)
        return payload, client_id

    def execute_request(self, request, request_executor, *args):
        current_request_id = APIResource.request_id
        APIResource.request_id = APIResource.request_id + 1
        logger.debug("Entering execute_request")
        logger.info(f"{current_request_id} Serving {request.base_url}")
        try:
            if self.is_request_from_valid_origin(request):
                payload, client_id = self.validate_token(request)
                if payload and client_id:
                    return request_executor(request, payload, *args)
            return {"message": "Unauthorized. 1"}, 401
        except Exception as ex:
            logger.error("execute_request: " + traceback.format_exc())
            return {"message": "Server failed to execute operation. Please try again."}, 500
        finally:
            logger.info(f"{current_request_id} Finished Serving {request.base_url}")
            logger.debug("Exiting execute_request")

    def execute_store_request(self, request, request_executor, *args):
        current_request_id = APIResource.request_id
        APIResource.request_id = APIResource.request_id + 1
        logger.debug("Entering execute_request")
        logger.info(f"{current_request_id} Serving {request.base_url}")
        try:
            if self.is_request_from_valid_origin(request):
                payload, client_id = self.validate_token(request)
                if payload and client_id:
                    store_id = request.headers.get(STORE_ID_HEADER_KEY, None)
                    if store_id:
                        store = store_util.get_store_by_id(store_id)
                        if store:
                            is_valid = appconfig.is_debug_enabled() or auth_service.permission_check(store['id'], request.url, request.method, payload['username'])
                            if is_valid:
                                return request_executor(request, payload, store, *args)
                            else:
                                return {'message': 'You do not have permission to perform this operation'}, 403
                    return {"message": "Invalid store id."}, 404
                return {"message": "Unauthorized. 2"}, 401
            return {"message": "Unauthorized. 3"}, 401
        except Exception as ex:
            logger.error("execute_store_request: " + traceback.format_exc())
            return {"message": "Server failed to execute operation. Please try again."}, 500
        finally:
            logger.info(f"{current_request_id} Finished Serving {request.base_url}")
            logger.debug("Exiting execute_request")


    def execute_google_app_script_request(self, request, request_executor, *args):
        current_request_id = APIResource.request_id
        APIResource.request_id = APIResource.request_id + 1

        logger.debug("Entering gogole app script request")
        logger.info(f"{current_request_id} Serving {request.base_url}")

        try:
            # Step 1: Extract the JWT Token from the Authorization header
            auth_header = request.headers.get('Authorization')
            if not auth_header:
                return {"message": "Unauthorized: No token provided"}, 401

            token = auth_header.split(" ")[1]  # Assuming format 'Bearer <token>'
            store_id = request.headers.get('Store')

            store = store_util.get_store_by_id(store_id)

            # Step 2: Retrieve the secret key from the database
            jwt_secret = store['apps']['google']['jwt_secret']  # Modify this to fetch the secret from your database

            try:
                # Step 3: Decode and verify the JWT token
                decoded_token = jwt.decode(token, jwt_secret, algorithms=["HS256"])
                logger.info(f"Token validated for request {current_request_id}")

            except jwt.ExpiredSignatureError:
                return {"message": "Unauthorized: Token has expired"}, 401

            except jwt.InvalidTokenError:
                return {"message": "Unauthorized: Invalid token"}, 401

            # Step 4: If the token is valid, proceed with the request
            if self.is_request_from_valid_origin(request):
                return request_executor(request, token, store, *args)

            return {"message": "Unauthorized: Invalid origin"}, 401

        except Exception as ex:
            logger.error("execute_store_request: " + traceback.format_exc())
            return {"message": "Server failed to execute operation. Please try again."}, 500

        finally:
            logger.info(f"{current_request_id} Finished Serving {request.base_url}")
            logger.debug("Exiting execute_request")


class API():

    def __init__(self, app, service):
        logger.info("Entering API init ...")

        self.service = service
        self._api = Api(app, prefix=appconfig.get_api_prefix())

        """
        Tenant Create & Get all API
        POST : Create a new tenant
        GET : Get all tenants
        """
        from api import tenant_api
        self._api.add_resource(
            tenant_api.Tenants, "/tenant", resource_class_kwargs={"service": service}
        )

        """
        Tenant: API to get, update & delete a tenant
        Path Param: tenant_id
        PUT : Update tenant
        GET : Get tenant
        DELETE : Delete tenant
        """
        self._api.add_resource(
            tenant_api.Tenant, "/tenant/<string:tenant_id>", resource_class_kwargs={"service": service}
        )

        self._api.add_resource(
            tenant_api.Stores,
            "/tenant/<string:tenant_id>/store",
            resource_class_kwargs={"service": service},
        )

        self._api.add_resource(
            tenant_api.EnvCreds,
            "/tenant/<string:tenant_id>/envcreds/<string:store_id>",
            resource_class_kwargs={"service": service},
        )

        self._api.add_resource(
            tenant_api.NavigationIds,
            "/tenant/<string:tenant_id>/navigationids/<string:store_id>",
            resource_class_kwargs={"service": service},
        )

        self._api.add_resource(tenant_api.Templates, "/settings/tenant/<string:tenant_id>/mailtemplates/<string:store_id>/templatelist", resource_class_kwargs={"service": service})  # get all templates
        self._api.add_resource(tenant_api.TemplateOperations, "/settings/tenant/<string:tenant_id>/mailtemplates/<string:store_id>/template",
                               resource_class_kwargs={'service': service})  # template operations
        self._api.add_resource(tenant_api.TemplateDelete, "/settings/tenant/<string:tenant_id>/mailtemplates/<string:store_id>/<string:template_code>",
                               resource_class_kwargs={'service': service})  # template operations

        self._api.add_resource(
            tenant_api.SyncStore, "/tenant/sync/store", resource_class_kwargs={"service": service}
        )

        self._api.add_resource(
            tenant_api.BuildStore, "/tenant/<string:tenant_id>/store/<string:store_id>/tasks", resource_class_kwargs={"service": service}
        )

        self._api.add_resource(
            tenant_api.Store,
            "/tenant/<string:tenant_id>/store/<string:store_id>",
            resource_class_kwargs={"service": service},
        )

        from api import data_update_api
        self._api.add_resource(
            data_update_api.DataUpdate, "/tenant/<string:tenant_id>/store/<string:store_id>/data_update", resource_class_kwargs={"service": service}
        )

        # Admin auth apis...
        from api import auth_api
        self._api.add_resource(auth_api.Login, "/auth/login",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(
            auth_api.Logout, "/auth/logout", resource_class_kwargs={'service': service})

        self._api.add_resource(
            auth_api.GoogleLogin, "/auth/glogin", resource_class_kwargs={'service': service})

        self._api.add_resource(
            auth_api.ValidateToken, "/ping", resource_class_kwargs={'service': service})

        # Bigcommerce api...
        from api import bc_api
        self._api.add_resource(bc_api.BCApi, "/bc/api",
                               resource_class_kwargs={"service": service})

        # BC setup api...
        from api import bc_setup
        self._api.add_resource(
            bc_setup.Webhook, "/bc/config/webhook", resource_class_kwargs={'service': service})

        # BigCommerce webhook
        from api import bc_webhook
        self._api.add_resource(
            bc_webhook.BCWebhook,
            "/bc/webhook",
            resource_class_kwargs={"service": service}
        )

        # BigCommerce webhook
        from api import bc_webhook
        self._api.add_resource(
            bc_webhook.BCWebhooks,
            "/bc/webhooks",
            resource_class_kwargs={"service": service}
        )
        self._api.add_resource(
            bc_webhook.BCWebhooksRegister,
            "/bc/webhooks/register",
            resource_class_kwargs={"service": service}
        )


         # BigCommerce webhook
        from api import bc_webhook
        self._api.add_resource(
            bc_webhook.WebhookStatus,
            "/hook/status",
            resource_class_kwargs={"service": service}
        )

        # Task Management API
        from api import task_api
        self._api.add_resource(
            task_api.TaskRunner,
            "/settings/task",
            resource_class_kwargs={"service": service}
        )

        #### ORDER APIS ####
        from api import order_api
        self._api.add_resource(order_api.Orders, "/orders",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(order_api.Order, "/orders/<string:order_id>",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(order_api.BCOrderShippingAddress,
                               "/orders/bc/<string:order_id>/shipping_address", resource_class_kwargs={'service': service})
        self._api.add_resource(order_api.BCOrderDetails, "/orders/bc/<string:order_id>",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(order_api.CustomerOrderDetails, "/orders/customer/<string:customer_id>/orderhistory",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(order_api.BCOrderProducts, "/orders/bc/carts/<string:cart_id>",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(order_api.BCOrderProductsListing,
                               "/orders/bc/<string:order_id>/products", resource_class_kwargs={'service': service})
        self._api.add_resource(order_api.OrderConsignment,
                               "/order-consignment", resource_class_kwargs={'service': service})
        self._api.add_resource(order_api.OrdersPriceList,
                               "/orders/price-list", resource_class_kwargs={'service': service})
        self._api.add_resource(order_api.OrderPriceAudit,
                               "/orders/price-audit", resource_class_kwargs={'service': service})
        self._api.add_resource(order_api.OrderPriceAuditReport,
                               "/orders/price-audit/report", resource_class_kwargs={'service': service})
        self._api.add_resource(order_api.OrderPriceAuditReportDetails,
                               "/orders/price-audit/report/<string:order_id>", resource_class_kwargs={'service': service})
        self._api.add_resource(order_api.OrdersProductsSuggestion,
                               "/orders/products/suggestion", resource_class_kwargs={'service': service})
        self._api.add_resource(order_api.OrdersProductsVariants, "/orders/products/variants/<string:parent_sku>", resource_class_kwargs={"service": service})
        self._api.add_resource(order_api.OrdersCheckLineItemAvailability, "/orders/<string:order_id>/lineitem/availability", resource_class_kwargs={"service": service})

        self._api.add_resource(
            order_api.BlockedOrders,
            "/orders/blocked-orders",
            resource_class_kwargs={"service": service},
        )
        self._api.add_resource(
            order_api.BlockedOrdersOperations,
            "/orders/blocked-orders/<string:order_id>",
            resource_class_kwargs={"service": service},
        )
        self._api.add_resource(
            order_api.BlockedOrdersDetailProducts,
            "/orders/blocked-order/<string:order_id>/products",
            resource_class_kwargs={"service": service},
        )
        self._api.add_resource(
            order_api.BlockedOrderLogs,
            "/orders/blocked-order/<string:order_id>/logs",
            resource_class_kwargs={"service": service},
        )

        self._api.add_resource(
            order_api.OrderUnitPrices,
            "/orders/unit-price/<string:order_id>/products",
            resource_class_kwargs={"service": service},
        )

        from api import live_cart_api
        self._api.add_resource(live_cart_api.Cartsdata, "/orders/live/carts", resource_class_kwargs={"service": service})
        self._api.add_resource(live_cart_api.Cartdetails, "/orders/live/cartdetails/<int:customer_id>", resource_class_kwargs={"service": service})
        self._api.add_resource(live_cart_api.CheckLineItemAvailability, "/orders/live/cart/lineitem/<string:sku>", resource_class_kwargs={"service": service})

        self._api.add_resource(live_cart_api.ProductsSuggestion, "/orders/live/products/suggestion", resource_class_kwargs={"service": service})
        self._api.add_resource(live_cart_api.ProductsVariants, "/orders/live/products/variants/<string:parent_sku>", resource_class_kwargs={"service": service})

        self._api.add_resource(live_cart_api.CartLineItemReport, "/orders/live/cart/lineitems", resource_class_kwargs={"service": service})
        self._api.add_resource(live_cart_api.CartLineItemCustomers, "/orders/live/cart/lineitem/customers", resource_class_kwargs={"service": service})

        #### ORDER APIS ####


        #### PRODUCT APIS ####
        from api import products_api
        self._api.add_resource(
            products_api.Products, "/products", resource_class_kwargs={'service': service})
        self._api.add_resource(products_api.Product, "/products/<string:product_id>",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(products_api.ProductAttachments, "/products/<string:product_id>/attachments",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(products_api.ProductAttachmentsPreview, "/products/<string:product_id>/attachments/preview/<string:type>/<string:month>/<string:file_name>",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(products_api.ProductVersions, "/products/versions/<string:product_id>",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(products_api.ProductPageBuilderImages, "/products/page-builder/images",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(products_api.StoreProductListing,"/products/product-listing",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.TaxClasses,"/products/tax-classes",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.UploadCsvData,"/products/upload-data",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.ProductPriceList,"/products/price-list",
                               resource_class_kwargs={"service": service})        
        self._api.add_resource(products_api.ProductPriceListDetails,"/products/price-list/<string:product_id>",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.ProductPriceListCspList,"/products/price-list/<string:product_id>/csp-list",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.CostAnalysisReport,"/products/cost-analysis-report",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.PriceListProductsDropdown,"/products/price-list/filtered-products/dropdown", # get all products according to the classification applied in the dropdown.
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.PriceListClassifiedAsDropdown,"/products/price-list/classified-as/dropdown", # get all products according to the classification-as applied in the dropdown.
                               resource_class_kwargs={"service": service})

        self._api.add_resource(products_api.ProductPriceListLogs,"/products/price-list/logs",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.ProductPriceListLogsUsers,"/products/price-list/logs/users",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.ProductPriceListLogsDropdown,"/products/price-list/logs/dropdown",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.ProductPriceListStatusCheck,"/products/price-list/status-check", # get the status of the prices updated or not in the background.
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.ProductPriceListTags,"/products/price-list/tags",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.ProductPriceListClassifications,"/products/price-list/classifications",
                               resource_class_kwargs={"service": service})
        
        self._api.add_resource(products_api.ProductInquiries,"/products/inquiries",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.ProductInquiriesDetails,"/products/inquiries/<string:id>",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.CustomerRepresentatives,"/products/inquiries/customer/reps",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.InquiriesColumnsListing,"/products/inquiries/columns/listing",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.InquiriesColumns,"/products/inquiries/columns",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(products_api.InquiriesByRepStatus, "/products/inquiries/sales-rep-report",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.InquiriesByProductStatus, "/products/inquiries/product-report",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.InquiriesByPurchaserStatus, "/products/inquiries/purchaser-report",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.PurchaserDropdown, "/products/inquiries/filters/purchaser",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(products_api.ProductPriceListAppScript,
                               "/products/pricelist", resource_class_kwargs={'service': service})
        self._api.add_resource(products_api.ProductPriceListDistributors,"/products/price-list/distributors",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.PriceGroupDropdown,"/products/price-list/price-groups",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.PriceListCategoriesDropdown,"/products/price-list/categories",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.PriceListRules,"/products/price-list/rules",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.PriceListRuleDetails,"/products/price-list/rules/<string:rule_id>",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(products_api.ProductCustomerPriceMapping,"/products/price-list/customers/price",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.UpadateCustomerProductPriceMapping,"/products/price-list/customers/<string:customer_id>/price",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(products_api.PriceListCsvUpload,"/products/price-list/upload/csv",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(products_api.PriceListCsvUploadStatus,"/products/price-list/upload/csv/status",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(products_api.ProductsOrderHistory, "/analytics/sold-products/order/history/<int:product_id>", resource_class_kwargs={'service': service})

        self._api.add_resource(products_api.PriceListCsvExport,"/products/price-list/export/csv",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.PriceListMappedUsers,"/products/price-list/mapped-users",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(products_api.PriceListColumnsListing,"/products/price-list/columns/listing",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.PriceListColumns,"/products/price-list/columns",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(products_api.PriceListCustomer,"/products/price-list/filters/customers",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(products_api.PriceListCustomerEmail,"/products/price-list/filters/customer-emails",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(products_api.PriceListCustomerRepresentative,"/products/price-list/filters/customer-reps",
                               resource_class_kwargs={"service": service})

        # Temp API to update new price lists with cost plus percentage
        self._api.add_resource(products_api.PriceListUpdateWithCostPlusPercentage,"/products/price-list/update-with-cost-plus-percentage",
                               resource_class_kwargs={"service": service})
        
        # Multi store product price list apis
        self._api.add_resource(products_api.MultiProductPriceList,"/products/price-list/multi",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.ProductPriceListDetailsMulti,"/products/price-list/multi/<string:product_sku>",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.PriceListMultiCsvUpload,"/products/price-list/multi/upload-csv",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.PriceListMultiCsvUploadStatus,"/products/price-list/multi/upload/csv/status",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.PriceListMultiCsvExport,"/products/price-list/multi/export/csv",
                               resource_class_kwargs={"service": service})
        
        self._api.add_resource(products_api.PriceListMappedUsersMulti,"/products/price-list/mapped-users/multi",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.ProductPriceListClassificationsMulti,"/products/price-list/classifications/multi",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.ProductPriceListTagsMulti,"/products/price-list/tags/multi",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.PriceListClassifiedAsDropdownMulti,"/products/price-list/classified-as/dropdown/multi",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(products_api.PriceListProductsDropdownMulti,"/products/price-list/filtered-products/dropdown/multi",
                               resource_class_kwargs={"service": service})
        
        # Multi store product price list apis ends


        #### PRODUCT APIS ####

        #### PRODUCTS LIQUIDATION API ####
        from api import products_liquidation_api
        self._api.add_resource(products_liquidation_api.ProductsLiquidation, "/products/liquidation/report", resource_class_kwargs={'service': service})
        self._api.add_resource(products_liquidation_api.ProductsLiquidationCsv, "/products/liquidation/report/csv", resource_class_kwargs={'service': service})
        self._api.add_resource(products_liquidation_api.ProductsLiquidationUpdate, "/products/liquidation/update/<int:product_id>", resource_class_kwargs={'service': service})

        self._api.add_resource(products_liquidation_api.ProductsLiquidationLogs, "/analytics/sold-products/liquidated-product/logs/<int:product_id>", resource_class_kwargs={'service': service})

        #### MY PRODUCT APIS ####
        from api import my_products_api
        self._api.add_resource(my_products_api.MyProducts, "/my-products", resource_class_kwargs={'service': service})
        self._api.add_resource(my_products_api.MyProduct, "/my-products/<string:product_id>", resource_class_kwargs={'service': service})
        self._api.add_resource(my_products_api.ProductImages, "/my-products/images", resource_class_kwargs={'service': service})
        self._api.add_resource(my_products_api.ProductStaticImages, "/my-products/image/<string:image_type>/<string:month>/<string:file_name>", resource_class_kwargs={'service': service})
        self._api.add_resource(my_products_api.SkuvaultBrands, "/my-products/skuvault/brands", resource_class_kwargs={'service': service})
        self._api.add_resource(my_products_api.SkuvaultClassification, "/my-products/skuvault/classification", resource_class_kwargs={'service': service})
        self._api.add_resource(my_products_api.SkuvaultSuppliers, "/my-products/skuvault/suppliers", resource_class_kwargs={'service': service})

        #### MY PRODUCT APIS ####


        #### PRODUCT CATEGORIES APIS ####
        from api import products_api
        self._api.add_resource(
            products_api.ProductsCategories, "/products/categories", resource_class_kwargs={'service': service})
        self._api.add_resource(
            products_api.CategoryDetails, "/products/categories/<string:category_id>", resource_class_kwargs={'service': service})
        self._api.add_resource(
            products_api.SyncAllCategories, "/products/categories/moveall", resource_class_kwargs={'service': service})
        self._api.add_resource(
            products_api.ProductsCategoriesTreeView, "/products/categories/treeview", resource_class_kwargs={'service': service})

        #### PRODUCT CATEGORIES APIS ####
        
        #### PRODUCT INSTOCK NOTIFY ####
        from api import products_instock_notify_api
        self._api.add_resource(products_instock_notify_api.ProductsInstockNotify, "/products/instock-notify", resource_class_kwargs={'service': service})
        self._api.add_resource(products_instock_notify_api.ProductsInstockNotifyDetails, "/products/instock-notify/<int:product_id>/details", resource_class_kwargs={'service': service})
        #### PRODUCT INSTOCK NOTIFY ####

        #### PRODUCT SMART BADGE APIS ####

        from api import smart_badges_api
        self._api.add_resource(smart_badges_api.SmartBadges, "/products/product-badges",
                       resource_class_kwargs={"service": service})
        self._api.add_resource(smart_badges_api.SmartBadge, "/products/product-badges/<string:id>",
                       resource_class_kwargs={"service": service})
        self._api.add_resource(smart_badges_api.AllProducts, "/products/product-badges/all-products/dropdown",
                       resource_class_kwargs={"service": service})
        self._api.add_resource(smart_badges_api.AllBrands, "/products/product-badges/all-brands/dropdown",
                       resource_class_kwargs={"service": service})
        self._api.add_resource(smart_badges_api.AllCategories, "/products/product-badges/all-categories/dropdown",
                       resource_class_kwargs={"service": service})
        self._api.add_resource(smart_badges_api.CategoryTreeview, "/products/product-badges/categories/treeview",
                       resource_class_kwargs={"service": service})
        self._api.add_resource(smart_badges_api.BadgeProductDetails, "/products/product-badges/<string:id>/product-details",
                       resource_class_kwargs={"service": service})

        #### PRODUCT SMART BADGE APIS ####
        
        #### CUSTOMERS APIS ####
        from api import customers_api
        self._api.add_resource(
            customers_api.Customers, "/customers", resource_class_kwargs={'service': service})
        self._api.add_resource(customers_api.Customer, "/customers/<string:customer_id>",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(customers_api.CustomerLogin,
                               "/customers/login", resource_class_kwargs={'service': service})
        self._api.add_resource(customers_api.BCCustomers, "/customers/bc/<string:customer_id>",
                               resource_class_kwargs={'service': service}) ## Overview ##
        self._api.add_resource(customers_api.BCCustomerGroupdetails,
                               "/customers/bc/customer_groups/<string:customer_id>", resource_class_kwargs={'service': service})
        self._api.add_resource(customers_api.BCCustomerShippingAddressDetails,
                               "/customers/bc/<string:customer_id>/addresses", resource_class_kwargs={'service': service}) ## Addresses ##
        self._api.add_resource(customers_api.BCCustomerGroups,
                               "/customers/bc/customer_groups", resource_class_kwargs={'service': service})
        self._api.add_resource(customers_api.History,
                               "/customers/bc/<string:customer_id>/loyalty/history", resource_class_kwargs={'service': service})
        self._api.add_resource(customers_api.BCCustomerConsignmentOrders, "/customers/bc/<string:customer_id>/consignment/orders",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(customers_api.CustomerTags, "/customers/tags/<string:customer_id>",
                               resource_class_kwargs={'service': service})

        self._api.add_resource(customers_api.CustomerPriceMapping,"/customers/<string:customer_id>/product-price",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(customers_api.UpadateCustomerPriceMapping,"/customers/<string:customer_id>/update-product-price",
                               resource_class_kwargs={'service': service})
        
        self._api.add_resource(customers_api.CustomerReturnsData,"/customers/<string:email_id>/returns-data",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(customers_api.CustomerSalesforceFinancialPaymentDetails,
                               "/customers/<string:customer_id>/salesforce-financial-payment", resource_class_kwargs={'service': service})
        self._api.add_resource(customers_api.CustomerSalesforceSystemActivityDetails,
                               "/customers/<string:customer_id>/salesforce-system-activity", resource_class_kwargs={'service': service})
        self._api.add_resource(customers_api.CustomerSalesforceAccontInfo,"/customers/<string:customer_id>/salesforce-account-info",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(customers_api.CustomerSalesforceOrders,"/customers/<string:customer_id>/salesforce-orders",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(customers_api.CustomerSalesforceDocuments,"/customers/<string:customer_id>/salesforce-documents",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(customers_api.CustomerSalesforceDocumentsDownload,"/customers/salesforce-documents/download",
                               resource_class_kwargs={'service': service})

        #### CUSTOMERS APIS ####

        #### CUSTOMER representative API ####
        self._api.add_resource(customers_api.CustomerRepresentative,
                               "/customers/reps", resource_class_kwargs={'service': service})

        self._api.add_resource(customers_api.CustomerGroups,
                               "/customers/groups", resource_class_kwargs={'service': service})

        #### CUSTOMERS LOYALTY APIS #####
        from api import customers_loyalty_api
        self._api.add_resource(
            customers_loyalty_api.CustomersWithPoints, "/customers/loyalty/loyaltypoints", resource_class_kwargs={'service': service})
        self._api.add_resource(
            customers_loyalty_api.Rewards, "/customers/loyalty/rewardlist", resource_class_kwargs={'service': service})
        self._api.add_resource(
            customers_loyalty_api.CreateReward, "/customers/loyalty/reward", resource_class_kwargs={'service': service})
        self._api.add_resource(
            customers_loyalty_api.RewardOperation, "/customers/loyalty/reward/<string:reward_id>", resource_class_kwargs={'service': service})
        self._api.add_resource(
            customers_loyalty_api.UpdateWelcomePoint, "/customers/loyalty/welcome", resource_class_kwargs={'service': service})
        self._api.add_resource(
            customers_loyalty_api.Coupons,"/customers/loyalty/customer/coupons", resource_class_kwargs={'service': service})
        self._api.add_resource(
            customers_loyalty_api.UpdateLoyaltyPoint, "/customers/loyalty/update-points", resource_class_kwargs={'service': service})
        self._api.add_resource(
            customers_loyalty_api.CustomerHistory, "/customers/loyalty/customer/history", resource_class_kwargs={'service': service})
        #### CUSTOMERS LOYALTY APIS #####

        #### STOREFRONT APIS ####
        from api import navigation_api
        self._api.add_resource(navigation_api.Navigation, "/storefronts/navigations",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(navigation_api.NavigationOperations,
                               "/storefronts/navigations/<string:navigation_id>", resource_class_kwargs={'service': service})
        self._api.add_resource(navigation_api.NavigationBrands,
                               "/storefronts/navigations/brands", resource_class_kwargs={'service': service})
        self._api.add_resource(navigation_api.NavigationCategories,
                               "/storefronts/navigations/categories", resource_class_kwargs={'service': service})
        self._api.add_resource(navigation_api.NavigationWebPages,
                               "/storefronts/navigations/webpages", resource_class_kwargs={'service': service})
        self._api.add_resource(navigation_api.SyncAllBrands,
                               "/storefronts/navigations/brands/moveall", resource_class_kwargs={'service': service})


        from api import sub_navigations_api
        self._api.add_resource(sub_navigations_api.SubNavigation,
                               "/storefronts/navigations/<string:navigation_id>/sub", resource_class_kwargs={'service': service})
        self._api.add_resource(sub_navigations_api.GetSubNavigationByShortCode,
                               "/storefronts/<string:tenant_id>/<string:store_id>/navigation/<string:short_code>", resource_class_kwargs={'service': service})

        from api import theme_build_api
        self._api.add_resource(theme_build_api.Build, "/storefronts/themes/build",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(theme_build_api.NetlifyBuilds, "/storefronts/themes/netlifybuilds",
                               resource_class_kwargs={"service": service})  # get all builds from netlify with db data
        self._api.add_resource(theme_build_api.PublishedBuild, "/storefronts/themes/publishedbuild",
                               resource_class_kwargs={"service": service})  # to get published build and activate selacted deploye
        self._api.add_resource(theme_build_api.TriggerBuild, "/storefronts/themes/triggerbuild",
                               resource_class_kwargs={"service": service})  # trigger build
        self._api.add_resource(theme_build_api.CurrentBuild, "/storefronts/themes/currentbuild/<string:build_id>",
                               resource_class_kwargs={"service": service})  # current build state
        self._api.add_resource(theme_build_api.CancelBuild, "/storefronts/themes/cancelbuild",
                               resource_class_kwargs={"service": service})  # cancel triggered build

        from api import storeinfo_api
        self._api.add_resource(storeinfo_api.StoreInformation,"/storefronts/storeinfo",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(storeinfo_api.InfoImageOperation, "/storefronts/storeinfo/images",
                               resource_class_kwargs={'service': service})  # store and get images to server

        from api import image_api
        self._api.add_resource(image_api.StaticImageAPI, "/static/images/<string:image_type>/<string:file_name>",
                               resource_class_kwargs={'service': service})

        ### PRODUCT PROMOTION API ###

        from api import rule_engine_api
        self._api.add_resource(rule_engine_api.VariantsVisibilityRules,"/analytics/automation/rule-engine/variants",resource_class_kwargs={'service': service})
        self._api.add_resource(rule_engine_api.ProductsVisibilityRules,"/analytics/automation/rule-engine/products",resource_class_kwargs={'service': service})
        self._api.add_resource(rule_engine_api.ProductRulesModalData,"/analytics/automation/rule-engine/products/rule/modal/data",resource_class_kwargs={'service': service})

        self._api.add_resource(rule_engine_api.UnhideProductRules,"/analytics/automation/rule-engine/unhideproducts",resource_class_kwargs={'service': service})
        ### PRODUCT PROMOTION API ###

        #### STOREFRONT APIS ####

        #### CMS APIS ####
        from api import dynamic_pages
        self._api.add_resource(dynamic_pages.Pages, "/cms/webpages/<string:tenant_id>/<string:store_id>/pages",
                               resource_class_kwargs={"service": service})  # get all webpages with active versions data
        self._api.add_resource(dynamic_pages.CreatePages, "/cms/webpages",
                               resource_class_kwargs={'service': service})  # to create pages
        self._api.add_resource(dynamic_pages.PageOperations, "/cms/webpages/<string:webpage_id>",
                               resource_class_kwargs={'service': service})   # to delete or update page (name status etc)
        self._api.add_resource(dynamic_pages.PageComponents, "/cms/webpages/<string:webpage_id>/version", resource_class_kwargs={
                               'service': service})  # operations for add webpage version and get webpage with active version
        self._api.add_resource(dynamic_pages.PageVersionsOperation, "/cms/webpages/<string:webpage_id>/versionids",
                               resource_class_kwargs={'service': service})  # get list of webpage version ids and active given version
        self._api.add_resource(dynamic_pages.ImageOperation, "/cms/webpages/images",
                               resource_class_kwargs={'service': service})  # store and get images to server
        self._api.add_resource(dynamic_pages.BcProductDetails, "/cms/webpages/product/details",
                               resource_class_kwargs={'service': service})  # get bd product details
        self._api.add_resource(dynamic_pages.PageVersionData, "/cms/webpages/<string:webpage_id>/versiondata",
                               resource_class_kwargs={'service': service})  # get webpage with selected versions data
        self._api.add_resource(dynamic_pages.PagePreviewData, "/cms/webpages/<string:webpage_id>/previewdata",
                               resource_class_kwargs={'service': service})  # set page preview data
        self._api.add_resource(dynamic_pages.StorefrontPages, "/cms/webpages/<string:tenant_id>/<string:store_id>/storefront",
                               resource_class_kwargs={"service": service})  # get all webpages with active versions data for storefront

        self._api.add_resource(dynamic_pages.BCPageOperations, "/cms/bcwebpage",
                               resource_class_kwargs={'service': service})  # Big commerce page operations
        self._api.add_resource(dynamic_pages.BCWebPages, "/cms/webpages/sync",
                               resource_class_kwargs={'service': service})  # Sync BC webpages

        from api import component_api
        self._api.add_resource(component_api.Components, "/cms/components",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(component_api.ComponentOperations,
                               "/cms/components/variant", resource_class_kwargs={"service": service})
        #### CMS APIS ####

        ###### Blogs APIS#####
        from api import blogs_api
        self._api.add_resource(blogs_api.Blogs, "/cms/blogs/<string:tenant_id>/<string:store_id>/bloglist",
                               resource_class_kwargs={"service": service})  # get all blogs
        self._api.add_resource(blogs_api.CreateBlogs, "/cms/blogs",
                               resource_class_kwargs={'service': service})  # to create blogs
        self._api.add_resource(blogs_api.BlogOperations, "/cms/blogs/<string:blog_id>",
                               resource_class_kwargs={'service': service})   # to delete or update blog (name status etc)
        self._api.add_resource(blogs_api.BlogData, "/cms/blogs/<string:blog_id>/data", resource_class_kwargs={
                               'service': service})  # operations for add blog data and get blog with it's data
        self._api.add_resource(blogs_api.BlogImageOperation, "/cms/blogs/images",
                               resource_class_kwargs={'service': service})  # store and get images to server

        self._api.add_resource(blogs_api.BCBlogs, "/cms/blogs/sync",
                               resource_class_kwargs={'service': service})  # Sync BC webpages

        #### Blogs Author APIS######
        from api import blogs_author_api
        self._api.add_resource(blogs_author_api.Authors, "/cms/blogs/authors/<string:tenant_id>/<string:store_id>/authorlist",
                               resource_class_kwargs={"service": service})  # get all author
        self._api.add_resource(blogs_author_api.CreateAuthors, "/cms/blogs/authors",
                               resource_class_kwargs={'service': service})  # to create author
        self._api.add_resource(blogs_author_api.AuthorOperations, "/cms/blogs/authors/<string:author_id>",
                               resource_class_kwargs={'service': service})   # to delete or update author (name status etc)

        #### Blogs Category APIS######
        from api import blogs_category_api
        self._api.add_resource(blogs_category_api.Categories, "/cms/blogs/categories/<string:tenant_id>/<string:store_id>/categorylist",
                               resource_class_kwargs={"service": service})  # get all category
        self._api.add_resource(blogs_category_api.CreateCategories, "/cms/blogs/categories",
                               resource_class_kwargs={'service': service})  # to create category
        self._api.add_resource(blogs_category_api.CategoryOperations, "/cms/blogs/categories/<string:category_id>",
                               resource_class_kwargs={'service': service})   # to delete or update category (name status etc)

        #### BIGCOMMERCE MAIL TEMPLATES Types ####
        from api import mail_template_types_api
        self._api.add_resource(mail_template_types_api.TemplateTypes, "/settings/mails/types/<string:tenant_id>/<string:store_id>/typelist",
                               resource_class_kwargs={"service": service})  # get all mails types
        self._api.add_resource(mail_template_types_api.CreateTemplates, "/settings/mails/template/type",
                               resource_class_kwargs={'service': service})  # to create Template type
        self._api.add_resource(mail_template_types_api.TypeOperations, "/settings/mails/template/type/<string:type_id>",
                               resource_class_kwargs={'service': service})   # to delete or update template type (name status etc)

        #### BIGCOMMERCE MAIL TEMPLATES Types ####

        #### UTILITY APIS ####
        # SHEETS #
        from api import google_sheet_data_api
        self._api.add_resource(google_sheet_data_api.SheetData,
                               "/utils/sheets/data", resource_class_kwargs={"service": service})
        self._api.add_resource(google_sheet_data_api.SheetDataHeader,
                               "/utils/sheets/data/header", resource_class_kwargs={"service": service})
        self._api.add_resource(google_sheet_data_api.MappingTable,
                               "/utils/sheets/mappingtable", resource_class_kwargs={'service': service})
        self._api.add_resource(google_sheet_data_api.MappingTableCSV,
                               "/utils/sheets/mappingtable/csv", resource_class_kwargs={'service': service})
        self._api.add_resource(google_sheet_data_api.MappingTableOperations,
                               "/utils/sheets/mappingtable/<string:mapping_table_id>", resource_class_kwargs={'service': service})
        self._api.add_resource(google_sheet_data_api.MappingTableDataOperations,
                               "/utils/sheets/mappingtable/<string:mapping_table_id>/data", resource_class_kwargs={'service': service})
        self._api.add_resource(google_sheet_data_api.SheetCsvData,
                               "/utils/sheets/mappingtable/<string:mapping_table_id>/csv/data", resource_class_kwargs={'service': service})

        #### UTILITY APIS ####

        #### ANALYTICS APIS ####
        # SOLD PRODUCT LISTING
        from api import analytics_api
        self._api.add_resource(analytics_api.SoldProductsAnalytics,
                               "/analytics/soldproducts", resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.HideProductFromStoreFront,
                               "/analytics/soldproducts/visibility", resource_class_kwargs={"service": service})

        self._api.add_resource(analytics_api.ProductsConsignmentAnalytics,
                               "/analytics/consignment/products", resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.ProductsOnHoldAnalytics,
                               "/analytics/on-hold/products", resource_class_kwargs={"service": service})
         #### ANALYTICS APIS ####

        #### SETTINGS APIS ####
        # USERS #
        from api import adminuser_api
        self._api.add_resource(adminuser_api.AdminUsers, "/settings/users",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(adminuser_api.AdminUser, "/settings/users/<string:user_id>",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(adminuser_api.ChangeUserPassword,
                               "/settings/users/password", resource_class_kwargs={'service': service})
        self._api.add_resource(adminuser_api.UserLogin,
                               "/settings/users/login", resource_class_kwargs={'service': service})


        # Supplier management users #
        from api import supplier_user_api
        self._api.add_resource(supplier_user_api.SupplierUsers, "/suppliers/users",
                               resource_class_kwargs={'service': service})    # fetch all supplier users
        self._api.add_resource(supplier_user_api.SupplierUser, "/suppliers/users/<string:user_id>",
                               resource_class_kwargs={'service': service})    # fetch supplier user by user id
        self._api.add_resource(supplier_user_api.ChangeSupplierUserPassword,
                               "/suppliers/users/password", resource_class_kwargs={'service': service})    # change supplier user password
        self._api.add_resource(supplier_user_api.DropdownUsersList, "/suppliers/users/dropdown",
                               resource_class_kwargs={'service': service})  # fetch all supplier users for dropdown
        self._api.add_resource(supplier_user_api.UserSupplierMappingGet,
                               "/suppliers/users/user-brand/mapping", resource_class_kwargs={"service": service})    # fetch, update and delete user brand mapping
        self._api.add_resource(supplier_user_api.UserSuppliersMapping,
                               "/suppliers/users/user-brand/mappings", resource_class_kwargs={"service": service})    # fetch all user brand mappings and create user brand mapping
        self._api.add_resource(supplier_user_api.AllBrandsDropdown,
                               "/suppliers/users/mapped/brands/list", resource_class_kwargs={"service": service}) # fetch all/user specific brands for dropdown
        self._api.add_resource(supplier_user_api.AllBcBrandList,
                               "/suppliers/users/bc-brand/list", resource_class_kwargs={"service": service}) # fetch all bc brands for dropdown
        self._api.add_resource(supplier_user_api.ProductsList,
                               "/suppliers/users/brand/products", resource_class_kwargs={"service": service})  #fetch all the products for given brand ids 
        self._api.add_resource(supplier_user_api.ProductsDropDown,
                               "/suppliers/users/brand/products/dropdown", resource_class_kwargs={"service": service}) #fetch all the products for dropdown
        self._api.add_resource(supplier_user_api.ProductsMapping,
                               "/suppliers/users/brand/products/mapping", resource_class_kwargs={"service": service}) # add, remove products from maaping and show all the mapped products list

        ## SUPPLIERS DASHBOARD APIS ##
        self._api.add_resource(supplier_user_api.SuppliersDashboardSummary, "/suppliers/dashboard/summary", # Suppliers dashboard for Summary orders, earnings
                               resource_class_kwargs={"service": service})
        self._api.add_resource(supplier_user_api.SuppliersDashboardTopProducts, "/suppliers/dashboard/top-products", # Suppliers dashboard for Top 10 products
                               resource_class_kwargs={"service": service})
        self._api.add_resource(supplier_user_api.SuppliersDashboardTopStates, "/suppliers/dashboard/top-states", # Suppliers dashboard for Top 10 states
                               resource_class_kwargs={"service": service})
        self._api.add_resource(supplier_user_api.SupplierAggregatedProductReport, "/suppliers/sold-products/state-wise-stats/aggregated-product-report",
                               resource_class_kwargs={"service": service})

        ## SUPPLIERS SOLD PRODUCTS APIS ##
        self._api.add_resource(supplier_user_api.SupplierProducts, "/suppliers/products", resource_class_kwargs={"service": service})
        self._api.add_resource(supplier_user_api.SoldProductSummarySuppliers, "/suppliers/sold-products/state-wise-stats",
                               resource_class_kwargs={"service": service}) # Done #
        self._api.add_resource(supplier_user_api.VariantSalesByStatesSuppliers, "/suppliers/sold-products/state-wise-stats/states",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(supplier_user_api.ProductSalesSuppliers, "/suppliers/product-sales",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(supplier_user_api.AllStatesForFilterSuppliers, "/suppliers/sold-products/state-wise-stats/allstates",
                               resource_class_kwargs={"service": service}) # Done #
        self._api.add_resource(supplier_user_api.SoldProductSkuReportSuppliers, "/suppliers/sold-products/state-wise-stats/variants",
                               resource_class_kwargs={"service": service})
        
        # supplier app unused apis
        self._api.add_resource(supplier_user_api.SoldProductPriceReportSuppliers, "/suppliers/sold-products/state-wise-stats/price-report",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(supplier_user_api.VariantSalesByCustomersSuppliers, "/suppliers/sold-products/state-wise-stats/customers",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(supplier_user_api.ProductPerformanceTableSuppliers, "/suppliers/sold-products/state-wise-stats/product-performance",
                               resource_class_kwargs={"service": service}) # Done #
        

        ## CMS categories ##
        from api import cms_api
        self._api.add_resource(cms_api.Cms, "/products/categories/<string:tenant_id>/<string:store_id>/data",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(cms_api.CmsCategoryChild, "/products/categories/child/<string:id>",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(cms_api.CreateCms, "/products/categories/create",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(cms_api.CmsVersions, "/products/categories/<string:id>/version",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(cms_api.CmsVersionsOperation, "/products/categories/<string:id>/versionids",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(cms_api.CmsVersionData, "/products/categories/<string:id>/versiondata",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(cms_api.BCCategories, "/products/categories/sync",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(cms_api.SyncAllSubNavigations, "/products/categories/navigation/script",
                               resource_class_kwargs={'service': service})  # Sync BC categories
        self._api.add_resource(cms_api.CategoryImageOperation, "/products/categories/images",
                               resource_class_kwargs={'service': service})  # store and get images to server
        self._api.add_resource(cms_api.CmsCategoriesTreeView, "/products/cms-categories/treeview", 
                               resource_class_kwargs={'service': service})
        #script for adding subcatgeory flag
        self._api.add_resource(cms_api.CmsSubcategory, "/admin/cms/data",
                               resource_class_kwargs={"service": service})
        ## CMS categories ##

        ##CMS Brands
        from api import cms_brands_api
        self._api.add_resource(cms_brands_api.CmsBrands, "/products/brands/<string:tenant_id>/<string:store_id>/data",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(cms_brands_api.CmsBrandVersions, "/products/brands/<string:id>/version",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(cms_brands_api.CmsBrandsVersionsOperation, "/products/brands/<string:id>/versionids",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(cms_brands_api.CmsBrandVersionData, "/products/brands/<string:id>/versiondata",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(cms_brands_api.CmsBrandImageOperation, "/products/brands/images",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(cms_brands_api.BCBrands, "/products/brands/sync",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(cms_brands_api.BrandPurchaserMapping, "/products/brands/purchaser/mapping",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(cms_brands_api.BrandPurchaserMappingDetails, "/products/brands/purchaser/mapping/<string:email_id>",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(cms_brands_api.BrandPurchaserMappingPurchaserDropdown, "/products/brands/purchaser/mapping/purchaser/dropdown",
                               resource_class_kwargs={'service': service})
        self._api.add_resource(cms_brands_api.BrandPurchaserMappingBrandDropdown, "/products/brands/purchaser/mapping/brand/dropdown",
                               resource_class_kwargs={'service': service})
        # self._api.add_resource(cms_brands_api.BCBrands, "/admin/cms/brands/sync",
        #                        resource_class_kwargs={'service': service})

        ##CMS Brands

        # ROLES #
        from api import user_roles
        self._api.add_resource(user_roles.Roles, "/settings/users/roles",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(user_roles.Role, "/settings/users/roles/<string:role_id>",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(user_roles.ConvertRoles, "/convert/roles",
                               resource_class_kwargs={"service": service})

        from api import role_permissions
        self._api.add_resource(role_permissions.Permissions,
                               "/settings/users/roles/permissions", resource_class_kwargs={"service": service})

        # JOBS #
        from api import jobs_api
        self._api.add_resource(jobs_api.Jobs,
                               "/settings/jobs", resource_class_kwargs={"service": service})
        self._api.add_resource(jobs_api.Job,
                               "/settings/job", resource_class_kwargs={"service": service})

        # API TOKEN #
        from api import api_tokens, columns_api
        self._api.add_resource(api_tokens.Tokens,
                               "/settings/API/tokens", resource_class_kwargs={"service": service})
        self._api.add_resource(jobs_api.JobDetails,
                               "/settings/job/details", resource_class_kwargs={"service": service})
        self._api.add_resource(columns_api.Columns,
                               "/settings/columns", resource_class_kwargs={"service": service})
        #### SETTINGS APIS ####

        #### BIGCOMMERCE WBPAGES TEST ####

        #### BIGCOMMERCE WBPAGES TEST ####

        #### Analytics API ####
        from api import analytics_api
        self._api.add_resource(analytics_api.ProductSales, "/analytics/product-sales",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.ProductInOrderConsignment, "/analytics/product/consignment",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.ProductList, "/analytics/product",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.ProductInfo, "/analytics/product/information",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.VariantReport, "/analytics/variant/report",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.SoldProductPriceReport, "/analytics/sold-products/state-wise-stats/price-report",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.SoldProductSummary, "/analytics/sold-products/state-wise-stats",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.SoldProductSkuReport, "/analytics/sold-products/state-wise-stats/variants",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.VariantSalesByStates, "/analytics/sold-products/state-wise-stats/states",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.VariantSalesByCustomers, "/analytics/sold-products/state-wise-stats/customers",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.AllStatesForFilter, "/analytics/sold-products/state-wise-stats/allstates",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.ProductPerformanceTable, "/analytics/sold-products/state-wise-stats/product-performance",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.ProductReportDetails, "/analytics/sold-products/details/aggregated-product-report",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.AggregatedProductReport, "/analytics/sold-products/state-wise-stats/aggregated-product-report",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.ReorderRateReport, "/analytics/sold-products/reorder-rate-report",
                               resource_class_kwargs={"service": service})
        # self._api.add_resource(analytics_api.RevenueReport, "/analytics/sold-products/revenue-report",
        #                        resource_class_kwargs={"service": service})
        # self._api.add_resource(analytics_api.QuantitySoldReport, "/analytics/sold-products/quantity-sold-report",
                            #    resource_class_kwargs={"service": service})
        # self._api.add_resource(analytics_api.NewCustomersTable, "/analytics/sold-products/new-customers-table",
        #                        resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.NewCustomersReport, "/analytics/sold-products/new-customers-report",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.ProductPriceReportOrderDetails, "/analytics/sold-products/state-wise-stats/price-report/order-details",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.ReplenishmentReport, "/analytics/sold-products/replenishment-report",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.AnalyticsReplenishmentOutOfStockOccurenceDetails,
                               "/analytics/sold-products/products/out-of-stock/occurence/<string:parent_sku>/details", resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.AnalyticsZohoReturnsData,
                               "/analytics/sold-products/zoho/returns/data", resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.AnalyticsZohoReturnsParentData,
                               "/analytics/sold-products/zoho/returns/parent/data", resource_class_kwargs={"service": service})

        self._api.add_resource(analytics_api.AnalyticsDashboardSummary, "/analytics/dashboard/summary", # Analytics dashboard for Summary orders, earnings
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.AnalyticsDashboardTopProducts, "/analytics/dashboard/top-products", # Analytics dashboard for Top 10 products
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.AnalyticsDashboardTopCustomers, "/analytics/dashboard/top-customers", # Analytics dashboard for Top 10 customers
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.AnalyticsDashboardTopStates, "/analytics/dashboard/top-states", # Analytics dashboard for Top 10 states
                               resource_class_kwargs={"service": service})

        self._api.add_resource(analytics_api.ConsignmentTopProducts, "/analytics/dashboard/consignments/top-products", # Analytics consignment Top 10 products
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.OnHoldTopProducts, "/analytics/dashboard/on-hold/top-products", # Analytics on hold Top 10 products
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.TopDistributors, "/analytics/dashboard/top-distributors", # Analytics Top 5 distributors
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.ConsignmentSummary, "/analytics/dashboard/consignment-summary", # Analytics consignment summary
                               resource_class_kwargs={"service": service})
        self._api.add_resource(analytics_api.ConsignmentTopCustomers, "/analytics/dashboard/consignments/top-customers", # Analytics consignment Top customers
                               resource_class_kwargs={"service": service})

        self._api.add_resource(analytics_api.TopSalesRep, "/analytics/top/sales-rep",
                               resource_class_kwargs={"service": service})

        ############### PROFITABILITY #####################
        from api import customers_profitability_api
        self._api.add_resource(customers_profitability_api.CustomersProfitability, "/analytics/profitability/customers", resource_class_kwargs={"service": service})

        self._api.add_resource(customers_profitability_api.CustomerGroupsProfitability, "/analytics/profitability/customer-groups", resource_class_kwargs={"service": service})
        # self._api.add_resource(customers_profitability_api.TopCustomerGroupsProfitability, "/analytics/profitability/top-customer-groups", resource_class_kwargs={"service": service})

        self._api.add_resource(customers_profitability_api.ReportSummary, "/analytics/profitability/report-summary", resource_class_kwargs={"service": service})

        self._api.add_resource(customers_profitability_api.CustomerTypesProfitability, "/analytics/profitability/customer-types", resource_class_kwargs={"service": service})

        self._api.add_resource(customers_profitability_api.CustomerRepsProfitability, "/analytics/profitability/customer-reps", resource_class_kwargs={"service": service})

        self._api.add_resource(customers_profitability_api.PurchasersProfitability, "/analytics/profitability/purchasers", resource_class_kwargs={"service": service})

        self._api.add_resource(customers_profitability_api.ProductWiseProfitability, "/analytics/profitability/product-wise", resource_class_kwargs={"service": service})

        self._api.add_resource(customers_profitability_api.ClassificationProfitability, "/analytics/profitability/classification", resource_class_kwargs={"service": service})

        self._api.add_resource(customers_profitability_api.SuppliersProfitability, "/analytics/profitability/suppliers", resource_class_kwargs={"service": service})

        self._api.add_resource(customers_profitability_api.OrdersProfitability, "/analytics/profitability/orders", resource_class_kwargs={"service": service})

        self._api.add_resource(customers_profitability_api.BrandsProfitability, "/analytics/profitability/brands", resource_class_kwargs={"service": service})

        self._api.add_resource(customers_profitability_api.BrandsDropdown, "/analytics/profitability/brands/dropdown", resource_class_kwargs={"service": service})

        self._api.add_resource(customers_profitability_api.PurchasersDropdown, "/analytics/profitability/purchasers/dropdown", resource_class_kwargs={"service": service})

        self._api.add_resource(customers_profitability_api.PurchaserByBrandProfitability, "/analytics/profitability/purchaser/by/brand", resource_class_kwargs={"service": service})

        self._api.add_resource(customers_profitability_api.OrdersWiseProfitability, "/analytics/profitability/orders/wise", resource_class_kwargs={"service": service})

        self._api.add_resource(customers_profitability_api.ProfitabilityCharts, "/analytics/profitability/charts", resource_class_kwargs={"service": service})

        ############# profitability CSV MAIL APIS #############
        self._api.add_resource(customers_profitability_api.CustomersProfitabilityCsv, "/analytics/profitability/customers/csv", resource_class_kwargs={"service": service})
        self._api.add_resource(customers_profitability_api.ProductWiseProfitabilityCsv, "/analytics/profitability/product-wise/csv", resource_class_kwargs={"service": service})
        self._api.add_resource(customers_profitability_api.SuppliersProfitabilityCsv, "/analytics/profitability/suppliers/csv", resource_class_kwargs={"service": service})
        self._api.add_resource(customers_profitability_api.ClassificationProfitabilityCsv, "/analytics/profitability/classification/csv", resource_class_kwargs={"service": service})
        self._api.add_resource(customers_profitability_api.OrdersProfitabilityCsv, "/analytics/profitability/orders/csv", resource_class_kwargs={"service": service})
        self._api.add_resource(customers_profitability_api.BrandsProfitabilityCsv, "/analytics/profitability/brands/csv", resource_class_kwargs={"service": service})

        ############### SHIPPING REPORTS #####################
        from api import shipping_report_api
        self._api.add_resource(shipping_report_api.CustomerGroupsShippingReport, "/analytics/shipping-report/customer-groups", resource_class_kwargs={"service": service})

        self._api.add_resource(shipping_report_api.CustomerTypesShippingReport, "/analytics/shipping-report/customer-types", resource_class_kwargs={"service": service})

        self._api.add_resource(shipping_report_api.SalesRepShippingReport, "/analytics/shipping-report/sales-rep", resource_class_kwargs={"service": service})
        self._api.add_resource(shipping_report_api.SalesRepTypeShippingReport, "/analytics/shipping-report/sales-rep-type", resource_class_kwargs={"service": service})
        self._api.add_resource(shipping_report_api.ShippingReportCharts, "/analytics/shipping-report/charts", resource_class_kwargs={"service": service})

        ############### COUPON CODE DISCOUNT REPORTS #####################
        from api import coupon_discount_report_api
        self._api.add_resource(coupon_discount_report_api.CustomerGroupsCouponDiscountReport, "/analytics/coupon-discount/report/customer-groups", resource_class_kwargs={"service": service})
        self._api.add_resource(coupon_discount_report_api.CustomerTypesCouponDiscountReport, "/analytics/coupon-discount/report/customer-types", resource_class_kwargs={"service": service})
        self._api.add_resource(coupon_discount_report_api.SalesRepCouponDiscountReport, "/analytics/coupon-discount/report/sales-rep", resource_class_kwargs={"service": service})
        self._api.add_resource(coupon_discount_report_api.SalesRepTypeCouponDiscountReport, "/analytics/coupon-discount/report/sales-rep-type", resource_class_kwargs={"service": service})
        self._api.add_resource(coupon_discount_report_api.CouponCodeDiscountReport, "/analytics/coupon-discount/report/coupon-code", resource_class_kwargs={"service": service})
        self._api.add_resource(coupon_discount_report_api.CouponDiscountCharts, "/analytics/coupon-discount/charts", resource_class_kwargs={"service": service})

        ############### PRODUCT DISCOUNT REPORTS #####################
        from api import order_discount_report_api
        self._api.add_resource(order_discount_report_api.CustomerGroupsOrderDiscountReport, "/analytics/order-discount/report/customer-groups", resource_class_kwargs={"service": service})
        self._api.add_resource(order_discount_report_api.CustomerTypesOrderDiscountReport, "/analytics/order-discount/report/customer-types", resource_class_kwargs={"service": service})
        self._api.add_resource(order_discount_report_api.SalesRepOrderDiscountReport, "/analytics/order-discount/report/sales-rep", resource_class_kwargs={"service": service})
        self._api.add_resource(order_discount_report_api.SalesRepTypeOrderDiscountReport, "/analytics/order-discount/report/sales-rep-type", resource_class_kwargs={"service": service})
        self._api.add_resource(order_discount_report_api.OrderDiscountCharts, "/analytics/order-discount/charts", resource_class_kwargs={"service": service})

        ############### Filters API #####################
        from api import profitability_filters
        self._api.add_resource(profitability_filters.ProfitabilityCustomerGroups,  "/analytics/profitability/filters/customer-groups", resource_class_kwargs={'service': service})
        self._api.add_resource(profitability_filters.ProfitabilityCustomerRepresentative, "/analytics/profitability/filters/customer-reps", resource_class_kwargs={'service': service})
        self._api.add_resource(profitability_filters.ProfitabilityProductClassifications,"/analytics/profitability/filters/classifications", resource_class_kwargs={"service": service})
        self._api.add_resource(profitability_filters.ProfitabilityProductSuppliers,"/analytics/profitability/filters/suppliers", resource_class_kwargs={"service": service})
        self._api.add_resource(profitability_filters.ProfitabilityProducts,"/analytics/profitability/filters/products", resource_class_kwargs={"service": service})
        self._api.add_resource(profitability_filters.ProfitabilityCustomerRepresentativeTypes,"/analytics/profitability/filters/customer-rep-types", resource_class_kwargs={"service": service})

        logger.info("Exiting API init ...")

        #### Analytics POSTGRESQL APIS ####
        from api import replenishment_api, inventory_api, product_limit_api
        self._api.add_resource(replenishment_api.ReplenishmentData,
                               "/analytics/replenishment/query/data", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.ReplenishmentProductsData,
                               "/analytics/replenishment/products/data", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.ReplenishmentFilters,
                               "/analytics/replenishment/query/filters", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.PostGreSql,
                               "/analytics/customquery/query/data", resource_class_kwargs={"service": service})

        self._api.add_resource(replenishment_api.ReplenishmentAggregateData,
                               "/analytics/replenishment/aggregate/data", resource_class_kwargs={"service": service})
        
        self._api.add_resource(replenishment_api.ReplenishmentProductsInstockNotifyDetails,
                               "/analytics/replenishment/products/instock-notify/<string:parent_sku>/details", resource_class_kwargs={"service": service})
        
        self._api.add_resource(replenishment_api.ReplenishmentOutOfStockOccurenceDetails,
                               "/analytics/replenishment/products/out-of-stock/occurence/<string:parent_sku>/details", resource_class_kwargs={"service": service})
        
        self._api.add_resource(replenishment_api.ZohoReturnsData,
                               "/analytics/replenishment/zoho/returns/data", resource_class_kwargs={"service": service})
        
        self._api.add_resource(replenishment_api.ZohoReturnsParentData,
                               "/analytics/replenishment/zoho/returns/parent/data", resource_class_kwargs={"service": service})

        self._api.add_resource(replenishment_api.DailySoldReplenishmentAggregateData,
                               "/analytics/replenishment/daily-sold/data", resource_class_kwargs={"service": service})

        self._api.add_resource(replenishment_api.DailySoldReplenishmentAggregateDataCsv,
                               "/analytics/replenishment/daily-sold/csvdata", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.DiscontinuedProductsDataCsv,
                               "/analytics/replenishment/discontinued/products/csvdata", resource_class_kwargs={"service": service})

        self._api.add_resource(replenishment_api.ReplenishmentAggregateChild,
                               "/analytics/replenishment/aggregate/child/data", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.ReplenishmentAggregateDataCsv,
                               "/analytics/replenishment/aggregate/csvdata", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.ReplenishmentAggregateCsv,
                               "/analytics/replenishment/aggregate/csv", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.ReplenishmentDashboardDataCsv,
                               "/analytics/replenishment/dashboard/csv", resource_class_kwargs={"service": service})

        self._api.add_resource(replenishment_api.UserSupplierMapping,
                               "/analytics/replenishment/supplier/mapping", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.SupplierList,
                               "/analytics/replenishment/supplier/data", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.AllUsersDropDown,
                               "/analytics/replenishment/users/dropdown", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.MappedUsers,
                               "/analytics/replenishment/mapped/users", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.UserMapping,
                               "/analytics/replenishment/sppliers", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.TableColumnsListing,
                               "/analytics/replenishment/columns/listing", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.TableColumns,
                               "/analytics/replenishment/columns", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.BackOrderQtySkuValidate,
                               "/analytics/replenishment/backorder/sku/validate", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.BackOrderQty,
                               "/analytics/replenishment/backorder/sku/qty", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.BackOrderQtyChange,
                               "/analytics/replenishment/backorder/sku/qty/<string:id>", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.SafetyStockQtySkuValidate,
                               "/analytics/replenishment/safetystock/sku/validate", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.SafetyStockQty,
                               "/analytics/replenishment/safetystock/sku/qty", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.SafetyStockQtyChange,
                               "/analytics/replenishment/safetystock/sku/qty/<string:id>", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.Dashboard,
                               "/analytics/replenishment/dashboard", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.SaveDashboardData,
                               "/analytics/replenishment/dashboard/save/<string:parent_sku>", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.DashboardFilters,
                               "/analytics/replenishment/dashboard/filters", resource_class_kwargs={"service": service})
        self._api.add_resource(inventory_api.InventoryReport,
                               "/analytics/replenishment/inventory/report", resource_class_kwargs={"service": service})
        self._api.add_resource(product_limit_api.ProductLimitReport,
                               "/analytics/replenishment/product/limit/report", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.UniqueClassifications,
                               "/analytics/replenishment/unique/classifications", resource_class_kwargs={"service": service})
        self._api.add_resource(replenishment_api.ReplenishmentClassifications,
                               "/analytics/replenishment/classifications", resource_class_kwargs={"service": service})

        from api import classified_as_api
        self._api.add_resource(classified_as_api.ReplenishmentClassifiedAs,
                               "/analytics/replenishment/classified-as", resource_class_kwargs={"service": service})
        self._api.add_resource(classified_as_api.ReplenishmentClassifiedAsDetails,
                               "/analytics/replenishment/classified-as/<string:id>", resource_class_kwargs={"service": service})

        from api import webvisitor_api
        self._api.add_resource(webvisitor_api.WebVisitors,
                               "/analytics/web-visitors", resource_class_kwargs={"service": service})

        from api import customer_churn_api
        self._api.add_resource(customer_churn_api.CustomerChurnReport,
                               "/analytics/customer-churn-report", resource_class_kwargs={"service": service})

       #### WHATSAPP POSTGRES APIS
        from api import whatsapp_api
        self._api.add_resource(whatsapp_api.ReceiveMessage,
                               "/whatsapp/receive/message", resource_class_kwargs={"service": service})
        self._api.add_resource(whatsapp_api.SendMessage,
                               "/whatsapp/send/message", resource_class_kwargs={"service": service})
        self._api.add_resource(whatsapp_api.GetAllMessages,
                               "/whatsapp/get/messages/list", resource_class_kwargs={"service": service})
        self._api.add_resource(whatsapp_api.GetMessageDetail,
                               "/whatsapp/get/message/detail", resource_class_kwargs={"service": service})
        self._api.add_resource(whatsapp_api.AssignRepresentative,
                               "/whatsapp/change/representative", resource_class_kwargs={"service": service})
        self._api.add_resource(whatsapp_api.GetVendorRepresentativeList,
                               "/whatsapp/get/vendor/representative/list", resource_class_kwargs={"service": service})
        self._api.add_resource(whatsapp_api.GetVendorFromID,
                               "/whatsapp/get/vendor", resource_class_kwargs={"service": service})
        self._api.add_resource(whatsapp_api.SaveDocuments,
                               "/whatsapp/documents/data", resource_class_kwargs={"service": service})

        # self._api.add_resource(postgre_db_api.SendMessage,
        #                        "/analytics/whatsapp/sendMessage", resource_class_kwargs={"service": service})
        # self._api.add_resource(postgre_db_api.ReceiveMessage,
        #                        "/analytics/whatsapp/receiveMessage", resource_class_kwargs={"service": service})


        #### Analytics POSTGRESQL APIS ####

        from api import po_api
        self._api.add_resource(po_api.POReorderAPI,
                               "/analytics/replenishment/po/reorder", resource_class_kwargs={"service": service})
        self._api.add_resource(po_api.POReorderDeleteRecord,
                               "/analytics/replenishment/po/reorder/delete/record", resource_class_kwargs={"service": service})
        self._api.add_resource(po_api.POReorderCsv,
                               "/analytics/replenishment/po/reorder/csvdata", resource_class_kwargs={"service": service})

        from api import purchaser_api
        ############# Purchasers report api ##############
        self._api.add_resource(purchaser_api.NoSoldProductsAnalyticsData,
                               "/purchaser/analytics/no-sold/data", resource_class_kwargs={"service": service})
        self._api.add_resource(purchaser_api.PurchaserMappedUsers,
                               "/purchaser/analytics/mapped/users", resource_class_kwargs={"service": service})
        self._api.add_resource(purchaser_api.NoSoldProductsAnalyticsDataCsv,
                               "/purchaser/analytics/no-sold/csvdata", resource_class_kwargs={"service": service})

        ###### supplier list apis #########
        from api import appbuilder_api
        self._api.add_resource(appbuilder_api.AppBuilderApi,
                               "/settings/appbuilder/create/app", resource_class_kwargs={"service": service})
        self._api.add_resource(appbuilder_api.SubSectionBuilderApi,
                               "/settings/appbuilder/<string:parent_section>/create/subsection", resource_class_kwargs={"service": service})
        self._api.add_resource(appbuilder_api.LeafBuilderApi,
                               "/settings/appbuilder/<string:grand_parent>/<string:parent>/create/leafsection", resource_class_kwargs={"service": service})
        self._api.add_resource(appbuilder_api.AppBuilderImageOperation,
                               "/settings/appbuilder/images", resource_class_kwargs={'service': service})
        #  from api import bc_api
        self._api.add_resource(appbuilder_api.GetBCData, "/getBCData/api",
                               resource_class_kwargs={"service": service})
        self._api.add_resource(appbuilder_api.GetZohoformData, "/getZohoformData/api",
                               resource_class_kwargs={"service": service})

        # Sidebar update ...
        from api import sidebar
        self._api.add_resource(sidebar.App,
                               "/app/menu", resource_class_kwargs={"service": service})
        # Sidebar update ...

        from api import tag_api
        self._api.add_resource(tag_api.TagAPI, "/analytics/replenishment/tags",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(tag_api.ProductTagAPI, "/analytics/replenishment/tags/products",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(tag_api.ProductValidate, "/analytics/replenishment/tags/products/validate",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(tag_api.ProductsAdd, "/analytics/replenishment/tags/products/add",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(tag_api.ProductsTagAPI, "/analytics/replenishment/tags/productslist",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(tag_api.GenericTags, "/tags",
                               resource_class_kwargs={'service': service})

        from api import redirects_api
        self._api.add_resource(redirects_api.AllRedirects, "/redirects/<string:tenant_id>/<string:store_id>/data",
                               resource_class_kwargs={"service": service})

        # customer with no orders ...
        from api import customer_orders
        self._api.add_resource(customer_orders.CustomerNoOrders, "/analytics/customers/no-orders",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(customer_orders.CustomerOrderLongAgo, "/analytics/customers/no-recent-orders",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(customer_orders.ActiveCustomersWithNoRecentOrders, "/analytics/customers/active/no-orders",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(customer_orders.CustomerSessionAnalysis, "/analytics/customers/<customer_id>/session-analysis",
                               resource_class_kwargs={"service": service})

        self._api.add_resource(customer_orders.CustomerIPInfo, "/analytics/customers/<customer_id>/ip-info",
                               resource_class_kwargs={"service": service})


        from api import project_api
        self._api.add_resource(project_api.Boards, "/agile/boards", resource_class_kwargs={"service": service}) # Project Boards

        self._api.add_resource(project_api.BusinessUnits, "/agile/businessunits", resource_class_kwargs={"service": service}) # Project Business unit
        self._api.add_resource(project_api.BusinessUnitDetails, "/agile/businessunits/<string:id>", resource_class_kwargs={"service": service}) # Project Business unit

        self._api.add_resource(project_api.ProjectListing, "/agile/projects", resource_class_kwargs={"service": service}) # List the projects
        self._api.add_resource(project_api.ProjectDetails, "/agile/projects/<string:id>", resource_class_kwargs={"service": service}) # particular project
        self._api.add_resource(project_api.FevoriteProject, "/agile/projects/<string:project_id>/fevorite", resource_class_kwargs={"service": service}) # opration for fevorite project

        self._api.add_resource(project_api.GlobalPermittedMembers, "/agile/projects/permitted/members", resource_class_kwargs={"service": service}) # project members
        self._api.add_resource(project_api.GlobalMembers, "/agile/projects/members", resource_class_kwargs={"service": service}) # project members
        self._api.add_resource(project_api.GlobalMembersProjectList, "/agile/projects/members/projectlist", resource_class_kwargs={"service": service}) # project list for the members
        self._api.add_resource(project_api.GlobalMembersDelete, "/agile/projects/members/<string:username>", resource_class_kwargs={"service": service}) # project global members delete
        self._api.add_resource(project_api.ProjectSpecificMembersDropDown, "/agile/projects/<string:project_id>/dropdown/members", resource_class_kwargs={"service": service}) # project members

        self._api.add_resource(project_api.ProjectMembers, "/agile/projects/<string:project_id>/members", resource_class_kwargs={"service": service}) # project members
        self._api.add_resource(project_api.ProjectMembersDetails, "/agile/projects/<project_id>/members/<id>", resource_class_kwargs={"service": service}) # project members
        self._api.add_resource(project_api.ProjectModules, "/agile/projects/<string:project_id>/modules", resource_class_kwargs={"service": service}) # modules of the projects
        self._api.add_resource(project_api.ProjectModuleDetails, "/agile/projects/<string:project_id>/modules/<string:id>", resource_class_kwargs={"service": service}) # modules of the projects
        self._api.add_resource(project_api.ProjectColumns, "/agile/projects/<string:project_id>/columns", resource_class_kwargs={"service": service}) # columns of the projects
        self._api.add_resource(project_api.ProjectColumnDetails, "/agile/projects/<string:project_id>/columns/<string:id>", resource_class_kwargs={"service": service}) # columns of the projects
        self._api.add_resource(project_api.ProjectCardStatus, "/agile/projects/card-status", resource_class_kwargs={"service": service}) # columns of the projects
        self._api.add_resource(project_api.ProjectCardPriorities, "/agile/projects/card-priorities", resource_class_kwargs={"service": service}) # columns of the projects

        self._api.add_resource(project_api.ProjectCardStatusDetail, "/agile/projects/card-status/<string:id>", resource_class_kwargs={"service": service}) # columns of the projects
        self._api.add_resource(project_api.ProjectCardPrioritiesDetail, "/agile/projects/card-priorities/<string:id>", resource_class_kwargs={"service": service}) # columns of the projects

        self._api.add_resource(project_api.Teams, "/agile/projects/teams", resource_class_kwargs={"service": service})
        self._api.add_resource(project_api.TeamDetails, "/agile/projects/teams/<string:team_id>", resource_class_kwargs={"service": service})
        self._api.add_resource(project_api.AddTeams, "/agile/projects/teams/add/<string:project_id>", resource_class_kwargs={"service": service})

        self._api.add_resource(project_api.GlobalCustomFields, "/agile/projects/custom-fields", resource_class_kwargs={"service": service}) # columns of the projects
        self._api.add_resource(project_api.GlobalCustomFieldDetail, "/agile/projects/custom-fields/<string:id>", resource_class_kwargs={"service": service}) # columns of the projects

        self._api.add_resource(project_api.DefaultColumns, "/agile/projects/default/columns", resource_class_kwargs={"service": service}) # columns of the projects
        self._api.add_resource(project_api.DefaultColumnDetails, "/agile/projects/default/columns/<string:id>", resource_class_kwargs={"service": service}) # columns of the projects


        self._api.add_resource(project_api.ProjectCustomFields, "/agile/projects/<string:project_id>/custom-fields", resource_class_kwargs={"service": service}) # columns of the projects
        self._api.add_resource(project_api.ProjectCustomFieldDetail, "/agile/projects/<string:project_id>/custom-fields/<string:id>", resource_class_kwargs={"service": service}) # columns of the projects

        self._api.add_resource(project_api.ProjectCardCustomField, "/agile/projects/<string:project_id>/<string:card_id>/custom-fields", resource_class_kwargs={"service": service}) # columns of the projects
        self._api.add_resource(project_api.ProjectCardCustomFieldDetail, "/agile/projects/<string:project_id>/<string:card_id>/custom-fields/<string:id>", resource_class_kwargs={"service": service}) # columns of the projects


        self._api.add_resource(project_api.ProjectCards, "/agile/projects/<project_id>/modules/<id>/cards", resource_class_kwargs={"service": service}) # to create card
        self._api.add_resource(project_api.ProjectCardsList, "/agile/projects/<project_id>/cards", resource_class_kwargs={"service": service}) # to get cards data for borad screen
        self._api.add_resource(project_api.ProjectAllCards, "/agile/projects/<project_id>/cards-list", resource_class_kwargs={"service": service}) # to get all cards of project for assign parent or child cards
        self._api.add_resource(project_api.ProjectCardsListView, "/agile/projects/<project_id>/cards/listview", resource_class_kwargs={"service": service}) # to get cards data for list view
        self._api.add_resource(project_api.ProjectStructure, "/agile/projects/<project_id>/structure", resource_class_kwargs={"service": service}) # to get borad strecture
        self._api.add_resource(project_api.ProjectCard, "/agile/projects/<project_id>/cards/<id>", resource_class_kwargs={"service": service}) # to do operations on card
        self._api.add_resource(project_api.CreateCardComments, "/agile/projects/cards/<id>/comments", resource_class_kwargs={"service": service}) # to create card comments
        self._api.add_resource(project_api.CardComment, "/agile/projects/cards/<card_id>/comments/<id>", resource_class_kwargs={"service": service}) # to do operations on card comments
        self._api.add_resource(project_api.CardHistoryLogs, "/agile/projects/cards/<card_id>/history", resource_class_kwargs={"service": service}) # to do operations on card comments
        self._api.add_resource(project_api.RemoveCardRelationLinks, "/agile/projects/<project_id>/cards/<id>/remove-links", resource_class_kwargs={"service": service}) # to do operations on card
        self._api.add_resource(project_api.ProjectCardDelete, "/agile/projects/<project_id>/delete-cards", resource_class_kwargs={"service": service})

        self._api.add_resource(project_api.ProjectMapping, "/agile/projects/project-mapping/tables", resource_class_kwargs={"service": service}) # to do operations on project mapping
        self._api.add_resource(project_api.StatusMappingDropDown, "/agile/projects/dbtable/<string:db_table_id>/columns", resource_class_kwargs={"service": service}) # to do operations on status mapping
        self._api.add_resource(project_api.ProjectStatusMappingDropDown, "/agile/projects/<project_id>/dbtable", resource_class_kwargs={"service": service}) # to do operations on status mapping
        self._api.add_resource(project_api.ColumnStatusMappingDropDown, "/agile/projects/dbtable/<string:db_table_id>/column/<string:column>/mapping", resource_class_kwargs={"service": service}) # to do operations on status mapping
        self._api.add_resource(project_api.StatusMapping, "/agile/projects/<string:project_id>/column-mapping/<string:column_id>", resource_class_kwargs={"service": service}) # to do operations on status mapping
        self._api.add_resource(project_api.CustomFieldStatusMapping, "/agile/projects/<string:project_id>/custom-field-mapping/<string:field_id>", resource_class_kwargs={"service": service}) # api to map the custom field with column

        self._api.add_resource(project_api.TaskReport, "/agile/projects/task-report", resource_class_kwargs={"service": service})
        self._api.add_resource(project_api.MembersReport, "/agile/projects/members-report", resource_class_kwargs={"service": service})
        self._api.add_resource(project_api.AllCards, "/agile/projects/all-cards", resource_class_kwargs={"service": service})
        self._api.add_resource(project_api.TimeSheet, "/agile/projects/timesheet", resource_class_kwargs={"service": service})
        self._api.add_resource(project_api.Matrix, "/agile/projects/matrix", resource_class_kwargs={"service": service})
        self._api.add_resource(project_api.GlobalFilterSave, "/agile/projects/global/filters/save", resource_class_kwargs={"service": service})
        self._api.add_resource(project_api.ProjectsDropdown, "/agile/projects/dropdown", resource_class_kwargs={"service": service})

        self._api.add_resource(project_api.TimeLog, "/agile/projects/<card_id>/time-log", resource_class_kwargs={"service": service})
        self._api.add_resource(project_api.TimeLogUpdate, "/agile/projects/<card_id>/time-log/<time_log_id>", resource_class_kwargs={"service": service})

        self._api.add_resource(project_api.GanttChartData, "/agile/projects/<project_id>/gantt-chart", resource_class_kwargs={"service": service})
        self._api.add_resource(project_api.GanttChartDataSorting, "/agile/projects/<project_id>/gantt-chart/sorting", resource_class_kwargs={"service": service})

        self._api.add_resource(project_api.ProjectCardAttachments,
                               "/agile/projects/<string:project_id>/attachments/<string:card_id>", resource_class_kwargs={"service": service})
        self._api.add_resource(project_api.ProjectCardAttachment,
                               "/agile/projects/<string:project_id>/attachments/<string:card_id>/<string:file_name>", resource_class_kwargs={"service": service})
        self._api.add_resource(project_api.CardAttachmentDetails, "/agile/projects/cards/<string:id>/attachments", resource_class_kwargs={"service": service})

        from api import bulk_orders_api
        self._api.add_resource(bulk_orders_api.BulkOrders, "/bulkOrders/productInfo", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.BulkOrderInfo, "/bulkOrders/productInfo/<string:order_id>", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.PreorderProductInfo, "/preorder/productInfo/<string:product_id>", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.BulkOrdersProductList, "/bulkOrders/productList", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.BOProductVariantDashbord, "/bulkOrders/product/<string:bop_id>/variants", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.BOProductBCOrders, "/bulkOrders/product/<string:bop_id>/bc/orders", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.BOProductPOOrders, "/bulkOrders/product/<string:bop_id>/po/orders", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.BulkProductDropdown, "/bulkOrders/dropdown/products", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.PurchaseOrders, "/purchase/orders", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.PurchaseOrderInfo, "/purchase/orders/<string:po_id>", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.PriceListsData, "/purchase/orders/price-lists", resource_class_kwargs={"service": service})
        # self._api.add_resource(bulk_orders_api.PriceListsData, "/purchase/orders/price-lists/<string:price_list_id>/csp", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.PriceListsDropdown, "/purchase/orders/price-lists/dropdown", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.PurchaseOrderCompleted, "/purchase/orders/<string:po_id>/completed", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.PurchaseOrderCustomerDetails, "/purchase/orders/customer/details/<string:customer_id>", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.PurchaseOrderCreate, "/purchase/orders/<string:po_id>/create/order", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.ProductDistribution, "/bulkOrders/productInfo/<string:order_id>/distribution", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.DestributionDetails, "/bulkOrders/productInfo/distribution/<string:distribution_id>", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.ProductDistributionLogs, "/bulkOrders/productInfo/<string:order_id>/distribution/logs", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.ProductBrands, "/bulkOrders/product/brands", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.ProductBrand, "/bulkOrders/product/brand", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.ProductBrandOperation, "/bulkOrders/product/brand/<string:brand_id>", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.ProductBrandlist, "/bulkOrders/product/brandlist", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.CustomerAddresses, "/bulkOrders/purchaseOrders/<string:po_id>/customers/<string:customer_id>/addresses", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.CustomerReport, "/bulkOrders/reports/customers", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.SalesRepresentativeReport, "/bulkOrders/reports/salesrep", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.SalesRepCustomerList, "/bulkOrders/reports/salesrep/customers", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.ByProductReport, "/bulkOrders/reports/products", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.ProductCustomerTrackingReport, "/bulkOrders/reports/product/<string:bop_id>/customer/tracking", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.ProductCustomerTrackingCsv, "/bulkOrders/reports/product/<string:bop_id>/customer/tracking/csv", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.SuggestedCustomerReport, "/bulkOrders/reports/suggested/customers", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.CustomerPurchasedFlavours, "/bulkOrders/reports/product/<string:bop_id>/customer/<string:customer_id>/flavours", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.CustomersStates, "/bulkOrders/reports/product/<string:bop_id>/customers/states", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.GlobalProductsReport, "/bulkOrders/reports/products/global", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.GlobalProductsReportCSV, "/bulkOrders/reports/products/global/csv", resource_class_kwargs={"service": service})
        self._api.add_resource(bulk_orders_api.CreateShippingForBulkOrder, "/purchase/orders/<string:po_id>/shipping/options", resource_class_kwargs={"service": service})

        from api import notification_api
        self._api.add_resource(notification_api.Notifications, "/notifications", resource_class_kwargs={"service": service})
        self._api.add_resource(notification_api.NotificationDetails, "/notifications/<string:notification_id>", resource_class_kwargs={"service": service})
        self._api.add_resource(notification_api.NotificationModules, "/notifications/modules", resource_class_kwargs={"service": service})
        self._api.add_resource(notification_api.NotificationsGroupByDate, "/settings/notifications/groupby/date", resource_class_kwargs={"service": service})
        self._api.add_resource(notification_api.StopEmailNotification, "/settings/notifications/handel/email/notification", resource_class_kwargs={"service": service})

        from api import script_manager_api
        self._api.add_resource(script_manager_api.Scripts, "/storefronts/script/manager/scripts", resource_class_kwargs={"service": service})
        self._api.add_resource(script_manager_api.ScriptDetails, "/storefronts/script/manager/scripts/<string:script_id>", resource_class_kwargs={"service": service})
        self._api.add_resource(script_manager_api.ImportLoyaltyPoints, "/storefronts/script/manager/loyalty/points", resource_class_kwargs={"service": service})
        

    def get_api(self):
        return self._api

