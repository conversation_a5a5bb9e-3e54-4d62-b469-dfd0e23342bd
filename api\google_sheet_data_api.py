from flask import request
import logging
import traceback
from api import APIResource
from schemas.mapping_table import mapping_table_schema, mapping_table_update_schema, update_row_data_schema
logger = logging.getLogger()

class MappingTable(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering MappingTables GET")
        try:
          res = self.service.get_mappingtables_service('spreadsheet').get_all_mapping_tables()
          return res, 200
        finally:
            logger.debug("Exiting MappingTables GET")

    def post_executor(self, request, token_payload, store):
        try:
            req_body = request.get_json(force=True)
            validated_data = mapping_table_schema.validate(req_body)
            res = self.service.get_mappingtables_service('spreadsheet').create_mapping_table(validated_data)
            if res['status'] == 200:                                        
                coll = self.service.get_mappingtables_service('spreadsheet').create_table_data_collection(res['id'], res['table_name'])
                return {"status": res['status'], "message": res['message']}, 200
            else:
                return {"status": res['status'], "message": res['message']}, res['status']
        finally:
            logger.debug("Exiting Table POST")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
        
    def post(self):
        return self.execute_store_request(request, self.post_executor)

class MappingTableCSV(APIResource):
    def post_executor(self, request, token_payload, store, token=None):
        try:            
            req_body = request                         
            res = self.service.get_mappingtables_service('spreadsheet').create_mapping_table_csv(req_body)            
            if res['status'] == 200:                                        
                collection = self.service.get_mappingtables_service('spreadsheet').create_table_data_collection_csv(res)
                return {"status": res['status'], "message": res['message'], "id": str(res['id'])}, 200
            else:
                return {"status": res['status'], "message": res['message']}, res['status']            
        finally:
            logger.debug("Exiting Table CSV POST")

    def put_executor(self, request, token_payload, store):
        try:
            req_body = request.get_json(force=True)                                       
            id = self.service.get_mappingtables_service('spreadsheet').update_mapping_table_csv(req_body)                
            
            if id:
                return {"status": "ok"}, 200
            else:
                return {"message": "Something went wrong at server!!"}, 500
        finally:
            logger.debug("Exiting Table CSV PUT")
            

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
    def put(self):
        return self.execute_store_request(request, self.put_executor)


class MappingTableOperations(APIResource):
    def put_executor(self, request, token_payload, store, mapping_table_id):
        try:
            req_body = request.get_json(force=True)
            validated_data = mapping_table_update_schema.validate(req_body)                
            res = self.service.get_mappingtables_service('spreadsheet').update_mapping_table(validated_data, mapping_table_id) 
            if res['status'] == 200:
                result = self.service.get_mappingtables_service('spreadsheet').update_mapping_table_collection_name(res, mapping_table_id)
                if result:
                    return {"message": result}, 200
                else:
                    return {"message": "Something went wrong at server!!"}, 500
            else:
                return {"message": res['message']}, res['status']
        finally:
            logger.debug("Exiting MappingTable PUT")
    
    def delete_executor(self, request, token_payload, store, mapping_table_id):
        try:      
            query_params = request.args.to_dict()                 
            success = self.service.get_mappingtables_service('spreadsheet').delete_by_id(mapping_table_id, query_params)
            if success:
                return {"status": "ok"}, 200                        
        finally:
            logger.debug("Exiting MappingTable DELETE")

    def get_executor(self, request, token_payload, store, mapping_table_id):
        logger.debug("Entering MappingTables GET")
        try:
          res = self.service.get_mappingtables_service('spreadsheet').get_mapping_table(mapping_table_id)
          return res, 200
        finally:
            logger.debug("Exiting MappingTables GET")

    def put(self, mapping_table_id):
        return self.execute_store_request(request, self.put_executor, mapping_table_id)
    
    def delete(self, mapping_table_id):
        return self.execute_store_request(request, self.delete_executor, mapping_table_id)
    
    def get(self, mapping_table_id):
        return self.execute_store_request(request, self.get_executor, mapping_table_id)

class MappingTableDataOperations(APIResource):
    def post_executor(self, request, token_payload, store, mapping_table_id):
        try:            
            req_body = request.get_json(force=True)                         
            id = self.service.get_mappingtables_data_service('spreadsheet').insert_row_to_table(req_body)                       
            if id:                                                       
                return {"status":"ok", "message": "data inserted successfully"}, 200
            else:
                return {"status": 400, "message": "Something went wrong at server!!"}, 400            
        finally:
            logger.debug("Exiting Insert Row to table POST")

    def put_executor(self, request, token_payload, store, mapping_table_id):
        try:
            req_body = request.get_json(force=True)
            validated_data = update_row_data_schema.validate(req_body)                
            res = self.service.get_mappingtables_data_service('spreadsheet').update_row_data(validated_data)                
            
            if res['status'] == 200:
                return res, 200
            else:
                return res, 500
        finally:
            logger.debug("Exiting Table row data PUT")

    def delete_executor(self, request, token_payload, store, mapping_table_id):
        try:      
            # query_params = request.args.to_dict()
            req_body = request.get_json(force=True)                 
            res = self.service.get_mappingtables_data_service('spreadsheet').delete_row_by_id(req_body)            
            if res['status'] == 200:
                return res, 200  
            else:
                return res, 500                      
        finally:
            logger.debug("Exiting Table row data DELETE")
    
    def get_executor(self, request, token_payload, store, mapping_table_id):
        logger.debug("Entering MappingTables GET")
        try:
          query_params = request.args.to_dict()  
          res = self.service.get_mappingtables_data_service('spreadsheet').get_mapping_table_data(query_params, mapping_table_id)
          if res['status'] == 200:
            return res, 200  
          else:
            return res, 500  
        finally:
            logger.debug("Exiting MappingTables GET")

    def post(self, mapping_table_id):
        return self.execute_store_request(request, self.post_executor, mapping_table_id)
    
    def put(self, mapping_table_id):
        return self.execute_store_request(request, self.put_executor, mapping_table_id)
    
    def delete(self, mapping_table_id):
        return self.execute_store_request(request, self.delete_executor, mapping_table_id)
    
    def get(self, mapping_table_id):
        return self.execute_store_request(request, self.get_executor, mapping_table_id)


class SheetData(APIResource):
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Sheet Data GET")
        try:          
          req_body = request.get_json(force=True)
          res = self.service.get_sheet_data_service().get_sheet_data(req_body)
          return res, 200
        finally:
            logger.debug("Exiting Sheet Data GET")

    
    def put_executor(self, request, token_payload, store):
        try:
            req_body = request.get_json(force=True)
            res = self.service.get_sheet_data_service().update_sheet_data(req_body)
            if res:                   
                return res, 200           
        finally:
            logger.debug("Exiting Sheet Data POST")        
    
    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
    def put(self):
        return self.execute_store_request(request, self.put_executor)
            
class SheetDataHeader(APIResource):
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Sheet Data Header GET")
        try:         
          req_body = request.get_json(force=True)
          res = self.service.get_sheet_data_service().get_sheet_data_header(req_body)
          return res, 200
        finally:
            logger.debug("Exiting Sheet Data Header GET")

    
    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
class SheetCsvData(APIResource):
    def get_executor(self, request, token_payload, store, mapping_table_id):
        logger.debug("Entering MappingTables Csv GET")
        try:
          query_params = request.args.to_dict()  
          res = self.service.get_mappingtables_data_service('spreadsheet').get_mapping_table_data_for_csv(query_params, mapping_table_id)
          if res['status'] == 200:
            return res['data'], 200  
          else:
            return res, 500  
        finally:
            logger.debug("Exiting MappingTables Csv GET")

    def get(self, mapping_table_id):
        return self.execute_store_request(request, self.get_executor, mapping_table_id)
            