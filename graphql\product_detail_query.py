product_details_query = """
  query productById ($productId: Int = product_id) {
    site {
      product(entityId: $productId) {
        entityId
        name
        sku
        condition
        path
        categories (first: 1){
          edges {
            node {
              name
              path
            }
          }
        }
        prices {
          price {
            ...PriceFields
          }
          retailPrice {
            ...PriceFields
          }
          salePrice {
            ...PriceFields
          }
        }
        images {
          edges {
            node {
              ...ImageFields
            }
          }
        }
        upc
        brand {
          name
        }
        customFields {
          edges {
            node {
              name
              value
            }
          }
        }
        inventory {
          isInStock
          hasVariantInventory
        }
        minPurchaseQuantity
        maxPurchaseQuantity
        reviewSummary {
          numberOfReviews
          summationOfRatings
        }
        reviews(first: 3) {
          edges {
            node {
              rating
              author {
                name
              }
              title
              text
              createdAt {
                utc
              }
              entityId
            }
          }
        }
        relatedProducts (first: 10) {
          edges {
            node {
              entityId
              name
              path
              brand {
                name
              }
              defaultImage {
                url(width: 300)
              }
              prices {
                price {
                  ...PriceFields
                }
                retailPrice {
                  ...PriceFields
                }
                salePrice {
                  ...PriceFields
                }
              }
            }
          }
        }
        weight {
          unit
          value
        }
        availabilityV2 {
          status
          description
          ... on ProductAvailable {
            status
            description
          }
          ... on ProductPreOrder {
            message
            willBeReleasedAt {
              utc
            }
            status
            description
          }
          ... on ProductUnavailable {
            status
            description
            message
          }
        }
        description
        warranty
      }
    }
  }
  fragment ImageFields on Image {
    url (width: 1200)
    urlOriginal
    altText
    isDefault
  }
  fragment PriceFields on Money {
    value
    currencyCode
  }
"""

def get_query(product_id):
    x = product_details_query.replace("product_id", str(product_id))
    return x