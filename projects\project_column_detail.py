from sqlalchemy import text
import pg_db
from mongo_db import user_db
from datetime import datetime
from utils import common
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import logging
import traceback
from utils.common import convert_to_timestamp

logger = logging.getLogger()


def _fetch_column_detail(conn, project_id, id):
    data = [] 
    query = text(
        f"""SELECT * 
            FROM {pg_db.project_columns}
            WHERE id = :id 
            AND project_id = :project_id;
        """
    )
    query = query.params(id=id, project_id=project_id)
    result = conn.execute(query)    
    for row in result.fetchall():
        business_unit_data = {
            'id': row[0],
            'project_id': row[1],
            'name': row[2],
            'sort_id': row[3],
            'is_first_column': row[4],
            'is_last_column': row[5],
            'description': row[6],
            'is_archived': row[7],
            'created_by': row[8],
            'updated_by': row[9],
            'created_at': convert_to_timestamp(row[10]),
            'updated_at': convert_to_timestamp(row[11]),
            'is_visible': row[12],
            'is_default': row[13],
            'is_resolved': row[14]
        }
        data.append(business_unit_data)
    return data

def get_column_detail(project_id, id):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        data = _fetch_column_detail(conn, project_id, id)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['message'] = []
    finally:
        if conn:
            conn.close()
    return response


def modify_column_detail(conn, update_fields, project_id, id, username):
    set_clause = ", ".join([f"{field} = :{field}" for field in update_fields])
    query = text(
        f"""UPDATE {pg_db.project_columns}
            SET 
                {set_clause},
                updated_by = :updated_by,
                updated_at = CURRENT_TIMESTAMP
            WHERE 
                id = :id
                AND project_id = :project_id;"""
    )

    params = update_fields.copy()
    params.update({'updated_by': username, 'id': id, 'project_id': project_id})    
    result = conn.execute(query, params)    
    return result.rowcount > 0


def update_column_detail(payload, username, project_id, id):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        existing_column = _fetch_column_detail(conn, project_id, id)
        if not existing_column:
            response['status'] = 404
            response['message'] = "column not found."
            return response

        existing_column = existing_column[0]

        # Update fields if provided in payload
        update_fields = {}
        for field in ['name', 'description','is_archived', 'is_visible', 'is_default', 'is_resolved']:
            if field in payload:
                update_fields[field] = payload[field]

        data = modify_column_detail(conn, update_fields, project_id, id, username)
    
        if data:
            if 'is_default' in payload:
                query = text(
                    f"""UPDATE {pg_db.project_columns} set is_default = FALSE where project_id = :project_id and id != :id and is_default = TRUE;"""
                )
                query = query.params(project_id=project_id, id=id)
                conn.execute(query)
            
            if 'is_resolved' in payload:
                query = text(
                    f"""UPDATE {pg_db.project_columns} set is_resolved = FALSE where project_id = :project_id and id != :id and is_resolved = TRUE;"""
                )
                query = query.params(project_id=project_id, id=id)
                conn.execute(query)
                
            response['status'] = 200
            response['message'] = "Data updated successfully."
        else:
            response['status'] = 409
            response['message'] = "name: This column already exists.."
    except IntegrityError as e:
        logger.error(traceback.format_exc())
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 409
            response['message'] = "name: This column already exists."
        else:
            response['status'] = 500
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response


def _remove_column(conn, project_id, id):
    # Check if there are any cards in the column
    dependency_query = text(
        f"""SELECT COUNT(pc.current_column_id) AS card_count
            FROM {pg_db.project_columns} AS pm
            LEFT JOIN {pg_db.project_cards} AS pc ON pm.id = pc.current_column_id
            WHERE pm.project_id = :project_id
            AND pm.id = :id
        """
    )
    dependency_query = dependency_query.params(project_id=project_id, id=id)
    result = conn.execute(dependency_query)
    dependency_result = result.fetchone()
    card_count = dependency_result[0]
   
    if card_count > 0:
        return False

    # Now, let's retrieve the sort_id of the column we are going to delete
    sort_id_query = text(
        f"""SELECT sort_id
            FROM {pg_db.project_columns}
            WHERE project_id = :project_id
            AND id = :id
        """
    )
    sort_id_query = sort_id_query.params(project_id=project_id, id=id)
    sort_id_result = conn.execute(sort_id_query)
    sort_id_row = sort_id_result.fetchone()
    
    if sort_id_row is None:
        # Column not found, return False indicating deletion failure
        return False

    sort_id = sort_id_row[0]

    # Now, let's delete the column
    delete_query = text(
        f"""DELETE FROM {pg_db.project_columns}
            WHERE project_id = :project_id
            AND id = :id
        """
    )
    delete_query = delete_query.params(project_id=project_id, id=id)
    delete_result = conn.execute(delete_query)

    # After deleting, let's adjust the first_column and last_column flags
    # Find the column with the lowest sort_id and mark it as first_column = true
    update_first_column_query = text(
        f"""UPDATE {pg_db.project_columns}
            SET is_first_column = true
            WHERE project_id = :project_id
            AND sort_id = (
                SELECT MIN(sort_id) FROM {pg_db.project_columns}
                WHERE project_id = :project_id
            )
        """
    )
    update_first_column_query = update_first_column_query.params(project_id=project_id)
    conn.execute(update_first_column_query)

    # Find the column with the highest sort_id and mark it as last_column = true
    update_last_column_query = text(
        f"""UPDATE {pg_db.project_columns}
            SET is_last_column = true
            WHERE project_id = :project_id
            AND sort_id = (
                SELECT MAX(sort_id) FROM {pg_db.project_columns}
                WHERE project_id = :project_id
            )
        """
    )
    update_last_column_query = update_last_column_query.params(project_id=project_id)
    conn.execute(update_last_column_query)

    return delete_result.rowcount > 0




def delete_column(project_id, id):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:
        if id:
            data = _remove_column(conn, project_id, id)
            if data:
                response['status'] = 200
                response['message'] = "Data deleted successfully."
            else:
                response['status'] = 409
                response['message'] = "Data deletion failed."
        else:
            response['status'] = 400
            response['message'] = "id is missing."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response