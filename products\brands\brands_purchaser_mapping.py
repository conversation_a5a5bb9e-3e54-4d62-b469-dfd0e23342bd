from datetime import datetime
import new_mongodb
from new_mongodb import cms_db, delete_documents_from_admin_collection, fetch_one_document_from_admin_collection
from utils.common import get_paginated_records_updated, calculatePaginationData, convert_to_timestamp, get_paginated_records_brands
import logging
import traceback
from new_mongodb import get_admin_db_client_for_store_id
import new_pgdb
from sqlalchemy import text
from bson import ObjectId
logger = logging.getLogger()

def create_brands_purchaser_mapping(store_id, payload, username):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        purchaser_name = payload["purchaser_name"]
        purchaser_email = payload["purchaser_email"]

        # ✅ Check if the purchaser_email already exists
        check_query = """
            SELECT 1 FROM brand_purchaser_mapping 
            WHERE purchaser_email = :purchaser_email
            LIMIT 1
        """
        existing = conn.execute(text(check_query), {"purchaser_email": purchaser_email}).fetchone()

        if existing:
            response["status"] = 409
            response["message"] = f"purchaser_id: Purchaser email already exists in the mapping."
            return response

        insert_query = """
            INSERT INTO brand_purchaser_mapping
            (purchaser_name, purchaser_email, brand_id, brand_name, created_by, created_at)
            VALUES (:purchaser_name, :purchaser_email, :brand_id, :brand_name, :created_by, CURRENT_TIMESTAMP)
        """

        for brand in payload["brands"]:
            brand_id = brand["brand_id"]
            brand_name = brand["name"]

            conn.execute(text(insert_query), {
                "purchaser_name": purchaser_name,
                "purchaser_email": purchaser_email,
                "brand_id": brand_id,
                "brand_name": brand_name,
                "created_by": username
            })

        conn.commit()
        response["status"] = 200
        response["message"] = "Mapping created successfully"
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        conn.close()
    return response

def get_brands_purchaser_mapping(store_id, search, page, limit, sort_array):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        count_query = """
            SELECT COUNT(DISTINCT purchaser_email) FROM brand_purchaser_mapping
        """
        query = """
            SELECT
                MIN(id) AS id,
                purchaser_name,
                purchaser_email,
                EXTRACT(EPOCH FROM MIN(created_at))::BIGINT AS created_at,
                EXTRACT(EPOCH FROM MAX(updated_at))::BIGINT AS updated_at,
                JSON_AGG(
                    JSON_BUILD_OBJECT(
                        'brand_id', brand_id,
                        'name', brand_name
                    )
                ) AS brands
            FROM
                brand_purchaser_mapping
        """

        if search:
            query += " WHERE purchaser_name ILIKE :search OR purchaser_email ILIKE :search"
            count_query += " WHERE purchaser_name ILIKE :search OR purchaser_email ILIKE :search"

        query += " GROUP BY purchaser_name, purchaser_email"

        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            if sort_array[0] in ["purchaser_name", "purchaser_email", "created_at"]:
                query += f" ORDER BY {sort_array[0]} {sort_direction}"

        query += f" LIMIT :limit OFFSET :offset"
        
        count_result = conn.execute(text(count_query), {"search": f"%{search}%"}).scalar()
        result = conn.execute(text(query), {"search": f"%{search}%", "limit": limit, "offset": (page - 1) * limit}).fetchall()
        
        mappings_list = [
            {
                "id": row[0],
                "purchaser_name": row[1],
                "purchaser_email": row[2],
                "created_at": convert_to_timestamp(row[3]),
                "updated_at": convert_to_timestamp(row[4]),
                "brands": row[5]
            }
            for row in result
        ]

        paginated_data = calculatePaginationData(mappings_list, page, limit, count_result)
        response["status"] = 200
        response["data"] = paginated_data
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        conn.close()
    return response

def get_brands_purchaser_mapping_details(store_id, email_id):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        query = """
            SELECT
                purchaser_name, 
                purchaser_email,
                JSON_AGG(
                    JSON_BUILD_OBJECT(
                        'brand_id', brand_id,
                        'name', brand_name
                    )
                ) AS brands
            FROM brand_purchaser_mapping
            WHERE purchaser_email = :email_id
            GROUP BY purchaser_name, purchaser_email
        """
        result = conn.execute(text(query), {"email_id": email_id}).fetchone()

        if not result:
            return None  # or raise a 404 error

        res = {
            "purchaser_name": result[0],
            "purchaser_email": result[1],
            "brands": result[2]
        }

        response["status"] = 200
        response["data"] = res
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        conn.close()
    return response
    
def delete_brands_purchaser_mapping(store_id, email_id):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        query = """
            DELETE FROM brand_purchaser_mapping WHERE purchaser_email = :email_id
        """
        conn.execute(text(query), {"email_id": email_id})
        conn.commit()
        response["status"] = 200
        response["message"] = 'Mapping deleted successfully'
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        conn.close()
    return response

def update_brands_purchaser_mapping(store_id, email_id, payload, username):
    response = {
        "status": 400,
    }
    try:
        delete_response = delete_brands_purchaser_mapping(store_id, email_id)
        if delete_response["status"] == 200:
            create_brands_purchaser_mapping(store_id, payload, username)
        else:
            response["status"] = delete_response["status"]
            response["message"] = delete_response["message"]
            return response
        response["status"] = 200
        response["message"] = 'Mapping updated successfully'
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    return response

def get_brands_purchaser_mapping_purchaser_dropdown(store_id, search, page, limit):
    response = {
        "status": 400,
    }
    db = get_admin_db_client_for_store_id(store_id)
    try:
        payload = {
            "page": page,
            "limit": limit,
            "filterBy": ["name", "username"],
            "filter": search
        }
        users, total_data_length, page, limit = get_paginated_records_updated(
            db, "users", payload, {"name": 1, "username": 1, "_id": 0}, {}
        )
        if users:
            final_result = {
                "data": users,
                "meta": {
                    "current_page": page,
                    "next_page": (page + 1 if page and limit and (page * limit) < total_data_length else None),
                    "total_count": total_data_length
                }
            }    
            response['data'] = final_result
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = {
                "data": [],
                "meta": {
                    "current_page": page,
                    "next_page": None,
                    "total_count": total_data_length
                }
            }
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    return response


def get_brands_purchaser_mapping_brand_dropdown(store_id, search, page, limit, purchaser_email=None):
    response = {
        "status": 400,
    }
    db = get_admin_db_client_for_store_id(store_id)
    conn = new_pgdb.get_connection(store_id)
    try:
        payload = {
            "page": page,
            "limit": limit,
            "filterBy": ["name"],
            "filter": search
        }
        additional_query = {}

        prioritized_brand_ids = []
        # Step 1: If purchaser_email is passed, fetch brand IDs from Postgres
        if purchaser_email:
            brand_id_query = text("""
                SELECT DISTINCT brand_id
                FROM brand_purchaser_mapping
                WHERE purchaser_email = :purchaser_email
            """)
            brand_ids_result = conn.execute(brand_id_query, {"purchaser_email": purchaser_email}).fetchall()
            prioritized_brand_ids = [row[0] for row in brand_ids_result if row[0]]

        # Step 2: Build additional_query with prioritization if we have brand IDs
        if prioritized_brand_ids:
            additional_query = {
                "addFields": {
                    "priority": {
                        "$cond": [{"$in": ["$id", prioritized_brand_ids]}, 0, 1]
                    }
                },
                "match": {}
            }
            payload["sortBy"] = [{"field": "priority", "order": 1}, {"field": "name", "order": 1}]
        else:
            additional_query = {
                "match": {}
            }
            payload["sortBy"] = [{"field": "name", "order": 1}]

        brands, total_data_length, page, limit = get_paginated_records_brands(
            db, cms_db.BRANDS_COLLECTION, payload, {"name": 1, "id": 1, "_id": 0}, additional_query
        )
        if brands:
            final_result = {
                "data": brands,
                "meta": {
                    "current_page": page,
                    "next_page": (page + 1 if page and limit and (page * limit) < total_data_length else None),
                    "total_count": total_data_length
                }
            }    
            response['data'] = final_result
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = {
                "data": [],
                "meta": {
                    "current_page": page,
                    "next_page": None,
                    "total_count": total_data_length
                }
            }
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    return response