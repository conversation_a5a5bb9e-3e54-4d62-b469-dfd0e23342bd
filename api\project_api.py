from flask import request
import logging
import traceback
from api import APIResource
from analytics import replenishment
from new_mongodb import store_admin_db
from projects import business_unit, global_custom_field_details, global_custom_fields, global_permitted_members, \
      global_members, project_gantt_chart, project_members_detail, projects, project_members, project_modules, project_columns, project_details, \
        project_module_details, project_card, project_cards, business_unit_detail, project_column_detail, project_card_status, \
            project_card_priorities, card_custom_field, card_custom_field_detail, boards, project_custom_fields, project_custom_field_detail, \
                default_columns, default_column_details, project_mapping, teams, project_task_report, project_members_report, project_timesheet, project_matrix

from schemas.products import products_list_update
from flask import make_response, send_file
from io import BytesIO
from PIL import Image
import os

logger = logging.getLogger()

class Boards(APIResource):
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering boards POST}")
        try:
            req_body = request.get_json(force=True)
            name = req_body.get('name', '')
            description = req_body.get('description', '')
            is_archived = req_body.get('is_archived', False)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                if name and name != '' and description: 
                    res = boards.add_board(name, description, is_archived, username)
                    if res['status'] == 200:
                        return {'message': res['message']}, res['status']
                    else:
                        return {'message': res['message']}, res['status']
                else:
                    return {'message': 'Please check your payload.'}, 400
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Board POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)

class BusinessUnits(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering business units GET")
        try:
            res = business_unit.get_business_unit()
            if res['status'] == 200:                
                return res['data'], 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Business units GET")


    def post_executor(self, request, token_payload, store):
        logger.debug("Entering business units POST}")
        try:
            req_body = request.get_json(force=True)
            name = req_body.get('name', None)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                if name:    
                    res = business_unit.post_business_unit(name, username)
                    if res['status'] == 200:
                        return {'message': res['message']}, res['status']
                    else:
                        return {'message': res['message']}, res['status']
                else:
                    return {'message': 'Please check your payload.'}, 400
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Business Unit POST")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
class BusinessUnitDetails(APIResource):

    def get_executor(self, request, token_payload, store, id):
        logger.debug("Entering business units GET")
        try:
            res = business_unit_detail.get_business_unit_detail(id)
            if res['status'] == 200:                
                return res['data'], 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Business units GET")

    def patch_executor(self, request, token_payload, store, id):
        logger.debug("Entering particular Business Unit PATCH")
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':  
                res = business_unit_detail.update_business_unit(req_body, username, id)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"particular Business Unit PATCH")
    
    def delete_executor(self, request, token_payload, store, id):
        logger.debug("Entering Business unit Delete")
        try:            
            res = business_unit_detail.delete_business_unit(id)
            if res['status'] == 200:                
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Business unit Delete")

       
    def delete(self, id):
        return self.execute_store_request(request, self.delete_executor, id)

    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id)

    def patch(self, id):
        return self.execute_store_request(request, self.patch_executor, id)

    
class ProjectListing(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Project listing GET")
        try:
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']
            req_body = request.args.to_dict()            
            search_value = req_body.get("search", "").strip()
            is_archived = req_body.get("is_archived", False)
            limit = int(req_body.get("limit", 10)) 
            page = int(req_body.get("page", 1))
            sort_by = req_body.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            if username != '':                
                res = projects.get_project_listing(username, search_value, is_archived, limit, page, sort_array)                
            
                if res['status'] == 200:                
                    return res['data'], 200
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Project listing GET")
    
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Project listing POST")
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':  
                res = projects.create_new_project(req_body, username)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401         
        finally:
            logger.info(f"Project listing POST")
          
    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
class GlobalPermittedMembers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering global permitted members listing GET")
        try:
            res = global_permitted_members.get_global_permitted_members(store['id'])
            if res['status'] == 200:                
                return {'data': res['data']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting global permitted members GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class GlobalMembers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering global members listing GET")
        try:
            req_body = request.args.to_dict()
            search_value = req_body.get("search", "").strip()
            page = int(req_body.get("page", 1)) if req_body.get("page") is not None else None
            limit = int(req_body.get("limit", 10)) if req_body.get("limit") is not None else None
            sort_by = req_body.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            res = global_members.get_global_members(page, limit, search_value, sort_array)
            if res['status'] == 200:                
                return res['data'], 200
            else:
                return {'data': res['data']}, res['status']

        finally:
            logger.debug("Exiting global members GET")

    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Global members POST")
        try:
            req_body = request.get_json(force=True)
            usernames = req_body.get('usernames', '')
            if usernames and usernames != '':
                res = global_members.add_new_global_member(usernames)
                if res['status'] == 200:                
                    return {'message': res['message']}, 200
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'message': "Invalid payload."}, 500                
        finally:
            logger.info(f"Global Members POST")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    def post(self):
        return self.execute_store_request(request, self.post_executor)

class ProjectSpecificMembersDropDown(APIResource):
    def get_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering global members listing GET")
        try:
            req_body = request.args.to_dict()
            search_value = req_body.get("search", "").strip()
            sort_by = req_body.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            res = global_members.get_members_dropdown(project_id, search_value, sort_array)
            if res['status'] == 200:                
                return {'data': res['data']}, 200
            else:
                return {'message': res['message']}, res['status']

        finally:
            logger.debug("Exiting global members GET")

    def get(self, project_id):
        return self.execute_store_request(request, self.get_executor, project_id)

class GlobalMembersDelete(APIResource):
    def delete_executor(self, request, token_payload, store, username):
        logger.debug("Entering Global members Delete")
        try:
            res =  global_members.delete_global_member(username)
            if res['status'] == 200:                
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Global members Delete")


    def delete(self, username):
        return self.execute_store_request(request, self.delete_executor, username)
    

class GlobalMembersProjectList(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering global members listing GET")
        try:
            query_param = request.args.to_dict()
            username = query_param.get('username', '')
            if username != '':
                res = global_members.get_member_projectlist(username)
                if res['status'] == 200:                
                    return res['data'], 200
                else:
                    return {'data': res['message']}, res['status']
            else:
                return {'message': 'Please checke your parameters.'}, 500
        finally:
            logger.debug("Exiting global members GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)


class ProjectMembers(APIResource):
    def get_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering project members listing GET")
        try:
            req_body = request.args.to_dict()
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '': 
                res = project_members.get_project_members(req_body, project_id, username)
                if res['status'] == 200:                
                    return {'data': res['data']}, 200
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'message': 'Unauthorized'}, 401

        finally:
            logger.debug("Exiting project members GET")
 

    def post_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Project members POST")
        try:
            req_body = request.get_json(force=True)
            usernames = req_body.get('usernames', '')
            if usernames and usernames != '':
                res = project_members.add_new_member(usernames, project_id)
                if res['status'] == 200:                
                    return {'message': res['message']}, 200
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'message': "Invalid payload."}, 500                
        finally:
            logger.info(f"Project Members POST")
    
    # update project ownership
    def put_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Project members PUT")
        try:
            req_body = request.get_json(force=True)
            new_owner = req_body.get('username', '')
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']
            if username and username != '':
                res = project_members.change_project_ownership(project_id, new_owner, username)
                if res['status'] == 200:                
                    return {'message': res['message']}, 200
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'message': 'Unauthorized'}, 401 
        finally:
            logger.info(f"Project Members PUT")

    def get(self, project_id):
        return self.execute_store_request(request, self.get_executor, project_id)
    
    def post(self, project_id):
        return self.execute_store_request(request, self.post_executor, project_id)
    
    def put(self, project_id):
        return self.execute_store_request(request, self.put_executor, project_id)


class ProjectModules(APIResource):
    def get_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Project modules listing GET")
        try:
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '': 
                res = project_modules.get_modules(project_id, username)
                if res['status'] == 200:                
                    return {'data': res['data']}, 200
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'message': 'Unauthorized'}, 401
        finally:
            logger.debug("Exiting Project modules GET")


    def post_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Project modules POST")
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':    
                res = project_modules.add_new_module(req_body, username, project_id)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Project modules POST")

    def patch_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Project modules sorting PATCH")
        try:
            req_body = request.get_json(force=True)
            module_ids = req_body.get('module_ids', 0)
            res = project_modules.update_modules_sort_id(project_id, module_ids)
            if res['status'] == 200:                
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Project modules PATCH")



    def get(self, project_id):
        return self.execute_store_request(request, self.get_executor, project_id)
    
    def post(self, project_id):
        return self.execute_store_request(request, self.post_executor, project_id)
    
    def patch(self, project_id):
        return self.execute_store_request(request, self.patch_executor, project_id)
     

class ProjectColumns(APIResource):

    def get_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Project columns listing GET")
        try:
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '': 
                res = project_columns.get_columns(project_id, username)
                if res['status'] == 200:                
                    return {'data': res['data']}, 200
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Project columns GET")

    def post_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Project columns POST")
        try:
            req_body = request.get_json(force=True)
            name = req_body.get('name', '')
            description = req_body.get('description', '')
            is_archived = req_body.get('is_archived', False)
            is_visible = req_body.get('is_visible', True)
            is_resolved = req_body.get('is_resolved', False)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                if name and name != '': 
                    res = project_columns.add_new_column(name, description, is_archived, username, project_id, is_visible, is_resolved)
                    if res['status'] == 200:
                        return {'message': res['message']}, res['status']
                    else:
                        return {'message': res['message']}, res['status']
                else:
                    return {'message': 'Please check your payload.'}, 400
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Project columns POST")

    def patch_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Project columns sorting PATCH")
        try:
            req_body = request.get_json(force=True)
            columns_ids = req_body.get('column_ids', 0)
            res = project_columns.update_columns_sort_id(project_id, columns_ids)
            if res['status'] == 200:                
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Project columns PATCH")

    def get(self, project_id):
        return self.execute_store_request(request, self.get_executor, project_id)

    def post(self, project_id):
        return self.execute_store_request(request, self.post_executor, project_id)
    
    def patch(self, project_id):
        return self.execute_store_request(request, self.patch_executor, project_id)
    

class ProjectColumnDetails(APIResource):
    def get_executor(self, request, token_payload, store, project_id, id):
        logger.debug("Entering Project columns GET")
        try:
            res = project_column_detail.get_column_detail(project_id, id)
            if res['status'] == 200:                
                return {'data': res['data']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Project column detail GET")

    def patch_executor(self, request, token_payload, store, project_id, id):
        logger.debug("Entering particular Column PATCH")
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':    
                res = project_column_detail.update_column_detail(req_body, username, project_id, id)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"particular Column PATCH")

    def delete_executor(self, request, token_payload, store, project_id, id):
        logger.debug("Entering Project column Delete")
        try:            
            res =  project_column_detail.delete_column(project_id, id)
            if res['status'] == 200:                
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Project column Delete")

    def patch(self, project_id, id):
        return self.execute_store_request(request, self.patch_executor, project_id, id)

    def get(self, project_id, id):
        return self.execute_store_request(request, self.get_executor, project_id, id)
    
    def delete(self, project_id, id):
        return self.execute_store_request(request, self.delete_executor, project_id, id)

class ProjectDetails(APIResource):
    def get_executor(self, request, token_payload, store, id):
        logger.debug("Entering particular Project listing GET")
        try:
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']
            if username != '':
                res = project_details.get_project_detail(id, username)
                if res['status'] == 200:                
                    return {'data': res['data']}, 200
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting particular Project GET")
    
    def patch_executor(self, request, token_payload, store, id):
        logger.debug("Entering particular Project PATCH")
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':    
                res = project_details.update_project(req_body, username, id)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"particular Project PATCH")
    
    def delete_executor(self, request, token_payload, store, id):
        logger.debug("Entering particular Project Delete")
        try:     
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':          
                res =  project_details.delete_project(store['id'], username, id)
                if res['status'] == 200:                
                    return {'message': res['message']}, 200
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting particular Project Delete")  

    def patch(self, id):
        return self.execute_store_request(request, self.patch_executor, id)

    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id)
    
    def delete(self, id):
        return self.execute_store_request(request, self.delete_executor, id) 

class FevoriteProject(APIResource):
    def post_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Add Project fevorite POST")
        try:                                    
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':                   
                res = projects.add_projects_to_fevorite(username, project_id)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']                
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Add Project fevorite POST") 

    def post(self, project_id):
        return self.execute_store_request(request, self.post_executor, project_id)

class ProjectMembersDetails(APIResource):

    def get_executor(self, request, token_payload, store, project_id, id):
        logger.debug("Entering Project member detail GET")
        try:
            res = project_members_detail.get_member_detail(project_id, id)
            if res['status'] == 200:                
                return {'data': res['data']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Project member detail GET")
    
    def delete_executor(self, request, token_payload, store, project_id, id):
        logger.debug("Entering Project members Delete")
        try:            
            res =  project_members_detail.delete_member(project_id, id)
            if res['status'] == 200:                
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Project members Delete")

       
    def get(self, project_id, id):
        return self.execute_store_request(request, self.get_executor, project_id, id)
    
    def delete(self, project_id, id):
        return self.execute_store_request(request, self.delete_executor, project_id, id)    

class ProjectModuleDetails(APIResource):

    def get_executor(self, request, token_payload, store, project_id, id):
        logger.debug("Entering Project module details GET")
        try:
            res = project_module_details.get_module_detail(project_id, id)
            if res['status'] == 200:                
                return {'data': res['data']}, 200
        finally:
            logger.debug("Exiting Project module details GET")

    def patch_executor(self, request, token_payload, store, project_id, id):
        logger.debug("Entering Project module details PUT")
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':    
                res = project_module_details.update_project_module_detail(req_body, username, project_id, id)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Project modules PATCH")

    def delete_executor(self, request, token_payload, store, project_id, id):
        logger.debug("Entering Project modules Delete")
        try:            
            res =  project_module_details.delete_module(project_id, id)
            if res['status'] == 200:                
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Project modules Delete")

    def get(self, project_id, id):
        return self.execute_store_request(request, self.get_executor, project_id, id)

    def patch(self, project_id, id):
        return self.execute_store_request(request, self.patch_executor, project_id, id)
    
    def delete(self, project_id, id):
        return self.execute_store_request(request, self.delete_executor, project_id, id)

class ProjectCardStatus(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Project cards status listing GET")
        try:
            res = project_card_status.get_project_card_status()
            if res['status'] == 200:                
                return {'data': res['data']}, 200
        finally:
            logger.debug("Exiting Project card status GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ProjectCardStatusDetail(APIResource):
    def get_executor(self, request, token_payload, store, id):
        logger.debug("Entering Project cards status detail listing GET")
        try:
            res = project_card_status.get_project_card_status_detail(id)
            if res['status'] == 200:                
                return {'data': res['data']}, 200
        finally:
            logger.debug("Exiting Project card status detail GET")

    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id)
    

class ProjectCardPriorities(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Project cards priorities listing GET")
        try:
            res = project_card_priorities.get_project_card_priorities()
            if res['status'] == 200:                
                return {'data': res['data']}, 200
        finally:
            logger.debug("Exiting Project card priorities GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ProjectCardPrioritiesDetail(APIResource):
    def get_executor(self, request, token_payload, store, id):
        logger.debug("Entering Project cards priorities detail listing GET")
        try:
            res = project_card_priorities.get_project_card_priorities_detail(id)
            if res['status'] == 200:                
                return {'data': res['data']}, 200
        finally:
            logger.debug("Exiting Project card priorities detail GET")

    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id)

class Teams(APIResource):
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Teams POST")
        try:
            req_body = request.get_json(force=True)
            name = req_body.get('name', '')   
            usernames = req_body.get('usernames', [])     
            if token_payload and 'username' in token_payload:
                email = token_payload['username']
            if usernames and usernames != []:
                if name and name != '':    
                    res = teams.create_team(email, name, usernames)
                    if res['status'] == 200:
                        return {'message': res['message']}, res['status']
                    else:
                        return {'message': res['message']}, res['status']
                else:
                    return {'message': 'Please check your payload.'}, 400
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Entering Teams POST")

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering teams listing GET")
        try:
            query_params = request.args.to_dict()
            search = query_params.get("search", "").strip()
            sort_by = query_params.get("sort_by", "").strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            status = query_params.get("status", "")
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']
            if username != '':
                res = teams.get_teams(search, status, sort_array)
                if res['status'] == 200:                
                    return {'data': res['data']}, 200
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting teams listing GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def post(self):
        return self.execute_store_request(request, self.post_executor)

class TeamDetails(APIResource):
    def get_executor(self, request, token_payload, store, team_id):
        logger.debug("Entering Team details listing GET")
        try:
            res = teams.get_team_detail(team_id)
            if res['status'] == 200:                
                return {'data': res['data']}, 200
        finally:
            logger.debug("Exiting Team details GET")

    def patch_executor(self, request, token_payload, store, team_id):
        logger.debug("Entering Team details PUT")
        try:
            payload = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':    
                res = teams.update_team(payload, username, team_id)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Team details PATCH")

    def delete_executor(self, request, token_payload, store, team_id):
        logger.debug("Entering team Delete")
        try:            
            res =  teams.delete_team(team_id)
            if res['status'] == 200:                
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting team Delete")

    def get(self, team_id):
        return self.execute_store_request(request, self.get_executor, team_id)

    def patch(self, team_id):
        return self.execute_store_request(request, self.patch_executor, team_id)
    
    def delete(self, team_id):
        return self.execute_store_request(request, self.delete_executor, team_id)


class AddTeams(APIResource):
    def post_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Add teams POST")
        try:
            req_body = request.get_json(force=True)
            team_ids = req_body.get('teams', [])
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']
            if username != '':
                res = teams.add_teams_to_project(team_ids, project_id)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Add teams POST")
    
    def post(self, project_id):
        return self.execute_store_request(request, self.post_executor, project_id)
            
class GlobalCustomFields(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Global custom fields listing GET")
        try:
            req_body = request.args.to_dict()
            search_value = req_body.get("search", "").strip()
            project_id = req_body.get("project_id", "").strip()
            res = global_custom_fields.get_custom_fields(search_value, project_id)
            if res['status'] == 200:                
                return {'data': res['data']}, 200
        finally:
            logger.debug("Exiting Global custom fields GET")

    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Global custom fields POST")
        try:
            req_body = request.get_json(force=True)
            name = req_body.get('name', '')
            datatype = req_body.get('datatype', '')
            is_mandatory = req_body.get('is_mandatory', False)
            is_multiselect_group = True if (datatype != '' and datatype == 'multi select') else False

            group_values = req_body.get('group_values', None)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                if name and name != '' and datatype and datatype != '':    
                    res = global_custom_fields.add_new_field(name, datatype, is_multiselect_group, group_values, is_mandatory, username)
                    if res['status'] == 200:
                        return {'message': res['message']}, res['status']
                    else:
                        return {'message': res['message']}, res['status']
                else:
                    return {'message': 'Please check your payload.'}, 400
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Global custom fields POST")

    def patch_executor(self, request, token_payload, store):
        logger.debug("Entering Global custom fields sorting PATCH")
        try:
            req_body = request.get_json(force=True)
            custom_field_ids = req_body.get('custom_field_ids', 0)
            res = global_custom_fields.update_sort_id(custom_field_ids)
            if res['status'] == 200:                
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Global custom fields PATCH")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
    def patch(self):
        return self.execute_store_request(request, self.patch_executor)

class GlobalCustomFieldDetail(APIResource):

    def get_executor(self, request, token_payload, store, id):
        logger.debug("Entering Global custom fields listing GET")
        try:
            res = global_custom_field_details.get_custom_field_detail(id)
            if res['status'] == 200:
                return {'data': res['data']}, 200
        finally:
            logger.debug("Exiting Global custom fields GET")

    def patch_executor(self, request, token_payload, store, id):
        logger.debug("Entering Global custom field details PATCH")
        try:
            req_body = request.get_json(force=True)
            name = req_body.get('name', None)
            group_values = req_body.get('group_values', None)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':  
                res = global_custom_field_details.patch_global_custom_field(name, group_values, username, id)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Global custom field PATCH")

    def delete_executor(self, request, token_payload, store, id):
        logger.debug("Entering Global custom field Delete")
        try:            
            res =  global_custom_field_details.delete_field(id)
            if res['status'] == 200:                
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Global custom field Delete")

    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id)

    def patch(self, id):
        return self.execute_store_request(request, self.patch_executor, id)
    
    def delete(self, id):
        return self.execute_store_request(request, self.delete_executor, id)
    

class DefaultColumns(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering DEFAULT COLUMN GET")
        try:
            req_body = request.args.to_dict()
            search_value = req_body.get("search", "").strip()
            res = default_columns.get_default_columns(search_value)
            if res['status'] == 200:                
                return {'data': res['data']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting DEFAULT COLUMN GET")

    def post_executor(self, request, token_payload, store):
        logger.debug("Entering DEFAULT COLUMN POST")
        try:
            req_body = request.get_json(force=True)
            name = req_body.get('name', '')
            description = req_body.get('description', '')
            is_archived = req_body.get('is_archived', False)   
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                if name and name != '':    
                    res = default_columns.add_new_default_column(name, description, is_archived, username)
                    if res['status'] == 200:
                        return {'message': res['message']}, res['status']
                    else:
                        return {'message': res['message']}, res['status']
                else:
                    return {'message': 'Please check your payload.'}, 400
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"DEFAULT COLUMN POST")

    def patch_executor(self, request, token_payload, store):
        logger.debug("Entering DEFAULT COLUMN fields sorting PATCH")
        try:
            req_body = request.get_json(force=True)
            default_columns_ids = req_body.get('default_columns_ids', 0)
            res = default_columns.update_sort_id_default_columns(default_columns_ids)
            if res['status'] == 200:                
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting DEFAULT COLUMN PATCH")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
    def patch(self):
        return self.execute_store_request(request, self.patch_executor)


class DefaultColumnDetails(APIResource):
    def get_executor(self, request, token_payload, store, id):
        logger.debug("Entering DEFAULT COLUMN details listing GET")
        try:
            res = default_column_details.get_default_column_detail(id)
            if res['status'] == 200:
                return {'data': res['data']}, 200
        finally:
            logger.debug("Exiting DEFAULT COLUMN details GET")

    def patch_executor(self, request, token_payload, store, id):
        logger.debug("Entering DEFAULT COLUMN details PATCH")
        try:
            req_body = request.get_json(force=True)
            name = req_body.get('name', None)
            description = req_body.get('description', None)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':  
                res = default_column_details.patch_default_columns(name, description, username, id)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"DEFAULT COLUMN PATCH")

    def delete_executor(self, request, token_payload, store, id):
        logger.debug("Entering DEFAULT COLUMN Delete")
        try:            
            res =  default_column_details.delete_default_column(id)
            if res['status'] == 200:                
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting DEFAULT COLUMN Delete")

    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id)

    def patch(self, id):
        return self.execute_store_request(request, self.patch_executor, id)
    
    def delete(self, id):
        return self.execute_store_request(request, self.delete_executor, id)
    
class ProjectCustomFields(APIResource):
    def get_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Project custom fields listing GET")
        try:
            res = project_custom_fields.get_fields_from_project(project_id)
            if res['status'] == 200:
                return {'data': res['data']}, 200
        finally:
            logger.debug("Exiting Project custom fields GET")

    def post_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering project custom fields POST")
        try:
            req_body = request.get_json(force=True)
            field_type = req_body.get('type', 'global')
            customfield_meta_ids = req_body.get('customfield_meta_ids', [])

            name = req_body.get('name', '')
            datatype = req_body.get('datatype', '')
            is_mandatory = req_body.get('is_mandatory', False)
            is_multiselect_group = True if (datatype != '' and datatype == 'multi select') else False
            group_values = req_body.get('group_values', None)
            
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                if (field_type == 'global'and customfield_meta_ids) or (field_type == 'custom'):    
                    res = project_custom_fields.add_new_field_to_project(customfield_meta_ids, username, project_id, name, datatype, is_multiselect_group, group_values, is_mandatory, field_type)
                    if res['status'] == 200:
                        return {'data': res['message']}, res['status']
                    else:
                        return {'message': res['message']}, res['status']
                else:
                    return {'message': 'Please check your payload.'}, 400
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"project custom fields POST")

    def patch_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Project custom field sort_id PATCH")
        try:
            req_body = request.get_json(force=True)
            project_custom_field_ids = req_body.get('project_custom_field_ids', 0)
            res = project_custom_fields.update_sort_id_in_project_customfield(project_custom_field_ids, project_id)
            if res['status'] == 200:
                return {'message': res['message']}, res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.info(f"Project custom field sort_id PATCH")

    def get(self, project_id):
        return self.execute_store_request(request, self.get_executor, project_id)
    
    def post(self, project_id):
        return self.execute_store_request(request, self.post_executor, project_id)
    
    def patch(self, project_id):
        return self.execute_store_request(request, self.patch_executor, project_id)

class ProjectCustomFieldDetail(APIResource):
    def delete_executor(self, request, token_payload, store, project_id, id):
        logger.debug("Entering Project custom field Delete")
        try:            
            res =  project_custom_field_detail.delete_field_from_project(project_id, id)
            if res['status'] == 200:                
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Project custom field Delete")

    def patch_executor(self, request, token_payload, store, project_id, id):
        logger.debug("Entering Project custom field details PATCH")
        try:
            req_body = request.get_json(force=True)
            # name = req_body.get('name', None)
            # is_visible = req_body.get('is_visible', True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':  
                res = project_custom_field_detail.patch_project_custom_field(username, project_id, id, req_body)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Project custom field PATCH")

    def patch(self, project_id, id):
        return self.execute_store_request(request, self.patch_executor, project_id, id)
 
    def delete(self, project_id, id):
        return self.execute_store_request(request, self.delete_executor, project_id, id)

class ProjectCardCustomField(APIResource):

    def get_executor(self, request, token_payload, store, project_id, card_id):
        logger.debug("Entering Project card custom fields GET")
        try:
            res = card_custom_field.get_fields_from_card(project_id, card_id)
            if res['status'] == 200:                
                return {'data': res['data']}, 200
        finally:
            logger.debug("Exiting Project card custom fields GET")

    def post_executor(self, request, token_payload, store, project_id, card_id):
        logger.debug("Entering Project card custom fields POST")
        try:
            req_body = request.get_json(force=True)
            project_customfield_id = req_body.get('project_customfield_id', 0)
            str_value = req_body.get('str_value', None)
            number_value = req_body.get('number_value', None)
            date_value = req_body.get('date_value', None)
            select_value = req_body.get('select_value', None)
            multi_select_value = req_body.get('multi_select_value', None)

            if str_value == "":
                str_value = None
            
            if number_value == "":
                number_value = None
            
            if date_value == "":
                date_value = None
            
            if select_value == "":
                select_value = None
            
            if multi_select_value == []:
                multi_select_value = None

            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                if project_customfield_id:    
                    res = card_custom_field.add_new_field_to_card(store, str_value, number_value, date_value, select_value, multi_select_value, username, project_id, card_id, project_customfield_id)
                    if res['status'] == 200:
                        return {'message': res['message']}, res['status']
                    else:
                        return {'message': res['message']}, res['status']
                else:
                    return {'message': 'Please check your payload.'}, 400
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Project card custom fields POST")

    def get(self, project_id, card_id):
        return self.execute_store_request(request, self.get_executor, project_id, card_id)
    
    def post(self, project_id, card_id):
        return self.execute_store_request(request, self.post_executor, project_id, card_id)

class ProjectCardCustomFieldDetail(APIResource):

    def get_executor(self, request, token_payload, store, project_id, card_id, id):
        logger.debug("Entering Project custom fields listing GET")
        try:
            res = card_custom_field_detail.get_field_detail_from_card(project_id, card_id, id)
            if res['status'] == 200:                
                return {'data': res['data']}, 200
        finally:
            logger.debug("Exiting Project custom fields GET")

    # def patch_executor(self, request, token_payload, store, project_id, card_id, id):
    #     logger.debug("Entering Project card custom field details PUT")
    #     try:
    #         req_body = request.get_json(force=True)
    #         str_value = req_body.get('str_value', None)
    #         int_value = req_body.get('int_value', None)
    #         float_value = req_body.get('float_value', None)

    #         if str_value == "":
    #             str_value = None

    #         if int_value == "":
    #             int_value = None
            
    #         if float_value == "":
    #             float_value = None

    #         username = ''
    #         if token_payload and 'username' in token_payload:
    #             username = token_payload['username']            
    #         if username != '':
    #             if str_value or int_value or float_value:    
    #                 res = card_custom_field_detail.update_field_to_card(str_value, int_value, float_value, username, project_id, card_id, id)
    #                 if res['status'] == 200:
    #                     return {'message': res['message']}, res['status']
    #                 else:
    #                     return {'message': res['message']}, res['status']
    #             else:
    #                 return {'message': 'Please check your payload.'}, 400
    #         else:
    #             return {'messsage': 'Unauthorized.'}, 401
    #     finally:
    #         logger.info(f"Project card custom field PATCH")

    def delete_executor(self, request, token_payload, store, project_id, card_id, id):
        logger.debug("Entering Project custom field Delete")
        try:            
            res =  card_custom_field_detail.delete_field_from_card(project_id, card_id, id)
            if res['status'] == 200:                
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Project custom field Delete")

    def get(self, project_id, card_id, id):
        return self.execute_store_request(request, self.get_executor, project_id, card_id, id)
    
    # def patch(self, project_id, card_id, id):
    #     return self.execute_store_request(request, self.patch_executor, project_id, card_id, id)
    
    def delete(self, project_id, card_id, id):
        return self.execute_store_request(request, self.delete_executor, project_id, card_id, id)


    

class ProjectCards(APIResource):
    
    def post_executor(self, request, token_payload, store, project_id, id):
        logger.debug("Entering Project Card POST")
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':   
                res = project_cards.create_card(store['id'], req_body, username, project_id, id)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Project Card POST")

    def post(self, project_id, id):
        return self.execute_store_request(request, self.post_executor, project_id, id)    
    
class ProjectCardsList(APIResource):
    def get_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Project Cards GET")
        try:     
            req_body = request.args.to_dict()
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':   
                search = req_body.get('search', '').strip()        
                res = project_cards.get_all_card(store['id'], project_id, username, search)
                if res['status'] == 200:
                    return {'data': res['data']}, res['status']
                else:
                    return {'message': res['message']}, res['status']   
            else:
                return {'messsage': 'Unauthorized.'}, 401                     
        finally:
            logger.debug("Exiting Project Cards GET")
    
    def get(self, project_id):
        return self.execute_store_request(request, self.get_executor, project_id)

class ProjectCardsListView(APIResource):
    def get_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Project Cards list view GET")
        try:     
            req_body = request.args.to_dict()
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':   
                search = req_body.get('search', '').strip()   
                limit = int(req_body.get("limit", 10)) 
                page = int(req_body.get("page", 1))  
                sort_by = req_body.get('sort_by', '').strip()
                sort_array = sort_by.split("/") if sort_by != '' else []
                res = project_cards.get_all_card_list_view(store['id'], project_id, username, page,limit, search, sort_array)
                if res['status'] == 200:
                    return res['data'], res['status']
                else:
                    return {'message': res['message']}, res['status']   
            else:
                return {'messsage': 'Unauthorized.'}, 401                     
        finally:
            logger.debug("Exiting Project Cards list view GET")
    
    def get(self, project_id):
        return self.execute_store_request(request, self.get_executor, project_id)

class ProjectStructure(APIResource):
    def get_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Project Structure GET")
        try:     
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':         
                res = project_cards.get_project_structure(project_id, username)
                if res['status'] == 200:
                    return {'data': res['data']}, res['status'] 
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401                     
        finally:
            logger.debug("Exiting Project Structure GET")
    
    def get(self, project_id):
        return self.execute_store_request(request, self.get_executor, project_id)

class ProjectAllCards(APIResource):
    def get_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Project Cards List For Parent Child Cards GET")
        try:     
            req_body = request.args.to_dict()
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':   
                search = req_body.get('search', '').strip()        
                res = project_cards.get_all_cards_for_parent_child(store['id'], project_id, username, search)
                if res['status'] == 200:
                    return {'data': res['data']}, res['status']
                else:
                    return {'message': res['message']}, res['status']   
            else:
                return {'messsage': 'Unauthorized.'}, 401                     
        finally:
            logger.debug("Exiting Project Cards For Parent Child Cards GET")
    
    def get(self, project_id):
        return self.execute_store_request(request, self.get_executor, project_id)
    
class RemoveCardRelationLinks(APIResource):
    def patch_executor(self, request, token_payload, store, project_id, id):
        logger.debug("Entering Card links remove PATCH")
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                res = project_card.remove_card_relation_links(store, req_body, project_id, id)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Exiting Card links remove PATCH")

    def patch(self, project_id, id):
        return self.execute_store_request(request, self.patch_executor, project_id, id)
    
class ProjectCard(APIResource):
    def get_executor(self, request, token_payload, store, project_id, id):
        logger.debug("Entering Project Card GET")
        try:  
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':           
                res = project_card.get_card_details(project_id, id, store, username)
                if res['status'] == 200:
                    return {'data': res['data']}, res['status']                      
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Project Card GET")

    def patch_executor(self, request, token_payload, store, project_id, id):
        logger.debug("Entering Project module details PUT")
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                res = project_card.update_card(req_body, username, project_id, id, store)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Project modules PATCH")
        
    def get(self, project_id, id):
        return self.execute_store_request(request, self.get_executor, project_id, id)
    
    def patch(self, project_id, id):
        return self.execute_store_request(request, self.patch_executor, project_id, id)
    
class CreateCardComments(APIResource):
    def post_executor(self, request, token_payload, store, id):
        logger.debug("Entering Project Card Commnts POST")
        try:
            req_body = request.get_json(force=True)
            comment = req_body.get('comment', '')
            to_email = req_body.get('to_email', None)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                if comment and comment != '':    
                    res = project_card.add_card_comment(comment, username, id, store, to_email)
                    if res['status'] == 200:
                        return {'message': res['message']}, res['status']
                    else:
                        return {'message': res['message']}, res['status']
                else:
                    return {'message': 'Please check your payload.'}, 400
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Project Card Commnts POST")    

    def get_executor(self, request, token_payload, store, id):
        logger.debug("Entering Project Card Comment GET")
        try:      
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':        
                res = project_card.get_card_comment(store['id'], id, username)
                if res['status'] == 200:
                    return {'data': res['data']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401                        
        finally:
            logger.debug("Exiting Project Card Comment GET")

    def post(self, id):
        return self.execute_store_request(request, self.post_executor, id)

    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id)      
    
class CardComment(APIResource):
    def put_executor(self, request, token_payload, store, card_id, id):
        logger.debug("Entering Project Card Commnts PUT")
        try:
            req_body = request.get_json(force=True)
            comment = req_body.get('comment', '')
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':
                if comment and comment != '':    
                    res = project_card.update_card_comment(store['id'], comment, card_id, id, username)
                    if res['status'] == 200:
                        return {'message': res['message']}, res['status']
                    else:
                        return {'message': res['message']}, res['status']
                else:
                    return {'message': 'Please check your payload.'}, 400
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Project Card Commnts PUT")
    
    def delete_executor(self, request, token_payload, store, card_id, id):
        logger.debug("Entering Project Card Commnts Delete")
        try:  
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':          
                res =  project_card.delete_card_comment(card_id, id, username)
                if res['status'] == 200:                
                    return {'message': res['message']}, 200
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Project Card Commnts Delete")           

    def put(self, card_id, id):
        return self.execute_store_request(request, self.put_executor, card_id, id)
    
    def delete(self, card_id, id):
        return self.execute_store_request(request, self.delete_executor, card_id, id)
    
class CardHistoryLogs(APIResource):
    def post_executor(self, request, token_payload, store, card_id):
        logger.debug("Entering Project Card History Logs POST")
        try:
            req_body = request.get_json(force=True)
            field_name = req_body.get('field_name', '')
            old_value = req_body.get('old_value', None)
            new_value = req_body.get('new_value', None)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                if field_name == '':     
                    res = project_card.add_card_history_log(store, card_id, field_name, old_value, new_value, username)
                    if res['status'] == 200:
                        return {'message': res['message']}, res['status']
                    else:
                        return {'message': res['message']}, res['status']
                else:
                    return {'message': 'Please check your payload.'}, 400
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Project Card History Logs POST")    

    def get_executor(self, request, token_payload, store, card_id):
        logger.debug("Entering Project Card History Logs GET")
        try:      
            query_param = request.args.to_dict()
            page = query_param.get('page', 1)
            limit = query_param.get('limit', 20)            
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':        
                res = project_card.get_card_history_logs(store['id'], card_id, page, limit, username)
                if res['status'] == 200:
                    return {'data': res['data']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401                        
        finally:
            logger.debug("Exiting Project Card History Logs GET")

    def post(self, card_id):
        return self.execute_store_request(request, self.post_executor, card_id)

    def get(self, card_id):
        return self.execute_store_request(request, self.get_executor, card_id)      

class ProjectMapping(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Project Mapping GET")
        try:      
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':        
                res = project_mapping.get_project_mapping_tables(store['id'])
                if res['status'] == 200:
                    return {'data': res['data']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401                        
        finally:
            logger.debug("Exiting Project Mapping GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class StatusMappingDropDown(APIResource):
    def get_executor(self, request, token_payload, store, db_table_id):
        logger.debug("Entering Status Mapping GET")
        try:     
            query_param = request.args.to_dict()
            column_mapping = query_param.get('column_mapping', 'true') 
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':        
                res = project_mapping.get_module_table_statuses(store['id'], db_table_id, column_mapping)
                if res['status'] == 200:
                    return {'data': res['data']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401                        
        finally:
            logger.debug("Exiting Status Mapping GET")

    def get(self, db_table_id):
        return self.execute_store_request(request, self.get_executor, db_table_id)

    
class ProjectStatusMappingDropDown(APIResource):
    
    def post_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Status Mapping POST")
        try:
            req_body = request.get_json(force=True)
            column_key = req_body.get('column_key', None)
            db_table_id = req_body.get('table_id', None)
            default_assignee = req_body.get('default_assignee', None)
            ticket_name = req_body.get('ticket_name', None)
            project_type = req_body.get('project_type', None)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':    
                res = project_mapping.set_module_table_status_value(store['id'], db_table_id, column_key, project_id, default_assignee, ticket_name, project_type)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Status Mapping POST")

    def post(self, project_id):
        return self.execute_store_request(request, self.post_executor, project_id)

class ColumnStatusMappingDropDown(APIResource):
    def get_executor(self, request, token_payload, store, db_table_id, column):
        logger.debug("Entering Status Mapping GET")
        try:      
            query_param = request.args.to_dict()
            page = query_param.get('page', 1)
            limit = query_param.get('limit', 50) 
            search = query_param.get('search', '')
            project_id = query_param.get('project_id', '')
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':        
                res = project_mapping.get_unique_column_values_for_mapping(store['id'], db_table_id, column, page, limit, search, project_id)
                if res['status'] == 200:
                    return {'data': res['data']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401                        
        finally:
            logger.debug("Exiting Status Mapping GET")
    
    def get(self, db_table_id, column):
        return self.execute_store_request(request, self.get_executor, db_table_id, column)


class StatusMapping(APIResource):
    def post_executor(self, request, token_payload, store, project_id, column_id):
        logger.debug("Entering Status Mapping POST")
        try:
            req_body = request.get_json(force=True)
            # db_table_id = req_body.get('db_table_id', None)
            column_value = req_body.get('column_value', '')
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':    
                res = project_mapping.map_status_to_column(store, project_id, column_id, column_value)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Status Mapping POST")

    def post(self, project_id, column_id):
        return self.execute_store_request(request, self.post_executor, project_id, column_id)
    
class CustomFieldStatusMapping(APIResource):
    def post_executor(self, request, token_payload, store, project_id, field_id):
        logger.debug("Entering Status Mapping POST")
        try:
            req_body = request.get_json(force=True)
            # db_table_id = req_body.get('db_table_id', None)
            column_value = req_body.get('column_value', '')
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':    
                res = project_mapping.map_status_to_custom_field(store, project_id, field_id, column_value)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Status Mapping POST")

    def post(self, project_id, field_id):
        return self.execute_store_request(request, self.post_executor, project_id, field_id)
    

class TaskReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Task Report GET")
        try:
            query_params = request.args.to_dict()
            project_id = query_params.get('project_id', None)
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 20))
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            report_type = query_params.get('report_type', 'project_task_report').strip() if query_params.get('report_type', 'project_task_report').strip() != '' else 'project_task_report'
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':        
                res = project_task_report.get_task_report(store['id'], project_id, username, page, limit, report_type, sort_array)
                if res['status'] == 200:
                    return res['data'], res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Task Report GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class MembersReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Members Report GET")
        try:
            query_params = request.args.to_dict()
            project_id = query_params.get('project_id', None)
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 20))
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            report_type = query_params.get('report_type', 'project_members_report').strip() if query_params.get('report_type', 'project_members_report').strip() != '' else 'project_members_report'
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':        
                res = project_members_report.get_members_report(store['id'], project_id, username, page, limit, report_type, sort_array)
                if res['status'] == 200:
                    return res['data'], res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Task Report GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class AllCards(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering All Tickets GET")
        try:
            query_params = request.args.to_dict()
            email = query_params.get('email', None)
            project_id = query_params.get('project_id', None)
            search = query_params.get('search', None)
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 20))
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':        
                res = project_cards.get_all_cards(email, project_id, search, sort_array, page, limit)
                if res['status'] == 200:
                    return res['data'], res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting All Tickets GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ProjectsDropdown(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':        
                res = projects.get_projects_dropdown(username)
                if res['status'] == 200:
                    return res['data'], res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting projects dropdown GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class TimeSheet(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering timesheet GET")
        try:
            query_params = request.args.to_dict()
            project_id = query_params.get('project_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            timezone = query_params.get('timezone', 'UTC')
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            report_type = query_params.get('report_type', 'project_timesheet_report').strip() if query_params.get('report_type', 'project_timesheet_report').strip() != '' else 'project_timesheet_report'
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':        
                res = project_timesheet.get_timesheet(store['id'], project_id, username, start_date, end_date, report_type, sort_array, timezone)
                if res['status'] == 200:
                    return res['data'], res['status']
                else:
                    return res
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting timesheet GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class Matrix(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Matrix GET")
        try:
            query_params = request.args.to_dict()
            project_id = query_params.get('project_id', None)
            x_axis = query_params.get('x_axis', None)
            y_axis = query_params.get('y_axis', None)
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':
                if x_axis != '' and y_axis != '':
                    res = project_matrix.get_matrix(project_id, username, x_axis, y_axis, sort_array)
                    if res['status'] == 200:
                        return res['data'], res['status']
                    else:
                        return {'message': res['message']}, res['status']
                else:
                    return {'message': 'Please provide x_axis and y_axis.'}, 400
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Matrix GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class GlobalFilterSave(APIResource):
    def post_executor(self, request, token_payload, store):
        try:
            payload = request.get_json(force=True)
            username = token_payload['username']
            logger.debug("Entering global filters GET")
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            if user is None:
                return {"message": "Unauthorized access."}, 401  
            if payload:     
                result = replenishment.save_table_columns_global_filters(store, payload, user)
                if result['status'] == 200:
                    return {"message": result['message']}, 200
                else:
                    return {"message": result['message']}, result['status']
            else:    
                return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting global filters GET")

    def post(self):
        return self.execute_store_request(request, self.post_executor)

class GanttChartData(APIResource):
    def get_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Project Guntt Chart GET")
        try:     
            req_body = request.args.to_dict()
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':   
                search = req_body.get('search', '').strip()   
                view = req_body.get('view', '').strip()
                report_type = req_body.get('report_type', 'gantt_chart').strip() if req_body.get('report_type') != '' else 'gantt_chart'
                res = project_gantt_chart.get_gantt_chart_data(store['id'], project_id, username, report_type, search)
                if res['status'] == 200:
                    return res['data']
                else:
                    return {'message': res['message']}, res['status']   
            else:
                return {'messsage': 'Unauthorized.'}, 401                     
        finally:
            logger.debug("Exiting Project Guntt Chart GET")
    
    def patch_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Project Gantt Chart PATCH")
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':    
                res = project_gantt_chart.update_ticket_using_gantt_chart(store, project_id, req_body, username)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Exiting Project Guntt Chart PATCH")
    
    def get(self, project_id):
        return self.execute_store_request(request, self.get_executor, project_id)
    
    def patch(self, project_id):
        return self.execute_store_request(request, self.patch_executor, project_id)
    
class GanttChartDataSorting(APIResource):
    def patch_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Project Guntt Chart Ticket Sorting PATCH")
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']  
            module_id = req_body.get('module_id', None)
            ticket_id = req_body.get('ticket_id', None)       
            ticket_ids = req_body.get('ticket_ids', '')   
            if username != '':    
                res = project_gantt_chart.update_ticket_sorting_for_gantt_chart(project_id, ticket_id, module_id, ticket_ids, username)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Exiting Project Guntt Chart Ticket Sorting PATCH")
    
    def patch(self, project_id):
        return self.execute_store_request(request, self.patch_executor, project_id)
    
class TimeLog(APIResource):
    def get_executor(self, request, token_payload, store, card_id):
        logger.debug("Entering Timelog GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1)) if query_params.get('page', 1) else 1
            limit = int(query_params.get('limit', 20)) if query_params.get('limit', 20) else 20
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':
                res = project_card.get_time_logs(card_id, page, limit)
                if res['status'] == 200:
                    return res['data'], res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Timelog GET")

    def get(self, card_id):
        return self.execute_store_request(request, self.get_executor, card_id)


class TimeLogUpdate(APIResource):
    def patch_executor(self, request, token_payload, store, card_id, time_log_id):
        logger.debug("Entering timelog PATCH")
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':  
                res = project_card.update_time_log(username, req_body, card_id, time_log_id)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"timelog PATCH")

    def delete_executor(self, request, token_payload, store, card_id, time_log_id):
        logger.debug("Entering timelog Delete")
        try: 
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']             
            if username != '':
                res = project_card.delete_time_log(card_id, time_log_id, username)
                if res['status'] == 200:                
                    return {'message': res['message']}, 200
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting timelog Delete")

       
    def delete(self, card_id, time_log_id):
        return self.execute_store_request(request, self.delete_executor, card_id, time_log_id)

    def patch(self, card_id, time_log_id):
        return self.execute_store_request(request, self.patch_executor, card_id, time_log_id)

class ProjectCardAttachments(APIResource):
    def post_executor(self, request, token_payload, store, project_id, card_id):
        try:
            files = request.files.getlist("files")  
            # Check if no files were selected
            if not files or len(files) == 0 or files[0].filename == '':
                return {"data": "No files selected for uploading."}, 400
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                res = project_card.upload_attachments(store['id'], username, files, project_id, card_id)
                if res['status'] == 200:
                    return {"data": res['files']}, 200
                else:
                    return {"message": res['message']}, res['status']
        finally:
            logger.debug("Exiting Image Upload POST")

    def post(self, project_id, card_id):
        return self.execute_store_request(request, self.post_executor, project_id, card_id)
    
class ProjectCardAttachment(APIResource):
    def get_executor(self, request, file_name, project_id, card_id):
        if not file_name:
            return make_response({'error': 'Invalid request parameters'}, 400)

        # file_path = '/app/images/' + type + '/' + file_name
        file_path = os.path.join('/app/images/project', project_id, card_id, file_name)
        # file_path = '/AD/backend/admin-api-service/images/'

        if not os.path.isfile(file_path):
            return make_response({'error': 'File not found'}, 404)

        # Supported file formats
        IMAGE_EXTENSIONS = ('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp', '.svg', '.ico')
        VIDEO_EXTENSIONS = ('.mp4', '.webm', '.ogv', '.ogg', '.avi', '.mov', '.mkv')
        DOCUMENT_EXTENSIONS = ('.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.rtf', '.csv', '.xml', '.json', '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.exe')

        # Handling Images
        if file_name.lower().endswith(IMAGE_EXTENSIONS):
            if request.args.get('thumbnail') == 'true':  # Serve a 100x100 thumbnail
                try:
                    with Image.open(file_path) as img:
                        img.thumbnail((1000, 100))  # Resize while maintaining aspect ratio
                        img_byte_arr = BytesIO()
                        img.save(img_byte_arr, format='PNG')
                        img_byte_arr.seek(0)
                        return send_file(img_byte_arr, mimetype='image/png')
                except Exception as e:
                    logger.error(f"Error processing image: {e}")
                    return make_response({'error': 'Failed to process image'}, 500)
            else:
                return send_file(file_path)  # Serve full-size image

        # Handling Videos
        elif file_name.lower().endswith(VIDEO_EXTENSIONS):
            if request.args.get('thumbnail') == 'true':  # Serve a 100x100 video thumbnail
                thumbnail = project_card.get_video_thumbnail(file_path)
                if thumbnail:
                    return send_file(thumbnail, mimetype='image/png')
                else:
                    return make_response({'error': 'Failed to process video thumbnail'}, 500)
            else:
                return send_file(file_path)  # Serve full video for playback

        # Handling Documents
        elif file_name.lower().endswith(DOCUMENT_EXTENSIONS):
            return send_file(file_path, as_attachment=False)

        return make_response({'error': 'Unsupported file format'}, 415)  # 415 Unsupported Media Type
    
    def delete_executor(self, request, token_payload, store, file_name, project_id, card_id):
        logger.debug("Entering Project Card Attachment DELETE")
        try:
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':
                res = project_card.delete_card_attachment(username, [file_name], project_id, card_id)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'message': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Project Card Attachment DELETE")

    def delete(self, file_name, project_id, card_id):
        return self.execute_store_request(request, self.delete_executor, file_name, project_id, card_id)

    def get(self, file_name, project_id, card_id):
        return self.execute_open_api_request(request, self.get_executor, file_name, project_id, card_id)
    
class CardAttachmentDetails(APIResource):
    def get_executor(self, request, token_payload, store, id):
        logger.debug("Entering Project Card Attachment GET")
        try:      
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':        
                res = project_card.get_card_attachment(store['id'], id, username)
                if res['status'] == 200:
                    return {'data': res['data']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401                        
        finally:
            logger.debug("Exiting Project Card Attachment GET")

    def delete_executor(self, request, token_payload, store, id):
        logger.debug("Entering Project Card Attachments DELETE")
        try:
            query_params = request.args.to_dict()
            file_names = query_params.get('file_names', '')
            file_name_array = file_names.split(',') if file_names else []
            project_id = query_params.get('project_id', '')
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':
                res = project_card.delete_card_attachment(username, file_name_array, project_id, id)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'message': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Project Card Attachments DELETE")

    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id) 
    
    def delete(self, id):
        return self.execute_store_request(request, self.delete_executor, id)
    
class ProjectCardDelete(APIResource):
    def delete_executor(self, request, token_payload, store, project_id):
        logger.debug("Entering Project card Delete")
        try:   
            payload = request.get_json(force=True)
            card_ids = payload.get('card_ids', [])  

            if not card_ids or not isinstance(card_ids, list):
                return {'message': 'card_ids must be a non-empty list'}, 400
            
            res =  project_card.delete_card(project_id, card_ids)
            if res['status'] == 200:                
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Project card Delete")

    def delete(self, project_id):
        return self.execute_store_request(request, self.delete_executor, project_id)