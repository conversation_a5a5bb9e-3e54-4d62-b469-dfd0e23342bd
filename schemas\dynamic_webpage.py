from schema import Schema, Or, Optional

component_schema = Schema([{
	"name": str,	
	"variants":{
		"id" : str,
		"name": str,
		"admin_layout": str,
		"class": [],
		"style": str,
		"config": {
            "image": str,
            "image_url": str,
            "title": str,
            "visiblity": <PERSON><PERSON><PERSON>(Or(1, 0))
		}
	}
}])

webpage_schema = Schema({
    'name': str
})

version_schema = Schema({
    'version': str
})

webpage_update_schema = Schema({
    'name': str,
    'status': str,
    'status_update': str
})