from flask import request
import logging
import traceback
from api import APIResource
from schemas.theme_build import theme_build_schema
import netlify
from utils import store_util
from builds import builds_service

logger = logging.getLogger()

class Build(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Builds GET")
        
        try:
          store_id = store['id']
          res = builds_service.get_all_builds(store_id)
          return res, 200
        finally:
            logger.debug("Exiting Builds GET")

    def post_executor(self, request, token_payload, store):
        try:
            req_body = request.get_json(force=True)           
            validated_data = theme_build_schema.validate(req_body)
            response = builds_service.create_build(store, validated_data)   

            if response['status'] == 200:
                return {"build_id": response['message']}, 200
            else:
                return {"message": response['message']}, 500  
        finally:
            logger.debug("Exiting Build POST")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
        
    def post(self):
        return self.execute_store_request(request, self.post_executor)
    

class NetlifyBuilds(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Netlify Builds GET")
        try:
          res = netlify.get_all_netlify_builds(store)
          if res['status'] == 200:
              return {"builds": res['builds']}, 200
          else: 
              return {"message": res['message']}, res['status']          
        finally:
            logger.debug("Exiting Netlify Builds GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class PublishedBuild(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Published Build GET")
        try:
          res = netlify.get_published__build(store)       
          if res['status'] == 200:
              return {"data": res['published_deploy']}, 200
          else: 
              return {"message": res['message']}, res['status']          
        finally:
            logger.debug("Exiting Netlify Builds GET")

    def post_executor(self, request, token_payload, store):
        try:
            req_body = request.get_json(force=True)                       
            if req_body and 'deploy_id' in req_body:                    
                response = netlify.activate_selected_deploy(store, req_body)   
                               
                if response['status'] == 200:
                    return {"message": response['message']}, 200
                else:
                    return {"message": response['message']}, response['status']   
            else:
                return {"message": "Please esure that your request body is correct."}, 400                            

        finally:
            logger.debug("Exiting Activate Build POST")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
class TriggerBuild(APIResource):
    def post_executor(self, request, token_payload, store):
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']                       
            if req_body and 'description' in req_body:                    
                response = netlify.trigger_new_build(store, req_body, username)  
                if response['status'] == 200:
                    return {"message": response['message']}, 200
                else:
                    return {"message": response['message']}, response['status']   
            else:
                return {"message": "Please esure that your request body is correct."}, 400                            

        finally:
            logger.debug("Exiting Trigger Build POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
class CurrentBuild(APIResource):
    def get_executor(self, request, token_payload, store, build_id):
        logger.debug("Entering Current Build GET")
        try:
          res = netlify.get_current__build(store, build_id)                 
          if res['status'] == 200:
              return {"data": res['current_build']}, 200
          else: 
              return {"message": res['message']}, res['status']          
        finally:
            logger.debug("Exiting Current Builds GET")

    def get(self, build_id):
        return self.execute_store_request(request, self.get_executor, build_id)
    
class CancelBuild(APIResource):
    def post_executor(self, request, token_payload, store):
        try:
            req_body = request.get_json(force=True)                       
            if req_body and 'deploy_id' in req_body:                    
                response = netlify.set_cancel_build(store, req_body)                                   
                if response['status'] == 200:
                    return {"message": response['message']}, 200
                else:
                    return {"message": response['message']}, response['status']   
            else:
                return {"message": "Please esure that your request body is correct."}, 400                            
        finally:
            logger.debug("Exiting Cancel Build POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)

