from sqlalchemy.dialects.postgresql import insert
import pg_db as db
from sqlalchemy import Boolean, Column, DateTime, String, Integer, ForeignKey, Float
from sqlalchemy.sql import func

class WebVisitors(db.Base):
    __tablename__ = "web_visitors"
    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(String)
    customer_name = Column(String)
    email = Column(String)
    channel = Column(String)
    ip_address = Column(String)
    company_name = Column(String)
    location = Column(String)
    created_at = Column(DateTime, onupdate=func.now())
    ip_company_name = Column(String)
