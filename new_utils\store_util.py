import new_mongodb
from config.appconfig import ADConfigKey
from config import appconfig
from new_utils import cache_util
from mongo_db import store_db
from enum import Enum

class AppName(Enum):
    skuvault = 'skuvault'
    salesforce = 'salesforce'

def get_active_stores():
    return new_mongodb.get_active_stores()

def get_store_by_id(store_id):
    store = cache_util.get_store(store_id)
    if not store:
        store = new_mongodb.get_store_by_id(store_id)
    return store

def get_store_by_store_hash(store_hash):
    store = cache_util.get_store(store_hash)
    if not store:
        store = new_mongodb.get_store_by_store_hash(store_hash)
    return store

def get_store_by_domain(store_domain):
    store = cache_util.get_store(store_domain)
    if not store:
        store = new_mongodb.get_store_by_domain(store_domain)
    return store

def get_redis_config(store_id):
    return new_mongodb.get_redis_config(store_id)

def get_db_config_from_store(store, app_name):
    profile = appconfig.get_app_profile()    
    return store.get(ADConfigKey.AD_CONFIG_KEY, {}).get(profile, {}).get(ADConfigKey.DB_INFO_KEY, {}).get(app_name, None)

def get_mongodb_name(store, app_name):
    db_name = None
    if store:
        app_db_info = get_db_config_from_store(store, app_name)
        if app_db_info is not None:
            db_name = app_db_info.get(ADConfigKey.DB_NAME_KEY, None)
    return db_name

def get_store_mongodb_name(store):
    return get_mongodb_name(store, ADConfigKey.STORE_DB_KEY)

def get_admin_mongodb_name(store):
    return get_mongodb_name(store, ADConfigKey.ADMIN_DB_KEY)

def get_skuvault_api_info(store_id):
    return fetch_app_api_data(store_id, AppName.skuvault.value)

def get_salesforce_api_data(store_id):
    return store_db.fetch_app_api_data(store_id, AppName.salesforce.value)

def fetch_app_api_data(store_id, app_name):
    app_data = None
    store = get_store_by_id(store_id)
    if store:
        app_data = store.get("apps", {}).get(app_name, None)
    return app_data
