# import datetime
from datetime import datetime
import logging
from fields.cms_fields import cms_fields_navigation

from bson import ObjectId
import mongo_db
import new_mongodb
from utils.common import parse_json, processList, processListCategory


logger = logging.getLogger()

CMS_COLLECTION = "cms"
SUB_NAVIGATION_COLLECTION = "sub_navigations"
WEB_PAGES="web_pages"
DYNAMIC_PAGES="pages"
BRANDS_COLLECTION = "cms_brands"

def get_category_by_id(id):
    db = mongo_db.get_admin_db_client()
    result= db[CMS_COLLECTION].find_one({"_id": ObjectId(id)})
    return result

def get_category_by_bc_id(id):
    db = mongo_db.get_admin_db_client()
    result= db[CMS_COLLECTION].find_one({"id": int(id)})
    return result

def get_brand_by_id(id):
    db = mongo_db.get_admin_db_client()
    result= db[BRANDS_COLLECTION].find_one({"_id": ObjectId(id)})
    return result

def get_all_categories():
    db = mongo_db.get_admin_db_client()
    result= db[CMS_COLLECTION].find()
    data = processListCategory(result)
    return parse_json(data)



def get_page_by_name(url):
    db = mongo_db.get_admin_db_client()
    result= db[CMS_COLLECTION].find_one({"url": url},{"id":1})
    return result

def delete_category_by_id(category_id):
    db = mongo_db.get_admin_db_client()
    doc = db[CMS_COLLECTION].delete_one({'id': int(category_id)})

def processDocumentCms(obj):
        if obj:
            if 'id' in obj:
                obj['id']=obj['id']
            if '_id' in obj:
                # obj['id'] = str(obj['_id'])
                del obj['_id']
        
            for key, value in obj.items():
                if isinstance(value, datetime) or isinstance(value, ObjectId):
                    obj[key] = str(value)

        return obj

def processListCms( data):
        result = []
        if data:
            for _obj in data:
                result.append(processDocumentCms(_obj))
        return result

def processDocumentPages(obj):
        if obj:
            if 'bc_id' in obj:
                obj['id']=obj['bc_id']
                del obj['bc_id']
            else:
                 obj['id']=str(obj['_id'])
            if '_id' in obj:
                # obj['id'] = str(obj['_id'])
                del obj['_id']
        
            for key, value in obj.items():
                if isinstance(value, datetime) or isinstance(value, ObjectId):
                    obj[key] = str(value)

        return obj

def processListPages( data):
        result = []
        if data:
            for _obj in data:
                result.append(processDocumentPages(_obj))
        return result

def get_categories(payload,fields,store):
        db = new_mongodb.get_admin_db_client(store)
            
        def get_query(search_result):
            # return all products if search params is empty.
            if search_result == "":
                return {"is_visible": True}

            # return text based search if search param's exists.        
            return {"$or": [{'name':{"$regex": search_result, "$options": "i"}}]}                                  

        def set_limit(limit):
            page_limit = 0
            skips = 0
            
            if int(limit) != 0 and int(limit) > 0:
                page_limit = int(limit)

            return page_limit, skips
        
        page_limit , skips = set_limit(payload['limit'] if 'limit' in payload else 0)
        
        query = get_query(payload["search"]) if "search" in payload else {"is_visible": True} 
        result= db[CMS_COLLECTION].find(query, fields).skip(skips).limit(page_limit)
        result=processListCms(result)
        return result

def get_webpages(payload,fields,store):
        db = mongo_db.get_store_db_client(store)
            
        def get_query(search_result):
            # return all products if search params is empty.
            if search_result == "":
                return {"is_visible": True}

            # return text based search if search param's exists.        
            return {"$or": [{'name':{"$regex": search_result, "$options": "i"}}]}                                  

        def set_limit(limit):
            page_limit = 0
            skips = 0
            
            if int(limit) != 0 and int(limit) > 0:
                page_limit = int(limit)

            return page_limit, skips
        
        page_limit , skips = set_limit(payload['limit'] if 'limit' in payload else 0)
        
        query = get_query(payload["search"]) if "search" in payload else {"is_visible": True} 
        result= db[DYNAMIC_PAGES].find(query, fields).skip(skips).limit(page_limit)
        result=processListPages(result)
        return result 

def create(payload):
    db = mongo_db.get_admin_db_client()
    result=db[CMS_COLLECTION].insert_one(payload)
    return result

def update(query, payload):
    db = mongo_db.get_admin_db_client()
    result=db[CMS_COLLECTION].update_one(query, payload)
    return result

def update_brands(query, payload):
    db = mongo_db.get_admin_db_client()
    result=db[BRANDS_COLLECTION].update_one(query, payload)
    return result

def has_categories_with_parent_id(parent_id):
    db = mongo_db.get_admin_db_client()
    result= db[CMS_COLLECTION].find_one({"parent_id": int(parent_id)})
    return result is not None

def add_has_sub_child_flag(id,has_sub_child):
    db = mongo_db.get_admin_db_client()
    if id:
        db[CMS_COLLECTION].update_one(
            {"_id":ObjectId(id)},
            {"$set": {"has_sub_child": has_sub_child}}
        )
    
def get_categories_by_parent_id(parent_id):
    db = mongo_db.get_admin_db_client()
    pipeline = [
        {"$match": {"parent_id": int(parent_id)}},  # Filter documents by parent_id
        {"$addFields": {"versions": {"$filter": {
            "input": "$versions",  # Input array to filter
            "as": "version",  # Variable name for each element
            "cond": {"$eq": ["$$version.status", "active"]}  # Condition to filter by status
        }}}},
        {"$project": {"_id":1,"id":1,"name":1,"created_at":1,"updated_at":1,"url":1,"status":1,"is_visible":1,"parent_id":1,"views":1,"sort_order":1,"type":1,"is_customers_only":1,"versions": "$versions","has_sub_child":1}}  # Project only the active_versions field
    ]
    result= db[CMS_COLLECTION].aggregate(pipeline)
    result=processListCategory(result)
    return result

def get_sub_nav(store):
    db = mongo_db.get_store_db_client(store)
    result=db[SUB_NAVIGATION_COLLECTION].find()
    return result

def update_sub_nav_by_id(store, data):
    customer_id = data['id']
    db = mongo_db.get_store_db_client(store)
    doc = db[SUB_NAVIGATION_COLLECTION].update_one({"_id": ObjectId(customer_id)}, { "$set": data }, upsert=True)

def get_webpage_by_name(store,url):
    db = mongo_db.get_store_db_client(store)
    result= db[WEB_PAGES].find_one({"url": url},{"id":1})
    return result

def get_brand_by_name(store,url):
    db = mongo_db.get_store_db_client(store)
    result= db[BRANDS_COLLECTION].find_one({"custom_url.url": url},{"id":1})
    return result

def get_paginated_records_updated(store, payload, fields, additionalQuery):
        
        db = mongo_db.get_store_db_client(store)
        collection = db[DYNAMIC_PAGES]
        sort_by = payload.get('sort_by', 'date_created')  # Default sort_by to 'date_created' if not provided
        sort_order = 1 if payload.get('sort_order', 'asc') == 'asc' else -1  # Default sort_order to ascending if not provided

        def create_reg_ex_query(filterBy, filter):
            query = {"$or": []}

            for i in filterBy:
                query['$or'].append({i: {"$regex": filter, "$options": "i"}})

            if "type" not in query and "type" in payload:
                query["type"] = payload["type"]

            if "status" in payload:
                if payload["status"] == "active":
                    query["is_visible"] = True
                elif payload["status"] == "inactive":
                    query["is_visible"] = False
                elif payload["status"] == "out_of_stock":
                    query["inventory_level"] = 0

            query.update(additionalQuery)
            return query

        limit = int(payload.get("limit", 10))
        page = int(payload.get("page", 1))
        skips = payload.get('skips', 0)

        query = create_reg_ex_query(payload.get("filterBy", []), payload.get('filter', ''))
        skips = limit * (page - 1)
        # Aggregation pipeline to sort the documents with empty values for the specified field at the end
        aggregation_pipeline = [
            {"$match": query},  # Match documents based on the query
            {"$addFields": {sort_by + "_exists": {"$ne": ["$" + sort_by, ""]}}},  # Add a field to indicate whether sort_by exists
            {"$addFields": {sort_by + "_is_empty": {"$eq": ["$" + sort_by, ""]}}},  # Add a field to indicate whether sort_by is empty
            {"$sort": {sort_by + "_is_empty": 1, sort_by: sort_order}},  # Sort documents based on sort_by and sort_by_is_empty
            {"$skip": skips},  # Skip documents
            {"$limit": limit},  # Limit the number of documents returned
            {"$project": {sort_by + "_exists": 0, sort_by + "_is_empty": 0}}  # Exclude the added fields from the final result
        ]

        data = collection.aggregate(aggregation_pipeline)

        # ProcessList ...
        data = processList(data)

        document_length = collection.count_documents(query)

        return parse_json(data), document_length, page, limit
