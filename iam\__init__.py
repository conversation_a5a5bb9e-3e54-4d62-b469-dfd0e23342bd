from enum import Enum

class Constants:
    store_token_expiry_duration = 7*24*60*60
    admin_token_expiry_duration = 7*24*60*60
    customer_login_token_expiry_duration = 60*2
    jwt_token_algorithm = 'HS256'

class TokenKeys:
    CLIENT_ID = "client_id"
    SECRET = "secret"
    EXPIRY = "exp"
    ACCESS_TOKEN = "access_token"
    STORES = "stores"
    USERNAME = "username"
    NAME = "name"
    DEFAULT_STORE = "default_store"
    TENANT_ID = "tenant_id"

#old logic only for refrence
def build_side_menu_old(features, permissions):
    side_menu = {}
    user_permissions = {}
    for feature, content in features.items():        
        feature_permission = permissions[feature]
        children = feature_permission['children']
        item_name = feature_permission['name']
        parent_menu_item = {
            'label': content['label'],
            'route': content['route'],
            'icon': content['icon'],
            'children': {}
        }
        has_permission = False
    
        if len(children) > 0:
            flag = False

            for child in children:
                child_name = child

                for key, value in children.items():
                    if child_name == key:
                        for permission_key, permission_value in value[key].items():
                            user_permissions.update({child_name + "-" + permission_key: permission_value})
                
                if children[child_name][child_name]['read']:
                    flag = True
                    side_menu_child = content['children'].get(child, None)
                    if side_menu_child:
                        parent_menu_item['children'].update({
                                child_name: {
                                    "label": side_menu_child['label'],
                                    "route": side_menu_child['route'],
                                    "icon": side_menu_child['icon'],
                                    'children': side_menu_child['children']
                                }
                            })

            if flag == True:
                has_permission = True
                side_menu.update({item_name: parent_menu_item})

        else:
            if feature_permission[item_name]['read']:
                has_permission = True
                side_menu.update({item_name: parent_menu_item})

        if has_permission:
            for key, value in feature_permission[feature].items():
                user_permissions.update({feature_permission['name'] + "-" + key: value})

    result = {
        "side_menu": side_menu,
        "user_permissions": user_permissions
    }
    return result

#old logic only for refrence

def build_side_menu(features, permissions):
    def process_children(permissions_item, store_side_menu_item):
        if not permissions_item.get('operations', {}).get('read'):
            return None  # Skip this item if no 'read' permission for the parent
        item_name = permissions_item.get('name', '')
        side_menu_item = {
            'label': store_side_menu_item['label'],
            'route': store_side_menu_item['route'],
            'icon': store_side_menu_item['icon'],
            'operations': permissions_item.get('operations', {}),
            'name': store_side_menu_item['name'],
            'api': store_side_menu_item['api'],
            'children': {}
        }
        has_permission = True

        children = permissions_item.get('children', {})
        if children:
            for child_key, child_permissions in children.items():
                child_menu_item = store_side_menu_item['children'].get(child_key)
                if child_menu_item:
                    child_side_menu = process_children(child_permissions, child_menu_item)
                    if child_side_menu:
                        side_menu_item['children'][child_key] = child_side_menu
                        has_permission = True
            if not side_menu_item['children']:
                has_permission = False

        return (side_menu_item if has_permission else None)

    side_menu = {}

    for menu_item in features:
        if menu_item in {'_id', 'store_id', 'created_at', 'updated_at'}:
            continue

        permissions_item = permissions.get(menu_item)
        if not permissions_item:
            continue

        store_side_menu_item = features[menu_item]
        processed_menu_item = process_children(permissions_item, store_side_menu_item)

        if processed_menu_item:
            side_menu[menu_item] = processed_menu_item

    result = {
        "side_menu": side_menu
    }
    return result
