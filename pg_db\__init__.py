from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text
from utils.common import parse_json
import datetime
import appconfig
import atexit
import logging

logger = logging.getLogger()

Base = declarative_base()

analytics_products_trend_table = "analytics_products_trend"
analytics_variants_trend_table = "analytics_variants_trend"
products = "products"
order_shipping_addresses = "order_shipping_addresses"
customers = "customers"
salesforce_customer_rep = "salesforce_customer_rep"
bo_bulk_order_products = "bo_bulk_order_products"
bo_product_variants = "bo_product_variants"
business_units = "business_units"
projects = "agile_projects"
project_access = "agile_project_access"
project_modules = "agile_project_modules"
project_cards = "agile_project_cards" 
agile_project_cardindex = "agile_project_cardindex" 
project_columns = "agile_project_columns"
agile_project_customfield = "agile_project_customfield"
agile_customfield_meta = "agile_customfield_meta"
agile_customfield_value = "agile_customfield_value"
agile_project_boards = "agile_project_boards"
po_reorders_table = "po_reorders"
bo_purchase_orders="bo_purchase_orders"
bo_purchase_order_lineitems="bo_purchase_order_lineitems"
bo_purchase_order_bc_order_mapping="bo_purchase_order_bc_order_mapping"
express_orders="express_orders"
order_consignment="order_consignment"
chat_messages_table = "chat_messages"
chat_users_table="chat_users"
chat_user_mapping_table="chat_user_mapping"
user_supplier_mapping_table = 'user_supplier_mapping'
product_tags_table = "product_tags"
tags_table = "tags"
bo_bulk_products_brands = "bo_bulk_products_brands"
pipeline_db_tables = "pipeline_db_tables"
pipeline_project_table_mapping = "pipeline_project_table_mapping"
pipeline_column_mapping = "pipeline_column_mapping"
pipeline_modules = "pipeline_modules"
pipeline_custom_field_mapping = "pipeline_custom_field_mapping"

class PGDBConnectionPool:
    _instance = None
    _engine = None

    def __new__(cls):
        if cls._instance is None:
            logger.info('Creating the PG DB connection pool')
            cls._instance = super(PGDBConnectionPool, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        logger.info("Entering PGDBConnectionPool")
        cur_time = str(datetime.datetime.now())
        self.engine = create_engine(appconfig.get_pgdb_conn_str(), connect_args={"application_name":cur_time},
                                    pool_size=5, max_overflow=10, pool_pre_ping=True, echo=False)
        from pg_db import tag_db, whatsapp_db, express_orders_db, bulk_orders_db, live_carts_db, csv_upload_details_db, replenishment_db, product_inquiries_db, pricelist_change_logs_db, projects_db, notifications_db, web_visitors_db, brand_purchaser_mapping_db, order_audit_report_db
        Base.metadata.create_all(self.engine)
        logger.info("Exiting PGDBConnectionPool")

    def get_connection(self):
        return self.engine.connect()
    
    def get_session(self):
        session = sessionmaker(bind=self.engine)
        return session()

conn_pool = PGDBConnectionPool()


@atexit.register
def shutdown_db_engine():
    logger.error("pg_db: shutdown_db_engine: Exiting from the application. Closing DB connection pool")
    conn_pool.engine.dispose()

def get_connection():
    return conn_pool.get_connection()

def get_session():
    return conn_pool.get_session()
    
def create_all_table():
    pass

def run_and_parse_query(query, conn):
    rs = conn.execute(text(query))
    obj = parse_json(rs)
    return obj

def run_query(query):
    result = []
    conn = get_connection()
    try:
        rs = conn.execute(text(query))
        for row in rs:
            result.append(list(row))
    finally:
        conn.close()
    return result

def truncate_table(table_name):
    conn = get_connection()
    result = False
    try:
        query = f"TRUNCATE {table_name} CASCADE"
        conn.execute(text(query))
        conn.commit()
        result = True
    finally:
        conn.close()    
    return result

def execute_stmt(statment, values=None, session=None):
    local_session = None
    if not session:
        session = get_session()
        local_session = session
    try:
        if values:
            session.execute(statment, values)
        else:
            session.execute(statment)
    finally:
        if local_session:
            local_session.commit()
            local_session.close()