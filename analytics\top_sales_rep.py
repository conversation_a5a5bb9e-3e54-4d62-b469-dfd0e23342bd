import pg_db
import datetime
from sqlalchemy import text

def get_top_sales_rep( start_date, end_date,sort_order):
    date_format = "%Y-%m-%d"
    date_start = datetime.datetime.strptime(start_date, date_format)
    date_end = datetime.datetime.strptime(end_date, date_format)
    date_end = date_end.replace(hour=23, minute=59, second=59)
    
    query = None
   
    query = """
            WITH representative_revenue AS (
        SELECT
            scr.rep_name,
            SUM(o.total_including_tax) AS revenue
        FROM
            salesforce_customer_rep scr
        JOIN
            orders o ON scr.customer_id = o.customer_id
        WHERE
            o.order_created_date_time BETWEEN '{start_date}' AND '{end_date}'
            AND o.order_status_id NOT IN (0,5,6,7)
        GROUP BY
            scr.rep_name
        )
        SELECT
        rep_name,
        revenue
        FROM
        representative_revenue
        ORDER BY
        revenue {sort_order}
    """.format(start_date=date_start, end_date=date_end,sort_order=sort_order)

    result_list = []

    if query:
        query=query.replace('\n', '')
        conn = pg_db.get_connection()
        try:
            rs = conn.execute(text(query))
            revenue_total=0
            
            for row in rs:
                result={}
                result['rep_name']=row[0]
                result['revenue']=round(row[1],2)
                revenue_total += result['revenue']
                result_list.append(result)
            for result in result_list:
                percentage=(result['revenue']/revenue_total)*100
                result['percentage']=round(percentage,2)

        finally:
            conn.close()

        return result_list