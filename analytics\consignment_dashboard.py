import logging
import traceback
import new_pgdb
from new_pgdb import order_consignment_db
import new_utils
from new_mongodb import store_catalog_db
from sqlalchemy import text
from . import calculate_pagination
from new_pgdb.analytics_db import AnalyticsDB
from appconfig import is_pgdb_read_only_enabled

logger = logging.getLogger()

def get_consignment_order_listing(store, page, limit, search_term, sort_array=[]):
    response = {
        "status" :400
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        search_condition = ""
        if search_term:
            search_condition = f"AND p.product_name ILIKE '%{search_term}%'"

        count_query = f"""SELECT COUNT(DISTINCT oc.product_id) FROM order_consignment AS oc
                        JOIN products AS p ON oc.product_id = p.product_id
                        WHERE oc.order_type = 'consignment' {search_condition}"""
        
        res = conn.execute(text(count_query))
        total_count_row = res.fetchone()
        total_count = total_count_row[0] if total_count_row else 0

        query = f"""SELECT p.product_id, p.product_name, p.sku, sum(oc.quantity) AS quantity, p.inventory_level, p.is_visible
                FROM order_consignment AS oc
                JOIN products AS p ON oc.product_id = p.product_id
                WHERE oc.order_type = 'consignment' {search_condition}
            """
        
        query += " GROUP BY p.product_id,p.product_name"
        
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ["quantity"]:                
                query += f" ORDER BY sum(oc.{sort_array[0]}) {sort_direction}"                
            elif sort_array[0] in ["inventory_level"]: 
                query += f" ORDER BY p.inventory_level {sort_direction}"
            elif sort_array[0] in ["is_visible"]:
                query += f" ORDER BY p.is_visible {sort_direction}"
            elif sort_array[0] in ["sku"]:
                query += f" ORDER BY p.sku {sort_direction}"
            

        offset = (page - 1) * limit
        query += f" LIMIT {limit} OFFSET {offset}"
    
        base_query = text(query)
        results = conn.execute(base_query)
        orders = []
        for row in results.fetchall():
            row_data = {
                'product_id': row[0],
                'product_name': row[1],
                'variant_sku': row[2],
                'quantity': row[3],
                'inventory_level': row[4],
                'is_visible': row[5]
            }
            orders.append(row_data)

        data = calculate_pagination(orders, page, limit, total_count)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 404
            response['message'] = 'No data found.'
    finally:
        if conn:
            conn.close()
    return response


def get_on_hold_order_listing(store, page, limit, search_term, sort_array=[]):
    response = {
        "status" :400
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        search_condition = ""
        if search_term:
            search_condition = f"AND p.product_name ILIKE '%{search_term}%'"

        count_query = f"""SELECT COUNT(DISTINCT oc.product_id) FROM order_consignment AS oc
                        JOIN products AS p ON oc.product_id = p.product_id
                        WHERE oc.order_type = 'on-hold' {search_condition}"""
        
        res = conn.execute(text(count_query))
        total_count_row = res.fetchone()
        total_count = total_count_row[0] if total_count_row else 0

        query = f"""SELECT p.product_id, p.product_name, p.sku, sum(oc.quantity) AS quantity, p.inventory_level, p.is_visible
                FROM order_consignment AS oc
                JOIN products AS p ON oc.product_id = p.product_id
                WHERE oc.order_type = 'on-hold' {search_condition}
            """
        
        query += " GROUP BY p.product_id,p.product_name"
        
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ["quantity"]:                
                query += f" ORDER BY sum(oc.{sort_array[0]}) {sort_direction}"                
            elif sort_array[0] in ["inventory_level"]: 
                query += f" ORDER BY p.inventory_level {sort_direction}"
            elif sort_array[0] in ["is_visible"]:
                query += f" ORDER BY p.is_visible {sort_direction}"

        offset = (page - 1) * limit
        query += f" LIMIT {limit} OFFSET {offset}"
    
        base_query = text(query)
        results = conn.execute(base_query)
        orders = []
        for row in results.fetchall():
            row_data = {
                'product_id': row[0],
                'product_name': row[1],
                'variant_sku': row[2],
                'quantity': row[3],
                'inventory_level': row[4],
                'is_visible': row[5]
            }
            orders.append(row_data)

        data = calculate_pagination(orders, page, limit, total_count)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 404
            response['message'] = 'No data found.'
    finally:
        if conn:
            conn.close()
    return response


def get_top_products_consignment(store, start_date, end_date, sort):
    response = {
        "status" :400
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        query = text (
            f"""SELECT 
                    p.product_id, 
                    p.product_name,
                    SUM(p.price * oc.quantity) as value,
                    (SELECT SUM(p.price * oc.quantity) 
                    FROM order_consignment oc
					JOIN {AnalyticsDB.get_products_trend_table()} p ON oc.product_id = p.product_id
                    WHERE oc.order_type = 'consignment') AS total_value
                FROM 
                     order_consignment oc
                JOIN 
                    {AnalyticsDB.get_products_trend_table()} p ON oc.product_id = p.product_id
                WHERE 
                    oc.order_type = 'consignment'
                    AND p.order_date_time BETWEEN :start_date AND :end_date
                GROUP BY
                    p.product_id, p.product_name
                ORDER BY 
                    value DESC
                LIMIT 10;
            """
        )
        results = conn.execute(query.params(start_date=start_date, end_date=end_date))
        data = []
        for row in results.fetchall():
            percentage = round((row[2] / row[3]) * 100, 2)
            value = "{:,.0f}".format(row[2])
            row_data = {
                'product_id': row[0],
                'product_name': row[1],
                'value': value,
                'percentage': percentage
            }
            data.append(row_data)

        if sort.lower() == 'asc':
            data = sorted(data, key=lambda x: x['percentage'])
        else:
            data = sorted(data, key=lambda x: x['percentage'], reverse=True)

        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response

def get_top_customers_consignment(store, page, limit, sort_array=[]):
    response = {
        "status" :400
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        count_query = f"""SELECT COUNT(DISTINCT soc.customer_id) FROM salesforce_order_consignment AS soc
                        JOIN customers c ON soc.customer_id = c.customer_id"""
        
        res = conn.execute(text(count_query))
        total_count_row = res.fetchone()
        total_count = total_count_row[0] if total_count_row else 0

        query =  f"""SELECT soc.customer_id, c.first_name, c.last_name, c.email,
                    SUM(
                        CASE 
                            WHEN soc.order_status = 'Unpaid' THEN soc.total_amount
                            WHEN soc.order_status = 'Partial' THEN soc.due_amount
                            ELSE 0
                        END
                    ) AS total_due_amount,
                    COUNT(DISTINCT soc.order_id) AS order_count,
                    SUM(soc.total_amount) AS total_amount,
                    scr.rep_name,
                    scr.rep_email
                    FROM salesforce_order_consignment soc
					JOIN 
                        customers c ON soc.customer_id = c.customer_id
                    JOIN
                        salesforce_customer_rep scr on soc.customer_id = scr.customer_id
                    GROUP BY
                        soc.customer_id, c.first_name, c.last_name, c.email, scr.rep_name, scr.rep_email"""
        
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'                   
            query += f" ORDER BY {sort_array[0]} {sort_direction}"                

        offset = (page - 1) * limit
        query += f" LIMIT {limit} OFFSET {offset}"
        base_query = text(query)
        results = conn.execute(base_query)

        orders = []
        for row in results.fetchall():
            # percentage = round((row[2] / row[3]) * 100, 2)
            due_value = "{:,.0f}".format(row[4])
            total_value = "{:,.0f}".format(row[6])
            row_data = {
                'customer_id': row[0],
                'customer_name': row[1] + ' ' + row[2],
                'email': row[3],
                'total_due_amount': due_value,
                'order_count': row[5],
                'total_amount': total_value,
                'rep_name': row[7],
                'rep_email': row[8]
            }
            orders.append(row_data)

        data = calculate_pagination(orders, page, limit, total_count)

        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response


def get_top_products_onhold(store, start_date, end_date, sort):
    response = {
        "status" :400
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        query = text (
            f"""SELECT 
                    p.product_id, 
                    p.product_name,
                    SUM(p.price * oc.quantity) as value,
                    (SELECT SUM(p.price * oc.quantity) 
                    FROM order_consignment oc
					JOIN {AnalyticsDB.get_products_trend_table()} p ON oc.product_id = p.product_id
                    WHERE oc.order_type = 'on-hold') AS total_value
                FROM 
                     order_consignment oc
                JOIN 
                    {AnalyticsDB.get_products_trend_table()} p ON oc.product_id = p.product_id
                WHERE 
                    oc.order_type = 'on-hold'
                    AND p.order_date_time BETWEEN :start_date AND :end_date
                GROUP BY
                    p.product_id, p.product_name
                ORDER BY 
                    value DESC
                LIMIT 10;
            """
        )
        results = conn.execute(query.params(start_date=start_date, end_date=end_date))
        data = []
        for row in results.fetchall():
            percentage = round((row[2] / row[3]) * 100, 2)
            value = "{:,.0f}".format(row[2])
            row_data = {
                'product_id': row[0],
                'product_name': row[1],
                'value': value,
                'percentage': percentage
            }
            data.append(row_data)
        
        if sort.lower() == 'asc':
            data = sorted(data, key=lambda x: x['percentage'])
        else:
            data = sorted(data, key=lambda x: x['percentage'], reverse=True)

        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response


def get_top_distributors(store):
    response = {
        "status" :400
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        query = text (
            f"""SELECT 
                soc.customer_id, 
                c.first_name, 
                c.last_name, 
                SUM(
                    CASE 
                        WHEN soc.order_status = 'Unpaid' THEN soc.total_amount
                        WHEN soc.order_status = 'Partial' THEN soc.due_amount
                        ELSE 0
                    END
                ) AS total_outstanding_amount
            FROM 
                salesforce_order_consignment soc
            JOIN 
                customers c ON soc.customer_id = c.customer_id
            WHERE 
                soc.order_status = 'Unpaid' OR soc.order_status = 'Partial'
            GROUP BY 
                soc.customer_id, c.first_name, c.last_name
            HAVING 
                SUM(
                    CASE 
                        WHEN soc.order_status = 'Unpaid' THEN soc.total_amount
                        WHEN soc.order_status = 'Partial' THEN soc.due_amount
                        ELSE 0
                    END
                ) > 0
            ORDER BY 
                total_outstanding_amount DESC
            LIMIT 5;"""
        )
        results = conn.execute(query)
        data = []
        for row in results.fetchall():
            if row[3] >= 1000000:
                value = "{:.1f}M".format(row[3] / 1000000)
            elif row[3] >= 1000:
                value = "{:.1f}k".format(row[3] / 1000)
            else:
                value = "{:,.0f}".format(row[3])
            row_data = {
                'customer_id': row[0],
                'customer_name': f"{row[1]} {row[2]}",
                'value': value,
                'value_amount': round(row[3], 2)
            }
            data.append(row_data)

        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response


def get_consignment_summary(store):
    response = {"status": 400}
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        # Query for total value of warehouse
        # warehouse_query = text(
        #     f"""SELECT SUM(p.price * svc.quantity_available) as total_value_warehouse
        #             FROM skuvault_catalog svc
		# 			JOIN analytics_products_trend p ON svc.parent_SKU = p.parent_SKU
        #             WHERE p.order_date_time BETWEEN :start_date AND :end_date
        #     """
        # )
        consignment_query = text (
            f"""SELECT 
                SUM(
                    CASE 
                        WHEN soc.order_status = 'Unpaid' THEN soc.total_amount
                        WHEN soc.order_status = 'Partial' THEN soc.due_amount
                        ELSE 0
                    END
                ) AS total_outstanding_amount
            FROM 
                salesforce_order_consignment soc
            WHERE 
               soc.order_status = 'Unpaid' OR soc.order_status = 'Partial'
            """
        )
        consignment_result = conn.execute(consignment_query)
        consignment_total = consignment_result.scalar()
        consignment_total = consignment_total if consignment_total else 0

        # Query for total value of on-hold products
        # on_hold_query = text(
        #     f"""SELECT SUM(p.price * oc.quantity) as total_value_on_hold
        #             FROM order_consignment oc
		# 			JOIN analytics_products_trend p ON oc.product_id = p.product_id
        #             WHERE oc.order_type = 'on-hold' AND p.order_date_time BETWEEN :start_date AND :end_date
        #     """
        # )
        block_order_query = text(
            f"""SELECT SUM(bo.total) as total_block_order_value FROM blocked_orders bo"""
        )
        block_order_result = conn.execute(block_order_query)
        block_order_total = block_order_result.scalar()
        block_order_total = block_order_total if block_order_total else 0

        # Query for total value of consignment products
        # consignment_query = text(
        #     f"""SELECT SUM(p.price * oc.quantity) as total_value_consignment
        #             FROM order_consignment oc
		# 			JOIN analytics_products_trend p ON oc.product_id = p.product_id
        #             WHERE oc.order_type = 'consignment' AND p.order_date_time BETWEEN :start_date AND :end_date
        #     """
        # )

        total_valuation = consignment_total + block_order_total 

        if consignment_total >= 1000000:
            consignment_total_value = "{:.1f}M".format(consignment_total / 1000000)
        elif consignment_total >= 1000:
            consignment_total_value = "{:.1f}k".format(consignment_total / 1000)
        else:
            consignment_total_value = "{:,.0f}".format(consignment_total)

        if block_order_total >= 1000000:
            block_order_total_value = "{:.1f}M".format(block_order_total / 1000000)
        elif block_order_total >= 1000:
            block_order_total_value = "{:.1f}k".format(block_order_total / 1000)
        else:
            block_order_total_value = "{:,.0f}".format(block_order_total)

        if total_valuation >= 1000000:
            total_valuation_formatted = "{:.1f}M".format(total_valuation / 1000000)
        elif total_valuation >= 1000:
            total_valuation_formatted = "{:.1f}k".format(total_valuation / 1000)
        else:
            total_valuation_formatted = "{:,.0f}".format(total_valuation)

        data = [
            {
                'consignment_total_value': consignment_total_value,
                'block_order_total_value': block_order_total_value,
                'total_valuation_formatted': total_valuation_formatted,

                'consignment_total': round(consignment_total, 2),
                'block_order_total': round(block_order_total, 2),
                'total_valuation': round(total_valuation, 2)
            }
        ]

        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response

def get_product_info(store, body):
    consignment_sum = order_consignment_db.OrderConsignmentSchema.get_consignment_sum_for_product(store, body['id'])
    onhold_sum = order_consignment_db.OrderConsignmentSchema.get_onhold_sum_for_product(store, body['id'])

    product = store_catalog_db.get_product_by_id(store['id'],body['id'])
    
    parent_sku=product['sku']
    incoming_qty=0
    cost=0
    try:
        with new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled()) as conn:
            query = text(
                f"SELECT SUM(quantity_incoming) AS total_quantity FROM skuvault_catalog WHERE parent_sku = :parent_sku "
                
            )
            user = conn.execute(query.params(parent_sku=parent_sku))
            result = user.fetchone()
            quantity = 0  
            if result and result[0] is not None:
                quantity = result[0]
                incoming_qty=quantity

            query = text(f"SELECT cost FROM skuvault_catalog WHERE parent_sku = :parent_sku LIMIT 1 ")
            user = conn.execute(query.params(parent_sku=parent_sku))
            result = user.fetchone()
            quantity = None  
            if result:
                quantity = result[0]
                cost=quantity
    except Exception as e:
        # Log the error (if applicable)
        raise  # Ensure the error is re-raised to handle it properly
    
    inventory_level_sum=0
    product_json = new_utils.parse_json(product)
    variants=product_json['variants']
    for variant in variants:
        inventory_level_sum+=variant['inventory_level']

    consignment_sum_cost=consignment_sum*cost
    onhold_sum_cost=onhold_sum*cost
    totalStock=consignment_sum+onhold_sum+incoming_qty+inventory_level_sum
    totalValuation=consignment_sum_cost+onhold_sum_cost+(inventory_level_sum*cost)
    incoming_cost=incoming_qty*cost
    total_orders=order_consignment_db.OrderConsignmentSchema.count_unique_orders(store, body['id'])
    total_vendors=order_consignment_db.OrderConsignmentSchema.count_unique_customers(store, body['id'])
    records={}
    records['consignment_sum']=consignment_sum
    records['onhold_sum']=onhold_sum
    records['incoming_qty']=incoming_qty
    records['inventory_level_sum']=inventory_level_sum
    records['total_stock']=totalStock
    records['total_consignment_cost']=consignment_sum_cost
    records['total_consignment_cost_display']=new_utils.format_price(consignment_sum_cost)
    records['consignment_vendors']=total_vendors
    records['total_onhold_cost']=onhold_sum_cost
    records['total_onhold_cost_display']=new_utils.format_price(onhold_sum_cost)
    records['onhold_orders']=total_orders
    records['total_incoming_cost']=incoming_cost
    records['total_incoming_cost_display']=new_utils.format_price(incoming_cost)
    records['total_valuation']=totalValuation
    records['total_valuation_display']=new_utils.format_price(totalValuation)

    return records