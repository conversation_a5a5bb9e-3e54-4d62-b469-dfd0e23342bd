from datetime import datetime, timezone
import logging
import traceback
from new_mongodb import get_admin_db_client_for_store_id, fetchall_documents_from_admin_collection, process_documents, update_document_in_admin_collection
from utils.common import get_paginated_records_updated, processList
from new_mongodb import StoreAdminDBCollections
from bson import ObjectId
import new_pgdb
from sqlalchemy import text
from collections import defaultdict
import pytz


logger = logging.getLogger()

def get_notifications(store_id, page, limit, username, notification_filter):
    response = {
        "status": 400,
    }
    try:
        if page and limit:
            page = int(page)
            limit = int(limit)
        else:
            page = 1
            limit = 10

        db = get_admin_db_client_for_store_id(store_id)

        # Split notification_filter into a list of event types
        module_names = [event.strip() for event in notification_filter.split(',') if event.strip()] if notification_filter else []

        # Build the query to match notifications
        match_query = {
            "receivers.email": username,  # Match records where the username exists in receivers
            "receivers": {"$exists": True, "$ne": []}  # Exclude records with empty receivers
        }
        if module_names:
            match_query["module"] = {"$in": module_names}

        # Define the aggregation pipeline
        pipeline = [
            # Step 1: Match notifications for the given user and filter conditions
            {"$match": match_query},
            # Step 2: Add an `is_read` field by matching the current user in the receivers array
            {
                "$addFields": {
                    "is_read": {
                        "$let": {
                            "vars": {
                                "matchedReceiver": {
                                    "$arrayElemAt": [
                                        {"$filter": {
                                            "input": "$receivers",
                                            "as": "receiver",
                                            "cond": {"$eq": ["$$receiver.email", username]}
                                        }},
                                        0  # Get the first matching receiver
                                    ]
                                }
                            },
                            "in": {"$ifNull": ["$$matchedReceiver.is_read", None]}  # Safely extract `is_read`
                        }
                    }
                }
            },
            # Step 3: Sort notifications to prioritize unread first, then by creation date
            {
                "$sort": {
                    "is_read": 1,  # Unread (false) notifications come first
                    "created_at": -1  # Latest notifications within each group
                }
            },
            # Step 4: Pagination - Skip and limit
            {"$skip": (page - 1) * limit},  # Skip records for the current page
            {"$limit": limit}  # Limit the number of records per page
        ]


        # Execute the pipeline
        notifications = list(db[StoreAdminDBCollections.NOTIFICATIONS_ADMIN].aggregate(pipeline))

        notifications = processList(notifications)

        # Get the total count of notifications matching the query (without pagination)
        total_count = db[StoreAdminDBCollections.NOTIFICATIONS_ADMIN].count_documents(match_query)

        # Get the unread notification count for the user
        unread_count_query = {}
        unread_count_query["receivers"] = {
            "$elemMatch": {  # Match at least one element in the array
                "email": username,
                "is_read": False
            }
        }
        unread_count = db[StoreAdminDBCollections.NOTIFICATIONS_ADMIN].count_documents(unread_count_query)


        if notifications:
            final_result = {
                "data": notifications,
                "meta": {
                    "current_page": page,
                    "next_page": (page + 1 if page and limit and (page * limit) < total_count else None),
                    "total_count": total_count,
                    "unread_count": unread_count
                }
            }
            response['data'] = final_result
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = {
                "data": [],
                "meta": {
                    "current_page": page,
                    "next_page": None,
                    "total_count": total_count,
                    "unread_count": unread_count
                }
            }
    except Exception as e:
        logger.error(traceback.format_exc())

    return response


def update_notification(store_id, notification_id, username, is_read):
    db = get_admin_db_client_for_store_id(store_id)
    try:

        # Current timestamp for read_time if is_read is True
        current_time = int(datetime.now(timezone.utc).timestamp())

        # Define the query and update data
        query = {"_id": ObjectId(notification_id)}
        update_data = {
            "$set": {
                "receivers.$[elem].is_read": is_read,
                "receivers.$[elem].read_time": current_time if is_read else None
            }
        }

        # Array filter to target the specific username
        array_filters = [{"elem.email": username}]

        result = db[StoreAdminDBCollections.NOTIFICATIONS_ADMIN].update_one(
            query,
            update_data,
            array_filters=array_filters  # Explicitly pass array filters here
        )

        return {"status": 200, "message": "Notification updated successfully"}
        
    except Exception as e:
        logger.error(f"Error occurred: {str(e)}")
        return {"status": 500, "message": f"An error occurred: {str(e)}"}


def get_notification_modules(store_id):
    # Fetch all documents from the notification modules collection
    modules = fetchall_documents_from_admin_collection(store_id, StoreAdminDBCollections.NOTIFICATION_MODULES, {})
    
    # Initialize a dictionary to organize data by module
    module_data = {}
    
    for module in modules:
        module_name = module.get("module")
        event_type = module.get("event_type")
        
        # Ensure the module_name exists in the dictionary
        if module_name not in module_data:
            module_data[module_name] = {
                "module_name": module_name,
                "events": []
            }
        
        # Add the event_type to the module's events list if it's not already there
        if event_type not in module_data[module_name]["events"]:
            module_data[module_name]["events"].append(event_type)
    
    # Convert the dictionary values to a list
    result = list(module_data.values())
    return result


def update_po_status_in_notifications(store_id, po_id):
    response = {
        "status" :400
    }
    conn = new_pgdb.get_connection(store_id)
    db = get_admin_db_client_for_store_id(store_id)
    try:
        query = text(
            f"""SELECT status FROM bo_purchase_orders WHERE po_id = :po_id
            """
        )
        result = conn.execute(query, {"po_id": po_id}).fetchone()
        if result:
            po_status = result[0]
            po_status = po_status.lower()
            if po_status == "partially fulfilled":
                po_status = "pending"

            # Update the status in the notifications_admin collection
            update_result = db["notifications_admin"].update_many(
                {"po_id": int(po_id)},
                {"$set": {"status": po_status}}
            )
            if update_result.modified_count > 0:
                response["status"] = 200  # Successfully updated
            else:
                response["status"] = 404  # Document not found
    finally:
        conn.close()
    return response

def get_notifications_group_by_date(store_id, username, page, limit, notification_filter, start_date, end_date, timezone="UTC"):
    response = {"status": 400}
    db = get_admin_db_client_for_store_id(store_id)
    try:
        # Validate and set timezone
        try:
            user_timezone = pytz.timezone(timezone)
        except pytz.UnknownTimeZoneError:
            user_timezone = pytz.UTC  # Default to UTC

        # Set default pagination values if not provided
        page = int(page) if page else 1
        limit = int(limit) if limit else 10

        module_names = [event.strip() for event in notification_filter.split(',') if event.strip()] if notification_filter else []

        match_query = {
            "receivers.email": username,  # Match records where the username exists in receivers
            "receivers": {"$exists": True, "$ne": []}  # Exclude records with empty receivers
        }
        if module_names:
            match_query["module"] = {"$in": module_names}

        # Apply date range filter if provided
        date_filters = {}
        if start_date:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            start_dt = user_timezone.localize(start_dt.replace(hour=0, minute=0, second=0))  # Local timezone
            start_timestamp = int(start_dt.astimezone(pytz.utc).timestamp())  # Convert to UTC timestamp
            date_filters["$gte"] = start_timestamp

        if end_date:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
            end_dt = user_timezone.localize(end_dt.replace(hour=23, minute=59, second=59))  # Local timezone
            end_timestamp = int(end_dt.astimezone(pytz.utc).timestamp())  # Convert to UTC timestamp
            date_filters["$lte"] = end_timestamp

        if date_filters:
            match_query["created_at"] = date_filters

        total_counts = db[StoreAdminDBCollections.NOTIFICATIONS_ADMIN].count_documents(match_query)

        # Fetch notifications from DB with main pagination
        skip = (page - 1) * limit
        notifications = list(db[StoreAdminDBCollections.NOTIFICATIONS_ADMIN].find(match_query).sort("created_at", -1).skip(skip).limit(limit))
        
        # Group notifications by date
        grouped_notifications = defaultdict(list)

        for notification in notifications:
            created_at = notification.get("created_at")
            if not created_at:
                continue
            
            # created_at_dt = datetime.utcfromtimestamp(created_at).replace(tzinfo=pytz.utc)
            created_at_dt = datetime.fromtimestamp(created_at, tz=pytz.utc)

            created_at_local = created_at_dt.astimezone(user_timezone)

            date_str = created_at_local.strftime("%b %d, %Y")
            
            formatted_notification = {key: value for key, value in notification.items() if key != "_id"}
            formatted_notification["id"] = str(notification.get("_id"))

            user_receiver = next((receiver for receiver in notification.get("receivers", []) if receiver.get("email") == username), None)
            formatted_notification["is_read"] = user_receiver.get("is_read", False) if user_receiver else False
            
            grouped_notifications[date_str].append(formatted_notification)
        
        sorted_notifications = dict(sorted(grouped_notifications.items(), key=lambda x: datetime.strptime(x[0], "%b %d, %Y"), reverse=True))

        if notifications:
            final_result = {
                "data": sorted_notifications,
                "meta": {
                    "current_page": page,
                    "next_page": (page + 1 if page and limit and (page * limit) < total_counts else None),
                    "total_count": total_counts
                }
            }
            response['data'] = final_result
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = {
                "data": [],
                "meta": {
                    "current_page": page,
                    "next_page": None,
                    "total_count": total_counts
                }
            }

    except Exception as e:
        logger.error(traceback.format_exc())
    
    return response


def get_email_notification_sections(store_id, username):
    response = {"status": 400}
    try:
        data = fetchall_documents_from_admin_collection(store_id, StoreAdminDBCollections.EMAIL_NOTIFICATION_MASTER, {})
        data = process_documents(data)

        # Process each document as per your requirements
        for doc in data:
            if 'sections' in doc:
                for section in doc['sections']:
                    if 'notifications' in section:
                        for notification in section['notifications']:
                            enabled_users = notification.get('enabled_users', [])
                            if username in enabled_users:
                                notification['checked'] = True
                            else:
                                notification['checked'] = False
                            # Remove the discontinued_users key
                            if 'enabled_users' in notification:
                                del notification['enabled_users']

        response['data'] = data
        response['status'] = 200

    except Exception as e:
        logger.error(traceback.format_exc())
    
    return response

def update_permission_for_email_notification(store_id, username, payload):
    response = {"status": 400}
    results = []
    try:
        data = payload.get('notifications', [])
        # Expecting data to be a list of notification update objects
        if not isinstance(data, list):
            response['message'] = "Data should be a list of notification objects"
            return response

        db = get_admin_db_client_for_store_id(store_id)
        for item in data:
            module_name = item.get('module_name', '')
            notification_type = item.get('notification_type', '')
            notification_name = item.get('notification_name', '')
            checked = item.get('checked', False)

            # Find the document for the given module_name
            doc = db[StoreAdminDBCollections.EMAIL_NOTIFICATION_MASTER].find_one({"module_name": module_name})
            if not doc:
                results.append({
                    'module_name': module_name,
                    'notification_type': notification_type,
                    'notification_name': notification_name,
                    'status': 404,
                    'message': "Module not found"
                })
                continue

            updated = False
            # Traverse sections and notifications to find the correct notification
            for section in doc.get('sections', []):
                if section.get('notification_type') == notification_type:
                    for notification in section.get('notifications', []):
                        if notification.get('name') == notification_name:
                            enabled_users = notification.get('enabled_users', [])
                            if checked:
                                if username not in enabled_users:
                                    enabled_users.append(username)
                            else:
                                if username in enabled_users:
                                    enabled_users.remove(username)
                            notification['enabled_users'] = enabled_users
                            updated = True
                            break  # Found the notification, no need to continue
                if updated:
                    break

            if updated:
                # Update the document in the DB
                db[StoreAdminDBCollections.EMAIL_NOTIFICATION_MASTER].update_one(
                    {"_id": doc["_id"]},
                    {"$set": {"sections": doc["sections"]}}
                )
                results.append({
                    'module_name': module_name,
                    'notification_type': notification_type,
                    'notification_name': notification_name,
                    'status': 200,
                    'message': "Permission updated successfully"
                })
            else:
                results.append({
                    'module_name': module_name,
                    'notification_type': notification_type,
                    'notification_name': notification_name,
                    'status': 404,
                    'message': "Notification not found"
                })

        # Count successes and failures
        success_count = sum(1 for r in results if r['status'] == 200)
        failure_count = len(results) - success_count
        total = len(results)
        if success_count == total:
            summary_message = "All notifications updated successfully."
        elif success_count == 0:
            summary_message = "No notifications were updated."
        else:
            summary_message = f"{success_count} notifications were updated successfully, {failure_count} failed."

        response['status'] = 200
        response['message'] = summary_message

    except Exception as e:
        logger.error(traceback.format_exc())
        response['message'] = str(e)
    
    return response