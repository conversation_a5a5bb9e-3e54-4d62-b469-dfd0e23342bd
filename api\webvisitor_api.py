from flask import request
import logging
import traceback
from api import APIResource
from analytics import web_visitors
from new_mongodb import store_admin_db
from products.all_products import products_list


logger = logging.getLogger()


class WebVisitors(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Web Visitors GET")
        try:
            query_params = request.args.to_dict()
            page = query_params.get('page')
            limit = query_params.get('limit')
            search = query_params.get('search', '')
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            res = web_visitors.get_web_visitors(store['id'], page, limit, search, sort_array)
            if res['status'] == 200:
                return res['data'], 200
        finally:
            logger.debug("Exiting Web Visitors GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)