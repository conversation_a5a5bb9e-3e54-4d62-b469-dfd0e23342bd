from services import Service
from bson import ObjectId
from utils import redis_util
import mongo_db


class Tenant(Service):

    def get_all_tenants(self):
        tenants = super().find_all_active()
        return super().processList(tenants)


class SyncStore(Service):
    pass


class TenantStore(Service):

    def __init__(self, repository):
        super().__init__(repository)
        self.stores = {}
        for store in self.find_all_active():
            self.stores[store["store_url"]] = store

    def create(self, tenant_id, dto):
        dto["tenant_id"] = str(tenant_id)
        id = super().create(dto)
        return id

    def get_store_by_id(self, store_id):
        store = super().find_one(
            {"_id": ObjectId(str(store_id)), "status": "active"})
        return store

    def get_store_by_url(self, storeUrl):
        if storeUrl in self.stores:
            return self.stores[storeUrl]
        else:
            store = super().find_one(
                {"store_url": storeUrl, "status": "active"})
            return store

    def update_store(self, tenant_id, store_id, data):
        store = self.get_store_by_id(tenant_id, store_id)
        if store:
            for key, value in data.items():
                store[key] = value
            super().update_one({"_id": ObjectId(str(store_id))}, store)

    def delete_store(self, store):
        if store:
            mongo_db.drop_db(store["db"])
            super().delete_by_id(str(store["id"]))

    def delete_by_id(self, store_id):
        store = super().find_by_id(store_id)
        if store:
            self.delete_store(store)
            return True
        return False

    def delete_tanant_store(self, tenant_id):
        stores = self.get_tanant_stores(tenant_id)
        for store in stores:
            self.delete_store(store)
        return True

    def get_tanant_stores(self, tenant_id):
        return super().find({'tenant_id': str(tenant_id)})

    def update_store_navigationIds(self, store_id, data):
        id = super().update_one({"_id": ObjectId(str(store_id))}, {
            "$set": {
                'navigation_ids': data
            }
        })
        return id

    def update_mail_templates(self, store_id, store, data):
        mail_templates = store['mail_templates']
        if data['template_for'] in mail_templates:
            key_name = data['template_for']
            key_name = key_name.replace(" ", "_").lower()
            data['short_code'] = key_name
            data.pop('template_for')
            mail_templates[key_name] = data
        else:
            key_name = data['template_for']
            key_name = key_name.replace(" ", "_").lower()
            data['short_code'] = key_name
            data.pop('template_for')
            mail_templates[key_name] = data

        id = super().update_one({"_id": ObjectId(str(store_id))}, {
            "$set": {
                'mail_templates': mail_templates
            }
        })
        return id

    def delete_mail_template(self, store, store_id, template_code):
        response = {
            "status": 400,
            "message": ''
        }
        mail_templates = store['mail_templates']
        if template_code in mail_templates:
            mail_templates.pop(template_code)
            id = super().update_one({"_id": ObjectId(str(store_id))}, {
                "$set": {
                    'mail_templates': mail_templates
                }
            })
            response['status'] = 200
            response['message'] = "Template deleted sucessfully!"
        else:
            response['status'] = 404
            response['message'] = "Template not found"
        return response
