from flask_restful import Api, Resource, request
import config
import traceback
import logging
from utils import redis_util, webhook_util
from api import APIResource

logger = logging.getLogger()


class BCWebhook(Resource):

    def __init__(self, service):
        self.service = service

    def post(self):
        token = request.headers.get("Authorization", None)
        if token:
            req_payload = request.get_json(force=True)
            ret = webhook_util.process_webhook(req_payload, token)
            if not ret:
                return {"message": "Unauthorized"}, 401
        else:
            logger.error("Unauthorized request received.")
            return {"message": "Unauthorized"}, 401

        return {"message": "accepted"}, 200
    

class WebhookStatus(Resource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Webhook GET")
        try:
            res = redis_util.get_webhook_status()
            if res:
                return res, 200
            else:
                return {"message": "Webhook status not found"}, 404
        finally:
            logger.debug("Exiting Webhook GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)

class BCWebhooks(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Webhooks GET")
        try:
            res = webhook_util.get_webhook_listing(store)
            return {"data": res}, 200
        finally:
            logger.debug("Exiting Webhooks GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class BCWebhooksRegister(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Webhooks register GET")
        try:
            res = webhook_util.get_webhooks(store['id'])
            if res:
                return {"data": res}, 200
            else:
                return {"message": "Webhooks not found"}, 404
        finally:
            logger.debug("Exiting Webhooks register GET")
    
    def put_executor(self, request, token_payload, store):
        logger.debug("Entering Webhooks register PUT")
        try:
            webhooks = request.get_json(force=True)
            if webhooks:
                # webhooks = data.get("webhooks", [])
                res = webhook_util.add_webhooks(store['id'], webhooks)
                return res, 200
            return "Invalid request", 409
        finally:
            logger.debug("Exiting Webhook PUT")
    
    def put(self):
        return self.execute_store_request(request, self.put_executor)

    def get(self):
        return self.execute_store_request(request, self.get_executor)