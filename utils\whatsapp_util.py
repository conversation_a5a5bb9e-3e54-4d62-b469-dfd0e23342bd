import logging
import traceback

from flask_socketio import emit
import pg_db as db
from pg_db import tag_db
from pg_db import whatsapp_db
from utils.common import calculatePaginationData, paginate_data
import os
from datetime import datetime
from werkzeug.utils import secure_filename
from flask import send_file, make_response
from socketio_setup import socketio
logger = logging.getLogger()

ALLOWED_EXTENSIONS = set()

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower()


def change_file_name(filename):
    rendom = str(round(datetime.utcnow().timestamp()))
    if ('.' in filename):
        ext = filename.rsplit('.', 1)[1].lower()
        fname = rendom + '.' + ext
        return fname


def receive_message(payload, session=None):
    socketio.emit('ws', {'data': 'new message received on server...'})
    entry=payload['entry'] 
    lists=entry[0]
    value= lists['changes'][0]['value']
    phone_number=value['metadata']['display_phone_number']
    messages=value['messages'][0]
    message=messages['text']['body']
    wa_id=value['contacts'][0]['wa_id']
    profile=value['contacts'][0]['profile']
    name=profile['name']
    message_modal={
        'sender_user_id':None,
        'vender_user_id':wa_id,
        'type':messages['type'],
        'parent_chat_id':None,
        'root_chat_id':None,
        'message':message,
        'is_archived':False,
        'is_deleted':False,
        "created_at":None,
        "is_search_found":False,
        "parent_message":None
     }
    chat_user_modal={
         'wa_id':wa_id,
         'name':name,
         'mobile':phone_number,
         }
    
    whatsapp_user_id = wa_id
     # socketio.emit('server_message', {'data': message_modal})
    vendor_exists = whatsapp_db.ChatUserSchema.get_user(whatsapp_user_id,session)
    if vendor_exists is not None:       
         whatsapp_db.MessageSchema.add_chat_messages(message_modal)
    else:
         whatsapp_db.ChatUserSchema.add_user(chat_user_modal)
         whatsapp_db.ChatUserMappingSchema.add_chat_user_mapping(chat_user_modal)
         whatsapp_db.MessageSchema.add_chat_messages(message_modal)
    whatsapp_db.ChatUserMappingSchema.add_received_messages_count(message_modal['vender_user_id'])
    whatsapp_db.ChatUserMappingSchema.add_chat_user_mapping_message_time(message_modal)
    total_count=whatsapp_db.ChatUserMappingSchema.send_received_messages_count(message_modal['vender_user_id'])
    message_modal_socket={
        'sender_user_id':None,
        'vender_user_id':wa_id,
        'message_type':messages['type'],
        'parent_chat_id':None,
        'root_chat_id':None,
        'message':message,
        'is_archived':False,
        'is_deleted':False,
        "created_at":int(datetime.utcnow().timestamp()),
        "is_search_found":False,
        "parent_message":None,
        "received_message_count":total_count,
        "isNedded":'true'
     }
    socketio.emit('server_message', {'data': message_modal_socket})
    # socketio.emit('last_message',{'data':message})
  
def send_message(payload, session=None):
    sender_id=int(payload['sender_user_id'],16)
    parent_chat_id=None
    parentHasParent=whatsapp_db.MessageSchema.has_parent(payload['parent_chat_id'])
    if payload['parent_chat_id']  is not None:
       parent_chat_id=payload['parent_chat_id']
    
    message_modal={
        'sender_user_id':sender_id,
        'vender_user_id':payload['vender_user_id'],
        'type':payload['type'],
        'message':payload['message']['body'],
        'parent_chat_id':parent_chat_id,
        'root_chat_id':parentHasParent,
        'is_archived':payload['is_archived'],
        'is_deleted':payload['is_deleted'],
        'parent_message':payload['parent_message']
    }
    message_modal_for_socket={
        'sender_user_id':payload['sender_user_id'],
        'vender_user_id':payload['vender_user_id'],
        'type':payload['type'],
        'message':payload['message']['body'],
        'parent_chat_id':parent_chat_id,
        'root_chat_id':parentHasParent,
        'is_archived':payload['is_archived'],
        'is_deleted':payload['is_deleted'],
        "created_at":int(datetime.utcnow().timestamp()) ,
        "is_search_found":False,
        "parent_message":None,
        "received_message_count":0,
        "isNedded":'true'
    }
    
    vendor_exists = whatsapp_db.ChatUserSchema.get_user(payload['vender_user_id'],session)
    if vendor_exists is not None:        
         whatsapp_db.MessageSchema.add_chat_messages(message_modal)
    whatsapp_db.ChatUserMappingSchema.add_chat_user_mapping_message_time(message_modal)
    logger.debug(f"{message_modal_for_socket} here in send message before socket")
    try:
       logger.debug("here in socket")
       socketio.emit('server_message', {'data': message_modal_for_socket})
       logger.debug('done socket send')
    except Exception as e:
       logger.error(traceback.format_exc())
    logger.debug("here in after socket")

def get_all_messages(payload, session=None):
    vender_id=payload['vender_user_id']
    message_modal_socket={
        'vender_user_id':int(vender_id),
        'isNedded':'false'
 }
    
    socketio.emit('server_message',{'data':message_modal_socket})
    whatsapp_db.ChatUserMappingSchema.set_received_messages_count_zero(vender_id)
    message_list = whatsapp_db.MessageSchema.get_message_list(payload,vender_id,session)
    if message_list is not None: 
         total_count=whatsapp_db.MessageSchema.get_messages_count(vender_id) 
         paginated_rows, current_page, total_pages, total_items = paginate_data(
                total_count, message_list, int(payload['page']), int(payload['limit']))  
         
         data = calculatePaginationData(
                paginated_rows, current_page, payload['limit'], total_items)    
         return data
        
    else:
          return {}
    
def get_message_detail(payload, session=None):
    if payload['id']:
       message_id=payload['id']
       message_list = whatsapp_db.MessageSchema.get_message_detail(payload,message_id,session)
       if message_list is not None:   
          return message_list
       else:
          return {}


def setImage(uploaded_file, type, vendor_id, rep_id):     
     response = {
          "status": 400
     }     
     folder_name = str(vendor_id) + '_' + str(rep_id)
     base_folder_path = 'images/whatsapp/'
     folder_path = os.path.join(base_folder_path, folder_name)
     if not os.path.exists(folder_path):
          os.makedirs(folder_path)

     file = uploaded_file['document']
     sub_folder_path = ''
     UPLOAD_FOLDER = folder_path
    
     if type == 'image':
          sub_folder = 'images'
          sub_folder_path = os.path.join(folder_path, sub_folder)                               
     elif type == 'video':
          sub_folder = 'videos'
          sub_folder_path = os.path.join(folder_path, sub_folder)          
     elif type == 'document':
          sub_folder = 'documents'
          sub_folder_path = os.path.join(folder_path, sub_folder)          
     else:
          response['message'] = "Inappropriate document type."
          response['status'] = 500 
          return response 
     
     if not os.path.exists(sub_folder_path):
               os.makedirs(sub_folder_path)
     UPLOAD_FOLDER = sub_folder_path   

     if (file.filename == ''):
          response['message'] = "No document selected for uploading"
          response['status'] = 500
          return response
    
     if (file and allowed_file(file.filename)):
          newName = change_file_name(file.filename)
          fname = secure_filename(newName)
          file.save(os.path.join(UPLOAD_FOLDER, fname))
          base_path = os.path.join(os.path.abspath(
               os.getcwd()), UPLOAD_FOLDER, newName)
                    
          if '/app/images/whatsapp' in base_path:
               base_path = base_path.replace('/app/images/whatsapp', '')  
          data = {
               'path': base_path,
               'type': type
          }
          response['message'] = data
          response['status'] = 200
     else:
          response['message'] = "Something went wrong "
          response['status'] = 500

     return response


def getImage(body):
     if body['file_path'] != '':
          file_path = '/app/images/whatsapp'
          file_path = file_path + body['file_path'] 
          if os.path.exists(file_path):         
               return send_file(file_path, as_attachment=True)   
          else:      
               return make_response({'error': 'Document not found'}, 404)
     else:
          return make_response({'error': 'Document not found'}, 404)      
         

def assign_representative(payload, session=None):
     if(payload):
          whatsapp_db.ChatUserMappingSchema.get_chat_user_mapping_and_update(payload)

def get_vendor_and_representative(payload):
          result=whatsapp_db.ChatUserMappingSchema.get_all_chat_user_mapping(payload)
          limit = int(payload.get("limit", 10))
          page = int(payload.get("page", 1))
          if result is not None: 
             total_count=whatsapp_db.ChatUserMappingSchema.get_chat_user_count()
             paginated_rows, current_page, total_pages, total_items = paginate_data(
                total_count, result, page, limit)  
         
             data = calculatePaginationData(
                paginated_rows, current_page, limit, total_items)    
             return data
          else:
             return {}

def get_vendor_from_id(payload):
          vender_id=payload['id']
          result=whatsapp_db.ChatUserSchema.get_vender_by_id(vender_id)
          return result

