from flask import request
from api import APIResource
from services import products_instock_notify_service
import logging

logger = logging.getLogger()    

class ProductsInstockNotify(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering ProductsInstockNotify GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            search = query_params.get('search', '').strip()
            
            res = products_instock_notify_service.get_products_instock_notify(store, page, limit, sort_array, search)
            
            if res['status'] == 200:
                return {'data': res['data'], 'meta': res['meta']}, res['status']
            else:
                return {'message': res['message']}, res['status']
            
        except Exception as e:
            logger.error(f"Error in ProductsInstockNotify GET: {str(e)}")
            return {'message': 'Internal server error'}, 500
        finally:
            logger.debug("Exiting ProductsInstockNotify GET")   

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ProductsInstockNotifyDetails(APIResource):
    def get_executor(self, request, token_payload, store, product_id):
        logger.debug(f"Entering ProductsInstockNotifyDetails GET for product_id: {product_id}")
        try:
            if not product_id:
                return {"message": "Please enter product_id as a path parameter"}, 400
            
            query_params = request.args.to_dict()
            variant_id = query_params.get('variant_id', None)
            
            res = products_instock_notify_service.get_products_instock_notify_details(store, product_id, variant_id)
            
            if res['status'] == 200:
                return {'data': res['data']}, res['status']
            else:
                return {'message': res['message']}, res['status']
            
        except Exception as e:
            logger.error(f"Error in ProductsInstockNotifyDetails GET: {str(e)}")
            return {'message': 'Internal server error'}, 500
        finally:
            logger.debug(f"Exiting ProductsInstockNotifyDetails GET for product_id: {product_id}")

    def get(self, product_id):
        return self.execute_store_request(request, self.get_executor, product_id)
