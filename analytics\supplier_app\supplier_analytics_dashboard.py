from sqlalchemy import text
import new_pgdb
import logging
import traceback
from new_pgdb.analytics_db import AnalyticsDB

logger = logging.getLogger()

def get_supplier_summary(store, start_date, end_date, brands, user):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        base_query = f"""
                    SELECT
                        COUNT(DISTINCT pt.product_id) AS product_count,
                        SUM(pt.quantity) AS quantity_sold,
                        COUNT(DISTINCT sh.state) AS total_state
                    FROM
                        {AnalyticsDB.get_products_trend_table()} pt
                    JOIN 
                        order_shipping_addresses sh ON pt.order_id = sh.order_id
                    WHERE
                        pt.order_date_time BETWEEN :start_date AND :end_date"""
        
        # Apply supplier filter if provided
        params = {"start_date": start_date, "end_date": end_date}

        if user:
            base_query += f" AND pt.product_id IN (select DISTINCT product_id from {new_pgdb.DBTables.supplier_app_user_supplier_mapping} where email_id = :user)"
            # base_query += " AND pt.supplier_email = :user"
            params["user"] = user.strip()

        if brands:
            base_query += f" AND pt.product_id IN (select DISTINCT product_id from {new_pgdb.DBTables.supplier_app_user_supplier_mapping} where brand_id IN :brands)"
            params["brands"] = tuple(brands.split(','))

        query = text(base_query)

        results = conn.execute(query, params)

        data = []
        for row in results.fetchall():
            product_count = "{:,.0f}".format(row[0])
            quantity_sold = 0
            if row[1]:
                if row[1] >= 1000000:
                    quantity_sold = "{:.1f}M".format(row[1] / 1000000)
                elif row[1] >= 1000:
                    quantity_sold = "{:.1f}k".format(row[1] / 1000)
                else:
                    quantity_sold = "{:,.1f}".format(row[1])

            row_data = {
                'product_count': product_count,
                'quantity_sold': quantity_sold,
                'total_state': row[2]
            }
            data.append(row_data)

        response['status'] = 200
        response['data'] = data if data else []
    finally:
        if conn:
            conn.close()
    return response

def get_supplier_top_products(store, start_date, end_date, brands, user):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        base_query = f"""
            SELECT 
                pt.product_id, 
                pt.product_name, 
                SUM(pt.total) AS revenue, 
                SUM(pt.quantity) AS quantity,
                COUNT(pt.order_id) AS order_count, 
                AVG(pt.price) AS price
            FROM 
                {AnalyticsDB.get_products_trend_table()} pt
            WHERE 
                pt.order_date_time BETWEEN :start_date AND :end_date
        """

        params = {"start_date": start_date, "end_date": end_date}

        if user:
            base_query += f" AND pt.product_id IN (select DISTINCT product_id from {new_pgdb.DBTables.supplier_app_user_supplier_mapping} where email_id = :user)"
            # base_query += " AND pt.supplier_email = :user"
            params["user"] = user.strip()

        if brands:
            base_query += f" AND pt.product_id IN (select DISTINCT product_id from {new_pgdb.DBTables.supplier_app_user_supplier_mapping} where brand_id IN :brands)"
            params["brands"] = tuple(brands.split(','))

        base_query += """
            GROUP BY 
                pt.product_id, 
                pt.product_name
            ORDER BY 
                revenue DESC
            LIMIT 10
        """

        query = text(base_query)
        results = conn.execute(query, params)

        data = []
        for row in results.fetchall():
            revenue = "{:,.0f}".format(row[2])
            quantity_sold = "{:,.0f}".format(row[3])
            row_data = {
                'product_id': row[0],
                'product_name': row[1],
                'revenue': revenue,
                'quantity_sold': quantity_sold,
                'order_count': row[4]
            }
            data.append(row_data)

        response['status'] = 200
        response['data'] = data if data else []

    finally:
        if conn:
            conn.close()
    return response

def get_supplier_top_states(store, start_date, end_date, brands, user):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        base_query = f"""
            SELECT
                sh.state,
                SUM(pt.quantity) AS quantity,
                COUNT(DISTINCT pt.order_id) AS order_count
            FROM
                {AnalyticsDB.get_products_trend_table()} pt
            JOIN 
                order_shipping_addresses sh ON pt.order_id = sh.order_id
            WHERE
                pt.order_date_time BETWEEN :start_date AND :end_date
                AND sh.state != ''
        """

        params = {"start_date": start_date, "end_date": end_date}

        if user:
            base_query += f" AND pt.product_id IN (select DISTINCT product_id from {new_pgdb.DBTables.supplier_app_user_supplier_mapping} where email_id = :user)"
            # base_query += " AND pt.supplier_email = :user"
            params["user"] = user.strip()

        if brands:
            base_query += f" AND pt.product_id IN (select DISTINCT product_id from {new_pgdb.DBTables.supplier_app_user_supplier_mapping} where brand_id IN :brands)"
            params["brands"] = tuple(brands.split(','))

        base_query += """
            GROUP BY
                sh.state
            ORDER BY
                quantity DESC
            LIMIT 10
        """

        query = text(base_query)
        results = conn.execute(query, params)

        data = []
        for row in results.fetchall():
            quantity = "{:,.0f}".format(row[1])
            row_data = {
                'state': row[0],
                'quantity': quantity,
                'order_count': row[2],
            }
            data.append(row_data)

        response['status'] = 200
        response['data'] = data if data else []

    finally:
        if conn:
            conn.close()
    return response