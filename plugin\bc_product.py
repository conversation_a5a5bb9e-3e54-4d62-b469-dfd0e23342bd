from utils import bc, store_util
def process_images(images):
    result = []

    if len(images['edges']) == 0:
      return result
    else:
      for i in images['edges']:
        obj = {}
        obj['url_zoom'] = i['node']['url']
        obj['url_standard'] = i['node']['urlOriginal']
        obj['url_thumbnail'] = i['node']['urlOriginal']
        obj['is_default'] = i['node']['isDefault']
        obj['alt_text'] = i['node']['altText']
        result.append(obj)

    return result

def process_custom_fields(fields):
    result = []

    if len(fields['edges']) == 0:
      return result
    else:
      for i in fields['edges']:
        obj = {}
        obj['name'] = i['node']['name']
        obj['value'] = i['node']['value']

        result.append(obj)

    return result

def process_reviews(reviews):
    result = []

    if len(reviews['edges']) == 0:
      return result
    else:
      for i in reviews['edges']:
        obj = {}
        obj['id'] = i['node']['entityId']
        obj['rating'] = i['node']['rating']
        obj['text'] = i['node']['text']
        obj['title'] = i['node']['title']
        obj['date_created'] = i['node']['createdAt']['utc']
        obj['author'] = i['node']['author']['name']

        result.append(obj)

    return result

def process_related_products(related_products):
    result = []

    if len(related_products['edges']) == 0:
      return result
    else:
      for i in related_products['edges']:
        obj = {}
        obj['id'] = i['node']['entityId']
        obj['name'] = i['node']['name']
        obj['custom_url'] = {
        "url": i['node']['path']
      } 
        obj['brand_name'] = i['node']['brand']['name'] if i['node']['brand'] else ''
        obj['image'] = i['node']['defaultImage']['url'] if i['node']['defaultImage'] else ''
        obj['price'] = i['node']['prices']['price']['value'] or 0
        obj['retail_price'] = i['node']['prices']['retailPrice']['value'] if i['node']['prices']['retailPrice'] else 0
        obj['sale_price'] = i['node']['prices']['salePrice']['value'] if i['node']['prices']['salePrice'] else 0
        result.append(obj)

    return result

def process_single_product(product):
    result = {
      "data": {}
    }
    item = product['data']['site']['product']

    if item == None:
      return result
    else:
      result['data']['id'] = item['entityId']
      result['data']['sku'] = item['sku'] or ''
      result['data']['name'] = item['name']
      result['data']['price'] = item['prices']['price']['value'] or 0
      result['data']['retail_price'] = item['prices']['retailPrice']['value'] if item['prices']['retailPrice'] else 0
      result['data']['sale_price'] = item['prices']['salePrice']['value'] if item['prices']['salePrice'] else 0
      result['data']['images'] = process_images(item['images'])
      result['data']['custom_fields'] = process_custom_fields(item['customFields'])
      result['data']['reviews'] = process_reviews(item['reviews'])
      result['data']['related_products'] = process_related_products(item['relatedProducts'])
      result['data']['upc'] = item['upc'] or ''
      result['data']['condition'] = item['condition'] 
      result['data']['brand_name'] = item['brand']['name'] if item['brand'] else ''
      result['data']['inventory_is_in_stcok'] = item['inventory']['isInStock']
      result['data']['order_quantity_minimum'] = item['minPurchaseQuantity']
      result['data']['order_quantity_maximum'] = item['maxPurchaseQuantity'] 
      result['data']['reviews_count'] = item['reviewSummary']['numberOfReviews']
      result['data']['reviews_rating_sum'] = item['reviewSummary']['summationOfRatings']
      result['data']['weight'] = {
        "unit": item['weight']['unit'],
        "value": item['weight']['value']
      }
      result['data']['availability'] = item['availabilityV2']
      result['data']['description'] = item['description'] or ''
      result['data']['warranty'] = item['warranty'] or ''
      result['data']['breadcrumb_url'] = item['categories']['edges'][0]['node']['path'] + item['path' ].replace("/","")

    return result

def process_individual_variant(variant, headings, row_titles):
  res = {}
  
  variant = variant['node']
  res['id'] = variant['entityId']
  res['sku'] = variant['sku']
  
  res['inventory'] = {
    "available_to_sell": variant['inventory']['aggregated']['availableToSell'],
    "warning_level": variant['inventory']['aggregated']['warningLevel'],
  }
  
  prices = variant['prices']
  res['price'] = {
    "value":  prices['price']['value'],
    "currency_code": prices['price']['currencyCode']
  }
  
  res['sale_price'] = {
    "value":  prices['salePrice']['value'],
    "currency_code": prices['salePrice']['currencyCode']
  } if prices['salePrice'] else {"value": 0, "currency_code": ""}

  res['retail_price'] = {
    "value":  prices['retailPrice']['value'],
    "currency_code": prices['retailPrice']['currencyCode']
  } if prices['retailPrice'] else {"value": 0, "currency_code": ""}
  
  res['options'] = []
  if len(variant['productOptions']['edges']) > 0:
    for i in variant['productOptions']['edges']:
      obj = {}
      opt = i['node']
      
      obj['id'] = opt['entityId']
      # obj['display_name'] = opt['displayName']
      # obj['is_required'] = opt['isRequired']
      
      # generating table headers...
      if 'Nicotine' in opt['displayName']:
        if len(opt['values']['edges']) > 0:
          for i in opt['values']['edges']:
            op = i['node']
            dic = {
              "id": op['entityId'],
              "label": op['label']
            }
            if dic not in headings:
              headings.append(dic)

      # generating row titles...
      if len(opt['values']['edges']) > 0:
        for i in opt['values']['edges']:
          op = i['node']
          dic = {
            "id": op['entityId'],
            "label": op['label']
          }
          if dic not in row_titles and dic not in headings:
            row_titles.append(dic)
      
      # varaints ...
      if len(opt['values']['edges']) > 0:
        values = []
        for i in opt['values']['edges']:
          option = {}
          op = i['node']

          option['id'] = op['entityId']
          option['label'] = op['label']
          # option['is_default'] = op['isDefault']

          values.append(option)
        obj['values'] = values
      res['options'].append(obj)

  return res

def process_product_variants(item):
  resulted_variants = []
  headings = []
  row_titles = []
  variants = item['variants']['edges']

  if len(variants) > 0:
    for i in variants:
      res = process_individual_variant(i, headings, row_titles)
      resulted_variants.append(res) 
  else:
    return resulted_variants
    
  return resulted_variants, headings, row_titles

def dataToPrices(data):
  res = []
  for item in data['data']['site']['products']['edges']:
    res.append(item['node'])
  return res

def update_bc_product_variants(store, req_body):
  url = f'v3/catalog/variants'
  bc_api = store_util.get_bc_api_creds(store)
  res = bc.call_api(bc_api, "PUT", url, req_body=req_body) 
  # success ...   
  if res.status_code == 200:
      return res.json(), 200
  else:
      # unprocess able entity...
      return res.json(), res.status_code
