from services import Service
from utils.common import parse_json
from bson import ObjectId
from datetime import datetime
from flask import send_file, make_response
import os
from werkzeug.utils import secure_filename
import logging
import requests

logger = logging.getLogger()


class Categories(Service):
    def get_categories(self, body):
        result = []
        if (body):
            res = {}
            category_id = body['category_id']
            category = super().find_one({"_id": ObjectId(str(category_id))})

            res['id'] = category['id']
            res['name'] = category['name']
            res['description'] = category['description']
            res['created_at'] = category['created_at']
            res['updated_at'] = category['updated_at']
            res['status'] = category['status']

            result.append(res)
        else:
            categories = super().find_all()
            categories = parse_json(self.processList(categories))
            for category in categories:
                res = {}

                res['id'] = category['id']
                res['name'] = category['name']
                res['description'] = category['description']
                res['created_at'] = category['created_at']
                res['updated_at'] = category['updated_at']
                res['status'] = category['status']

                result.append(res)

        return result

    def create_category(self, body):
        response = {
            "status": 400
        }
        isUniqueName = self.checkForUniqueCategoryName(
            body['name'].strip(), '')
        if isUniqueName:
            body["created_at"] = int(datetime.utcnow().timestamp())
            body["updated_at"] = ""

            body['name'] = body['name'].strip()
            id = super().create(body)

            response['message'] = "Category created successfully"
            response['status'] = 200
        else:
            response['message'] = "The provided category name has already been matched with other categories. Please provide a different category name."
            response['status'] = 409

        return response

    def update_category(self, body, category_id=None):
        response = {
            "status": 400
        }
        if body['status_update'] == 'false':
            isUniqueName = self.checkForUniqueCategoryName(
                body['name'].strip(), category_id)
        else:
            isUniqueName = True

        if isUniqueName:
            id = super().update_one({"_id": ObjectId(str(category_id))}, {"$set":
                                                                          {
                                                                              "name": body['name'].strip(),
                                                                              "description": body['description'],
                                                                              "status": body['status'],
                                                                              "updated_at":  int(datetime.utcnow().timestamp())
                                                                          }
                                                                          })
            response['message'] = "Category Updated successfully"
            response['status'] = 200
        else:
            response['message'] = "The provided category name has already been matched with other categories. Please provide a different category name."
            response['status'] = 409

        return response

    def get_category(self, category_id=None):
        result = {}
        category = super().find_one({"_id": ObjectId(str(category_id))})

        result['id'] = category['id']
        result['name'] = category['name']
        result['description'] = category['description']
        result['created_at'] = category['created_at']
        result['updated_at'] = category['updated_at']
        result['status'] = category['status']

        return result

    def delete_by_id(self, category_id):
        return super().delete({"_id": ObjectId(str(category_id))})

    def checkForUniqueCategoryName(self, name, category_id):
        name = name.replace(" ", "").lower()
        # url = '/pages/' + url.replace(" ", "_").()
        categories = super().find_all()
        categories = parse_json(self.processList(categories))
        for category in categories:
            if not category['id'] == category_id:
                if category['name'].replace(" ", "").lower().strip() == name:
                    return False
        return True
