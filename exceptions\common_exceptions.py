
class InvalidInputException(Exception):

    def __init__(self, message="Invalid input."):
        self.message = message
        super().__init__(self.message)


class ResourceNotFoundException(Exception):

    def __init__(self, message="Resource doesn't exist."):
        self.message = message
        super().__init__(self.message)

class InactiveResourceException(Exception):

    def __init__(self, message="Resource status is inactive."):
        self.message = message
        super().__init__(self.message)


class ResourceAlreadyExistException(Exception):

    def __init__(self, message="Resource already exists."):
        self.message = message
        super().__init__(self.message)