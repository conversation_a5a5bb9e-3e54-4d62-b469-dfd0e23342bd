from flask import request
import logging
import traceback
import api
from utils import auth_util, store_util

logger = logging.getLogger()

class Login(api.APIResource):

    def post_executor(self, request):
        logger.debug("Entering Login POST")
        try:
            auth_header = request.headers.get("Authorization", None)
            store_id = request.headers.get("x-store-id", None)            
            
            store = store_util.get_store_by_id(store_id)            

            if auth_header:
                content = auth_util.login(auth_header, store)
                
                if content == False:
                    return {"message": "You are not authorized to login"}, 401

                if content['status'] == 200:
                    return content, 200
                
            return {"message": "Invalid credentials."}, 401
        finally:
            logger.debug("Exiting Login POST")

    def post(self):
        return self.execute_open_api_request(request, self.post_executor)

class Logout(api.APIResource):

    def post_executor(self, request, token_payload):
        logger.debug("Entering Logout POST")
        try:
            if token_payload:
                token = request.headers.get(api.AUTH_TOKEN_HEADER_KEY, None)
                result = auth_util.logout(token_payload['username'], token)
                if result:
                    return {"status": 200}, 200
            return {"message": "Unauthorized"}, 401
        finally:
            logger.debug("Exiting Logout POST")

    def post(self):
        return self.execute_request(request, self.post_executor)
    

class GoogleLogin(api.APIResource):
    def post_executor(self, request):
        logger.debug("Entering Login POST")
        try:
            req_body = request.get_json(force=True)  
            store_id = request.headers.get("x-store-id", None)
            store = store_util.get_store_by_id(store_id)
            content = auth_util.googleLogin(req_body, store)
            
            if content == False:
                return {"message": "You are not authorized to login"}, 401

            if content['status'] == 200:
                return content, 200   
            else: 
                return {"message": "You are not authorized to login"}, 401         
        finally:
            logger.debug("Exiting Login POST")

    def post(self):
        return self.execute_open_api_request(request, self.post_executor)
    

class ValidateToken(api.APIResource):
    def post_executor(self, request, token_payload):
        logger.debug("Entering ValidateToken POST")
        try:
            return {"message": "pong"}, 200    
        finally:
            logger.debug("Exiting ValidateToken POST")

    def post(self):
        return self.execute_request(request, self.post_executor) 