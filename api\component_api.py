from flask import request
import logging
from api import APIResource
import traceback
from schemas.dynamic_webpage import webpage_schema, component_schema
from utils import store_util

logger = logging.getLogger()

class Components(APIResource):
    def get_executor(self, request, token_payload, store):
        try:                       
            res = self.service.get_components_service().get_all_component()
            return res, 200
        finally:
            logger.debug("Exiting Component GET")    
    
       
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Component POST")
        try:
            component = request.get_json(force=True)           
            component_id = self.service.get_components_service().create(component)
            return {"component_id": str(component_id)}, 200
        finally:
            logger.debug("Exiting Components POST")    


    def post(self):        
        return self.execute_store_request(request, self.post_executor)
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ComponentOperations(APIResource):
    def put_executor(self, request, token_payload, store, webpage_id):
        try:
            req_body = request.get_json(force=True)
            validated_data = webpage_schema.validate(req_body)
            cms_db = store_util.get_cms_db(store)
            id = self.service.get_dynamic_pages_service(cms_db).update_page(validated_data, webpage_id)                
            if id:
                return {"status": "ok"}, 200
            else:
                return {"message": "Something went wrong at server!!"}, 500
        finally:
            logger.debug("Exiting webpage PUT")
                
    def delete_executor(self, request, token_payload, store, webpage_id):
        try:            
            cms_db = store_util.get_cms_db(store)
            success = self.service.get_dynamic_pages_service(cms_db).delete_by_id(webpage_id)
            if success:
                return {"status": "ok"}, 200
            return {"status": "failed"}, 500            
        finally:
            logger.debug("Exiting Webpage DELETE")
    
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Component POST")
        try:
            body = request.get_json(force=True)                      
            res = self.service.get_components_service().getVariant(body)
            if res:
                return res
            else: 
                return {"Somthing went wrong at server!!"}, 500
        finally:
            logger.debug("Exiting Components POST")    


    def put(self, webpage_id):
        return self.execute_store_request(request, self.put_executor, webpage_id)

    def delete(self, webpage_id):
        return self.execute_store_request(request, self.delete_executor, webpage_id)
    
    def post(self):        
        return self.execute_store_request(request, self.post_executor)
