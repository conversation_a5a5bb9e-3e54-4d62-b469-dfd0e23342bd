import logging
import mongo_db

logger = logging.getLogger()

STORE_INFO_COLLECTION = "store_info"

def fetch_all_product_listing_types():
    db = mongo_db.get_admin_db_client()
    coll = db[STORE_INFO_COLLECTION]
    stores_info = []
    cur = coll.find({"type": {"$in": ["featured_products", "new_products", "preorder_products"]}})
    for row in cur:
        data = mongo_db.process_data(row)
        stores_info.append(data)
    return stores_info