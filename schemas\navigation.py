from schema import Schem<PERSON>, Or, Optional

navigation_entity_schema = Schema([{
    'label': str,
    'url':  str,
    'class_name': str,
    'link_target': <PERSON>hem<PERSON>(Or("_blank", "_self")),
    'children': list,
    'type': str
}])

navigation_schema = Schema({
    'name': str,
    'description': str
})

navigation_update_schema = Schema({
    'name': str,
    'description': str,
    'status': str,
})
