from sqlalchemy import text
import pg_db
from mongo_db import user_db
from datetime import datetime
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from projects.project_modules import _insert_new_module
from projects.project_columns import add_new_column
from projects.default_columns import _fetch_default_coulmns
from utils.common import calculatePaginationData, convert_to_timestamp
import logging
import traceback
import re

logger = logging.getLogger()

def _fetch_projects(conn, username=None, id=None, search_value=None, is_archived=None, sort_array=[]):
    base_query = """
        SELECT
            p.id AS project_id,
            p.name AS project_name, 
            p.description AS description,
            p.bu_id AS business_unit_id,
            b.name AS business_unit, 
            (SELECT COUNT(*) FROM agile_project_access WHERE project_id = p.id and status = 'active') AS member_count,
            p.owner_username AS owner, 
            p.created_at, 
            p.updated_at, 
            p.updated_by, 
            p.is_archived,
            p.project_initials
        FROM 
            agile_projects p
        LEFT JOIN 
            business_units b ON p.bu_id = b.id
        LEFT JOIN 
            agile_project_access pa ON p.id = pa.project_id
    """
    
    conditions = []
    params = {}
    
    if search_value:
        conditions.append("(p.name ILIKE :search_value OR b.name ILIKE :search_value OR p.owner_username ILIKE :search_value)")
        params['search_value'] = f"%{search_value}%"

    
    conditions.append("p.is_archived = :is_archived")
    conditions.append("pa.username = :username")
    conditions.append("pa.status = 'active'")
    params['is_archived'] = is_archived
    params['username'] = username
    
    if id:
        conditions.append("p.id = :id")
        params['id'] = id

    
    if conditions:
        base_query += " WHERE " + " AND ".join(conditions)

    base_query += " GROUP BY p.id, b.id"       

    if len(sort_array):
        sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
        if sort_array[0] in ["name", "owner_username", "created_at", "updated_at"]:                
            base_query += f" ORDER BY p.{sort_array[0]} {sort_direction}"                
        elif sort_array[0] in ["business_unit"]: 
            base_query += f" ORDER BY b.name {sort_direction}"   
        elif sort_array[0] in ["members"]:
            base_query += f" ORDER BY COUNT(pa.project_id) {sort_direction}"               

    query = text(base_query)    
    result = conn.execute(query, params)

    fevorite_query = text(
        f"""SELECT * FROM agile_fevorite_projects WHERE username = :username"""
    )
    fevorite_query = fevorite_query.bindparams(username=username)
    fevorite_result = conn.execute(fevorite_query)        
    fevorite_data = fevorite_result.fetchall() 

    data = []    
    for row in result.fetchall():
        user_data = user_db.fetch_user_by_username(row[6])
        if user_data:
            name = user_data.get('name', '')
        else:
            name = ''
        project_data = {
            'id': row[0],
            'name': row[1],
            'description': row[2],
            'business_unit_id': row[3],
            'business_unit': row[4],
            'members': row[5],
            'owner_username': row[6],
            'owner_name': name,
            'created_at': convert_to_timestamp(row[7]),
            'updated_at': convert_to_timestamp(row[8]),
            'updated_by': row[9],
            'is_archived': row[10],
            'is_fevorite': 0,
            'project_initials': row[11]
        }
        data.append(project_data)
    
    if fevorite_data:                            
        for project in data:
            for fevorite_project in fevorite_data:
                if project['id'] == fevorite_project[1]:
                    project['is_fevorite'] = 1
                    break  # Break inner loop since the project has been found as favorite
    
    sorted_data = sorted(data, key=lambda x: x['is_fevorite'], reverse=True)
    return sorted_data

def get_project_listing(username, search_value, is_archived, limit, page, sort_array):
    response = {
        "status": 400,
    }
    conn = pg_db.get_connection()
    try:
        id = None       
        
        projects = _fetch_projects(conn, username, id, search_value, is_archived, sort_array)        

        start_index = (page - 1) * limit
        end_index = start_index + limit

        paginated_data = projects[start_index:end_index]
        
        total_records = len(projects)
        data = calculatePaginationData(paginated_data, page, limit, total_records)
        response['data'] = data
        response['status'] = 200
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response

            
def _insert_project(name, bu_id, description, owner_username, is_archived, project_initials):
    conn = pg_db.get_connection()
    try:
        # Insert into projects table
        project_query = text(
            f"""INSERT INTO {pg_db.projects} (name, bu_id, description, owner_username, is_archived, updated_by, updated_at, project_initials)
                VALUES (:name, :bu_id, :description, :owner_username, :is_archived, :owner_username, CURRENT_TIMESTAMP, :project_initials)
                RETURNING id;
            """
        )
        project_query = project_query.params(name=name, bu_id=bu_id, description=description, owner_username=owner_username, is_archived=is_archived, project_initials=project_initials)
        result = conn.execute(project_query)
        project_id = result.fetchone()[0]

        # Insert into project_access table
        is_owner = True
        access_query = text(
            f"""INSERT INTO {pg_db.project_access} (project_id, username, is_owner)
                VALUES (:project_id, :username, :is_owner);
            """
        )
        access_query = access_query.params(project_id=project_id, username=owner_username, is_owner=is_owner)
        conn.execute(access_query)
    except IntegrityError as e:
        logger.error(traceback.format_exc())
        return False
    finally:
        if conn:
            conn.commit()
            conn.close()    
    return project_id

def insert_project_card_index(project_id, card_index):
    conn = pg_db.get_connection()
    try:
        insert_query = text(
            f"""INSERT INTO {pg_db.agile_project_cardindex} (project_id, card_index)
                VALUES (:project_id, :card_index);
            """
        )
        insert_query = insert_query.params(project_id=project_id, card_index=card_index)
        conn.execute(insert_query)

        return True
    except IntegrityError as e:
        logger.error(traceback.format_exc())
        return False
    finally:
        if conn:
            conn.commit()
            conn.close()

def create_new_project(payload, username):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:
        name = payload['name']
        description = payload.get('description', '')
        bu_id = payload['bu_id']
        is_archived = payload['is_archived']

        # Restrict special characters except '-' and ensure '-' is not the starting character
        if not re.match(r"^[A-Za-z0-9][A-Za-z0-9\s-]*$", name):
            response['status'] = 409
            response['message'] = "name: Only alphanumeric characters, spaces, and '-' are allowed. The name must start with an alphanumeric character."
            return response
        
        words = re.split(r'\s+|-', name)
        project_initials = ''.join([word[0].upper() for word in words if word.isalnum()])
        project_initials = project_initials[:3]

        # Check if these initials are unique, if not, append letters to make it unique
        unique_query = text(f"SELECT project_initials FROM {pg_db.projects} WHERE LOWER(project_initials) LIKE LOWER(:project_initials_pattern);")
        initial_check_query = unique_query.params(project_initials_pattern=f'{project_initials}%')
        initial_check_result = conn.execute(initial_check_query)

        # Collect all initials that match the generated pattern
        existing_initials = [row[0] for row in initial_check_result]

        # Function to generate next unique initials based on existing ones
        def generate_unique_initials(initial, existing_initials):
            suffix = ''
            while initial + suffix in existing_initials:
                if not suffix:
                    suffix = 'A'
                else:
                    suffix = increment_suffix(suffix)  # AA, AB, BA, etc.
            return initial + suffix

        # Function to increment suffix like A -> B, Z -> AA, etc.
        def increment_suffix(suffix):
            if all(c == 'Z' for c in suffix):
                return 'A' * (len(suffix) + 1)
            return chr(ord(suffix[-1]) + 1) if suffix[-1] != 'Z' else increment_suffix(suffix[:-1]) + 'A'

        # If there are matching initials, generate a unique version
        if project_initials in existing_initials:
            project_initials = generate_unique_initials(project_initials, existing_initials)

        unique_query = text (f"""SELECT * from {pg_db.projects} where LOWER(name) = LOWER(:name);""")
        unique_query = unique_query.params(name=name)
        project_result = conn.execute(unique_query)
        if project_result.rowcount > 0:
            response['status'] = 409
            response['message'] = "name: The Project name with the same name already exists."
            return response

        if name and bu_id and username:

            data = _insert_project(name, bu_id, description, username, is_archived, project_initials)            
            if data:
                # Adding default columns                
                default_columns = _fetch_default_coulmns(conn, None)
                for column_data in default_columns:   
                    column_data['project_id'] = data                                    
                    name = column_data['name'] + "_" + str(data)
                    description = column_data['description']
                    is_archived = column_data['is_archived']

                    column = add_new_column(name, description, is_archived, username, data, True, False)                                    

                create_module = _insert_new_module(conn, data, "Uncategorized" + "_" + str(data), 'This is a default module', is_archived, username, True, True)
                card_index = insert_project_card_index(data, 0)

                if column and create_module and card_index:
                    response['status'] = 200
                    response['message'] = "Data inserted successfully and default columns and module created."
                else:
                    response['status'] = 200
                    response['message'] = "Data insertion successfully but default columns or module not created."
            else:
                response['status'] = 400
                response['message'] = "Data insertion failed."
        else:
            response['status'] = 400
            response['message'] = "Invalid payload, fields are missing."
        
    except IntegrityError as e:
            logger.error(traceback.format_exc())
            if isinstance(e.orig, UniqueViolation):
                response['status'] = 409
                response['message'] = "Duplicate key violation: This module already exists in the rules."
            else:
                response['status'] = 500
                response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response

def add_projects_to_fevorite(username, project_id):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:            
        if username and project_id:            
            existing_query = text(
                f"""SELECT * FROM agile_fevorite_projects WHERE username = :username and project_id = :project_id"""
            )
            existing_query = existing_query.bindparams(username=username, project_id=project_id)
            existing_result = conn.execute(existing_query)        
            existing_data = existing_result.fetchall()
            if existing_data:
                remove_query = text(
                    f"""DELETE FROM agile_fevorite_projects WHERE username = :username and project_id = :project_id"""                    
                )
                remove_query = remove_query.bindparams(username=username, project_id=project_id)
                remove_result = conn.execute(remove_query)                  
                response['status'] = 200
                response['message'] = "Project is removed from favorite."
                return response
            else:
                insert_query = text(
                    f"""INSERT INTO agile_fevorite_projects (project_id, username, created_at)
                        VALUES (:project_id, :username, now());
                    """
                )
                insert_query = insert_query.params(project_id=project_id, username=username)
                conn.execute(insert_query)

                response['status'] = 200
                response['message'] = "Project is added to favorite."
        else:
            response['status'] = 400
            response['message'] = "Invalid payload, fields are missing."
        
    except IntegrityError as e:
        logger.error(traceback.format_exc())
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 409
            response['message'] = "Duplicate key violation: This module already exists in the rules."
        else:
            response['status'] = 500
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response

def get_projects_dropdown(username):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        query = text(
            f"""SELECT ap.id, ap.name, ap.is_archived FROM agile_projects ap JOIN agile_project_access apa ON ap.id = apa.project_id 
            WHERE apa.username = :username AND apa.status = 'active'
                ORDER BY ap.name ASC
            """
        )
        query = query.params(username=username)
        result = conn.execute(query)
        data = result.fetchall()
        projects = []
        for row in data:
            project_data = {
                'id': row[0],
                'name': f"Archived : {row[1]}" if row[2] else row[1]
            }
            projects.append(project_data)
        if projects:
            response['data'] = projects
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        conn.close()
    return response
        
