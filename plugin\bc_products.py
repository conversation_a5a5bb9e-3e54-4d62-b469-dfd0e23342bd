from graphql import products_query
from utils import bc, redis_util, store_util
import plugin
import time
from mongo_db import catalog_db
import logging
from plugin import bc_price_list
import re
import traceback
import math
from new_mongodb import get_admin_db_client_for_store_id
import new_pgdb
from utils.common import fetch_static_price_lists
logger = logging.getLogger()


def process_options(node):
    options = {}
    options['id'] = node['entityId']
    options['displayName'] = node['displayName']
    options['isRequired'] = node['isRequired']
    options['values'] = plugin.process_edges(node, 'values')
    return options

def process_variant(node):
    variant = {}
    variant['id'] = node['entityId']
    variant['sku'] = node['sku']
    variant['isPurchasable'] = node['isPurchasable']
    variant['inventory'] = node['inventory']
    variant['options'] = plugin.process_edges(node, 'options', process_options)
    variant['prices'] = node['prices']
    return variant

def process_product(product):
    result = {}
    result['_id'] = product['entityId']
    result['sku'] = product['sku']
    result['name'] = product['name']
    result['description'] = product['description']
    result['minPurchaseQuantity'] = product['minPurchaseQuantity']
    result['type'] = product['type']
    result['weight'] = product['weight']
    result['height'] = product['height']
    result['width'] = product['width']
    result['depth'] = product['depth']
    result['inventory'] = product['inventory']
    result['defaultImage'] = product['defaultImage']
    result['brand'] = product['brand']
    result['prices'] = product['prices']
    result['reviewSummary'] = product['reviewSummary']
    result['date_created'] = product['date_created']
    result['search_field'] = product['name'] + " " + product['sku'] + " " + str(product['id'])
    result['categories'] = plugin.process_edges(product,'categories')
    result['variants'] = plugin.process_edges(product, 'variants', process_variant)
    result['images'] = plugin.process_edges(product, 'images')
    return result

def fetch_products(store, query_params={}):
    api = "v3/catalog/products"
    req_body = {
        "query_params": query_params,
        "method": "GET",
        "url": api
    }
    status, res = bc.process_bc_api_request(store, req_body)    
    if str(status) == "200":
        for idx, obj in enumerate(res):
            image = ""
            custom_url = ""
            brand = redis_util.get_brand_by_id(store["id"], obj["brand_id"])
            brand_name = ""
            brand_url = ""
            if brand:
                brand_name = brand["name"]
                brand_url = brand["custom_url"]["url"]

            res[idx]["brand_name"] = brand_name
            res[idx]["brand_url"] = brand_url
            if 'images' in obj and len(obj['images']) > 0:
                image = obj["images"][0]["url_standard"]
                del res[idx]["images"]
            if 'custom_url' in obj and not obj['custom_url']['is_customized']:
                custom_url = res[idx]["custom_url"]["url"]
                del res[idx]["custom_url"]
            res[idx]["image"] = image
            res[idx]["custom_url"] = custom_url
    return status, res

def get_product_card_query_param(page=1, limit=15, include_fields=[], include=[]):
    query_params = {
        "availability": "available",
        "direction": "desc",
        "is_visible": "true",
        "sort": "id",
        "include_fields": "name,sku,custom_url,brand_id,brand_name,reviews_rating_sum,reviews_count,price,retail_price,sale_price,calculated_price,inventory_level,images,base_variant_id",
        "include": "images",
        "limit": limit,
        "page": page
    }
    if include_fields and len(include_fields) > 0:
        query_params["include_fields"] = ','.join(include_fields)

    if include and len(include) > 0:
        query_params["include"] = ','.join(include)

    return query_params

def fetch_featured_products(store, include_fields=[], include=[], page=1, limit=15):
    query_params = get_product_card_query_param(page, limit, include_fields, include)
    query_params["is_featured"] = 1
    return fetch_products(store, query_params)

def fetch_new_products(store, include_fields=[], include=[], page=1, limit=15):
    query_params = get_product_card_query_param(page, limit, include_fields, include)
    return fetch_products(store, query_params)

def fetch_popular_products(store, include_fields=[], include=[], page=1, limit=15):
    query_params = get_product_card_query_param(page, limit, include_fields, include)
    query_params["sort"] = "total_sold"
    return fetch_products(store, query_params)

def fetch_products_rest_api(store):
    include_fields=["name","sku","availability","type","categories","custom_url","brand_id","is_featured","page_title","is_visible","is_price_hidden","price","retail_price","sale_price","calculated_price","inventory_level","images","options","variants","bulk_pricing_rules", "date_created"]
    include=["images","options","variants","bulk_pricing_rules"]
    query_params = get_product_card_query_param(0, 10, include_fields, include)
    del query_params["is_visible"]
    api = "v3/catalog/products"
    return plugin.fetch_all_by_rest_api(store, api, limit_per_req=10, query_params=query_params, 
            db_collection=catalog_db.PRODUCTS_COLLECTION, db_process_threshold=100)

def fetch_products_graphql(store):
    return plugin.fetch_all_with_pagination(store=store, query_builder=products_query, page_size=250, 
            resource_name="products", resource_processor=process_product,
            db_collection = catalog_db.PRODUCTS_COLLECTION)

def fetch_all_products(store):
    return fetch_products_rest_api(store)

def fetch_customer_group_pricing(store, customer_group_id, product_list):
    api = "v3/pricing/products"
    req_body = {
        "channel_id": 1,
        "currency_code": "USD",
        "customer_group_id": customer_group_id,
        "items": product_list
    }
    req_data = {
                "query_params": {},
                "method": "POST",
                "url": "v3/pricing/products",
                "body": req_body
            }
    bc_api = store_util.get_bc_api_creds(store)
    res = bc.process_api(bc_api, req_data, exclude_meta=False)
    prices = []
    if res["status_code"] == 200:
        data = res['data']['data']
        for pp in data:
            prices.append({
                "product_id": pp["product_id"],
                "variant_id": pp["variant_id"],
                "price": pp["price"]["as_entered"]
            })
    return prices

def update_products(store, products=[]):
    include_fields=["name","sku","availability","type","categories","custom_url","brand_id","is_featured","page_title","is_visible","is_price_hidden","price","retail_price","sale_price","calculated_price","inventory_level","images","options","variants","bulk_pricing_rules"]
    include=["images","options","variants","bulk_pricing_rules"]
    query_params = get_product_card_query_param(0, 10, include_fields, include)
    del query_params["is_visible"]
    api = "v3/catalog/products"

    product_list = []
    for product_id in products:
        product_list.append(str(product_id))
        if len(product_list) == 10:
            query_params['id:in'] = ",".join(product_list)            
            plugin.fetch_all_by_rest_api(store, api, limit_per_req=10, query_params=query_params, 
                    db_collection=catalog_db.PRODUCTS_COLLECTION, db_process_threshold=10)
            product_list = []
    
    if len(product_list) > 0:
        query_params['id:in'] = ",".join(product_list)
        plugin.fetch_all_by_rest_api(store, api, limit_per_req=10, query_params=query_params, 
                    db_collection=catalog_db.PRODUCTS_COLLECTION, db_process_threshold=10)
        product_list = []


def fetch_product_category_bc(store, pid):        
    url = f"v3/catalog/products/{pid}"
    bc_api = store_util.get_bc_api_creds(store)
    query_params = {       
        "include_fields": "name,sku,categories",        
    }
    product_data = bc.call_api(bc_api, "GET", url,query_params=query_params) 
    product = {}
    if product_data.status_code == 200:  
        product = product_data.json()     
    return product

def fetch_bc_product_by_sku(store, sku):        
    url = f"v3/catalog/products"
    bc_api = store_util.get_bc_api_creds(store)
    query_params = { 
        "sku:in": sku, 
        "include":'variants,images,custom_fields',     
        "include_fields": "name,sku,page_title,custom_url,is_visible,images,upc,inventory_level",
                
    }
    product_data = bc.call_api(bc_api, "GET", url,query_params=query_params) 
    product = {}
    if product_data.status_code == 200:  
        product = product_data.json()     
    return product

def fetch_bc_variant_by_sku(store, sku):
    url = f"v3/catalog/variants"
    bc_api = store_util.get_bc_api_creds(store)
    query_params = { 
        "sku": sku,     
    }
    variant_data = bc.call_api(bc_api, "GET", url,query_params=query_params) 
    variant = {}
    if variant_data.status_code == 200:  
        variant = variant_data.json()     
    return variant

def fetch_bc_product_by_name(store, name):        
    url = f"v3/catalog/products"
    bc_api = store_util.get_bc_api_creds(store)
    query_params = { 
        "name": name, 
        "include":'variants,images,custom_fields',     
        "include_fields": "name,sku,page_title,custom_url,is_visible,images,upc,inventory_level",
                
    }
    product_data = bc.call_api(bc_api, "GET", url,query_params=query_params) 
    product = {}
    if product_data.status_code == 200:  
        product = product_data.json()     
    return product

def fetch_products_by_ids(store, ids=[]):
    products = []
    include_fields = ["name", "sku", "availability", "is_visible", "price", "inventory_level", "variants"]
    include = ["variants"]
    api = "v3/catalog/products"
    bc_api = store_util.get_bc_api_creds(store)
    # Determine the number of chunks needed
    num_chunks = math.ceil(len(ids) / 50)

    for i in range(num_chunks):
        # Get the current chunk of ids
        chunk_ids = ids[i * 50: (i + 1) * 50]
        query_params = {
            "include": ",".join(include),
            "include_fields": ",".join(include_fields),
            "id:in": ",".join(chunk_ids)
        }
        logger.error(api)
        logger.error(bc_api)
        logger.error(query_params)
        res = bc.call_api(bc_api, "GET", api, query_params=query_params)
        if res.status_code < 299:
            products.extend(res.json()['data'])
        else:
            logger.error(f"Error fetching products for chunk {i+1}/{num_chunks}: {res.status_code}")

    return products

def fetch_bc_product(store, pid):
    url = f"v3/catalog/products/{pid}"
    bc_api = store_util.get_bc_api_creds(store)
    query_params = {                
        "include_fields": "name,type,sku,description,weight,width,depth,height,price,cost_price,retail_price,map_price,categories,brand_id,brand_name,inventory_warning_level,is_visible,custom_url,brand_id,brand_name,reviews_rating_sum,reviews_count,price,retail_price,sale_price,calculated_price,inventory_level,custom_fields,images,videos,variants,options,mpn,gtin,upc,bin_picking_number,price,tax_class_id,inventory_tracking,search_keywords,sort_order,warranty,availability,availability_description,condition,is_condition_shown,custom_fields,is_free_shipping,fixed_cost_shipping_price,order_quantity_maximum,order_quantity_minimum,page_title,meta_description,fixed_cost_shipping_price",
        "include": "custom_fields,images,variants,videos,variants,options",
    }
    product_data = bc.call_api(bc_api, "GET", url,query_params=query_params) 
    product = {}
    if product_data.status_code == 200:  
        product = product_data.json()     
    return product


def fetch_static_prices(store, product_id, variant_id):
    db = get_admin_db_client_for_store_id(store['id'])
    # static_price_lists_collection = db.static_price_lists
    # db['static_price_lists'].find_one({"product_id": int(product_id)})

    # Query for matching product_id and variant_id
    result = db['static_price_lists'].find_one(
        {
            "product_id": int(product_id),
            "variants.variant_id": int(variant_id)
        },
        {
            "variants.$": 1  # Limit result to matching variant
        }
    )

    # Extract price list data if result is found
    if result:
        return result['variants'][0]['price_list']
    return []


def get_customer_group_pricing(store, product_id, product_variants):
    res = bc_price_list.fetch_price_lists(store)
    
    variant_counts = len(product_variants)
    extracted_data = []
    meta_dict = {}
    meta_static = {}
    res = res[0]
    groups = res['data']

    if res and 'data' in res:
        all_prices = {}
        all_static_prices = {}
        price_list_ids = []
        found = False
        count = 0
        
        # Prioritize price_list_id 7, if present
        primary_price_list_id = 7
        primary_group = next((group for group in groups if group['id'] == primary_price_list_id and group['active']), None)
        remaining_groups = [group for group in groups if group['id'] != primary_price_list_id and group['active']]

        # Process primary group first if it exists
        if primary_group:
            name_without_price_words = re.sub(r'\b(price|list|words|to|remove)\b', '', primary_group['name'], flags=re.IGNORECASE).strip()
            meta_dict[f"price_1"] = name_without_price_words
            key = 'price_1'
            price_list_id = primary_group['id']
            price_list_ids.append(price_list_id)
            
            price_list_records = bc_price_list.fetch_price_list_records(store, price_list_id, product_id)
            if len(price_list_records) != variant_counts:
                found = True

            if price_list_records:
                for record in price_list_records:
                    variant_group_info = {}
                    variant_id = str(record['variant_id'])
                    if variant_id not in all_prices:
                        all_prices[variant_id] = []
                    
                    variant_group_info[key] = record['price']
                    variant_group_info['price_list_id'] = price_list_id
                    all_prices[variant_id].append(variant_group_info)
            else:
                variant_group_info = {}
                if '0' not in all_prices:
                    all_prices['0'] = []
                variant_group_info[key] = '-'
                variant_group_info['price_list_id'] = price_list_id
                all_prices['0'].append(variant_group_info)
        
        # Process remaining active dynamic price lists
        for index, group in enumerate(remaining_groups, start=(2 if primary_group else 1)):
            name_without_price_words = re.sub(r'\b(price|list|words|to|remove)\b', '', group['name'], flags=re.IGNORECASE).strip()
            meta_dict[f"price_{index}"] = name_without_price_words
            key = f'price_{index}'
            price_list_id = group['id']
            price_list_ids.append(price_list_id)
            
            price_list_records = bc_price_list.fetch_price_list_records(store, price_list_id, product_id)
            if len(price_list_records) != variant_counts and not found:
                found = True

            if price_list_records:
                for record in price_list_records:
                    variant_group_info = {}
                    variant_id = str(record['variant_id'])
                    if variant_id not in all_prices:
                        all_prices[variant_id] = []
                    
                    variant_group_info[key] = record['price']
                    variant_group_info['price_list_id'] = price_list_id
                    all_prices[variant_id].append(variant_group_info)
            else:
                variant_group_info = {}
                if '0' not in all_prices:
                    all_prices['0'] = []
                variant_group_info[key] = '-'
                variant_group_info['price_list_id'] = price_list_id
                all_prices['0'].append(variant_group_info)

        # Populate any missing dynamic price lists with '-'
        if found:
            for key, value in all_prices.items():
                for index, id in enumerate(price_list_ids):
                    key = 'price_' + str(index+1)
                    is_found = False
                    for list_item in value:
                        if str(list_item['price_list_id']) == str(id):
                            is_found = True
                            break
                    if not is_found:
                        default_value = {}
                        default_value[key] = '-'
                        default_value['price_list_id'] = id
                        value.append(default_value)

        default_static_price_lists = fetch_static_price_lists(store['id'])

        # Add static price lists to meta_dict
        static_start_index = 1  # Continue from the next available price count
        for i, static_price in enumerate(default_static_price_lists, start=static_start_index):
            # Update meta_static with sequential keys
            meta_static[f"price_{i}"] = static_price["name"]

        # Fetch and integrate static prices for each variant
        for variant in product_variants:
            vid = str(variant['id'])
            static_prices = fetch_static_prices(store, product_id, int(vid))

            # Initialize static customer groups with placeholders
            static_customer_groups = [
                {
                    f"price_{i + 1}": "-",
                    "price_list_id": static_price_list["id"]
                }
                for i, static_price_list in enumerate(default_static_price_lists)
            ]

            # Populate available prices into the placeholders
            if static_prices:
                # Convert static_prices to a dictionary for faster lookups
                static_price_map = {sp["price_list_id"]: sp["price"] for sp in static_prices}

                for i, static_price_list in enumerate(default_static_price_lists):
                    price_list_id = static_price_list["id"]
                    # Update the placeholder if the price exists
                    if price_list_id in static_price_map:
                        static_customer_groups[i][f"price_{i + 1}"] = static_price_map[price_list_id]

            
            all_static_prices[vid] = static_customer_groups

    for variant in product_variants:
        vid = str(variant['id'])
        variant_info = {
            'sku': variant['sku'],
            'variant_id': variant['id'],
            'customer_groups': [],
            'static_customer_groups': [],
        }
        option_values = variant.get('option_values', [])

        if vid in all_prices:
            variant_info['customer_groups'] = sorted(
                all_prices[vid],
                key=lambda x: next(key for key in x.keys() if key.startswith('price_') and 'price_list_id' not in key)
            )
        else:
            variant_info['customer_groups'] = all_prices['0']
        
        if vid in all_static_prices:
            variant_info['static_customer_groups'] = all_static_prices[vid]
        else:
            variant_info['static_customer_groups'] = all_static_prices['0']
        
        for index, option in enumerate(option_values):
            label = 'label_' + str(index+1)
            display_name = "display_name_" + str(index+1)
            variant_info[label] = option['label']
            variant_info[display_name] = option['option_display_name']

        extracted_data.append(variant_info)

    return extracted_data, meta_dict, meta_static



def update_bc_product(store, req_body, product_id):
    url = f'v3/catalog/products/{product_id}'
    bc_api = store_util.get_bc_api_creds(store)
    res = bc.call_api(bc_api, "PUT", url, query_params={} ,req_body=req_body) 

    # success ...   
    if res.status_code == 200:
        return res.json(), 200
    else:

        # unprocess able entity...
        return res.json(), res.status_code
    
def create_bc_product(store, req_body):
    url = f'v3/catalog/products'
    bc_api = store_util.get_bc_api_creds(store)
    res = bc.call_api(bc_api, "POST", url, query_params={} ,req_body=req_body) 

    # success ...   
    if res.status_code == 200:
        return res.json(), 200
    else:
        # unprocess able entity...
        return res.json(), res.status_code

    
def update_bc_customer_group_price(store, req_body):
    res = None
    customer_group_pricing = req_body.get("customer_group_pricing", [])
    if customer_group_pricing is not []:

        groups_by_price_list = {}
        variant_ids_by_price_list = {}

        for group in customer_group_pricing:
            price_list_id = group.get("price_list_id")
            price = group.get("price", 0)
            if price != 0:
                if price_list_id is not None:
                    if price_list_id not in groups_by_price_list:
                        groups_by_price_list[price_list_id] = []
                    groups_by_price_list[price_list_id].append(group)
            else:
                if 'variant_id' in group:
                    variant_id = group.get("variant_id")
                else:
                    pass
                if price_list_id not in groups_by_price_list:
                        groups_by_price_list[price_list_id] = []
                        variant_ids_by_price_list[price_list_id] = []  # Initialize list for variant IDs
                groups_by_price_list[price_list_id].append(group)

                if variant_id:
                    variant_ids_by_price_list[price_list_id].append(variant_id)  # Collect variant IDs
        
        # Iterate over the groups and update price lists
        for price_list_id, groups in groups_by_price_list.items():
            res = bc_price_list.update_price_list(store, price_list_id, groups)

        
        if len(variant_ids_by_price_list) > 0:
            for price_list_id, variant_ids in variant_ids_by_price_list.items():
                res = bc_price_list.delete_price_list(store, variant_ids, price_list_id)

    return res
    


def fetch_bc_tax_classes(store):
    url = f"v2/tax_classes"
    bc_api = store_util.get_bc_api_creds(store)
    query_params = {                
        "page": 1,
        "limit": 250
    }
    tax_data = bc.call_api(bc_api, "GET", url,query_params=query_params) 
    tax_classes = {}
    if tax_data.status_code == 200:  
        tax_classes = tax_data.json()     
    return tax_classes      


def get_products(store, sku):
    bc_api = store_util.get_bc_api_creds(store)
    api = f"v3/catalog/products"
    query_params = {                
        "include": "variants",
        "sku": sku
    }
    return bc.call_api(api_data=bc_api, method="GET", url=api, query_params=query_params)

def fetch_bc_product_variant(store, product_id, variant_id):
    url = f"v3/catalog/products/{product_id}/variants/{variant_id}"
    bc_api = store_util.get_bc_api_creds(store)
    product_data = bc.call_api(bc_api, "GET", url) 
    product = {}
    if product_data.status_code == 200:  
        product = product_data.json()     
    return product

def fetch_bc_product_variants(store, product_id):
    url = f"v3/catalog/products/{product_id}/variants"
    bc_api = store_util.get_bc_api_creds(store)
    variant_data = bc.call_api(bc_api, "GET", url) 
    variants = []
    if variant_data.status_code == 200:  
        variants = variant_data.json()['data']     
    return variants

def fetch_product_complex_rules(store, product_id):
    url = f"v3/catalog/products/{product_id}/complex-rules"
    bc_api = store_util.get_bc_api_creds(store)
    rules = bc.call_api(bc_api, "GET", url)
    if rules.status_code == 200:
        return rules.json()
    else:
        return []
    
def fetch_product_options(store, product_id, option_id):
    url = f"v3/catalog/products/{product_id}/options/{option_id}"
    bc_api = store_util.get_bc_api_creds(store)
    options = bc.call_api(bc_api, "GET", url)
    if options.status_code == 200:
        return options.json()
    else:
        return []
    
def fetch_product_option_values(store, product_id, option_id, value_id):
    url = f"v3/catalog/products/{product_id}/options/{option_id}/values/{value_id}"
    bc_api = store_util.get_bc_api_creds(store)
    values = bc.call_api(bc_api, "GET", url)
    if values.status_code == 200:
        return values.json()
    else:
        return []
    
def fetch_product_variant_by_id(store, product_id, variant_id):
    url = f"v3/catalog/products/{product_id}/variants/{variant_id}"
    bc_api = store_util.get_bc_api_creds(store)
    variant = bc.call_api(bc_api, "GET", url)
    if variant.status_code == 200:
        return variant.json()
    else:
        return []
    
def delete_bc_custom_field(store, product_id, custom_field_id):
    url = f"v3/catalog/products/{product_id}/custom-fields/{custom_field_id}"
    bc_api = store_util.get_bc_api_creds(store)
    custom_field = bc.call_api(bc_api, "DELETE", url)
    if custom_field.status_code == 204:
        return {"message": "Custom field deleted successfully"}
    else:
        return {"message": "Failed to delete custom field"}
    
def get_bc_custom_fields(store, product_id):
    url = f"v3/catalog/products/{product_id}/custom-fields"
    bc_api = store_util.get_bc_api_creds(store)
    custom_fields = bc.call_api(bc_api, "GET", url)
    if custom_fields.status_code == 200:
        return custom_fields.json()
    else:
        return []