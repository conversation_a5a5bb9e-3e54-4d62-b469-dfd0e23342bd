from sqlalchemy import text
import pg_db
from mongo_db import user_db
from datetime import datetime
from utils import common
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import logging
import traceback
from utils.common import convert_to_timestamp

logger = logging.getLogger()

def _fetch_business_unit_detail(conn, id):
    data = [] 
    query = text(
        f"""SELECT * 
            FROM {pg_db.business_units}
            WHERE id = :id;
        """
    )
    query = query.params(id=id)
    result = conn.execute(query)    
    for row in result.fetchall():
        business_unit_data = {
            'id': row[0],
            'business_unit_name': row[1],
            'created_by': row[2],
            'updated_by': row[3],
            'created_at': convert_to_timestamp(row[4]),
            'updated_at': convert_to_timestamp(row[5])
        }
        data.append(business_unit_data)
    return data


def get_business_unit_detail(id):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        data = _fetch_business_unit_detail(conn, id)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 404
            response['message'] = 'No data found.'
    finally:
        if conn:
            conn.close()
    return response

def update_business_unit(payload, username, id):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        if not id:
            response['status'] = 400
            response['message'] = "'id' is missing."
            return response

        existing_unit = _fetch_business_unit_detail(conn, id)
        if not existing_unit:
            response['status'] = 404
            response['message'] = "Business Unit not found."
            return response

        existing_unit = existing_unit[0]

        # Update fields if provided in payload
        update_fields = {}
        for field in ['name']:
            if field in payload:
                update_fields[field] = payload[field]

        # Update the project with the modified data
        data = _modify_business_unit(conn, update_fields, id, username)
        if data:
            response['status'] = 200
            response['message'] = "Data updated successfully."
        else:
            response['status'] = 500
            response['message'] = "Data updation failed."
    except IntegrityError as e:
        logger.error(traceback.format_exc())
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 422
            response['message'] = "Duplicate key violation: This Business unit already exists."
        else:
            response['status'] = 500
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response


def _modify_business_unit(conn, update_fields, id, username):
    set_clause = ", ".join([f"{field} = :{field}" for field in update_fields])
    query = text(
        f"""UPDATE {pg_db.business_units}
            SET 
                {set_clause},
                updated_by = :updated_by,
                updated_at = CURRENT_TIMESTAMP
            WHERE 
                id = :id;"""
    )
    params = update_fields.copy()
    params.update({'updated_by': username, 'id': id})
    result = conn.execute(query, params)    
    return result.rowcount > 0


def _remove_business_unit(conn, id):
    # Check if the id is associated with any records in the projects table
    query_check = text(
        f"""SELECT COUNT(*) 
            FROM {pg_db.projects}
            WHERE bu_id = :id;
        """
    )
    query_check = query_check.params(id=id)
    result_check = conn.execute(query_check)
    count = result_check.scalar()  # Retrieve the count of associated records

    if count > 0:
        # If there are associated records, do not perform deletion
        return False
    else:
        # If there are no associated records, perform deletion
        query_delete = text(
            f"""DELETE FROM {pg_db.business_units}
                WHERE id = :id;
            """
        )
        query_delete = query_delete.params(id=id)
        result_delete = conn.execute(query_delete)
        return result_delete.rowcount > 0

def delete_business_unit(id):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:
        if id:
            data = _remove_business_unit(conn, id)
            if data == True:
                response['status'] = 200
                response['message'] = "Data deleted successfully."
            else:
                response['status'] = 400
                response['message'] = "Data deletion failed."
        else:
            response['status'] = 500
            response['message'] = "id is missing."
    finally:
        if conn:
            conn.commit()
            conn.close()

    return response