from sqlalchemy import text
import new_pgdb
from datetime import datetime
import new_utils
from utils.common import get_order_status_name, json_serial, conver_to_json, paginate_data, calculatePaginationData, convert_to_timestamp
import traceback
import logging
from appconfig import is_pgdb_read_only_enabled

logger = logging.getLogger()

def get_customers_with_no_orders(store, payload):
    data = None
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        limit = int(payload.get('limit', 50))
        page = int(payload.get('page', 1))
        offset = (page - 1) * limit
        sort_by = payload.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []

        count_query = """
            SELECT COUNT(*) AS total_records
            FROM customers cus
            WHERE NOT EXISTS (
                SELECT 1
                FROM orders o
                WHERE o.customer_id = cus.customer_id
            )
            AND cus.customer_group_name IN ('Tier Pro Pricing Group (No Sale Coupons No CC Agreement Required)', 'Tiered 1 Prices Group  (Make Sure Customer Has No Coupon CODE)', 'Tiered 2 Prices Group  (Make Sure Customer Has No Coupon CODE)', 'Value Tier Pricing Group')
        """

        count_rs = conn.execute(text(count_query))
        total_count = int(count_rs.scalar())

        query = """
            SELECT cus.customer_id, cus.first_name, cus.last_name, cus.email, cus.phone, cus.date_created, cus.customer_group_name, scr.rep_name
            FROM customers cus
            JOIN salesforce_customer_rep scr ON cus.customer_id = scr.customer_id
            WHERE NOT EXISTS (
                SELECT 1
                FROM orders o
                WHERE o.customer_id = cus.customer_id
            )
            AND cus.customer_group_name IN ('Tier Pro Pricing Group (No Sale Coupons No CC Agreement Required)', 'Tiered 1 Prices Group  (Make Sure Customer Has No Coupon CODE)', 'Tiered 2 Prices Group  (Make Sure Customer Has No Coupon CODE)', 'Value Tier Pricing Group')
        """
        
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            if sort_array[0] in ["date_created"]:
                query += f" ORDER BY cus.{sort_array[0]} {sort_direction}"

        if limit and page:
            query = query + " OFFSET " + str(offset) + " LIMIT " + str(limit)
        
        rs = conn.execute(text(query))

        results = []

        for row in rs:
          # Populate row_data with the converted timestamp
          row_data = {
              'customer_id': row[0],
              'first_name': row[1],
              'last_name': row[2],
              'email': row[3],
              'phone': row[4],
              'date_created': convert_to_timestamp(row[5]),  # Converted timestamp here
              'customer_group_name': row[6],
              'rep_name': row[7]
          }
          
          # Append the result to the list
          results.append(row_data)

        if page and limit:
            paginated_rows, current_page, total_pages, total_items = paginate_data(
                total_count, results, page, limit
            )

            data = new_utils.calculate_pagination(
                paginated_rows, current_page, limit, total_items
            )
        else:
            data = results
    finally:
        conn.close()

    return data

def get_recently_inacctive_customers(store, payload):
  data = None
  conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
  try:
    limit = int(payload.get('limit', 50))
    page = int(payload.get('page', 1))
    offset = (page - 1) * limit
    sort_by = payload.get('sort_by', '').strip()
    sort_array = sort_by.split("/") if sort_by != '' else []
    
    count_query = """
      SELECT COUNT(*) AS total_records
      FROM customers cus
      LEFT JOIN (
        SELECT
          customer_id,
          MAX(order_created_date_time) AS order_created_date_time,
          COUNT(order_id) AS total_orders
          FROM orders
          GROUP BY customer_id
      ) o ON cus.customer_id = o.customer_id
      WHERE
          o.order_created_date_time < CURRENT_DATE - INTERVAL '180 days'
          AND cus.customer_group_name IN ('Tier Pro Pricing Group (No Sale Coupons No CC Agreement Required)', 'Tiered 1 Prices Group  (Make Sure Customer Has No Coupon CODE)', 'Tiered 2 Prices Group  (Make Sure Customer Has No Coupon CODE)', 'Value Tier Pricing Group')
    """
    
    count_rs = conn.execute(text(count_query))
    total_count = int(count_rs.scalar())

    query = """
      SELECT
        o.order_created_date_time,
        COALESCE(o.total_orders, 0) AS total_orders,
        cus.customer_id,
        cus.first_name,
        cus.last_name,
        cus.email,
        cus.phone,
        cus.customer_group_name,
        cus.date_created,
        scr.rep_name
      FROM
        customers cus
      LEFT JOIN (
        SELECT
          customer_id,
          MAX(order_created_date_time) AS order_created_date_time,
          COUNT(order_id) AS total_orders
          FROM orders
          GROUP BY customer_id
      ) o ON cus.customer_id = o.customer_id
      LEFT JOIN salesforce_customer_rep scr ON cus.customer_id = scr.customer_id
      WHERE
          o.order_created_date_time < CURRENT_DATE - INTERVAL '180 days'
          AND cus.customer_group_name IN ('Tier Pro Pricing Group (No Sale Coupons No CC Agreement Required)', 'Tiered 1 Prices Group  (Make Sure Customer Has No Coupon CODE)', 'Tiered 2 Prices Group  (Make Sure Customer Has No Coupon CODE)', 'Value Tier Pricing Group')
    """

    if len(sort_array):
      sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
      if sort_array[0] in ["order_created_date_time", "total_orders"]:                
          query += f" ORDER BY {sort_array[0]} {sort_direction}"
    
    if limit and page:
      query = query + " OFFSET " + \
                str(offset) + " LIMIT " + str(limit)
    
    rs = conn.execute(text(query))

    results = []

    for row in rs:
      # Populate row_data with the converted timestamp
      row_data = {
          'order_created_date_time': convert_to_timestamp(row[0]),  # Converted timestamp here
          'total_orders': row[1],
          'customer_id': row[2],
          'first_name': row[3],
          'last_name': row[4],
          'email': row[5],
          'phone': row[6],
          'customer_group_name': row[7],
          'customer_date_created': convert_to_timestamp(row[8]),  # Converted timestamp here
          'rep_name': row[9]
      }
      
      # Append the result to the list
      results.append(row_data)

    if page and limit:
      paginated_rows, current_page, total_pages, total_items = paginate_data(total_count, results, page, limit)
      
      data = new_utils.calculate_pagination(paginated_rows, current_page, limit, total_items)      
    else:
      data = results
  finally:
    conn.close()

  return data
  

def get_active_customer_with_no_recent_orders(store, payload):
  data = None
  conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
  try:
    limit = int(payload.get('limit', 50))
    page = int(payload.get('page', 1))
    offset = (page - 1) * limit
    sort_by = payload.get('sort_by', '').strip()
    sort_array = sort_by.split("/") if sort_by != '' else []

    count_query = """
      SELECT
        COUNT(*) AS total_customers
        FROM (
        SELECT
            COUNT(DISTINCT cls.login_timestamp) AS total_sessions,
            COUNT(DISTINCT cls.ip_address) AS total_unique_ips,
            c.customer_id,
            c.first_name,
            c.last_name,
            c.email,
            c.phone,
            c.customer_group_name,
            MAX(o2.last_order_date) AS last_order_date_from_orders,
            COALESCE(MAX(o2.total_order_count), 0) AS total_order_count
        FROM customers c
        INNER JOIN customer_login_sessions cls ON c.customer_id = cls.customer_id
        LEFT JOIN (
            SELECT
                customer_id,
                MAX(order_created_date_time) AS last_order_date,
                COUNT(DISTINCT order_id) AS total_order_count
            FROM orders
            GROUP BY customer_id
        ) o2 ON c.customer_id = o2.customer_id
        LEFT JOIN orders o ON c.customer_id = o.customer_id AND o.order_created_date_time >= CURRENT_DATE - INTERVAL '30 DAYS'
        WHERE o.order_id IS NULL
        GROUP BY c.customer_id, c.first_name, c.last_name, c.email, c.phone, c.customer_group_name
        ORDER BY total_order_count ASC
    ) AS subquery
    """

    count_rs = conn.execute(text(count_query))
    total_count = int(count_rs.scalar())

    query = """
      SELECT
          COUNT(DISTINCT cls.login_timestamp) AS total_sessions,
          COUNT(DISTINCT cls.ip_address) AS total_unique_ips,
          COALESCE(MAX(o2.total_orders), 0) AS total_orders,
          MAX(o2.last_order_date) AS last_order_date_from_orders,
          c.customer_id,
          c.first_name,
          c.last_name,
          c.email,
          c.phone,
          c.customer_group_name,
          scr.rep_name
      FROM customers c
      INNER JOIN customer_login_sessions cls ON c.customer_id = cls.customer_id
      LEFT JOIN (
          SELECT
              customer_id,
              MAX(order_created_date_time) AS last_order_date,
              COUNT(DISTINCT order_id) AS total_orders
          FROM orders
          GROUP BY customer_id
      ) o2 ON c.customer_id = o2.customer_id
      LEFT JOIN orders o ON c.customer_id = o.customer_id AND o.order_created_date_time >= CURRENT_DATE - INTERVAL '30 DAYS'
      LEFT JOIN salesforce_customer_rep scr ON c.customer_id = scr.customer_id
      WHERE o.order_id IS NULL
      GROUP BY c.customer_id, c.first_name, c.last_name, c.email, c.phone, c.customer_group_name, scr.rep_name
    """
    if len(sort_array):
      sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
      if sort_array[0] in ["last_order_date_from_orders", "total_orders"]:                
          query += f" ORDER BY {sort_array[0]} {sort_direction}"
    
    if page and limit:
      query = query + " OFFSET " + \
                str(offset) + " LIMIT " + str(limit)
    
    rs = conn.execute(text(query))
    # columns = ['total_sessions', 'total_unique_ips', 'total_orders', 'last_order_date', 'customer_id', 'first_name', 'last_name', 'email', 'phone', 'customer_group_name', 'rep_name']

    # results = conver_to_json(rs, columns)
    results = []

    for row in rs:
      row_data = {
          'total_sessions': row[0],
          'total_unique_ips': row[1],
          'total_orders': row[2],
          'last_order_date': convert_to_timestamp(row[3]),
          'customer_id': row[4],
          'first_name': row[5],
          'last_name': row[6],
          'email': row[7],
          'phone': row[8],
          'customer_group_name': row[9],
          'rep_name': row[10]
      }
      
      # Append the result to the list
      results.append(row_data)
       

    if page and limit:
      paginated_rows, current_page, total_pages, total_items = paginate_data(total_count, results, page, limit)

      data = new_utils.calculate_pagination(paginated_rows, current_page, limit, total_items)
    else:
      data = results
  finally:
    conn.close()
  return data

def get_customer_session_details(store, customer_id):
  result = None
  conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
  try:
    query = """
      SELECT
        COUNT(DISTINCT cls.login_timestamp) AS total_sessions,
        COUNT(DISTINCT cls.ip_address) AS total_unique_ips,
        COALESCE(SUM(CASE WHEN o.order_created_date_time >= CURRENT_DATE - INTERVAL '30 DAYS' THEN 1 ELSE 0 END), 0) AS total_orders,
        MAX(CASE WHEN o.order_created_date_time < CURRENT_DATE - INTERVAL '30 DAYS' THEN o.order_created_date_time END) AS last_order_date,
        c.customer_id,
        c.first_name,
        c.last_name,
        c.email,
        c.phone,
        c.company,
        c.date_created,
        c.store_credit_in_USD
      FROM customers c
      INNER JOIN customer_login_sessions cls ON c.customer_id = cls.customer_id
      LEFT JOIN orders o ON c.customer_id = o.customer_id
      WHERE c.customer_id = {customer_id}
      GROUP BY c.customer_id
    """.format(customer_id=customer_id)

    rs = conn.execute(text(query))
    columns = ['total_sessions', 'total_unique_ips', 'total_orders', 'last_order_date', 'customer_id', 'first_name', 'last_name', 'email', 'phone', 'company', 'customer_date_created', 'store_credit_in_USD']
    result = {}

    for row in rs:
      result_dict = dict(zip(columns, row))
      for key, value in result_dict.items():
        if isinstance(value, datetime):
          result_dict[key] = json_serial(value)
      result = result_dict    
  finally:
    conn.close()
  return result
  
def get_customer_ip_info(store, customer_id, payload):
  data = None
  conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
  try:
    limit = int(payload.get('limit', 50))
    page = int(payload.get('page', 1))
    offset = (page - 1) * limit

    query = """
        SELECT ip_address, city, login_timestamp
        FROM customer_login_sessions
        WHERE customer_id = {customer_id}
      """.format(customer_id=customer_id)

    count_query = """
        SELECT COUNT(*) AS total_records
        FROM customer_login_sessions
        WHERE customer_id = {customer_id}
      """.format(customer_id=customer_id)
      
    query = query + " OFFSET " + \
            str(offset) + " LIMIT " + str(limit)
      
    count_rs = conn.execute(text(count_query))
    total_count = int(count_rs.scalar())

    rs = conn.execute(text(query))
    columns = ['ip_address', 'city', 'login_timestamp']
      
    results = conver_to_json(rs, columns)

    paginated_rows, current_page, total_pages, total_items = paginate_data(total_count, results, page, limit)

    data = new_utils.calculate_pagination(paginated_rows, current_page, limit, total_items)
  finally:
    conn.close()
  
  return data

def get_order_by_id(store_id, order_id):
        response = {'status': 400}
        conn = new_pgdb.get_connection(store_id, read_only=is_pgdb_read_only_enabled())
        try:
                      
            query = text(f"""SELECT order_status_id, total_including_tax  FROM orders WHERE order_id= :order_id""")
            
            res = conn.execute(query.params(order_id=order_id)).fetchall()                             
            row_data = {}
            if res:
                for row in res:
                    row_data = {
                        'status': get_order_status_name(row[0]),
                        'total': row[1]
                    }
                  
                response['data'] = row_data
                response['status'] = 200

        except Exception as e:
            error_message = str(e)
            response['status'] = 422
            response['message'] = error_message
            raise e
        finally:
            if conn:
                conn.close()       
        return response