from sqlalchemy import text
import pg_db
from mongo_db import user_db
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from datetime import datetime
from projects.default_columns import _fetch_default_coulmns, update_sort_id_default_columns
from projects.project_columns import update_column_attributes, get_sort_ids
import time
import logging
import traceback
from utils.common import convert_to_timestamp

logger = logging.getLogger()


def _fetch_default_column_detail(conn, id):
    query = text (f"""SELECT * from {pg_db.project_columns} where id = :id;""")

    query = query.params(id=id)
    result = conn.execute(query)
    data = []
    for row in result.fetchall():
        row_data = {
            'id': row[0],
            'project_id': row[1],
            'name': row[2],
            'sort_id': row[3],
            'is_first_column': row[4],
            'is_last_column': row[5],
            'description': row[6],
            'is_archived': row[7],
            'created_by': row[8],
            'updated_by': row[9],
            'created_at': convert_to_timestamp(row[10]),
            'updated_at': convert_to_timestamp(row[11])
        }
        data.append(row_data)
    return data

def get_default_column_detail(id):
    response = {
        "status": 400 
    }
    conn = pg_db.get_connection()
    try:
        data = _fetch_default_column_detail(conn, id)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response


def _update_default_column_detail(conn, update_fields, id, username):
    try: 
        set_clauses = []
        params = {'updated_by': username, 'id': id}
        
        # Generate SET clause for each field in update_fields
        for field, value in update_fields.items():
            set_clause = f"{field} = :{field}"
            set_clauses.append(set_clause)
            params[field] = value
        
        set_clause = ", ".join(set_clauses)
    
        query = text(
            f"""UPDATE {pg_db.project_columns}
                SET 
                    {set_clause},
                    updated_by = :updated_by,
                    updated_at = CURRENT_TIMESTAMP
                WHERE 
                    id = :id;"""
        )

        conn.execute(query, params)    
        return True
    except Exception as e:
        logger.error(traceback.format_exc())
        return False

    
def patch_default_columns(name, description, username, id):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        existing_field = _fetch_default_column_detail(conn, id)
        if not existing_field:
            response['status'] = 404
            response['message'] = "column not found."
            return response
       
        existing_field = existing_field[0]

        update_fields = {}
        
        if name is not None:
            update_fields['name'] = name
        if description is not None:
            update_fields['description'] = description
    
        # If both name and group_values are provided, update both fields
        if name is not None and description is not None:
            update_fields = {'name': name, 'description': description}
       
        
        data = _update_default_column_detail(conn, update_fields, id, username)
        if data:
            response['status'] = 200
            response['message'] = "Data updated successfully."
        else:
            response['status'] = 409
            response['message'] = "name: Data updation failed, column already exists"
    except IntegrityError as e:
        logger.error(traceback.format_exc())
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 409
            response['message'] = "name: This project already exists."
        else:
            response['status'] = 500
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response
    


def delete_default_column(id):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()

    try:
        delete_query = text(
            f"""DELETE FROM {pg_db.project_columns}
                WHERE id = :id
                AND project_id IS NULL
            """
        )
        delete_query = delete_query.params(id=id)
        delete_result = conn.execute(delete_query)
        conn.commit()

        if delete_result.rowcount > 0:
            default_columns = _fetch_default_coulmns(conn)
            default_column_ids = [column['id'] for column in default_columns]

            res = update_sort_id_default_columns(default_column_ids)
            
            if res['status'] == 200:
                min_sort_id, max_sort_id, is_first_column, sort_id, success = get_sort_ids(conn, None)
                if success:
                    update_column_attributes(conn, min_sort_id, max_sort_id, sort_id, is_first_column, None, call_from_func=True)
            
            response['status'] = 200
            response['message'] = "Data deleted successfully."
        else:
            response['status'] = 400
            response['message'] = "Data deletion failed, This columns field is used in multiple projects or id does not exists"
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response