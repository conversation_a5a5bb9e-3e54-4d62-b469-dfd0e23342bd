import calendar
from sqlalchemy import text
from new_mongodb import get_store_db_client_for_store_id
import new_pgdb
import new_utils
import utils
from utils import common
from datetime import datetime, timedelta, timezone
from dateutil.relativedelta import relativedelta
import datetime
from decimal import Decimal
import copy
import logging
import traceback
from utils.common import calculatePaginationData, convert_to_timestamp, get_month_array_for_meta, paginate_data_postgres
from collections import defaultdict
from new_pgdb.analytics_db import AnalyticsDB
from appconfig import is_pgdb_read_only_enabled
from utils.common import convert_to_timestamp
from orders.view_orders import orders_list
from dateutil import parser

logger = logging.getLogger()


def get_sold_product_analytics_summary(store, product_id, start_date, end_date):
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    summary_data_list = []
    summary = {}

    try:
        query = text(
            f"""SELECT
                sc.type,
                COUNT(DISTINCT apt.customer_id) AS total_customers,
                COUNT(apt.order_id) AS order_count,
                SUM(apt.quantity) AS sold_quantity,
                SUM(apt.total) AS earning,
                AVG(apt.total) AS average_price
            FROM
                {AnalyticsDB.get_products_trend_table()} apt
            LEFT JOIN salesforce_customer_rep sc ON apt.customer_id = sc.customer_id
            WHERE
                apt.product_id = :product_id
                AND apt.order_date_time BETWEEN :start_date AND :end_date
            GROUP BY
                sc.type
            ORDER BY
                sold_quantity DESC;"""
        )
        data = conn.execute(query.params(product_id=product_id, start_date=start_date, end_date=end_date))
        results = data.fetchall()

        # Calculate totals for all customer types
        total_customers_all = sum(result[1] for result in results)
        order_count_all = sum(result[2] for result in results)
        sold_quantity_all = sum(result[3] or 0 for result in results)
        earning_all = sum(result[4] or 0 for result in results)
        
        for result in results:
            total_customers = f"{int(result[1]):,}" if result[1] else 0
            order_count = f"{int(result[2]):,}" if result[2] else 0
            sold_quantity = f"{int(result[3] or 0):,}" if result[3] else 0
            earning = f"{float(result[4] or 0.0):,.2f}" if result[4] else "0.00"

            # Calculate percentages
            total_customers_percent = (result[1] / total_customers_all) * 100
            order_count_percent = (result[2] / order_count_all) * 100
            sold_quantity_percent = (result[3] / sold_quantity_all) * 100
            earning_percent = (result[4] / earning_all) * 100

            summary_data = {
                'customer_type': result[0] if result[0] else 'Other',
                'total_customers': total_customers,
                'order_count': order_count,
                'sold_quantity': sold_quantity,
                'earning': earning,
                # 'average_price': average_price,
                'total_customers_percent': round(total_customers_percent, 2),
                'order_count_percent': round(order_count_percent, 2),
                'sold_quantity_percent': float(round(sold_quantity_percent, 2)) if isinstance(sold_quantity_percent, Decimal) else round(sold_quantity_percent, 2),
                'earning_percent': float(round(earning_percent, 2)) if isinstance(earning_percent, Decimal) else round(earning_percent, 2)
            }
            summary_data_list.append(summary_data)

        summary_data_totals = {
            'customer_type': 'Total',
            'total_customers': f"{int(total_customers_all):,}" if total_customers_all else 0,
            'order_count': f"{int(order_count_all):,}" if order_count_all else 0,
            'sold_quantity': f"{int(sold_quantity_all):,}" if sold_quantity_all else 0,
            'earning': f"{float(earning_all):,.2f}" if earning_all else "0.00"
        }
        summary['data'] = summary_data_list
        summary['totals'] = summary_data_totals

    finally:
        if conn:
            conn.close()
    return summary

        
def _fetch_variants_range_data(conn, product_id, start_date, end_date, state_join_clause_query, 
                                     state_condition, state):

    state_select = ", sh.state" if state else ""
    state_group_by = ", sh.state" if state else ""

    sum_query = text(
    f"""SELECT
            av.variant_id,
            SUM(av.quantity) AS total_quantity
            {state_select}
        FROM
            {AnalyticsDB.get_variants_trend_table()} av
            {state_join_clause_query}
        WHERE
            av.product_id = :product_id
            AND av.order_date_time BETWEEN :start_date AND :end_date
            {state_condition} 
        GROUP BY
            av.variant_id
            {state_group_by}
            ;
        """
    )
    if state:
        sum_query = sum_query.params(product_id=product_id, start_date=start_date, end_date=end_date, 
                                        state=state)
    else:
        sum_query = sum_query.params(product_id=product_id, start_date=start_date, end_date=end_date)

    sum_data = conn.execute(sum_query)
    sum_results = sum_data.fetchall()
    variants = {}
    total_quantity = 0
    fetched_state = None
    for row in sum_results:
        variants[row[0]] = row[1]
        total_quantity += row[1]
        if state:  # Fetch state only if it was included in the query
            fetched_state = row[2]
    
    variant_count = len(variants)

    result = {
        "data": variants,
        "total_quantity": total_quantity,
        "variant_count": variant_count,
    }

    if state:
        result["state"] = fetched_state

    return result

def _fetch_variants_monthly_data(conn, product_id, start_date, end_date, state_join_clause_query, 
                                       state_condition, state):
    state_select = ", sh.state" if state else ""
    state_group_by = ", sh.state" if state else ""
    query = text (
            f"""SELECT
                av.variant_id,
                av.order_month,
                SUM(av.quantity) AS total_quantity_monthly
                {state_select}
            FROM
                {AnalyticsDB.get_variants_trend_table()} av
                {state_join_clause_query}
            WHERE
                av.product_id = :product_id
                AND av.order_date_time BETWEEN :start_date AND :end_date
                {state_condition} 
            GROUP BY
                av.variant_id,
                av.order_month
                {state_group_by}
            ;"""
        )
        
    if state:
        query = query.params(product_id=product_id, start_date=start_date, end_date=end_date, state=state)
    else:
        query = query.params(product_id=product_id, start_date=start_date, end_date=end_date)

    data = conn.execute(query)
    result = data.fetchall()
    variants = {}
    for row in result:
        variant = variants.get(row[0], {})
        variant[row[1]] = row[2]

        variants[row[0]] = variant
        if state:
            variants['state'] = row[3]
    return variants

def _fetch_product_variants(conn, product_id):
    query = text (
            f"select variants_id, variants_sku, variant_options from variants where product_id = {product_id};"
        )

    data = conn.execute(query)
    result = data.fetchall()
    variants = {}
    for row in result:
        if row[2] and row[2] != "":
            variants[row[0]] = {
                "sku": row[1],
                "option": row[2]
            }
        else:
             variants[row[0]] = {
                "sku": row[1],
                "option": row[1]
            }
    return variants

def _get_months_in_range(start_date, end_date):
    range_months = []
    current_month = start_date
    while current_month <= end_date:
        range_months.append(current_month.month)
        current_month += relativedelta(months=1)

    return range_months

def get_sku_report(store, product_id, start_date, end_date, sort="desc", state=None):
    result_data = {}
    
    # Check if 'state' is provided and not None
    state_join_clause_query = ""
    state_condition = ""

    if state:
        state_condition = "AND sh.state = :state"
        state_join_clause_query = f"JOIN {new_pgdb.DBTables.order_shipping_addresses_table} sh ON av.order_id = sh.order_id"

    current_date = datetime.datetime.now()
    month_start_date = current_date - datetime.timedelta(days=current_date.day - 1) - datetime.timedelta(weeks=12)

    month_start_date = month_start_date.replace(day=1).date()
    month_end_date = current_date.date()
    _months = _get_months_in_range(copy.deepcopy(month_start_date), copy.deepcopy(month_end_date))
    _months.reverse()

    tasks = []
    tasks.append({
        "func": _fetch_product_variants,
        "args": [product_id]
    })
    tasks.append({
        "func": _fetch_variants_range_data,
        "args": [product_id, start_date, end_date, state_join_clause_query, state_condition, state]
    })
    tasks.append({
        "func": _fetch_variants_monthly_data,
        "args": [product_id, str(month_start_date), str(month_end_date), state_join_clause_query, 
                 state_condition, state]
    })

    variants, range_data, monthly_data = utils.concurrent_db_execution(store, tasks)

    sku_report_data = []
    sku_distribution_data = []
    month_keys = {}

    total_range_quantity = range_data.get('total_quantity', 0)
    variant_range_data = range_data.get('data')
    total_variant_count = range_data.get('variant_count')

    MONTH_NAMES = common.get_month_names()
    current_month = datetime.date.today().month   
    current_year = datetime.date.today().year
    meta_info = {'month_' + str(i): '' for i in range(1, 5)}

    for i in range(1, 5):
        month_key = 'month_' + str(i)
        month_value = current_month - (i - 1)
        year_value = current_year if month_value > 0 else current_year - 1
        month_value = month_value if month_value > 0 else month_value + 12
        month_name = MONTH_NAMES[month_value]
        meta_info[month_key] = f"{month_name} {year_value}"
       

    if not variant_range_data:
        result_data['message'] = "No variant data available for the specified product ID."
        result_data['meta'] = {'month_rows':[meta_info]}
        return result_data
    
    for variant_id, variant in variants.items():
        range_quantity = variant_range_data.get(variant_id, 0)
        quantity_percentage = 0
        if total_range_quantity > 0:
            quantity_percentage = round((range_quantity / total_range_quantity) * 100, 2)

        state_abbreviation = common.get_state_abbreviation(range_data.get('state', '')) if range_data.get('state', '') else ''


        sku_data = {
            'variants': variant['option'],
            'variant_id': variant_id,
            'total_quantity_by_range': int(range_quantity),
            'percentage': quantity_percentage
        }
        if state:
            sku_data['state'] = state_abbreviation
            sku_data['state_name'] = range_data.get('state', '')

        variant_monthly_data = monthly_data.get(variant_id,{})
        for idx, m in enumerate(_months):
            month_str = MONTH_NAMES.get(m)
            month_key = f"month_{idx+1}"
            month_quantity = variant_monthly_data.get(month_str, 0)
            sku_data[month_key] = month_quantity
            month_keys[month_key] = month_str
        
        sku_report_data.append(sku_data)
    
    if sort.lower() == 'asc':
        sku_report_data = sorted(sku_report_data, key=lambda x: x['percentage'])
    else:
        sku_report_data = sorted(sku_report_data, key=lambda x: x['percentage'], reverse=True)

    sku_distribution = {
        'total_quantity_sku_distribution': total_range_quantity,
        'total_variant_count': total_variant_count
    }
    sku_distribution_data.append(sku_distribution)

    result_data['data'] = sku_report_data
    result_data['sku_distribution'] = sku_distribution_data
    result_data['meta'] = {
        'month_rows': [meta_info]
    }
    return result_data


def _fetch_product_sell_by_state(conn, product_id, start_date, end_date):
    query = text(
        f"""SELECT
                sh.state,
                scr.type,
                SUM(pt.quantity) AS total_quantity,
                COUNT(pt.customer_id) AS customers
            FROM
                {AnalyticsDB.get_products_trend_table()} pt
            JOIN 
                {new_pgdb.DBTables.order_shipping_addresses_table} sh ON pt.order_id = sh.order_id
            JOIN 
                {new_pgdb.DBTables.salesforce_customer_rep} scr ON pt.customer_id = scr.customer_id
            WHERE
                pt.product_id = :product_id
                AND pt.order_date_time BETWEEN :start_date AND :end_date
            GROUP BY
                sh.state,
                scr.type;"""
    )

    result = conn.execute(query.params(product_id=product_id, start_date=start_date, end_date=end_date))
    data = {}
    total_quantity = 0
    for row in result.fetchall():
        state = str(row[0])
        customer_type = str(row[1])
        quantity = int(row[2])
        customers = int(row[3])

        if state not in data:
            data[state] = {
                "state": state,
                "quantity": 0,
                "retailer": 0,
                "distributor": 0,
                "chain_store_retailer": 0
            }
        data[state]["quantity"] += quantity
        total_quantity += quantity
        
        if customer_type.lower() == "retailer":
            data[state]["retailer"] += customers
        elif customer_type.lower() == "distributor":
            data[state]["distributor"] += customers
        elif customer_type.lower() == "chain store retailer":
            data[state]["chain_store_retailer"] += customers

    return {
        'data': data,
        'total_quantity': total_quantity
    }

    
def _fetch_monthly_product_sell_by_state(conn, product_id, start_date, end_date):
    query = text (
        f"""SELECT
                sh.state,
                pt.order_month,
                SUM(pt.quantity) AS total_quantity_monthly
            FROM
                {AnalyticsDB.get_products_trend_table()} pt
            JOIN
                {new_pgdb.DBTables.order_shipping_addresses_table} sh ON pt.order_id = sh.order_id
            WHERE
                pt.product_id = :product_id
                AND pt.order_date_time BETWEEN :start_date AND :end_date
            GROUP BY
                sh.state,
                pt.order_month;"""
        )
    result = conn.execute(query.params(product_id=product_id, start_date=start_date, end_date=end_date)) 
    data = {}
    for row in result.fetchall():
        state_data = data.get(str(row[0]), {})
        state_data[str(row[1])] = int(row[2])
        data[str(row[0])] = state_data

    return data

def get_variant_sales_by_states(store, product_id, start_date, end_date, sort="desc"):
    result_data = {}
    start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d")
    end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d")
    #offset = (int(page) - 1) * int(limit)

    current_date = datetime.datetime.now()
    month_start_date = current_date - datetime.timedelta(days=current_date.day - 1) - datetime.timedelta(weeks=12)

    month_start_date = month_start_date.replace(day=1).date()
    month_end_date = current_date.date()

    _months = _get_months_in_range(copy.deepcopy(month_start_date), copy.deepcopy(month_end_date))
    _months.reverse()

    tasks = []
    tasks.append({
        "func": _fetch_product_sell_by_state,
        "args": [product_id, start_date, end_date]
    })
    tasks.append({
        "func": _fetch_monthly_product_sell_by_state,
        "args": [product_id, month_start_date, month_end_date]
    })

    range_data, monthly_data = utils.concurrent_db_execution(store, tasks)

    MONTH_NAMES = common.get_month_names()

    range_state_data = range_data['data']
    total_range_quantity = range_data['total_quantity']

    states = set().union(range_state_data, monthly_data.keys())

    current_month = datetime.date.today().month   
    current_year = datetime.date.today().year
    meta_info = {'month_' + str(i): '' for i in range(1, 5)}

    for i in range(1, 5):
        month_key = 'month_' + str(i)
        month_value = current_month - (i - 1)
        year_value = current_year if month_value > 0 else current_year - 1
        month_value = month_value if month_value > 0 else month_value + 12
        month_name = MONTH_NAMES[month_value]
        meta_info[month_key] = f"{month_name} {year_value}"

    if not states:
        result_data['message'] = "No variant data available for the specified product ID."
        result_data['meta'] = {'month_rows':[meta_info]}
        return result_data

    state_data = []
    month_keys = {}
    
    for state in states:
        range_data = range_state_data.get(state, None)
        percentage = 0
        distributor = 0
        retailer = 0
        chain_store_retailer = 0
        range_quantity = 0
        if range_data:
            range_quantity = range_data.get('quantity', 0)
            distributor = range_data.get('distributor', 0)
            retailer = range_data.get('retailer', 0)
            chain_store_retailer = range_data.get('chain_store_retailer', 0)
            if total_range_quantity > 0:
                percentage = round((range_quantity / total_range_quantity) * 100, 2)
        
        _state_data = {
            "state": state,
            "total_quantity_by_range": range_quantity,
            "distributor": distributor,
            "retailer": retailer,
            "chain_store_retailer": chain_store_retailer,
            "percentage": percentage
        }

        monthly_state_data = monthly_data.get(state, {})
        for idx, m in enumerate(_months):
            month_str = MONTH_NAMES.get(m)
            month_key = f"month_{idx+1}"
            _state_data[month_key] = monthly_state_data.get(str(m), 0)
            month_keys[month_key] = month_str

        state_data.append(_state_data)

    if sort.lower() == 'asc':
            state_data = sorted(state_data, key=lambda x: x['percentage'])
    else:
        state_data = sorted(state_data, key=lambda x: x['percentage'], reverse=True)

    result_data['data'] = state_data
    result_data['meta'] = {
        "month_rows": [meta_info]
    }

    return result_data

def _fetch_variant_sales_by_customers(conn, product_id, start_date, end_date, state_join_clause_query, 
                                            state_condition, state=None):
    query = text(
        f"""SELECT
                ap.customer_id,
                MAX(c.company) AS company,
                MAX(c.customer_group_name) AS customer_group,
                CAST(SUM(ap.quantity) AS INTEGER) AS total_quantity,
                sh.state,
                scr.type
            FROM
                {AnalyticsDB.get_products_trend_table()} ap
            JOIN
                {new_pgdb.DBTables.customers_table} c ON c.customer_id = ap.customer_id
            JOIN
                {new_pgdb.DBTables.salesforce_customer_rep} scr ON scr.customer_id = ap.customer_id
                {state_join_clause_query}
            WHERE
                ap.product_id = :product_id
                AND ap.order_date_time BETWEEN :start_date AND :end_date
                {state_condition}
            GROUP BY
                ap.customer_id,
                sh.state,
                scr.type
            ORDER BY
                total_quantity DESC;"""
        )
    
    if state:
        query = query.params(product_id=product_id, start_date=start_date, end_date=end_date, state=state)
    else:
        query = query.params(product_id=product_id, start_date=start_date, end_date=end_date)

    result = conn.execute(query)
    data = {}
    total_quantity = 0
    for row in result.fetchall():
        data[int(row[0])] = {
            'company': str(row[1]),
            'customer_group': str(row[2]),
            'quantity': int(row[3]),
            'state': row[4],
            'customer_type': row[5]
        }
        total_quantity += int(row[3])
    return {
        'data': data,
        'total_quantity': total_quantity
    }

def _fetch_variant_monthly_sales_by_customers(conn, product_id, start_date, end_date, state_join_clause_query, 
                                            state_condition,  state=None):
    query = text(
                f"""SELECT
                        ap.customer_id,
                        ap.order_month,
                        SUM(ap.quantity) AS total_quantity_monthly,
                        sh.state,
                        scr.type
                    FROM
                        {AnalyticsDB.get_products_trend_table()} ap
                    JOIN
                        {new_pgdb.DBTables.salesforce_customer_rep} scr ON scr.customer_id = ap.customer_id
                        {state_join_clause_query}
                    WHERE
                        ap.product_id = :product_id
                        AND ap.order_date_time BETWEEN :start_date AND :end_date
                        {state_condition}
                    GROUP BY
                        ap.customer_id,
                        ap.order_month,
                        sh.state,
                        scr.type
                   ;"""
            )

    if state:
        query = query.params(product_id=product_id, start_date=start_date, end_date=end_date, state=state)
    else:  
        query = query.params(product_id=product_id, start_date=start_date, end_date=end_date)

    result = conn.execute(query)
    data = {}
    for row in result.fetchall():
        customer_id = int(row[0])
        customer = data.get(customer_id, {})
        customer[str(row[1])] = int(row[2])
        customer['state'] = row[3]
        customer['customer_type'] = row[4]
        data[customer_id] = customer
    return data

def get_variant_sales_by_customers(store, product_id, start_date, end_date, sort="desc", state=None):
    result_data = {}
    state_condition = ""

    # Check if 'state' is provided and not None
    if state:
        state_condition = "AND sh.state = :state"

    state_join_clause_query = f"JOIN {new_pgdb.DBTables.order_shipping_addresses_table} sh ON ap.order_id = sh.order_id" #if state else ""

    current_date = datetime.datetime.now()
    month_start_date = current_date - datetime.timedelta(days=current_date.day - 1) - datetime.timedelta(weeks=12)

    month_start_date = month_start_date.replace(day=1).date()
    month_end_date = current_date.date()

    _months = _get_months_in_range(copy.deepcopy(month_start_date), copy.deepcopy(month_end_date))
    _months.reverse()

    tasks = []
    tasks.append({
        "func": _fetch_variant_sales_by_customers,
        "args": [product_id, start_date, end_date,state_join_clause_query, state_condition, state]
    })
    tasks.append({
        "func": _fetch_variant_monthly_sales_by_customers,
        "args": [product_id, month_start_date,month_end_date, state_join_clause_query, state_condition, state]
    })

    range_data, monthly_data = utils.concurrent_db_execution(store, tasks)

    total_quantity_sold = range_data.get("total_quantity", 0)

    MONTH_NAMES = common.get_month_names()

    month_keys = {}
    customers_data = []
    range_sell_data = range_data.get('data', {})

    current_month = datetime.date.today().month   
    current_year = datetime.date.today().year
    meta_info = {'month_' + str(i): '' for i in range(1, 5)}

    for i in range(1, 5):
        month_key = 'month_' + str(i)
        month_value = current_month - (i - 1)
        year_value = current_year if month_value > 0 else current_year - 1
        month_value = month_value if month_value > 0 else month_value + 12
        month_name = MONTH_NAMES[month_value]
        meta_info[month_key] = f"{month_name} {year_value}"

    if not range_sell_data:
        result_data['message'] = "No variant data available for the specified product ID."
        result_data['meta'] = {'month_rows':[meta_info]}
        return result_data

    for customer_id, customer_data in range_sell_data.items():
        range_quantity = customer_data.get("quantity", 0)
        quantity_percentage = round((range_quantity / total_quantity_sold) * 100, 2)

        state_abbreviation = common.get_state_abbreviation(customer_data['state'])
        customer = {
            'company': customer_data['company'],
            'customer_group': customer_data['customer_group'],
            'state': state_abbreviation,
            'customer_type': customer_data['customer_type'],
            'total_quantity_by_range': range_quantity,
            'percentage': quantity_percentage
        }

        monthly_sell_data = monthly_data.get(customer_id, {})
        for idx, m in enumerate(_months):
            month_str = MONTH_NAMES.get(m)
            month_key = f"month_{idx+1}"
            customer[month_key] = monthly_sell_data.get(str(m), 0)
            month_keys[month_key] = month_str

        customers_data.append(customer)

    if sort.lower() == 'asc':
            customers_data = sorted(customers_data, key=lambda x: x['percentage'])
    else:
        customers_data = sorted(customers_data, key=lambda x: x['percentage'], reverse=True)

    result_data['data'] = customers_data
    result_data['meta'] = {
        "month_rows": [meta_info]
    }

    return result_data

def _fetch_product_quantity_by_price(conn, product_id, start_date, end_date):
    query = text(
                f"""SELECT
                        price_ex_tax, 
                        sum(quantity) as quantity
                    FROM
                        {AnalyticsDB.get_variants_trend_table()}
                    WHERE
                        product_id = :product_id AND
                        order_date_time BETWEEN :start_date AND :end_date
                    GROUP BY
                        price_ex_tax
                   ;"""
            )
 
    query = query.params(product_id=product_id, start_date=start_date, end_date=end_date)

    result = conn.execute(query)
    data = {}
    total_quantity = 0
    for row in result.fetchall():
        price = row[0]
        data[price] = row[1]
        total_quantity += row[1]
        
    return {
        "total_quantity": total_quantity,
        "prices": data
    }

def _fetch_product_quantity_by_price_month(conn, product_id, start_date, end_date):
    query = text(
                f"""SELECT
                        price_ex_tax, 
                        order_month, 
                        sum(quantity) as quantity
                    FROM
                        {AnalyticsDB.get_variants_trend_table()}
                    WHERE
                        product_id = :product_id AND
                        order_date_time BETWEEN :start_date AND :end_date
                    GROUP BY
                        price_ex_tax,
                        order_month
                   ;"""
            )
 
    query = query.params(product_id=product_id, start_date=start_date, end_date=end_date)

    result = conn.execute(query)
    data = {}
    for row in result.fetchall():
        price = row[0]
        month = row[1]
        quantity = row[2]

        monthly_data = data.get(price, {})
        monthly_data[month] = quantity

        data[price] = monthly_data
        
    return data

def get_product_price_report(store, product_id, start_date, end_date, sort="desc"):
    result_data = {}
    
    current_date = datetime.datetime.now()
    month_start_date = current_date - datetime.timedelta(days=current_date.day - 1) - datetime.timedelta(weeks=12)

    month_start_date = month_start_date.replace(day=1).date()
    month_end_date = current_date.date()

    _months = _get_months_in_range(copy.deepcopy(month_start_date), copy.deepcopy(month_end_date))
    _months.reverse()

    tasks = []
    tasks.append({
        "func": _fetch_product_quantity_by_price,
        "args": [product_id, start_date, end_date]
    })
    tasks.append({
        "func": _fetch_product_quantity_by_price_month,
        "args": [product_id, month_start_date, month_end_date]
    })

    range_data, monthly_data = utils.concurrent_db_execution(store, tasks)
    total_quantity = range_data['total_quantity']
    range_sell_data = range_data.get('prices', {})

    MONTH_NAMES = common.get_month_names()
    current_month = datetime.date.today().month   
    current_year = datetime.date.today().year
    meta_info = {'month_' + str(i): '' for i in range(1, 5)}

    for i in range(1, 5):
        month_key = 'month_' + str(i)
        month_value = current_month - (i - 1)
        year_value = current_year if month_value > 0 else current_year - 1
        month_value = month_value if month_value > 0 else month_value + 12
        month_name = MONTH_NAMES[month_value]
        meta_info[month_key] = f"{month_name} {year_value}"

    price_data_list = []
    month_keys = {}
    for price, _data in monthly_data.items():
        range_quantity = range_sell_data.get(price, 0)
        quantity_percentage = 0
        if total_quantity > 0:
            quantity_percentage = round((range_quantity / total_quantity) * 100, 2)

        price_data = {
            "price": price,
            "quantity": range_quantity,
            "percentage": quantity_percentage
        }

        for idx, m in enumerate(_months):
            month_str = MONTH_NAMES.get(m)
            month_key = f"month_{idx+1}"
            price_data[month_key] = _data.get(month_str, 0)
            month_keys[month_key] = month_str

        price_data_list.append(price_data)

    if sort.lower() == 'asc':
        price_data_list = sorted(price_data_list, key=lambda x: x['percentage'])
    else:
        price_data_list = sorted(price_data_list, key=lambda x: x['percentage'], reverse=True)

    result_data['data'] = price_data_list
    result_data['meta'] = {
        "month_rows": [meta_info]
    }

    return result_data

def get_product_sold_states(store, product_id=None, start_date=None, end_date=None):
    result_data = {}
    states = []
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        if not product_id and start_date and end_date:
            return {"message": "Invalid product_id, start_date or end_date"}
        query = f"SELECT DISTINCT sh.state FROM {new_pgdb.DBTables.order_shipping_addresses_table} sh"
        if product_id:
            query = query + f""", {AnalyticsDB.get_products_trend_table()} ap 
                WHERE ap.product_id = :product_id AND ap.order_id = sh.order_id
            """
            if start_date and end_date:
                query = query + f" AND ap.order_date_time BETWEEN :start_date AND :end_date"
                query = text(query).params(product_id=product_id, start_date=start_date, end_date=end_date)
            else:
                query = text(query).params(product_id=product_id)
        else:
            query = text(query)
        
        data = conn.execute(query)
        results = data.fetchall()
        states = [result[0] for result in results if result[0] is not None and result[0] != '']

        total_states_count = len(states)
        result_data = {
                'states' : states,
                'state_count' : total_states_count
            }
    finally:
        if conn:
            conn.close()

    return result_data

def get_product_performance_data(store, product_id, start_date, end_date):
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    result_data = {}
    try:
        end_date = datetime.datetime.now()
        start_date = end_date - timedelta(days=120)  # 4 months = 30 days/month * 4 months

        chart_query = text(
            f"""SELECT 
                    COUNT(product_id) AS total_products_sold,
                    EXTRACT(WEEK FROM order_date_time) AS week_number
                FROM 
                    {AnalyticsDB.get_products_trend_table()}
                WHERE
                    product_id = :product_id
                    AND order_date_time BETWEEN :start_date AND :end_date
                GROUP BY
                    week_number;"""
        )

        total_variants_data = conn.execute(chart_query.params(product_id=product_id, start_date=start_date, end_date=end_date))
        chart_results = total_variants_data.fetchall()

        sequential_week_number = 1
        for result in chart_results:
            products_sold = result[0]
            result_data[f'week_{sequential_week_number}'] = products_sold
            sequential_week_number += 1

        for i in range(sequential_week_number, 19):
            result_data[f'week_{i}'] = 0

    finally:
        if conn:
            conn.close()

    return [result_data]
        

def _fetch_product_available_quantity(conn, start_date, end_date, page, limit, sort_array):
    offset = (page - 1) * limit
    
    base_query =f"""SELECT
                        apt.product_name,
                        SUM(apt.quantity) AS quantity, 
                        p.inventory_level,
                        apt.product_id
                    FROM
                        {AnalyticsDB.get_products_trend_table()} apt
                    JOIN 
                        {new_pgdb.DBTables.products} p ON apt.product_id = p.product_id
                    WHERE
                        apt.order_date_time BETWEEN :start_date AND :end_date
                    GROUP BY
                        apt.product_name, 
                        p.inventory_level,
                        apt.product_id
                   """
    if len(sort_array):
        sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
        if sort_array[0] in ["quantity"]:                
            base_query += f" ORDER BY sum(apt.{sort_array[0]}) {sort_direction}"                
        
    base_query += " LIMIT :limit OFFSET :offset"

    query = text(base_query).params(start_date=start_date, end_date=end_date, limit=limit, offset=offset)

    result = conn.execute(query)
    data = {}
    for row in result.fetchall():
        product_name = row[0]
        quantity = int(row[1])
        data[product_name] = {
            "inventory_level": row[2],
            "quantity": quantity,
            "product_id": row[3]
        }
    
    # Query to count total records
    count_query = text(
                f"""SELECT
                        COUNT(DISTINCT apt.product_name)
                    FROM
                        {AnalyticsDB.get_products_trend_table()} apt
                    JOIN 
                        {new_pgdb.DBTables.products} p ON apt.product_id = p.product_id
                    WHERE
                        apt.order_date_time BETWEEN :start_date AND :end_date
                   ;"""
            )
 
    count_query = count_query.params(start_date=start_date, end_date=end_date)
    total_count = conn.execute(count_query).scalar()
    
    return {
        "total_count": total_count,
        "inventory_levels": data
    }
    
def _fetch_product_quantity_by_month(conn, start_date, end_date):
    query = text(
                f"""SELECT
                        apt.product_name,
						apt.order_month, 
                        sum(apt.quantity) as quantity
                    FROM
                        {AnalyticsDB.get_products_trend_table()} apt
                    JOIN 
                        {new_pgdb.DBTables.products} p ON apt.product_id = p.product_id
                    WHERE
                        apt.order_date_time BETWEEN :start_date AND :end_date
                    GROUP BY
                        apt.product_name,
						apt.order_month
                   ;"""
            )
 
    query = query.params(start_date=start_date, end_date=end_date)

    result = conn.execute(query)
    data = {}
    for row in result.fetchall():
        product_name = row[0]
        month = int(row[1])
        quantity = int(row[2])
        
        monthly_data = data.get(product_name, {})
        monthly_data[month] = quantity
        data[product_name] = monthly_data
    return data

def get_aggregated_product_report(store , start_date, end_date, page, limit, sort_array):

    
    current_date = datetime.datetime.now()
    month_start_date = current_date - datetime.timedelta(days=current_date.day - 1) - datetime.timedelta(weeks=12)

    month_start_date = month_start_date.replace(day=1).date()
    month_end_date = current_date.date()

    _months = _get_months_in_range(copy.deepcopy(month_start_date), copy.deepcopy(month_end_date))
    _months.reverse()

    tasks = []
    tasks.append({
        "func": _fetch_product_available_quantity,
        "args": [start_date, end_date, page, limit, sort_array]
    })
    tasks.append({
        "func": _fetch_product_quantity_by_month,
        "args": [month_start_date, month_end_date]
    })

    range_data, monthly_data = utils.concurrent_db_execution(store, tasks)
    total_count = range_data.get('total_count', 0)
    range_sell_data = range_data.get('inventory_levels', {})
    MONTH_NAMES = common.get_month_names()
    current_month = datetime.date.today().month   
    meta_info = {'month_' + str(i): '' for i in range(1, 5)}

    for i in range(1, 5):
        month_key = 'month_' + str(i)
        month_value = current_month - (i - 1)
        month_value = month_value if month_value > 0 else month_value + 12
        month_name = MONTH_NAMES[month_value]
        meta_info[month_key] = month_name

    product_report_list = []
    month_keys = {}
    for product_name, _data in range_sell_data.items():
        inventory = _data['inventory_level']
        sold_quantity = _data['quantity']
        product_id = _data['product_id']
       
        product_data = {
            "product_id": product_id,
            "product_name": product_name,
            "available_quantity": inventory,
            "sold_quantity": sold_quantity,
            "total": 0
        }

        monthly_sell_data = monthly_data.get(product_name, {})
        for idx, m in enumerate(_months):
            month_str = MONTH_NAMES.get(m)
            month_key = f"month_{idx+1}"
            month_quantity = monthly_sell_data.get(m, 0)
            product_data[month_key] = month_quantity
            product_data["total"] += month_quantity
            month_keys[month_key] = month_str

        product_report_list.append(product_data)

    
    data = calculatePaginationData(product_report_list, page, limit, total_count)

    data['meta'] = {
        **data.get('meta', {}),
        "month_rows": [month_keys]
    }

    return data

# def _fetch_revenue_report(store, product_id):
#     response = {
#         'status': 400,
#         'meta': "Something went wrong. Please try again later.",
#     }
#     conn = new_pgdb.get_connection(store['id'])
#     try:
#         month_names = {}
#         month_map = {}
#         current_date = datetime.datetime.now()
#         m_start_date = current_date.replace(day=1)
#         start_date = (m_start_date - relativedelta(months=6)).date()
#         end_date = current_date.date()
        
#         for i in range(7):
#             month_name = (m_start_date - relativedelta(months=i)).strftime('%b')
#             month_names[f'month_{7 - i}'] = month_name  # Assign to 'month_1', 'month_2', etc.

#         for i in range(7):
#             month_number = (m_start_date - relativedelta(months=i)).strftime('%m').lstrip('0')
#             month_map[month_number] = f'month_{7 - i}'

#         result = {
#             "product_name": "",
#             "data": {},
#             "meta": {
#                 "month_rows": [month_names]
#             }
#         }

#         query = """
#                 select max(pa.product_name) as product_name, cu.type, pa.order_month, sum(pa.quantity) as total_quantity, avg(pa.price) as avg_price, sum(pa.total) as revenue
#                 from analytics_products_trend pa, salesforce_customer_rep cu
#                 where pa.product_id={product_id} 
#                 and pa.customer_id = cu.customer_id 
#                 and order_date_time >= '{start_date}' 
#                 and order_date_time <= '{end_date}' 
#                 group by pa.order_month, cu.type order by pa.order_month;""".format(product_id=product_id, start_date=start_date, end_date=end_date)
        
#         query=query.replace('\n', '')
#         rs = conn.execute(text(query))
#         if rs:
#             d = {}
#             total_revenue = 0
#             for row in rs:
#                 result["product_name"] = str(row[0])
#                 index = str(row[2])
#                 total_revenue += row[5]
#                 d.setdefault(row[1], {})[index] = row[5]
#             data = []
#             for k, v in d.items():
#                 test = {}
#                 test['type'] = k
#                 type_total = 0
#                 for k1, v1 in v.items():
#                     test[month_map[k1]] = v1
#                     type_total += v1
#                 test['total'] = type_total
#                 test['percentage'] = round((type_total / total_revenue) * 100, 2)
#                 data.append(test)
            
#             result['data'] = data
#             response['data'] = result
#             response['status'] = 200
#         else:
#             response['data'] = {}
#             response['status'] = 200

#     except Exception as e:
#         response['data'] = str(e)
#         response['status'] = 500
#     finally:
#         if conn:
#             conn.close()

#     return response

def _fetch_quantity_sold_report(store, product_id):
    response = {
        'status': 400,
        'meta': "Something went wrong. Please try again later.",
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        month_names = {}
        month_map = {}
        current_date = datetime.datetime.now()
        m_start_date = current_date.replace(day=1)
        start_date = (m_start_date - relativedelta(months=6)).date()
        end_date = current_date.date()
        
        for i in range(7):
            month_name = (m_start_date - relativedelta(months=i)).strftime('%b')
            month_names[f'month_{7 - i}'] = month_name  # Assign to 'month_1', 'month_2', etc.

        for i in range(7):
            month_number = (m_start_date - relativedelta(months=i)).strftime('%m').lstrip('0')
            month_map[month_number] = f'month_{7 - i}'

        result = {
            "product_name": "",
            "data": {},
            "meta": {
                "month_rows": [month_names]
            }
        }

        query = """
                select max(pa.product_name) as product_name, cu.type, pa.order_month, sum(pa.quantity) as total_quantity
                from analytics_products_trend pa, salesforce_customer_rep cu
                where pa.product_id={product_id} 
                and pa.customer_id = cu.customer_id 
                and order_date_time >= '{start_date}' 
                and order_date_time <= '{end_date}' 
                group by pa.order_month, cu.type order by pa.order_month;""".format(product_id=product_id, start_date=start_date, end_date=end_date)
        
        query=query.replace('\n', '')
        rs = conn.execute(text(query))
        if rs:
            d = {}
            total_qty_sold = 0
            for row in rs:
                result["product_name"] = str(row[0])
                index = str(row[2])
                total_qty_sold += int(row[3])
                d.setdefault(row[1], {})[index] = int(row[3])
            data = []
            for k, v in d.items():
                test = {}
                test['type'] = k
                type_total = 0
                for k1, v1 in v.items():
                    test[month_map[k1]] = v1
                    type_total += v1
                test['total'] = type_total
                test['percentage'] = round((type_total / total_qty_sold) * 100, 2)
                data.append(test)
            
            result['data'] = data
            response['data'] = result
            response['status'] = 200
        else:
            response['data'] = {}
            response['status'] = 200

    except Exception as e:
        response['data'] = str(e)
        response['status'] = 500
    finally:
        if conn:
            conn.close()

    return response

# def _fetch_new_customers_table(store, product_id):
#     response = {
#         'status': 400,
#         'meta': "Something went wrong. Please try again later.",
#     }
#     conn = new_pgdb.get_connection(store['id'])
#     try:
#         month_names = {}
#         month_map = {}
#         current_date = datetime.datetime.now()
#         m_start_date = current_date.replace(day=1)
#         start_date = (m_start_date - relativedelta(months=6)).date()
#         end_date = current_date.date()
        
#         for i in range(7):
#             month_name = (m_start_date - relativedelta(months=i)).strftime('%b')
#             month_names[f'month_{7 - i}'] = month_name  # Assign to 'month_1', 'month_2', etc.

#         for i in range(7):
#             month_number = (m_start_date - relativedelta(months=i)).strftime('%m').lstrip('0')
#             month_map[month_number] = f'month_{7 - i}'

#         result = {
#             "product_name": "",
#             "data": {},
#             "meta": {
#                 "month_rows": [month_names]
#             }
#         }

#         query = """
#                 select max(pa.product_name) as product_name, cu.type, pa.order_month, count(distinct pa.customer_id) as new_customers
#                 from analytics_products_trend pa, salesforce_customer_rep cu
#                 where pa.product_id={product_id}
#                 and pa.customer_id = cu.customer_id
#                 and order_date_time >= '{start_date}' 
#                 and order_date_time <= '{end_date}' 
#                 group by pa.order_month, cu.type
#                 order by pa.order_month;""".format(product_id=product_id, start_date=start_date, end_date=end_date)
        
#         query=query.replace('\n', '')
#         rs = conn.execute(text(query))
#         if rs:
#             d = {}
#             total_customer_count = 0
#             for row in rs:
#                 result["product_name"] = str(row[0])
#                 index = str(row[2])
#                 total_customer_count += int(row[3])
#                 d.setdefault(row[1], {})[index] = int(row[3])
#             data = []
    
#             for k, v in d.items():
#                 test = {}
#                 test['type'] = k
#                 type_total = 0
#                 for k1, v1 in v.items():
#                     test[month_map[k1]] = v1
#                     type_total += v1
#                 test['total'] = type_total
#                 test['percentage'] = round((type_total / total_customer_count) * 100, 2)
#                 data.append(test)
            
#             result['data'] = data
#             response['data'] = result
#             response['status'] = 200
#         else:
#             response['data'] = {}
#             response['status'] = 200

#     except Exception as e:
#         response['data'] = str(e)
#         response['status'] = 500
#     finally:
#         if conn:
#             conn.close()

#     return response

def _format_month_year(month, year):
    month_name = calendar.month_abbr[int(month)]
    year_short = str(year)[-2:]
    return f"{month_name}_{year_short}"

def _fetch_reorder_rate_report(store, product_id):
    response = {
        'status': 400,
        'meta': "Something went wrong. Please try again later.",
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        current_date = datetime.datetime.now()
        m_start_date = current_date.replace(day=1)
        start_date = (m_start_date - relativedelta(months=11)).date()
        end_date = current_date.date()
        
        result = {
            "product_name": "",
            "data": {},
            "total_percentage": {}
        }

        query = f"""
                select max(pa.product_name) as product_name, cu.type, count(pa.customer_id) AS total_customers, count(distinct pa.customer_id) AS distinct_customers, 
                ((count(pa.customer_id)-count(distinct pa.customer_id))/count(pa.customer_id)*100) reorder_rate,  pa.order_month, pa.order_year
                from {AnalyticsDB.get_products_trend_table()} pa, salesforce_customer_rep cu
                where pa.product_id={product_id} 
                and pa.customer_id = cu.customer_id 
                and order_date_time >= '{start_date}' 
                and order_date_time <= '{end_date}' 
                group by pa.order_month, pa.order_year, cu.type order by pa.order_month;"""
        
        query=query.replace('\n', '')
        rs = conn.execute(text(query))
        if rs:
            d = defaultdict(int)
            data = defaultdict(dict)
            for row in rs:
                result["product_name"] = str(row[0])
                index = _format_month_year(row[5], row[6])
                d[index] += (int(row[2]) - int(row[3]))
                data[row[1]][index] = { "percentage": (int(row[2]) - int(row[3]))}

            max_value = max(d.values())
            percentage_data = {}
            for key, value in d.items():
                percentage = round((value / max_value) * 100, 2) if max_value > 0 else 0.0  # Convert to percentage
                percentage_data[key] = percentage
            for k, v in data.items():
                for k1, v1 in v.items():
                    v1['percentage'] = round((v1['percentage'] / d[k1]) * 100, 2) if d[k1] > 0 else 0.0
        
            result['data'] = data
            result['total_percentage'] = percentage_data
            response['data'] = result
            response['status'] = 200
        else:
            response['data'] = {}
            response['status'] = 200

    except Exception as e:
        response['message'] = str(e)
        response['status'] = 500
    finally:
        if conn:
            conn.close()

    return response

def _fetch_new_customers_report(store, product_id):
    response = {
        'status': 400,
        'meta': "Something went wrong. Please try again later.",
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        current_date = datetime.datetime.now()
        m_start_date = current_date.replace(day=1)
        start_date = (m_start_date - relativedelta(months=11)).date()
        end_date = current_date.date()

        result = {
            "product_name": "",
            "data": {},
            "total_percentage": {}
        }

        query = f"""
                select max(pa.product_name) as product_name, cu.type, pa.order_month, count(distinct pa.customer_id) as new_customers, pa.order_year
                from {AnalyticsDB.get_products_trend_table()} pa, salesforce_customer_rep cu
                where pa.product_id={product_id}
                and pa.customer_id = cu.customer_id
                and order_date_time >= '{start_date}' 
                and order_date_time <= '{end_date}' 
                group by pa.order_month, cu.type, pa.order_year
                order by pa.order_month;"""
        
        query=query.replace('\n', '')
        rs = conn.execute(text(query))
        if rs:
            d = defaultdict(int)
            data = defaultdict(dict)
            for row in rs:
                result["product_name"] = str(row[0])
                index = _format_month_year(row[2], row[4])
                d[index] += int(row[3])
                data[row[1]][index] = { "percentage": int(row[3])}
                
            max_value = max(d.values())
            percentage_data = {}
            for key, value in d.items():
                percentage = round((value / max_value) * 100, 2) if max_value > 0 else 0.0  # Convert to percentage
                percentage_data[key] = percentage or 0.0
            for k, v in data.items():
                for k1, v1 in v.items():
                    v1['percentage'] = round((v1['percentage'] / d[k1]) * 100, 2) if d[k1] > 0 else 0.0
            
            result['data'] = data
            result['total_percentage'] = percentage_data
            response['data'] = result
            response['status'] = 200
        else:
            response['data'] = {}
            response['status'] = 200

    except Exception as e:
        response['message'] = str(e)
        response['status'] = 500
    finally:
        if conn:
            conn.close()

    return response

def get_supplier_aggregated_product_report(store , start_date, end_date, page, limit, sort_array, brands, user):

    current_date = datetime.datetime.now()
    month_start_date = current_date - datetime.timedelta(days=current_date.day - 1) - datetime.timedelta(weeks=12)

    month_start_date = month_start_date.replace(day=1).date()
    month_end_date = current_date.date()

    _months = _get_months_in_range(copy.deepcopy(month_start_date), copy.deepcopy(month_end_date))
    _months.reverse()

    tasks = []
    tasks.append({
        "func": _fetch_supplier_product_available_quantity,
        "args": [start_date, end_date, page, limit, sort_array, brands, user]
    })
    tasks.append({
        "func": _fetch_supplier_product_quantity_by_month,
        "args": [month_start_date, month_end_date]
    })

    range_data, monthly_data = utils.concurrent_db_execution(store, tasks)
    total_count = range_data.get('total_count', 0)
    range_sell_data = range_data.get('inventory_levels', {})
    MONTH_NAMES = common.get_month_names()
    current_month = datetime.date.today().month   
    meta_info = {'month_' + str(i): '' for i in range(1, 5)}

    for i in range(1, 5):
        month_key = 'month_' + str(i)
        month_value = current_month - (i - 1)
        month_value = month_value if month_value > 0 else month_value + 12
        month_name = MONTH_NAMES[month_value]
        meta_info[month_key] = month_name

    product_report_list = []
    month_keys = {}
    for product_name, _data in range_sell_data.items():
        inventory = _data['inventory_level']
        sold_quantity = _data['quantity']
        product_id = _data['product_id']
       
        product_data = {
            "product_id": product_id,
            "product_name": product_name,
            "available_quantity": inventory,
            "sold_quantity": sold_quantity,
            "total": 0
        }

        monthly_sell_data = monthly_data.get(product_name, {})
        for idx, m in enumerate(_months):
            month_str = MONTH_NAMES.get(m)
            month_key = f"month_{idx+1}"
            month_quantity = monthly_sell_data.get(m, 0)
            product_data[month_key] = month_quantity
            product_data["total"] += month_quantity
            month_keys[month_key] = month_str

        product_report_list.append(product_data)

    
    data = calculatePaginationData(product_report_list, page, limit, total_count)

    data['meta'] = {
        **data.get('meta', {}),
        "month_rows": [month_keys]
    }

    return data

def _fetch_supplier_product_available_quantity(conn, start_date, end_date, page, limit, sort_array, brands=None, user=None):
    offset = (page - 1) * limit
    filters = "apt.order_date_time BETWEEN :start_date AND :end_date"

    if brands:
        filters += f""" AND apt.product_id IN (SELECT DISTINCT product_id FROM {new_pgdb.DBTables.supplier_app_user_supplier_mapping} WHERE brand_id IN :brands)"""
    if user:
        filters += f""" AND apt.product_id IN (SELECT DISTINCT product_id FROM {new_pgdb.DBTables.supplier_app_user_supplier_mapping} WHERE email_id = :user)"""

    base_query = f"""
        SELECT
            apt.product_name,
            SUM(apt.quantity) AS quantity,
            p.inventory_level,
            apt.product_id
        FROM
            {AnalyticsDB.get_products_trend_table()} apt
        JOIN 
            {new_pgdb.DBTables.products} p ON apt.product_id = p.product_id
        WHERE {filters}
        GROUP BY
            apt.product_name, p.inventory_level, apt.product_id
    """

    if sort_array and len(sort_array):
        sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
        if sort_array[0] in ["quantity"]:
            base_query += f" ORDER BY sum(apt.{sort_array[0]}) {sort_direction}"

    base_query += " LIMIT :limit OFFSET :offset"

    params = {"start_date": start_date, "end_date": end_date, "limit": limit, "offset": offset}
    if brands:
        params["brands"] = tuple(brands.split(','))
    if user:
        params["user"] = user.strip()

    query = text(base_query).params(**params)
    result = conn.execute(query)

    data = {}
    for row in result.fetchall():
        product_name = row[0]
        quantity = int(row[1])
        data[product_name] = {
            "inventory_level": row[2],
            "quantity": quantity,
            "product_id": row[3]
        }

    # Count query
    count_query = f"""
        SELECT COUNT(DISTINCT apt.product_name)
        FROM {AnalyticsDB.get_products_trend_table()} apt
        JOIN {new_pgdb.DBTables.products} p ON apt.product_id = p.product_id
        WHERE {filters}
    """
    count_result = conn.execute(text(count_query).params(**params))
    total_count = count_result.scalar()

    return {"total_count": total_count, "inventory_levels": data}
    
def _fetch_supplier_product_quantity_by_month(conn, start_date, end_date):
    query = text(
                f"""SELECT
                        apt.product_name,
						apt.order_month, 
                        sum(apt.quantity) as quantity
                    FROM
                        {AnalyticsDB.get_products_trend_table()} apt
                    JOIN 
                        {new_pgdb.DBTables.products} p ON apt.product_id = p.product_id
                    WHERE
                        apt.order_date_time BETWEEN :start_date AND :end_date
                    GROUP BY
                        apt.product_name,
						apt.order_month
                   ;"""
            )
 
    query = query.params(start_date=start_date, end_date=end_date)

    result = conn.execute(query)
    data = {}
    for row in result.fetchall():
        product_name = row[0]
        month = int(row[1])
        quantity = int(row[2])
        
        monthly_data = data.get(product_name, {})
        monthly_data[month] = quantity
        data[product_name] = monthly_data
    return data

def get_product_price_report_order_details(store_id, product_id, start_date, end_date, price):
    response = {
        'status': 400,
    }
    conn = new_pgdb.get_connection(store_id, read_only=is_pgdb_read_only_enabled())
    try:
        query = """WITH order_quantities AS (
                        SELECT
                            order_id,
                            SUM(quantity) AS total_quantity
                        FROM analytics.analytics_variants_trend
                        WHERE product_id = :product_id AND price_ex_tax = :price
                        AND order_date_time BETWEEN :start_date AND :end_date 
                        GROUP BY order_id
                    )
                    SELECT
                        o.order_created_date_time,
                        oq.order_id,
                        c.first_name,
                        c.last_name,
                        scr.rep_name,
                        scr.rep_email,
                        o.order_status_id,
                        oq.total_quantity,
                        o.total_including_tax
                    FROM order_quantities oq
                    JOIN orders o ON oq.order_id = o.order_id
                    LEFT JOIN customers c ON o.customer_id = c.customer_id
                    LEFT JOIN salesforce_customer_rep scr ON o.customer_id = scr.customer_id
                    ORDER BY o.order_created_date_time"""
        
        result = conn.execute(text(query), {'product_id': int(product_id), 'price': float(price), 'start_date': start_date, 'end_date': end_date})
        data = result.fetchall()
        order_details = []
        for row in data:
            order_details.append({
                'order_created_date_time': convert_to_timestamp(row[0]),
                'order_id': row[1],
                'customer_name': row[2] + ' ' + row[3],
                'sales_rep_name': row[4],
                'sales_rep_email': row[5],
                'order_status_id': row[6],
                'order_status': orders_list.get_order_status_name(row[6]) if row[6] else None,
                'total_quantity': row[7],
                'total_including_tax': row[8]
            })
        response['data'] = order_details
        response['status'] = 200
    except Exception as e:
        logger.error(traceback.format_exc())
    finally:
        conn.close()
    
    return response

def get_replenishment_report(store, query_params):
    response = {
        "status": 400,
        "data": {}
    }
    parent_sku = query_params.get('parent_sku')
    if not parent_sku:
        response['message'] = "Missing required parameter: parent_sku"
        return response

    sale_history_months = int(query_params.get('sale_history_months', 6))
    sort_by = query_params.get('sort_by', '').strip()
    sort_array = sort_by.split("/") if sort_by != '' else []

    order_clause = "ORDER BY rp.sku ASC"

    if len(sort_array):
        sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
        nulls_order = "NULLS FIRST" if sort_direction == "ASC" else "NULLS LAST"
        field = sort_array[0]

        if field in ['reserved_quantity', 'rtv_quantity', 'zero_quantity']:
            order_clause = f"ORDER BY {field} {sort_direction} {nulls_order}"
        else:
            order_clause = f"ORDER BY {field} {sort_direction} {nulls_order}"

    column_query = ''
    if sale_history_months:
        for i in range(int(sale_history_months), 0, -1):
            index = i + 1
            column_query += f', rp.month_{index}'

    month_names, day_difference = get_month_array_for_meta(sale_history_months)
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        formatted_data = []
        final_result = {
            'data': [],
            'meta': {}
        }

        parent_query = f"""
            SELECT 
                rp.product_title, rp.parent_sku, rp.quantity_incoming, rp.quantity_available, rp.month_1,  
                rp.out_of_stock_date AS last_out_of_stock_date, rp.out_of_stock_end_date AS last_received_date, rp.occurrence_range,
                rp.total_rtv_quantity, rp.total_return_quantity AS total_zero_quantity
                {column_query}
            FROM {AnalyticsDB.get_replenishment_products_table()} rp
            WHERE rp.parent_sku = :parent_sku
            GROUP BY rp.product_title, rp.parent_sku, rp.quantity_incoming, rp.quantity_available, rp.month_1, rp.out_of_stock_date, rp.out_of_stock_end_date, rp.occurrence_range, rp.total_rtv_quantity, rp.total_return_quantity
                {column_query}
        """
        parent_result = conn.execute(text(parent_query), {"parent_sku": parent_sku})
        parent_data = parent_result.fetchone()

        # --- Child variant data query ---
        base_query = f"""
            SELECT rp.product_title, rp.sku as sku, rp.parent_sku, rp.quantity_incoming as quantity_incoming, rp.quantity_available as quantity_available, 
                   MAX(v.out_of_stock_date) as out_of_stock_date, rp.month_1, SUM(rrv.quantity) as reserved_quantity,
                   rp.out_of_stock_end_date AS last_received_date, rp.total_out_of_stock_days, 
                   rp.restocked_inventory_level, rp.out_of_stock_start_date, 
                   MAX(rp.rtv_quantity) AS rtv_quantity, MAX(rp.order_quantity) AS zero_quantity
                   {column_query}
            FROM {AnalyticsDB.get_replenishment_variants_table()} rp 
            LEFT JOIN po_reorders po ON rp.sku = po.sku
            LEFT JOIN variants v ON rp.sku = v.variants_sku
            LEFT JOIN replenishment_reserved_variants rrv ON rp.variant_id = rrv.variant_id
            WHERE rp.parent_sku = :parent_sku
            GROUP BY rp.product_title, rp.sku, rp.parent_sku, rp.quantity_incoming, rp.quantity_available, 
                     rp.month_1, rp.out_of_stock_end_date, rp.total_out_of_stock_days, 
                     rp.restocked_inventory_level, rp.out_of_stock_start_date
                     {column_query}
            {order_clause}
        """

        query_result = conn.execute(text(base_query), {"parent_sku": parent_sku})
        query_result = list(query_result)

        # --- If parent record found ---
        if parent_data:
            obj = {}
            obj["product_title"] = parent_data[0]
            obj["parent_sku"] = parent_data[1]
            obj["quantity_incoming"] = int(parent_data[2])
            obj["quantity_available"] = int(parent_data[3])
            obj["month_1"] = int(parent_data[4]) if parent_data[4] is not None else 'NA'
            obj["last_out_of_stock_date"] = convert_to_timestamp(parent_data[5])
            obj["last_received_date"] = convert_to_timestamp(parent_data[6])
            obj["occurrence_range"] = parent_data[7]
            obj["total_rtv_quantity"] = parent_data[8]
            obj["total_zero_quantity"] = parent_data[9]
            obj["is_parent"] = True

            # --- SUM reserved_quantity from child query ---
            total_reserved_quantity = sum(
                int(data[7]) if data[7] is not None else 0 for data in query_result
            )
            obj["reserved_quantity"] = total_reserved_quantity

            count = 1
            for i in range(int(sale_history_months), 0, -1):
                month_index = i + 1
                key = f"month_{month_index}"
                value_key = 9 + count
                obj[key] = int(parent_data[value_key]) if parent_data[value_key] is not None else 'NA'
                count += 1

            formatted_data.append(obj)

        # --- Process each child variant row ---
        if query_result:
            for data in query_result:
                obj = {}
                obj["product_title"] = data[0]
                obj["sku"] = data[1] if data[1] else data[2]
                obj["parent_sku"] = data[2]
                obj["quantity_incoming"] = int(data[3])
                obj["quantity_available"] = int(data[4])
                obj["out_of_stock_date"] = convert_to_timestamp(data[5]) if data[5] else convert_to_timestamp(data[11])
                obj["month_1"] = int(data[6]) if data[6] is not None else 'NA'
                obj["reserved_quantity"] = int(data[7]) if data[7] is not None else 0
                obj["last_received_date"] = convert_to_timestamp(data[8])
                obj["total_out_of_stock_days"] = int(data[9]) if data[9] is not None else 0
                obj["restocked_inventory_level"] = data[10]
                obj["rtv_quantity"] = int(data[12]) if data[12] is not None else 0
                obj["zero_quantity"] = int(data[13]) if data[13] is not None else 0
                obj["is_parent"] = False

                if data[5] and isinstance(data[5], datetime.datetime):
                    obj["days_out_of_stock"] = (datetime.datetime.now() - data[5]).days
                else:
                    obj["days_out_of_stock"] = 0

                count = 1
                for i in range(int(sale_history_months), 0, -1):
                    month_index = i + 1
                    key = f"month_{month_index}"
                    value_key = 13 + count
                    obj[key] = int(data[value_key]) if data[value_key] is not None else 'NA'
                    count += 1

                formatted_data.append(obj)

        final_result['data'] = formatted_data
        final_result['meta'] = month_names
        response['data'] = final_result
        response['status'] = 200
    finally:
        conn.close()

    return response