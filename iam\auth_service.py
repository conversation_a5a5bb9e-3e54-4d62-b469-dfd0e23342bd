from tokenize import Token
import bcrypt
import base64
import uuid
import datetime
import requests
from urllib.parse import urlparse
from new_mongodb import get_store_by_id, tenant_db, UserKeys, UserStatus, StoreKeys, ClientAppsKeys, store_admin_db
from utils import redis_util, jwt_util
from exceptions.common_exceptions import InvalidInputException, ResourceNotFoundException, InactiveResourceException
from exceptions.auth_exceptions import InvalidCredentialsException
from iam import TokenKeys, Constants
import traceback
import logging
from config import appconfig
import new_pgdb
from sqlalchemy import text
from mongo_db import user_db

logger = logging.getLogger()

def _decode_user_creds(auth_header):
    result = None
    if auth_header:
        token = auth_header.split(" ")
        auth_token = None
        if len(token) == 1:
            auth_token = token[0]
        elif len(token) == 2:
            auth_token = token[1]
        if auth_token:
            payload = base64.b64decode(auth_token).decode("utf-8")
            result = payload.split(":")

    return result

def _validate_credentials(arg1, arg2):
    result = None
    if arg1 and arg2:
        user = tenant_db.fetch_user_by_username(arg1)
        if user:
            if not user[UserKeys.STATUS] == UserStatus.active.value:
                raise InactiveResourceException("User is not active.")

            yoyo = user[UserKeys.YOYO]
            salt = yoyo[UserKeys.YOYO_KEY]
            hashed = bcrypt.hashpw(arg2.encode('utf-8'), salt)
            if hashed == yoyo[UserKeys.YOYO_VALUE]:
                result = user
            else:
                raise InvalidCredentialsException
        else:
            raise ResourceNotFoundException("User doesn't exist.")
    else:
        raise InvalidInputException("Invalid user input.")
    
    return result

def _get_jwt_secret(tenant_id):
    payload = redis_util.get_store_secret(str(tenant_id))
    if not payload:
        payload = {
            TokenKeys.CLIENT_ID: jwt_util.secret_generator(24),
            TokenKeys.SECRET: jwt_util.secret_generator(),
        }
        redis_util.update_store_secret(str(tenant_id), payload)
    return payload

def _generate_access_token(tenant_id, username):
    secret_payload = _get_jwt_secret(tenant_id)
    client_id = secret_payload[TokenKeys.CLIENT_ID]
    secret = secret_payload[TokenKeys.SECRET]
    iat = int(datetime.datetime.now(datetime.timezone.utc).timestamp())
    exp = iat + Constants.admin_token_expiry_duration
    payload = dict(iss=client_id, iat=iat, exp=exp, jti=uuid.uuid4().hex, 
                   tenant_id=tenant_id, username=username)
    access_token = jwt_util.encode(payload, secret, algorithm=Constants.jwt_token_algorithm)
    token = {
        TokenKeys.CLIENT_ID: client_id,
        TokenKeys.ACCESS_TOKEN: access_token,
        TokenKeys.EXPIRY: exp
    }
    return token

def _generate_login_response(user):
    is_supplier = False
    username = user[UserKeys.USERNAME]
    tenant_id = user[UserKeys.TENANT_ID]
    token = _generate_access_token(tenant_id = tenant_id, username=username)
    redis_util.add_access_token(username=username, 
                                client_id=token[TokenKeys.CLIENT_ID], 
                                access_token=token[TokenKeys.ACCESS_TOKEN], 
                                expires_at=token[TokenKeys.EXPIRY])
    stores = user[UserKeys.STORES].keys()
    store_info = {}
    for store_id in stores:
        store = get_store_by_id(store_id)
        store_name = store[StoreKeys.NAME].replace(" ", "_").lower()
        store_info[store_id] = {
            StoreKeys.NAME: store[StoreKeys.NAME],
            StoreKeys.ADMIN_URL: store[StoreKeys.ADMIN_URL],
        }
        conn = new_pgdb.get_connection(store['id'])
        # Check if username exists in user_supplier_mapping table
        is_supplier_query = """
            SELECT EXISTS (SELECT 1 FROM user_supplier_mapping WHERE email_id = :username)
        """
        is_supplier = conn.execute(text(is_supplier_query), {'username': username}).fetchone()[0]
        
        # Special case for '<EMAIL>'
        if username == '<EMAIL>':
            is_supplier = False

        dynamic_key = f'is_supplier_{store_name}'
        token[dynamic_key] = is_supplier

        # Check if the username exists in the salesforce_customer_rep table
        is_rep_query = """
            SELECT EXISTS (SELECT 1 FROM salesforce_customer_rep WHERE rep_email = :username)
        """
        is_rep = conn.execute(text(is_rep_query), {'username': username}).fetchone()[0]

        special_usernames = {
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        }

        if username in special_usernames:
            is_rep = False
        
        # Add the rep flag to the token
        token['is_representative'] = is_rep
        conn.close()
        user_db.update_user_last_login(store_id,user[UserKeys.ID])

    role_id = user[UserKeys.ROLE_ID]     
    default_store_id = user[UserKeys.DEFAULT_STORE_ID]
    if default_store_id:
        default_store = user[UserKeys.STORES].get(default_store_id, None)
        if default_store:
            role_id = default_store['role_id']
    
    user_role = None   
    if role_id:
        user_role = store_admin_db.fetch_role_by_id(default_store_id, role_id)
    token[TokenKeys.NAME] = user[UserKeys.NAME]
    token[TokenKeys.USERNAME] = username
    token[TokenKeys.TENANT_ID] = tenant_id
    token[TokenKeys.DEFAULT_STORE] = user[UserKeys.DEFAULT_STORE_ID]
    token[TokenKeys.STORES] = store_info
    token['is_super_admin'] = user_role['is_super_admin'] if user_role else False
    token['is_administrator_access'] = user_role['is_administrator_access'] if user_role else False
    token['is_owner'] = True if role_id in ['66f4f1aacb3b3b19fd774f98', '67f423e0f957f344198b4720', '67a59e3aaa8d8cf1aee24b36'] else False
    token['role_id'] = role_id
    return token

def login(auth_header):
    creds = None 
    try:
        creds = _decode_user_creds(auth_header)
    except Exception as ex:        
        logger.error(traceback.format_exc())
        raise InvalidInputException("Invalid user input.")
    if creds and len(creds) == 2:
        username = creds[0]
        user = _validate_credentials(arg1=username, arg2=creds[1])
        if user:
            return _generate_login_response(user)
        else:
            raise InvalidCredentialsException
    else:
        raise InvalidInputException("Invalid user input.")

def admin_user_login(tenant_id, token):
    if token:
        payload = validate_access_token(tenant_id, token)
        if payload:
            user = tenant_db.fetch_user_by_username(payload['username'])
            if user:
                return _generate_login_response(user)
            else:
                raise InvalidCredentialsException
        else:
            raise InvalidInputException("Invalid user input.")

def logout(username, access_token):
    redis_util.delete_access_token(username, access_token)
    return True
    
def validate_client(client_id, token):
    payload = None
    if client_id and token:
        client_app = tenant_db.fetch_client_app_by_id(client_id=client_id)
        if client_app and client_app[ClientAppsKeys.ACCESS_TOKEN] == token and client_app[ClientAppsKeys.IS_ACTIVE]:
            payload = {
                ClientAppsKeys.USERNAME: client_app[ClientAppsKeys.USERNAME],
                ClientAppsKeys.TENANT_ID: client_app[ClientAppsKeys.TENANT_ID],
                ClientAppsKeys.STORE_ID: client_app[ClientAppsKeys.STORE_ID],
                "client_id": client_id
            }
    return payload

def validate_access_token(tenant_id, access_token):
    parse_token = None
    secret_payload = _get_jwt_secret(tenant_id)
    client_id = None
    client_secret = None
    if secret_payload:
        client_id = secret_payload[TokenKeys.CLIENT_ID]
        client_secret = secret_payload[TokenKeys.SECRET]
        try:
            parse_token = jwt_util.validate_jwt(access_token, client_id, client_secret)
            client_id = redis_util.get_access_token(parse_token[TokenKeys.USERNAME], access_token)
            if not client_id:
                parse_token = None
            # user = None
            # if parse_token and "username" in parse_token:
            #     user = user_db.fetch_user_by_username(parse_token['username'])

            # if appconfig.is_debug_enabled() or (user and user['status'] == 'active'):
            #     return parse_token
            # else:
            #     return False
        except Exception as e:
            logger.error(traceback.format_exc())
    return parse_token

def operation_permission_check(target_url, method, permission):
    if method == 'GET':
        return permission.get("read", False)

    if method == 'POST' or method == 'PUT' or method == 'DELETE' or method == 'PATCH':
        if method == 'POST' and 'customers/login' in target_url:
            return True
        return permission.get("write", False)

def feature_access_check(api_endpoint, method, feature, permission):
    root_endpoint = permission.get("api", None)    
    if root_endpoint and api_endpoint.startswith(root_endpoint):
        root_permission =  permission.get('operations', None)
        
        if not operation_permission_check(api_endpoint, method, root_permission):
            return False
    
        children = permission.get("children", None)
        if children:
            for _sub_feature, _sub_feature_permission in children.items():
                if feature_access_check(api_endpoint, method, _sub_feature, _sub_feature_permission):
                    return True
        return True
    
    return False

def permission_check(store_id, url, method, username):
    user = store_admin_db.fetch_user_by_username(store_id, username)

    if user and user['role_id']:
        role = store_admin_db.fetch_role_by_id(store_id, user['role_id'])

        if not role:
            return False
        
        # when request is from admin.
        is_administrator_access = role.get('is_administrator_access', False)
        if is_administrator_access:
            return True

        # Find the position of the first "/" character after "admin/api"
        _url = urlparse(url)
        api_endpoint = _url.path

        api_base_path = appconfig.get_api_prefix()
        if api_base_path and api_base_path != "":
            api_endpoint = api_endpoint.replace(api_base_path, '')
            
        # Get the substring starting from that position
        permissions = role.get('permissions', {})
        for feature, permission in permissions.items():
            is_accessible = feature_access_check(api_endpoint, method, feature, permission)
            if is_accessible:
                return True

    return False


def google_login(access_token):
    google_creds = tenant_db.fetch_google_creds()
    if google_creds:
        validation_url = google_creds['api']
        google_client_id = google_creds['client_id']
        client_secret = google_creds['client_secret']

        params = {
            'access_token': access_token,
            'client_id': google_client_id,
            'client_secret': client_secret
        }

        response = requests.get(validation_url, params=params)
        valid_response = response.json()

        if response.status_code == 200 and valid_response['aud'] == google_client_id:
            username = valid_response['email']
            user = tenant_db.fetch_user_by_username(username)
            if user:
                return _generate_login_response(user)

    raise InvalidCredentialsException
