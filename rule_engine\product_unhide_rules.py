
import logging
from datetime import datetime
import new_pgdb
from utils.common import paginate_data_postgres, calculatePaginationData, convert_to_timestamp
from pg_db import get_connection
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError
logger = logging.getLogger()

def get_product_unhide_rules(store, body):
    response = {"status": 400,}
    page = int(body.get("page"))
    limit = int(body.get("limit"))
    sort_by = body.get("sort_by")
    search_keyword = body.get("search")
    sort_order = int(body.get("sort_order"))
    filter_by = int(body.get('is_executed'))
    
    with new_pgdb.get_connection(store['id']) as conn:
        try:
            offset = (page - 1) * limit
            sort_order_str = "DESC" if sort_order == -1 else "ASC"            
            order_by_clause = f"ORDER BY {sort_by} {sort_order_str}" if sort_by else ""    
            where_clause = f"WHERE is_executed = true" if filter_by else ""                            
            where_clause += (f" and product_name ILIKE '%{search_keyword}%'" if search_keyword else "")  if where_clause != '' else (f"WHERE product_name ILIKE '%{search_keyword}%'" if search_keyword else "")
            
            total_records_query = f"SELECT COUNT(*) FROM products_unhide_rules {where_clause}"              
            total_records_result = conn.execute(text(total_records_query))
            total_records_result = int(total_records_result.scalar())             
            
            get_data_query = f"SELECT * FROM products_unhide_rules {where_clause} {order_by_clause} LIMIT {limit} OFFSET {offset}"               
            res = conn.execute(text(get_data_query))            
            
            data = []
            for row in res:              
                a = {
                    "product_id": row[0],
                    "product_name": row[1],                  
                    "category": row[2],
                    "is_executed": row[3],
                    # "executed_date": row[4].strftime("%Y-%m-%d %H:%M:%S") if isinstance(row[4], datetime) else str(row[4]),
                    "executed_date": convert_to_timestamp(row[4]),
                    "is_active": row[5],
                    "created_by": row[6],
                    "created_at": convert_to_timestamp(row[7])                   
                }    
                data.append(a)    
                                                     
            paginated_rows, current_page, total_pages, total_items = paginate_data_postgres(
            total_records_result, data, page, limit)            
            pagination_data = calculatePaginationData(
            paginated_rows, current_page, limit, total_items)
            
            response['status'] = 200
            response['data'] = pagination_data
            
            
        except SQLAlchemyError as e:
                error_message = str(e)
                response['status'] = 422
                response['message'] = error_message
    return response
