from services import Service
from bson import ObjectId
from datetime import datetime
from mongo_db import product_db, user_db
from utils.common import parse_json, calculatePaginationData
import os
from werkzeug.utils import secure_filename
from flask import send_file, make_response
import logging
from fields.products import products_list_fields
from utils.common import resize_image

logger = logging.getLogger()

ALLOWED_EXTENSIONS = set(['png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov', 'wmv', 'mkv'])

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def change_file_name(filename):
    rendom = str(round(datetime.utcnow().timestamp()))
    if ('.' in filename):
        ext = filename.rsplit('.', 1)[1].lower()
        fname = rendom + '.' + ext
        return fname

class StoreInfo(Service):
    def get_data(self, cdn_baseurl):
        response = {
            'status': 400,            
        }
        store_info = super().find_all()                 
        store_info = parse_json(self.processList(store_info))
        if store_info:
            if 'content' in store_info:
                content = store_info['content']
                if 'header_info' in content:
                    header_info = content['header_info']
                    if 'header_logo' in header_info:
                        if header_info['header_logo'] != '':
                            header_info['header_logo'] = cdn_baseurl + '/storeinfo' + header_info['header_logo'].replace('/storeinfo/images', '')                   
                    if 'header_pencil_banner' in header_info:
                        if header_info['header_pencil_banner'] != '':
                            header_info['header_pencil_banner'] = cdn_baseurl + '/storeinfo' + header_info['header_pencil_banner'].replace('/storeinfo/images', '')
                    if 'mobile_header_pencil_banner' in header_info:
                        if header_info['mobile_header_pencil_banner'] != '':
                            header_info['mobile_header_pencil_banner'] = cdn_baseurl + '/storeinfo' + header_info['mobile_header_pencil_banner'].replace('/storeinfo/images', '')
                
                if 'age_verification_info' in content:
                     age_verification_info = content['age_verification_info']
                     if 'aging_img' in age_verification_info:
                         if age_verification_info['aging_img'] != '':
                             age_verification_info['aging_img'] = cdn_baseurl + '/storeinfo' + age_verification_info['aging_img'].replace('/storeinfo/images', '')
                             
            response['data'] = store_info
            response['status'] = 200
        else:
            response['message'] = "Data not found"
            response['status'] = 403

        return response
    

    def set_data(self, body):
        response = {
            'status': 400,
            'message': 'Data is not saved successfully'
        }
        created_by = {}
        if 'created_by' in body:
            user = user_db.fetch_user_by_username(body['created_by'])        
            created_by = {
                "user_id": str(user['_id']),
                "user_name": user['name']
            }
        
        content = {
            'header_info': body['header_info'],
            'footer_info': body['footer_info'],
            'age_verification_info': body['age_verification_info']
        }
        # data = {}
        # data['name'] = 'Store Information'
        # data['updated_at'] = int(datetime.utcnow().timestamp())        
        # data['created_by'] = created_by
        # data['content'] = content
        # data['status'] = 'active'

        id = super().update_one({"_id": ObjectId(str(body['id']))}, {"$set":
                                                                         {  
                                                                            "content": content,                                                                                                                                                                                                                                   
                                                                             "created_by": created_by,
                                                                             "updated_at":  int(datetime.utcnow().timestamp())
                                                                         }
                                                                         })
        response['message'] = "Data saved successfully"
        response['status'] = 200
    
        return response
    
    def update_product_listing(self, body):
        response = {
            'status': 400,
            'message': 'Data is not saved successfully'
        }
        check = body['checked']
        productType = body['type'].strip()
        productID = body['product_id']
        product = super().find_one({'type': productType})
        if product:
            finalproducts = product['product_ids']
            finalproduct = []
                    
            if check == True:
                finalproduct=finalproducts
                finalproduct.append(productID)
            else:
                for p_id in finalproducts:
                    if p_id != productID:
                        finalproduct.append(p_id)        

            id = super().update_one({"_id": ObjectId(str(product['id']))}, {"$set":
                                                                            {  
                                                                                "product_ids": finalproduct
                                                                            
                                                                            }
                                                                            })                        

            response['message'] = "Data saved successfully"
            response['status'] = 200
        else:
            response['message'] = "Please ensure that the given type is correct."
            response['status'] = 404
    
        return response
    
    def get_products_list(self, store, params):
        type = params.get('status', '')       
        if type:
            products_data = super().find_one({'type': type})
            product_ids = products_data['product_ids']
            products, total_data_length, page, limit = product_db.get_products_by_id(store, product_ids, params, products_list_fields)

            for product in products:
                category_ids = product.get('categories', [])
                category_names = product_db.get_categories_by_id(store, category_ids)  # Function to retrieve category names
                product['category_names'] = category_names
            
            data = calculatePaginationData(products, page, limit, total_data_length)
            return data        
    
class SetImages():
    def setImage(self, body):
        response = {
            "status": 400
        }

        if not os.path.exists('images/storeinfo/images'):
            os.makedirs('images/storeinfo/images')

        file = body['image']
        UPLOAD_FOLDER = os.path.join('images/storeinfo/images')

        if (file.filename == ''):
            response['message'] = "No image selected for uploading"
            response['status'] = 500

        if (file and allowed_file(file.filename)):
            newName = change_file_name(file.filename)
            fname = secure_filename(newName)
            file.save(os.path.join(UPLOAD_FOLDER, fname))
            base_path = os.path.join(os.path.abspath(
                os.getcwd()), UPLOAD_FOLDER, newName)
            
            if '/app/images/storeinfo/images' in base_path:
                base_path = base_path.replace('/app/images/storeinfo/images', '')  
            response['message'] = base_path
            response['status'] = 200
        else:
            response['message'] = "Allowed image types are -> png, jpg, jpeg, gif, mp4, avi, mov, wmv, mkv"
            response['status'] = 500

        return response

    def getImage(self, body):
        url = './images/storeinfo/images/' + body['image']
        if not os.path.exists(url):
            return make_response({'error': 'Image not found'}, 404)

        return send_file(url, as_attachment=True)

    