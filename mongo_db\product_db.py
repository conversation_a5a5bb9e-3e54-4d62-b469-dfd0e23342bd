import logging
from pymongo.collation import Collation
from bson import ObjectId
import mongo_db
from utils.common import parse_json
import datetime
import new_mongodb

logger = logging.getLogger()

PRODUCT_COLLECTION = "products"
CATEGORIES_COLLECTION = "categories"
PRODUCT_COMPONENTS = "product_components"
MY_PRODUCTS = "my_products"

def get_page_by_id(store,id):
    db = mongo_db.get_store_db_client(store)
    result= db[PRODUCT_COLLECTION].find_one({"_id": id})
    return result

def get_product_count_by_brand_id(store, brand_id):
    db = mongo_db.get_store_db_client(store)
    count = db[PRODUCT_COLLECTION].count_documents({"brand_id": brand_id})
    return count

def get_variant_details_by_id(store,product_id,variant_id):
    db = mongo_db.get_store_db_client(store)
    collection=db[PRODUCT_COLLECTION]
    pipeline = [
        # Match the product by ID
        {"$match": {"id": product_id}},
        # Unwind the variants array
        {"$unwind": "$variants"},
        # Match the variant by ID
        {"$match": {"variants.id": variant_id}},
        # Project only the inventory level of the matched variant
        {"$project": {"_id": 0, "inventory_level": "$variants.inventory_level"}}
    ]
    # Execute the aggregation pipeline
    result = list(collection.aggregate(pipeline))
    return result[0]['inventory_level']

def update(store,query, payload):
    db = mongo_db.get_store_db_client(store)
    result=db[PRODUCT_COLLECTION].update_one(query, payload)
    return result

def get_products_by_id(store, ids, params, fields):    
    db = mongo_db.get_store_db_client(store)
    params['filterBy'] = ['name','sku']
    sort = {
            'sort_by': params['sort_by'] or 'date_created'
        }
    if params['sort_order'] == 'asc':
            sort['sort_order'] = 1
    else:
        sort['sort_order'] = -1

    def create_reg_ex_query(filterBy, filter):
            query = {
                "$or": [],
            }

            for i in filterBy:
                query['$or'].append({i: {"$regex": filter, "$options": "i"}},)
            
            # query.update(additionalQuery)
            return query
    
    limit = int(params["limit"]) if params.__contains__("limit") else 10
    page = int(params["page"]) if params.__contains__("page") else 1
    skips = params['skips'] if params.__contains__('skips') else 0

    id_query = {'id': {'$in': ids}}

    query = create_reg_ex_query(params["filterBy"], params['filter']) if len(
            params["filterBy"]) else {}
    
    query = {'$and': [query, id_query]} if query else id_query
    
    skips = limit * (page - 1)
    collation = Collation(locale='en', strength=2)
    data = db[PRODUCT_COLLECTION].find(query, fields).collation(collation).sort(
        sort['sort_by'], sort['sort_order']).skip(skips).limit(limit)
    # ProcessList ...
    data = processList(data)

    document_length = db[PRODUCT_COLLECTION].count_documents(query)
        
    return parse_json(data), document_length, page, limit

def processList(data):
        result = []
        if data:
            for _obj in data:
                result.append(processDocument(_obj))
        return result

def processDocument(obj):
        if obj:
            if '_id' in obj:
                obj['id'] = str(obj['_id'])
                del obj['_id']

            for key, value in obj.items():
                if isinstance(value, datetime.datetime) or isinstance(value, ObjectId):
                    obj[key] = str(value)

        return obj

def get_categories_by_id(store, category_ids):
    db = mongo_db.get_store_db_client(store)
    category_names = []
    for category_id in category_ids:
        category = db[CATEGORIES_COLLECTION].find_one({"_id": category_id})
        if category:
            category_names.append(category.get("name"))
    return category_names

def get_product_by_id(store, product_id):
    db = mongo_db.get_store_db_client(store)
    product = db[PRODUCT_COMPONENTS].find_one({"_id": int(product_id)})
    return product

def get_product_versions_by_id(store, product_id):
    db = mongo_db.get_store_db_client(store)
    result = db[PRODUCT_COMPONENTS].find_one({"_id": int(product_id)})
    return result

def insert_product_version(store, payload):
    db = mongo_db.get_store_db_client(store)
    result = db[PRODUCT_COMPONENTS].insert_one(payload)
    return result

def update_product_version(store, query, payload):
    db = mongo_db.get_store_db_client(store)
    result = db[PRODUCT_COMPONENTS].update_one(query, payload)
    return result

def delete_category_by_id(store, category_id):
    db = new_mongodb.get_store_db_client_for_store_id(store['id'])
    # Use $pull to remove the category with the matching category_id from the categories array
    result = db[MY_PRODUCTS].update_many(
        {"categories.id": category_id},  # Find products with the matching category_id in the categories array
        {"$pull": {"categories": {"id": category_id}}}  # Remove the category from the array
    )

    return result