import new_mongodb
import new_pgdb
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError
from new_mongodb import StoreDBCollections, get_store_db_client_for_store_id
from new_utils import _process_list, calculate_pagination, parse_json
from pymongo.collation import Collation
from new_pgdb.analytics_db import AnalyticsDB
from appconfig import is_pgdb_read_only_enabled

sold_product_list_fields = {
    "id": 1,
    "name": 1,
    "sku": 1,
    "total_sold": 1,
    'inventory_level': 1,
    'is_visible': 1,
}
def _create_reg_ex_query(payload, filterBy, filter, additionalQuery):
    query = {
        "$or": [],
    }

    for i in filterBy:
        query['$or'].append({i: {"$regex": filter, "$options": "i"}},)

    if "type" not in query and "type" in payload:
        query["type"] = payload["type"]

    if "status" in payload:
        status = payload.get("status", "active")
        if status == "active":
            query["is_visible"] = True
        elif status == "inactive":
            query["is_visible"] = False
        elif status == "out_of_stock":
            query["inventory_level"] = 0     

    query.update(additionalQuery)

    return query

def get_paginated_records_updated(store,db_client, collection_name, payload, fields, additional_query):
    sort_by = payload.get('sort_by', 'date_created')
    sort_order = payload.get('sort_order', 'asc')
    limit = int(payload.get('limit', '10'))
    page = int(payload.get('page', '1'))
    skips = int(payload.get('skips', '1'))
    filterBy = payload.get("filterBy", [])
    filter = payload.get("filter", '')

    sort = {
        'sort_by': sort_by
    }

    if sort_order == 'asc':
        sort['sort_order'] = 1
    else:
        sort['sort_order'] = -1

    query = {}
    if len(filterBy):
        query = _create_reg_ex_query(payload, filterBy, filter, additional_query)

    # Calculate number of records to skip ...
    skips = limit * (page - 1)
    collation = Collation(locale='en', strength=2)

    data = new_mongodb.fetchall_documents_from_collection(db_client, collection_name, query, fields).collation(collation).sort(sort['sort_by'], sort['sort_order']).skip(skips).limit(limit)
    # ProcessList ...
    data = _process_list(data)
    
    document_length = new_mongodb.count_documents_storefront_collection(store['id'], collection_name, query)

    return parse_json(data), document_length, page, limit

def get_sold_products(store, payload):
    response ={"status" : 400}
    store_id = store['id']
    with new_pgdb.get_connection(store_id, read_only=is_pgdb_read_only_enabled()) as conn:
        try:  
            payload['filterBy'] = ['name', 'sku']       
            db_client = get_store_db_client_for_store_id(store_id)
            sold_products, total_data_length, page, limit = \
                    get_paginated_records_updated(store,db_client, StoreDBCollections.PRODUCTS, payload, sold_product_list_fields, \
                                                  {"total_sold": {"$gt": 0}})
            if len(sold_products):
                ids = [item['id'] for item in sold_products]
                id_string = '(' + ', '.join(ids) + ')'
            
                get_data_query = f"SELECT product_id, sum(quantity) as sold_quantity FROM {AnalyticsDB.get_products_trend_table()} WHERE product_id IN {id_string} GROUP BY product_id"            
                res = conn.execute(text(get_data_query)) 
                query_result = res.fetchall()                
            
                for sold_product in sold_products:                  
                    for row in query_result:                             
                        if int(sold_product['id']) == int(row[0]):                            
                            sold_product['total_sold'] = int(row[1])  
                            break   
                    
                if payload['sort_by'] == 'total_sold':
                    if payload['sort_order'] == 'asc':
                        sold_products = sorted(sold_products, key=lambda x: x['total_sold'])
                    else:
                        sold_products = sorted(sold_products, key=lambda x: x['total_sold'], reverse=True)
                                                    
            # include pagination data in response ...
            data = calculate_pagination(sold_products, page, limit, total_data_length)
            response['status'] = 200
            response['data'] = data
        except SQLAlchemyError as e:
            error_message = str(e)
            response['status'] = 422
            response['message'] = error_message
        finally:
            conn.close()
    return response