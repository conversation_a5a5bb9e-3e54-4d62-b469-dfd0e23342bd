from new_mongodb import get_admin_db_client_for_store_id, fetchall_documents_from_admin_collection, process_documents
from new_mongodb import StoreAdminDBCollections, UserKeys
from sqlalchemy import text, bindparam
from new_pgdb import user_supplier_mapping_db
from datetime import datetime
import new_pgdb
from utils.common import calculatePaginationData, convert_to_timestamp
from utils import bc, store_util
import traceback
import logging

logger = logging.getLogger()

def get_all_users(store):
    response = {
        "status": 400,
        "message": "No data found",
        "data": []
    }
    fields = {
        UserKeys.USERNAME: 1,
        UserKeys.NAME: 1
    }
    users = fetchall_documents_from_admin_collection(store['id'], StoreAdminDBCollections.USERS_COLLECTION, {'type': 'admin_app_supplier_user', 'status': {'$in': ['active', 'inactive']}}, fields)
    users = process_documents(users)
    # Sort users by 'name' in ascending order
    users.sort(key=lambda x: x.get('name', '').lower())
    response['message'] = 'data retrieved successfully'
    response['data'] = users
    response['status'] = 200
    return response


def _check_user_exists(conn, email_id):
    query = text(
        f"SELECT EXISTS (SELECT 1 FROM {new_pgdb.DBTables.supplier_app_user_supplier_mapping} WHERE email_id = :email_id)"
    )
    result = conn.execute(query.params(email_id=email_id))
    exists = result.scalar()
    return exists

def _remove_existing_suppliers(conn, email_id):
    query = text(
                f"DELETE FROM {new_pgdb.DBTables.supplier_app_user_supplier_mapping} WHERE email_id = :email_id"
            )
    result = conn.execute(query.params(email_id=email_id))
    rs = result.rowcount
    conn.commit()
    return rs

def _get_products_using_brand_ids(conn, product_ids, brand_ids):
    query = text("""
        SELECT DISTINCT 
            p.product_id, p.sku, p.brand_id, p.brand_name 
        FROM products p 
        WHERE p.product_id NOT IN :product_ids 
        AND p.brand_id IN :brand_ids
    """).bindparams(
        bindparam("product_ids", expanding=True),
        bindparam("brand_ids", expanding=True)
    )   
    result = conn.execute(query.params(brand_ids=brand_ids, product_ids=product_ids))
    rs = result.fetchall()
    data = []
    for row in rs:
        data.append({
            "product_id": row[0],
            "product_sku": row[1],
            "brand_id": row[2],
            "brand_name": row[3]
        })
    return data

def _get_brands_using_product_ids(conn, product_ids):
    query = text("""
        SELECT DISTINCT
            p.brand_id, p.brand_name, p.product_id, p.sku
        FROM products p
        WHERE p.product_id IN :product_ids
    """)
    result = conn.execute(query.params(product_ids=product_ids))
    rs = result.fetchall()
    data = []
    for row in rs:
        data.append({
            "brand_id": row[0],
            "brand_name": row[1],
            "product_id": row[2],
            "product_sku": row[3]
        })
    return data


def build_supplier_mapping_model(user_name, products_data, email_id):
    models = []

    for product in products_data:
        model = user_supplier_mapping_db.UserSupplierMappingSchema(
            user_name=user_name,
            email_id=email_id,
            product_id= product['product_id'],
            product_sku=product['product_sku'],
            brand_id=product['brand_id'],
            brand_name=product['brand_name'],
            modified_at=datetime.now(),
        )
        models.append(model)

    return models

def _create_mapping(store_id, user_name, products_data, email_id):
    session = new_pgdb.get_session(store_id)
    try:    
        model = build_supplier_mapping_model(user_name, products_data, email_id)
        session.add_all(model)
        session.commit()
        return True
    except Exception as e:
        session.rollback()
        raise e
    finally:
        session.close()

def save_user_supplier_mapping_data(store, payload, is_edit):
    result = {}      
    conn = new_pgdb.get_connection(store['id'])
    try:
        # code execution start from here.
        if payload and 'user_name' in payload:
            user_name = payload['user_name']
            email_id = payload['email_id']
            brand_ids = payload.get('brand_ids', [])
            product_ids = payload.get('product_ids', [])

            # while adding user
            if not is_edit:
                # check if user is already added
                user_exists = _check_user_exists(conn, email_id)
            
                if user_exists:
                    result['status'] = 409
                    result['message'] = 'email_id: User with the given name already exits.'
                    return result
                
                # create new user
                else:
                    products_data = _get_products_using_brand_ids(conn, product_ids, brand_ids)
                    _create_mapping(store['id'], user_name, products_data, email_id)
                    result['status'] = 200
                    return result
            
            # while updating existing user.
            else:
                rs = _remove_existing_suppliers(conn, email_id)
                
                # update only if existing deleted.
                if (rs):
                    products_data = _get_products_using_brand_ids(conn, product_ids, brand_ids)
                    _create_mapping(store['id'], user_name, products_data, email_id)
                    result['status'] = 200
                    return result
    finally:
        conn.close()
    
    return result

def get_user_supplier_mapping_data(store, page, limit, sort_array, search):
    response = {
        "status": 400,
        "message": "No data found",
    }
    with new_pgdb.get_connection(store['id']) as conn:
        page = int(page)
        limit = int(limit)
        offset = (page - 1) * limit

        search_query = ""
        if search:
            search_query = f" WHERE (email_id ILIKE :search OR user_name ILIKE :search)"

        count_query = f"""SELECT COUNT(distinct email_id) FROM {new_pgdb.DBTables.supplier_app_user_supplier_mapping} {search_query}"""
        if search:
            count_rs = conn.execute(text(count_query).params(search=f"%{search}%"))
        else:
            count_rs = conn.execute(text(count_query))
        total_count = count_rs.scalar()

        query = f"""SELECT DISTINCT email_id, 
                    MAX(user_name) as user_name,
                    array_agg(DISTINCT brand_name) as brand_name,
                    COUNT(DISTINCT product_id) as product_count,
                    MAX(modified_at) as modified_at 
                    FROM {new_pgdb.DBTables.supplier_app_user_supplier_mapping} {search_query}
                    GROUP BY email_id"""
        
        if len(sort_array):
            sort_direction = "ASC" if sort_array[1] == "1" else "DESC"
            query += f" ORDER BY {sort_array[0]} {sort_direction} "
    
        query += f" LIMIT :limit OFFSET :offset"
        if search:
            results = conn.execute(text(query).params(limit=limit, offset=offset, search=f"%{search}%")).fetchall()
        else:
            results = conn.execute(text(query).params(limit=limit, offset=offset)).fetchall()

        grouped_data = {}

        for entry in results:
            user_name = entry[1]
            brand_name = entry[2]  # This is already an array from PostgreSQL
            product_count = entry[3]
            email_id = entry[0]
                
            if email_id not in grouped_data:
                grouped_data[email_id] = {
                    "email_id": email_id, 
                    "user_name": user_name, 
                    "brand_name": brand_name,
                    "product_count": product_count
                }

        data = list(grouped_data.values())

        # 1. Collect all email_ids for the current page
        email_ids = [user['email_id'] for user in data]

        fields = { UserKeys.USERNAME: 1, UserKeys.STATUS: 1 }       
        users_from_mongo = fetchall_documents_from_admin_collection(store['id'], StoreAdminDBCollections.USERS_COLLECTION, { "username": { "$in": email_ids }, "status": "deleted" }, fields)
        email_status_map = { user['username']: "deleted" for user in users_from_mongo }

        # 3. Add is_deleted flag to each user in data
        for user in data:
            status = email_status_map.get(user['email_id'], '')
            user['is_deleted'] = True if status == 'deleted' else False

        pagination_data = calculatePaginationData(data, page, limit, total_count)
        
        response['message'] = 'data retrieved successfully'
        response['data'] = pagination_data
        response['status'] = 200
    
    return response

def delete_user_record(store, body):
    with new_pgdb.get_connection(store['id']) as conn:
        query = f"""DELETE FROM {new_pgdb.DBTables.supplier_app_user_supplier_mapping} WHERE email_id = :email_id"""
        conn.execute(text(query).params(email_id=body['email_id']))
        conn.commit()

def get_user_mapped_suppliers(store, email_id):
    response = {
        "status": 400,
        "message": "No data found",
    }

    with new_pgdb.get_connection(store['id']) as conn:
        query = text(
            f"SELECT user_name, brand_name, brand_id, email_id FROM {new_pgdb.DBTables.supplier_app_user_supplier_mapping} WHERE email_id = :email_id"
        ).params(email_id=email_id)

        rs = conn.execute(query)

        if not rs:
            response['status'] = 404
            response['message'] = 'User with the given email id does not exist.'
            return response
    
        results = {
            "name": "",
            "brand_name": [],
            "brand_id": [],
            "email_id": ""
        }
        
        brand_ids = []
        brand_names = []
        for row in rs:              
            results['name']= row[0]
            brand_names.append(row[1])
            brand_ids.append(row[2])
            results['email_id'] = row[3]
        
        results['brand_name'] = brand_names
        results['brand_id'] = brand_ids

        response['message'] = 'data retrieved successfully'
        response['data'] = {
            'data': results
        }

        response['status'] = 200
    
    return response

def get_brands_dropdown(store, user):
    response = {
        "status": 400,
        "message": "No data found",
    }

    with new_pgdb.get_connection(store['id']) as conn:
        base_query = f"""
            SELECT DISTINCT p.brand_name, p.brand_id
            FROM products p
            LEFT JOIN {new_pgdb.DBTables.supplier_app_user_supplier_mapping} usm
                ON p.product_id = usm.product_id
        """

        # where_clause = "WHERE p.brand_id = usm.brand_id"
        where_clause = ""

        params = {}
        if user:
            where_clause = " WHERE usm.email_id = :user"
            params["user"] = user.strip()


        final_query = base_query + "\n" + where_clause + " ORDER BY p.brand_name ASC"

        rs = conn.execute(text(final_query), params)

        def convert_to_json(result_set):
            return [{'name': row[0] if row[0] != '' else 'None', 'brand_id': row[1]} for row in result_set]

        results = convert_to_json(rs)

        response['message'] = 'data retrieved successfully'
        response['data'] = {'data': results}
        response['status'] = 200

    return response

def get_brand_list(store):
    try:
        url = "v3/catalog/brands"
        bc_api = store_util.get_bc_api_creds(store)

        brands = []
        page = 1
        limit = 250  

        while True:
            query_params = {
                "limit": limit,
                "page": page
            }

            brand_data = bc.call_api(bc_api, "GET", url, query_params=query_params)

            if brand_data.status_code != 200:
                break

            data = brand_data.json()

            if 'data' not in data or not data['data']:
                break

            brands.extend([{"id": brand["id"], "name": brand["name"]} for brand in data['data']])

            # If fewer items returned than limit, it means it's the last page
            if len(data['data']) < limit:
                break

            page += 1  # Move to next page

        if brands:
            return {
                "status": 200,
                "message": "Brands retrieved successfully",
                "data": {"data": brands}
            }
        else:
            return {
                "status": 404,
                "message": "No brands found"
            }

    except Exception as e:
        return {
            "status": 500,
            "message": f"Error while fetching brands: {str(e)}"
        }

def get_product_list(store, brand_ids, search, page=1, limit=10, email_id=None):
    response = {
        "status": 400,
        "message": "No data found",
    }
    mapped_users_message = ""

    with new_pgdb.get_connection(store['id']) as conn:
        base_query = """
            SELECT product_id, sku, product_name, brand_name
            FROM products
        """
        count_query = "SELECT COUNT(*) FROM products"

        where_clauses = []
        params = {}
        if brand_ids:
            brand_ids_list = [int(b.strip()) for b in brand_ids.split(',') if b.strip().isdigit()]
            if brand_ids_list:
                where_clauses.append("brand_id = ANY(:brand_ids)")
                params['brand_ids'] = brand_ids_list

        if search:
            where_clauses.append("(product_name ILIKE :search OR sku ILIKE :search)")
            params['search'] = f"%{search}%"

        
        where_clause = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""

        # Pagination setup
        page = max(1, int(page)) if page else 1
        limit = max(1, int(limit)) if limit else 10
        offset = (page - 1) * limit
        params.update({'limit': limit, 'offset': offset})

        final_query = f"""
            {base_query}
            {where_clause}
            ORDER BY product_name ASC
            LIMIT :limit OFFSET :offset
        """
        final_count_query = f"""
            {count_query}
            {where_clause}
        """

        total_records = conn.execute(text(final_count_query), params).scalar()
        rs = conn.execute(text(final_query), params).fetchall()

        results = [
            {
                'product_id': row[0],
                'sku': row[1],
                'product_name': row[2],
                'brand_name': row[3]
            } for row in rs
        ]

        if brand_ids_list:
            mapping_query = f"""
                SELECT DISTINCT usm.brand_name, usm.user_name
                FROM {new_pgdb.DBTables.supplier_app_user_supplier_mapping} usm
                WHERE usm.brand_id = ANY(:brand_ids) AND usm.email_id != :email_id
            """
            mapped_users_rs = conn.execute(
                text(mapping_query), 
                {'brand_ids': brand_ids_list, 'email_id': email_id}
            ).fetchall()
            if mapped_users_rs:
                brand_user_map = {}
                for brand_name, user_name in mapped_users_rs:
                    if brand_name not in brand_user_map:
                        brand_user_map[brand_name] = set()
                    brand_user_map[brand_name].add(user_name)
                # Return as a dictionary: {brand_name: [user1, user2, ...], ...}
                brand_mapping_message = {brand: list(users) for brand, users in brand_user_map.items()}
            else:
                brand_mapping_message = {}
        else:
            brand_mapping_message = {}

        # Final response
        meta_data = {
            'current_page': page,
            'next_page': (page + 1 if page * limit < total_records else None),
            'total_count': total_records,
            'brand_mapping_message': brand_mapping_message
        }

        response['message'] = 'data retrieved successfully'
        response['data'] = {
            'data': results,
            'meta': meta_data
        }
        response['status'] = 200

    return response

def get_product_dropdown(store, search, page=1, limit=10):
    response = {
        "status": 400,
        "message": "No data found",
    }

    with new_pgdb.get_connection(store['id']) as conn:
        base_query = """
            SELECT product_id, sku, product_name
            FROM products
        """
        count_query = "SELECT COUNT(*) FROM products"

        where_clauses = []
        params = {}

        if search:
            where_clauses.append("(product_name ILIKE :search OR sku ILIKE :search)")
            params['search'] = f"%{search}%"

        
        where_clause = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""

        # Pagination setup
        page = max(1, int(page)) if page else 1
        limit = max(1, int(limit)) if limit else 10
        offset = (page - 1) * limit
        params.update({'limit': limit, 'offset': offset})

        final_query = f"""
            {base_query}
            {where_clause}
            LIMIT :limit OFFSET :offset
        """
        final_count_query = f"""
            {count_query}
            {where_clause}
        """

        total_records = conn.execute(text(final_count_query), params).scalar()
        rs = conn.execute(text(final_query), params).fetchall()

        results = [
            {
                'product_id': row[0],
                'sku': row[1],
                'product_name': row[2]
            } for row in rs
        ]

        response['message'] = 'data retrieved successfully'
        response['data'] = {
            'data': results,
            'meta': {
                'current_page': page,
                'next_page': (page + 1 if page * limit < total_records else None),
                'total_count': total_records
            }
        }
        response['status'] = 200

    return response

def get_product_mapping(store, search, user_email, brand, page, limit, sort_array):
    response = {
        "status": 400,
        "message": "No data found",
    }

    with new_pgdb.get_connection(store['id']) as conn:
        page = int(page)
        limit = int(limit)
        offset = (page - 1) * limit

        where_clauses = []
        params = {}

        if search:
            where_clauses.append("(p.product_name ILIKE :search OR usm.product_sku ILIKE :search)")
            params['search'] = f"%{search}%"

        if user_email:
            where_clauses.append("usm.email_id = :user_email")
            params['user_email'] = user_email

        if brand:
            brand_list = [b.strip() for b in brand.split(',') if b.strip()]
            where_clauses.append("usm.brand_id IN :brand")
            params['brand'] = tuple(brand_list)

        where_clause = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""

        # Sorting logic
        allowed_sort_fields = {
            'sku': 'usm.product_sku',
            'product_name': 'p.product_name',
            'brand_name': 'usm.brand_name',
            'modified_at': 'usm.modified_at'
        }

        default_sort_field = 'p.product_name'
        sort_field = default_sort_field
        sort_direction = 'ASC'

        if sort_array and len(sort_array) == 2:
            field_key = sort_array[0]
            direction_flag = sort_array[1]
            if field_key in allowed_sort_fields:
                sort_field = allowed_sort_fields[field_key]
                sort_direction = 'ASC' if direction_flag == '1' else 'DESC'

        order_clause = f"ORDER BY {sort_field} {sort_direction}"

        # Count query
        count_query = f"""
            SELECT COUNT(*) 
            FROM {new_pgdb.DBTables.supplier_app_user_supplier_mapping} usm
            JOIN products p ON usm.product_id = p.product_id
            {where_clause}
        """
        count_rs = conn.execute(text(count_query), params)
        total_count = count_rs.scalar()

        # Final paginated query
        final_query = f"""
            SELECT 
                usm.product_id, 
                usm.product_sku, 
                p.product_name, 
                p.brand_name,
                usm.modified_at
            FROM {new_pgdb.DBTables.supplier_app_user_supplier_mapping} usm
            JOIN products p ON usm.product_id = p.product_id
            {where_clause}
            {order_clause}
            LIMIT :limit OFFSET :offset
        """

        params.update({"limit": limit, "offset": offset})
        rs = conn.execute(text(final_query), params).fetchall()

        result_data = [
            {
                'product_id': row[0],
                'sku': row[1],
                'product_name': row[2],
                'brand_name': row[3],
                'modified_at': convert_to_timestamp(row[4])
            } for row in rs
        ]

        pagination_data = calculatePaginationData(result_data, page, limit, total_count)

        response['message'] = 'data retrieved successfully'
        response['data'] = pagination_data
        response['status'] = 200

    return response

def delete_product_from_mapping(store_id, payload):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        email_id = payload.get('email_id', None)
        product_ids = payload.get('product_ids', [])
        if email_id and product_ids:
            query = text(f"DELETE FROM {new_pgdb.DBTables.supplier_app_user_supplier_mapping} WHERE email_id = :email_id AND product_id IN :product_ids")
            conn.execute(query, {"email_id": email_id, "product_ids": tuple(product_ids)})
            conn.commit()
            response['status'] = 200
            response['message'] = 'Product deleted successfully'
    except Exception as e:
        conn.rollback()
        logger.error(traceback.format_exc())
        raise e
    finally:
        conn.close()
    return response

def add_products_to_mapping(store_id, payload):
    response = {
        "status": 400,
        "message": "Something went wrong."
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        user_name = payload.get('user_name', None)
        email_id = payload.get('email_id', None)
        product_ids = payload.get('product_ids', [])
        if email_id and product_ids:
            # Fetch product_ids already mapped to this email
            query = f"""
                SELECT product_id
                FROM {new_pgdb.DBTables.supplier_app_user_supplier_mapping}
                WHERE email_id = :email_id AND product_id = ANY(:product_ids)
            """
            rs = conn.execute(text(query), {"email_id": email_id, "product_ids": product_ids})
            existing_product_ids = {row[0] for row in rs.fetchall()}
            
            # Filter out already associated product_ids
            new_product_ids = [pid for pid in product_ids if pid not in existing_product_ids]

            if new_product_ids != []:
                brands_data = _get_brands_using_product_ids(conn, tuple(new_product_ids))
                _create_mapping(store_id, user_name, brands_data, email_id)
                response['status'] = 200
                response['message'] = 'Products added successfully'
            else:
                response['status'] = 200
                response['message'] = 'No new products to add'
    except Exception as e:
        logger.error(traceback.format_exc())
        response['status'] = 500
        response['message'] = str(e)
        raise e
    finally:
        conn.close()
    return response
