from flask import request
import logging
import traceback
from api import APIResource
from analytics import product_limit_report
from new_mongodb import store_admin_db


logger = logging.getLogger()

class ProductLimitReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering product limit report data GET")
        try:
            query_params = request.args.to_dict()
            res = product_limit_report.get_product_limit_report(store['id'], query_params)
            return res, 200
        finally:
            logger.debug("Exiting product limit report data GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)