from flask import Flask
import logging

logger = logging.getLogger()

class AppConfig:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            logger.info('Creating the AppConfig')
            cls._instance = super(AppConfig, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        logger.info("Entering AppConfig")
        self.app = Flask(__name__)
        self.app.config.from_object("config")
        logger.info("Exiting AppConfig")
    
    def get_app(self):
        return self.app

appConfig = AppConfig()

def get_api_prefix():
    return appConfig.get_app().config["API_PREFIX"]

def is_debug_enabled():
    return appConfig.get_app().config["DEBUG"]

def get_pgdb_conn_str():
    return appConfig.get_app().config["PG_CONN_STRING"]

def get_celery_broker_url():
    return appConfig.get_app().config["CELERY_BROKER_URL"]

def get_celery_result_backend():
    return appConfig.get_app().config["CELERY_RESULT_BACKEND"]

def get_celery_app_name():
    return appConfig.get_app().config["CELERY_APP_NAME"]

def get_admin_store_key():
    return "admin"

def get_admin_panel_url():
    return appConfig.get_app().config["ADMIN_PANEL_URL"]

def get_redis_host():
    return appConfig.get_app().config["REDIS_HOST"]

def get_redis_port():
    return appConfig.get_app().config["REDIS_PORT"]

def get_redis_db():
    return appConfig.get_app().config["REDIS_DB"]

def get_admin_user():
    return appConfig.get_app().config["DEFAULT_ADMIN_USER"]

def get_admin_secret():
    return appConfig.get_app().config["DEFAULT_ADMIN_SECRET"]

def get_admin_user_role():
    return appConfig.get_app().config["DEFAULT_ADMIN_ROLE"]

def get_admin_db_name():
    return appConfig.get_app().config["ADMIN_DB_NAME"]

def get_mongodb_connection_str():
    return appConfig.get_app().config["MONGO_CONN_STRING"]

def get_mongodb_max_conn_pool_size():
    return appConfig.get_app().config["MONGO_MAX_POOL_SIZE"]

def is_pgdb_read_only_enabled():
    return False