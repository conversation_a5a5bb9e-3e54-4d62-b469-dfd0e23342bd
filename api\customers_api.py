from flask import request
import logging
import traceback
from api import APIResource
from services import customer_service
from utils import auth_util, bc, customer_util, store_util, tag_util
from customers.all_customer import customers_list, customer_tags, customer_returns_data
from products.all_products import customer_product_price_list

logger = logging.getLogger()

class Customers(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Customers GET")
        try:
            query_params = request.args.to_dict()
            bc_db = store_util.get_bc_db(store)
            res = customers_list.get_all_customers(query_params,store)
            if res['status'] == 200:                
                return res['data'], 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Customers GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class Customer(APIResource):
    def get_executor(self, request, token_payload, store, customer_id):
        logger.debug("Entering Customer Details GET")
        try:
            if not customer_id:
                return {"message": "Please enter customerId as a query params in request"}, 400
            
            res = customers_list.get_customer(store, customer_id)
            # Return response ...
            return res, 200
        finally:
            logger.debug("Exiting Customer Details GET")

    def put_executor(self, request, token_payload, store, customer_id):
        logger.debug("Entering BC Customer Groups PUT")
        try:
            if not customer_id:
                return {"message": "Please enter customerId as a query params in request"}, 400
            req_body = request.get_json(force=True)

            username = ""
            if token_payload and 'username' in token_payload:
                username = token_payload['username']

            if username != "":
                res = customers_list.update_customer_invoice_visibility(store['id'], req_body, customer_id)
                return {"message": res['message']}, res['status']
            else:
                return {"message": "Unauthorized."}, 401

        finally:
            logger.debug("Exiting BC Customer Groups PUT")

    def put(self, customer_id):
        return self.execute_store_request(request, self.put_executor, customer_id)

    def get(self, customer_id):
        return self.execute_store_request(request, self.get_executor, customer_id)

class CustomerLogin(APIResource):
    # Create customer login ...
    def post_executor(self, request, token_payload, store, token=None):
        try:
            user = request.get_json(force=True)
            res = auth_util.store_customer_login(user['customer_id'], store, token_payload['username'])
            return res, res['status']
        finally:
            logger.debug("Exiting Customer LoginAPI POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)

class BCCustomers(APIResource):
    def get_executor(self, request, token_payload, store, customer_id):
        logger.debug("Entering BC Customer Details GET")
        try:
            if not customer_id:
                return {"message": "Please enter customerId as a query params in request"}, 400
            query_params = request.args.to_dict()
            api = store_util.get_bc_api_creds(store)
            res = bc.get_bc_customers(api, customer_id, query_params,store)
            # Return response ...
            return res, 200
        finally:
            logger.debug("Exiting BC Customer Details GET")
     
    def get(self, customer_id):
        return self.execute_store_request(request, self.get_executor, customer_id)

class BCCustomerGroupdetails(APIResource):
    def get_executor(self, request, token_payload, store, customer_id):
        logger.debug("Entering BC Customer Group Details GET")
        try:
            if not customer_id:
                return {"message": "Please enter customerId as a query params in request"}, 400
            
            api = store_util.get_bc_api_creds(store)
            res = bc.get_bc_customer_group_by_id(api, customer_id, {})
            
            # Return response ...
            return res, 200
        finally:
            logger.debug("Exiting BC Customer Group Details GET")
     
    def get(self, customer_id):
        return self.execute_store_request(request, self.get_executor, customer_id)

class BCCustomerShippingAddressDetails(APIResource):
    def get_executor(self, request, token_payload, store, customer_id):
        logger.debug("Entering BC Customer Shipping Address Details GET")
        try:
            if not customer_id:
                return {"message": "Please enter customerId as a query params in request"}, 400
            
            api = store_util.get_bc_api_creds(store)
            res = bc.get_bc_customer_shipping_address_details(api, customer_id, {})
            
            # Return response ...
            return res, 200
        finally:
            logger.debug("Exiting BC Customer Shipping Address Details GET")
     
    def get(self, customer_id):
        return self.execute_store_request(request, self.get_executor, customer_id)
    
class History(APIResource):
    def get_executor(self, request, token_payload, store, customer_id):
        logger.debug("Entering Reward points customer details GET")
        try:
            if not customer_id:
                return {"message": "Please enter customerId as a query params in request"}, 400
            query_params = request.args.to_dict()
            res = customer_util.get_customer_history(store, customer_id, query_params)
            
            # Return response ...
            return res['data'], 200
        finally:
            logger.debug("Exiting Reward points customer details GET")
     
    def get(self, customer_id):
        return self.execute_store_request(request, self.get_executor, customer_id)
class BCCustomerGroups(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering BC Customer Groups GET")
        try:
            api = store_util.get_bc_api_creds(store)
            bc_db = store_util.get_bc_db(store)
            res = customers_list.get_all_customer_groups(api, store['id'])
            # Return response ...
            return res['data'], 200
        finally:
            logger.debug("Exiting BC Customer Groups GET")

    def put_executor(self, request, token_payload, store):
        logger.debug("Entering BC Customer Groups PUT")
        try:
            req_body = request.get_json(force=True)

            username = ""
            if token_payload and 'username' in token_payload:
                username = token_payload['username']

            if username != "":
                res = customers_list.update_invoice_visibility(store['id'], req_body, username)
                return {"message": res['message']}, res['status']
            else:
                return {"message": "Unauthorized."}, 401

        finally:
            logger.debug("Exiting BC Customer Groups PUT")

    def put(self):
        return self.execute_store_request(request, self.put_executor)

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class CustomerRepresentative(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering customer representative GET")
        try:
            res = customers_list.get_customer_representatives(store['id']) 
            if res['status'] == 200:
                return {"data":res['data']}, res['status']
            else:
                return {"message": res['message']}, res['status']
        finally:
            logger.debug("Exiting customer representative GET")
     
    def get(self):
        return self.execute_store_request(request, self.get_executor)

class CustomerGroups(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Customer Groups GET")
        try:
            res = customers_list.get_customer_groups(store['id'])
            return res['data'], res['status']
        finally:
            logger.debug("Exiting Customer Groups GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)

    
class BCCustomerConsignmentOrders(APIResource):
    def get_executor(self, request, token_payload, store, customer_id):
        logger.debug("Entering Customer Consignment Orders GET")
        try:
            if not customer_id:
                return {"message": "Please enter customerId as a query params in request"}, 400
            query_params = request.args.to_dict()
            res = bc.get_customer_consignment_orders(store, customer_id, query_params)
            if res['status'] == 200:
                return  res['data'], res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Customer Consignment Orders GET")
     
    def get(self, customer_id):
        return self.execute_store_request(request, self.get_executor, customer_id)
    
class CustomerTags(APIResource):
    def post_executor(self, request, token_payload, store, customer_id):
        logger.debug("Entering Customer Tags POST")
        try:
            payload = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']
            if payload:
                res = customer_tags.store_customer_tags(store, payload, username, customer_id)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'message': 'Payload is required'}, 400
        finally:
            logger.debug("Exiting Customer Tags POST")

    def get_executor(self, request, token_payload, store, customer_id):
        logger.debug("Entering Customer Tags GET")
        try:
            res = customer_tags.get_customer_tags(store, customer_id)
            if res['status'] == 200:
                return res['data'], res['status']
            else:
                return {'data': res['data']}, res['status']
        finally:
            logger.debug("Exiting Customer Tags GET")

    def get(self, customer_id):
        return self.execute_store_request(request, self.get_executor, customer_id)
    
    def post(self, customer_id):
        return self.execute_store_request(request, self.post_executor, customer_id)
    

class CustomerPriceMapping(APIResource):
    def get_executor(self, request, token_payload, store, customer_id):
        logger.debug("Entering Customer Product price lists GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            sort_by = query_params.get('sort_by', '').strip()
            search = query_params.get('search', '').strip()
            
            res = customer_product_price_list.get_customer_product_price_list(
                store['id'], page, limit, sort_by, search, customer_ids=customer_id
            )

            if res['status'] == 200:
                return res['data'], res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Customer Product price lists GET")

    
    def post_executor(self, request, token_payload, store, customer_id):
        logger.debug("Entering Customer Product price lists POST")
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']

            price = float(req_body.get('price', 0))
            parent_sku = req_body.get('parent_sku', None)

            if username != '':
                if parent_sku:
                    email = customers_list.get_email_by_customer_id(store['id'], customer_id)
                    if not email:
                        return {'message': 'Invalid customer_id. Customer not found.'}, 404

                    res = customer_product_price_list.add_customer_product_price(
                        store['id'], email, parent_sku, price, username
                    )
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': 'Parent SKU is required.'}, 400
            else:
                return {'message': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Customer Product price lists POST")

    def delete_executor(self, request, token_payload, store, customer_id):
        logger.debug("Entering Customer Product price lists DELETE")
        try:
            query_params = request.args.to_dict()
            mapping_ids = query_params.get('ids', '')
            if mapping_ids != '':
                id_list = [int(i) for i in mapping_ids.split(",") if i.isdigit()]

                valid_ids = customers_list.get_customer_filtered_ids(store['id'], id_list, customer_id)

                if not valid_ids:
                    return {"message": "No matching records found for this customer."}, 404

                res = customer_product_price_list.delete_customer_product_price(store['id'], ",".join(valid_ids))
                if res:
                    return {"message": res['message']}, res['status']
            else:
                return {"message": "Mapping id(s) required."}, 400
        finally:
            logger.debug("Exiting Customer Product price lists DELETE")

    def get(self, customer_id):
        return self.execute_store_request(request, self.get_executor, customer_id)
    
    def post(self, customer_id):
        return self.execute_store_request(request, self.post_executor, customer_id)
    
    def delete(self, customer_id):
        return self.execute_store_request(request, self.delete_executor, customer_id)
    

class UpadateCustomerPriceMapping(APIResource):
    def put_executor(self, request, token_payload, store, customer_id):
        logger.debug("Entering Update Customer Product price lists PUT")
        try:
            if not customer_id:
                return {"message": "customer_id is required"}, 400
            
            req_body = request.get_json(force=True)
            username = ""
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                res = customer_product_price_list.update_customer_product_price(store['id'], req_body, customer_id, username)
                return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Update Customer Product price lists PUT")
    
    def put(self, customer_id):
        return self.execute_store_request(request, self.put_executor, customer_id)

class CustomerReturnsData(APIResource):
    def get_executor(self, request, token_payload, store, email_id):
        logger.debug("Entering Update Customer returns data GET")
        try:
            username = ""
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                res = customer_returns_data.get_customer_returns_data(store['id'], email_id, username)
                return res['data'], res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Update Customer returns data GET")

    def get(self, email_id):
        return self.execute_store_request(request, self.get_executor, email_id)