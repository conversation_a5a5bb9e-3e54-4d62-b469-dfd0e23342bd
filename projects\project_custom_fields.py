from sqlalchemy import text
import pg_db
from mongo_db import user_db
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from datetime import datetime
import logging
import traceback

logger = logging.getLogger() 

def _fetch_custom_fields_from_project(conn, project_id):
    data = []
    query = text (
        f"""SELECT apc.id,
                apc.name,
                acm.datatype,
                acm.is_multiselect_group,
                acm.group_values,
                acm.is_mandatory,
                apc.sort_id,
                COUNT(DISTINCT acv.card_id) AS card_counts,
                apc.is_visible,
                acm.project_id,
                pcm.db_table_column,
                apc.read_only
            FROM {pg_db.agile_project_customfield} apc
            JOIN {pg_db.agile_customfield_meta} acm ON apc.customfield_meta_id = acm.id
            LEFT JOIN {pg_db.agile_customfield_value} acv ON apc.id = acv.project_customfield_id
            LEFT JOIN {pg_db.pipeline_custom_field_mapping} pcm ON pcm.project_customfield_id = apc.id
            WHERE apc.project_id = :project_id and apc.status = 'active'
            GROUP BY apc.id, acm.datatype, acm.is_multiselect_group, acm.group_values, acm.is_mandatory, acm.project_id, pcm.db_table_column
            ORDER BY apc.sort_id ASC;
        """
    )
    query = query.params(project_id=project_id)
    result = conn.execute(query)
    for row in result.fetchall():
        row_data = {
            'id': row[0],
            'custom_field_name':row[1],
            'field_type': row[2],
            'is_multiselect_group': row[3],
            'group_values': row[4],
            'is_mandatory': row[5],
            'sort_id': row[6],
            'card_counts': row[7],
            'is_visible': row[8],
            'is_global': False if row[9] else True,
            'column_value': row[10],
            'read_only': row[11]
        }
        data.append(row_data)
    
    return data

def get_fields_from_project(project_id):
    response = {
        "status": 400 
    }
    conn = pg_db.get_connection()
    try:
        data = _fetch_custom_fields_from_project(conn, project_id)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response


def _insert_new_custom_field_to_project(conn, customfield_meta_id, name, username, project_id, is_visible, read_only):
    try:
        max_sort_id_query = text(
            f"""SELECT MAX(sort_id) AS max_sort_id FROM {pg_db.agile_project_customfield};"""
        )
        max_sort_id_result = conn.execute(max_sort_id_query).fetchone()
        max_sort_id = max_sort_id_result[0] or 0
        
        new_sort_id = max_sort_id + 1

        query = text(
            f"""INSERT INTO {pg_db.agile_project_customfield} (project_id, name, customfield_meta_id, sort_id, created_by, updated_by, is_visible, read_only)
                VALUES (:project_id, :name, :customfield_meta_id, :sort_id, :created_by, :created_by, :is_visible, :read_only);
            """
        )
        query = query.params(project_id=project_id, name=name, customfield_meta_id=customfield_meta_id, sort_id=new_sort_id, created_by=username, is_visible=is_visible, read_only=read_only)
        conn.execute(query)

        return True, None
    except IntegrityError as e:
        # Handle Unique Constraint Violations
        if isinstance(e.orig, UniqueViolation):
            error_message = f"Custom field with name '{name}' already exists for project ID {project_id}."
        else:
            error_message = str(e)
        return False, error_message

def add_new_field_to_project(customfield_meta_ids, username, project_id, name, datatype, is_multiselect_group, group_values, is_mandatory, field_type):
    response = {
        "status" :400,
        "message": "Error occurred",
        "failed_ids": [],
        "success_ids": []
    }
    conn = pg_db.get_connection()
    try: 
        if field_type == "custom":
            if datatype == "select" or datatype == "multi select":
                # Check if required parameters for group datatype are provided
                if group_values is None or group_values == '':
                    response['status'] = 422
                    response['message'] = "For select datatype, select_values are required."
                    return response
            new_customfield_meta_id = _insert_new_custom_field(conn, name, datatype, is_multiselect_group, group_values, is_mandatory, username, project_id)
            if new_customfield_meta_id:
                is_success, error_message = _insert_new_custom_field_to_project(conn, new_customfield_meta_id, name, username, project_id, True, False)
                if is_success:
                    response['status'] = 200
                    response['message'] = "custom field insertions successfull."
                    response['success_ids'].append(new_customfield_meta_id)
                else:
                    response['failed_ids'].append({
                        'id': name,
                        'error': error_message
                    })
                    response['status'] = 409
                    response['message'] = "custom field insertions failed."
            else:
                response['status'] = 409
                response['message'] = "name: Data insertion failed, field with the same name already exists."
                
        elif field_type == "global":    
            if len(customfield_meta_ids):             
                for meta_id in customfield_meta_ids:
                    query = text (f"""SELECT id, name FROM {pg_db.agile_customfield_meta} WHERE id = :meta_id;""")
                    query = query.params(meta_id=meta_id)
                    result = conn.execute(query).fetchone()

                    if result:
                        name = result[1]

                        is_success, error_message = _insert_new_custom_field_to_project(conn, meta_id, name, username, project_id, True, False)
                        if is_success:
                            response['success_ids'].append(meta_id)
                        else:
                            response['failed_ids'].append({
                                'id': name,
                                'error': error_message
                            })
                    else:
                        response['failed_ids'].append({
                                'id': name,
                                'error': 'Custom field meta ID not found'
                            })

                    if len(response['success_ids']) == len(customfield_meta_ids):
                        response['status'] = 200
                        response['message'] = "all insertions successfull."
                    elif len(response['success_ids']) == 0:
                        response['status'] = 409
                        response['message'] = "All insertions failed."
                    else:
                        response['status'] = 200
                        response['message'] = "Custom fields processed. Check success and failed lists for details."
    except IntegrityError as e:
            logger.error(traceback.format_exc())
            response['status'] = 500
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response

def _insert_new_custom_field(conn, name, datatype, is_multiselect_group, group_values, is_mandatory, username, project_id):
    try:
        check_query = text(f"""SELECT COUNT(*) FROM {pg_db.agile_customfield_meta} WHERE LOWER(name) = LOWER(:name) AND datatype = :datatype AND project_id = :project_id AND status = 'active';""")
        check_result = conn.execute(check_query, {'name': name, 'datatype': datatype, 'project_id': project_id}).scalar()
        
        if check_result > 0:
            return 0 
        
        new_sort_id = None
        if datatype not in ["select", "multi select"]:
            group_values = None
        
        query = text(
            f"""INSERT INTO {pg_db.agile_customfield_meta} (name, datatype, is_multiselect_group, group_values, sort_id, is_mandatory, created_by, updated_by, project_id, status)
                VALUES (:name, :datatype, :is_multiselect_group, :group_values, :sort_id, :is_mandatory, :created_by, :created_by, :project_id, 'active') RETURNING id
            """
        )
        query = query.params(name=name, datatype=datatype, is_multiselect_group=is_multiselect_group, group_values=group_values, sort_id = new_sort_id, is_mandatory=is_mandatory, created_by=username, project_id=project_id)
        res = conn.execute(query)
        new_id = res.scalar()

        return new_id
    except Exception as e:
        logger.error(traceback.format_exc())
        return 0

def _modify_sort_id(conn, project_custom_field_ids, project_id):
    try:
        for index, id in enumerate(project_custom_field_ids, start=1):
            query = text (
                f"""UPDATE {pg_db.agile_project_customfield} SET sort_id = :sort_id WHERE project_id = :project_id AND id = :id"""
            )
            query = query.params(sort_id=index, project_id=project_id, id=id)
            conn.execute(query)
        return True
    except Exception as e:
        return False

def update_sort_id_in_project_customfield(project_custom_field_ids, project_id):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        if not isinstance(project_custom_field_ids, list):
            project_custom_field_ids = [project_custom_field_ids]

        data = _modify_sort_id(conn, project_custom_field_ids, project_id)
        if data:
            response['status'] = 200
            response['message'] = "Data updated successfully."
        else:
            response['status'] = 409
            response['message'] = "Data updation failed."
    except IntegrityError as e:
        logger.error(traceback.format_exc())
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 409
            response['message'] = "name: This custom field already exists."
        else:
            response['status'] = 500
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response