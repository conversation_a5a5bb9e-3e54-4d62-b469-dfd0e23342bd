from sqlalchemy.dialects.postgresql import insert
import pg_db as db
from sqlalchemy import <PERSON>olean, <PERSON>umn, DateTime, String, Integer, ForeignKey, Float
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import insert
import pg_db as db
from sqlalchemy import Boolean, Column, DateTime, String, Integer, ForeignKey, BigInteger
from sqlalchemy.sql import func


class ReplenishmentClassifiedAs(db.Base):
    __tablename__ = "replenishment_classified_as"
    id = Column(Integer, primary_key=True, nullable=False)
    name = Column(String(200), nullable=False, unique=True)
    created_by = Column(String(), nullable=False)
    updated_by = Column(String())
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())