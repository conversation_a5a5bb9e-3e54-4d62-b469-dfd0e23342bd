from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
import new_mongodb
from new_mongodb import StoreAdminDBCollections, get_admin_db_client_for_store_id, get_store_db_client_for_store_id
from collections import defaultdict
from products.all_products import products_list
from utils import store_util
from new_mongodb import pricelist_db

def get_google_sheet_service(store_id):
    """
    Fetch and authenticate Google Sheets API client based on the store ID.
    """
    store = new_mongodb.get_store_by_id(store_id)
    service_account_info = store['apps']['google']['service_account']
    scopes = store['apps']['google']['google_sheet']['scopes']

    credentials = Credentials.from_service_account_info(
        service_account_info, scopes=scopes
    )
    service = build('sheets', 'v4', credentials=credentials)

    return {
        "service": service.spreadsheets(),
        "spreadsheet_id": store['apps']['google']['google_sheet']['sheet_id'],
        "sheet_name": store['apps']['google']['google_sheet']['sheet_name'],
        "default_range": store['apps']['google']['google_sheet']['default_range']
    }

def find_variant_sku(variant_id, db):
    """
    Locate the SKU corresponding to a given variant_id.
    """
    document = db[StoreAdminDBCollections.PRODUCT_PRICE_LISTS].find_one({"variants.variant_id": variant_id})
    db_data = document

    for variant in db_data.get("variants", []):
        if variant["variant_id"] == variant_id:
            return variant["variant_sku"], db_data
        
    return None

def find_price_column(price_list_id, price_list_object):
    """
    Locate the price column corresponding to a given price_list_id.
    """        
    # Iterate through the price list data
    for variant in price_list_object.get("variants", []):
        for price_list in variant.get("price_list", []):
            if price_list["price_list_id"] == price_list_id:
                return price_list["name"]
        
    print(f"No matching price_list_id found in the variants for price_list_id: {price_list_id}")
    return None

def fetch_sheet_headers(sheet, sheet_id, sheet_name):
    """
    Fetch the headers of the Google Sheet to map column names.
    """
    result = sheet.values().get(
        spreadsheetId=sheet_id,
        range=f"{sheet_name}!A1:Z1"  # Assuming headers are in the first row
    ).execute()
    headers = result.get("values", [[]])[0]
    
    return headers

def find_row_for_sku(sheet, sheet_id, sheet_name, sku):
    """
    Locate the row number for a given SKU in the Google Sheet.
    """
    result = sheet.values().get(
        spreadsheetId=sheet_id,
        range=f"{sheet_name}!C:C"  # Assuming SKUs are in column C
    ).execute()
    rows = result.get("values", [])
    for i, row in enumerate(rows):
        if row and row[0] == sku:
            return i + 1  # Return 1-based row number
    
    return None

def update_google_sheet(sheet, sheet_id, updates):
    """
    Perform batch updates to Google Sheets.
    """
    body = {
        "data": updates,
        "valueInputOption": "RAW"
    }
    result = sheet.values().batchUpdate(
        spreadsheetId=sheet_id,
        body=body
    ).execute()
    
    print(f"{result.get('totalUpdatedCells')} cells updated.")

def update_price_in_sheet(store_id, input_data):
    """
    Main function to map `variant_id` to SKU, find the corresponding price column,
    and update the price in Google Sheets.
    """
    # Authenticate and fetch Google Sheets service
    sheet_info = get_google_sheet_service(store_id)
    sheet = sheet_info["service"]
    sheet_id = sheet_info["spreadsheet_id"]
    sheet_name = sheet_info["sheet_name"]

    db = get_admin_db_client_for_store_id(store_id)

    # Fetch headers from the sheet
    headers = fetch_sheet_headers(sheet, sheet_id, sheet_name)
    updates = []

    for item in input_data['data']:
        variant_id = item["variant_id"]
        price_list_id = item["price_list_id"]
        price = item["price"]

        # Find SKU for the variant_id
        sku, price_list_object = find_variant_sku(variant_id, db)
        if not sku:
            print(f"SKU not found for variant_id: {variant_id}")
            continue

        # Find the column name for the price list
        column_name = find_price_column(price_list_id, price_list_object)
        if not column_name:
            print(f"Price list not found for price_list_id: {price_list_id}")
            continue

        # Find the column index in the sheet
        if column_name not in headers:
            print(f"Column '{column_name}' not found in sheet headers.")
            continue
        column_index = headers.index(column_name) + 1  # Convert to 1-based index

        print(sheet_name, sku)
        # Find the row number for the SKU
        row_number = find_row_for_sku(sheet, sheet_id, sheet_name, sku)
        if not row_number:
            print(f"Row not found for SKU: {sku}")
            continue

        # Prepare update range and data
        cell_range = f"{sheet_name}!{chr(64 + column_index)}{row_number}"  # Column letter + row number
        updates.append({
            "range": cell_range,
            "values": [[price]]
        })

    # Execute batch updates
    if updates:
        update_google_sheet(sheet, sheet_id, updates)
    else:
        print("No valid updates to process.")


def process_google_sheet_request(store_id, sheet_data):
    """
    Transforms Google Sheets request into the required array of objects,
    groups SKUs by product ID, and applies updates for each product.
    """
    store = store_util.get_store_by_id(store_id)
    db = get_admin_db_client_for_store_id(store_id)
    store_db = get_store_db_client_for_store_id(store_id)

    # Extract all SKUs from the input data
    skus = [sku for sku_entry in sheet_data for sku in sku_entry.keys()]

    # Fetch all products with variants matching these SKUs in one query
    product_data_list = db[StoreAdminDBCollections.PRODUCT_PRICE_LISTS].find({"variants.variant_sku": {"$in": skus}})

    # Group SKUs by their parent product ID
    grouped_data = defaultdict(list)
    for product in product_data_list:
        product_id = product["parent_product_id"]
        for variant in product.get("variants", []):
            grouped_data[product_id].append({
                "sku": variant["variant_sku"],
                "variant_id": variant["variant_id"],
                "price_list": variant.get("price_list", [])
            })

    # Transform data for each product ID and apply updates
    for product_id, variants in grouped_data.items():
        data = {"data": [], "default_price": []}

        # Fetch all unique price list keys from sheet_data
        unique_keys = {key for sku_entry in sheet_data for sku, price_updates in sku_entry.items() for key in price_updates.keys()}

        # Fetch all active price lists at once
        active_price_lists = {key: pricelist_db.fetch_price_list_using_name(store_db, key) for key in unique_keys}

        # Process each SKU entry and map to the product's variants
        for sku_entry in sheet_data:
            for sku, price_updates in sku_entry.items():
                # Find the matching variant
                matching_variant = next((v for v in variants if v["sku"] == sku), None)

                if not matching_variant:
                    print(f"No matching variant found for SKU: {sku}")
                    continue

                variant_id = matching_variant["variant_id"]

                # Map each price key to its corresponding price_list_id and process updates
                for key, new_price in price_updates.items():
                    # Skip further processing for the "Cost" key
                    if key == "Cost":
                        continue  

                    # Process for "Wholesale" separately into cost
                    if key == "Wholesale":
                        data["default_price"].append({
                            "variant_id": variant_id,
                            "price": new_price
                        })
                        continue  # Skip further processing for this key
                    
                    # Fetch the price list for the given key
                    price_list = active_price_lists.get(key)

                    if not price_list:
                        print(f"No matching price list found for key: {key}")
                        continue

                    data["data"].append({
                        "variant_id": variant_id,
                        "price_list_id": price_list["price_list_id"],
                        "price": new_price
                    })

        # Update the product price list for the current product ID
        if data["data"]:  # Only proceed if there are updates
            products_list.update_product_price_list(store, product_id, data, "BigCommerce", is_coming_from_google_sheet=True)

    return {"status": "success", "message": "Product price lists updated successfully."}