from flask import request
import logging
from api import APIResource
import traceback
import json
from schemas.mail_template import template_update_schema
from utils import store_util
from settings import mail_service

logger = logging.getLogger()


# get all types
class TemplateTypes(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            query_params = request.args.to_dict()
            store_id = store['id']
            res = mail_service.get_types(store_id, query_params)
            return res, 200
        finally:
            logger.debug("Exiting Template Type GET")

    def get(self, tenant_id, store_id):
        return self.execute_store_request(request, self.get_executor)

# create type


class CreateTemplates(APIResource):
    def post_executor(self, request, token_payload, store, token=None):
        try:
            req_body = request.get_json(force=True)
            if req_body and "name" in req_body:
                store_db = store_util.get_store_db(store)
                res = mail_service.create_type(store, req_body)
                if res['status'] == 200:
                    return {"message": res['message']}, 200
                else:
                    return {"message": res['message']}, 409
            else:
                return {"message": "Please esure that your request body is correct."}, 400
        finally:
            logger.debug("Exiting Template Type POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)

# upadate and delete types


class TypeOperations(APIResource):
    def put_executor(self, request, token_payload, store, type_id):
        try:
            req_body = request.get_json(force=True)
            validated_data = template_update_schema.validate(req_body)
                
            res = mail_service.update_template(validated_data, type_id)

            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409
        finally:
            logger.debug("Exiting Template Type PUT")

    def get_executor(self, request, token_payload, store, type_id):
        try:
            res = mail_service.get_type(type_id)
            return res, 200
        finally:
            logger.debug("Exiting Template Type GET")

    def delete_executor(self, request, token_payload, store, type_id):
        try:
            success = mail_service.delete_by_id(store['id'], type_id)
            if success:
                return {"status": "ok"}, 200
            return {"status": "failed"}, 500
        finally:
            logger.debug("Exiting Template Type DELETE")

    def put(self, type_id):
        return self.execute_store_request(request, self.put_executor, type_id)

    def get(self, type_id):
        return self.execute_store_request(request, self.get_executor, type_id)

    def delete(self, type_id):
        return self.execute_store_request(request, self.delete_executor, type_id)
