from sqlalchemy import Column, DateTime, String, Integer
import new_pgdb as db


class UserCustomerMappingSchema(db.Base):
    __tablename__ = db.DBTables.user_supplier_mapping_table

    user_name = Column(String, primary_key=True)
    suppliers = Column(String)
    modified_at = Column(DateTime)
    email_id = Column(String)

class UserSupplierMappingSchema(db.Base):
    __tablename__ = db.DBTables.supplier_app_user_supplier_mapping

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_name = Column(String)
    email_id = Column(String)
    suppliers = Column(String)
    brand_id = Column(Integer)
    brand_name = Column(String)
    product_id = Column(Integer)
    product_sku = Column(String)
    modified_at = Column(DateTime)
