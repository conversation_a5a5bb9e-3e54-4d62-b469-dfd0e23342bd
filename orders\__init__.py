import datetime
from sqlalchemy import text
import new_pgdb
from new_pgdb import order_consignment_db
from utils import redis_util, store_util, bc, order_util
from new_mongodb import customer_db
import new_utils
from utils.common import convert_to_timestamp

def get_order_status_name(status_id):
    status_mapping = {
        0: "Incomplete",
        1: "Pending",
        2: "Shipped",
        3: "Partially Shipped",
        4: "Refunded",
        5: "Cancelled",
        6: "Declined",
        7: "Awaiting Payment",
        8: "Awaiting Pickup",
        9: "Awaiting Shipment",
        10: "Completed",
        11: "Awaiting Fulfillment",
        12: "Manual Verification Required",
        13: "Disputed",
        14: "Partially Refunded"
    }
    
    if status_id in status_mapping:
        return status_mapping[status_id]
    else:
        return ""

def get_all_orders(store, payload):
    response = {'status': 400}
    conn = new_pgdb.get_connection(store['id'])
    try:
        status_id = int(payload.get('status_id', 0))
        sort_by = str(payload.get('sort_by',''))
        sort_order = str(payload.get('sort_order', ''))
        page = int(payload.get('page', 1))
        limit = int(payload.get('limit', 10))
        filter = payload.get('filter', '')
        is_express_orders = payload.get('is_express_orders', 'false')
        
        condition = ""
        status_id_condition = ""
        if status_id > 0:
            status_id_condition = f" WHERE o.order_status_id = {status_id}"

        search_condition = ""
        if filter:
            search_condition = f"CONCAT(cs.first_name, ' ', cs.last_name) ILIKE '%{filter}%'"

        if status_id_condition and search_condition:
            condition = f"{status_id_condition} AND {search_condition}"
        elif status_id_condition:
            condition = status_id_condition
        elif search_condition:
            condition = f"WHERE {search_condition}"
        order_express = False
        if is_express_orders.lower() == 'true':
            order_express = True                
            total_count_query = f"""SELECT COUNT(*) FROM (SELECT o.order_created_date_time AS date_created, eo.order_id, cs.customer_id, STRING_AGG(DISTINCT oc.order_type, ',') AS order_type
                            FROM express_orders AS eo LEFT JOIN orders AS o ON eo.order_id = o.order_id
                            LEFT JOIN customers AS cs ON eo.customer_id = cs.customer_id
                            LEFT JOIN order_consignment as oc ON o.customer_id = oc.customer_id
                            {condition} GROUP BY o.order_created_date_time, eo.order_id, cs.customer_id) AS subquery""" 
            
            result_count = conn.execute(text(total_count_query))
            total_count = int(result_count.scalar())                               

            query = text(f"""SELECT 
                                o.order_created_date_time AS date_created, 
                                eo.order_id AS id, 
                                o.order_status AS status, 
                                cs.first_name, 
                                cs.last_name, 
                                cs.customer_id, 
                                o.total_including_tax, 
                                o.order_status_id,
                                STRING_AGG(DISTINCT oc.order_type, ',') AS order_type
                            FROM express_orders AS eo
                            LEFT JOIN orders AS o ON eo.order_id = o.order_id
                            LEFT JOIN customers AS cs ON eo.customer_id = cs.customer_id
                            LEFT JOIN order_consignment as oc ON o.customer_id = oc.customer_id
                            {condition}
                            GROUP BY o.order_created_date_time, eo.order_id,
                            o.order_status, cs.first_name, cs.last_name, cs.customer_id, o.total_including_tax, o.order_status_id
                        """)
            
        else:
            order_express = False
            total_count_query = f"""SELECT COUNT(*) FROM (SELECT o.order_created_date_time AS date_created, o.order_id, cs.customer_id, STRING_AGG(DISTINCT oc.order_type, ',') AS order_type
                            FROM orders AS o LEFT JOIN customers AS cs ON o.customer_id = cs.customer_id
                            LEFT JOIN order_consignment AS oc ON o.customer_id = oc.customer_id
                            {condition} GROUP BY o.order_created_date_time, o.order_id, cs.customer_id) AS subquery"""               
                            
            result_count = conn.execute(text(total_count_query))
            total_count = int(result_count.scalar())                                    

            query = text(f"""SELECT 
                                o.order_created_date_time AS date_created, 
                                o.order_id AS id, 
                                o.order_status AS status, 
                                cs.first_name, 
                                cs.last_name, 
                                cs.customer_id, 
                                o.total_including_tax, 
                                o.order_status_id,
                                STRING_AGG(DISTINCT oc.order_type, ',') AS order_type
                            FROM orders AS o
                            LEFT JOIN customers AS cs ON o.customer_id = cs.customer_id
                            LEFT JOIN order_consignment AS oc ON o.customer_id = oc.customer_id
                            {condition}
                            GROUP BY o.order_created_date_time, o.order_id, 
                            o.order_status, cs.first_name, cs.last_name, cs.customer_id, o.total_including_tax, o.order_status_id
                            """)
        
        if sort_by and sort_order:
            query = text(str(query) + f" ORDER BY {sort_by} {sort_order}")

        offset = (page - 1) * limit
        query = text(str(query) + f" LIMIT {limit} OFFSET {offset}")
        
        result = conn.execute(query)           

        res = result.fetchall()

        orders = []
        if res:
            for row in res:
                row_data = {
                    'date_created': convert_to_timestamp(row[0]),
                    'id': row[1],
                    'status': row[2] if row[2] else get_order_status_name(row[7]),
                    'first_name': row[3],
                    'last_name': row[4],
                    'customer_id': row[5],
                    'total_inc_tax': row[6],
                    'status_id': row[7],
                    'order_type': row[8],
                    'is_express_orders': order_express
                }
                orders.append(row_data)
        
        if orders:
            for order in orders:
                customer = redis_util.get_salesforce_customer(store['id'], order['customer_id'])
                if customer:
                    rep = customer['rep']
                    if rep:
                        order['customer_rep_name'] = rep['Name']
                else:
                    order['customer_rep_name']=""
                
                rewards = customer_db.get_order_reward_points(store, order['id'])
                if rewards:
                    order['rewards']=rewards
                else:
                    order['rewards']=0
                                
            limit = int(payload["limit"]) if payload.__contains__("limit") else 1
            page = int(payload["page"]) if payload.__contains__("page") else 1

            data = new_utils.calculate_pagination(orders, page, limit, total_count)
            response['data'] = data
            response['status'] = 200
        else:
            response['data'] = orders
            response['status'] = 200
    except Exception as e:
        error_message = str(e)
        response['status'] = 500
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()       
    return response

def get_bc_order_details(api_data, order_id,payload,store):
    req_body={}
    if 'type' in payload and (payload['type'] =='consignment' or payload['type']=='on-hold'):
        req_body = {
            'method': 'get',
            'url': "v2/orders",  
            'query_params': { 
                'include':'consignments.line_items'
            }
        }
     
    query_params={}
    if "query_params" in req_body and req_body["query_params"]:
        query_params = req_body["query_params"]

    res = bc.call_api(api_data, "GET", "v2/orders/"+order_id , query_params, {})
    order_details = None
    if res and res.status_code == 200:
        order_details = res.json()
        if 'type' in payload :
            order_util.add_product_details(store, payload, order_details, order_id)
            response = {"status": 200, }
            return response
        
        result = order_consignment_db.OrderConsignmentSchema.get_order(store, order_id) 

        if result > 0:
            order_type = order_consignment_db.OrderConsignmentSchema.get_order_type(store, order_id)
            order_details['type']=order_type
        else:
            order_details['type']=""
        customer=customer_db.fetch_customer_by_id(store,order_details['customer_id'])
        
        #get loyalty points
        order_details['reward_points'] = customer_db.fetch_loyalty_points_for_customer(store,order_details['customer_id'])
        #get customer group name
        
        customer_group_name = customer_db.fetch_customer_group_by_id(store, customer['customer_group_id'])
        
        if customer_group_name is not  None:
           order_details["customer_group_name"] = customer_group_name
        else:
           order_details["customer_group_name"] = "" 
        
    return order_details

def get_order(store, order_id):
    bc_api = store_util.get_bc_api_creds(store)
    res = get_bc_order_details(bc_api, order_id, None, store)
    
    return new_utils.parse_json(res)

def get_bc_order_details_with_queryparams(api_data, order_id,query_params):
    res = bc.call_api(api_data, "GET", "v2/orders/" + order_id, query_params, {})

    order_details = None
    if res and res.status_code == 200:
        order_details = res.json()

    return order_details

def get_order_by_order_id(store, order_id):
    bc_api = store_util.get_bc_api_creds(store)
    url = f"v2/orders/{order_id}"
    order = bc.call_api(bc_api, "GET", url)
    if order.status_code == 200:
        return order.json()
    else:
        return []
