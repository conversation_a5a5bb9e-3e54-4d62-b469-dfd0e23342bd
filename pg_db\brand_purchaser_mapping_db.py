from sqlalchemy.dialects.postgresql import insert
import pg_db as db
from sqlalchemy import Boolean, Column, DateTime, String, Integer, ForeignKey, Float
from sqlalchemy.sql import func


class BrandPurchaserMapping(db.Base):
    __tablename__ = "brand_purchaser_mapping"

    id = Column(Integer, primary_key=True, autoincrement=True)
    purchaser_name = Column(String)
    purchaser_email = Column(String, nullable=False)
    brand_name = Column(String)
    brand_id = Column(Integer)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
    created_by = Column(String)
    updated_by = Column(String)
