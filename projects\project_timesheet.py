from sqlalchemy import text
import pg_db
from mongo_db import user_db
import logging
from datetime import datetime, timedelta
from collections import defaultdict
import re
from new_mongodb import get_admin_db_client_for_store_id
from projects.project_task_report import get_filters
from utils.common import convert_timedelta_to_hhmmss, parse_time_format, convert_time_format
from collections import OrderedDict
import pytz

logger = logging.getLogger()


def get_timesheet(store_id, project_id, username, start_date, end_date, report_type, sort_array, timezone="UTC"):
    response = {
        "status": 400,
    }
    db = get_admin_db_client_for_store_id(store_id)
    conn = pg_db.get_connection()
    try:
        if not project_id:
            response['status'] = 200
            response['data'] = []
            return response
        
        # Fetch user data
        user_data = user_db.fetch_user_by_username(username)
        user_id = str(user_data['_id'])

        # Fetch user preferences from MongoDB
        user_pref = db["user_preference"].find_one({"user_id": user_id, "type": report_type})

        project_ids = list(map(int, project_id.split(","))) if isinstance(project_id, str) else project_id

        module_ids = []
        assignees = []
        current_column_ids = []
        priorities = []
        ticket_statuses = []

        if user_pref:
            columns = user_pref.get("columns", {})
            module_ids = columns.get("modules", []) if len(project_ids) == 1 else []
            assignees = columns.get("assignee", [])
            current_column_ids = columns.get("stages", []) if len(project_ids) == 1 else []
            priorities = columns.get("priority", [])
            ticket_statuses = columns.get("ticket_status", [])

        # Validate and set timezone
        try:
            user_timezone = pytz.timezone(timezone)
        except pytz.UnknownTimeZoneError:
            user_timezone = pytz.UTC  # Default to UTC

        # Query to fetch data
        query = """
            SELECT apcl.id, apcl.card_id, apcl.spent_time_diff, apcl.created_by, apc.title,
                    apcl.spent_time_created_at, apc.card_identifier, apc.project_id, apcl.old_value, apcl.new_value
            FROM agile_project_cards_logs apcl
            JOIN agile_project_cards apc ON apcl.card_id = apc.id
            WHERE apc.project_id = ANY(:project_ids) 
                AND apcl.spent_time_diff IS NOT NULL
                AND apcl.spent_time_created_at::DATE >= :start_date ::DATE
                AND apcl.spent_time_created_at::DATE <= :end_date ::DATE
        """
        # Add filters based on user preferences
        if module_ids:
            query += " AND apc.module_id = ANY(:module_ids)"
        if assignees:
            query += " AND apcl.created_by = ANY(:assignees)"
        if current_column_ids:
            query += " AND apc.current_column_id = ANY(:current_column_ids)"
        if priorities:
            query += " AND apc.priority = ANY(:priorities)"
        if ticket_statuses:
            query += " AND apc.status = ANY(:ticket_statuses)"

        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ["customer_name"]:                
                query += f" ORDER BY apcl.created_by {sort_direction}"

        filters_response = get_filters(project_ids, user_pref)

        records = conn.execute(text(query).params(
            project_ids=project_ids,
            start_date=start_date,
            end_date=end_date,
            module_ids=module_ids,
            assignees=assignees,
            current_column_ids=current_column_ids,
            priorities=priorities,
            ticket_statuses=ticket_statuses
        )).fetchall()

        # Generate meta dynamically
        date_format = "%Y-%m-%d"
        start = datetime.strptime(start_date, date_format)
        end = datetime.strptime(end_date, date_format)

        # Generate meta dynamically in reverse order
        meta = {}
        date_map = {}
        day_index = (end - start).days + 1  # Start from the last day

        current_date = end
        while current_date >= start:
            day_key = f"day_{day_index}"
            formatted_date = current_date.strftime("%a")[0] + " " + current_date.strftime("%d / %b %Y")
            meta[day_key] = formatted_date
            date_map[current_date.date()] = day_key
            current_date -= timedelta(days=1)
            day_index -= 1


        # Process fetched records
        customer_data = defaultdict(lambda: defaultdict(list))
        for row in records:
            card_id = row[1]
            spent_time_diff = row[2]
            created_by = row[3]
            ticket_title = row[4]
            created_at = row[5]
            card_identifier = row[6]
            project_id = row[7]

            # Adjust `created_at` to the specified timezone
            created_at = created_at.replace(tzinfo=pytz.utc).astimezone(user_timezone)

            day_key = date_map.get(created_at.date(), None)

            if not day_key:
                continue

        
            # Check if card_id exists and sum spent_time_diff
            existing_entry = next(
                (item for item in customer_data[created_by][card_id]
                if item["spent_time_distribution"].get(day_key)), None)


            if existing_entry:
                # Convert existing time and new time to minutes and sum them
                existing_time = time_to_minutes(existing_entry["spent_time_distribution"][day_key])
                additional_time = time_to_minutes(spent_time_diff)
                total_time = existing_time + additional_time

                # Update the spent time
                existing_entry["spent_time_distribution"][day_key] = minutes_to_time(total_time)
                existing_entry["spent_time_ticket_wise"] = minutes_to_time(
                    time_to_minutes(existing_entry["spent_time_ticket_wise"]) + additional_time
                )
            else:
                converted_minutes = time_to_minutes(spent_time_diff)
                customer_data[created_by][card_id].append({
                    "ticket_title": ticket_title,
                    "card_identifier": card_identifier,
                    "spent_time_ticket_wise": minutes_to_time(converted_minutes),
                    "card_id": card_id,
                    "project_id": project_id,
                    "spent_time_distribution": {
                        day_key: minutes_to_time(converted_minutes)
                    }
                })


        formatted_data = []
        # Format customer data
        for customer_name, tickets in customer_data.items():
            formatted_tickets = []
            total_spent_time = 0  # Initialize total spent time for the customer

            for card_id, ticket_data in tickets.items():
                # Aggregate time distribution per card
                spent_time_distribution = {}
                spent_time_ticket_wise = 0  # Initialize ticket-level spent time

                for ticket in ticket_data:
                    spent_time_distribution.update(ticket["spent_time_distribution"])
                    spent_time_ticket_wise += time_to_minutes(ticket["spent_time_ticket_wise"])

                # Sort spent_time_distribution keys in reverse order
                sorted_distribution = OrderedDict(sorted(spent_time_distribution.items(), key=lambda x: x[0], reverse=True))


                ticket_data[0]["spent_time_distribution"] = sorted_distribution
                ticket_data[0]["spent_time_ticket_wise"] = minutes_to_time(spent_time_ticket_wise)
                formatted_tickets.append(ticket_data[0])

                # Add ticket-level time to customer-level total
                total_spent_time += spent_time_ticket_wise

            # Fetch the user's name
            user_name = user_db.fetch_user_by_username(customer_name)
            if user_name:
                name = user_name.get('name', '')
            else:
                name = 'Unassigned'

            formatted_data.append({
                "customer_name": name,
                "total_spent_time": minutes_to_time(total_spent_time),
                "tickets": formatted_tickets
            })

        # Update response status
        response["status"] = 200
        response["data"] = {
            "customers": formatted_data,
            "filters": filters_response["filters"],
            "meta": meta
        }

    finally:
        conn.close()

    return response

def time_to_minutes(interval_time):
    """Convert an interval string (e.g., '1h 0m' or 'HH:MM:SS') to total minutes."""
    # Handle 'HH:MM:SS' format
    match = re.match(r'(\d+):(\d+):(\d+)', str(interval_time))
    if match:
        hours = int(match.group(1))
        minutes = int(match.group(2))
        seconds = int(match.group(3))  # Even though we ignore seconds, it's good to account for it
        return hours * 60 + minutes

    # Handle '1h 0m' format
    match = re.match(r'(?:(\d+)w)?\s*(?:(\d+)d)?\s*(?:(\d+)h)?\s*(?:(\d+)m)?', str(interval_time))
    if match:
        weeks = int(match.group(1)) if match.group(1) else 0
        days = int(match.group(2)) if match.group(2) else 0
        hours = int(match.group(3)) if match.group(3) else 0
        minutes = int(match.group(4)) if match.group(4) else 0
        total_minutes = (weeks * 5 * 8 * 60) + (days * 8 * 60) + (hours * 60) + minutes
        return total_minutes

    # Fallback for invalid format
    print(f"Unrecognized interval_time format: {interval_time}")
    return 0



def minutes_to_time(total_minutes):
    """Convert total minutes to a time string (e.g., '1h 30m')."""
    hours = total_minutes // 60
    minutes = total_minutes % 60
    return f"{hours}h {minutes}m"

def calculate_time_difference(new_value, old_value):
    """Calculates the difference between new_value and old_value in HH:MM:SS format."""
    new_timedelta = parse_time_format(new_value)
    old_timedelta = parse_time_format(old_value)
    difference = new_timedelta - old_timedelta
    return convert_timedelta_to_hhmmss(difference if difference > timedelta(0) else timedelta(0))
