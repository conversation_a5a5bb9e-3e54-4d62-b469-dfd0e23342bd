import time
import traceback
from utils import bc, store_util
from new_utils import cache_util

def process_edges(edges, sourceKey, node_processor=None):
    result = []
    for edge in edges[sourceKey]['edges']:
        if node_processor:
            result.append(node_processor(edge['node']))
        else:
            node = edge['node']
            if 'entityId' in node:
                node['id'] = node['entityId']
                del node['entityId']
            result.append(node)

    return result

def fetch_all_with_pagination(store, query_builder, page_size, resource_name, resource_processor = None, db_collection = None):
    bc_api = store_util.get_bc_api_creds(store)
    cursor = ""
    retry_count = 0
    max_retry_count = 5
    documents = []
    sleep_time = 10
    result_status = True
    result_message = "Completed successfully"
    while True:
        token = cache_util.get_graphql_token(store['id'])
        if not token:
            return False, "Invalid token"
        query = query_builder.get_paginated_query(page_size, cursor)
        status, res = bc.process_bc_graphql_request(bc_api["store_url"], token["token"], query)
        if status == 200 and res:
            retry_count = 0
            root = res['data']['site'][resource_name]
            cursor = root['pageInfo']['endCursor']
            edges = root['edges']
            if len(edges) > 0:
                for edge in edges:
                    document = edge
                    if "node" in edge:
                        document =  edge['node']
                    if resource_processor:
                        document = resource_processor(document)
                    documents.append(document)
            else:
                break
        elif (status == [401, 403] or page_size == 1) and retry_count < max_retry_count:
            retry_count = retry_count + 1
        elif status in [400, 413] and page_size >= 1:
            page_size = int(page_size * 0.9)
        elif status == 429 and retry_count < max_retry_count:
            retry_count = retry_count + 1
            sleep_time = sleep_time * retry_count
            time.sleep(sleep_time)
        else:
            result_status = False
            result_message = {
                "status": status,
                "message": res
            }
            break
        
        if len(documents) > 0 and db_collection:
            import task
            task.upsert_documents(store, db_collection, documents)

    return result_status, result_message

def fetch_all_by_rest_v2_api(store, api, limit_per_req=250, query_params={}, db_collection=None, db_process_threshold=100, max_resource_count=-1, resource_processor=None):
    retry_count = 0
    max_retry_count = 5
    resources = []
    page = 1
    return_status = True
    return_message = "Completed successfully"
    sleep_time = 20
    total_resources = 0
    processed_resources = 0
    is_last_page = False
    bc_api = store_util.get_bc_api_creds(store)
    try:
        while True:
            if max_resource_count > 0 and (processed_resources + limit_per_req) >= max_resource_count:
                limit_per_req = max_resource_count - processed_resources
                is_last_page = True
            query_params["page"] = page
            query_params["limit"] = limit_per_req
            req_body = {
                "query_params": query_params,
                "method": "GET",
                "url": api
            }
            res = bc.process_api(bc_api, req_body, exclude_meta=False)
            status = res["status_code"]
            if str(status) == "200" or str(status) == "204":
                retry_count = 0
                page = page + 1
                data = res['data']

                if str(status) == "204" or len(data) < limit_per_req:
                    is_last_page = True

                if data and len(data) > 0:
                    if resource_processor:
                        data = resource_processor(data)

                    for obj in data:
                        obj["_id"] = obj["id"]
                        resources.append(obj)

                    if db_collection and len(resources) > 0 and (len(resources) >= db_process_threshold or is_last_page):
                        import task
                        task.upsert_documents(store, db_collection, resources)
                        processed_resources = processed_resources + len(resources)
                        total_resources = processed_resources
                        resources = []
                            
                    if is_last_page:
                        break
            elif str(status) == "429" and retry_count < max_retry_count:
                retry_count = retry_count + 1
                time.sleep(sleep_time)
            else:
                return_status = False
                return_message = {
                    "message": res,
                    "status": status
                }
                return False, return_message
    except Exception as ex:
        return_message = "Exception: " + str(traceback.format_exc())
        return False, return_message
    return return_status, {"total":total_resources, "processed": processed_resources, "message":"Completed successfully"} 

def fetch_all_by_rest_api(store, api, limit_per_req=250, query_params={}, db_collection=None, db_process_threshold=100, max_resource_count=-1, resource_processor=None):
    if "v2" in api:
        return fetch_all_by_rest_v2_api(store, api, limit_per_req, query_params, db_collection, db_process_threshold, max_resource_count, resource_processor)
    retry_count = 0
    max_retry_count = 5
    resources = []
    page = 1
    return_status = True
    return_message = "Completed successfully"
    sleep_time = 20
    total_resources = 0
    processed_resources = 0
    is_last_page = False
    try:
        while True:
            if max_resource_count > 0 and (processed_resources + limit_per_req) >= max_resource_count:
                limit_per_req = max_resource_count - max_resource_count
                is_last_page = True
            query_params["page"] = page
            query_params["limit"] = limit_per_req
            req_body = {
                "query_params": query_params,
                "method": "GET",
                "url": api
            }
            res = bc.process_api(store_util.get_bc_api_creds(store), req_body, exclude_meta=False)
            status = res["status_code"]
            if str(status) == "200":
                retry_count = 0
                page = page + 1
                data = None
                meta = None
                if 'data' in res:
                    if 'data' in res['data']:
                        data = res['data']['data']
                    if 'meta' in res['data']:
                        meta = res['data']['meta']
                if total_resources == 0 and meta and 'pagination' in meta:
                    total_resources = meta['pagination']['total']
                    is_last_page = (total_resources == meta['pagination']['count'])

                if not data and len(data) == 0:
                    break
                else:
                    if resource_processor:
                        data = resource_processor(data)

                    for obj in data:
                        obj["_id"] = obj["id"]
                        resources.append(obj)

                    if len(resources) >= db_process_threshold or is_last_page:
                        if db_collection:
                            import task
                            task.upsert_documents(store, db_collection, resources)
                            processed_resources = processed_resources + len(resources)
                            resources = []
                    if is_last_page:
                        break
            elif str(status) == "429" and retry_count < max_retry_count:
                retry_count = retry_count + 1
                time.sleep(sleep_time)
            else:
                return_status = False
                return_message = {
                    "message": res,
                    "status": status
                }
                return return_status, return_message
        if len(resources) > 0 and db_collection:
            import task
            task.upsert_documents(store, db_collection, resources)
            processed_resources = processed_resources + len(resources)
            resources = []
    except Exception as ex:
        return_message = "Exception: " + str(traceback.format_exc())
        return False, return_message
    return return_status, {"total":total_resources, "processed": processed_resources, "message":"Completed successfully"}