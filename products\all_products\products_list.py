from datetime import datetime, timezone
from bson import ObjectId
from flask import send_file
from fields.products import products_list_fields
from new_mongodb import StoreAdminDBCollections, store_catalog_db, task_db, store_info_db, StoreDBCollections, get_admin_db_client_for_store_id, update_document_in_storefront_collection
from new_mongodb import fetchall_documents_from_storefront_collection, fetch_one_document_from_storefront_collection, fetch_one_document_from_admin_collection, process_documents, insert_document_in_storefront_collection
from new_mongodb import count_documents_storefront_collection, update_document_in_admin_collection, fetchall_documents_from_admin_collection, get_store_db_client_for_store_id, delete_documents_from_storefront_collection
import new_utils
from pymongo.collation import Collation
from plugin import bc_products, bc_price_list, bc_product
import new_pgdb
import csv
import io
import os
from werkzeug.utils import secure_filename
from sqlalchemy import text, bindparam
import logging
import traceback
from mongo_db import user_db, product_db
from PIL import Image
from utils.common import get_paginated_records_price_list, get_paginated_records_updated, calculatePaginationData, paginate_data_postgres, convert_to_timestamp, fetch_static_price_lists, get_month_array_for_meta, processList
from fields.products import product_price_list_fields, price_list_products_dropdown_fields
from new_mongodb import store_admin_db
from pymongo import UpdateOne
from analytics import replenishment
import task
from new_mongodb.cms_db import get_category_by_bc_id
import re
from utils.google_sheet_util import update_price_in_sheet, process_google_sheet_request
from itertools import islice
from new_pgdb.analytics_db import AnalyticsDB
from collections import defaultdict
from decimal import Decimal, ROUND_HALF_UP
import mimetypes
import zipfile
from utils import redis_util
import json
from bson import json_util
logger = logging.getLogger()

IMAGE_EXTENSIONS = ('png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg', 'ico')
DOCUMENT_EXTENSIONS = ('pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'csv', 'xml', 'json', 'zip')

def change_file_name(filename):
    rendom = str(round(datetime.utcnow().timestamp()))
    if ('.' in filename):
        ext = filename.rsplit('.', 1)[1].lower()
        fname = rendom + '.' + ext
        return fname

ALLOWED_EXTENSIONS = set(['png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov', 'wmv', 'mkv', 'webp'])

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# def format_file_size(size):
#     if size < 1024:
#         return f"{size} bytes"
#     elif size < 1024 * 1024:
#         return f"{size / 1024:.2f} KB"
#     elif size < 1024 * 1024 * 1024:
#         return f"{size / (1024 * 1024):.2f} MB"

def _create_reg_ex_query(payload, additionalQuery):
    filterBy = payload["filterBy"]
    filter = payload['filter']
    report_type = payload.get('report_type', '')

    query = {
        "$or": [],
    }

    for i in filterBy:
        query['$or'].append({i: {"$regex": filter, "$options": "i"}},)

    if "type" not in query and "type" in payload:
        query["type"] = payload["type"]

    if "status" in payload:
        if payload["status"] == "active":
            query["is_visible"] = True
        elif payload["status"] == "inactive":
            query["is_visible"] = False
        elif payload["status"] == "out_of_stock":
            query["inventory_level"] = 0
            
    # Attachment filter for msda/pmta reports
    if report_type == 'msda/pmta':
        msda_pmta_report_filter = {
            "$or": [
                {"attachments.msda": {"$exists": True, "$ne": []}},
                {"attachments.pmta": {"$exists": True, "$ne": []}}
            ]
        }
        # Combine with existing conditions using $and if needed
        if query:
            query = {"$and": [msda_pmta_report_filter, query]}
        else:
            query = msda_pmta_report_filter

    query.update(additionalQuery)
    return query

def _get_paginated_records_updated(db_client,collection_name, payload, fields, additionalQuery):
    coll = db_client[collection_name]
    sort = {
        'sort_by': payload.get('sort_by') or 'date_created'
    }

    if payload.get('sort_order') == 'asc':
        sort['sort_order'] = 1
    else:
        sort['sort_order'] = -1

    def create_reg_ex_query(filterBy, filter, payload=None, collection_name=None):
        query = {
            "$or": [],
        }
        if filter:
            for i in filterBy:
                query['$or'].append({i: {"$regex": filter, "$options": "i"}})
        
        # If filter is empty, remove the $or clause to avoid an empty array
        if not query['$or']:
            query = {}
        
        # Map status to correct field for products collection
        if payload and "status" in payload and collection_name == "products":
            if payload["status"] == "active":
                query["is_visible"] = True
            elif payload["status"] == "inactive":
                query["is_visible"] = False
            elif payload["status"] == "out_of_stock":
                query["inventory_level"] = 0

        query.update(additionalQuery)
        return query
    

    limit = int(payload["limit"]) if payload.__contains__("limit") else 10
    page = int(payload["page"]) if payload.__contains__("page") else 1
    skips = payload['skips'] if payload.__contains__('skips') else 0

    query = create_reg_ex_query(payload["filterBy"], payload['filter'], payload, collection_name) if len(
        payload["filterBy"]) else {}
    if coll.name == 'users':
       query['status'] = {"$ne": "deleted"}
    if 'customer_id' in payload:
        query['customer_id'] = payload['customer_id']
    # Calculate number of records to skip ...
    skips = limit * (page - 1)
    collation = Collation(locale='en', strength=2)
    data = coll.find(query, fields).collation(collation).sort(
        sort['sort_by'], sort['sort_order']).skip(skips).limit(limit)
    
    # ProcessList ...
    data = processList(data)

    document_length = coll.count_documents(query)

    return json.loads(json_util.dumps(data)), document_length, page, limit

def get_products(store, payload):
    db_client = get_store_db_client_for_store_id(store['id'])
    payload['filterBy'] = ['name','sku']

    product_ids = []
    attachment_map = {}
    # Get attachments mapping
    if payload.get('report_type', '') == 'msda/pmta':
        attachment_map = _get_products_with_attachments(store['id'])
        product_ids = list(attachment_map.keys())

    additional_query = {"id": {"$in": product_ids}} if product_ids else {}

    # products, total_data_length, page, limit = get_paginated_records_updated(db_client, StoreDBCollections.PRODUCTS, payload, products_list_fields, additional_query)
    products, total_data_length, page, limit = _get_paginated_records_updated(db_client, StoreDBCollections.PRODUCTS, payload, products_list_fields, additional_query)

    for product in products:
        product_id = product.get('id')
        product_id = int(product_id)
        # Add attachments if available
        if product_id in attachment_map:
            product['attachments'] = attachment_map[product_id]
        else:
            product['attachments'] = {"msda": [], "pmta": [], "mediakit":[], "mediakiturl":""}
        category_ids = product.get('categories', [])
        category_names = store_catalog_db.get_categories_by_id(store['id'], category_ids)  # Function to retrieve category names
        product['category_names'] = category_names

    # include pagination data in response ...
    task_data= task_db.fetch_task_by_id(store['id'], "update_product_card_cache")
    data = new_utils.calculate_pagination(products, page, limit, total_data_length)
    last_sync_time = ""
    if task_data:
        last_sync_time = task_data['store_run_status'][store['id']]['last_run_end_time']
    data['meta']['last_sync_time'] = last_sync_time
    return data

def _get_products_with_attachments(store_id):
    """
    Retrieve products that have at least one non-empty array in either msda or pmta attachments.
    Only returns products where either msda or pmta array has at least one element.
    """
    cursor = fetchall_documents_from_storefront_collection(
        store_id,
        StoreDBCollections.MSDA_PMTA_ATTACHMENTS,
        {
            "$or": [
                {"attachments.msda": {"$ne": [], "$exists": True}},
                {"attachments.pmta": {"$ne": [], "$exists": True}},
                {"attachments.mediakit": {"$ne": [], "$exists": True}},
                {"attachments.mediakiturl": {"$ne": "", "$exists": True}}
            ]
        },
        {"product_id": 1, "attachments": 1}
    )
    attachment_map = {}
    for doc in cursor:
        product_id = doc.get('product_id')
        attachments = doc.get('attachments', {})
        # Only include if at least one array is non-empty
        if product_id and (attachments.get('msda') or attachments.get('pmta') or attachments.get('mediakit') or attachments.get('mediakiturl')):
            attachment_map[product_id] = attachments
    return attachment_map

def get_product(store, product_id, version_id):
    res = bc_products.fetch_bc_product(store, product_id)
    variants = res['data']['variants'] if 'variants' in res['data'] else []
    response, meta_dict, meta_static = bc_products.get_customer_group_pricing(store, product_id, variants)
    sku_messages = process_complex_rules_with_conditions(store, product_id)

    complex_rules = []
    for rule in sku_messages:
        # Start constructing the rule_entry without `meta` first
        rule_entry = {
            "rule_id": rule.get('rule_id'),
            "status": rule.get('status'),
            "messages": rule.get('messages', []),
            "variants": rule.get('variants', [])
        }

        # Include nested option details temporarily
        meta = []
        for meta_key in rule.get('meta', []):
            meta.append(meta_key)
            if meta_key in rule:
                rule_entry[meta_key] = {
                    "label": rule[meta_key].get('label'),
                    "values": rule[meta_key].get('values', [])
                }

        # Add meta to the rule_entry at the end
        rule_entry["meta"] = meta
        complex_rules.append(rule_entry)


    for variant in variants:
        if 'option_values' in variant:
            # Concatenate the labels from option_values to form the variant name
            variant_name = " ".join([opt['label'] for opt in variant['option_values']])
            variant['variant'] = variant_name

    categories = res['data']['categories'] if 'categories' in res['data'] else []
    res['data']['categories'] = []  # Clear or initialize the categories list

    for category_id in categories:
        if isinstance(category_id, (int, str)):
            category = get_category_by_bc_id(store, category_id)

            # Ensure the fetched category contains 'id' and 'name'
            if isinstance(category, dict) and 'id' in category and 'name' in category:
                res['data']['categories'].append({
                    'id': category['id'],
                    'name': category['name']
                })

    if version_id is None or version_id == '':
        product_version_data = {}
    else:
        product_version_data = _get_product_version_data(store, product_id, version_id)
    # else:
    #     product_version_data = None

    res['data']['product_version_data'] = product_version_data

    res['data']['customer_group_pricing'] = response
    # res['data']['customer_group_pricing'] = processed_price_list_details

    res['meta'] = meta_dict
    res['meta_static'] = meta_static

    # res = super().find_one({"id": int(product_id)})

    res['data']['is_featured_product'] = False
    res['data']['is_new_product'] = False
    res['data']['is_pre_order_product'] = False

    product_listing_types = store_info_db.fetch_all_product_listing_types(store['id'])

    for product_type in product_listing_types:
        if int(product_id) in product_type['product_ids']:
            if product_type['type'] == 'featured_products':
                res['data']['is_featured_product'] = True
            elif product_type['type'] == 'new_products':
                res['data']['is_new_product'] = True
            elif product_type['type'] == 'preorder_products':
                res['data']['is_pre_order_product'] = True
            elif product_type['type'] == 'liquidated_products':
                res['data']['is_liquidated_product'] = True

    res['data']['complex_rules'] = complex_rules

    # Fetch MSDA and PMTA attachments from the product document (MongoDB)
    product = fetch_one_document_from_storefront_collection(
        store['id'],
        StoreDBCollections.MSDA_PMTA_ATTACHMENTS,
        {"product_id": int(product_id)},
        {"attachments": 1}
    )
    attachments = product.get("attachments", None) if product else None

    # Add attachments to the response if present
    if attachments is not None:
        res['data']['attachments'] = attachments

    return new_utils.parse_json(res)

def process_complex_rules_with_conditions(store, product_id):
    response = bc_products.fetch_product_complex_rules(store, product_id)
    rules = response.get("data", [])

    # To store the final output for all rules
    processed_rules = []

    for rule in rules:
        rule_id = rule["id"]
        status = rule.get("enabled")
        conditions = rule.get("conditions", [])
        messages = []
        variants = []
        options = {}
        meta = []

        # Process conditions
        for condition in conditions:
            variant_id = condition.get("variant_id")
            modifier_id = condition.get("modifier_id")
            modifier_value_id = condition.get("modifier_value_id")

            if variant_id:
                # Fetch SKU for the variant_id
                variant_data = bc_products.fetch_product_variant_by_id(store, product_id, variant_id)
                if variant_data and "data" in variant_data:
                    sku = variant_data["data"].get("sku")
                    if sku:
                        variants.append(sku)
            elif modifier_id:
                # Fetch option display_name for modifier_id
                option_data = bc_products.fetch_product_options(store, product_id, modifier_id)
                if option_data and "data" in option_data:
                    option_label = option_data["data"].get("display_name")
                    if option_label:
                        if modifier_value_id:
                            # Fetch option value label for modifier_value_id
                            value_data = bc_products.fetch_product_option_values(store, product_id, modifier_id, modifier_value_id)
                            if value_data and "data" in value_data:
                                value_label = value_data["data"].get("label")
                                if modifier_id not in options:
                                    options[modifier_id] = {
                                        "label": option_label,
                                        "values": []
                                    }
                                options[modifier_id]["values"].append(value_label)

        # Process messages based on rule attributes
        if rule.get("stop"):
            messages.append("Stop processing more rules")

        price_adjuster = rule.get("price_adjuster")
        if price_adjuster:
            adjuster = price_adjuster.get("adjuster")
            adjuster_value = price_adjuster.get("adjuster_value", 0)

            if adjuster == "relative":
                if adjuster_value > 0:
                    messages.append(f"Add ${adjuster_value} to Price")
                elif adjuster_value < 0:
                    messages.append(f"Remove ${abs(adjuster_value)} from Price")
            elif adjuster is None and adjuster_value != 0:
                messages.append(f"Set the Price to ${adjuster_value}")

        weight_adjuster = rule.get("weight_adjuster")
        if weight_adjuster:
            adjuster = weight_adjuster.get("adjuster")
            adjuster_value = weight_adjuster.get("adjuster_value", 0)

            if adjuster == "relative":
                if adjuster_value > 0:
                    messages.append(f"Add {adjuster_value} KGS to Weight")
                elif adjuster_value < 0:
                    messages.append(f"Remove {abs(adjuster_value)} KGS from Weight")
            elif adjuster is None and adjuster_value != 0:
                messages.append(f"Set the Weight to {adjuster_value}")

        if rule.get("purchasing_disabled"):
            message = rule.get("purchasing_disabled_message", "").strip()
            if message:
                messages.append(message)
            else:
                messages.append("Disable and hide this option")

        if rule.get("image_url"):
            messages.append("Show a different image")

        # Append metadata for options
        meta.extend([f"option_{index + 1}" for index in range(len(options))])

        # Prepare the rule response
        processed_rule = {
            "rule_id": rule_id,
            "status": status,
            "variants": variants,
            "messages": messages,
            "meta": meta
        }

        # Add options to the response
        for index, (modifier_id, option_data) in enumerate(options.items(), start=1):
            processed_rule[f"option_{index}"] = {
                "label": option_data["label"],
                "values": option_data["values"]
            }

        # Add the processed rule to the list
        processed_rules.append(processed_rule)

    return processed_rules



def get_product_versions(store, product_id):
        product = product_db.get_product_versions_by_id(store, product_id)
        version_array = []
        if product is None:
            return version_array
        if (len(product['versions']) > 0):

            versions = product['versions']
            for version in versions:
                data = {}
                data['id'] = int(version['version'])
                data['status'] = version['status']
                if 'created_by' in version:
                    user_name = version['created_by'].get('user_name', '')
                    if user_name:
                        data['title'] = f"{version['version']} ({user_name})"
                    else:
                        data['title'] = str(version['version'])
                else:
                    data['title'] = str(version['version'])
                version_array.append(data)

        return version_array

def _get_product_version_data(store, product_id, version_id):
        result = {}
        active_version = {}
        product_versions = product_db.get_product_versions_by_id(store, product_id)
        if product_versions is None:
            return result

        if (len(product_versions['versions']) == 0):
            active_version = {}
        else:
            versions = product_versions['versions']
            for version in versions:
                if version['version'] == int(version_id):
                    active_version = version
                    break

        result['versions'] = active_version

        return result

def setImage(body):
    response = {
        "status": 400
    }

    file = body.get('image')
    type = body.get('type')
    UPLOAD_FOLDER = os.path.join('images/products/page-builder')

    if file.filename == '':
        response['message'] = "No image selected for uploading"
        response['status'] = 500
        return response

    if file and allowed_file(file.filename):
        newName = change_file_name(file.filename)
        fname = secure_filename(newName)

        if not os.path.exists(UPLOAD_FOLDER):
            os.makedirs(UPLOAD_FOLDER)

        file_path = os.path.join(UPLOAD_FOLDER, fname)

        file.save(file_path)

        # Check the file extension
        _, file_extension = os.path.splitext(file_path)
        file_extension = file_extension.lower()

        if file_extension in ['.png', '.jpg', '.jpeg', '.webp']:
            image = Image.open(file_path)
            original_width, original_height = image.size

            # Specify the desired width for each type
            desired_width = {
                'top_ten_products': 200,
                'full_width_banner': 1656,
                'five_column_banner': 312,
                'four_column_banner': 396,
                'four_column_text_banner': 396
            }

            # Check if the type is in the desired_width dictionary
            if type in desired_width:
                # Calculate the new height to maintain the aspect ratio
                new_height = int((desired_width[type] / original_width) * original_height)

                # Resize the image
                image = image.resize((desired_width[type], new_height))
                image.save(file_path)
            else:
                # If the type is not in the dictionary, save the image without resizing
                image.save(file_path)

            base_path = os.path.join(os.path.abspath(os.getcwd()), UPLOAD_FOLDER, newName)
            if '/app/images' in base_path:
                base_path = base_path.replace('/app/images', '')
            # Only return the file name, not the full path
            response['message'] = base_path
            response['status'] = 200
        else:
            # Handle cases where the file is not an image, but a valid allowed file
            base_path = os.path.join(os.path.abspath(os.getcwd()), UPLOAD_FOLDER, newName)
            if '/app/images' in base_path:
                base_path = base_path.replace('/app/images', '')

            response['message'] = base_path
            response['status'] = 200
    else:
        response['message'] = "Allowed image types are -> png, jpg, jpeg, gif, mp4, avi, mov, wmv, mkv, webp"
        response['status'] = 500

    return response


def update_product(store, req_body, product_id, username):
    if "deleted_custom_fields" in req_body:
        for field in req_body["deleted_custom_fields"]:
            delete_custom_field(store, product_id, field)

    res = bc_products.update_bc_product(store, req_body, product_id)
    if res[1] == 200:
        # Prepare the update_data dictionary based on available fields in res[0]
        update_data = {"$set": {}}  # Using $set to update only specified fields

        data = res[0].get("data", {})

        if "order_quantity_maximum" in data:
            update_data["$set"]["order_quantity_maximum"] = data["order_quantity_maximum"]

        if "order_quantity_minimum" in data:
            update_data["$set"]["order_quantity_minimum"] = data["order_quantity_minimum"]

        if "availability_description" in data:
            update_data["$set"]["availability_description"] = data["availability_description"]

        # Ensure update_data has fields to update before calling the function
        if update_data["$set"]:
            # Update the document in the products collection
            update_document_in_storefront_collection(store['id'], "products", {"_id": int(product_id)}, update_data)

    if "custom_fields" in req_body:
        update_custom_fields_in_db(store, product_id)

    update_versions = _set_product_version(store, req_body, product_id, username)
    update_response = bc_products.update_bc_customer_group_price(store, req_body)
    if update_response:
        if update_response == ({'data': {}, 'meta': {}}, 200) or update_response[1] == 204:
            data = res[0]
            if isinstance(data, dict):
                data["group_pricing_message"] = "Customer Group pricing updated"
        else:
            data = res[0]
            data["group_pricing_message"] = update_response

    # Add the message from update_versions_response to the final response
    if update_versions.get('status') == 200:
        data = res[0]
        if isinstance(data, dict):
            data["versioning_message"] = update_versions['message']
    else:
        data = res[0]
        if isinstance(data, dict):
            data["versioning_message"] = "Failed to save version changes"

    # --- Start Attachment saving logic ---
    if "attachments" in req_body:
        attachments_data = req_body["attachments"]

        # First fetch the product document to get current attachments
        product_doc = fetch_one_document_from_storefront_collection(
            store['id'],
            StoreDBCollections.PRODUCTS,
            {"id": int(product_id)},
            {"attachments": 1}
        )

        # Create a structure for updated attachments
        updated_attachments = {}
        attachments_section = {}
        existing_files = {}

        if product_doc and 'attachments' in product_doc:
            # Track existing files for each attachment type
            for att_type in ['msda', 'pmta']:
                if att_type in product_doc['attachments']:
                    existing_files[att_type] = [attachment['file_path'] for attachment in product_doc['attachments'][att_type]]

        # Process each attachment type (msda, pmta)
        for attachment_type, attachments in attachments_data.items():
            if attachment_type not in ['msda', 'pmta', 'mediakit']:
                continue

            # Get list of new file paths
            new_file_paths = [attachment.get("file_path") for attachment in attachments if attachment.get("file_path")]

            # Find files to delete (files in existing attachments in product document but not in new attachments of req_body)
            files_to_delete = []
            if attachment_type in existing_files:
                files_to_delete = [path for path in existing_files[attachment_type] if path not in new_file_paths]

            # Delete files from filesystem
            for file_path in files_to_delete:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        logger.info(f"Deleted file {file_path} as it's no longer associated with product {product_id}")
                except Exception as e:
                    logger.error(f"Failed to delete file {file_path}: {e}")

            # Process attachments for this type
            processed_attachments = []

            for index, attachment in enumerate(attachments):
                file_path = attachment.get("file_path")
                file_name = attachment.get("file_name")

                if not file_path:
                    continue

                try:
                    # Extract file info from file_path
                    file_ext = file_path.rsplit('.', 1)[1].lower() if '.' in file_path else ''

                    # Get file metadata from the filesystem
                    file_id = str(int(datetime.now(timezone.utc).timestamp() * 1000)) + "_" + str(index)
                    file_size = os.path.getsize(file_path)
                    # file_type = mimetypes.guess_type(file_path)[0] or file_ext
                    upload_date = int(datetime.now(timezone.utc).timestamp())

                    # Create complete attachment data
                    attachment_data = {
                        "id": file_id,
                        "file_name": file_name,
                        "file_path": file_path,
                        "file_size": file_size,
                        "file_type": "attachment" if file_ext in IMAGE_EXTENSIONS else "file" if file_ext in DOCUMENT_EXTENSIONS else "unknown",
                        "upload_date": upload_date
                    }

                    processed_attachments.append(attachment_data)

                except Exception as e:
                    logger.error(f"Failed to process attachment {file_path}: {e}")

            # Add this attachment type to the updated attachments structure
            attachments_section[attachment_type] = processed_attachments
        
        # Handle mediakit_url separately from mediakit
        mediakit_url = attachments_data.get("mediakit_url","")
        if isinstance(mediakit_url, str) and mediakit_url.strip():
            attachments_section["mediakit_url"] = mediakit_url.strip()

        updated_attachments["product_id"] = int(product_id)
        updated_attachments["attachments"] = attachments_section

        _update_msda_pmta_attachments(store['id'], product_id, updated_attachments)
        
    # --- End Attachment saving logic ---

    return res

def _update_msda_pmta_attachments(store_id, product_id, updated_attachments):
    db = get_store_db_client_for_store_id(store_id)
    # Check if both msda and pmta are empty lists
    if not updated_attachments.get("attachments", {}).get("msda") and not updated_attachments.get("attachments", {}).get("pmta") and not updated_attachments.get("attachments", {}).get("mediakit"):
        # Delete the document if both are empty
        db[StoreDBCollections.MSDA_PMTA_ATTACHMENTS].delete_one(
            {"product_id": int(product_id)}
        )
    else:
        # Otherwise, update or insert as usual
        db[StoreDBCollections.MSDA_PMTA_ATTACHMENTS].update_one(
            {"product_id": int(product_id)},
            {"$set": updated_attachments},
            upsert=True
        )

def _set_product_version(store, body, product_id, username):
    response = {
        "status": 400
    }
    created_by = {}
    if username:
        user = user_db.fetch_user_by_username(username)
        created_by = {
            "user_id": str(user['_id']),
            "user_name": user['name']
        }

    # Extract only the versions object from the body
    versions_data = body.get('versions', [])

    product = product_db.get_product_by_id(store, product_id)

    # If the product does not exist, create a new product record
    if not product:
        product = {
            "_id": int(product_id),
            "versions": []
        }
        # Insert the new product into the database
        product_db.insert_product_version(store, product)

    # Get the current versions from the product
    total_versions = len(product['versions'])
    versions = product['versions']

    # If there are existing versions, mark them inactive
    if total_versions > 0:
        last_version = product['versions'][-1]['version']
        for version in versions:
            version['status'] = 'inactive'
    else:
        last_version = 0

    # If the total versions are more than 10, remove the oldest version
    if total_versions >= 10:
        product['versions'].pop(0)

    # Loop through the versions_data and add the new versions
    for version_data in versions_data:
        new_version = {
            "status": version_data.get("status", "inactive"),
            "components": version_data.get("components", []),
            "created_by": created_by,
            "created_at": int(datetime.utcnow().timestamp()),
            "updated_at": int(datetime.utcnow().timestamp()),
            "version": last_version + 1  # Increment version number
        }

        # Append the new version to the versions list
        versions.append(new_version)
        last_version += 1  # Increment the version counter

    # Update the product with the new versions array
    update_obj = {
        "versions": product['versions'],
        "updated_at": int(datetime.utcnow().timestamp())
    }

    product_db.update_product_version(store, {"_id": int(product_id)}, {"$set": update_obj})

    response['message'] = "Version changes saved successfully"
    response['status'] = 200
    return response

def update_custom_fields_in_db(store, product_id):
    db = get_store_db_client_for_store_id(store['id'])
    custom_fields = bc_products.get_bc_custom_fields(store, product_id)
    data = custom_fields.get('data', [])
    db[StoreDBCollections.PRODUCTS].update_one(
        {"_id": int(product_id)},  # Match the product by ID
        {"$set": {"custom_fields": data}}, # Update the custom fields
        upsert=True
    )

def delete_custom_field(store, product_id, custom_field_id):
    bc_products.delete_bc_custom_field(store, product_id, custom_field_id)
    db = get_store_db_client_for_store_id(store['id'])
    result = db[StoreDBCollections.PRODUCTS].update_one(
        {"_id": int(product_id)},  # Match the product by ID
        {"$pull": {"custom_fields": {"id": int(custom_field_id)}}}  # Remove the custom field with matching ID
    )
    if result.modified_count == 0:
        response = {
            "status": 200,
            "message": "Custom field deleted successfully"
        }
    else:
        response = {
            "status": 400,
            "message": "Custom field not found"
        }
    return response

def get_products_by_id(store, product_ids, params, fields):
    params['filterBy'] = ['name','sku']
    sort = {
            'sort_by': params.get('sort_by', None) or 'date_created'
        }
    sort_order = params.get('sort_order', 'desc')
    if sort_order == 'asc':
        sort['sort_order'] = 1
    else:
        sort['sort_order'] = -1

    limit = int(params["limit"]) if params.__contains__("limit") else 10
    page = int(params["page"]) if params.__contains__("page") else 1
    skips = params['skips'] if params.__contains__('skips') else 0

    id_query = {'id': {'$in': product_ids}}

    filterBy = params.get("filterBy", [])
    filter = params.get('filter', None)
    query = {}
    if len(filterBy) > 0 and filter:
        query = {
                "$or": [],
            }
        for i in filterBy:
            query['$or'].append({i: {"$regex": filter, "$options": "i"}},)

    query = {'$and': [query, id_query]} if query else id_query

    skips = limit * (page - 1)
    collation = Collation(locale='en', strength=2)

    data = fetchall_documents_from_storefront_collection(store['id'], StoreDBCollections.PRODUCTS, query=query, projection=fields).collation(collation).sort(sort['sort_by'], sort['sort_order']).skip(skips).limit(limit)

    data = process_documents(data)

    document_length = count_documents_storefront_collection(store['id'], StoreDBCollections.PRODUCTS, query)

    return new_utils.parse_json(data), document_length, page, limit

def get_products_list(store, params):
    data = None
    type = params.get('status', '')
    if type:
        products_data = fetch_one_document_from_admin_collection(store['id'], StoreAdminDBCollections.STORE_INFO_COLLECTION, \
                                                                 {'type': type})
        product_ids = products_data['product_ids']
        products, total_data_length, page, limit = get_products_by_id(store, product_ids, params, products_list_fields)

        for product in products:
            category_ids = product.get('categories', [])
            category_names = store_catalog_db.get_categories_by_id(store['id'], category_ids)  # Function to retrieve category names
            product['category_names'] = category_names

        data = new_utils.calculate_pagination(products, page, limit, total_data_length)
    return data

def update_product_listing(store, body):
    response = {
        'status': 400,
        'message': 'Data is not saved successfully'
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        check = body['checked']
        productType = body['type'].strip()
        productID = body['product_id']
        product = fetch_one_document_from_admin_collection(store['id'], StoreAdminDBCollections.STORE_INFO_COLLECTION, \
                                                                    {'type': productType})
        if product:
            finalproducts = product['product_ids']
            finalproduct = []

            if check == True:
                finalproduct=finalproducts
                finalproduct.append(productID)
            else:
                for p_id in finalproducts:
                    if p_id != productID:
                        finalproduct.append(p_id)

            query = {"_id": ObjectId(str(product['_id']))}
            update_data = {
                            "$set":{
                                "product_ids": finalproduct
                            }
                        }
            id = update_document_in_admin_collection(store['id'], StoreAdminDBCollections.STORE_INFO_COLLECTION, \
                                                    query=query, update_data=update_data)

            if productType == "liquidated_products" and check:
                custom_fields = bc_products.get_bc_custom_fields(store, productID)
                data = custom_fields.get('data', [])
                for field in data:
                    if field["name"].lower() == "pack count":
                        break
                else:
                    query = """SELECT bc_product_id, display_qty FROM bo_bulk_order_products WHERE bc_product_id = :bc_product_id"""
                    result = conn.execute(text(query), {"bc_product_id": productID}).fetchone()
                    if result:
                        display_qty = result[1]
                        display_qty = int(display_qty)
                        req_body = {
                            "custom_fields": [
                                {
                                    "name": "pack count",
                                    "value": str(display_qty)
                                }
                            ]
                        }
                        res = bc_products.update_bc_product(store, req_body, productID)
                        update_custom_fields_in_db(store, productID)

            if productType == "liquidated_products" and not check:
                delete_documents_from_storefront_collection(store_id=store['id'], collection_name=StoreDBCollections.LIQUIDATE_PRODUCT_PRICE_LOGS, query={"product_id": int(productID)})

            response['message'] = "Data saved successfully"
            response['status'] = 200
        else:
            response['message'] = "Please ensure that the given type is correct."
            response['status'] = 404
    except Exception as e:
        logger.error(traceback.format_exc(e))
    finally:
        if conn:
            conn.close()

    return response

def update_product_with_csv(store, req_body):
    response = {
        "status": 400
    }

    conn = new_pgdb.get_connection(store['id'])
    try:
        with conn.begin():
            # Extracting file name from request body
            file_name = req_body.filename

            uploaded_file = req_body.read()

            # Validate CSV data
            required_fields = ['price_list_id', 'sku', 'price']
            missing_fields = []
            empty_fields = []

            with io.StringIO(uploaded_file.decode('utf-8')) as file:
                reader = csv.DictReader(file)

                for field in required_fields:
                    if field not in reader.fieldnames:
                        missing_fields.append(field)

                for row in reader:
                    for field in required_fields:
                        if not row.get(field):
                            empty_fields.append(field)

            if missing_fields:
                status = "Failed"
                message = f"Missing fields in CSV data: {', '.join(missing_fields)}"
                query = text("INSERT INTO csv_upload_details (file_name, date, status) VALUES (:file_name, CURRENT_TIMESTAMP, :status)")
                query = query.params(file_name=file_name, status=status)
                conn.execute(query)
                response['status'] = status
                response['message'] = message
                return response

            if empty_fields:
                status = "Failed"
                message = f"Empty fields in CSV data: {', '.join(empty_fields)}"
                query = text("INSERT INTO csv_upload_details (file_name, date, status) VALUES (:file_name, CURRENT_TIMESTAMP, :status)")
                query = query.params(file_name=file_name, status=status)
                conn.execute(query)
                response['status'] = status
                response['message'] = message
                return response

            # Process CSV data
            customer_group_pricing = []
            with io.StringIO(uploaded_file.decode('utf-8')) as file:
                reader = csv.DictReader(file)
                for row in reader:
                    customer_group_pricing.append({
                        "price_list_id": int(row['price_list_id']),
                        "sku": row['sku'],
                        "currency": "usd",
                        "price": float(row['price'])
                    })

            formatted_data = {"customer_group_pricing": customer_group_pricing}

            # Update BigCommerce customer group pricing
            update_response = bc_products.update_bc_customer_group_price(store, formatted_data)

            status = "Completed" if update_response == ({'data': {}, 'meta': {}}, 200) else "Failed"

            # Insert into CSV upload details table
            query = text("INSERT INTO csv_upload_details (file_name, date, status) VALUES (:file_name, CURRENT_TIMESTAMP, :status)")
            query = query.params(file_name=file_name, status=status)
            conn.execute(query)

            if status == "Completed":
                response['status'] = 200
                response['message'] = "Customer group pricing updated"
            else:
                response['status'] = 500
                response['message'] = "Customer group pricing updation failed"
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response


def get_csv_upload_details(store, page, limit, sort_array=[]):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        total_count_query = text("SELECT COUNT(*) FROM csv_upload_details;")
        result_count = conn.execute(total_count_query)
        total_count = int(result_count.scalar())

        query = "SELECT * FROM csv_upload_details"

        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            if sort_array[0] in ["date"]:
                query += f" ORDER BY {sort_array[0]} {sort_direction}"

        offset = (page - 1) * limit
        query += f" LIMIT {limit} OFFSET {offset}"

        result = conn.execute(text(query))
        file_attributes = []
        for row in result.fetchall():
            file_details = {
                'id': row[0],
                'file_name': row[1],
                'date': convert_to_timestamp(row[2]),
                'status': row[3]
            }
            file_attributes.append(file_details)

        data = new_utils.calculate_pagination(file_attributes, page, limit, total_count)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 404
            response['message'] = 'No data found.'
    finally:
        if conn:
            conn.close()
    return response


def get_tax_classes(store):
    response = {
        "status": 400
    }
    tax_classes = bc_products.fetch_bc_tax_classes(store)
    if tax_classes:
        response['status'] = 200
        response['data'] = new_utils.parse_json(tax_classes)
    else:
        response['status'] = 404
        response['message'] = "data not found"
    return response

def get_product_price_lists(store, page, limit, sort_by, filter, user, tag_filter=None, classification_filter=None, classified_as_filter=None, products_filter=None, user_filter=None, supplier_filter=None, top_products_filter=None, cost_margin_filter=None):
    db = get_admin_db_client_for_store_id(store['id'])
    conn = new_pgdb.get_connection(store['id'])
    store_db = get_store_db_client_for_store_id(store['id'])
    try:

        # Extract field and sort order from sort_by
        field, sort_order = ('created_at', 'desc')
        if '/' in sort_by:
            field, order = sort_by.split('/')
            sort_order = 'asc' if order == '1' else 'desc'

        payload = {
            "page": page,
            "limit": limit,
            "sort_by": field,
            "sort_order": sort_order,
            "filterBy": ["parent_product_name", "parent_product_sku", "variant_name", "variant_sku"],
            "filter": filter
        }

        # Fetch price lists from BigCommerce
        role_id = user.get('role_id')
        static_price_lists = fetch_static_price_lists(store['id'])
        price_lists, _ = bc_price_list.fetch_price_lists(store)

        # fetching data from collection to show margin and sales percentage
        assignments = list(store_db[StoreDBCollections.PRICE_LIST_ASSIGNMENT].find({
            "price_list_id": {"$exists": True},
            "customer_group_id": {"$exists": True}
        }))

        # Step 2: Build a one-to-one mapping of price_list_id -> customer_group_id
        price_list_to_customer_group = {
            assignment["price_list_id"]: assignment["customer_group_id"]
            for assignment in assignments
            if "price_list_id" in assignment and "customer_group_id" in assignment
        }

        # Merge static price lists
        price_lists['data'].extend([{
            'id': pl['id'], 'name': pl['name'], 'date_created': None, 'date_modified': None, 'active': pl['active']
        } for pl in static_price_lists])

        # Role-specific filtering
        if role_id == '67f5f9c43c97938e59357472':
            price_lists['data'] = [pl for pl in price_lists['data'] if pl['id'] not in (14, 15)]
        elif role_id == '67fd12676af694b36923ce09':
            price_lists['data'] = [pl for pl in price_lists['data'] if pl['id'] in (13, 15, 52)]

        # Move price_list_id 7 to the top if active
        sorted_price_lists = sorted(price_lists['data'], key=lambda x: (x['id'] != 7, not x['active']))

        # Now, find positions of 14 and 15
        index_14 = next((i for i, pl in enumerate(sorted_price_lists) if pl['id'] == 14), None)
        index_15 = next((i for i, pl in enumerate(sorted_price_lists) if pl['id'] == 15), None)

        # If both exist, swap them
        if index_14 is not None and index_15 is not None:
            sorted_price_lists[index_14], sorted_price_lists[index_15] = sorted_price_lists[index_15], sorted_price_lists[index_14]

        # Generate price list mappings
        price_list_meta = {
            f"price_{i+1}": {'id': pl['id'], 'name': pl['name'], 'active': pl['active']}
            for i, pl in enumerate(sorted_price_lists, start=0) if pl['active']
        }
        price_list_map = {pl['id']: f"price_{i+1}" for i, pl in enumerate(sorted_price_lists, start=0) if pl['active']}
        reversed_price_list_map = {v: k for k, v in price_list_map.items()}

        variant_sku_list = []
        filter_query = []

        def fetch_skus(query, param_name, values):
            result = conn.execute(text(query), {param_name: tuple(values)}).fetchall()
            return [row[0] for row in result]

        if tag_filter:
            tag_skus = fetch_skus("SELECT sku FROM product_tags WHERE tag_id IN :tag_filter_list", 'tag_filter_list', tag_filter.split(','))
            tag_query = {"parent_product_sku": {"$in": tag_skus}}
            filter_query.append(tag_query)

        if tag_filter:
            tag_skus = fetch_skus("SELECT variant_sku FROM product_tags WHERE tag_id IN :tag_filter_list", 'tag_filter_list', tag_filter.split(','))
            variant_sku_list = [sku for sku in tag_skus if sku is not None]

        if classified_as_filter:
            classified_skus = fetch_skus("SELECT parent_sku FROM replenishment_dashboard WHERE classified_as_id IN :classified_as_filter_list", 'classified_as_filter_list', classified_as_filter.split(','))
            classified_as_query = {"parent_product_sku": {"$in": classified_skus}}
            filter_query.append(classified_as_query)

        if classification_filter:
            classification_skus = fetch_skus("SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE classification IN :classification_filter_list AND parent_sku IS NOT NULL AND parent_sku != ''", 'classification_filter_list', classification_filter.split(','))
            classification_query = {"parent_product_sku": {"$in": classification_skus}}
            filter_query.append(classification_query)

        if user_filter:
            query = """
                SELECT ARRAY_AGG(DISTINCT suppliers)
                FROM user_supplier_mapping
                WHERE user_name = :user_name
            """
            res = conn.execute(text(query), {'user_name': user_filter})
            suppliers_list = res.fetchone()[0] or []

            query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier = ANY(:suppliers_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
            result = conn.execute(text(query), {'suppliers_list': suppliers_list}).fetchall()
            user_skus = []
            for row in result:
                user_skus.append(row[0])
            user_query = {"parent_product_sku": {"$in": user_skus}}
            filter_query.append(user_query)

        if supplier_filter:
            supplier_filter_list = [supplier.strip() for supplier in supplier_filter.split(';')]
            query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier ILIKE ANY(:supplier_filter_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
            result = conn.execute(text(query), {'supplier_filter_list': supplier_filter_list}).fetchall()
            supplier_skus = [row[0] for row in result]
            supplier_query = {"parent_product_sku": {"$in": supplier_skus}}
            filter_query.append(supplier_query)

        if products_filter:
            filter_query.append({"parent_product_id": {"$in": list(map(int, products_filter.split(',')))}})

        if top_products_filter:
            top_n = int(top_products_filter)
            query = text(f"""
                SELECT product_id
                FROM {AnalyticsDB.get_products_revenue_table()}
                WHERE order_date >= NOW() - INTERVAL '30 days'
                GROUP BY product_id
                ORDER BY SUM(revenue) DESC
                LIMIT :top_n
            """)

            result = conn.execute(query, {'top_n': top_n}).fetchall()
            top_product_ids = [row[0] for row in result]

            if top_product_ids:
                top_products_query = {"parent_product_id": {"$in": top_product_ids}}
                filter_query.append(top_products_query)
                payload["product_ids"] = top_product_ids

        if cost_margin_filter:
            cost_margin_filter = int(cost_margin_filter)
            if cost_margin_filter > 0:
                filter_query.append({
                    "cost_margin": {"$gte": 0, "$lte": cost_margin_filter}
                })
            else:
                filter_query.append({
                    "cost_margin": {"$gte": cost_margin_filter, "$lte": 0}
                })

        top_100_product_ids_set = set()

        # --- Always fetch top 100 for static flagging ---
        top_100_query = text(f"""
            SELECT product_id
            FROM {AnalyticsDB.get_products_revenue_table()}
            WHERE order_date >= NOW() - INTERVAL '30 days'
            GROUP BY product_id
            ORDER BY SUM(revenue) DESC
            LIMIT 100
        """)
        top_100_result = conn.execute(top_100_query).fetchall()
        top_100_product_ids_set = {row[0] for row in top_100_result}

        additional_query = {"$and": [{"syncing": False}] + filter_query} if filter_query else {"syncing": False}

        products, total_data_length, page, limit = get_paginated_records_price_list(
            db, StoreAdminDBCollections.PRODUCT_PRICE_LISTS, payload, product_price_list_fields, additional_query
        )

        # Extract variant SKUs from product variants
        all_parent_skus = [product['parent_product_sku'] for product in products]

        all_parent_product_ids = list({int(product['parent_product_id']) for product in products if 'parent_product_id' in product})

        month_names, day_difference = get_month_array_for_meta(6)

        # ✅ NEW: Fetch month_1 to month_7 from analytics.replenishment_products in PG
        if all_parent_product_ids:
            product_data = _fetch_margin_and_sales_percentage(store['id'], all_parent_product_ids)

            # product_data = []
            # Step 1: Build product_data_map for fast lookup
            product_data_map_for_margin_and_sales = {
                (item['product_id'], item['customer_group_id']): {
                    "margin_percentage": item['margin_percentage'],
                    "sales_percentage": item['sales_percentage']
                }
                for item in product_data
            }

            customer_specific_prices = _fetch_customer_specific_prices(store['id'], all_parent_product_ids)

        if all_parent_skus != []:
            classification_query = text("""
                SELECT DISTINCT
                    sc.parent_sku,
                    sc.classification,
                    sc.primary_supplier,
                    usm.user_name
                FROM skuvault_catalog sc
                LEFT JOIN user_supplier_mapping usm
                    ON sc.primary_supplier = usm.suppliers
                WHERE sc.parent_sku IN :parent_product_skus
            """)

            classification_results = conn.execute(classification_query, {'parent_product_skus': tuple(all_parent_skus)}).fetchall()

            classification_map = {}

            for parent_sku, classification, primary_supplier, user_name in classification_results:
                if parent_sku in classification_map:
                    classification_map[parent_sku]['classifications'].add(classification)
                else:
                    classification_map[parent_sku] = {
                        'classifications': {classification},  # use a set for uniqueness
                        'primary_supplier': primary_supplier,
                        'user_name': user_name
                    }

            # Convert classifications set to a comma-separated string
            for sku in classification_map:
                classification_map[sku]['classifications'] = ", ".join(classification_map[sku]['classifications'])

        else:
            classification_map = {}


        # Fetch user data in bulk
        updated_by_users = {product.get('updated_by') for product in products if product.get('updated_by')}
        user_data_map = user_db.fetch_users_by_usernames(updated_by_users) if updated_by_users else {}

        price_list_update_map = {}

        # if parent_product_ids:
        #     query = text("""
        #         SELECT product_id, updated_at, updated_by
        #         FROM pricelist_change_logs
        #         WHERE product_id IN :parent_product_ids AND is_active = TRUE
        #     """)
        #     print(query)
        #     result = conn.execute(query, {'parent_product_ids': tuple(parent_product_ids)}).fetchall()

        #     price_list_update_map = {
        #         row[0]: {'updated_at': row[1], 'updated_by': row[2]} for row in result
        #     }

        # Loop over each product and calculate the price at the product level
        for product in products:
            # Check price_list_update_data collection for updated fields
            parent_product_sku = product.get('parent_product_sku', None)
            product_updated_at = product.get('updated_at')
            product['updated_at'] = convert_to_timestamp(product.get('updated_at'))
            product['created_at'] = convert_to_timestamp(product.get('created_at'))
            product['inventory_level'] = product.get('inventory_level', 0)
            product["top_100"] = product['parent_product_id'] in top_100_product_ids_set

            # Calculate total_sum of months
            if role_id != "67fd12676af694b36923ce09":
                # monthly_data = product_monthly_data_map.get(parent_product_id, {})
                for i in range(1, 8):
                    product[f'month_{i}'] = product.get(f'month_{i}', None)

            product['turn_rate'] = product.get('turn_rate', 0)
            product['weeks_on_hand'] = product.get('weeks_on_hand', 0)

            # Apply batch-fetched price list updates
            # if parent_product_id in price_list_update_map:
            #     update_data = price_list_update_map[parent_product_id]
            #     price_list_updated_at_str = update_data['updated_at']

            #     if product_updated_at and price_list_updated_at_str:
            #         product_updated_at_dt = datetime.strptime(product_updated_at, "%Y-%m-%dT%H:%M:%SZ")
            #         price_list_updated_at_dt = datetime.strptime(price_list_updated_at_str, "%Y-%m-%dT%H:%M:%SZ")

            #         if product_updated_at_dt <= price_list_updated_at_dt or abs((product_updated_at_dt - price_list_updated_at_dt).total_seconds()) <= 3:
            #             product['updated_at'] = convert_to_timestamp(price_list_updated_at_dt)
            #             product['updated_by'] = update_data['updated_by']


            product['classification'] = classification_map.get(parent_product_sku, {}).get('classifications', "")
            product['primary_supplier'] = classification_map.get(parent_product_sku, {}).get('primary_supplier', "")
            product['purchaser'] = classification_map.get(parent_product_sku, {}).get('user_name', "")


            # Assign user-friendly name for updated_by
            updated_by = product.get('updated_by')
            product['updated_by'] = user_data_map.get(updated_by, {}).get("name", updated_by)

            product['customer_specific_price'] = customer_specific_prices.get(product['parent_product_id'], "")

            # Filter variants by variant_sku_list
            product['variants'] = [v for v in product['variants'] if v.get('variant_sku') in variant_sku_list] or product['variants']

            # Initialize pricing data
            price_list_data = {key: {"min": None, "max": None} for key in price_list_meta}
            variant_prices = []
            variant_costs = []

            for variant in product['variants']:
                variant_price = product['default_price'] if variant.get('variant_price') is None else variant.get('variant_price')
                cost = variant.get('variant_cost', None)

                if variant_price is not None:
                    variant_prices.append(variant_price)
                if cost is not None:
                    variant_costs.append(cost)

                # Process price list efficiently
                for price in variant['price_list']:
                    price_list_key = price_list_map.get(price['price_list_id'])
                    if price_list_key:
                        price_value = float(price['price'])

                        current_min = price_list_data[price_list_key]["min"]
                        current_max = price_list_data[price_list_key]["max"]

                        if current_min is None or price_value < current_min:
                            price_list_data[price_list_key]["min"] = price_value
                        if current_max is None or price_value > current_max:
                            price_list_data[price_list_key]["max"] = price_value

            # Now format the result as required (string range or single value)
            for key in price_list_data:
                min_price = price_list_data[key]["min"]
                max_price = price_list_data[key]["max"]
                if min_price is None:
                    price_list_data[key] = ""
                elif min_price == max_price:
                    price_list_data[key] = str(min_price)
                else:
                    price_list_data[key] = f"{min_price}-{max_price}"

            # Set default_price as a range or single value
            if variant_prices:
                product_default_price = product['default_price']
                product['default_price'] = f"{product['default_price']}-{max(variant_prices)}" if len(set(variant_prices)) > 1 else str(product['default_price'])

            # Set cost_range as a range or single value
            product_cost_price = 0
            if variant_costs:
                product['cost_range'] = f"{min(variant_costs)}-{max(variant_costs)}" if len(set(variant_costs)) > 1 else str(min(variant_costs))
                product_cost_price = min(variant_costs)
            else:
                product['cost_range'] = None

            if product_cost_price > 0 and product_default_price > 0:
                product['default_price_margin'] = round(((product_default_price - product_cost_price) / product_cost_price) * 100, 2)
            else:
                product['default_price_margin'] = 0

            deafult_price_sales = product_data_map_for_margin_and_sales.get(
                (product["parent_product_id"], 1),
                {"margin_percentage": 0.0, "sales_percentage": 0.0}
            )
            product['default_price_sales'] = deafult_price_sales["sales_percentage"]

            product["price_list"] = []

            if not product_cost_price or product_cost_price == '':
                product_cost_price = 0
            #logger.error(f"product_cost_price: {product_cost_price}")
            product_cost_price = float(product_cost_price)
            for key, value in price_list_meta.items():
                price_list_id = value["id"]
                customer_group_id = price_list_to_customer_group.get(price_list_id)

                margin_sales = product_data_map_for_margin_and_sales.get(
                    (product["parent_product_id"], customer_group_id),  # Assuming product_id is here
                    {"margin_percentage": 0.0, "sales_percentage": 0.0}
                )

                price_list_price = price_list_data.get(key, '')
                if '-' in price_list_price:
                    min_price, max_price = map(float, price_list_price.split('-'))
                    price_list_price = min_price
                if not price_list_price or price_list_price == '':
                    price_list_price = 0
                price_list_price = float(price_list_price)
                margin_per = 0
                if price_list_price > 0 and product_cost_price > 0:
                    margin_per = round(((price_list_price - product_cost_price) / product_cost_price) * 100, 2)

                #logger.error(f"price_list_id: {price_list_id}, margin: {margin_per}")
                product["price_list"].append({
                    key: price_list_data[key],
                    "price_list_id": price_list_id,
                    "margin_percentage": margin_per,
                    "sales_percentage": margin_sales["sales_percentage"]
                })


            # Remove variants from final output (optional)
            if 'variants' in product:
                del product['variants']

            # Process variant price list updates
            # for variant in product['variants']:
            #     variant['parent_product_id'] = product['parent_product_id']
            #     variant['parent_product_name'] = product['parent_product_name']

            #     updated_price_list = []
            #     for price_list_key, price_list_name in price_list_meta.items():
            #         price_list_id = reversed_price_list_map.get(price_list_key)
            #         price_entry = {'price_list_id': price_list_id, price_list_key: ""}

            #         for price in variant['price_list']:
            #             if price['price_list_id'] == price_list_id:
            #                 price_entry[price_list_key] = str(price.pop('price'))
            #                 break

            #         updated_price_list.append(price_entry)

            #     variant['price_list'] = updated_price_list

        data = calculatePaginationData(products, page, limit, total_data_length)

        # Call get_table_columns and pass 'product_inquiries' as the type
        query_params = {'type': 'product_pricelist'}
        columns_response = replenishment.get_table_columns(store, user, query_params)

        if columns_response and 'data' in columns_response and 'columns' in columns_response['data']:
            data['meta']['columns'] = columns_response['data'].get('columns')

        data['meta']['price_lists'] = price_list_meta
        data['meta']['months'] = month_names if role_id != "67fd12676af694b36923ce09" else {}
        return data
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        if conn:
            conn.close()

def _fetch_margin_and_sales_percentage(store_id, product_ids):
    conn = new_pgdb.get_connection(store_id)
    try:
        query = text("""WITH product_filter AS (
                        SELECT unnest(CAST(:product_ids AS int[])) AS product_id
                        )
                        SELECT
                            li.product_id,
                            li.customer_group_id,
                            SUM(li.revenue) AS revenue,
                            SUM(li.total_cost) AS total_cost,
                            SUM(li.profit) AS profit
                        FROM analytics.profitability_product_customer_groups li
                        JOIN product_filter pf ON pf.product_id = li.product_id
                        WHERE li.order_date >= current_date - interval '30 day'
                        GROUP BY li.product_id, li.customer_group_id;
                """)
        result = conn.execute(query, {"product_ids": list(product_ids)}).fetchall()

        # Step 1: Aggregate values in one loop
        grouped_data = []
        total_revenue_per_product = defaultdict(Decimal)

        for row in result:
            product_id = row[0]
            customer_group_id = row[1]
            revenue = Decimal(str(row[2] or 0))

            total_revenue_per_product[product_id] += revenue

            grouped_data.append({
                'product_id': product_id,
                'customer_group_id': customer_group_id,
                'revenue': revenue
            })

        # Step 2: Build final list with percentages (minimal loop)
        product_data = []
        for item in grouped_data:
            revenue = item['revenue']
            product_id = item['product_id']

            sales_pct = (revenue * 100 / total_revenue_per_product[product_id]).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP) if total_revenue_per_product[product_id] > 0 else Decimal('0.00')

            product_data.append({
                'product_id': product_id,
                'customer_group_id': item['customer_group_id'],
                'margin_percentage': 0,
                'sales_percentage': float(sales_pct)
            })

        return product_data

    except Exception as e:
        logger.error("Error in _fetch_margin_and_sales_percentage:\n" + traceback.format_exc())
        raise e

    finally:
        conn.close()


def _fetch_customer_specific_prices(store_id, product_ids):
    conn = new_pgdb.get_connection(store_id)
    try:
        if not product_ids:
            return {}

        query = """
            SELECT product_id, MIN(price) AS min_price, MAX(price) AS max_price
            FROM product_customer_price
            WHERE product_id = ANY(:product_ids)
            GROUP BY product_id;
        """
        result = conn.execute(text(query), {"product_ids": product_ids}).fetchall()

        # Map product_id -> price or price range
        price_map = {}
        for row in result:
            product_id, min_price, max_price = row
            if min_price == max_price:
                price_map[product_id] = f"{min_price:.2f}"
            else:
                price_map[product_id] = f"{min_price:.2f}-{max_price:.2f}"

        return price_map

    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        conn.close()


def get_product_price_list_tags(store_id):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store_id)

    try:
        query = text(
            """
            SELECT id, tag
            FROM tags
            """
        )
        result = conn.execute(query)
        data = result.fetchall()

        # Ensure that 'row' is a tuple with exactly two elements (id, tag)
        data_as_dict = [{"id": row[0], "tag": row[1]} for row in data]

        response["status"] = 200
        response["data"] = data_as_dict
    except Exception as e:
        logger.error(traceback.format_exc())

    finally:
        conn.close()
    return response

def get_product_price_list_classifications_and_suppliers(store_id, user=None):
    response = {"status": 400}
    conn = new_pgdb.get_connection(store_id)
    try:
        if user:
            query = text("""
                WITH user_suppliers AS (
                    SELECT DISTINCT suppliers
                    FROM user_supplier_mapping
                    WHERE user_name = :user_name
                )
                SELECT
                    ARRAY_AGG(DISTINCT classification) AS classifications,
                    ARRAY_AGG(DISTINCT primary_supplier) AS suppliers
                FROM skuvault_catalog
                WHERE classification IS NOT NULL AND classification != ''
                AND primary_supplier IS NOT NULL AND primary_supplier != ''
                AND primary_supplier IN (SELECT suppliers FROM user_suppliers)
            """)
            result = conn.execute(query, {"user_name": user}).fetchone()
        else:
            query = text("""
                SELECT
                    ARRAY_AGG(DISTINCT classification) AS classifications,
                    ARRAY_AGG(DISTINCT primary_supplier) AS suppliers
                FROM skuvault_catalog
                WHERE classification IS NOT NULL AND classification != ''
                AND primary_supplier IS NOT NULL AND primary_supplier != ''
            """)
            result = conn.execute(query).fetchone()

        response["status"] = 200
        response["data"] = {
            "classifications": result[0] if result[0] else [],
            "suppliers": result[1] if result[1] else []
        }

    except Exception as e:
        logger.error(traceback.format_exc())
        response["status"] = 422
        response["message"] = str(e)

    finally:
        conn.close()

    return response


def get_filtered_products_dropdown(store, page, limit, search, classification_filter, user_filter=None, supplier_filter=None, hide_products=False):
    response = {
        "status": 400,
    }
    db = get_admin_db_client_for_store_id(store['id'])
    conn = new_pgdb.get_connection(store['id'])
    try:
        total_data_length = 0
        payload = {
            "sort_by": "created_at",
            "sort_order": "desc",
            "filterBy": ["parent_product_name", "parent_product_sku", "variant_name", "variant_sku"],
            "filter": search
        }
        filter_query = []

        if classification_filter:
            query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE classification IN :classification_filter_list AND parent_sku IS NOT NULL AND parent_sku != ''"
            # classification_skus = fetch_skus("SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE classification IN :classification_filter_list AND parent_sku IS NOT NULL AND parent_sku != ''", 'classification_filter_list', classification_filter.split(','))
            classification_skus = conn.execute(text(query), {'classification_filter_list': tuple(classification_filter.split(','))}).fetchall()
            classification_skus = [row[0] for row in classification_skus]
            classification_query = {"parent_product_sku": {"$in": classification_skus}}
            filter_query.append(classification_query)

        if user_filter:
            query = """
                SELECT ARRAY_AGG(DISTINCT suppliers)
                FROM user_supplier_mapping
                WHERE user_name = :user_name
            """
            res = conn.execute(text(query), {'user_name': user_filter})
            suppliers_list = res.fetchone()[0] or []

            query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier = ANY(:suppliers_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
            result = conn.execute(text(query), {'suppliers_list': suppliers_list}).fetchall()
            user_skus = []
            for row in result:
                user_skus.append(row[0])
            user_query = {"parent_product_sku": {"$in": user_skus}}
            filter_query.append(user_query)

        if supplier_filter:
            supplier_filter_list = [supplier.strip() for supplier in supplier_filter.split(';')]
            query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier ILIKE ANY(:supplier_filter_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
            result = conn.execute(text(query), {'supplier_filter_list': supplier_filter_list}).fetchall()
            supplier_skus = [row[0] for row in result]
            supplier_query = {"parent_product_sku": {"$in": supplier_skus}}
            filter_query.append(supplier_query)

        # Build the base additional query
        additional_conditions = [{"syncing": False}] + filter_query

        if hide_products:
            additional_conditions.append({"parent_product_sku": {"$exists": True, "$ne": ""}})

        additional_query = {"$and": additional_conditions} if additional_conditions else {}

        # Check for pagination
        if page and limit:
            page = int(page)
            limit = int(limit)
            payload.update({
                "page": page,
                "limit": limit
            })

            products, total_data_length, page, limit = get_paginated_records_price_list(
                db, StoreAdminDBCollections.PRODUCT_PRICE_LISTS, payload, price_list_products_dropdown_fields, additional_query
            )
            if products:
                final_result = {
                    "data": products,
                    "meta": {
                        "current_page": page,
                        "next_page": (page + 1 if page and limit and (page * limit) < total_data_length else None),
                        "total_count": total_data_length
                    }
                }
                response['data'] = final_result
                response['status'] = 200
            else:
                response['status'] = 200
                response['data'] = {
                    "data": [],
                    "meta": {
                        "current_page": page,
                        "next_page": None,
                        "total_count": total_data_length
                    }
                }
        else:
            search_query = {}
            if search:
                search_query = {
                    "$or": [
                        {"parent_product_name": {"$regex": search, "$options": "i"}},  # Case-insensitive search
                        {"parent_product_sku": {"$regex": search, "$options": "i"}},  # Case-insensitive search
                    ]
                }

            # Update the additional_query with the search filter
            additional_query.update(search_query)

            products_cursor = fetchall_documents_from_admin_collection(store['id'], StoreAdminDBCollections.PRODUCT_PRICE_LISTS, additional_query, price_list_products_dropdown_fields)
            products = list(products_cursor)
            for product in products:
                product['id'] = str(product['_id'])
                del product['_id']

            response['data'] = products
            response['status'] = 200


    except Exception as e:
        logger.error(traceback.format_exc())
    finally:
        conn.close()
    return response


def get_product_price_list_details(store, product_id, user, tag_filter=None):
    db = get_admin_db_client_for_store_id(store['id'])
    conn = new_pgdb.get_connection(store['id'])
    store_db = get_store_db_client_for_store_id(store['id'])
    try:
        role_id = user.get('role_id')
        static_price_lists = fetch_static_price_lists(store['id'])

        # Fetch all available price lists from BigCommerce
        price_lists, _ = bc_price_list.fetch_price_lists(store)

        # fetching data from collection to show margin and sales percentage
        assignments = list(store_db[StoreDBCollections.PRICE_LIST_ASSIGNMENT].find({
            "price_list_id": {"$exists": True},
            "customer_group_id": {"$exists": True}
        }))

        # Step 2: Build a one-to-one mapping of price_list_id -> customer_group_id
        price_list_to_customer_group = {
            assignment["price_list_id"]: assignment["customer_group_id"]
            for assignment in assignments
            if "price_list_id" in assignment and "customer_group_id" in assignment
        }

        # Combine static_price_lists into price_lists
        for static_price_list in static_price_lists:
            price_lists['data'].append({
                'id': static_price_list['id'],
                'name': static_price_list['name'],
                'date_created': None,
                'date_modified': None,
                'active': static_price_list['active']
            })

        if role_id == '67f5f9c43c97938e59357472':
            price_lists['data'] = [pl for pl in price_lists['data'] if pl['id'] not in (14, 15)]
        elif role_id == '67fd12676af694b36923ce09':
            price_lists['data'] = [pl for pl in price_lists['data'] if pl['id'] in (13, 15, 52)]

        # Create price list mappings and separate active/inactive price lists
        price_list_meta = {}
        price_list_map = {}
        active_price_lists = []

        # Ensure price_list_id 7 appears at the top of active price lists
        priority_price_list = None

        for price_list in price_lists['data']:
            price_list_entry = {
                'id': price_list['id'],
                'key': f"price_{len(price_list_meta) + 1}",  # key like "price_1", "price_2", etc.
                'name': price_list['name'],
                'active': price_list['active']
            }

            if price_list['id'] == 7 and price_list['active']:
                priority_price_list = price_list_entry  # Keep reference to price_list_id 7
            elif price_list['active']:
                active_price_lists.append(price_list_entry)

            price_list_map[price_list['id']] = price_list_entry

        # Insert priority price list (id 7) at the top if it exists
        if priority_price_list:
            active_price_lists.insert(0, priority_price_list)

        # Now swap positions of price list id 14 and 15
        index_14 = next((i for i, pl in enumerate(active_price_lists) if pl['id'] == 14), None)
        index_15 = next((i for i, pl in enumerate(active_price_lists) if pl['id'] == 15), None)

        if index_14 is not None and index_15 is not None:
            active_price_lists[index_14], active_price_lists[index_15] = active_price_lists[index_15], active_price_lists[index_14]

        # Populate price_list_meta, ensuring price_list_id 7 is first in order
        for idx, price_list in enumerate(active_price_lists, 1):
            price_list_key = f"price_{idx}"
            price_list_meta[price_list_key] = {
                'id': price_list['id'],
                'name': price_list['name'],
                'active': price_list['active']
            }
            price_list_map[price_list['id']]['key'] = price_list_key

        variant_sku_list = None
        if tag_filter:
            tag_filter_list = [tag.strip() for tag in tag_filter.split(',')]
            query = text("SELECT variant_sku FROM product_tags WHERE tag_id = ANY(:tag_filter_list)")
            result = conn.execute(query, {"tag_filter_list": tag_filter_list}).fetchall()
            tag_skus = [row[0] for row in result]
            variant_sku_list = [sku for sku in tag_skus if sku is not None]

        if variant_sku_list:
            product = db[StoreAdminDBCollections.PRODUCT_PRICE_LISTS].aggregate([
                {
                    "$match": {
                        "parent_product_id": int(product_id)
                    }
                },
                {
                    "$addFields": {
                        "variants": {
                            "$filter": {
                                "input": "$variants",
                                "as": "v",
                                "cond": {"$in": ["$$v.variant_sku", variant_sku_list]}
                            }
                        }
                    }
                }
            ])
            product = next(product, None)
        else:
            product = db[StoreAdminDBCollections.PRODUCT_PRICE_LISTS].find_one({
                "parent_product_id": int(product_id)
            })


        # product = db[StoreAdminDBCollections.PRODUCT_PRICE_LISTS].find_one({"parent_product_id": int(product_id)})

        if not product:
            data = {"data": [], "meta": {"price_lists": price_list_meta}}
            return data

        # Collect all variant SKUs
        variant_skus = [variant['variant_sku'] for variant in product['variants'] if variant.get('variant_sku')]

        if variant_skus != []:
            classification_query = text("""
                SELECT DISTINCT
                    sc.sku,
                    sc.classification
                FROM skuvault_catalog sc
                WHERE sc.sku IN :variant_skus
            """)

            classification_results = conn.execute(classification_query, {'variant_skus': tuple(variant_skus)}).fetchall()

            classification_map = {}

            for sku, classification in classification_results:
                if sku in classification_map:
                    classification_map[sku]['classifications'].add(classification)
                else:
                    classification_map[sku] = {
                        'classifications': {classification},  # use a set for uniqueness
                    }

            # Convert classifications set to a comma-separated string
            for sku in classification_map:
                classification_map[sku]['classifications'] = ", ".join(classification_map[sku]['classifications'])

        else:
            classification_map = {}

        parent_product_id = product['parent_product_id']

        month_names, day_difference = get_month_array_for_meta(6)

        # ✅ NEW: Fetch month_1 to month_7 from analytics.replenishment_products in PG
        if parent_product_id:
            product_data = _fetch_margin_and_sales_percentage(store['id'], [parent_product_id])
            # Build a map based on customer_group_id only
            product_data_map = {
                item['customer_group_id']: {
                    "margin_percentage": item['margin_percentage'],
                    "sales_percentage": item['sales_percentage']
                }
                for item in product_data
            }

        if variant_skus:
            # Build inventory map
            cursor = store_db[StoreDBCollections.PRODUCTS].find(
                {
                    "variants.sku": {"$in": variant_skus}
                },
                {
                    "variants.sku": 1,
                    "variants.inventory_level": 1
                }
            )

            for product_doc in cursor:
                for variant in product_doc.get("variants", []):
                    sku = variant.get("sku")

        variant_costs = []
        price_list_data = {key: "" for key in price_list_meta}
        minimum_price_data = {}

        for variant in product['variants']:
            # Process price list efficiently
            for price in variant['price_list']:
                price_list_key = price_list_map.get(price['price_list_id'])
                if price_list_key:
                    price_list_key = price_list_key['key']
                    price_value = float(price['price'])
                    if price_list_data[price_list_key]:  # Already has a value
                        min_price, max_price = map(float, price_list_data[price_list_key].split('-')) if '-' in price_list_data[price_list_key] else (float(price_list_data[price_list_key]), float(price_list_data[price_list_key]))
                        price_list_data[price_list_key] = f"{min(min_price, price_value)}-{max(max_price, price_value)}" if min_price != max_price else str(min_price)
                    else:
                        price_list_data[price_list_key] = str(price_value)

                    # Track minimum price for each price list
                    minimum_price_key = f"minimum_{price_list_key}"
                    if minimum_price_key in minimum_price_data:
                        current_min = float(minimum_price_data[minimum_price_key])  # convert string to float
                        minimum_price_data[minimum_price_key] = str(min(current_min, price_value))
                    else:
                        minimum_price_data[minimum_price_key] = str(price_value)

            variant['classification'] = classification_map.get(variant['variant_sku'], {}).get('classifications', "")
            variant['variant_price'] = product['default_price'] if variant.get('variant_price') is None else variant.get('variant_price')
            variant_price_lists = []
            default_price_lists = {price_list['id']: "" for price_list in active_price_lists}

            for price in variant['price_list']:
                price_list_id = price['price_list_id']
                if price_list_id in price_list_map and price_list_map[price_list_id]['active']:
                    default_price_lists[price_list_id] = str(price.pop('price'))

            for price_list in active_price_lists:
                variant_price_lists.append({
                    "price_list_id": price_list['id'],
                    price_list['key']: default_price_lists[price_list['id']]
                })

            variant['price_list'] = variant_price_lists
            variant["parent_product_id"] = product['parent_product_id']
            variant["parent_product_name"] = product['parent_product_name']
            variant["inventory_level"] = variant.get("inventory_level")


            # Calculate total_sum of months
            if role_id != "67fd12676af694b36923ce09":
                for i in range(1, 8):
                    variant[f'month_{i}'] = variant.get(f'month_{i}', None)

            variant['turn_rate'] = variant.get('turn_rate', 0)
            variant['weeks_on_hand'] = variant.get('weeks_on_hand', 0)


            variant['cost'] = variant.get('variant_cost', None)
            if variant['cost'] is not None:
                variant_costs.append(variant['cost'])

        product_cost_price = 0
        if len(set(variant_costs)) > 1:
            product['cost_range'] = f"{min(variant_costs)}-{max(variant_costs)}"
            product_cost_price = min(variant_costs)
        elif variant_costs:
            product['cost_range'] = str(variant_costs[0])
            product_cost_price = min(variant_costs)
        else:
            product['cost_range'] = None

        if product_cost_price > 0 and product['default_price'] > 0:
            product['default_price_margin'] = round(((product['default_price'] - product_cost_price) / product_cost_price) * 100, 2)
        else:
            product['default_price_margin'] = 0

        deafult_price_sales = product_data_map.get(
            1,
            {"margin_percentage": 0.0, "sales_percentage": 0.0}
        )
        product['default_price_sales'] = deafult_price_sales["sales_percentage"]


        # Set the flag based on variants length and variant_name condition
        show_parent_product = not(len(product['variants']) == 1 and product['variants'][0]['variant_name'] == "Parent Product")
        product["price_list"] = []

        if not product_cost_price or product_cost_price == '':
            product_cost_price = 0

        product_cost_price = float(product_cost_price)
        for key, value in price_list_meta.items():
            price_list_id = value["id"]
            customer_group_id = price_list_to_customer_group.get(price_list_id)
            margin_sales = product_data_map.get(
                customer_group_id,
                {"margin_percentage": 0.0, "sales_percentage": 0.0}
            )

            price_list_price = minimum_price_data.get(f"minimum_{key}", None)
            if not price_list_price or price_list_price == '':
                price_list_price = 0
            price_list_price = float(price_list_price)
            margin_per = 0
            if price_list_price > 0 and product_cost_price > 0:
                margin_per = round(((price_list_price - product_cost_price) / product_cost_price) * 100, 2)

            product["price_list"].append({
                key: value["name"],
                "price_list_id": price_list_id,
                f"minimum_{key}": minimum_price_data.get(f"minimum_{key}", None),
                "margin_percentage": margin_per,
                "sales_percentage": margin_sales["sales_percentage"]
            })

        data = {
            "data": [{
                "parent_product_id": product['parent_product_id'],
                "parent_product_name": product['parent_product_name'],
                "parent_product_sku": product['parent_product_sku'],
                "default_price": product['default_price'],
                "default_price_margin": product['default_price_margin'],
                "default_price_sales": product['default_price_sales'],
                "cost_range": product.get('cost_range', None),
                "cost_margin": product.get('cost_margin', None),
                "minimum_cost": str(product_cost_price),
                "show_parent_product": show_parent_product,
                "price_list": product['price_list'],
                "variants": product['variants']
            }],
            "meta": {
                "price_lists": price_list_meta,
                "months": month_names
            }
        }

        # Conditionally add month_1 to month_7 and remove month labels if role_id is restricted
        if role_id != "67fd12676af694b36923ce09":
            for i in range(1, 8):
                data["data"][0][f"month_{i}"] = product.get(f"month_{i}", None)
        else:
            data["meta"].pop("months", None)

    except Exception as e:
        logger.error(traceback.format_exc())
        return {"data": [], "meta": {"price_lists": price_list_meta, "months": month_names}}
    finally:
        if conn:
            conn.close()

    return data


def update_product_price_list(store, product_id, req_body, username, is_coming_from_google_sheet=False):
    db = get_admin_db_client_for_store_id(store['id'])

    # Prepare batch updates & logs
    updates, price_logs, bc_payload_map, bc_deletion_map, old_data_map = [], [], {}, {}, {}


    #  Extract default_price payload
    default_price_payload = req_body.get("default_price", [])

    old_data_map = _fetch_old_data_of_products(store['id'], [product_id])

    # Update default prices using the helper function
    if default_price_payload:
        try:
            default_price_logs = update_default_price(store, product_id, default_price_payload, username)
            if default_price_logs:
                price_logs.extend(default_price_logs)
        except ValueError as e:
            return {"message": str(e), "status": 404}

    # Fetch necessary data from MongoDB in **one query** for efficiency
    product = db[StoreAdminDBCollections.PRODUCT_PRICE_LISTS].find_one({"parent_product_id": int(product_id)})
    static_price_list_doc = db['static_price_lists'].find_one({"product_id": int(product_id)})

    if not product:
        return {"message": f"Product with parent_product_id {product_id} not found", "status": 404}

    # Fetch all available price lists from BigCommerce
    price_lists, _ = bc_price_list.fetch_price_lists(store)

    # Fetch static price lists from MongoDB
    static_price_lists = list(db["price_list_distributors"].find({"status": "active"}, {"id": 1, "title": 1, "_id": 0, "derived": 1}))
    static_price_lists = [{"id": doc["id"], "name": doc["title"], "active": True, "derived": doc["derived"]} for doc in static_price_lists]

    static_price_list_ids = {pl["id"] for pl in static_price_lists}

    # Extract price_list_ids that have derived = "true"
    derived_price_list_ids = {doc["id"] for doc in static_price_lists if doc.get("derived") == "true"}


    # Combine static_price_lists into price_lists
    for static_price_list in static_price_lists:
        price_lists['data'].append({
            'id': static_price_list['id'],
            'name': static_price_list['name'],
            'date_created': None,  # Placeholder
            'date_modified': None,  # Placeholder
            'active': static_price_list['active']
        })

    price_list_meta = {pl['id']: pl['name'] for pl in price_lists['data']}

    parent_product_name = product.get('parent_product_name')
    parent_product_sku = product.get('parent_product_sku')

    # Dictionary to map variant_id to variant data
    variant_map = {variant['variant_id']: variant for variant in product.get('variants', [])}

    # Iterate over each entry in the request body
    for entry in req_body['data']:
        variant_id = entry.get('variant_id')
        price_list_id = entry.get('price_list_id')
        new_price = entry.get('price')

        variant = variant_map.get(variant_id)
        if not variant:
            return {"message": f"Variant with variant_id {variant_id} not found for product {product_id}", "status": 404}

        # Extract additional variant details
        variant_name = variant.get('variant_name')
        variant_sku = variant.get('variant_sku')

        # Fetch the old price for logging
        old_price_entry = next((pl for pl in variant.get('price_list', []) if pl['price_list_id'] == price_list_id), None)
        old_price = old_price_entry['price'] if old_price_entry else None

        if old_price == new_price:
            continue

        # Handle empty price case
        if new_price == "":
            # Handle static price list IDs - only delete from DB
            if price_list_id in [pl['id'] for pl in static_price_lists]:
                # Remove from static price list
                if static_price_list_doc:
                    for static_variant in static_price_list_doc['variants']:
                        if static_variant['variant_id'] == variant_id:
                            static_variant['price_list'] = [
                                pl for pl in static_variant['price_list'] if pl['price_list_id'] != price_list_id
                            ]

                            # Check if the removed price_list_id was in derived_price_list_ids
                            if price_list_id in derived_price_list_ids:
                                static_variant['is_updated_admin'] = True

            variant['price_list'] = [pl for pl in variant.get('price_list', []) if pl['price_list_id'] != price_list_id]

            updates.append(UpdateOne(
                {"parent_product_id": int(product_id), "variants.variant_id": variant_id},
                {"$set": {"variants.$[v].price_list": variant['price_list'],
                          "updated_at": datetime.utcnow().replace(tzinfo=timezone.utc).isoformat(timespec='seconds').replace('+00:00', 'Z'),
                          "updated_by": username}},
                array_filters=[{"v.variant_id": variant_id}]
            ))

            if price_list_id not in static_price_list_ids:
                if price_list_id not in bc_deletion_map:
                    bc_deletion_map[price_list_id] = []
                bc_deletion_map[price_list_id].append(variant_id)
        else:
            # Handle static price list update (when price is not empty)
            if price_list_id in [pl['id'] for pl in static_price_lists]:
                if not static_price_list_doc:
                    # If the product does not exist in static_price_lists, create it
                    static_price_list_doc = {
                        "product_id": int(product_id),
                        "variants": []
                    }

                # Find or create the variant entry in static_price_lists
                static_variant = next((v for v in static_price_list_doc['variants'] if v['variant_id'] == variant_id), None)
                if not static_variant:
                    static_variant = {"variant_id": variant_id, "price_list": []}
                    static_price_list_doc['variants'].append(static_variant)

                # Update or add the price_list entry
                price_list_found = False
                for price_list in static_variant['price_list']:
                    if price_list['price_list_id'] == price_list_id:
                        price_list['price'] = float(new_price)
                        price_list_found = True
                        break

                if not price_list_found:
                    static_variant['price_list'].append({
                        "price_list_id": price_list_id,
                        "name": price_list_meta[price_list_id],
                        "price": float(new_price)
                    })

                # If the price_list_id is in the derived list, set the flag
                if price_list_id in derived_price_list_ids:
                    static_variant["is_updated_admin"] = True

                # Update product_price_lists as well
                variant['price_list'] = [
                    pl for pl in variant.get('price_list', []) if pl['price_list_id'] != price_list_id
                ]
                variant['price_list'].append({
                    "price_list_id": price_list_id,
                    "name": price_list_meta[price_list_id],
                    "price": float(new_price)
                })

                updates.append(UpdateOne(
                    {
                        "parent_product_id": int(product_id),
                        "variants.variant_id": variant_id
                    },
                    {
                        "$set": {
                        "variants.$[v].price_list": variant['price_list'],
                        "updated_at": datetime.utcnow().replace(tzinfo=timezone.utc).isoformat(timespec='seconds').replace('+00:00', 'Z'),
                        "updated_by": username
                        }
                    },
                    array_filters=[{"v.variant_id": variant_id}]
                ))
            else:
                # Handle non-static price list updates (for BigCommerce and MongoDB)
                try:
                    new_price = float(new_price)
                except ValueError:
                    return {"message": f"Invalid price value {new_price} for variant {variant_id}, price_list {price_list_id}", "status": 400}

                price_list_found = False
                for price_list in variant.get('price_list', []):
                    if price_list['price_list_id'] == price_list_id:
                        price_list['price'] = new_price
                        price_list_found = True
                        break

                if not price_list_found:
                    variant['price_list'].append({
                        "price_list_id": price_list_id,
                        "name": price_list_meta.get(price_list_id, "Unknown Price List"),
                        "price": new_price
                    })

                updates.append(UpdateOne(
                    {
                        "parent_product_id": int(product_id),
                        "variants.variant_id": variant_id
                    },
                    {
                        "$set": {
                            "variants.$[v].price_list": variant['price_list'],
                            "updated_at": datetime.utcnow().replace(tzinfo=timezone.utc).isoformat(timespec='seconds').replace('+00:00', 'Z'),
                            "updated_by": username
                        }
                    },
                    array_filters=[{"v.variant_id": variant_id}]
                ))

                # Collect BigCommerce payload
                bc_entry = {
                    "variant_id": variant_id,
                    "currency": "usd",
                    "price": new_price
                }

                if price_list_id not in bc_payload_map:
                    bc_payload_map[price_list_id] = []
                bc_payload_map[price_list_id].append(bc_entry)

        new_price = None if new_price == "" else new_price

        if old_price is not None or new_price is not None:
            price_logs.append({
                "price_list_id": price_list_id,
                "variant_id": variant_id,
                "parent_product_name": parent_product_name,
                "parent_product_sku": parent_product_sku,
                "variant_name": variant_name,
                "variant_sku": variant_sku,
                "old_price": old_price,
                "new_price": new_price,
                "updated_by": username
            })
    
    
    # Perform all updates in bulk for MongoDB
    if updates:
        db[StoreAdminDBCollections.PRODUCT_PRICE_LISTS].bulk_write(updates)

    # Upsert the static_price_lists document
    if static_price_list_doc:
        db['static_price_lists'].update_one(
            {"product_id": int(product_id)},
            {"$set": static_price_list_doc},
            upsert=True
        )

    # Perform BigCommerce deletions after MongoDB updates
    for price_list_id, variant_ids in bc_deletion_map.items():
        res, status_code = bc_price_list.delete_price_list(store, variant_ids, price_list_id)
        if status_code != 204:
            return {"message": f"Failed to delete price list {price_list_id} for variants {variant_ids}", "bc_response": res.json(), "status": status_code}

    if product_id and old_data_map != {}:
        task.send_task(task.CHECK_PROMO_PRODUCT_AND_CREATE_TICKET_TASK, args=(store['id'], [int(product_id)], old_data_map))

    # Call BigCommerce API for each price_list_id with the collected payload
    for price_list_id, bigcommerce_payload in bc_payload_map.items():
        logger.info(f"Updating price list {price_list_id} with payload {bigcommerce_payload}")
        bc_response, status_code = bc_price_list.update_price_list(store, price_list_id, bigcommerce_payload)
        logger.info(f"BigCommerce update response: {bc_response}")
        if status_code != 200:
            return {"message": f"BigCommerce update failed for price_list_id {price_list_id}", "bc_response": bc_response, "status": status_code}

    # if not is_coming_from_google_sheet:
        # perform google sheet updation ...
        # res = update_price_in_sheet(store['id'], req_body)

    task.send_task(task.PRICE_LIST_PRODUCTS_LOGS_TASK, args=(store['id'], product_id, price_logs))


    return {"message": "Prices updated successfully.", "status": 200}


# Function to fetch old data of products to create a ticket if price changes
def _fetch_old_data_of_products(store_id, product_ids):
    db = get_admin_db_client_for_store_id(store_id)
    product_price_lists_collection = db["product_price_lists"]
    
    try:
        product_ids = [int(product_id) for product_id in product_ids]
        # Fetch all product_price_list records for the given product_ids in one query
        products = list(product_price_lists_collection.find({
            "parent_product_id": {"$in": product_ids}
        }))
        
        # Build a map from product_id to its variants and default_price
        product_variants_map = {}
        for product in products:
            product_id = product.get("parent_product_id")
            if product_id:
                product_variants_map[product_id] = {
                    "variants": product.get("variants", []),
                    "default_price": product.get("default_price")
                }

        result_map = {}

        for product_id, product_data in product_variants_map.items():
            variants = product_data.get("variants", [])
            default_price = product_data.get("default_price", 0)

            # Collect all prices including default and variant prices
            all_prices = [default_price] if default_price is not None else []

            # Price list aggregation
            price_list_map = defaultdict(lambda: {"name": "", "prices": []})

            for variant in variants:
                variant_price = variant.get("variant_price")
                if variant_price is not None:
                    all_prices.append(variant_price)

                for price_entry in variant.get("price_list", []):
                    price_list_id = price_entry["price_list_id"]
                    name = price_entry["name"]
                    price = price_entry["price"]
                    price_list_map[price_list_id]["name"] = name
                    price_list_map[price_list_id]["prices"].append(price)

            product_price_output = {}

            # Optionally add combined price info if needed
            if all_prices:
                min_combined = min(all_prices)
                max_combined = max(all_prices)
                combined_price_desc = f"${min_combined}" if min_combined == max_combined else f"${min_combined}-{max_combined}"
                product_price_output["Wholesale"] = combined_price_desc

            # Format individual price list prices
            for pl in price_list_map.values():
                prices = pl["prices"]
                name = pl["name"]
                if not prices:
                    continue

                min_price = min(prices)
                max_price = max(prices)

                if min_price == max_price:
                    price_desc = f"${min_price}"
                else:
                    price_desc = f"${min_price}-{max_price}"

                product_price_output[name] = price_desc

            result_map[product_id] = product_price_output

        return result_map
    
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e

# Function to split list into batches of max_size
def batch(iterable, max_size):
    it = iter(iterable)
    while True:
        chunk = list(islice(it, max_size))
        if not chunk:
            break
        yield chunk

def update_default_price(store, product_id, default_price_payload, username):
    redis_client = redis_util.get_redis_client(store['id'])
    db = get_admin_db_client_for_store_id(store['id'])
    updates = []
    bc_payload = []  # Payload for BigCommerce
    default_price_updated = False
    price_logs = []
    bc_variants = []

    # Fetch the product document
    product = db[StoreAdminDBCollections.PRODUCT_PRICE_LISTS].find_one({"parent_product_id": int(product_id)})
    if not product:
        logger.error(f"Product with parent_product_id {product_id} not found")
        return

    parent_product_sku = product.get("parent_product_sku")
    parent_product_name = product.get("parent_product_name", "Unknown")

    # Fetch variant data from BigCommerce
    bc_variants = bc_products.fetch_bc_product_variants(store, product_id)
    bc_variants_map = {variant["id"]: variant for variant in bc_variants} if bc_variants else {}

    # Create a dictionary for faster variant lookup (O(1) time complexity)
    variant_map = {variant["variant_id"]: variant for variant in product.get("variants", [])}

    # Iterate through the default price list
    for entry in default_price_payload:
        variant_id = entry.get("variant_id")
        new_price = entry.get("price")
        new_price = float(new_price) if new_price != "" else 0
        old_price = None
        variant_name = None
        variant_sku = None
        # Handle case where variant_id is null
        if variant_id is None:
            parent_product_price_logs = update_parent_product_default_price(store, product, product_id, new_price, username, updates)
            if parent_product_price_logs:
                price_logs.extend(parent_product_price_logs)
            continue

        # Update variant-level default price
        variant = variant_map.get(variant_id)
        if variant:
            old_price = variant.get("variant_price")
            variant["variant_price"] = new_price
            variant_name = variant.get("variant_name", "Unknown")
            variant_sku = variant.get("variant_sku", "Unknown")

            # Check if variant_sku matches parent_product_sku for product-level update
            if variant.get("variant_sku") == parent_product_sku:
                product["default_price"] = new_price
                default_price_updated = True

            bc_variant = bc_variants_map.get(variant_id, {})
            sale_price = bc_variant.get("sale_price", 0)

            # Prepare payload for BigCommerce based on sale_price
            if sale_price not in (0, None):
                bc_payload.append({
                    "id": variant_id,  # BigCommerce variant ID
                    "sale_price": new_price
                })
            else:
                bc_payload.append({
                    "id": variant_id,  # BigCommerce variant ID
                    "price": new_price
                })

        if old_price is not None or new_price is not None:
            price_logs.append({
                "price_list_id": None,
                "variant_id": variant_id,
                "parent_product_name": parent_product_name,
                "parent_product_sku": parent_product_sku,
                "variant_name": variant_name,
                "variant_sku": variant_sku,
                "old_price": old_price,
                "new_price": new_price,
                "updated_by": username
            })

        # Prepare MongoDB update operation for the variant
        updates.append(UpdateOne(
            {
                "parent_product_id": int(product_id),
                "variants.variant_id": variant_id
            },
            {
                "$set": {
                    "variants.$[v].variant_price": new_price,
                    "updated_at": datetime.utcnow().replace(tzinfo=timezone.utc).isoformat(timespec='seconds').replace('+00:00', 'Z'),
                    "updated_by": username
                }
            },
            array_filters=[{"v.variant_id": variant_id}]
        ))

    # Add product-level default price update if applicable
    if default_price_updated:
        updates.append(UpdateOne(
            {"parent_product_id": int(product_id)},
            {"$set": {
                "default_price": product["default_price"],
                "updated_at": datetime.utcnow().replace(tzinfo=timezone.utc).isoformat(timespec='seconds').replace('+00:00', 'Z'),
                "updated_by": username
            }}
        ))

    # Perform bulk updates in MongoDB
    if updates:
        db[StoreAdminDBCollections.PRODUCT_PRICE_LISTS].bulk_write(updates)

    redis_key = f"admin_update:{store['id']}:{product_id}"
    redis_client.set(redis_key, "1", ex=7)  # Expires in 7 seconds

    if bc_payload:
        for batch_payload in batch(bc_payload, 49):  # Split into batches of 49
            res, status_code = bc_product.update_bc_product_variants(store, batch_payload)
            if status_code != 200:
                logger.error(f"Failed to update BigCommerce variants. Status Code: {status_code}, Response: {res}")

    return price_logs


def update_parent_product_default_price(store, product, product_id, new_price, username, updates):
    """
    Handle cases where variant_id is null and update parent product's default price.
    """
    redis_client = redis_util.get_redis_client(store['id'])
    price_logs = []
    bc_product_data = bc_products.fetch_bc_product(store, product_id)
    if not bc_product_data:
        logger.error(f"Failed to fetch BigCommerce product data for product_id {product_id}")
        return

    sale_price = bc_product_data.get("data", {}).get("sale_price")

    if sale_price not in (0, None):
        bc_payload = {"sale_price": new_price}
    else:
        bc_payload = {"price": new_price}

    res, status_code = bc_products.update_bc_product(store, bc_payload, product_id)
    if status_code != 200:
        logger.error(f"Failed to update BigCommerce product price. Status Code: {status_code}, Response: {res}")
        return

    old_price = product.get("default_price")
    product["default_price"] = new_price
    updates.append(UpdateOne(
        {"parent_product_id": int(product_id)},
        {"$set": {
            "default_price": new_price,
            "updated_at": datetime.utcnow().replace(tzinfo=timezone.utc).isoformat(timespec='seconds').replace('+00:00', 'Z'),
            "updated_by": username
        }}
    ))
    parent_product_name = product.get("parent_product_name", "Unknown")
    parent_product_sku = product.get("parent_product_sku")

    redis_key = f"admin_update:{store['id']}:{product_id}"
    redis_client.set(redis_key, "1", ex=7)  # Expires in 7 seconds

    if old_price is not None or new_price is not None:
        price_logs.append({
            "price_list_id": None,
            "variant_id": None,
            "parent_product_name": parent_product_name,
            "parent_product_sku": parent_product_sku,
            "variant_name": None,
            "variant_sku": None,
            "old_price": old_price,
            "new_price": new_price,
            "updated_by": username
        })

    return price_logs

def get_product_inquiries(store, page, limit, sort_by, filter, rep_email, product_sku, sort_array, status, user, purchaser_name):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store['id'])

    try:
        # Base query for product inquiries
        base_query = f"""
            WITH sorted_inquiries AS (
                SELECT 
                    pi.id, 
                    pi.customer_id, 
                    pi.customer_name, 
                    pi.customer_email, 
                    pi.product_id, 
                    pi.date_submitted, 
                    pi.message, 
                    pi.status, 
                    pi.updated_by, 
                    pi.updated_at, 
                    scr.rep_email, 
                    scr.rep_name, 
                    p.product_name, 
                    p.sku, 
                    pi.is_guest, 
                    c.customer_group_id, 
                    c.customer_group_name, 
                    usm.user_name AS purchaser_name,
                    ROW_NUMBER() OVER (
                        PARTITION BY pi.id 
                        ORDER BY pi.id
                    ) as rn
                FROM product_inquiries pi
                LEFT JOIN salesforce_customer_rep scr ON scr.customer_id = pi.customer_id
                LEFT JOIN products p ON p.product_id = pi.product_id
                LEFT JOIN customers c ON pi.customer_id = c.customer_id
                LEFT JOIN skuvault_catalog sc ON p.sku = sc.parent_sku
                LEFT JOIN user_supplier_mapping usm on sc.primary_supplier = usm.suppliers
                WHERE 1=1
        """

        # Prepare query parameters
        query_params = {}

        # Filter by rep_email if provided
        if rep_email == "<EMAIL>":
            base_query += " AND pi.customer_id IS NULL"
        elif rep_email:
            base_query += " AND scr.rep_email = :rep_email"
            query_params['rep_email'] = rep_email

        # Filter by product_sku if provided
        if product_sku:
            base_query += " AND p.sku = :product_sku"
            query_params['product_sku'] = product_sku

        # Filter by purchaser_name if provided
        if purchaser_name:
            base_query += " AND usm.user_name = :purchaser_name"
            query_params['purchaser_name'] = purchaser_name

        # Add status filter logic
        if status:
            base_query += " AND pi.status = :status"
            query_params['status'] = status
        else:
            base_query += " AND pi.status != 'Archived'"

        # Add filtering logic for product_name, sku, and customer_name
        if filter:
            filter = filter.replace("'", "''")  # Escaping single quotes in the filter string
            base_query += f" AND (p.product_name ILIKE '%{filter}%' OR p.sku ILIKE '%{filter}%' OR pi.customer_name ILIKE '%{filter}%')"

        # Close the CTE
        base_query += """
            )
            SELECT * FROM sorted_inquiries WHERE rn = 1
        """

        # Sorting logic
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            sort_field = sort_array[0]
            
            # Map frontend column names to database column names
            column_mapping = {
                "product_name": "product_name",
                "date_submitted": "date_submitted",
                "customer_name": "customer_name",
                "rep_name": "rep_name",
                "status": "status",
                "sku": "sku",
                "customer_email": "customer_email",
                "message": "message",
                "updated_at": "updated_at",
                "purchaser_name": "purchaser_name"
            }
            
            if sort_field in column_mapping:
                db_column = column_mapping[sort_field]
                # Add NULLS LAST for better sorting behavior
                base_query += f" ORDER BY {db_column} {sort_direction} NULLS LAST"
            else:
                # Default sorting if invalid column is provided
                base_query += " ORDER BY date_submitted DESC NULLS LAST"

        # Get the total count of records for pagination
        count_query = f"SELECT COUNT(*) FROM ({base_query}) AS total"
        total_count = conn.execute(text(count_query), query_params).scalar()

        # Pagination logic
        offset = (page - 1) * limit
        paginated_query = base_query + f" LIMIT {limit} OFFSET {offset}"

        # Fetch paginated results
        results = conn.execute(text(paginated_query), query_params)

        # Process results
        data = []
        for row in results:
            if row[8] is not None:
                user_name = store_admin_db.fetch_user_by_username(store['id'], row[8])
            else:
                user_name = None
            row_data = {
                'id': row[0],
                'customer_id': row[1],
                'customer_name': row[2],
                'customer_email': row[3],
                'product_id': row[4],
                'date_submitted': convert_to_timestamp(row[5]),
                'message': row[6],
                'status': row[7],
                'updated_by': user_name['name'] if user_name else None,
                'updated_at': convert_to_timestamp(row[9]),
                'rep_email': row[10] if row[10] else "<EMAIL>",
                'rep_name': row[11] if row[11] else "Dan Charitan",
                'product_name': row[12],
                'sku': row[13],
                'is_guest': row[14],
                'customer_group_id': row[15],
                'customer_group_name': row[16],
                'purchaser_name': row[17]
            }
            data.append(row_data)

        # Handle pagination
        paginated_rows, current_page, total_pages, total_items = paginate_data_postgres(
            total_count, data, page, limit)

        paginated_data = new_utils.calculate_pagination(paginated_rows, current_page, limit, total_items)

        # Call get_table_columns and pass 'product_inquiries' as the type
        query_params = {'type': 'product_inquiries'}
        columns_response = replenishment.get_table_columns(store, user, query_params)

        meta = paginated_data.get('meta', {})
        if columns_response and 'data' in columns_response and 'columns' in columns_response['data']:
            meta["columns"] = columns_response['data'].get('columns')

        paginated_data['meta'] = meta

        response['data'] = paginated_data
        response['status'] = 200

    except Exception as e:
        response['status'] = 422
        response['message'] = str(e)
        raise e
    finally:
        if conn:
            conn.close()

    return response

def get_product_inquiry_details(store, id):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store['id'])

    try:
        query = f"""
            SELECT pi.id, pi.product_id, pi.message, pi.status, p.product_name, pi.customer_name
            FROM product_inquiries pi
            left join products p ON p.product_id = pi.product_id
            WHERE pi.id = :id"""

        results = conn.execute(text(query), {'id': id})

        data = []
        for row in results:
            row_data = {
                'id': row[0],
                'product_id': row[1],
                'message': row[2],
                'status': row[3],
                'product_name': row[4],
                'customer_name': row[5]
            }
            data.append(row_data)

        response['data'] = data
        response['status'] = 200

    except Exception as e:
        response['status'] = 422
        response['message'] = str(e)
        raise e
    finally:
        if conn:
            conn.close()

    return response


def update_product_inquiry_details(store, payload, id, username):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store['id'])

    try:
        status = payload.get('status', None)
        query = f"""
            UPDATE product_inquiries
            SET status = :status, updated_by = :updated_by, updated_at = CURRENT_TIMESTAMP
            WHERE id = :id
            """

        results = conn.execute(text(query), {'status': status, 'updated_by': username, 'id': id})
        conn.commit()

        if results.rowcount > 0:
            response['data'] = {"updated_id": id}
            response['status'] = 200

            query = text (f"""SELECT id, project_id, title, pipeline_record_id, table_name from agile_project_cards where pipeline_record_id = :id and table_name = 'product_inquiries';""")
            query = query.params(id=id)
            res = conn.execute(query).fetchone()
            if res:
                data = {
                    'ticket_id': res[0],
                    'title': res[2],
                    'status': status,
                    'project_id': res[1],
                    'resource_id': res[3],
                    'table_name': res[4],
                    'updated_by': username
                }
                task.send_task(task.UPDATE_PROJECT_TICKET_TASK, args=(store['id'], data))
        else:
            response['status'] = 404
            response['message'] = "No rows were updated"

    except Exception as e:
        response['status'] = 422
        response['message'] = str(e)
        raise e
    finally:
        if conn:
            conn.close()

    return response



def get_product_price_list_replenishment_dashboard(store, parent_sku):
    db = get_admin_db_client_for_store_id(store['id'])
    static_price_lists = []

    # Condition to hide static price lists in CBD to store environment
    if store['id'] != '661239751b9ce4bd7f85237c':
        static_price_lists = fetch_static_price_lists(store['id'])

    # Fetch all available price lists from BigCommerce
    price_lists, _ = bc_price_list.fetch_price_lists(store)

    # Combine static_price_lists into price_lists
    if static_price_lists:
        for static_price_list in static_price_lists:
            price_lists['data'].append({
                'id': static_price_list['id'],
                'name': static_price_list['name'],
                'date_created': None,
                'date_modified': None,
                'active': static_price_list['active']
            })

    # Create price list mappings and separate active/inactive price lists
    price_list_meta = {}
    price_list_map = {}
    active_price_lists = []

    for price_list in price_lists['data']:
        price_list_entry = {
            'id': price_list['id'],
            'key': f"price_{len(price_list_meta) + 1}",
            'name': price_list['name'],
            'active': price_list['active']
        }

        if price_list['active']:
            active_price_lists.append(price_list_entry)

        # Create a map for price lists based on their ID
        price_list_map[price_list['id']] = price_list_entry

    # Sort active_price_lists to place price_list_id 7 at the top if it exists
    active_price_lists.sort(key=lambda x: (x['id'] != 7, x['id']))

    # Rebuild the price_list_meta with only active price lists
    for idx, price_list in enumerate(active_price_lists, 1):  # Only active lists
        price_list_key = f"price_{idx}"
        price_list_meta[price_list_key] = {
            'name': price_list['name'],
            'active': price_list['active']
        }
        price_list_map[price_list['id']]['key'] = price_list_key  # Update key in price_list_map

    # Fetch the product with the specified parent_sku
    product = db[StoreAdminDBCollections.PRODUCT_PRICE_LISTS].find_one({"parent_product_sku": str(parent_sku)})

    if not product:
        return {"message": f"Product with parent_sku {parent_sku} not found", "status": 404}

    # Collect all variant prices to determine the range for default_price
    variant_prices = [variant['variant_price'] for variant in product['variants']]

    # Check if all variant prices are the same as the default price
    if len(set(variant_prices)) > 1:
        valid_prices = [price for price in variant_prices if price is not None]
        if valid_prices:
            # Check if all variant prices are the same as the default price
            if len(set(valid_prices)) > 1:
                product['default_price'] = f"{min(valid_prices)}-{max(valid_prices)}"
            else:
                product['default_price'] = str(valid_prices[0])
        else:
            product['default_price'] = ""
        # product['default_price'] = f"{min(variant_prices)}-{max(variant_prices)}"
    else:
        product['default_price'] = str(variant_prices[0])

    # Collect price ranges for each active price list at the product level
    price_list_ranges = {price_list['id']: [] for price_list in active_price_lists}

    # Loop over each variant and calculate the price at the variant level
    for variant in product['variants']:
        variant['variant_price'] = str(variant.get('variant_price', ''))
        variant_price_lists = []
        default_price_lists = {price_list['id']: "" for price_list in active_price_lists}  # Only active lists

        # Collect price_list data for the variant and map the prices
        for price in variant['price_list']:
            price_list_id = price['price_list_id']
            if price_list_id in price_list_map and price_list_map[price_list_id]['active']:
                price_value = str(price.pop('price'))
                default_price_lists[price_list_id] = price_value
                price_list_ranges[price_list_id].append(float(price_value))

        # Build the variant's price list with only active price lists and their prices
        for price_list in active_price_lists:
            variant_price_lists.append({
                "price_list_id": price_list['id'],
                price_list['key']: default_price_lists[price_list['id']]
            })

        # Update the variant's price_list with the list of sorted active price lists
        variant['price_list'] = variant_price_lists

    # Calculate product-level price ranges for each active price list
    product_price_lists = []
    for price_list in active_price_lists:
        price_list_id = price_list['id']
        if price_list_ranges[price_list_id]:
            min_price = min(price_list_ranges[price_list_id])
            max_price = max(price_list_ranges[price_list_id])
            price_range = str(min_price) if min_price == max_price else f"{min_price}-{max_price}"
        else:
            price_range = ""

        product_price_lists.append({
            "price_list_id": price_list_id,
            price_list['key']: price_range
        })

    # Prepare the response data with only variants and meta (price list mapping)
    data = {
        "data": [{"price_list": product_price_lists}],
        "meta": {"price_lists": price_list_meta}
    }

    return data

def update_batch_price_list(store_id, incoming_price_list):
    db = get_admin_db_client_for_store_id(store_id)

    rs = process_google_sheet_request(incoming_price_list, db)
    return rs


def get_product_price_list_status_check(store):
    db = get_admin_db_client_for_store_id(store['id'])
    product_price_lists = db[StoreAdminDBCollections.PRODUCT_PRICE_LISTS]

    # Check if any record has price_update_status = "processing"
    in_progress = product_price_lists.find_one({"price_update_status": "processing"}) is not None

    return {"in_progress": in_progress}

def update_price_list_with_cost_plus_percentage(store_id, price_list_id, percentage, is_replace):
    response = {"status": 400}
    price_list_id = int(price_list_id)
    percentage = float(percentage)
    if is_replace == 'true':
        is_replace = True
    if is_replace == 'false' or is_replace == '':
        is_replace = False

    task_id = task.send_task(task.UPDATE_PRICE_LIST_WITH_COST_PLUS_PERCENTAGE, args=(store_id, price_list_id, percentage, is_replace))
    if task_id:
        response["status"] = 200
        response["message"] = f"Price update process has started"
    else:
        response["message"] = "Something went wrong while updating the product prices."

    return response

def get_inquiry_report_by_rep(store, sort_by, sort_array, search_value="", filter_rep_email=""):
    conn = new_pgdb.get_connection(store['id'])
    response = {
        "status": 400,
    }

    try:
        allowed_sort_fields = ["rep_email", "rep_name", "open", "in_progress", "pending", "done", "archived", "total"]

        # Default sort by Total in descending order
        sort_field = "Total"
        sort_direction = "DESC"

        if len(sort_array) == 2:
            if sort_array[0] in allowed_sort_fields:
                sort_field = sort_array[0]
                sort_direction = "ASC" if sort_array[1] == "1" else "DESC"

        summary_query = """
            SELECT
                COALESCE(scr.rep_email, '<EMAIL>') AS rep_email,
                COALESCE(scr.rep_name, 'Dan Charitan') AS rep_name,
                pi.status,
                COUNT(DISTINCT pi.id) AS total_inquiry
            FROM product_inquiries pi
            LEFT JOIN salesforce_customer_rep scr ON scr.customer_id = pi.customer_id
            GROUP BY COALESCE(scr.rep_email, '<EMAIL>'), COALESCE(scr.rep_name, 'Dan Charitan'), pi.status
        """

        result = conn.execute(text(summary_query))

        summary_data = {}
        for row in result:
            rep = row[0] if row[0] else "<EMAIL>"
            rep_name = row[1] if row[1] else "Dan Charitan"
            status = row[2]
            count = row[3]

            normalized_status = status.lower().replace(" ", "_")

            if rep not in summary_data:
                summary_data[rep] = {
                    "rep_email": rep,
                    "rep_name": rep_name,
                    "open": 0,
                    "in_progress": 0,
                    "pending": 0,
                    "done": 0,
                    "archived": 0,
                    "total": 0
                }

            if normalized_status not in summary_data[rep]:
                summary_data[rep][normalized_status] = 0

            summary_data[rep][normalized_status] += count
            summary_data[rep]["total"] += count

        data_list = list(summary_data.values())

        # Filter by rep_email
        if filter_rep_email:
            data_list = [item for item in data_list if item["rep_email"].lower() == filter_rep_email]

        # search filter
        elif search_value:
            data_list = [item for item in data_list if search_value in item["rep_email"].lower() or search_value in item["rep_name"].lower()]

        # Apply sorting
        if sort_field in ["total", "open", "in_progress", "pending", "done", "archived"]:
            data_list.sort(key=lambda x: x.get(sort_field, 0), reverse=(sort_direction == "DESC"))
        else:
            data_list.sort(key=lambda x: x.get(sort_field, "").lower(), reverse=(sort_direction == "DESC"))

        response['data'] = {"data": data_list}
        response['status'] = 200

    except Exception as e:
        response['status'] = 422
        response['message'] = str(e)
        raise e
    finally:
        if conn:
            conn.close()

    return response


def get_inquiry_report_by_product(store, page, limit, sort_by, sort_array, search_value=""):
    conn = new_pgdb.get_connection(store['id'])
    response = {
        "status": 400,
    }

    try:
        allowed_sort_fields = ["product_name", "sku", "open", "in_progress", "pending", "done", "archived", "total"]

        # Default sorting
        sort_field = "total"
        sort_direction = "DESC"

        if sort_array and len(sort_array) == 2:
            if sort_array[0] in allowed_sort_fields:
                sort_field = sort_array[0]
                sort_direction = "ASC" if sort_array[1] == "1" else "DESC"

        summary_query = """
            SELECT
                p.product_name,
                p.sku,
                pi.status,
                COUNT(DISTINCT pi.id) as total_inquiry
            FROM product_inquiries pi
            LEFT JOIN products p ON p.product_id = pi.product_id
            GROUP BY p.product_name, p.sku, pi.status
        """

        result = conn.execute(text(summary_query))

        summary_data = {}
        for row in result:
            product_name = row[0]
            sku = row[1]
            status = row[2]
            count = row[3]

            normalized_status = status.lower().replace(" ", "_")

            key = (product_name, sku)
            if key not in summary_data:
                summary_data[key] = {
                    "product_name": product_name,
                    "sku": sku,
                    "open": 0,
                    "in_progress": 0,
                    "pending": 0,
                    "done": 0,
                    "archived": 0,
                    "total": 0
                }

            if normalized_status not in summary_data[key]:
                summary_data[key][normalized_status] = 0

            summary_data[key][normalized_status] += count
            summary_data[key]["total"] += count

        # Convert dict to list
        data_list = list(summary_data.values())

        # Filter search
        if search_value:
            search_value_lower = search_value.lower()
            data_list = [
                item for item in data_list
                if search_value_lower in item["product_name"].lower() or search_value_lower in item["sku"].lower()
            ]

        # Sort data
        if sort_field in ["total", "open", "in_progress", "pending", "done", "archived"]:
            data_list.sort(key=lambda x: x.get(sort_field, 0), reverse=(sort_direction == "DESC"))
        else:
            data_list.sort(key=lambda x: x.get(sort_field, "").lower(), reverse=(sort_direction == "DESC"))

        # Total records count
        total_items = len(data_list)

        # Pagination logic: limit and offset
        offset = (page - 1) * limit
        paginated_rows = data_list[offset:offset + limit]

        # Use utility to structure paginated data
        paginated_data = new_utils.calculate_pagination(paginated_rows, page, limit, total_items)

        response['data'] = paginated_data
        response['status'] = 200

    except Exception as e:
        response['status'] = 422
        response['message'] = str(e)
        raise e
    finally:
        if conn:
            conn.close()

    return response

def get_inquiry_report_by_purchaser(store_id, sort_array):
    conn = new_pgdb.get_connection(store_id)
    response = {
        "status": 400,
    }

    try:
        # Step 1: Fetch all distinct statuses
        status_query = "SELECT DISTINCT status FROM product_inquiries;"
        statuses = conn.execute(text(status_query)).fetchall()
        status_list = [row[0].lower().replace(" ", "_") for row in statuses]

        # Step 2: Handle sorting
        allowed_sort_fields = ["purchaser_name", "total"] + status_list
        sort_field = "total"
        sort_direction = "DESC"
        if sort_array and len(sort_array) == 2:
            if sort_array[0] in allowed_sort_fields:
                sort_field = sort_array[0]
                sort_direction = "ASC" if sort_array[1] == "1" else "DESC"

        # Step 3: Fetch inquiry summary grouped by purchaser and status
        summary_query = """
            SELECT
                purchaser_name,
                status,
                COUNT(*) AS total_inquiry
            FROM (
                SELECT DISTINCT ON (pi.id)
                    pi.id,
                    pi.status,
                    usm.user_name AS purchaser_name
                FROM product_inquiries pi
                LEFT JOIN products p ON p.product_id = pi.product_id
                LEFT JOIN skuvault_catalog sc ON p.sku = sc.parent_sku
                LEFT JOIN user_supplier_mapping usm ON sc.primary_supplier = usm.suppliers
                WHERE usm.user_name IS NOT NULL
            ) AS distinct_inquiries
            GROUP BY purchaser_name, status
        """
        result = conn.execute(text(summary_query))

        # Step 4: Structure the data
        summary_data = {}
        for row in result:
            purchaser_name = row[0]
            status = row[1].lower().replace(" ", "_")
            count = row[2]

            if purchaser_name not in summary_data:
                summary_data[purchaser_name] = {
                    "purchaser_name": purchaser_name,
                    "total": 0,
                    **{s: 0 for s in status_list}
                }

            summary_data[purchaser_name][status] += count
            summary_data[purchaser_name]["total"] += count

        # Step 5: Convert to list and sort
        data_list = list(summary_data.values())
        data_list.sort(
            key=lambda x: x.get(sort_field, "").lower() if isinstance(x.get(sort_field), str)
            else x.get(sort_field, 0),
            reverse=(sort_direction == "DESC")
        )

        response["data"] = data_list
        response["status"] = 200

    except Exception as e:
        response["status"] = 422
        response["message"] = str(e)
        raise e
    finally:
        if conn:
            conn.close()

    return response

def get_purchaser_dropdown(store, username):
    conn = None
    try:
        user = store_admin_db.fetch_user_by_username(store['id'], username)
        if not user:
            return {"message": "Unauthorized access."}, 401

        conn = new_pgdb.get_connection(store['id'])

        query = """
            SELECT DISTINCT user_name AS name, email_id AS email
            FROM user_supplier_mapping
            WHERE user_name IS NOT NULL
            ORDER BY user_name
        """
        results = conn.execute(text(query)).mappings().all()

        dropdown_data = [{"name": row["name"], "email": row["email"]} for row in results]
        return dropdown_data, 200

    except Exception as e:
        logger.error(f"Error in get_purchaser_dropdown: {str(e)}")
        return {"message": "Failed to load purchaser dropdown."}, 500
    finally:
        if conn:
            conn.close()

def get_product_price_list_csp_list(store, product_id):
    conn = new_pgdb.get_connection(store['id'])
    response = {
        "status": 400,
    }

    try:
        query = """
            SELECT
                c.customer_id,
                CONCAT(c.first_name, ' ', c.last_name) AS customer_name,
                c.customer_group_name,
                c.company,
                scr.rep_name AS sales_rep,
                pcp.price,
                c.email
            FROM product_customer_price pcp
            LEFT JOIN customers c ON pcp.customer_id = c.customer_id
            LEFT JOIN salesforce_customer_rep scr ON c.customer_id = scr.customer_id
            WHERE pcp.product_id = :product_id
        """
        results = conn.execute(text(query), {"product_id": product_id}).fetchall()
        customer_list = []
        for row in results:
            customer_list.append({
                "customer_id": row[0],
                "customer_name": row[1],
                "customer_group": row[2],
                "company": row[3],
                "sales_rep": row[4],
                "price": row[5],
                "customer_email": row[6]
            })
        response["data"] = {"data": customer_list}
        response["status"] = 200
    except Exception as e:
        response["status"] = 422
        response["message"] = str(e)
    finally:
        if conn:
            conn.close()
    return response


def save_product_attachments(store, product_id, request):

    response = {
        "status": 400,
        "message": "Failed to save attachments",
        "summary": {"total": 0, "successful": 0, "failed": 0},
        "data": {"successful": [], "failed": []}
    }

    try:
        # 1. Validate basic request data
        files = request.files.getlist('file')
        attachment_type = request.form.get('attachment_type')

        # Validate attachment type
        if attachment_type not in ['msda', 'pmta', 'mediakit']:
            return {"status": 400, "message": "Invalid attachment type. Must be 'msda', 'pmta', or 'mediakit'"}

        # Validate files
        if not files:
            return {"status": 400, "message": "No files provided"}

        response["summary"]["total"] = len(files)

        # 2. Check if product exists
        product = fetch_one_document_from_storefront_collection(
            store['id'],
            StoreDBCollections.PRODUCTS,
            {"id": int(product_id)},
            {"_id": 1, "attachments": 1}
        )

        if not product:
            return {
                "status": 404,
                "message": "Product not found",
                "summary": {"total": len(files), "successful": 0, "failed": len(files)},
                "data": {"successful": [], "failed": [{"reason": "Product not found"}]}
            }

        # 3. Prepare file storage location
        current_month_year = datetime.now().strftime('%b_%y').lower()
        filesystem_path = "/app/images/products"
        files_folder = os.path.join(filesystem_path, attachment_type, current_month_year)
        os.makedirs(files_folder, exist_ok=True)

        # 5. Track database updates to apply in batch
        successful_attachments = []

        # 6. Process each file
        for index, file in enumerate(files):

            # Size validation
            file.seek(0, 2)
            file_size = file.tell()
            file.seek(0)

            # if file_size > 5 * 1024 * 1024:  # 5MB limit
            #     response["data"]["failed"].append({
            #         "file_name": file.filename,
            #         "reason": "File size exceeds 5MB limit"
            #     })
            #     response["summary"]["failed"] += 1
            #     continue

            # Generate unique filename
            timestamp = str(round(datetime.now(timezone.utc).timestamp() * 1000))
            file_id = timestamp + "_" + str(index)
            file_ext = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
            new_filename = f"{product_id}_{timestamp}_{index}.{file_ext}"
            file_path = os.path.join(files_folder, new_filename)

            try:
                # Save file to disk
                file.save(file_path)

                # Prepare attachment data after file is saved
                attachment_data = {
                    "file_id": file_id,
                    "file_name": file.filename,
                    "file_path": file_path,
                    "file_size": file_size,
                    "file_type": "attachment" if file_ext in IMAGE_EXTENSIONS else "file" if file_ext in DOCUMENT_EXTENSIONS else "unknown",
                }

                # Track successful upload
                successful_attachments.append(attachment_data)
                response["data"]["successful"].append(attachment_data)
                response["summary"]["successful"] += 1

            except Exception as e:
                response["data"]["failed"].append({
                    "file_name": file.filename,
                    "reason": str(e)
                })
                response["summary"]["failed"] += 1

        # 7. Set appropriate response status
        if response["summary"]["successful"] > 0:
            if response["summary"]["failed"] == 0:
                response["status"] = 200
                response["message"] = f"All {response['summary']['total']} files uploaded successfully"
            else:
                response["status"] = 207  # Partial success
                response["message"] = f"{response['summary']['successful']} files uploaded successfully, {response['summary']['failed']} files failed"
        else:
            response["status"] = 400
            response["message"] = f"All {response['summary']['total']} files failed to upload"

    # Handle unexpected errors
    except Exception as e:
        response["status"] = 422
        response["message"] = str(e)

    return response


def download_product_attachments(store, product_id, query_params):
    response = {
        "status": 400,
        "message": "Failed to download attachments, please try again later",
    }
    
    attachment_type = query_params.get('attachment_type')
    file_id = query_params.get('file_id')
    
    # validate attachment type
    if attachment_type not in ['msda', 'pmta', 'mediakit']:
        response["status"] = 400
        response["message"] = "Invalid attachment type. Must be 'msda', 'pmta', or 'mediakit'"
        return response, 400
    
    try:
        product = fetch_one_document_from_storefront_collection(
            store['id'],
            StoreDBCollections.MSDA_PMTA_ATTACHMENTS,
            {"product_id": int(product_id)},
            {"attachments": 1}
        )
        
        # 1. Check if product exists
        if not product:
            response["status"] = 404
            response["message"] = "Product not found"
            return response, 404
        
        # 2. Check if attachment exists
        attachments = product.get('attachments', None)
        if attachments:
            msda_files = attachments.get('msda', [])
            pmta_files = attachments.get('pmta', [])
            mediakit_files = attachments.get('mediakit', [])
            has_attachments = len(msda_files) > 0 or len(pmta_files) > 0 or len(mediakit_files) > 0
        else:
            has_attachments = False
            
        if not has_attachments:
            response["status"] = 404
            response["message"] = "No attachments found for this product"
            return response, 404
        
        # 3. Get files based on attachment type
        files = attachments.get(attachment_type, [])
        if not files or len(files) == 0:
            response["status"] = 404
            response["message"] = f"No {attachment_type.upper()} files attached to this product"
            return response, 404
        
        # 3. If file_id is provided, filter and return that specific file
        if file_id:
            matched_file = next((f for f in files if f.get("id") == file_id), None)
            if not matched_file:
                response["status"] = 404
                response["message"] = f"File '{file_id}' not found under {attachment_type.upper()}"
                return response, 404

            file_path = matched_file.get('file_path')
            if not file_path or not os.path.exists(file_path):
                response["status"] = 404
                response["message"] = "File not found at the specified path"
                return response, 404

            return send_file(file_path, as_attachment=True, download_name=matched_file.get('file_name'))
        
        # 4. Process the files
        if len(files) == 1:
            # Single file - send directly
            file_path = files[0].get('file_path')
            if not file_path or not os.path.exists(file_path):
                response["status"] = 404
                response["message"] = f"File not found at the specified path"
                return response, 404
            
            # Get the original filename from the path
            filename = files[0].get('file_name')
            return send_file(file_path, as_attachment=True, download_name=filename)
        else:
            # Multiple files - create a zip
            memory_file = io.BytesIO()
            with zipfile.ZipFile(memory_file, 'w') as zf:
                for file_info in files:
                    file_path = file_info.get('file_path')
                    if file_path and os.path.exists(file_path):
                        # Add file to the zip using its basename
                        filename = file_info.get('file_name')
                        zf.write(file_path, filename)
            
            memory_file.seek(0)
            zip_filename = f"{product_id}_{attachment_type.upper()}_files.zip"
            return send_file(
                memory_file,
                as_attachment=True,
                download_name=zip_filename,
                mimetype='application/zip'
            )
            
    except Exception as e:
        response["status"] = 422
        response["message"] = str(e)

    return response