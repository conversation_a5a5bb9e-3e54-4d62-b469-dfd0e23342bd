from flask import request
import logging
import traceback
from api import APIResource
from live_carts import carts_data

logger = logging.getLogger()

class Cartsdata(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Live users GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            filter = query_params.get("filter", "").strip()
            is_live = str(query_params.get('is_live', 'true'))
            rep_name = query_params.get('rep_name')
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            res = carts_data.get_carts_data(store, is_live, filter, rep_name, sort_array, page, limit)
            if res['status'] == 200:
                    return res['data'], res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Live users GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class Cartdetails(APIResource):
    def get_executor(self, request, token_payload, store, customer_id):
        logger.debug("Entering Live users details GET")
        try:
            query_params = request.args.to_dict()
            filter = query_params.get("filter", "").strip()
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            channel = query_params.get("channel", "express").strip()
            res = carts_data.get_cart_details(store, filter, sort_array, customer_id, channel)
            if res['status'] == 200:
                    return {'data': res['data']}, res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Live users details GET")
    
    def put_executor(self, request, token_payload, store, customer_id):
        logger.debug("Entering Live users details PUT")
        
        try:
            payload = request.get_json(force=True)

            if customer_id:
                payload = request.get_json(force=True)
                username = token_payload['username']
                
                result = carts_data.update_customer_cart(store, customer_id, payload, username)
                if result['status'] == 200:
                    return {"message": result['message']}, result['status']
                else:
                    return {"message": result['message']}, result['status']
            return {"message": "Invalid request."}, 400
        
        finally:
            logger.debug("Exiting PUT Live users details")

    def get(self, customer_id):
        return self.execute_store_request(request, self.get_executor, customer_id)
    
    def put(self, customer_id):
        return self.execute_store_request(request, self.put_executor, customer_id)

class CheckLineItemAvailability(APIResource):
    def get_executor(self, request, token_payload, store, sku):
        logger.debug("Entering Cart Lineitem Availability Check GET")
        try:
            res = carts_data.check_line_item_availability(store, sku)
            if res['status'] == 200:
                return {'data': res['data']}, res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Cart Lineitem Availability Check GET")
    
    def get(self, sku):
        return self.execute_store_request(request, self.get_executor, sku)
    
class ProductsSuggestion(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Products suggestion GET")
        try:
            search = request.args.get('search', None)
            page = request.args.get('page', 1)
            limit = request.args.get('limit', 10)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':
                if search:
                    res = carts_data.get_products_suggestion(store['id'], search, page, limit)
                    if res['status'] == 200:
                        return res['data'], res['status']
                else:
                    return {'data': []}, 200
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Products suggestion GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ProductsVariants(APIResource):
    def get_executor(self, request, token_payload, store, parent_sku):
        logger.debug("Entering Products variants GET")
        try:
            page = request.args.get('page', 1)
            limit = request.args.get('limit', 10)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':
                res = carts_data.get_product_variants(store['id'], parent_sku, page, limit)
                if res['status'] == 200:
                    return res['data'], res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Products variants GET")
    
    def get(self, parent_sku):
        return self.execute_store_request(request, self.get_executor, parent_sku)
    
class CartLineItemReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Cart Lineitem Report GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            filter = query_params.get("filter", "").strip()
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            res = carts_data.get_lineitem_list(store, filter, sort_array, page, limit)
            if res['status'] == 200:
                    return res['data'], res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Cart Lineitem Report GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class CartLineItemCustomers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Cart lineitem customers GET")
        try:
            query_params = request.args.to_dict()
            variant_sku = query_params.get('variant_sku', '')
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            if variant_sku == '':
                return {'message': 'Variant SKU is required.'}, 400
            res = carts_data.get_lineitem_customers(store, variant_sku, sort_array)
            if res['status'] == 200:
                    return {'data': res['data']}, res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Cart lineitem customers GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)