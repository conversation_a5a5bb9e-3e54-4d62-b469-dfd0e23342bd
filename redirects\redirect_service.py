import logging
from fields.redirects_fields import redirect_fields
from utils.common import calculatePaginationData
from settings import get_paginated_records_updated

logger = logging.getLogger()


REDIRECT_DB  = 'redirects'


def get_all_redirects(store, body):
    body['filterBy'] = ['to_path', 'from_path']
    redirects, total_data_length, pagination_page, limit = get_paginated_records_updated(
        store, REDIRECT_DB, body, redirect_fields, '')

    result = []
    for redirect in redirects:
        res = {
            'id': redirect['id'],
            'from_path': redirect['from_path'],
            'to': redirect['to'],
            'to_url': redirect['to_url'].replace('https://www.midwestgoods.com', ''),
            'to_type': redirect['to_type'],
            'to_entity_id': redirect['to_entity_id'] if 'to_entity_id' in redirect else 0,
            'to_path': redirect['to_path'] if 'to_path' in redirect else ''
        }
        result.append(res)

    data = calculatePaginationData(
        result, pagination_page, limit, total_data_length)

    return data
