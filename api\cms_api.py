from flask import request
import logging
import traceback
from schemas.dynamic_webpage import webpage_schema, version_schema, webpage_update_schema
from api import APIResource
from utils import store_util, bc
from utils.common import parse_json
from products.categories import categories_list

logger = logging.getLogger()



class Cms(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            query_params = request.args.to_dict()
            res = categories_list.get_categories_data(store,query_params, store_util.get_cdn_base_url(store))
            return res, 200
        finally:
            logger.debug("Exiting Cart GET")

    def get(self, tenant_id, store_id):
        return self.execute_store_request(request, self.get_executor)
    
class CmsSubcategory(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            query_params = request.args.to_dict()
            res = categories_list.addSubCategoryFlag(store, query_params, store_util.get_cdn_base_url(store))
            return res, 200
        finally:
            logger.debug("Exiting CMS category script GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class CmsCategoryChild(APIResource):
    def get_executor(self, request, token_payload, store,id):
        try:
            res = categories_list.get_child_data(store, id, store_util.get_cdn_base_url(store))
            return res, 200
        finally:
            logger.debug("Exiting category child GET")

    def get(self,id):
        return self.execute_store_request(request, self.get_executor,id)

class CreateCms(APIResource):
    def post_executor(self, request, token_payload, store, token=None):
        try:
            req_body = request.get_json(force=True)
            # validated_data = webpage_schema.validate(req_body)
            res = self.service.get_cms_service().create_list(req_body)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409
        finally:
            logger.debug("Exiting Dynamic Webpage POST")
 
    def post(self):
        return self.execute_store_request(request, self.post_executor)
 
class CmsVersions(APIResource):
    def put_executor(self, request, token_payload, store, id):
        try:
            req_body = request.get_json(force=True)
            res = categories_list.set_version(store, req_body, id)

            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409
        finally:
            logger.debug("Exiting page PUT")

    def post_executor(self, request, token_payload, store, id):
        try:
            req_body = request.get_json(force=True)
            if req_body and 'is_customers_only' in req_body:
                res = categories_list.update_customer_only_flag(store, req_body, id)

                if res['status'] == 200:
                    return {"status": res['message']}, 200
                else:
                    return {"message": "Something went wrong at server!!"}, 500
            else:
                return {"message": "Please esure that your request body is correct."}, 400
        finally:
            logger.debug("Exiting CMS Category POST")

    def get_executor(self, request, token_payload, store, id):
        try:
            res = categories_list.get_category(store, id)
            return res, 200
        finally:
            logger.debug("Exiting Webpage GET")

    def put(self, id):
        return self.execute_store_request(request, self.put_executor, id)
    
    def post(self, id):
        return self.execute_store_request(request, self.post_executor, id)
    
    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id)

class CmsVersionsOperation(APIResource):
    def get_executor(self, request, token_payload, store, id):
        try:
            res = categories_list.get_cms_versions(store, id)
            return res, 200
        finally:
            logger.debug("Exiting page GET")

    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id)
    
class CmsVersionData(APIResource):
    def get_executor(self, request, token_payload, store, id):
        try:
            query_params = request.args.to_dict()
            res = categories_list.get_cms_versionData(store, query_params, id)
            return res, 200
        finally:
            logger.debug("Exiting page GET")

    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id)
    
# store and get images to server
class CategoryImageOperation(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            query_params = request.args.to_dict()
            res = categories_list.getImage(query_params)
            return res
        finally:
            logger.debug("Exiting CMS app builder category image GET")

    def post_executor(self, request, token_payload, store):
        try:
            req_body = request.files
            res = categories_list.setImage(req_body)

            if res['status'] == 200:
                return {"status": res['message']}, 200
            else:
                return {"status": res['message']}, 500
        finally:
            logger.debug("Exiting CMS app builder category Image Upload POST")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def post(self):
        return self.execute_store_request(request, self.post_executor)

class BCCategories(APIResource):
    def post_executor(self, request, token_payload, store, token=None):
        try:
            req_body = request.get_json(force=True)
            if 'created_by' in req_body:
                # STEP 1 - FETCH BC CATEGORIES
                res = bc.get_bc_categories(store_util.get_bc_api_creds(store))                
                bc_cat_arr=[]
                # STEP 2 - ITERATE OVER BC WEBPAGES AND CREATE PAGES INTO THE DB
                for category in res:  
                    bc_cat_arr.append(category['category_id'])
                    categories_list.create_bc_category(category, req_body,store)
                categories_list.check_all_categories(bc_cat_arr, store)
                categories_list.update_sub_category_flag(store)

                return {"message": "success"}, 200
            else:
                return {"message": "Please esure that your request body is correct."}, 400
        finally:
            logger.debug("Exiting Dynamic Category POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)   

class SyncAllSubNavigations(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Products Categories GET")
        try:
          res=categories_list.syncAllSubNavigation(store)
          return res, 200
        finally:
            logger.debug("Exiting Products Categories GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor) 