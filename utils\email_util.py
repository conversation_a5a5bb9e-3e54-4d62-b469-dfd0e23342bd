
from datetime import datetime
import smtplib
from email.message import EmailMessage
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import logging
import traceback
from utils import store_util
from jinja2 import Environment, FileSystemLoader

logger = logging.getLogger()

def send_po_request_email(data,po_id,po_status,po_created,customer_name,customer_rep,customer_rep_email,customer_email_id):
    order_id=data['bc_order_id']
    order_total=data['order_total']
    order_date=data['created_at']
    # order_datetime = datetime.strptime(order_date, '%Y-%m-%dT%H:%M:%S.%f')

    # order_formatted_date = order_datetime.strftime('%Y-%m-%d')
    # datetime_obj = datetime.strptime(po_created, "%Y-%m-%d %H:%M:%S.%f")

    # date_part = datetime_obj.strftime("%Y-%m-%d")

    env = Environment(loader=FileSystemLoader('.'))
    template = env.get_template('email_template.html')
    msg_body = template.render(order_id=order_id,order_total=order_total,order_date=order_date, po_id=po_id, po_status=po_status,po_created=po_created,customer_name=customer_name,customer_rep=customer_rep)

    sender_email = '<EMAIL>'
    receiver_email = [customer_email_id,customer_rep_email]
    subject = "PO created"
    password='rZ21*g3ZEYrV'
    smtp_server = 'smtp.gmail.com'
    smtp_port = 587

    send_html_email_direct(smtp_server, smtp_port, sender_email, password, subject, msg_body, sender_email, receiver_email)
 

def send_text_email(smtp_server, smtp_port, username, password, subject, msg_body, from_email, to_email):
    msg = EmailMessage()
    msg.set_content(msg_body)
    msg['Subject'] = subject
    msg['From'] = from_email
    msg['To'] = ", ".join(to_email)
    server = smtplib.SMTP(smtp_server, smtp_port)
    server.starttls()
    server.login(username, password)
    server.send_message(msg)
    server.close()
    return True
    
def send_comment_alert_email(store_id, email, card_id, comment):
    if store_id and email:
        store = store_util.get_store_by_id(store_id)        
        if store:
            mail_template = store_util.get_email_template(store_id, "comments_tag_alert")                        
            subject = mail_template['subject']
            from_email = mail_template['email']
            password = mail_template['password']
            # msg_body = mail_template['body']
            msg_body = f"{mail_template['body']}\n\nCard ID: {card_id}\nComment: {comment}"
            to_email = email.split(';')
            send_text_email("smtp.gmail.com", 587, from_email,
                            password, subject, msg_body, from_email, to_email)
            
def send_html_email_direct(smtp_server, smtp_port, username, password, subject, msg_body, from_email, to_email):
    try:
        msg = MIMEMultipart('related')
        msg['Subject'] = subject
        msg['From'] = from_email
        msg['To'] = ", ".join(to_email)
        msg.attach(MIMEText(msg_body, 'html'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(username, password)
        server.sendmail(from_email, to_email, msg.as_string())
        server.close()
        return True
    except Exception as ex:
        return_message = "Exception: " + str(traceback.format_exc())
        return False, return_message
