from flask import request
import logging
import traceback
from api import APIResource
from utils import bc
from rule_engine import variants_visibility_rules, products_visibility_rules, product_unhide_rules

logger = logging.getLogger()

class VariantsVisibilityRules(APIResource):
    def post_executor(self, request, payload, store):
        try:
            req_body = request.get_json(force=True)
            if req_body:
                res = variants_visibility_rules.create_rule(store, req_body)
                if res['status'] == 200:
                    return {"message": res['message']}, 200
                else:                     
                    return {"error": res["message"]}, res["status"]
            return {"error": "request payload is missing"}, 400
        finally:
            logger.debug("Exiting Variant hide rules POST")


    def get_executor(self, request, payload, store):
        try:
            body = request.args
            if body:
                res = variants_visibility_rules.get_rules(store, body)
                if res['status'] == 200:
                    return res['data'], 200
                else:                     
                    return {"error": res["message"]}, res["status"]
        finally:
            logger.debug("Exiting Variant hide rules GET")


    def put_executor(self, request, payload, store):
        try:
            req_body = request.get_json(force=True)
            if req_body:
                res = variants_visibility_rules.update_rule(store, req_body)
                if res['status'] == 200:
                    return {"message": res['message']}, 200
                else: 
                    return {"error": res["message"]}, res["status"]
            return {"error": "Request payload is missing"}, 400            
        finally:
            logger.debug("Exiting Variant hide rules PUT")

    def delete_executor(self, request, payload, store):
        try:                    
            variant_sku = request.args.get("variant_sku")
            if variant_sku:
                res = variants_visibility_rules.delete_rules(store, variant_sku)
                if res['status'] == 200:
                    return {"message": res['message']}, 200
                else:
                    return {"error": res['message']}, res["status"]
            return {"error": "Variant sku missing"}, 400        
        finally:
            logger.debug("Exiting Variant hide rules DELETE")

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
    def put(self):
        return self.execute_store_request(request, self.put_executor)
    
    def delete(self):
        return self.execute_store_request(request, self.delete_executor)
    
class ProductsVisibilityRules(APIResource):
    def get_executor(self, request, payload, store):
        try:
            body = request.args
            if body:
                res = products_visibility_rules.get_product_hide_rules(store, body)
                if res['status'] == 200:
                    return res['data'], 200
                else:                     
                    return {"error": res["message"]}, res["status"]
        finally:
            logger.debug("Exiting Product hide rules GET")

    def post_executor(self, request, payload, store):
        try:
            req_body = request.get_json(force=True)
            if req_body:
                res = products_visibility_rules.create_product_hide_rule(store, req_body)
                if res['status'] == 200:
                    return {"message": res['message']}, 200
                else:                     
                    return {"error": res["message"]}, res["status"]
            return {"error": "Request payload is missing"}, 400
        finally:
            logger.debug("Exiting Product hide rules POST")

    def put_executor(self, request, payload, store):
        try:
            req_body = request.get_json(force=True)
            if req_body:
                res = products_visibility_rules.update_product_hide_rule(store, req_body)
                if res['status'] == 200:
                    return {"message": res['message']}, 200
                else: 
                    return {"error": res["message"]}, res["status"]
            return {"error": "Request payload is missing"}, 400            
        finally:
            logger.debug("Exiting Product hide rules PUT")

    def delete_executor(self, request, payload, store):
        try:
            product_sku = request.args.get("product_sku")
            if product_sku:
                res = products_visibility_rules.delete_product_hide_rule(store, product_sku)
                if res['status'] == 200:
                    return {"message": res['message']}, 200
                else:
                    return {"error": res['message']}, res["status"]               
            return {"error": "Product id missing"}, 400
        finally:
            logger.debug("Exiting Product hide rules DELETE")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
    def put(self):
        return self.execute_store_request(request, self.put_executor)
    
    def delete(self):
        return self.execute_store_request(request, self.delete_executor)


class ProductRulesModalData(APIResource):
    def get_executor(self, request, payload, store):
        try:           
            res = products_visibility_rules.get_data_for_modal(store)
            if res['status'] == 200:
                return res['data'], 200
            else:                     
                return {"error": res["message"]}, res["status"]
        finally:
            logger.debug("Exiting Product hide rules modal data GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class UnhideProductRules(APIResource):
    def get_executor(self, request, payload, store):
        try:
            body = request.args
            if body:
                res = product_unhide_rules.get_product_unhide_rules(store, body)
                if res['status'] == 200:
                    return res['data'], 200
                else:                     
                    return {"error": res["message"]}, res["status"]
        finally:
            logger.debug("Exiting Product unhide rules GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
