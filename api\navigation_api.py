from flask import request
import logging
import traceback
from api import APIResource
from schemas.navigation import navigation_schema, navigation_update_schema
from navigations import navigation_service, sub_navigation_service

logger = logging.getLogger()

class Navigation(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Navigations GET")
        
        try:
            store_id = store['id']
            res = navigation_service.get_all_navigations(store_id)
            return res, 200
        finally:
            logger.debug("Exiting Navigations GET")

    def post_executor(self, request, token_payload, store, token=None):
        try:
            req_body = request.get_json(force=True)
            validated_data = navigation_schema.validate(req_body)
            id, name, short_code = navigation_service.create_navigation(store, validated_data)
            if id:
                sub_nav = sub_navigation_service.create_sub_nav(store, id, name, short_code)
                if sub_nav:
                    return {"status": "ok", "navigation_id": str(id)}, 200
            return {"message": "Bad request."}, 400
        finally:
            logger.debug("Exiting Navigation POST")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
        
    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
    
class NavigationOperations(APIResource):
    def put_executor(self, request, token_payload, store, navigation_id):
        try:
            req_body = request.get_json(force=True)
            store_id = store['id']
            validated_data = navigation_update_schema.validate(req_body)
            nav_name = req_body['name']
            id = navigation_service.update_navigation(store_id, validated_data, navigation_id)
            if id:
                res = sub_navigation_service.update_sub_navigation_name(store_id, navigation_id, nav_name)
            if id and res:
                return {"status": "ok"}, 200
            else:
                return {"message": "Something went wrong at server!!"}, 500
        finally:
            logger.debug("Exiting Navigation PUT")
    
    def delete_executor(self, request, token_payload, store, navigation_id):
        try:
            store_id = store['id']
            res = navigation_service.delete_navigation_by_id(store_id, navigation_id)

            if res == 1:
                success = sub_navigation_service.delete_sub_navigation_by_id(store_id, navigation_id)
                
                if success:
                    return {"status": "ok"}, 200
                return {"status": "partial failed"}, 500
            return {"status": "failed"}, 500
        finally:
            logger.debug("Exiting Navigation DELETE")
        
    def put(self, navigation_id):
        return self.execute_store_request(request, self.put_executor, navigation_id)
    
    def delete(self, navigation_id):
        return self.execute_store_request(request, self.delete_executor, navigation_id)

class NavigationBrands(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Navigation Options GET")
        try:
            query_params = request.args.to_dict()
            res = navigation_service.get_brands(store, query_params)
            return res, 200
        finally:
            logger.debug("Exiting Navigation Options GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class NavigationCategories(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Navigation Options GET")
        try:
            query_params = request.args.to_dict()
            res = navigation_service.get_categories(query_params, store)
            return res, 200
        finally:
            logger.debug("Exiting Navigation Options GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class NavigationWebPages(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Navigation Options GET")
        try:
            query_params = request.args.to_dict()
            res = navigation_service.get_web_pages(query_params, store)
            return res, 200
        finally:
            logger.debug("Exiting Navigation Options GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class SyncAllBrands(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Brands GET")
        try:
          res = navigation_service.sync_all_brands(store)
          return res, 200
        finally:
            logger.debug("Exiting Brands GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)