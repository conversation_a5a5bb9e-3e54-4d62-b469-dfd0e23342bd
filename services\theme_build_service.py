from services import Service
from datetime import datetime

class Builds(Service):
    def create_build(self, body):
        response = {
            "status": 400
        }  
        build = super().find_one({"deploy_id": str(body['deploy_id']), "status": "active"})
        if build:
            response['message'] = "can't enter duplicate entry"
            response['status'] = 500 
            return response
        
        body["created_at"] = int(datetime.utcnow().timestamp())                 
        id = super().create(body)       
        response['message'] = str(id)
        response['status'] = 200 

        return response
        

    def get_all_builds(self):
        navigations = super().find_all()
        res = super().processList(navigations)
        
        for nav in res:
            nav['created_at'] = datetime.utcfromtimestamp(nav['created_at']).strftime('%m/%Y, %H:%M%p')            

        return res
