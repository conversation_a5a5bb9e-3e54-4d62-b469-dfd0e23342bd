import datetime
from flask import request
import logging
from apiv2 import APIResource
from new_mongodb import callback_db
from utils import store_util
from orders.view_orders import order_payment

logger = logging.getLogger()

class CallbackAPI(APIResource):

    def post_executor(self, request, payload):
        logger.debug("Entering CallbackAPI POST")
        try:
            request_body = request.get_json(force=True)
            store_id = payload["store_id"]
            username = ''
            if payload and 'username' in payload:
                username = payload['username']
            store = store_util.get_store_by_id(store_id)
            request_body["_id"] = request_body["payload"]["order_id"]
            request_body["updated_by"] = username
            request_body["updated_by_client"] = payload.get("client_id", None)
            request_body["updated_at"] = datetime.datetime.now(datetime.timezone.utc)
            callback_db.create_callback(store, request_body)
            if request_body["type"] == "bc_order_payment_update":
                res = order_payment.update_order_payment(store, request_body)
                if not res:
                    logger.error(f"Error updating order payment")
                    return {"message": "Order payment update failed"}, 400
            return {"id": request_body["_id"], 
                    "type": request_body["type"], 
                    "store": store["name"]
                    }, 200
        finally:
            logger.debug("Exiting CallbackAPI POST")

    def post(self):
        return self.execute_request(request, self.post_executor)