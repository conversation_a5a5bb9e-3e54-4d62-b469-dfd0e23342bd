from flask import request
import logging
import traceback
from api import APIResource
from analytics import customer_order

logger = logging.getLogger()


class CustomerNoOrders(APIResource):
    def get_executor(self, request, token_payload, store):
      logger.debug("Entering Customer With No Orders Details GET")
      try:
        query_params = request.args.to_dict()
        result = customer_order.get_customers_with_no_orders(store, query_params)
        return result, 200
      finally:
        logger.debug("Exiting Customer With No Orders GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class CustomerOrderLongAgo(APIResource):
    def get_executor(self, request, token_payload, store):
      logger.debug("Entering Customer Who Ordered Long Ago GET")
      try:
        query_params = request.args.to_dict()
        result = customer_order.get_recently_inacctive_customers(store, query_params)
        return result, 200
      finally:
        logger.debug("Exiting Customer Who Ordered Long Ago GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class ActiveCustomersWithNoRecentOrders(APIResource):
    def get_executor(self, request, token_payload, store):
      logger.debug("Entering Active Customer With No Recent Orders Details GET")
      try:
        query_params = request.args.to_dict()
        result = customer_order.get_active_customer_with_no_recent_orders(store, query_params)
        return result, 200
      finally:
        logger.debug("Exiting Active Customer With No Recent Orders GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class CustomerSessionAnalysis(APIResource):
    def get_executor(self, request, token_payload, store, customer_id):
      logger.debug("Entering Customer Session Analysis GET")
      
      try:
        result = customer_order.get_customer_session_details(store, customer_id)
        return {'data': result}, 200
      finally:
        logger.debug("Exiting Customer Session Analysis GET")

    def get(self, customer_id):
        return self.execute_store_request(request, self.get_executor, customer_id)
    
class CustomerIPInfo(APIResource):
    def get_executor(self, request, token_payload, store, customer_id):
      logger.debug("Entering Customer IP Info GET")
      try:
        query_params = request.args.to_dict()
        result = customer_order.get_customer_ip_info(store, customer_id, query_params)
        return result, 200
      finally:
        logger.debug("Exiting Customer IP Info GET")

    def get(self, customer_id):
        return self.execute_store_request(request, self.get_executor, customer_id)