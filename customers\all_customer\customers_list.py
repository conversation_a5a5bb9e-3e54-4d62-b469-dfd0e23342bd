from datetime import datetime
from sqlalchemy import text
import new_pgdb
import new_mongodb
import new_utils
from new_mongodb import customer_db
from utils import bc, store_util
from utils.common import calculatePaginationData, parse_json, convert_to_timestamp
from datetime import timezone


def get_all_customers(payload, store):
        response = {
            'status': 400
        }
        conn = new_pgdb.get_connection(store['id'])
        try:
            page = int(payload.get('page', 1))
            limit = int(payload.get('limit', 10))
            sort_by = str(payload.get('sort_by', ''))
            sort_order = str(payload.get('sort_order', ''))
            search_term = payload.get('search_term', '')
            rep_name = payload.get('rep_name', '')
            tag_id = payload.get('tag_id', '')
            customer_group_id = payload.get('customer_group_id', '')

            # Split the IDs into lists
            tag_ids = [tag.strip() for tag in tag_id.split(',')] if tag_id else []
            if customer_group_id:
                customer_group_ids = list(map(int, customer_group_id.split(","))) if isinstance(customer_group_id, str) else customer_group_ids
            else:
                customer_group_ids = []


            rep_name_condition = ""
            if rep_name:
                rep_name_condition = f"AND scr.rep_name = '{rep_name}'"

            tag_condition = ""
            if tag_id:
                tag_condition = f"AND gt.tag_id = ANY(:tag_ids)"


            customer_group_condition = ""
            if customer_group_id:
                customer_group_condition = f"AND cu.customer_group_id = ANY(:customer_group_ids)"

            search_condition = ""
            if search_term and search_term != '':
                search_condition = search_condition + "AND (cu.first_name ILIKE '%" + search_term + "%' or cu.last_name ILIKE '%" + search_term + "%' or REPLACE(CONCAT(cu.first_name,' ',cu.last_name), ' ', '') ILIKE '%" + search_term.replace(" ","") + "%' or cu.email ILIKE '%" + search_term + "%' or cu.company ILIKE '%" + search_term + "%')"

            total_count_query = f"""SELECT COUNT(*) FROM (SELECT cu.customer_id,
                                    COUNT(o.order_id) AS order_count FROM customers cu
                                    LEFT JOIN orders o ON cu.customer_id = o.customer_id
                                    LEFT JOIN salesforce_customer_rep AS scr ON cu.customer_id = scr.customer_id
                                    LEFT JOIN bo_generic_tags_mapping AS gt ON cu.customer_id = gt.entity_int_id
                                    WHERE 1=1 {search_condition} {rep_name_condition} {tag_condition} {customer_group_condition}
                                    GROUP BY cu.customer_id) AS subquery"""

            result_count = conn.execute(text(total_count_query).params(tag_ids=tag_ids, customer_group_ids=customer_group_ids))
            total_count = int(result_count.scalar())

            query = f"""WITH salesforce_totals AS (
                            SELECT sod.customer_id, COALESCE(SUM(sod.amount_due), 0) AS total_amount_due
                            FROM salesforce_order_details sod GROUP BY sod.customer_id
                        )
                        SELECT
                            cu.customer_id,
                            CONCAT(cu.first_name, ' ', cu.last_name) AS name,
                            cu.email,
                            cu."store_credit_in_USD" AS storecredit,
                            cu.phone,
                            cu.customer_group_name,
                            cu.customer_group_id,
                            cu.company,
                            cu.date_created,
                            cu.first_name,
                            cu.last_name,
                            COUNT(o.order_id) AS order_count,
                            scr.rep_name,
                            scr.payment_term,
                            COALESCE(sod.total_amount_due, 0) AS total_amount_due,
                            scr.credit_limit,
                            MAX(o.order_created_date_time) AS last_order_date,
                            cu.is_invoice_visible,
                            cu.is_loyalty_point_enable
                        FROM customers cu
                        LEFT JOIN orders o ON cu.customer_id = o.customer_id
                        LEFT JOIN salesforce_customer_rep AS scr ON cu.customer_id = scr.customer_id
                        LEFT JOIN bo_generic_tags_mapping AS gt ON cu.customer_id = gt.entity_int_id
                        LEFT JOIN salesforce_totals sod ON cu.customer_id = sod.customer_id
                        WHERE 1=1 {search_condition} {rep_name_condition} {tag_condition} {customer_group_condition}
                        GROUP BY cu.customer_id, cu.first_name, cu."store_credit_in_USD", cu.phone, cu.customer_group_name, cu.date_created, scr.rep_name, scr.payment_term, sod.total_amount_due, scr.credit_limit, cu.is_invoice_visible, cu.company, cu.is_loyalty_point_enable
                    """

            if sort_by and sort_order:
                nulls_order = "NULLS FIRST" if sort_order.lower() == "asc" else "NULLS LAST"
                if sort_by in ['is_invoice_visible', 'is_loyalty_point_enable']:
                    nulls_order = "NULLS LAST"
                query += f" ORDER BY {sort_by} {sort_order} {nulls_order}"

            offset = (page - 1) * limit
            query += f" LIMIT {limit} OFFSET {offset}"

            result = conn.execute(text(query).params(tag_ids=tag_ids, customer_group_ids=customer_group_ids))
            res = result.fetchall()
            customers = []
            if res:
                for row in res:
                    row_data = {
                        'id': str(row[0]),
                        'name': row[1],
                        'email': row[2],
                        'storecredit': row[3],
                        'phone': row[4],
                        'customer_group_name': row[5],
                        'customer_group_id': row[6],
                        'company': row[7],
                        'date_created': convert_to_timestamp(row[8]),
                        'first_name': row[9],
                        'last_name': row[10],
                        'orders_count': row[11],
                        'customer_rep_name': row[12],
                        'payment_term': row[13],
                        'total_amount_due': round(row[14], 2) if row[14] else 0,
                        'credit_limit': row[15],
                        'last_order_date': convert_to_timestamp(row[16]),
                        'is_invoice_visible': row[17],
                        'is_loyalty_point_enable': row[18]
                    }
                    customers.append(row_data)

            data = new_utils.calculate_pagination(customers, page, limit, total_count)

            response['data'] = data
            response['status'] = 200

        except Exception as e:
            error_message = str(e)
            response['status'] = 422
            response['message'] = error_message
            raise e
        finally:
            if conn:
                conn.close()
        return response

def get_all_customer_groups(api, store_id):
        response = {
            'status': 400
        }
        response['data'] = bc.get_bc_customer_groups(api, {})
        conn = new_pgdb.get_connection(store_id)
        try:
            for customer in response['data']:
                query = text("""
                    SELECT
                        COUNT(c.customer_id) AS customer_count,
                        COUNT(CASE
                            WHEN (COALESCE(cg.is_invoice_visible, TRUE) = TRUE AND c.is_invoice_visible IS NULL)
                                 OR (COALESCE(cg.is_invoice_visible, TRUE) = FALSE AND c.is_invoice_visible = TRUE)
                                 OR (COALESCE(cg.is_invoice_visible, TRUE) = TRUE AND c.is_invoice_visible = TRUE)
                            THEN c.customer_id
                        END) AS visible_customer_count,
                        COUNT(CASE
                            WHEN (COALESCE(cg.is_loyalty_point_enable, TRUE) = TRUE AND c.is_loyalty_point_enable IS NULL)
                                OR (COALESCE(cg.is_loyalty_point_enable, TRUE) = FALSE AND c.is_loyalty_point_enable = TRUE)
                                OR (COALESCE(cg.is_loyalty_point_enable, TRUE) = TRUE AND c.is_loyalty_point_enable = TRUE)
                            THEN c.customer_id
                        END) AS loyalty_customer_count
                    FROM customers c
                    LEFT JOIN customer_group_print_invoice_visibility cg
                        ON c.customer_group_id = cg.customer_group_id::bigint
                    WHERE c.customer_group_id = :your_customer_group_id
                    GROUP BY cg.is_invoice_visible, cg.is_loyalty_point_enable
                """)
                result = conn.execute(query.params(your_customer_group_id=customer['id']))
                row = result.fetchone()
                customer['customer_count'] = row[0] if row else 0
                customer['visible_customer_count'] = row[1] if row else 0
                customer['loyalty_customer_count'] = row[2] if row else 0

                invoice_query = text("""
                    SELECT is_invoice_visible, is_loyalty_point_enable
                    FROM customer_group_print_invoice_visibility
                    WHERE customer_group_id = :your_customer_group_id
                """)
                invoice_result = conn.execute(invoice_query.params(your_customer_group_id=customer['id']))
                invoice_row = invoice_result.fetchone()
                customer['is_invoice_visible'] = invoice_row[0] if invoice_row else True
                customer['is_loyalty_point_enable'] = invoice_row[1] if invoice_row else True

        except Exception as e:
            error_message = str(e)
            response['status'] = 422
            response['message'] = error_message
            raise e
        finally:
            if conn:
                conn.close()
        return response

def get_customer_representatives(store_id):
        response = {
            'status': 400
        }
        conn = new_pgdb.get_connection(store_id)
        try:
            query = f"""SELECT DISTINCT(rep_name), rep_email FROM salesforce_customer_rep WHERE rep_name != '' order by rep_name"""
            result = conn.execute(text(query))
            reps = {}
            for row in result.fetchall():
                reps[row[1]] = row[0]

            if reps:
                response['data'] = reps
                response['status'] = 200
            else:
                response['message'] = "No data found"
                response['status'] = 400

        except Exception as e:
            error_message = str(e)
            response['status'] = 422
            response['message'] = error_message
            raise e
        finally:
            if conn:
                conn.close()
        return response

def get_customer_groups(store_id):
    response = {
        'status': 400
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        store = store_util.get_store_by_id(store_id)
        api = store_util.get_bc_api_creds(store)
        res = bc.get_bc_customer_groups(api, {})
        customer_groups = []
        if res:
            for customer_group in res:
                customer_groups.append({
                    'id': customer_group['id'],
                    'name': customer_group['name']
                })
        
        if customer_groups:
            customer_groups = sorted(customer_groups, key=lambda x: x['name'])
            
        response['data'] = customer_groups
        response['status'] = 200
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response

def get_customer(store, customer_id):
    res = customer_db.fetch_customer_by_id(store, customer_id)
    return parse_json(res)

def get_customer_representative_types(store_id):
    response = {
        'status': 400
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        query = """SELECT DISTINCT rep_type FROM salesforce_customer_rep ORDER BY rep_type"""
        result = conn.execute(text(query))
        rows = result.fetchall()

        customer_representative_types = []
        for row in rows:
            if row[0]:
                customer_representative_types.append({
                    'label': row[0],
                    'value': row[0],
                })

        if customer_representative_types:
            response['data'] = customer_representative_types
            response['status'] = 200
        else:
            response['message'] = "No valid customer representative types found"
            response['status'] = 404

    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response


def get_customer_dropdown(store_id):
    response = {'status': 200, 'data': []}
    conn = new_pgdb.get_connection(store_id)
    try:
        # Get unique customer_ids from product_customer_price
        id_query = """
            SELECT DISTINCT customer_id
            FROM product_customer_price
        """
        id_result = conn.execute(text(id_query))
        id_list = [row[0] for row in id_result.fetchall() if row[0]]

        if not id_list:
            return response

        ids_str = ','.join(map(str, id_list))

        # Fetch customer names
        query = f"""
            SELECT customer_id, first_name, last_name
            FROM customers
            WHERE customer_id IN ({ids_str})
            ORDER BY first_name, last_name
        """
        result = conn.execute(text(query))

        dropdown_data = []
        for row in result.fetchall():
            customer_id = row[0]
            full_name = f"{row[1]} {row[2]}".strip()
            dropdown_data.append({
                "label": full_name,
                "value": customer_id
            })

        response['data'] = dropdown_data
        return response

    except Exception as e:
        response['status'] = 422
        response['message'] = str(e)
        raise e
    finally:
        if conn:
            conn.close()

def get_customer_email_dropdown(store_id, search="", page=1, limit=10):
    response = {"status": 400}
    conn = new_pgdb.get_connection(store_id)

    try:
        page = max(1, int(page)) if page else 1
        limit = max(1, int(limit)) if limit else 10
        offset = (page - 1) * limit

        search_condition = ""
        params = {}

        if search:
            search_condition = """
                WHERE c.email ILIKE :search 
                OR c.first_name ILIKE :search 
                OR c.last_name ILIKE :search
            """
            params["search"] = f"%{search}%"

        query_total = f"""
            SELECT COUNT(*) FROM customers c
            {search_condition}
        """

        query_data = f"""
            SELECT c.customer_id, c.email, CONCAT(c.first_name, ' ', c.last_name) AS name
            FROM customers c
            {search_condition}
            ORDER BY c.email
            LIMIT :limit OFFSET :offset
        """

        params.update({"limit": limit, "offset": offset})

        total_records = conn.execute(text(query_total), params).scalar()
        result = conn.execute(text(query_data), params).fetchall()

        customers = [
            {"customer_id": row[0], "email": row[1], "name": row[2]}
            for row in result
        ]

        final_result = {
            "data": customers,
            "meta": {
                "current_page": page,
                "next_page": (page + 1 if page * limit < total_records else None),
                "total_count": total_records
            }
        }

        response["data"] = final_result
        response["status"] = 200

    except Exception as e:
        response = {"status": 500, "message": str(e)}
    finally:
        conn.close()

    return response

def get_email_by_customer_id(store_id, customer_id):
    query = text("SELECT email FROM customers WHERE customer_id = :customer_id")
    try:
        with new_pgdb.get_connection(store_id) as conn:
            result = conn.execute(query, {"customer_id": customer_id}).fetchone()
            if result:
                return result[0]
            return None
    except Exception as e:
        raise Exception(f"Failed to fetch email for customer_id {customer_id}: {str(e)}")
    
def get_customer_filtered_ids(store_id, id_list, customer_id):
    if not id_list:
        return []

    query = text("""
        SELECT id FROM product_customer_price 
        WHERE id IN :ids AND customer_id = :customer_id
    """)

    with new_pgdb.get_connection(store_id) as conn:
        result = conn.execute(query, {
            "ids": tuple(id_list),
            "customer_id": customer_id
        })
        return [str(row[0]) for row in result.fetchall()]
    
def update_invoice_visibility(store_id, payload, username):
    customer_group_id = payload.get("customer_group_id", None)
    is_invoice_visible = payload.get("is_invoice_visible")
    is_loyalty_point_enable = payload.get("is_loyalty_point_enable")

    if not customer_group_id:
        return {"status": 400, "message": "customer_group_id is required"}

    update_fields = ["modified_by = EXCLUDED.modified_by", "modified_at = NOW()"]
    insert_fields = ["customer_group_id", "modified_by"]
    insert_values = [":customer_group_id", ":modified_by"]
    query_params = {
        "customer_group_id": customer_group_id,
        "modified_by": username
    }

    if "is_invoice_visible" in payload:
        insert_fields.append("is_invoice_visible")
        insert_values.append(":is_invoice_visible")
        update_fields.insert(0, "is_invoice_visible = EXCLUDED.is_invoice_visible") 
        query_params["is_invoice_visible"] = is_invoice_visible

    if "is_loyalty_point_enable" in payload:
        insert_fields.append("is_loyalty_point_enable")
        insert_values.append(":is_loyalty_point_enable")
        update_fields.insert(0, "is_loyalty_point_enable = EXCLUDED.is_loyalty_point_enable")
        query_params["is_loyalty_point_enable"] = is_loyalty_point_enable

    try:
        query = text(f"""
            INSERT INTO customer_group_print_invoice_visibility ({", ".join(insert_fields)})
            VALUES ({", ".join(insert_values)})
            ON CONFLICT (customer_group_id)
            DO UPDATE SET {", ".join(update_fields)}
        """)

        with new_pgdb.get_connection(store_id) as conn:
            conn.execute(query, query_params)
            conn.commit()

        return {
            "status": 200,
            "message": "Invoice visibility and loyalty point status updated successfully."
        }

    except Exception as e:
        return {
            "status": 422,
            "message": f"Failed to update invoice visibility: {str(e)}"
        }
    
def update_customer_invoice_visibility(store_id, payload, customer_id):
    is_invoice_visible = payload.get("is_invoice_visible")
    is_loyalty_point_enable = payload.get("is_loyalty_point_enable")

    db_value = None
    loyalty_db_value = None
    mongo_update_fields = {}

    if "is_invoice_visible" in payload:
        db_value = True if is_invoice_visible is True else False if is_invoice_visible is False else None
        mongo_update_fields["is_invoice_visible"] = db_value

    if "is_loyalty_point_enable" in payload:
        loyalty_db_value = True if is_loyalty_point_enable is True else False if is_loyalty_point_enable is False else None
        mongo_update_fields["is_loyalty_point_enable"] = loyalty_db_value

    mongo_db = new_mongodb.get_store_db_client_for_store_id(store_id)

    # Prepare SQL update dynamically
    sql_fields = []
    sql_params = {"customer_id": customer_id}

    if "is_invoice_visible" in payload:
        sql_fields.append("is_invoice_visible = :is_invoice_visible")
        sql_params["is_invoice_visible"] = db_value

    if "is_loyalty_point_enable" in payload:
        sql_fields.append("is_loyalty_point_enable = :is_loyalty_point_enable")
        sql_params["is_loyalty_point_enable"] = loyalty_db_value

    sql_fields.append("date_modified = NOW()")

    if len(sql_fields) > 1:  # Ensure at least one field is updated besides date_modified
        try:
            query = text(f"""
                UPDATE customers
                SET {', '.join(sql_fields)}
                WHERE customer_id = :customer_id
            """)
            with new_pgdb.get_connection(store_id) as conn:
                result = conn.execute(query, sql_params)
                conn.commit()
                if result.rowcount == 0:
                    return {
                        "status": 404,
                        "message": "Customer not found."
                    }
        except Exception as e:
            return {
                "status": 500,
                "message": f"Failed to update SQL: {str(e)}"
            }

    if mongo_update_fields:
        try:
            mongo_query = {"id": int(customer_id)}
            mongo_update = {"$set": mongo_update_fields}
            mongo_result = mongo_db[customer_db.CUSTOMERS_COLLECTION].update_one(mongo_query, mongo_update)
            if mongo_result.matched_count == 0:
                return {
                    "status": 404,
                    "message": "Customer not found in MongoDB."
                }
        except Exception as e:
            return {
                "status": 500,
                "message": f"Failed to update MongoDB: {str(e)}"
            }

    return {
        "status": 200,
        "message": "Invoice visibility and loyalty point status updated successfully."
    }