import logging
import mongo_db
from bson import ObjectId

logger = logging.getLogger()

USER_ROLE_COLLECTION = "roles"

def fetch_roles():
    db = mongo_db.get_admin_db_client()
    return db[USER_ROLE_COLLECTION].find()

def fetch_role_by_id(id):
    db = mongo_db.get_admin_db_client()
    res = db[USER_ROLE_COLLECTION].find_one({"_id": ObjectId(str(id))})

    if not res:
        return []
    else:
        return res

def update_user_role_count(role, operation):
    user_count = {
        'total_users': int(role['total_users']) + int(operation)
    }
    db = mongo_db.get_admin_db_client()
    return db[USER_ROLE_COLLECTION].update_one({"_id": ObjectId(role['_id'])}, {'$set' : user_count})


def update_single_role(role_id, updated_permissions):
    db = mongo_db.get_admin_db_client()
    return db[USER_ROLE_COLLECTION].update_one({"_id": ObjectId(role_id)}, {'$set' : {"permissions": updated_permissions}})
