from flask import request
import logging
import traceback
from api import APIResource
from analytics import inventory_report, user_supplier_mapping
from new_mongodb import store_admin_db


logger = logging.getLogger()

class InventoryReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Inventory report data GET")
        try:
            # query_params = request.args.to_dict()
            res = inventory_report.get_inventory_report(store)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Inventory report data GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)