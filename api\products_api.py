from flask import request
import logging
import traceback
from api import APIResource
from schemas.products import products_list_update
from catalog import product_service, category_service
from products.all_products import customer_product_price_list, multi_store_products_list, products_list, price_list_logs, price_list_rules, product_order_history, price_list_csv, cost_analysis_report
from customers.all_customer import customers_list
from new_mongodb import store_admin_db
from analytics import classified_as, replenishment, user_supplier_mapping
from flask import make_response, send_file
from PIL import Image
from io import BytesIO

logger = logging.getLogger()

class Products(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Products GET")
        try:
          query_params = request.args.to_dict()
          res = products_list.get_products(store, query_params)
          # Return response ...
          return res, 200
        finally:
            logger.debug("Exiting Products GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class Product(APIResource):
    def get_executor(self, request, token_payload, store, product_id):
        logger.debug("Entering Product Details GET")
        try:
            if not product_id:
                return {"message": "Please enter product_id as a query params in request"}, 400
            query_params = request.args.to_dict()
            version_id = query_params.get('version_id', '')
            res = products_list.get_product(store, product_id, version_id)
            # Return response ...
            return res, 200
        finally:
            logger.debug("Exiting Product Details GET")

    def put_executor(self, request, token_payload, store, product_id):
        logger.debug("Entering Update Product Details PUT")
        try:
            if not product_id:
                return {"message": "Product ID not valid for the request."}, 400

            req_body = request.get_json(force=True)
            if token_payload and 'username' in token_payload:
                username = token_payload['username']
            if username != '':
                res, status = products_list.update_product(store, req_body, product_id, username)
            else:
                return {'messsage': 'Unauthorized.'}, 401

            # Return response ...
            return res, status
        finally:
            logger.debug("Exiting Update Product Details PUT")

    def get(self, product_id):
        return self.execute_store_request(request, self.get_executor, product_id)

    def put(self, product_id):
        return self.execute_store_request(request, self.put_executor, product_id)
class ProductVersions(APIResource):
    def get_executor(self, request, token_payload, store, product_id):
        logger.debug("Entering Product Versions GET")
        try:
            if not product_id:
                return {"message": "Please enter product_id as a query params in request"}, 400
            res = products_list.get_product_versions(store, product_id)
            # Return response ...
            return res, 200
        finally:
            logger.debug("Exiting Product Versions GET")

    def get(self, product_id):
        return self.execute_store_request(request, self.get_executor, product_id)

class ProductPageBuilderImages(APIResource):
    def post_executor(self, request, token_payload, store):
        try:
            req_body = {
                "image": request.files.get("image"),
                "type": request.form.get("type")
            }

            res = products_list.setImage(req_body)
            if res['status'] == 200:
                return {"status": res['message']}, 200
            else:
                return {"status": res['message']}, 400
        finally:
            logger.debug("Exiting Image Upload POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)

class StoreProductListing(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Products Listing GET")
        try:
            query_params = request.args.to_dict()
            if 'status' in query_params and query_params['status'] != '':
                res = products_list.get_products_list(store, query_params)
                return res, 200
            else:
                return {"message": "Please provide status value in parameter."}, 500
        finally:
            logger.debug("Exiting Products Listing GET")

    def post_executor(self, request, token_payload, store, token=None):
        try:
            req_body = request.get_json(force=True)
            validated_data = products_list_update.validate(req_body)
            res = products_list.update_product_listing(store, validated_data)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, res['status']
        finally:
            logger.debug("Exiting Update Products Listing POST")


    def post(self):
        return self.execute_store_request(request, self.post_executor)

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ProductsCategories(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Products Categories GET")
        try:
          query_params = request.args.to_dict()
          categories = category_service.get_all_categories(store, query_params)
          return categories, 200
        finally:
            logger.debug("Exiting Products Categories GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class CategoryDetails(APIResource):
    def get_executor(self, request, token_payload, store, category_id):
        logger.debug("Entering Product Category Details GET")
        try:
            res = category_service.get_category(store, category_id)
            return res, 200
        finally:
            logger.debug("Exiting Product Category Details GET")

    def delete_executor(self, request, token_payload, store, category_id):
        logger.debug("Entering Product Category Delete")
        try:
            if category_id:
                res = category_service.delete_category(store, category_id)
                if res:
                    return {"message": "Category deleted successfully"}, 200
                else:
                    return {"message": "Category did not detleted"}, 404
            return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Product Category Delete")

    def get(self, category_id):
        return self.execute_store_request(request, self.get_executor, category_id)

    def delete(self, category_id):
        return self.execute_store_request(request, self.delete_executor, category_id)


class SyncAllCategories(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Products Categories GET")
        try:
            res = category_service.sync_all_categories(store)
            return res, 200
        finally:
            logger.debug("Exiting Products Categories GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ProductsCategoriesTreeView(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Products Categories Treeview GET")
        try:
            query_params = request.args.to_dict()
            search = query_params.get('search', None)
            res = category_service.category_treeview(store, search)
            return res, 200
        finally:
            logger.debug("Exiting Products Categories Treeview GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class TaxClasses(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Tax Classes GET")
        try:
            res = products_list.get_tax_classes(store)
            if res['status'] == 200:
                return {'data': res['data']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Tax Classes GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class UploadCsvData(APIResource):

    def put_executor(self, request, token_payload, store):
        logger.debug("Entering Update Product Details PUT from CSV file")
        try:
            req_body = request.files.get('file', None)
            res = products_list.update_product_with_csv(store, req_body)
            if res['status'] == 200:
                return {'message': res['message']}, res['status']
            else:
                return {"message": res['message']}, res['status']
        finally:
            logger.debug("Exiting Update Product Details PUT from CSV file")

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Product details from CSV GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            res = products_list.get_csv_upload_details(store, page, limit, sort_array)
            if res['status'] == 200:
                return res['data'], res['status']
            else:
                return {"message": res['message']}, res['status']
        finally:
            logger.debug("Exiting Product details from CSV GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def put(self):
        return self.execute_store_request(request, self.put_executor)


class ProductPriceList(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Product price lists GET")
        try:
            query_params = request.args.to_dict()
            # is_debug = query_params.get('debug', "false")
            # if is_debug == "false":
            #     return {}, 200
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            sort_by = query_params.get('sort_by', '').strip()
            filter = query_params.get('filter', None).strip()
            tag_filter = query_params.get('tag_filter', None).strip()
            classified_as_filter = query_params.get('classified_as_filter', None).strip()
            classification_filter = query_params.get('classification_filter', None)
            products_filter = query_params.get('products_filter', None)
            user_filter = query_params.get('user_filter', None)
            supplier_filter = query_params.get('supplier_filter', None)
            top_products_filter = query_params.get('top_products_filter', None)
            cost_margin_filter = query_params.get('cost_margin_filter', None)
            username = token_payload['username']
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username)
            if user is None:
                return {"message": "Unauthorized access."}, 401
            res = products_list.get_product_price_lists(store, page, limit, sort_by, filter, user, tag_filter, classification_filter, classified_as_filter, products_filter, user_filter, supplier_filter, top_products_filter, cost_margin_filter)
            return res, 200
        finally:
            logger.debug("Exiting Product price lists GET")


    def get(self):
        return self.execute_store_request(request, self.get_executor)


class MultiProductPriceList(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Product price lists GET")
        try:
            query_params = request.args.to_dict()
            # is_debug = query_params.get('debug', "false")
            # if is_debug == "false":
            #     return {}, 200
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            sort_by = query_params.get('sort_by', '').strip()
            filter = query_params.get('filter', None).strip()
            tag_filter = query_params.get('tag_filter', None).strip()
            classified_as_filter = query_params.get('classified_as_filter', None).strip()
            classification_filter = query_params.get('classification_filter', None)
            products_filter = query_params.get('products_filter', None)
            user_filter = query_params.get('user_filter', None)
            supplier_filter = query_params.get('supplier_filter', None)
            top_products_filter = query_params.get('top_products_filter', None)
            cost_margin_filter = query_params.get('cost_margin_filter', None)
            store_name = query_params.get('store_name', 'both')
            primary_sort_store = query_params.get('primary_sort_store', None)
            username = token_payload['username']
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username)
            if user is None:
                return {"message": "Unauthorized access."}, 401
            res = multi_store_products_list.get_multi_store_product_price_lists([store['id']], page, limit, sort_by, filter, user, tag_filter, classification_filter, classified_as_filter, products_filter, user_filter, supplier_filter, top_products_filter, cost_margin_filter, store_name, primary_sort_store)
            return res, 200
        finally:
            logger.debug("Exiting Product price lists GET")


    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ProductPriceListDetailsMulti(APIResource):
    def get_executor(self, request, token_payload, store, product_sku):
        logger.debug("Entering Products price list details Treeview GET")
        try:
            query_params = request.args.to_dict()
            tag_filter = query_params.get('tag_filter', None).strip()
            store_name = query_params.get('store_name', None)
            username = token_payload['username']
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username)
            if user is None:
                return {"message": "Unauthorized access."}, 401
            res = multi_store_products_list.get_multi_store_product_price_list_details([store['id']], product_sku, user, tag_filter, store_name)
            return res, 200
        finally:
            logger.debug("Exiting Products price list details GET")


    def patch_executor(self, request, token_payload, store, product_sku):
      logger.debug("Entering Product price list PATCH")
      try:
        username = ''
        if 'username' in token_payload:
            username = token_payload.get('username', '')
        req_body = request.get_json(force=True)
        if username and username != '':
          res = products_list.update_product_price_list(store, product_id, req_body, username)
          if res['status'] == 200:
            return {'message': res['message']}, res['status']
          else:
            return {'message': res['message']}, res['status']
        else:
            return {"message": "Unauthorized access."}, 401
      finally:
        logger.debug("Exiting Product price list PATCH")


    def get(self, product_sku):
        return self.execute_store_request(request, self.get_executor, product_sku)

    def patch(self, product_sku):
        return self.execute_store_request(request, self.patch_executor, product_sku)


class ProductPriceListDetails(APIResource):
    def get_executor(self, request, token_payload, store, product_id):
        logger.debug("Entering Products price list details Treeview GET")
        try:
            query_params = request.args.to_dict()
            tag_filter = query_params.get('tag_filter', None).strip()
            username = token_payload['username']
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username)
            if user is None:
                return {"message": "Unauthorized access."}, 401
            res = products_list.get_product_price_list_details(store, product_id, user, tag_filter)
            return res, 200
        finally:
            logger.debug("Exiting Products price list details GET")


    def patch_executor(self, request, token_payload, store, product_id):
      logger.debug("Entering Product price list PATCH")
      try:
        username = ''
        if 'username' in token_payload:
            username = token_payload.get('username', '')
        req_body = request.get_json(force=True)
        if username and username != '':
          res = products_list.update_product_price_list(store, product_id, req_body, username)
          if res['status'] == 200:
            return {'message': res['message']}, res['status']
          else:
            return {'message': res['message']}, res['status']
        else:
            return {"message": "Unauthorized access."}, 401
      finally:
        logger.debug("Exiting Product price list PATCH")


    def get(self, product_id):
        return self.execute_store_request(request, self.get_executor, product_id)

    def patch(self, product_id):
        return self.execute_store_request(request, self.patch_executor, product_id)

class ProductPriceListCspList(APIResource):
    def get_executor(self, request, token_payload, store, product_id):
        logger.debug("Entering Product price list CSP list GET")
        try:
            res = products_list.get_product_price_list_csp_list(store, product_id)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Product price list CSP list GET")

    def get(self, product_id):
        return self.execute_store_request(request, self.get_executor, product_id)

class CostAnalysisReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Cost Analysis Report GET")
        try:
            query_params = request.args.to_dict()
            product_id = query_params.get('product_id', None).strip()
            page = query_params.get('page', None)
            limit = query_params.get('limit', None)
            sort_by = query_params.get('sort_by', None).strip()
            search = query_params.get('search', None).strip()
            username = token_payload['username']
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username)
            if user is None:
                return {"message": "Unauthorized access."}, 401
            res = cost_analysis_report.get_cost_analysis_report(store['id'], product_id, page, limit, sort_by, search)
            return res, 200
        finally:
            logger.debug("Exiting Cost Analysis Report GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class PriceListProductsDropdown(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Price List Products Dropdown GET")
        try:
            query_params = request.args.to_dict()
            page = query_params.get('page', None)
            limit = query_params.get('limit', None)
            search = query_params.get('search', None).strip()
            classification_filter = query_params.get('classification_filter', None).strip()
            user_filter = query_params.get('user_filter', None).strip()
            supplier_filter = query_params.get('supplier_filter', None).strip()
            hide_products = query_params.get('hide_products', False)
            res = products_list.get_filtered_products_dropdown(store, page, limit, search, classification_filter, user_filter, supplier_filter, hide_products)
            return res['data'], 200
        finally:
            logger.debug("Exiting Price List Products Dropdown GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class PriceListClassifiedAsDropdown(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Price List Classified As Dropdown GET")
        try:
            res = classified_as.get_classified_as_data(store, '', '', '', [])
            return res['data'], res['status']
        finally:
            logger.debug("Exiting Price List Classified As Dropdown GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ProductPriceListLogs(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Product price list logs GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            user_filter = query_params.get('user_filter', None).strip()
            price_list_filter = query_params.get('price_list_filter', None).strip()
            filter = query_params.get('filter', None).strip()
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            res = price_list_logs.get_price_list_logs(store, page, limit, sort_array, filter, user_filter, price_list_filter)
            return res, 200
        finally:
            logger.debug("Exiting Product price list logs GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ProductPriceListLogsUsers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Product price list logs users GET")
        try:
            res = price_list_logs.get_price_list_logs_users(store)
            return {"data":res}, 200
        finally:
            logger.debug("Exiting Product price list logs users GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ProductPriceListLogsDropdown(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Product price list logs dropdown GET")
        try:
            res = price_list_logs.get_price_list_logs_dropdown(store)
            return {"data":res}, 200
        finally:
            logger.debug("Exiting Product price list logs dropdown GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ProductPriceListStatusCheck(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Product price list status check GET")
        try:
            res = products_list.get_product_price_list_status_check(store)
            return res
        finally:
            logger.debug("Exiting Product price list status check GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
class ProductPriceListTags(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Product price list tags GET")
        try:
            res = products_list.get_product_price_list_tags(store['id'])
            return {'data': res['data']}, res['status']
        finally:
            logger.debug("Exiting Product price list tags GET")


    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ProductPriceListClassifications(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Product price list classifications GET")
        try:
            query_params = request.args.to_dict()
            user = query_params.get('user', None)
            res = products_list.get_product_price_list_classifications_and_suppliers(store['id'], user)
            if res['status'] == 200:
                return res['data'], 200
        finally:
            logger.debug("Exiting Product price list classifications GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class ProductInquiries(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Product Inquiries GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            filter = query_params.get('filter', '').strip()
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            rep_email = query_params.get('rep_email', '').strip()
            product_sku = query_params.get('product_sku', '').strip()
            status = query_params.get('status', '').strip()
            purchaser_name = query_params.get('purchaser', '').strip()
            username = token_payload['username']
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username)
            if user is None:
                return {"message": "Unauthorized access."}, 401
            res = products_list.get_product_inquiries(store, page, limit, sort_by, filter, rep_email, product_sku, sort_array, status, user, purchaser_name)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Product Inquiries GET")


    def get(self):
        return self.execute_store_request(request, self.get_executor)


class ProductInquiriesDetails(APIResource):
    def get_executor(self, request, token_payload, store, id):
        logger.debug("Entering Product Inquiry Details GET")
        try:
            res = products_list.get_product_inquiry_details(store, id)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Product Inquiry Details GET")

    def patch_executor(self, request, token_payload, store, id):
        logger.debug("Entering Product Inquiry Details PATCH")
        try:
            username = ''
            if 'username' in token_payload:
                username = token_payload.get('username', '')
            payload = request.get_json(force=True)
            if username and username != '':
                res = products_list.update_product_inquiry_details(store, payload, id, username)
                if res['status'] == 200:
                    return res['data'], res['status']
                else:
                    return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Product Inquiry Details PATCH")

    def patch(self, id):
        return self.execute_store_request(request, self.patch_executor, id)

    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id)

class CustomerRepresentatives(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Customer Representative GET")
        try:
            res = customers_list.get_customer_representatives(store['id'])
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Customer Representative GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class InquiriesColumnsListing(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering user specific columns GET")
        try:
            query_params = request.args.to_dict()
            username = token_payload['username']
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username)
            if user is None:
                return {"message": "Unauthorized access."}, 401
            res = replenishment.get_table_columns(store, user, query_params)

            if res['status'] == 200:
                return res['data'], 200
        finally:
            logger.debug("Exiting user specific columns GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class InquiriesColumns(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Table Columns SAVE")
        try:
            query_params = request.args.to_dict()
            username = token_payload['username']
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username)
            if user is None:
                return {"message": "Unauthorized access."}, 401
            if query_params:
                result = replenishment.save_table_columns(store, query_params, user, True)
                if result['status'] == 200:
                    return {"message": result['message']}, 200
                else:
                    return {"message": result['message']}, result['status']
            else:
                return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Table Columns SAVE")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ProductPriceListAppScript(APIResource):
    def put_executor(self, request, token_payload, store):
        logger.debug("Entering Product price list PATCH")
        try:
            req_body = request.get_json(force=True)
            store_id = request.headers.get('Store', None)

            res = products_list.process_google_sheet_request(store_id, req_body)

            if res['status'] == 200:
                return res['data'], res['status']
            else:
                return {'message': res['message']}, res['status']

        finally:
            logger.debug("Exiting Product price list PATCH")

    def put(self):
        return self.execute_google_app_script_request(request, self.put_executor)

class ProductPriceListDistributors(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Product price list distributors GET")
        try:
            res = price_list_rules.get_product_price_list_distributors(store['id'])
            return res, 200
        finally:
            logger.debug("Exiting Product price list distributors GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class PriceGroupDropdown(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Price Group Dropdown GET")
        try:
            res = price_list_rules.get_price_group_dropdown(store)
            return res, 200
        finally:
            logger.debug("Exiting Price Group Dropdown GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class PriceListCategoriesDropdown(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Price List Categories Dropdown GET")
        try:
            query_params = request.args.to_dict()
            distributor_id = query_params.get('distributor_id', None)
            res = category_service.category_treeview(store, distributor_id=distributor_id)
            return res, 200
        finally:
            logger.debug("Exiting Price List Categories Dropdown GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class PriceListRules(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Price List Rules GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            sort_by = query_params.get('sort_by', '').strip()
            filter = query_params.get('filter', None).strip()
            res = price_list_rules.get_price_list_rules(store, page, limit, sort_by, filter)
            return res, 200
        finally:
            logger.debug("Exiting Price List Rules GET")

    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Price List Rules POST")
        try:
            payload = request.get_json(force=True)
            username = token_payload['username'] if 'username' in token_payload else ''
            res = price_list_rules.create_price_list_rule(store, payload, username)
            if res['status'] == 200:
                return {'message': res['message']}, res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Price List Rules POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class PriceListRuleDetails(APIResource):
    def get_executor(self, request, token_payload, store, rule_id):
        logger.debug("Entering Price List Rule Details GET")
        try:
            res = price_list_rules.get_price_list_rule_details(store, rule_id)
            if res['status'] == 200:
                return res['data'], 200
        finally:
            logger.debug("Exiting Price List Rule Details GET")

    def patch_executor(self, request, token_payload, store, rule_id):
        logger.debug("Entering Price List Rule Details PATCH")
        try:
            payload = request.get_json(force=True)
            username = token_payload['username'] if 'username' in token_payload else ''
            res = price_list_rules.update_price_list_rule(store, rule_id, payload, username)
            if res['status'] == 200:
                return {'message': res['message']}, res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Price List Rule Details PATCH")

    def delete_executor(self, request, token_payload, store, rule_id):
        logger.debug("Entering Price List Rule Details DELETE")
        try:
            res = price_list_rules.delete_price_list_rule(store, rule_id)
            if res['status'] == 200:
                return {'message': res['message']}, res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Price List Rule Details DELETE")

    def post_executor(self, request, token_payload, store, rule_id):
        logger.debug("Entering Price List Rule Sync POST")
        try:
            payload = request.get_json(force=True)
            is_normal_sync = payload.get('is_normal_sync', False)
            res = price_list_rules.sync_price_list_rule(store, rule_id, is_normal_sync)
            return res, 200
        finally:
            logger.debug("Exiting Price List Rule Sync POST")

    def post(self, rule_id):
        return self.execute_store_request(request, self.post_executor, rule_id)

    def delete(self, rule_id):
        return self.execute_store_request(request, self.delete_executor, rule_id)

    def patch(self, rule_id):
        return self.execute_store_request(request, self.patch_executor, rule_id)

    def get(self, rule_id):
        return self.execute_store_request(request, self.get_executor, rule_id)


class ProductsOrderHistory(APIResource):
    def get_executor(self, request, token_payload, store, product_id):
      logger.debug("Entering Product Order History GET")
      try:
        query_params = request.args.to_dict()
        page = query_params.get('page', 1)
        limit = query_params.get('limit', 10)
        sort_by = query_params.get("sort_by", "").strip()
        sort_array = sort_by.split("/") if sort_by != '' else []
        username = ''
        if token_payload and 'username' in token_payload:
            username = token_payload['username']
        if username != '':
            result = product_order_history.get_product_order_history(store, product_id, page, limit, sort_array)
            if result['status'] == 200:
                return result['data'], 200
            else:
                return {'data': result['data']}, result['status']
        else:
            return {'messsage': 'Unauthorized.'}, 401
      finally:
        logger.debug("Exiting Order History GET")

    def get(self, product_id):
        return self.execute_store_request(request, self.get_executor, product_id)

class ProductCustomerPriceMapping(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Customer Product price lists GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            sort_by = query_params.get('sort_by', '').strip()
            search = query_params.get('search', '').strip()
            customer_ids = query_params.get('customer_ids', '').strip()
            sales_rep_emails = query_params.get('sales_rep_emails', '').strip()
            res = customer_product_price_list.get_customer_product_price_list(store['id'], page, limit, sort_by, search, customer_ids, sales_rep_emails)
            if res['status'] == 200:
                return res['data'], res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Customer Product price lists GET")

    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Customer Product price lists POST")
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']
            price = float(req_body.get('price', 0))
            email = req_body.get('email', None)
            parent_sku = req_body.get('parent_sku', None)

            if username != '':
                if email and parent_sku:
                    res = customer_product_price_list.add_customer_product_price(store['id'], email, parent_sku, price, username)
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': 'Parent SKU and Customer Email are required.'}, 400
            else:
                return {'message': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Customer Product price lists POST")

    def delete_executor(self, request, token_payload, store):
        logger.debug("Entering Customer Product price lists DELETE")
        try:
            query_params = request.args.to_dict()
            mapping_ids = query_params.get('ids', '')
            if mapping_ids != '':
                res = customer_product_price_list.delete_customer_product_price(store['id'], mapping_ids)
                if res:
                    return {"message": res['message']}, res['status']
            else:
                return {"message": "Mapping id(s) required."}, 400

        finally:
            logger.debug("Exiting Customer Product price lists DELETE")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def post(self):
        return self.execute_store_request(request, self.post_executor)

    def delete(self):
        return self.execute_store_request(request, self.delete_executor)

class UpadateCustomerProductPriceMapping(APIResource):
    def put_executor(self, request, token_payload, store, customer_id):
        logger.debug("Entering Update Customer Product price lists PUT")
        try:
            if not customer_id:
                return {"message": "customer_id is required"}, 400

            req_body = request.get_json(force=True)
            username = ""
            if token_payload and 'username' in token_payload:
                username = token_payload['username']
            if username != '':
                res = customer_product_price_list.update_customer_product_price(store['id'], req_body, customer_id, username)
                return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Update Customer Product price lists PUT")

    def put(self, customer_id):
        return self.execute_store_request(request, self.put_executor, customer_id)

class PriceListCsvUpload(APIResource):
    def post_executor(self, request, token_payload, store):
        try:
            logger.debug("Entering Price List Csv Upload POST")
            payload = request.get_json(force=True)
            username = ""
            if token_payload and 'username' in token_payload:
                username = token_payload['username']
            if username != '':
                res = price_list_csv.upload_price_list_csv(store, payload, username)
                return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Price List Csv Upload POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)

class PriceListCsvUploadStatus(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Price List Csv Upload Status GET")
        try:
            username = ""
            if token_payload and 'username' in token_payload:
                username = token_payload['username']
            if username != '':
                res = price_list_csv.get_csv_upload_status(store['id'])
                if res['status'] == 200:
                    return res, 200
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Price List Csv Upload Status GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class PriceListCsvExport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Price List Csv Export GET")
        try:
            query_params = request.args.to_dict()
            username = ""
            if token_payload and 'username' in token_payload:
                username = token_payload['username']
            if username != '':
                res = price_list_csv.export_price_list_csv(store, query_params, username)
                return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Price List Csv Export GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class PriceListMappedUsers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Mapped Users GET")
        try:
            res = user_supplier_mapping.get_mapped_users(store)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Mapped Users GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class PriceListColumnsListing(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering user specific columns GET")
        try:
            query_params = request.args.to_dict()
            username = token_payload['username']
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username)
            if user is None:
                return {"message": "Unauthorized access."}, 401
            res = replenishment.get_table_columns(store, user, query_params)

            if res['status'] == 200:
                return res['data'], 200
        finally:
            logger.debug("Exiting user specific columns GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class PriceListColumns(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Table Columns SAVE")
        try:
            query_params = request.args.to_dict()
            username = token_payload['username']
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username)
            if user is None:
                return {"message": "Unauthorized access."}, 401
            if query_params:
                result = replenishment.save_table_columns(store, query_params, user, True)
                if result['status'] == 200:
                    return {"message": result['message']}, 200
                else:
                    return {"message": result['message']}, result['status']
            else:
                return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Table Columns SAVE")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class PriceListUpdateWithCostPlusPercentage(APIResource):
    def get_executer(self, request, token_payload, store):
        logger.debug("Entering Table Columns SAVE")
        try:
            query_params = request.args.to_dict()
            price_list_id = query_params.get('price_list_id')
            percentage = query_params.get('percentage')
            is_replace = query_params.get('is_replace', False)
            if query_params:
                result = products_list.update_price_list_with_cost_plus_percentage(store['id'], price_list_id, percentage, is_replace)
                if result['status'] == 200:
                    return {"message": result['message']}, 200
                else:
                    return {"message": result['message']}, result['status']
            else:
                return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Table Columns SAVE")

    def get(self):
        return self.execute_store_request(request, self.get_executer)

class PriceListCustomerRepresentative(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering customer representative GET")
        try:
            res = customers_list.get_customer_representatives(store["id"])
            if res["status"] == 200:
                # Transform to label/value format
                transformed_data = [
                    {"label": name, "value": email}
                    for email, name in res["data"].items()
                ]
                return {"data": transformed_data}, res["status"]
            else:
                return {"message": res["message"]}, res["status"]
        finally:
            logger.debug("Exiting customer representative GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class PriceListCustomer(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            res = customers_list.get_customer_dropdown(store["id"])

            if res["status"] == 200:
                return {"data": res["data"]}, 200
            else:
                return {"message": res["message"]}, res["status"]

        except Exception as e:
            return {"message": str(e)}, 500

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class PriceListCustomerEmail(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            query_params = request.args.to_dict()
            search = query_params.get('search', '').strip()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))

            res = customers_list.get_customer_email_dropdown(store["id"], search, page, limit)

            if res["status"] == 200:
                return {"data": res["data"]}, 200
            else:
                return {"message": res["message"]}, res["status"]
        except Exception as e:
            return {"message": str(e)}, 500

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class InquiriesByRepStatus(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Inquiries By Rep Status GET")
        try:
            query_params = request.args.to_dict()
            search_value = query_params.get('search', '').strip().lower()
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            filter_rep_email = query_params.get('filter_rep_email', '').strip().lower()
            username = token_payload['username']
            if not username:
                return {"message": "Unauthorized access."}, 401

            user = store_admin_db.fetch_user_by_username(store['id'], username)
            if not user:
                return {"message": "Unauthorized access."}, 401

            res = products_list.get_inquiry_report_by_rep(store, sort_by, sort_array, search_value, filter_rep_email)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Inquiries By Rep Status GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class InquiriesByProductStatus(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Inquiries By Product Status GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            search_value = query_params.get('search', '').strip().lower()
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            username = token_payload.get('username')
            if not username:
                return {"message": "Unauthorized access."}, 401

            user = store_admin_db.fetch_user_by_username(store['id'], username)
            if not user:
                return {"message": "Unauthorized access."}, 401

            res = products_list.get_inquiry_report_by_product(store, page, limit, sort_by, sort_array, search_value)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Inquiries By Product Status GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class InquiriesByPurchaserStatus(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Inquiries By Product Status GET")
        try:
            query_params = request.args.to_dict()
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            username = token_payload.get('username')
            if not username:
                return {"message": "Unauthorized access."}, 401

            user = store_admin_db.fetch_user_by_username(store['id'], username)
            if not user:
                return {"message": "Unauthorized access."}, 401

            res = products_list.get_inquiry_report_by_purchaser(store['id'], sort_array)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Inquiries By Product Status GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class PurchaserDropdown(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering PurchaserDropdown GET")
        try:
            username = token_payload['username']
            return products_list.get_purchaser_dropdown(store, username)
        finally:
            logger.debug("Exiting PurchaserDropdown GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ProductAttachments(APIResource):
    def get_executor(self, request, token_payload, store, product_id):
        logger.debug("Entering Product Attachments Download GET")
        try:
            query_params = request.args.to_dict()
            return products_list.download_product_attachments(store, product_id, query_params)
        finally:
            logger.debug("Exiting Product Attachments Download GET")
            
    def post_executor(self, request, token_payload, store, product_id):
        logger.debug("Entering Product Attachments POST")
        try:
            return products_list.save_product_attachments(store, product_id, request)

        finally:
            logger.debug("Exiting Product Attachments POST")

    def get(self, product_id):
            return self.execute_store_request(request, self.get_executor, product_id)
            
    def post(self, product_id):
        return self.execute_store_request(request, self.post_executor, product_id)

class ProductAttachmentsPreview(APIResource):
    def get_executor(self, request, product_id, type, month, file_name):
        if not (type and file_name and month):
            return make_response({'error': 'Invalid request parameters'}, 400)

        file_path = '/app/images/products/' + type + "/" + month + "/" + file_name
        
        # Supported file formats
        IMAGE_EXTENSIONS = ('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp', '.svg', '.ico')

        # Handling Images
        if file_name.lower().endswith(IMAGE_EXTENSIONS):
            if request.args.get('thumbnail') == 'true':  # Serve a 100x100 thumbnail
                try:
                    with Image.open(file_path) as img:
                        img.thumbnail((100, 100))  # Resize while maintaining aspect ratio
                        img_byte_arr = BytesIO()
                        img.save(img_byte_arr, format='PNG')
                        img_byte_arr.seek(0)
                        return send_file(img_byte_arr, mimetype='image/png')
                except Exception as e:
                    logger.error(f"Error processing image: {e}")
                    return make_response({'error': 'Failed to process image'}, 500)
            else:
                return send_file(file_path, as_attachment=True)  # Serve full-size image

        return make_response({'error': 'Unsupported file format'}, 415)  # 415 Unsupported Media Type

    def get(self, product_id, type, month, file_name):
        return self.execute_open_api_request(request, self.get_executor, product_id, type, month, file_name)
