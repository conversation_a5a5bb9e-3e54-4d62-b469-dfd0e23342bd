from datetime import datetime, timezone
from new_mongodb import StoreAdminDBCollections, StoreDBCollections, liquidated_products_db, store_admin_db, update_document_in_admin_collection, fetchall_documents_from_storefront_collection, fetch_one_document_from_admin_collection, fetch_one_document_from_storefront_collection, get_store_db_client_for_store_id, process_documents
from utils.common import calculatePaginationData, convert_to_timestamp
import new_pgdb
from sqlalchemy import text
import logging
import traceback
from orders.view_orders import orders_list

logger = logging.getLogger()


def get_product_order_history(store, product_id, page, limit, sort_array):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        count_query = """
            SELECT COUNT(DISTINCT oli.order_id) FROM order_line_items oli 
            JOIN 
                orders o 
                ON oli.order_id = o.order_id
            JOIN 
                customers c 
                ON o.customer_id = c.customer_id
            WHERE 
                oli.product_id = :product_id"""

        query = """
            SELECT 
                o.order_id,
                oli.product_id,
                AVG(oli.product_price_after_discount) AS product_price_after_discount,
                SUM(oli.quantity) AS total_quantity,
                SUM(oli.total_tax) AS tax, 
                AVG(oli.product_price_after_discount) * SUM(oli.quantity) AS product_total,
                o.total_including_tax AS order_total_including_tax,
                o.order_created_date_time,
                c.first_name,
                c.last_name,
                c.company,
                c.customer_id,
                c.email,
                o.order_status_id
            FROM 
                order_line_items oli
            JOIN 
                orders o 
                ON oli.order_id = o.order_id
            JOIN 
                customers c 
                ON o.customer_id = c.customer_id
            WHERE 
                oli.product_id = :product_id AND o.order_status_id NOT IN (6, 13, 4, 5, 0)
            GROUP BY 
                o.order_id, oli.product_id, o.total_including_tax, o.order_day, o.order_month, o.order_year, c.first_name, c.last_name, c.company, c.customer_id, c.email, o.order_status_id
        """

        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ["product_price_after_discount", "total_quantity", "tax", "product_total", "order_total_including_tax", "order_created_date_time", "company"]:
                query += f" ORDER BY {sort_array[0]} {sort_direction}"
            if sort_array[0] == "name":
                query += f" ORDER BY c.first_name {sort_direction}"
            if sort_array[0] == "total_quantity_in_packets":
                query += f" ORDER BY total_quantity {sort_direction}"

        if page and limit:
            page = int(page)
            limit = int(limit)
            offset = (page - 1) * limit
            query += f" LIMIT {limit} OFFSET {offset}"

        
        count_result = conn.execute(text(count_query), {'product_id': product_id}).fetchone()
        total_count = count_result[0]

        pack_count = 0
        custom_fields = fetch_one_document_from_storefront_collection(store['id'], StoreDBCollections.PRODUCTS, {"_id": int(product_id)}, {"custom_fields": 1})
        for field in custom_fields.get('custom_fields', []):
            if field["name"].lower() == "pack count":
                pack_count = field["value"]
                break
        
        results = conn.execute(text(query), {"product_id": product_id}).fetchall()
        paginated_data = [
            {
                "order_id": row[0],
                "product_id": row[1],
                "product_price_after_discount": row[2],
                "total_quantity": row[3],
                "total_quantity_in_packets": round(row[3] * int(pack_count), 2),
                "pack_count": pack_count,
                "tax": round(row[4], 2),
                "product_total": round(row[5], 2),
                "order_total_including_tax": round(row[6], 2),
                "order_created_date_time": convert_to_timestamp(row[7]),
                "name": row[8] + " " + row[9],
                "company": row[10],
                "customer_id": row[11],
                "email": row[12],
                "order_status_id": row[13],
                "order_status": orders_list.get_order_status_name(row[13])
            }
            for row in results
        ]

        # Fetch product name
        product_name_query = """
            SELECT product_name
            FROM products
            WHERE product_id = :product_id
        """
        product_name_result = conn.execute(text(product_name_query), {"product_id": product_id}).fetchone()
        product_name = product_name_result[0] if product_name_result else "Unknown Product"


        data = calculatePaginationData(paginated_data, page, limit, total_count)
        data["meta"]["product_name"] = product_name
        response["status"] = 200
        response["data"] = data
    except Exception as e:
        logger.error(traceback.format_exc())
    finally:
        conn.close()
    return response

