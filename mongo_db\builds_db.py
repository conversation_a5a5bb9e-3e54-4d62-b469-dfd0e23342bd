import logging
import datetime
from bson import ObjectId
import mongo_db

logger = logging.getLogger()

BUILDS_COLLECTION = "builds"

def fetch_all_db_builds(store):
    db = mongo_db.get_store_db_client(store)    
    builds = db[BUILDS_COLLECTION].find({})  
    db_builds = []  
    for build in builds:
        temp = {}
        temp['title'] = build['title'] 
        temp['description'] = build['description'] 
        temp['deploy_id'] = build['deploy_id'] 
        temp['created_at'] = build['created_at'] 
        temp['status'] = build['status'] 
        temp['id'] = build['_id']                
        db_builds.append(temp)
           
    return db_builds

# def set_build_details_to_db(store, description):
#     db = mongo_db.get_store_db_client(store)
#     title
#     db[BUILDS_COLLECTION].insert_one(task)
