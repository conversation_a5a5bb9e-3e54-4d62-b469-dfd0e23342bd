import new_pgdb
import datetime
from sqlalchemy import text
from new_pgdb.analytics_db import AnalyticsDB
from appconfig import is_pgdb_read_only_enabled

def get_product_sale(store, product_id, start_date, end_date, period="day"):
    
    date_format = "%Y-%m-%d"
    date_start = datetime.datetime.strptime(start_date, date_format)
    date_end = datetime.datetime.strptime(end_date, date_format)
    date_diff = (date_end - date_start).days
    period = period.lower()

    query = None
    data = {}
    if period == "day":

        # if date_diff > 30:
        #     date_end = (date_start + datetime.timedelta(days=30))
        #     end_date = date_end.strftime(date_format)

        # date_index = date_start
        # while date_index <= date_end:
        #     data[date_index.strftime(date_format)] = {
        #         "quantity": 0
        #     }
        #     date_index = date_index + datetime.timedelta(days=1)

        query = f"""
        select 
            max(apt.product_name) as product_name, 
            sum(apt.quantity) as quantity, 
            sum(apt.total) / sum(apt.quantity) as price, 
            sum(apt.total) as total, 
            count(apt.order_id) as orders, 
            apt.order_date_time, 
            apt.order_year, 
            COALESCE(scr.type, 'Other') AS customer_type
        from 
            {AnalyticsDB.get_products_trend_table()} apt 
        inner join 
            salesforce_customer_rep scr on apt.customer_id = scr.customer_id
        where 
            apt.product_id={product_id} 
            and apt.order_date_time >= '{start_date}' 
            and apt.order_date_time <= '{end_date}' 
        group by 
            apt.order_date_time, apt.order_year, COALESCE(scr.type, 'Other')
        order by
            apt.order_year, apt.order_date_time;
        """
    
    elif period == "week":

        # if date_diff > 92:
        #     date_end = (date_start + datetime.timedelta(days=91))
        #     end_date = date_end.strftime(date_format)

        # date_index = date_start
        # while date_index <= date_end:
        #     week = date_index.strftime("%V")
        #     data[week+'_'+str(date_index.year)] = {
        #         "quantity": 0
        #     }
        #     date_index = date_index + datetime.timedelta(days=7)

        query = f"""
        select 
            max(apt.product_name) as product_name, 
            sum(apt.quantity) as quantity, 
            sum(apt.total) / sum(apt.quantity) as price, 
            sum(apt.total) as total, 
            count(apt.order_id) as orders, 
            apt.order_week, 
            apt.order_year, 
            COALESCE(scr.type, 'Other') AS customer_type
        from 
            {AnalyticsDB.get_products_trend_table()} apt 
        left join 
            salesforce_customer_rep scr on apt.customer_id = scr.customer_id
        where 
            apt.product_id={product_id} 
            and apt.order_date_time >= '{start_date}' 
            and apt.order_date_time <= '{end_date}' 
        group by 
            apt.product_id, apt.order_week, apt.order_year, COALESCE(scr.type, 'Other')
        order by
            apt.order_year, apt.order_week;
        """
    
    elif period == "month":
        
        # if date_diff > 365:
        #     date_end = (date_start + datetime.timedelta(days=365))
        #     end_date = date_end.strftime(date_format)

        # date_index = date_start
        # while date_index <= date_end:
        #     data[str(date_index.month)+'_'+str(date_index.year)] = {
        #         "quantity": 0
        #     }
        #     date_index = date_index + datetime.timedelta(days=31)
            
        query = f"""
        select 
            max(apt.product_name) as product_name, 
            sum(apt.quantity) as quantity, 
            sum(apt.total) / sum(apt.quantity) as price, 
            sum(apt.total) as total, 
            count(apt.order_id) as orders, 
            apt.order_month, 
            apt.order_year, 
            COALESCE(scr.type, 'Other') AS customer_type
        from 
            {AnalyticsDB.get_products_trend_table()} apt 
        left join 
            salesforce_customer_rep scr on apt.customer_id = scr.customer_id
        where 
            apt.product_id={product_id} 
            and apt.order_date_time >= '{start_date}' 
            and apt.order_date_time <= '{end_date}' 
        group by 
            apt.product_id, apt.order_month, apt.order_year, COALESCE(scr.type, 'Other')
        order by
            apt.order_year, apt.order_month;
        """
    
    elif period == "year":
        # if date_diff > 3650:
        #     date_end = (date_start + datetime.timedelta(days=3650))
        #     end_date = date_end.strftime(date_format)
        date_index = date_start
        while date_index <= date_end:
            data[str(date_index.year)] = {
                "quantity": 0
            }
            date_index = date_index + datetime.timedelta(days=365)
        query = f"""
        select 
            max(apt.product_name) as product_name, 
            sum(apt.quantity) as quantity, 
            sum(apt.total) / sum(apt.quantity) as price, 
            sum(apt.total) as total, 
            count(apt.order_id) as orders,
            apt.order_year, 
            COALESCE(scr.type, 'Other') AS customer_type
        from 
            {AnalyticsDB.get_products_trend_table()} apt 
        left join 
            salesforce_customer_rep scr on apt.customer_id = scr.customer_id
        where 
            apt.product_id={product_id} 
            and apt.order_date_time >= '{start_date}' 
            and apt.order_date_time <= '{end_date}' 
        group by 
            apt.product_id, apt.order_year, COALESCE(scr.type, 'Other')
        order by
            apt.order_year;
        """
    result = {
        "product_name": "",
        "quantity": 0,
        "avg_price": 0,
        "total": 0,
        "orders": 0,
        "data": {}
    }

    if query:
        query=query.replace('\n', '')
        conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
        try:
            rs = conn.execute(text(query))
            for row in rs:
                if row[1] > 0:
                    result["product_name"] = str(row[0])
                    result["quantity"] = result["quantity"] + int(row[1])
                    result["total"] = result["total"] + row[3]
                    result["orders"] = result["orders"] + int(row[4])
                    index = str(row[5])
                    if period in ['week','qtr','month']:
                        index = str(row[5]) + "_" + str(row[6])
                    if period == 'year':
                        data.setdefault(row[6], {})[index] = {
                            "quantity": int(row[1]),
                            "revenue": round(row[3], 2)
                        }
                    else:
                        data.setdefault(row[7], {})[index] = {
                            "quantity": int(row[1]),
                            "revenue": round(row[3], 2)
                        }
            
            # Convert inner dictionary to array format while maintaining outer structure
            for customer_type, time_periods in data.items():
                period_array = []
                for period_key, details in time_periods.items():
                    if isinstance(details, dict):
                        period_data = {}
                        period_data[period_key] = {
                            "quantity": details["quantity"],
                            "revenue": details["revenue"]
                        }
                        period_array.append(period_data)
                data[customer_type] = period_array

            exist_query=f"""
                select sum(pt.quantity) as quantity_count, sh.state from 
                {AnalyticsDB.get_products_trend_table()} pt, order_shipping_addresses sh
                where pt.order_id=sh.order_id and pt.product_id={product_id} and pt.order_date_time >= '{start_date}' and pt.order_date_time <= '{end_date}'
                group by sh.state
                order by quantity_count desc
                """
            records=conn.execute(text(exist_query))
            state_quantity={}
            for record in records:
                state_quantity[record[1]]={
                    "quantity":int(record[0])
                }
            result['data'] = data
            result['state_quantity']=state_quantity
            if result['quantity'] > 0:
                result['avg_price'] = round(result['total'] / result['quantity'], 2)
                result["total"] = round(result["total"], 2)
            if result['product_name'] == "":
                name_query = """select product_name from products where product_id={product_id}""".format(product_id=product_id) 
                name_record=conn.execute(text(name_query))
                for row in name_record:
                    result["product_name"] = str(row[0])
        finally:
            conn.close()

        return result