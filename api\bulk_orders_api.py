from flask import request
import logging
import traceback
from api import APIResource
from orders.view_orders import orders_list
from bulk_products import bulk_order_reports, bulk_product_info, bulk_product_list, purchase_order,purchase_orders_list,purchase_order_info, bulk_product_distribution, product_brands

logger = logging.getLogger()

class BulkOrders(APIResource):
    def get_executor(self, request, token_payload, store):
      logger.debug("Entering Bulk product info GET")
      try:
        query_params = request.args.to_dict()
        sku = query_params.get('sku', '')
        res = bulk_product_info.get_bulk_product_info(store,sku)
        if res['status'] == 200:
                return {'data': res['data']}, res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk product info GET")


    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Bulk product info POST")
        try:
            req_body = request.get_json(force=True)
            bc_sku = req_body.get('bc_sku', '')
            bc_product_id = req_body.get('bc_product_id', 0)
            bc_name = req_body.get('bc_name', '')
            created_at = req_body.get('created_at', '')
            name = req_body.get('name', '')
            product_image = req_body.get('product_image', '')
            display_qty = req_body.get('display_qty', 0)
            case_qty = req_body.get('case_qty', 0)
            status = req_body.get('status', '').lower()
            product_type = req_body.get('type','')
            orders = req_body.get('orders', 0)
            variants = req_body.get('variants', [])
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            brand_id = req_body.get('brand_id', 0)
            is_marketing_product = req_body.get('is_marketing_product', False)
            min_market_price = req_body.get('min_market_price', 0)

            count = 1            
            if username:
                if variants:
                    for variant in variants:
                        variant_bc_sku=variant.get('bc_sku','')
                        bc_upc = variant.get('bc_upc', '')
                        bc_variant_id = variant.get('bc_variant_id', 0)
                        bo_upc = variant.get('bo_upc', '')
                        option = variant.get('option', '')
                        po_option = variant.get('po_option','')
                        current_stock = variant.get('current_stock', '')
                        is_active = variant.get('is_active', True)

                        if count == 1:
                            res = bulk_product_info.post_bulk_product_info(bc_sku, bc_product_id, bc_name, created_at, name, product_image, display_qty, case_qty, status, product_type, orders,variant_bc_sku, bc_upc, bc_variant_id, bo_upc, option, po_option, current_stock, username, brand_id, min_market_price, is_marketing_product, is_active, is_bulk_order=True)
                            count = 0
                            if res['status'] == 422:
                                return {'message': res['message']}, res['status']
                        else:
                            res = bulk_product_info.post_bulk_product_info(bc_sku, bc_product_id, bc_name, created_at, name, product_image, display_qty, case_qty, status, product_type, orders, variant_bc_sku, bc_upc, bc_variant_id, bo_upc, option, po_option, current_stock, username, brand_id, min_market_price, is_marketing_product, is_active)
                        
                    if res['status'] == 200:
                        return {'message': res['message']}, res['status']
                    else:
                        return {'message': res['message']}, res['status']    
                else:
                    return {'message': 'Please check your payload.'}, 400
            else:
                return {'message': 'Unauthorized.'}, 401
        finally:
            logger.info("Bulk product info POST")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
    def post(self):
        return self.execute_store_request(request, self.post_executor)

class BulkOrdersProductList(APIResource):
    def get_executor(self, request, token_payload, store):
      logger.debug("Entering Bulk product list GET")
      try:
        query_params = request.args.to_dict()
        page = int(query_params.get('page', 1))
        limit = int(query_params.get('limit', 10))
        filter = query_params.get('filter', '')
        status = query_params.get('status', '')
        sort_by = query_params.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []
        product_type = query_params.get('type','')
        pending_allocation = query_params.get('pending_allocation', False)

        res = bulk_product_list.get_bulk_product_list(store, page, limit, filter, status, sort_array, product_type, pending_allocation)
        if res['status'] == 200:
                return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk product list GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class BulkOrderInfo(APIResource):
    def get_executor(self, request, token_payload, store,order_id):
      logger.debug("Entering Bulk order info GET")
      try:
        res = bulk_product_info.get_bulk_order_info(store,order_id)
        if res['status'] == 200:
                return {'data': res['data']}, res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk order info GET")

    def patch_executor(self, request, token_payload, store, order_id):
        logger.debug("Entering particular Bulk order info PATCH")
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':  
                res = bulk_product_info.update_bulk_order_info(req_body, username, order_id, store)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Particular Bulk order info PATCH")

    def get(self,order_id):
        return self.execute_store_request(request, self.get_executor,order_id)
    
    def patch(self, order_id):
        return self.execute_store_request(request, self.patch_executor, order_id)
     
class PreorderProductInfo(APIResource):
    def put_executor(self, request, token_payload, store, product_id):
      logger.debug("Entering Preorder Product PUT")
      try:
        username = ''
        if 'username' in token_payload:
            username = token_payload.get('username', '')
        req_body = request.get_json(force=True)       
        if username and username != '':    
          res = bulk_product_info.update_preorder_product_info(product_id, req_body, username)
          if res['status'] == 200:
                  return {'message': res['message']}, res['status']
          else:
              return {'message': res['message']}, res['status']
        else:
            return {"message": "Unauthorized access."}, 401
      finally:
        logger.debug("Exiting Preorder Product PUT")

    def put(self, product_id):
        return self.execute_store_request(request, self.put_executor, product_id)

class PurchaseOrders(APIResource):
    def get_executor(self, request, token_payload, store):
      logger.debug("Entering Purchase order listing GET")
      try:
        query_params = request.args.to_dict()
        page = int(query_params.get('page', 1))
        limit = int(query_params.get('limit', 10))
        status= query_params.get('status')
        filter = query_params.get('filter', '')
        sort_by = query_params.get('sort_by', '').strip()
        rep_filter = query_params.get('rep_filter', '')
        product_filter = query_params.get('product_filter', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []
        product_id_array = product_filter.split(";") if product_filter != '' else []
        res = purchase_orders_list.get_purchase_orders_list(page, limit, filter, sort_array,status, rep_filter, product_id_array)
        if res['status'] == 200:
                return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Purchase order listing GET")
    def get(self):
        return self.execute_store_request(request, self.get_executor)
class PurchaseOrderInfo(APIResource):
    def get_executor(self, request, token_payload, store,po_id):
      logger.debug("Entering Purchase order info GET")
      try:
        res = purchase_order_info.get_po_info(store,po_id)
        if res['status'] == 200:
                return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk order info GET")

    def put_executor(self, request, token_payload, store, po_id):
      logger.debug("Entering Purchase order status Update PUT")
      try:
        req_body = request.get_json(force=True) 
        reason = req_body.get('reason', '') 
        status = req_body.get('status', '')
        username = ''
        if token_payload and 'username' in token_payload:
            username = token_payload['username']            
        if username != '':  
            if status == '':
                return {"message": "Please provide status."}, 422 
            if status == 'delete':
                res = purchase_order_info.delete_po_info(po_id, reason, username, store['id'])
            elif status == 'cancel':
                res = purchase_order_info.cancel_entire_po(po_id, username, store['id'])
            if res['status'] == 200:
                    return {'message': res['message']}, res['status']
            else:
                return {'message': res['message']}, res['status']
        else:
            return {"message": "Unauthorized access."}, 401
      finally:
        logger.debug("Exiting Bulk order status Update PUT")

    def get(self,po_id):
        return self.execute_store_request(request, self.get_executor,po_id)
    
    def put(self,po_id):
        return self.execute_store_request(request, self.put_executor, po_id)

class PriceListsData(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Purchase order info GET")
        try:
            query_params = request.args.to_dict()
            customer_id = query_params.get('customer_id', '')
            price_list_id = query_params.get('price_list_id', '')
            variant_ids = query_params.get('variant_ids', '')
            res = purchase_order_info.get_price_lists_data(store['id'], variant_ids, customer_id, price_list_id)
            return res
        finally:
            logger.debug("Exiting Bulk order info GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class PriceListsDropdown(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Purchase order info GET")
        try:
            result, status = orders_list.get_price_list(store)
            if status == 200:
                return result, 200
            return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Bulk order info GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)

            

class PurchaseOrderCompleted(APIResource):
    def get_executor(self, request, token_payload, store,po_id):
      logger.debug("Entering Purchase order info GET")
      try:
        res = purchase_order_info.get_po_info_completed(po_id)
        if res['status'] == 200:
                return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk order info GET")

    def get(self,po_id):
        return self.execute_store_request(request, self.get_executor,po_id)
    
class PurchaseOrderCustomerDetails(APIResource):
    def get_executor(self, request, token_payload, store,customer_id):
      logger.debug("Entering Purchase order customer info GET")
      try:
        res = purchase_order_info.get_po_customer_info(store,customer_id)
        if res['status'] == 200:
                return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Purchase order customer info GET")

    def get(self,customer_id):
        return self.execute_store_request(request, self.get_executor,customer_id)
    
class PurchaseOrderCreate(APIResource):
    def post_executor(self, request, token_payload, store,po_id):
        logger.debug("Entering Bulk product info POST")
        try:
            req_body = request.get_json(force=True)
            is_draft=req_body.get('is_draft')
            po_id=po_id
            po_status=req_body.get('status')
            po_created=req_body.get('created_at')
            customer_name=req_body.get('customer_name')
            customer_rep=req_body.get('customer_rep')
            customer_rep_email=req_body.get('customer_rep_email')
            updated_by=req_body.get('updated_by')
            customer_id=req_body.get('customer_id')
            created_by=req_body.get('created_by')
            customer_email_id=req_body.get('customer_email_id')
            bulling_address_id=req_body.get('billing_address', 0)
            shipping_address_id=req_body.get('shipping_address', 0)
            payment_method=req_body.get('payment_method', '')
            line_items=req_body.get('line_items',[])
            shipping_cost=float(req_body.get('shipping_cost',0.0)) if req_body.get('shipping_cost') else 0.0
            shipping_method=req_body.get('shipping_method', 'Freight  $5000 Min Order Required, 5-10 Days Delivery Time (LTL)')
            shipping_option_id= req_body.get('shipping_option_id', '')
            consignment_id = req_body.get('consignment_id', '')
            cart_id = req_body.get('cart_id', '')
            is_using_igen_tax = req_body.get('is_using_igen_tax', True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']
            res=purchase_order_info.process_purchase_order_data(store,is_draft,po_id,po_status,po_created,customer_name,customer_rep,customer_rep_email,updated_by,customer_id,created_by,customer_email_id,line_items, bulling_address_id, payment_method, shipping_option_id, consignment_id, cart_id, username, shipping_address_id, shipping_cost, shipping_method, is_using_igen_tax)
            
            if res['status'] == 200:
                return {'message': res['message']}, res['status']
            else:
                return {'message': res['message']}, res['status'] 
        finally:
            logger.info("Bulk product info POST")
    
    def post(self,po_id):
        return self.execute_store_request(request, self.post_executor,po_id)

class CreateShippingForBulkOrder(APIResource):
    def post_executor(self, request, token_payload, store,po_id):
        logger.debug("Entering Bulk product info POST")
        try:
            req_body = request.get_json(force=True)
            po_id=po_id
            username = ''
            if 'username' in token_payload:
                username = token_payload.get('username', '')
            customer_id=req_body.get('customer_id')
            customer_name=req_body.get('customer_name')
            customer_email_id=req_body.get('customer_email_id')
            customer_rep_email=req_body.get('customer_rep_email')
            shipping_address_id=req_body.get('shipping_address', 0)
            line_items=req_body.get('line_items',[])
            if username and username != '':
                res=purchase_order.create_cart_and_consignment(store,po_id,customer_name,customer_rep_email,customer_id,customer_email_id,line_items, shipping_address_id, username)
            
                if res['status'] == 200:
                    return {'data': res['data']}, res['status']
                else:
                    return {'message': res['message']}, res['status'] 
            else:
                return {'message': 'Unauthorized access.'}, 401
        finally:
            logger.info("Bulk product info POST")
    
    def post(self,po_id):
        return self.execute_store_request(request, self.post_executor,po_id)
class ProductDistribution(APIResource):
    def get_executor(self, request, token_payload, store, order_id):
      logger.debug("Entering Product distribution GET")
      try:
        res = bulk_product_distribution.get_distribution_details(store, order_id)
        if res['status'] == 200:
                return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Product distribution GET")
    
    def post_executor(self, request, token_payload, store, order_id):
      logger.debug("Entering Product distribution POST")
      try:        
        username = ''
        if 'username' in token_payload:
            username = token_payload.get('username', '')
        req_body = request.get_json(force=True)
        bop_id = req_body.get('bop_id')
        bc_product_id = req_body.get('bc_product_id')
        bc_name = req_body.get('bc_name')
        bc_sku = req_body.get('bc_sku')
        is_qty_locked = req_body.get('is_qty_locked')
        is_active = req_body.get('is_active')
        is_published = req_body.get('is_published')
        distribution_id = req_body.get('distribution_id')
        is_po_locked = req_body.get('is_po_locked')
        # min_market_price = req_body.get('min_market_price', 0)
        variants = req_body.get('variants', [])
        if username and username != '':
          res = bulk_product_distribution.store_distribution_details(store, order_id, bc_product_id, bc_name, bc_sku, is_qty_locked, is_active, is_published, variants, username, distribution_id, is_po_locked)
          if res['status'] == 200:
                  return {'message': res['message']}, res['status']
          else:
              return {'message': res['message']}, res['status']
        else:
            return {"message": "Unauthorized access."}, 401
      finally:
        logger.debug("Exiting Product distribution POST")

    def patch_executor(self, request, token_payload, store, order_id):
      logger.debug("Entering Product distribution PATCH")
      try:
        username = ''
        if 'username' in token_payload:
            username = token_payload.get('username', '')
        req_body = request.get_json(force=True)
        distribution_id = req_body.get('distribution_id', None)         
        if username and username != '':    
          res = bulk_product_distribution.update_distribution_status(distribution_id, order_id, req_body, username, store['id'])
          if res['status'] == 200:
                  return {'message': res['message']}, res['status']
          else:
              return {'message': res['message']}, res['status']
        else:
            return {"message": "Unauthorized access."}, 401
      finally:
        logger.debug("Exiting Product distribution PATCH")
      
    def get(self, order_id):
        return self.execute_store_request(request, self.get_executor, order_id)

    def post(self, order_id):
        return self.execute_store_request(request, self.post_executor, order_id)
    
    def patch(self, order_id):
        return self.execute_store_request(request, self.patch_executor, order_id)
    

class DestributionDetails(APIResource):
    def put_executor(self, request, token_payload, store, distribution_id):
      logger.debug("Entering Product distribution PUT")
      try:
        username = ''
        if 'username' in token_payload:
            username = token_payload.get('username', '')
        req_body = request.get_json(force=True)       
        variants = req_body.get('variants', [])    
        is_qty_locked = req_body.get('is_qty_locked', True)
        # min_market_price = req_body.get('min_market_price', 0)
        bop_id = req_body.get('bop_id', 0)
        bc_product_id = req_body.get('bc_product_id')
        bc_sku = req_body.get('bc_sku')

        if username and username != '':    
          res = bulk_product_distribution.update_distribution_details(distribution_id, variants, is_qty_locked, bop_id, username, bc_product_id, bc_sku, store['id'], req_body)
          if res['status'] == 200:
                  return {'message': res['message']}, res['status']
          else:
              return {'message': res['message']}, res['status']
        else:
            return {"message": "Unauthorized access."}, 401
      finally:
        logger.debug("Exiting Product distribution PUT")        
    
    
    def put(self, distribution_id):
        return self.execute_store_request(request, self.put_executor, distribution_id)
    
class ProductDistributionLogs(APIResource):
    def get_executor(self, request, token_payload, store, order_id):
      logger.debug("Entering Product distribution logs GET")
      try:
        res = bulk_product_distribution.get_distribution_logs(store, order_id)
        if res['status'] == 200:
                return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Product distribution logs GET")
    
   
    def get(self, order_id):
        return self.execute_store_request(request, self.get_executor, order_id)
    
class ProductBrands(APIResource):
    def get_executor(self, request, token_payload, store):
      logger.debug("Entering Bulk product brands list GET")
      try:
        query_params = request.args.to_dict()
        page = int(query_params.get('page', 1))
        limit = int(query_params.get('limit', 10))
        filter = query_params.get('filter', '')
        status = query_params.get('status', '')
        sort_by = query_params.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []

        res = product_brands.get_product_brands_list(page, limit, filter, status, sort_array)
        if res['status'] == 200:
                return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk product list GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ProductBrand(APIResource):
    def post_executor(self, request, token_payload, store):
      logger.debug("Entering Bulk Product brand POST")
      try:        
        username = ''
        if 'username' in token_payload:
            username = token_payload.get('username', '')
        req_body = request.get_json(force=True)
        brand_name = req_body.get('brand_name', '')
        if username and username != '':
          if brand_name == '':
            return {"message": "Please provide brand name."}, 422
          res = product_brands.create_bulk_product_brand(store, brand_name, username)
          if res['status'] == 200:
                  return {'message': res['message']}, res['status']
          else:
              return {'message': res['message']}, res['status']
        else:
            return {"message": "Unauthorized access."}, 401
      finally:
        logger.debug("Exiting Bulk Product brand POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
class ProductBrandOperation(APIResource):
    
    def get_executor(self, request, token_payload, store, brand_id):
      logger.debug("Entering Bulk product brand GET")
      try:
        res = product_brands.get_product_brand(brand_id)
        if res['status'] == 200:
                return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk product brand GET")

    def delete_executor(self, request, token_payload, store, brand_id):
      logger.debug("Entering Bulk Product brand DELETE")
      try:        
          res = product_brands.delete_bulk_product_brand(brand_id)
          if res['status'] == 200:
                  return {'message': res['message']}, res['status']
          else:
              return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk Product brand DELETE")

    def patch_executor(self, request, token_payload, store, brand_id):
        logger.debug("Entering Bulk Product brand PATCH")
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':  
                res = product_brands.update_bulk_product_brand(brand_id, req_body, username)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Bulk Product brand PATCH")

    def get(self, brand_id):
        return self.execute_store_request(request, self.get_executor, brand_id)
    
    def delete(self, brand_id):
        return self.execute_store_request(request, self.delete_executor, brand_id)
    
    def patch(self, brand_id):
        return self.execute_store_request(request, self.patch_executor, brand_id)
    
class ProductBrandlist(APIResource):
    def get_executor(self, request, token_payload, store):
      logger.debug("Entering Bulk product brands list GET")
      try:    
        res = product_brands.get_all_brands()
        if res['status'] == 200:
                return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk product list GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class CustomerAddresses(APIResource):
    def get_executor(self, request, token_payload, store, po_id, customer_id):
      logger.debug("Entering Bulk product brands list GET")
      try:    
        res = purchase_order_info.fetch_customer_addresses(store, po_id, customer_id)
        if res['status'] == 200:
          return {'data': res['data']}, res['status']
        else:
          return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk product list GET")

    def get(self, po_id, customer_id):
        return self.execute_store_request(request, self.get_executor, po_id, customer_id)
    
class BOProductVariantDashbord(APIResource):
    def get_executor(self, request, token_payload, store,bop_id):
      logger.debug("Entering Bulk order product variant info GET")
      try:
        res = bulk_product_info.get_product_variant_dashboard_data(store, bop_id)
        if res['status'] == 200:
                return {'data': res['data']}, res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk order product variant info GET")

    def get(self, bop_id):
        return self.execute_store_request(request, self.get_executor, bop_id)
    
class BulkProductDropdown(APIResource):
    def get_executor(self, request, token_payload, store):
      logger.debug("Entering Bulk product dropdown list GET")
      try:
        res = bulk_product_list.get_all_products()
        if res['status'] == 200:
                return {'data': res['data']}, res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk product dropdown list GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class BOProductBCOrders(APIResource):
    def get_executor(self, request, token_payload, store, bop_id):
      logger.debug("Entering Bulk order product BC Orders info GET")
      try:
        query_params = request.args.to_dict() 
        status = query_params.get('status', '')
        filter = query_params.get('filter', '')
        res = bulk_product_info.get_product_bc_orders(store, bop_id, status, filter)
        if res['status'] == 200:
            return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk order product BC Orders info GET")

    def get(self, bop_id):
        return self.execute_store_request(request, self.get_executor, bop_id)

class BOProductPOOrders(APIResource):
    def get_executor(self, request, token_payload, store, bop_id):
      logger.debug("Entering Bulk order product BC Orders info GET")
      try:
        query_params = request.args.to_dict() 
        status = query_params.get('status', '')
        filter = query_params.get('filter', '')
        rep_filter = query_params.get('rep_filter', '')
        sort_by = query_params.get('sort_by', 'created_at/-1').strip()
        sort_array = sort_by.split("/") if sort_by != '' else ['created_at', '-1']
        is_from_popup = query_params.get('is_from_popup', False)
        res = bulk_product_info.get_product_po_orders(store, bop_id, status, filter, rep_filter, is_from_popup, sort_array)
        if res['status'] == 200:
                return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk order product BC Orders info GET")

    def get(self, bop_id):
        return self.execute_store_request(request, self.get_executor, bop_id)

class CustomerReport(APIResource):
    def get_executor(self, request, token_payload, store):
      logger.debug("Entering Bulk Order By Customer Report GET")
      try:
        query_params = request.args.to_dict()
        page = int(query_params.get('page', 1))
        limit = int(query_params.get('limit', 10))
        search = query_params.get('search', '')
        customer_rep = query_params.get('customer_rep', '')
        tags = query_params.get('tags', '')
        start_date = query_params.get('start_date', None)
        end_date = query_params.get('end_date', None)
        sort_by = query_params.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []

        res = bulk_order_reports.get_bulk_order_customer_report(store, page, limit, search, customer_rep, tags, start_date, end_date, sort_array)
        if res['status'] == 200:
                return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk Order By Customer Report GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class SalesRepresentativeReport(APIResource):
    def get_executor(self, request, token_payload, store):
      logger.debug("Entering Bulk Order By Sales Rep Report GET")
      try:
        query_params = request.args.to_dict()
        page = int(query_params.get('page', 1))
        limit = int(query_params.get('limit', 10))
        search = query_params.get('search', '')
        sort_by = query_params.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []

        res = bulk_order_reports.get_bulk_order_sales_rep_report(store, page, limit, search, sort_array)
        if res['status'] == 200:
                return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk Order By Sales Rep Report GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class SalesRepCustomerList(APIResource):
    def get_executor(self, request, token_payload, store):
      logger.debug("Entering Bulk Order By Sales Rep Customer List GET")
      try:
        query_params = request.args.to_dict()
        page = int(query_params.get('page', 1))
        limit = int(query_params.get('limit', 10))
        rep_email = query_params.get('rep_email', '')

        if rep_email != '':
            res = bulk_order_reports.get_bulk_order_sales_rep_customers(page, limit, rep_email)
            if res['status'] == 200:
                    return res['data'], res['status']
            else:
                return {'message': res['message']}, res['status']
        else:
            return {'message': 'Please Provice Sales Rep email.'}, 422 
      finally:
        logger.debug("Exiting Bulk Order By Sales Rep Customer List GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ByProductReport(APIResource):
    def get_executor(self, request, token_payload, store):
      logger.debug("Entering Bulk Order By Product Report GET")
      try:
        query_params = request.args.to_dict()
        page = int(query_params.get('page', 1))
        limit = int(query_params.get('limit', 10))
        search = query_params.get('search', '')
        sort_by = query_params.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []

        res = bulk_order_reports.get_bulk_order_by_product_report(store, page, limit, search, sort_array)
        if res['status'] == 200:
                return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk Order By Product Report GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ProductCustomerTrackingReport(APIResource):
    def get_executor(self, request, token_payload, store, bop_id):
      logger.debug("Entering Bulk Order By Customer Report GET")
      try:
        query_params = request.args.to_dict()
        page = int(query_params.get('page', 1))
        limit = int(query_params.get('limit', 10))
        search = query_params.get('search', '')
        customer_rep = query_params.get('customer_rep', '')
        tags = query_params.get('tags', '')
        sort_by = query_params.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []
        state = query_params.get('state', '')

        res = bulk_order_reports.get_bulk_product_customer_tracking_report(store, bop_id, page, limit, search, customer_rep, tags, sort_array, state)
        if res['status'] == 200:
                return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk Order By Customer Report GET")

    def get(self, bop_id):
        return self.execute_store_request(request, self.get_executor, bop_id)
    
class ProductCustomerTrackingCsv(APIResource):
    def get_executor(self, request, token_payload, store, bop_id):
        logger.debug("Entering Bulk Order By Customer Report CSV GET")
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            query_params = request.args.to_dict()
            query_params['username'] = username
            query_params['bop_id'] = bop_id
            res = bulk_order_reports.product_customer_tracking_csv(store, query_params)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, res["status"]
        finally:
            logger.debug("Exiting Bulk Order By Customer Report CSV GET")

    def get(self, bop_id):
        return self.execute_store_request(request, self.get_executor, bop_id)

class SuggestedCustomerReport(APIResource):
    def get_executor(self, request, token_payload, store):
      logger.debug("Entering Bulk Order By Suggested Customer Report GET")
      try:
        query_params = request.args.to_dict()
        page = int(query_params.get('page', 1))
        limit = int(query_params.get('limit', 10))
        search = query_params.get('search', '')
        customer_rep = query_params.get('customer_rep', '')
        # start_date = query_params.get('start_date', None)
        # end_date = query_params.get('end_date', None)
        sort_by = query_params.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []

        res = bulk_order_reports.get_suggested_customer_report(store, page, limit, search, customer_rep, sort_array)
        if res['status'] == 200:
                return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk Order By Suggested Customer Report GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class CustomerPurchasedFlavours(APIResource):
    def get_executor(self, request, token_payload, store, bop_id, customer_id):
      logger.debug("Entering Bulk Order Customer Purchased Flavours GET")
      try:
        res = bulk_order_reports.get_customers_purchased_flavours(store, bop_id, customer_id)
        if res['status'] == 200:
                return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk Order Customer Purchased Flavours GET")

    def get(self, bop_id, customer_id):
        return self.execute_store_request(request, self.get_executor, bop_id, customer_id)
    
class CustomersStates(APIResource):
    def get_executor(self, request, token_payload, store, bop_id):
        logger.debug("Entering Bulk Order Purchased products customer's states GET")
        try:
            res = bulk_order_reports.get_product_sold_customer_states(store, bop_id)
            if res['status'] == 200:
              return {"data": res['data']}, res['status']
            else:
              return {'message': res['message']}, res['status']
        finally:
          logger.debug("Exiting Bulk Order Purchased products customer's states GET")
       
    def get(self, bop_id):
        return self.execute_store_request(request, self.get_executor, bop_id)
    
class GlobalProductsReport(APIResource):
    def get_executor(self, request, token_payload, store):
      logger.debug("Entering Bulk Order Global products report GET")
      try:
        query_params = request.args.to_dict()
        page = int(query_params.get('page', 1))
        limit = int(query_params.get('limit', 10))
        search = query_params.get('search', '')
        customer_rep = query_params.get('customer_rep', '')
        sort_by = query_params.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []
        state = query_params.get('state', '')
        bop_ids = query_params.get('product_filter', '').strip()
        sorting_bop_id = query_params.get('sorting_bop_id', '')

        res = bulk_order_reports.get_bulk_products_gloabl_report(store, page, limit, search, customer_rep, bop_ids, sort_array, state, sorting_bop_id)
        if res['status'] == 200:
                return res['data'], res['status']
        else:
            return {'message': res['message']}, res['status']
      finally:
        logger.debug("Exiting Bulk Order Global products report GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class GlobalProductsReportCSV(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Bulk Order Global products report CSV GET")
        try:
          username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
          if not username:
              return {"message": "Unauthorized access."}, 401
          query_params = request.args.to_dict()
          query_params['username'] = username
          res = bulk_order_reports.bulk_products_global_report_csv(store, query_params)
          if res['status'] == 200:
              return {"message": res['message']}, 200
          else:
              return {"message": res['message']}, res["status"]
        finally:
            logger.debug("Exiting Bulk Order Global products report CSV GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)