from utils import bc, store_util
import task
from mongo_db import pricing_db
import mongo_db

def fetch_price_list_records(store, price_list_id, product_id):
    page = 1
    qp = {
        "page": page,
        "limit": 10000,
        "include": "sku",
        "product_id:in": product_id
        
    }
    records = []
    api = "v3/pricelists/" + str(price_list_id) + "/records"
    while True:
        qp["page"] = page
        bc_api = store_util.get_bc_api_creds(store)
        res = bc.call_api(bc_api, "GET", api, query_params=qp)
        if res.status_code == 200:
            res = res.json()
            data = res["data"]
            meta = res["meta"]["pagination"]
            if meta["total"] == 0:
                break
            documents = []
            for row in data:
                if "sku" in row:
                    row['_id'] = row['sku']
                    records.append(row)
                    documents.append(row)
            if len(documents) > 0:
                mongo_db.upsert_documents(store, pricing_db.get_price_list_collection(price_list_id), documents)
            if meta['total_pages'] == meta['current_page']:
                break
            page = page + 1
        else:
            break
    return records

def fetch_price_list_assignments(store):
    req_body = bc.get_bc_api_request_object(url="v3/pricelists/assignments")
    bc_api = store_util.get_bc_api_creds(store)
    price_list_assignments = bc.fetch_all(bc_api, req_body)
    documents = []
    for assignment in price_list_assignments:
        assignment["_id"] = assignment["price_list_id"]
        documents.append(assignment)
        task.update_price_list.apply_async(args=[store, assignment["price_list_id"], True])
    mongo_db.upsert_documents(store, pricing_db.ASSIGNMENT_COLLECTION, documents)
    return True, {"message": "Completed."}

def update_price_list(store, price_list_id, req_body):
    url = f'v3/pricelists/{price_list_id}/records'
    bc_api = store_util.get_bc_api_creds(store)
    res = bc.call_api(bc_api, "PUT", url, query_params={} ,req_body=req_body) 

    # success ...   
    if res.status_code == 200:
        return res.json(), 200
    else:
        # unprocess able entity...
        return res.json(), res.status_code

def fetch_price_lists(store):
    url = f'v3/pricelists'
    bc_api = store_util.get_bc_api_creds(store)
    res = bc.call_api(bc_api, "GET", url, query_params={}) 

    # success ...   
    if res.status_code == 200:
        return res.json(), 200
    else:
        # unprocess able entity...
        return res.json(), res.status_code
    
def delete_price_list(store, variant_ids, price_list_id):
    variant_ids_str = ','.join(map(str, variant_ids))
    url = f'v3/pricelists/{price_list_id}/records'
    bc_api = store_util.get_bc_api_creds(store)
    qp = {"variant_id:in": variant_ids_str}
    res = bc.call_api(bc_api, "DELETE", url, query_params=qp) 

    # success ...   
    if res.status_code == 204:
        return res, 204
    else:
        # unprocess able entity...
        return res, res.status_code
    
def fetch_price_list_by_id(store, price_list_id):
    url = f'v3/pricelists/{price_list_id}'
    bc_api = store_util.get_bc_api_creds(store)
    res = bc.call_api(bc_api, "GET", url, query_params={}) 

    # success ...   
    if res.status_code == 200:
        return res.json(), 200
    else:
        # unprocess able entity...
        return res.json(), res.status_code