from pymongo.collation import Collation
from analytics import _parse_json, _process_list
from new_mongodb import (
    fetchall_documents_from_admin_collection,
    count_documents_admin_collection
)


def get_paginated_records_updated(store, collection_name, payload, fields, additionalQuery):
    sort = {
        'sort_by': payload['sort_by'] or 'date_created'
    }
    if payload['sort_order'] == 'asc':
        sort['sort_order'] = 1
    else:
        sort['sort_order'] = -1

    def create_reg_ex_query(filterBy, filter):
        query = {
            "$or": [],
        }

        for i in filterBy:
            query['$or'].append({i: {"$regex": filter, "$options": "i"}},)

        if "type" not in query and "type" in payload:
            query["type"] = payload["type"]

        if "status" in payload:
            if payload["status"] == "active":
                query["is_visible"] = True
            elif payload["status"] == "inactive":
                query["is_visible"] = False
            elif payload["status"] == "out_of_stock":
                query["$expr"] = {"$lte": ["$inventory_level", "$inventory_warning_level"]}

        query.update(additionalQuery)
        return query

    limit = int(payload.get("limit", 10))
    page = int(payload.get("page", 1))
    skips = payload.get('skips', 0)

    query = create_reg_ex_query(payload.get("filterBy", []), payload.get('filter', ''))

    # Calculate number of records to skip ...
    skips = limit * (page - 1)
    collation = Collation(locale='en', strength=2)

    data = fetchall_documents_from_admin_collection(
        store['id'], collection_name, query, fields
    ).collation(collation).sort(sort['sort_by'], sort['sort_order']).skip(skips).limit(limit)
    # ProcessList ...
    data = _process_list(data)

    document_length = count_documents_admin_collection(store['id'], collection_name, query)

    return _parse_json(data), document_length, page, limit
