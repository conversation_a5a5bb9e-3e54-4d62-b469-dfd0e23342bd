import os
from flask import make_response, send_file
import requests
from werkzeug.utils import secure_filename
import logging
import json
from datetime import datetime, timedelta
from mongo_db import app_builder_db

logger = logging.getLogger()


def get_all_main_app(body):
    result = {
        "status": 400,
        "message": "Something went wrong."
    }
    search = body.get('filterValue', '').strip()
    sections = app_builder_db.fetch_all_main_app(search)
    if sections:
        data = []
        for section in sections:
            temp = {}
            temp['id'] = section['id']
            temp['name'] = section['name']
            temp['description'] = section['description']
            temp['icon_image'] = section['icon_image']
            temp['template'] = section['template']
            temp['status'] = section['status']
            data.append(temp)
        result = {
            "status": 200,
            "data": data
        }
    else:
        result = {
            "status": 200,
            "data": "No sections found."
        }
    return result

def get_all_subsection(body, parent):
    result = {
        "status": 400,
        "message": "Something went wrong."
    }
    sections = app_builder_db.fetch_all_sub_section(parent)
    if sections:
        data = []
        for section in sections:
            temp = {}
            temp['name'] = section['name']
            temp['description'] = section['description']
            temp['status'] = section['status']
            data.append(temp)
        result = {
            "status": 200,
            "data": data,
            "message": "Sub section retrived successfully."
        }
    else:
        result = {
            "status": 404,
            "message": "No sub sections found."
        }
    return result

def get_all_leaf(body, grand_parent, parent):
    
    result = {
        "status": 400,
        "message": "Something went wrong."
    }
  
    grand_parent = grand_parent.strip().lower().replace(' ', '_')
    parent = parent.strip().lower().replace(' ', '_')
    
    sections = app_builder_db.get_leaf_section( grand_parent, parent)
     
    if sections:
        data = []
        for section in sections:
            temp = {}
            temp['name'] = section['name']
            temp['description'] = section['description']
            temp['status'] = section['status']
            data.append(temp)
        result = {
            "status": 200,
            "data": data,
            "message": "Leaf section retrived successfully."
        }
    else:
        result = {
            "status": 200,
            "data": [],
            "message": "No leaf sections found."
        }
    return result

# create parent section (1st level)
def CreateMainApp(store, payload):
    result = {
        "status": 400,
        "message": "Something went wrong."
    }
    collection_name = payload['name'].strip().lower().replace(' ', '_')
    section = app_builder_db.fetch_section_by_name(payload['name'])
    if section:
        result = {
            "status": 409,
            "message": "name: Duplicate entry not allowed."
        }
        return result
    template = {
        "collection_name": collection_name,
        "form": []
    }
    payload['template'] = template

    res = app_builder_db.set_main_app_to_db(payload)
    if res:
        col_res = app_builder_db.create_collection(collection_name)
        result = {
            "status": 200,
            "message": "App created successfully."
        }

    return result

# create child section (2nd level)


def CreateSubSection(store, payload, parent_section):
    result = {
        "status": 400,
        "message": "Something went wrong."
    }
    sub_section_name = payload['name'].strip().lower().replace(' ', '_')
    parent_section = parent_section.strip().lower().replace(' ', '_')
    sub_section = app_builder_db.fetch_sub_section_by_name(
        payload['name'], parent_section)
    if sub_section['status'] == 200:
        template = {
            "collection_name": parent_section + '_' + sub_section_name,
            "form": []
        }
        payload['template'] = template
        res = app_builder_db.set_sub_section_to_main(payload, parent_section)
        if res['status'] == 200:
            sub_collection = parent_section + '_' + sub_section_name
            col_res = app_builder_db.create_collection(sub_collection)
            result = {
                "status": 200,
                "message": "Sub section created successfully."
            }
    else:
        result = {
            "status": sub_section['status'],
            "message": sub_section['message']
        }
    return result

# create leaf section (3rd level)


def CreateLeafSection(store, payload, grand_parent, parent):
    result = {
        "status": 400,
        "message": "Something went wrong."
    }
    leaf_name = payload['name'].strip().lower().replace(' ', '_')
    grand_parent = grand_parent.strip().lower().replace(' ', '_')
    parent = parent.strip().lower().replace(' ', '_')
    template = {
        "collection_name": grand_parent + '_' + parent + '_' + leaf_name,
        "form": []
    }
    payload['template'] = template
    leaf_res = app_builder_db.set_leaf_to_sub_section(
        payload, parent, grand_parent)

    if leaf_res['status'] == 200:
        leaf_collection = grand_parent + '_' + parent + '_' + leaf_name
        col_res = app_builder_db.create_collection(leaf_collection)
        result = {
            "status": 200,
            "message": "Leaf Section created successfully."
        }
    else:
        result = {
            "status": leaf_res['status'],
            "message": leaf_res['message']
        }
    return result


def UpdateMainApp(payload):
    result = {
        "status": 400,
        "message": "Something went wrong."
    }
    collection_name = payload['name'].strip().lower().replace(' ', '_')
    old_collection_name = ''
    status_change=payload['is_status_change']
    if payload['is_status_change'] == False:
        
        is_duplicate = app_builder_db.fetch_section_by_name(payload['name'])
        if is_duplicate and str(is_duplicate['_id']) != payload['id']:
        # if is_duplicate:
             result = {
            "status": 409,
            "message": "Section with same name already exists."
             }
             return result
        else:
             section = app_builder_db.fetch_section_by_id(payload['id'])
             old_collection_name=section['name'].strip().lower().replace(' ', '_')
             if section:
                section['name'] = payload['name']
                section['description'] = payload['description']
                section['icon_image'] = payload['icon_image']
                template = {
                    "collection_name": collection_name,
                    "form": []
                }
                section['template'] = template
                # logger.debug(section,"section updated")
                res = app_builder_db.update_main_app_to_db(
                    section, payload['id'], status_change)
                # logger.debug(res,"res")
                # logger.debug(collection_name,"collectionname")
                # logger.debug(old_collection_name,"oldcollectionname")

                
                # app_builder_db.update_collection_name(old_collection_name,collection_name)
                # Ensure collection names are different before renaming
                if old_collection_name and old_collection_name != collection_name:
                    app_builder_db.update_collection_name(old_collection_name, collection_name)
                # logger.debug("all done")
                if res:
                    result = {
                        "status": 200,
                        "message": "Section updated successfully."
                    }
                else:
                    result = {
                        "status": 500,
                        "message": "Something went wrong at server!!."
                    }
             else:
                result = {
                       "status": 404,
                       "message": "Section not found."
                      }
                     
    else:
         res = app_builder_db.update_main_app_to_db(
                    payload, payload['id'], status_change)
         if res:
            result = {
                        "status": 200,
                        "message": "Section updated successfully."
                    }             
    return result
    
def UpdateLeafSection(payload, grand_parent , parent):
    result = {
        "status": 400,
        "message": "Something went wrong."
    }
    status_change=payload['is_status_change']
    collection_name = payload['name'].strip().lower().replace(' ', '_')
    old_name=payload['old_name'].strip().lower().replace(' ', '_')
    grandparent_parent_collection=grand_parent+'_'+parent
    prev_collection_name=grand_parent+'_'+parent+'_'+old_name
    
    is_duplicate = app_builder_db.fetch_leaf_by_name(collection_name,grand_parent,parent)
    if payload['is_status_change'] == False:   
       if is_duplicate['status'] == 200:
        leaf = app_builder_db.find_leaf(old_name,grand_parent,parent)
        if leaf:
           leaf['name']= payload['name']
           leaf['description']=payload['description']
           leaf['parent_collection'] = parent
           template = {
            "collection_name": grandparent_parent_collection + '_' + collection_name,
            "form": []
           }
           leaf['template'] = template
           res = app_builder_db.update_leaf_to_db(
                   grandparent_parent_collection, prev_collection_name,leaf, payload['old_name'], status_change,grand_parent,parent)
           
       
           if res:
                    result = {
                        "status": 200,
                        "message": "Section updated successfully."
                    }
           else:
               result = {
                        "status": 500,
                        "message": "Something went wrong at server!!."
                    }
       else: 
        return is_duplicate['status']
    else:
         res = app_builder_db.update_leaf_to_db(
                 grandparent_parent_collection,  prev_collection_name, payload, old_name, status_change,grand_parent,parent)
        
         if res:
            result = {
                        "status": 200,
                        "message": "Section updated successfully."
                    }      
    return result

def UpdateSubSection(payload, parent_section):
    result = {
        "status": 400,
        "message": "Something went wrong."
    }
    
    collection_name = payload['name'].strip().lower().replace(' ', '_')
    old_name=payload['old_name'].strip().lower().replace(' ', '_')
    
    prev_collection_name=parent_section+'_'+old_name
    status_change=payload['is_status_change']
    
    if payload['is_status_change'] == False:
       is_duplicate = app_builder_db.fetch_sub_section_by_name(collection_name,parent_section)
       if is_duplicate['status'] == 200:
   
       
           sub_section = app_builder_db.find_sub_section(old_name,parent_section)
        
           if sub_section:
              sub_section['name']= payload['name']
              sub_section['description']=payload['description']
              sub_section['parent_collection'] = parent_section
              template = {
                       "collection_name": parent_section + '_' + collection_name,
                       "form": []
                }
              sub_section['template'] = template
          
              res = app_builder_db.update_subsection_to_db(
                    prev_collection_name,sub_section, payload['old_name'], status_change,parent_section)
          
       
           if res:
                    result = {
                        "status": 200,
                        "message": "Section updated successfully."
                    }
           else:
               result = {
                        "status": 500,
                        "message": "Something went wrong at server!!."
                    }
       else: 
           return is_duplicate['status']
    else:
         res = app_builder_db.update_subsection_to_db(
                   prev_collection_name, payload, old_name, status_change,parent_section)   
         if res:
            result = {
                        "status": 200,
                        "message": "Section updated successfully."
                    }    
    return result

# delete parent section (1st level)
def DeleteMainApp(query_params):
    id = query_params['id']
    section = app_builder_db.fetch_section_by_id(id)
    if section:
        collection_name = section['name'].strip().lower().replace(' ', '_')
        section_res = app_builder_db.delete_section_by_id(id)
        if section_res['status'] == 200:
            app_builder_db.delete_collection(collection_name)
            return {
                "status": 200,
                "message": "Section deleted successfully."
            }
        else:
            return {
                "status": section_res['status'],
                "message": section_res['message']
            }
    else:
        return {
            "status": 404,
            "message": "Section not found."
        }

# delete child section (2nd level)


def DeleteSubSection(query_params, parent):
    name = query_params['name'].strip().lower().replace(' ', '_')
    parent = parent.strip().lower().replace(' ', '_')
    res = app_builder_db.delete_sub_section(name, parent)
    if res['status'] == 200:
        collection_name = parent + '_' + name
        app_builder_db.delete_collection(collection_name)
        return {
            "status": 200,
            "message": "Sub-section deleted successfully."
        }
    else:
        return {
            "status": res['status'],
            "message": res['message']
        }


# delete leaf section (3rd level)


def DeleteLeafSection(query_params, grand_parent, parent):
    name = query_params['name'].strip().lower().replace(' ', '_')
    grand_parent = grand_parent.strip().lower().replace(' ', '_')
    parent = parent.strip().lower().replace(' ', '_')
    res = app_builder_db.delete_leaf_section(name, grand_parent, parent)
    if res['status'] == 200:
        collection_name = grand_parent + '_' + parent + '_' + name
        app_builder_db.delete_collection(collection_name)
        return {
            "status": 200,
            "message": "Leaf-section deleted successfully."
        }
    else:
        return {
            "status": res['status'],
            "message": res['message']
        }

ALLOWED_EXTENSIONS = set(['svg', 'png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov', 'wmv', 'mkv'])
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def change_file_name(filename):
    rendom = str(round(datetime.utcnow().timestamp()))
    if ('.' in filename):
        ext = filename.rsplit('.', 1)[1].lower()
        fname = rendom + '.' + ext
        return fname

def setAppBuilderImage(body):
        response = {
            "status": 400
        }
        if not os.path.exists('images/appbuilder/images'):
            os.makedirs('images/appbuilder/images')
        file = body['image']
        UPLOAD_FOLDER = os.path.join('images/appbuilder/images')
        if (file.filename == ''):
            response['message'] = "No image selected for uploading"
            response['status'] = 500
        if (file and allowed_file(file.filename)):
            newName = change_file_name(file.filename)
            fname = secure_filename(newName)
            file.save(os.path.join(UPLOAD_FOLDER, fname))
            base_path = os.path.join(os.path.abspath(
                os.getcwd()), UPLOAD_FOLDER, newName)
            response['message'] = base_path
            response['status'] = 200
        else:
            response['message'] = "Allowed image types are -> svg, png, jpg, jpeg, gif, mp4, avi, mov, wmv, mkv"
            response['status'] = 500

        return response

def getAppBuilderImage(body):
    url = './images/appbuilder/images/' + body['image']
    if not os.path.exists(url):
        return make_response({'error': 'Image not found'}, 404)
    return send_file(url, as_attachment=True)