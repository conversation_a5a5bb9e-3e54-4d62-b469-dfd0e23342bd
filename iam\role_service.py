import datetime
from new_mongodb import process_documents, process_data, fetchall_documents_from_admin_collection, fetch_one_document_from_admin_collection, count_documents_admin_collection
from new_mongodb import store_admin_db, StoreAdminDBCollections
from exceptions.common_exceptions import ResourceAlreadyExistException, InvalidInputException, ResourceNotFoundException
from iam import feature_service
from bson import ObjectId
from utils import redis_util

def _process_feature_permission(feature_name, store_feature, permission):
    role_permission = {
                    "api": store_feature['api'],
                    "name": store_feature['name'],
                    "operations": {},
                    "children": {}
                }
    allowed_operations = permission.get("operations", {})
    for operation in store_feature['operations']:
        role_permission["operations"][operation] = allowed_operations.get(operation, False)

    store_feature_children = store_feature.get("children", {})
    children = permission.get("children", {})
    for child_feature, child_permission in children.items():
        child_store_feature = store_feature_children.get(child_feature, None)
        if child_store_feature:
            role_permission["children"][child_feature] = _process_feature_permission(child_feature, child_store_feature, child_permission)

    return role_permission

def create_role(store, data):
    role_id = None
    role_name = data.get("role", None)
    permissions = data.get("permissions", None)
    is_super_admin = data.get("is_super_admin", False)
    is_administrator_access = data.get("is_administrator_access", False)
    if permissions and len(permissions) > 0 and role_name and role_name.strip() != "":
        role_name = role_name.strip()
        store_id = store['id']
        existing_role = store_admin_db.fetch_role_by_name(store_id=store_id, role_name=role_name)
        if existing_role:
            raise ResourceAlreadyExistException(f"role: Role {role_name} already exists.")
        new_role = {
            "role": role_name,
            "permissions": {},
            "status": "active",
            "is_administrator_access": is_administrator_access,
            "is_super_admin": is_super_admin,
            "store_id": store_id,
            "total_users": 0,
            "created_at": int(datetime.datetime.now(datetime.timezone.utc).timestamp())
        }
        store_features = feature_service.get_store_features(store_id=store_id)
        if store_features:
            for feature, permission in permissions.items():
                store_feature = store_features.get(feature, None)
                if store_feature:
                    new_role["permissions"][feature] = _process_feature_permission(feature, store_feature, permission)
            
            result = store_admin_db.create_role(store_id=store_id, role=new_role)
            role_id = result.inserted_id
    else:
        raise InvalidInputException(f"Provide valid permissions and role name.")

    return role_id

def update_role(store, role_id, data):
    role_name = data.get("role", None)
    permissions = data.get("permissions", None)
    if permissions and len(permissions) > 0 and role_name and role_name.strip() != "" and role_id and role_id.strip() != "":
        role_name = role_name.strip()
        store_id = store['id']
        existing_role = store_admin_db.fetch_role_by_id(store_id=store_id, role_id=role_id, projection={})
        if not existing_role:
            raise ResourceNotFoundException("Role doesn't exist.")
        
        if existing_role['role'] != role_name:
            role_with_same_name = store_admin_db.fetch_role_by_name(store_id=store_id, role_name=role_name, projection={"role": 1})
            if role_with_same_name:
                raise ResourceAlreadyExistException(f"role: Role {role_name} already exists.")
        updated_permissions = {}
        store_features = feature_service.get_store_features(store_id=store_id)
        if store_features:
            for feature, permission in permissions.items():
                store_feature = store_features.get(feature, None)
                if store_feature:
                    updated_permissions[feature] = _process_feature_permission(feature, store_feature, permission)
            
            _update = {
                'permissions': updated_permissions,
                'role': role_name
            }
            if "is_super_admin" in data:
                _update["is_super_admin"] = data["is_super_admin"]
            if "is_administrator_access" in data:
                _update["is_administrator_access"] = data["is_administrator_access"]
            store_admin_db.update_role(store_id=store_id, role_id=role_id, role=_update)
            user_cursor = store_admin_db.fetch_users(store_id, {"role_id": role_id}, {"username": 1})
            usernames = [user["username"] for user in user_cursor]
            redis_util.delete_access_tokens_for_users(usernames)
    else:
        raise InvalidInputException(f"Provide valid role id, role name and permissions.")
        
    return True

def delete_by_id(store, role_id):
    response = {
        'status': 400
    }
    store_id = store['id']
    count_query = {
        "role_id": str(role_id)
    }
    user_count = store_admin_db.count_store_users(store_id=store_id, query=count_query)
    if user_count and user_count > 0:
        raise ResourceAlreadyExistException("Role is assigned to users. Cannot delete role.")
    
    result = store_admin_db.delete_role(store_id, role_id) 

    if result.deleted_count > 0:
        response['status'] = 200
        response['message'] = "Role deleted successfully"
    else:
        response['message'] = "Role not found"
        response['status'] = 404
    
    return response

def get_roles(store):
    query = {
        "store_id": store['id'],
        "status": "active"
    }

    projection = {
        "id": 1,
        "role": 1,
        "total_users": 1,
        "created_at": 1
    }

    rs = fetchall_documents_from_admin_collection(store['id'], StoreAdminDBCollections.ROLES_COLLECTION, query, projection)
    roles = process_documents(rs)
    if roles:
        for role in roles:
            query = {'role_id': str(role['id']), 'status': { '$ne': 'deleted' } }
            count = count_documents_admin_collection(store['id'], StoreAdminDBCollections.USERS_COLLECTION, query)
            role['total_users'] = count
    return roles

def get_one(store_id, role_id):
  query = {"_id": ObjectId(role_id)}
  res = fetch_one_document_from_admin_collection(store_id, StoreAdminDBCollections.ROLES_COLLECTION, query)
  return process_data(res)