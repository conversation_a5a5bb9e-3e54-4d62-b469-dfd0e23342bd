from services import Service
from bson import ObjectId
import datetime

class SubNavigations(Service):
    def create_sub_nav(self, id, name, short_code):
        res = {}
        res["created_at"] = int(datetime.datetime.utcnow().timestamp())
        res["updated_at"] = ''
        res['navigation_id'] = id
        res['navigations'] = []
        res['name'] = name
        res['short_code'] = short_code

        if len(res['navigations']):
            for idx, x in enumerate(res['navigations']):
                x['sort_order'] = idx + 1

        id = super().create(res)
        return id
    
    def get_sub_nav(self, nav_id):
        res = super().find_one({
            "navigation_id": ObjectId(str(nav_id)),
            "status": "active"
        })
        return res
    
    def update_sub_nav(self, body, nav_id): 
        def add_unique_id(data, parent_id=''):
            for index, item in enumerate(data):
                child_id = parent_id + str(index+1)
                if item['type']=='Brands':
                    item['id']=item['id']
                elif item['type']=='Categories':
                    item['id']=item['id']
                elif item['type']=='Web Pages':
                    item['id']=item['id']
                else:
                    item['id'] = child_id
                if item['children']:
                    add_unique_id(item['children'], child_id)
        
        add_unique_id(body)
        
        for idx, x in enumerate(body):
            x['sort_order'] = idx + 1
            for idcx, cx in enumerate(x['children']):   
                temp = str(x['sort_order']) + '_'             
                cx['sort_order'] = temp + str(idcx + 1)
                for idscx, scx in enumerate(cx['children']):   
                    temp2 = str(cx['sort_order']) + '_'             
                    scx['sort_order'] = temp2 + str(idscx + 1)    
        
        id = super().update_one({"navigation_id": ObjectId(str(nav_id))}, { 
            "$set": {
                'navigations': body,
                "updated_at": int(datetime.datetime.utcnow().timestamp())
            }
        })
        return id
    
    def delete_by_nav_id(self, nav_id):
        return super().delete({"navigation_id": ObjectId(str(nav_id))})
    
    def update_name(self, nav_id, name):
        id = super().update_one({"navigation_id": ObjectId(str(nav_id))}, { 
            "$set": {
                'name': name,
                "updated_at": int(datetime.datetime.utcnow().timestamp())
            }
        })
        return id
    
    def get_navigation_by_short_code(self, short_code):
        res = super().find_one({
            "short_code": str(short_code),
            "status": "active"
        })
        return res