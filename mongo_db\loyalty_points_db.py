import logging
from pymongo.collation import Collation
from bson import ObjectId
import mongo_db
from utils.common import parse_json
import datetime

logger = logging.getLogger()

PRODUCT_COLLECTION = "products"
REWARDS_COLLECTION = "loyalty_rewards"

def get_rewards(store):
    db = mongo_db.get_store_db_client(store)
    customers = db[REWARDS_COLLECTION].find()
    return customers

def create(store, payload):
    db = mongo_db.get_store_db_client(store)
    result=db[REWARDS_COLLECTION].insert_one(payload)
    return result

def update(query, payload):
    db = mongo_db.get_admin_db_client()
    result=db[REWARDS_COLLECTION].update_one(query, payload)
    return result