import task
from new_mongodb import get_admin_db_client_for_store_id, StoreAdminDBCollections


def upload_price_list_csv(store, payload, username):
    response = {"status": 409}
    db = get_admin_db_client_for_store_id(store['id'])
    
    file_data = payload.get("data", [])
    filename = payload.get("filename", "uploaded_price_list.csv")

    filename = "uploaded_price_list.csv" if filename == "" else filename
    
    if not file_data:
        response["message"] = "No data provided."
        return response
    
    # Extract SKUs from the payload
    payload_skus = {row["SKU"] for row in file_data}

    # Query MongoDB to check if these SKUs exist as parent_product_sku or variant_sku
    found_skus = set()
    query = {
        "$or": [
            {"parent_product_sku": {"$in": list(payload_skus)}},
            {"variants.variant_sku": {"$in": list(payload_skus)}}
        ]
    }
    
    results = db[StoreAdminDBCollections.PRODUCT_PRICE_LISTS].find(query, {"parent_product_sku": 1, "variants.variant_sku": 1})
    
    # Collect found SKUs
    for doc in results:
        found_skus.add(doc.get("parent_product_sku", ""))
        found_skus.update(variant["variant_sku"] for variant in doc.get("variants", []))

    # Check for missing SKUs
    missing_skus = payload_skus - found_skus
    if missing_skus:
        response["message"] = [
            f"SKU does not exist in the system. Please enter a valid SKU: {sku}"
            for sku in missing_skus
        ]
        return response


    # If all SKUs are valid, send the task
    task_id = task.send_task(task.UPDATE_PRICE_LIST_FROM_CSV, args=(store['id'], file_data, username, filename))
    if task_id:
        response["status"] = 200
        response["message"] = f"CSV validated successfully. Price update process has started. You will receive an email once it's completed."
        db[StoreAdminDBCollections.USER_PREFERENCE].update_one({"type": "price_list_import"}, {"$set": {"status": "running"}}, upsert=True)
    else:
        response["message"] = "Something went wrong while updating the product prices."

    return response

def get_csv_upload_status(store_id):
    response = {
        "status": 400
    }
    db = get_admin_db_client_for_store_id(store_id)
    result = db[StoreAdminDBCollections.USER_PREFERENCE].find_one({"type": "price_list_import"})
    if result:
        response['status'] = 200
        response['data'] = result.get("status", None)
    else:
        response['status'] = 200
        response['data'] = {}
    return response

def export_price_list_csv(store, query_params, username):
    response = {
        "status": 400
    }
    task_id = task.send_task(task.PRICE_LIST_CSV_EXPORT_TASK, args=(store['id'], query_params, username))
    if task_id:
        response["status"] = 200
        response["message"] = f"We are generating the data you requested for download as a CSV file. This process may take a few minutes. Once complete, the CSV will be sent to your email: {username}."
    else:
        response["message"] = "Something went wrong while updating the product prices."

    return response