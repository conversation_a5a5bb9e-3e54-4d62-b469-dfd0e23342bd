from services import Service
from utils.common import calculatePaginationData
from mongo_db import task_db

class Tasks(Service):
   def get_all(self, payload, store):
      payload['filterBy'] = ['name']
      task_list_fields = {
        'name': 1,
        'description': 1,
        'interval_seconds': 1,
        'is_periodic_task': 1,
        'is_active': 1,
        'store_run_status': {
           store['id']: {
              'status': 1,
              'last_run_end_time': 1,
              'last_run_outcome': 1
           }  
        }
      }

      tasks, total_data_length, page, limit = super().get_paginated_records_updated(payload, task_list_fields, {})
      
      data = calculatePaginationData(tasks, page, limit, total_data_length)
      return data
    
   
   def get_job(self, job_id, store):
      response = {
         'status': 400
      }
      job = task_db.fetch_task_by_id(job_id, store['id'])
      job_data = {}
      if job:
         job_status = {}
         store_run_status = job.get('store_run_status', {})
         if store_run_status:
            job_status = store_run_status.get(store['id'], {})
         job_data = {
            'name': job.get('name'),   
            'description': job.get('description'),
            'interval_seconds': job.get('interval_seconds'),
            'is_periodic_task': job.get('is_periodic_task'),
            'is_active': job.get('is_active'),
            'status': job_status.get('status'),
            'last_run_end_time': job_status.get('last_run_end_time'),
            'last_run_outcome': job_status.get('last_run_outcome'),
         }
      
      response['data'] = job_data
      response['status'] = 200

      return response