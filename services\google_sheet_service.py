from builtins import list
from services import Service
from datetime import datetime
from bson import ObjectId
from datetime import datetime
from oauth2client.service_account import ServiceAccountCredentials
import gspread
from utils.common import calculatePaginationData
import csv
from io import StringIO
import mongo_db


scope = ['https://spreadsheets.google.com/feeds',
         'https://www.googleapis.com/auth/drive']
# 'sheetId': '1p9DdN5690JV7XDnpfqD9rNvELcxW7vcZcULfo3D5pTc',
# "private_key_id": "64aba2c22718c7bf50a72633d78f21712ecd7b7c",


_SPREADSHEET_DB_NAME = "spreadsheet"

def getSheetCreds(sheetID):
    googleSheet = {
        'sheetId': sheetID,
        'token': ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    }
    return googleSheet


class MappingTables(Service):
    def create_table_data_collection(self, id, name):
        db = mongo_db.get_db_client(_SPREADSHEET_DB_NAME)
        table_name = name.replace(' ', '_')
        table_name = table_name + '_table_' + str(id)
        db.create_collection(table_name.lower())
        return table_name

    def create_mapping_table(self, body):
        mapping_tables = super().find_all()
        tables = super().processList(mapping_tables)
        response = {
            "status": 400
        }

        for table in tables:
            if (table['table_name'] == body['table_name']):
                response['message'] = "table_name: Table name already exists. please change table name."
                response['status'] = 400
                return response

        body["created_at"] = int(datetime.utcnow().timestamp())
        body["updated_at"] = ""

        id = super().create(body)

        response['message'] = "Table created successfully."
        response['id'] = id
        response['table_name'] = body['table_name']
        response['status'] = 200

        return response

    def create_mapping_table_csv(self, body):
        temp_body = {}
        response = {"status": 400}

        table_name = body.form.get('table_name')
        description = body.form.get('description')
        file = body.files['file']

        mapping_tables = super().find_all()
        tables = super().processList(mapping_tables)

        if 'file' not in body.files:
            response['message'] = "File not found"
            response['status'] = 400
            return response

        if not file.filename.endswith('.csv'):
            response['message'] = "Invalid file type"
            response['status'] = 400
            return response

        for table in tables:
            if (table['table_name'] == table_name):
                response['message'] = "Table name already exists. please change table name."
                response['status'] = 400
                return response

        # read the file contents as a string
        file_contents = file.read().decode('utf-8')

        # create a file-like object from the string
        file_obj = StringIO(file_contents)

        # parse the CSV data using csv.reader
        csv_reader = csv.reader(file_obj)

        # extract the header row
        header = csv_reader.__next__()

        # process the data rows
        data = []
        for row in csv_reader:
            # check if all cells in the row are empty
            if all(cell == '' for cell in row):
                continue  # skip the row if all cells are empty
            data.append(row)

        temp_header = {}
        for item in header:
            temp_header[item.replace('\ufeff', '')] = 'string'

        temp_body['table_name'] = table_name
        temp_body['description'] = description
        temp_body['header'] = temp_header
        temp_body["created_at"] = int(datetime.utcnow().timestamp())
        temp_body["updated_at"] = ""

        id = super().create(temp_body)

        response['message'] = "Table created successfully."
        response['id'] = id
        response['table_name'] = table_name
        response['data'] = data
        response['header'] = header
        response['status'] = 200

        return response

    def create_table_data_collection_csv(self, body):
        db = mongo_db.get_db_client(_SPREADSHEET_DB_NAME)
        table_name = body['table_name'].replace(' ', '_')
        table_name = table_name + '_table_' + str(body['id'])
        db.create_collection(table_name.lower())
        collection = db[table_name.lower()]

        dataArray = body['data']
        header = body['header']

        for data in dataArray:
            temp = {}
            for i, val in enumerate(data):
                temp[header[i].replace('\ufeff', '')] = val
            collection.insert_one(temp)

        return 'ok'

    def update_mapping_table_csv(self, body):
        id = body['id']
        id = super().update_one({"_id": ObjectId(str(id))}, {"$set":
                                                             {
                                                                 "header": body['header'],
                                                                 "updated_at":  int(datetime.utcnow().timestamp())
                                                             }
                                                             })
        return id

    def update_mapping_table(self, body, mapping_table_id=None):
        response = {"status": 400}
        # Check if the table with the new name already exists (excluding the current record)
        existing_table = self.find_one({
            "table_name": body['table_name'],
            "_id": {"$ne": ObjectId(str(mapping_table_id))}
        })
        
        if existing_table:
            # Return error response if a table with the new name already exists
            response['message'] = 'table_name: Table with the same name already exists!'
            response['status'] = 409  # Conflict HTTP status code
            return response
        
        table_data = self.get_mapping_table(mapping_table_id)
        # Check if the old and new collection names are the same
        old_collection_name = table_data['table_name'].lower()
        new_collection_name = body['table_name'].lower()
        
        if old_collection_name == new_collection_name:
            response['message'] = 'table_name: Old and new table names cannot be the same!'
            response['status'] = 409  # Conflict HTTP status code
            return response

        id = super().update_one({"_id": ObjectId(str(mapping_table_id))}, {"$set":
                                                                           {
                                                                               "table_name": body['table_name'],
                                                                               "description": body['description'],
                                                                               "updated_at":  int(datetime.utcnow().timestamp())
                                                                           }
                                                                           })

        if id:
            response['message'] = 'Data Updated Successfully'
            response['id'] = id
            response['table_name'] = table_data['table_name']
            response['updated_name'] = body['table_name']
            response['status'] = 200
        else:
            response['message'] = 'Something went wrong at server!!'
            response['status'] = 500

        return response

    def update_mapping_table_collection_name(self, body, mapping_table_id=None):
        db = mongo_db.get_db_client(_SPREADSHEET_DB_NAME)
        old_table_name = body['table_name'].replace(' ', '_')
        old_collection_name = old_table_name + \
            '_table_' + str(mapping_table_id)

        new_table_name = body['updated_name'].replace(' ', '_')
        new_collection_name = new_table_name + \
            '_table_' + str(mapping_table_id)

        db[old_collection_name.lower()].rename(new_collection_name.lower())

        return 'Table updated successfully'

    def get_all_mapping_tables(self):
        mapping_tables = super().find_all()
        res = super().processList(mapping_tables)
        return res

    def delete_by_id(self, mapping_table_id, body):
        db = mongo_db.get_db_client(_SPREADSHEET_DB_NAME)
        if body['table_collection'] in db.list_collection_names():
            db.drop_collection(body['table_collection'])

        return super().delete({"_id": ObjectId(str(mapping_table_id))})

    def get_mapping_table(self, mapping_table_id):
        res = super().find_one({
            "_id": ObjectId(str(mapping_table_id)),
            "status": "active"
        })
        # Get the keys of the header object
        header_keys = list(res['header'].keys())
        res['firstIndex'] = header_keys[0]  # Add a new key-value pair to res
        return res


class MappingTablesData(Service):
    def insert_row_to_table(self, body):
        db = mongo_db.get_db_client(_SPREADSHEET_DB_NAME)

        data = {}
        collection = db[body['table_collection']]

        for key, val in body['data'].items():
            data[key] = val

        result = collection.insert_one(data)
        id = str(result.inserted_id)
        return id

    def update_row_data(self, body):
        db = mongo_db.get_db_client(_SPREADSHEET_DB_NAME)

        collection = db[body['table_collection']]
        key_to_remove = 'id'
        if key_to_remove in body['data']:
            del body['data'][key_to_remove]
        update_query = {'$set': body['data']}
        updated_document = collection.find_one_and_update(
            {'_id': ObjectId(body['row_id'])},
            update_query,
            return_document=True
        )

        if updated_document:
            return {'status': 200, 'message': 'Data updated'}
        else:
            return {'status': 500, 'message': 'Document not found'}

    def delete_row_by_id(self, body):
        db = mongo_db.get_db_client(_SPREADSHEET_DB_NAME)
        collection = db[body['table_collection']]

        row_ids = body['row_id']
        deleted_count = 0

        for id in row_ids:
            result = collection.delete_one({"_id": ObjectId(str(id))})
            if result.deleted_count > 0:
                deleted_count += 1

        result = {'status': 200,
                  'message': f'{deleted_count} Item(s) deleted successfully.'}

        return result

    def get_mapping_table_data(self, body, mapping_table_id):
        db = mongo_db.get_db_client(_SPREADSHEET_DB_NAME)
        header = {}
        table_metadata = super().find_one({
            "_id": ObjectId(str(mapping_table_id)),
            "status": "active"
        })
        for key in table_metadata['header']:
            header[key] = 1

        if body['table_collection'] in db.list_collection_names():
            collection = db[body['table_collection']]
            documents = list(collection.find())
            for doc in documents:
                doc['_id'] = str(doc['_id'])

            table_data, total_data_length, page, limit = super(
            ).get_paginated_records(body, header, db, 1)

            # include pagination data in response ...
            data = calculatePaginationData(
                table_data, page, limit, total_data_length)
            return {'status': 200, 'message': 'Data retrived successfully', 'data': data}
        else:
            return {'status': 500, 'message': 'Collection not found'}
        
    def get_mapping_table_data_for_csv(self, body, mapping_table_id):
        db = mongo_db.get_db_client(_SPREADSHEET_DB_NAME)
        header = {}
        table_metadata = super().find_one({
            "_id": ObjectId(str(mapping_table_id)),
            "status": "active"
        })
        for key in table_metadata['header']:
            header[key] = 1

        if body['table_collection'] in db.list_collection_names():
            collection = db[body['table_collection']]
            documents = list(collection.find())
            for doc in documents:
                del doc['_id'] 
            
            return {'status': 200, 'message': 'Data retrived successfully', 'data': documents}
        else:
            return {'status': 500, 'message': 'Collection not found'}
class Data():
    def get_gsclient(self, sheetID):
        googleSheet = getSheetCreds(sheetID)
        creds = ServiceAccountCredentials.from_json_keyfile_dict(
            googleSheet['token'], scope)
        return gspread.authorize(creds)

    def get_sheet_data(self, body):
        sheetID = body['sheet_id']
        tabName = body['tab_name']
        googleSheet = getSheetCreds(sheetID)
        client = self.get_gsclient(sheetID)
        sheet = client.open_by_key(googleSheet['sheetId'])
        gs_sheet = sheet.worksheet(tabName)
        result = []

        if gs_sheet:
            data = gs_sheet.get_all_records()
            for row in data:
                result.append(row)

        return result

    def update_sheet_data(self, body):
        sheetID = body['sheet_id']
        tabName = body['tab_name']
        googleSheet = getSheetCreds(sheetID)
        client = self.get_gsclient(sheetID)
        sheet = client.open_by_key(googleSheet['sheetId'])
        gs_sheet = sheet.worksheet(tabName)
        cell_row = body['cell_row']
        cell_column = body['cell_column']
        gs_sheet.update_cell(cell_row, cell_column, body['value'])

        return True

    def get_sheet_data_header(self, body):
        sheetID = body['sheet_id']
        tabName = body['tab_name']
        googleSheet = getSheetCreds(sheetID)
        client = self.get_gsclient(sheetID)
        sheet = client.open_by_key(googleSheet['sheetId'])
        gs_sheet = sheet.worksheet(tabName)
        header_row = gs_sheet.row_values(1)
        return header_row
