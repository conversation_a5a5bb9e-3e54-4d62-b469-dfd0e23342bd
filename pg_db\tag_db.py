from sqlalchemy.dialects.postgresql import insert
import pg_db as db
import string
from sqlalchemy import Column, DateTime, String, Integer, Float, text, delete, update
from sqlalchemy.exc import SQLAlchemyError

class Tags(db.Base):
    __tablename__ = db.tags_table

    id = Column(String, primary_key=True)
    tag = Column(String)   

    @classmethod
    def get_tag_id(cls, tag):
        tag = tag.translate({ord(c): None for c in string.whitespace})
        return tag.lower() 

    @classmethod
    def get_all_tags(cls, session=None):
        local_session = None
        if not session:
            session = db.get_session()
            local_session = session
        try:
            tags = session.query(Tags).all()
            return tags
        finally:
            if local_session:
                local_session.close()
    
    @classmethod
    def get_tag(cls, tag_id, session=None):
        local_session = None
        if not session:
            session = db.get_session()
            local_session = session
        try:
            tag = session.query(Tags).get(tag_id)
            return tag
        finally:
            if local_session:
                local_session.close()

    @classmethod
    def add_tags(cls, tags, session=None):
        stmt = insert(Tags).on_conflict_do_nothing()
        values = []
        for tag in tags:
            tag_id = cls.get_tag_id(tag)
            values.append({"id": tag_id, "tag": tag})
        db.execute_stmt(stmt, values, session)
        return values
    
    @classmethod
    def add_tag(cls, tag, session=None):
        stmt = insert(Tags).on_conflict_do_nothing()
        values = []
        tag_id = cls.get_tag_id(tag)
        values.append({"id": tag_id, "tag": tag})
        db.execute_stmt(stmt, values, session)
        return values[0]
    
    @classmethod
    def delete_tags(cls, tagId, session=None):
        delete_stmt = delete(Tags).where(Tags.id.in_(tagId))
        db.execute_stmt(delete_stmt, session=session)
        delete_tags_in_product = delete(ProductTags).where(ProductTags.tag_id.in_(tagId))           
        db.execute_stmt(delete_tags_in_product, session=session)

    @classmethod
    def update_tags(cls, old_tag_ids, new_tags, session=None):
        delete_stmt = delete(Tags).where(Tags.id.in_(old_tag_ids))
        db.execute_stmt(delete_stmt, session=session)
        stmt = insert(Tags).on_conflict_do_nothing()
        values = []
        for tag in new_tags:
            tag_id = cls.get_tag_id(tag)
            values.append({"id": tag_id, "tag": tag})
            update_stmt = update(ProductTags).where(ProductTags.tag_id.in_(old_tag_ids)).values(tag_id=tag_id)
            db.execute_stmt(update_stmt, session=session)
        db.execute_stmt(stmt, values, session) 
        return values      
        

class ProductTags(db.Base):
    __tablename__ = db.product_tags_table

    tag_id = Column(String, primary_key=True)
    sku = Column(String, primary_key=True)
    
    @classmethod
    def get_tags_by_product_sku(cls, product_sku, session=None):
        local_session = None
        if not session:
            session = db.get_session()
            local_session = session
        try:
            tags = session.query(ProductTags, Tags).filter(ProductTags.tag_id==Tags.id).filter(ProductTags.sku == product_sku).all()
            return tags
        finally:
            if local_session:
                local_session.close()
    
    @classmethod
    def add_product_tags(cls, product_tags, session=None):
        stmt = insert(ProductTags).on_conflict_do_nothing()
        db.execute_stmt(stmt, product_tags, session)

    @classmethod
    def delete_product_tags(cls, product_skus, session=None):
        delete_stmt = delete(ProductTags).where(ProductTags.sku.in_(product_skus))
        db.execute_stmt(delete_stmt, session=session)

    @classmethod
    def get_all_product_tags(cls, session):
        products = session.query(ProductTags, Tags).filter(ProductTags.tag_id==Tags.id).all()   
        return products        