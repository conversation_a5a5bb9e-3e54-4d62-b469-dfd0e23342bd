import time
from graphql import category_tree_query
from utils import bc, store_util
from mongo_db import catalog_db
import mongo_db
import plugin
from new_utils import cache_util

def process_category(category):
    category['id'] = category['entityId']
    del category['entityId']
    if "children" in category:
        children = category['children']
        for index, childCategory in enumerate(children):
            children[index] = process_category(childCategory)
    return category

def process_category_tree(root):
    category_tree = root['data']['site']['categoryTree']
    categories = []
    for root_category in category_tree:
        root_category = process_category(root_category)
        root_category['_id'] = root_category['id']
        categories.append(root_category)
    
    return categories

def fetch_all_categories_graphql(store):
    bc_api = store_util.get_bc_api_creds(store)
    query = category_tree_query.get_category_tree_query()
    retry_count = 0
    max_retry_count = 5
    sleep_time = 10
    status = None 
    res = None 
    while retry_count < max_retry_count:
        token = cache_util.get_graphql_token(store['id'])
        status, res = bc.process_bc_graphql_request(bc_api["store_url"], token["token"], query)
        if status == 200 and res:
            categories = process_category_tree(res)
            mongo_db.upsert_documents(store, catalog_db.CATEGORY_TREE_COLLECTION, categories)
            # break the while loop
            retry_count = max_retry_count + 1
        elif (status == [401, 403]):
            retry_count = retry_count + 1
        elif status == 429 and retry_count < max_retry_count:
            retry_count = retry_count + 1
            sleep_time = sleep_time * retry_count
            time.sleep(sleep_time)
        else:
            # break the while loop
            retry_count = max_retry_count + 1

    if status == 200:
        return True, "Completed successfully"
    else:
        return False, {
            "status": status,
            "message": res
        }

def fetch_categories_rest_api(store):
    query_params = {
        "is_visible": "true",
        "include_fields": "name,parent_id,description,views,sort_order,page_title,meta_keywords,meta_description,image_url,url,custom_url,default_product_sort",
        "limit": 0,
        "page": 250
    }
    api = "v3/catalog/categories"
    return plugin.fetch_all_by_rest_api(store, api, limit_per_req=250, query_params=query_params, 
            db_collection=catalog_db.CATEGORY_COLLECTION, db_process_threshold=250)


def fetch_all_categories(store):
    return fetch_categories_rest_api(store)