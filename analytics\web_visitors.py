import new_pgdb
import datetime
from sqlalchemy import text
import new_utils
import logging
import traceback
from utils.common import convert_to_timestamp
logger = logging.getLogger()

def get_web_visitors(store_id, page, limit, search, sort_array):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        count_query = "select count(*) from web_visitors"

        query = "SELECT id, customer_id, customer_name, email, channel, ip_address, company_name, location, created_at, ip_company_name FROM web_visitors"
        if search:
            query += f" WHERE customer_name ILIKE '%{search}%' OR email ILIKE '%{search}%'"
            count_query += f" WHERE customer_name ILIKE '%{search}%' OR email ILIKE '%{search}%'"
        
        if len(sort_array) > 0:
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            query += f" ORDER BY {sort_array[0]} {sort_direction}"

        if page and limit:
            page = int(page)
            limit = int(limit)
            offset = (page - 1) * limit
            query += " LIMIT " + str(limit) + " OFFSET " + str(offset)

        total_count = conn.execute(text(count_query)).fetchone()[0]
        res = conn.execute(text(query)).fetchall()
        visitors = []
        for row in res:
            row_data = {
                'id': row[0],
                'customer_id': row[1],
                'customer_name': row[2],
                'email': row[3],
                'channel': row[4],
                'ip_address': row[5],
                'company_name': row[6],
                'location': row[7],
                'created_at': convert_to_timestamp(row[8]),
                'ip_company_name': row[9]
            }
            visitors.append(row_data)

        if visitors:
            data = new_utils.calculate_pagination(visitors, page, limit, total_count)
            response['data'] = data
            response['status'] = 200
        else:
            response['data'] = visitors
            response['status'] = 200
    except Exception as e:
        logger.error(traceback.format_exc())
        response['status'] = 422
        response['message'] = str(e)
        raise e
    finally:
        if conn:
            conn.close()
    return response