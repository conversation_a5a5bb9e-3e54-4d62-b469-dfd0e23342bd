

def process_cdn_image_url(cdn_baseurl, active_version):
    if 'components' in active_version:           
        for component in active_version['components']:
            if 'variant' in component:
                variant = component['variant']
                if 'config' in variant:
                    config = variant['config']
                    if 'image_url' in config:
                        if config['image_url'] != '':
                            config['image_url'] = cdn_baseurl + '/brands' + config['image_url'].replace('/brands/images', '') 
                    if 'mobile_image_url' in config:
                        if config['mobile_image_url'] != '':
                            config['mobile_image_url'] = cdn_baseurl + '/brands' + config['mobile_image_url'].replace('/brands/images', '')                           
                    elif 'slider' in  config and 'side_images' in config:                            
                        sliders = config['slider']
                        side_images = config['side_images']
                        for slider in sliders:
                            if 'image_url' in slider:
                                if slider['image_url'] != '':
                                    slider['image_url'] = cdn_baseurl + '/brands' + slider['image_url'].replace('/brands/images', '')

                        for side_image in side_images:
                            if 'image_url' in side_image:
                                if side_image['image_url'] != '':
                                    side_image['image_url'] = cdn_baseurl + '/brands' + side_image['image_url'].replace('/brands/images', '')
                    elif 'banners' in  config:
                        banners = config['banners']
                        for banner in banners:
                            if 'image_url' in banner:
                                if banner['image_url'] != '':
                                    banner['image_url'] = cdn_baseurl + '/brands' + banner['image_url'].replace('/brands/images', '')   
                            if 'mobile_image_url' in banner:
                                if banner['mobile_image_url'] != '':
                                    banner['mobile_image_url'] = cdn_baseurl + '/brands' + banner['mobile_image_url'].replace('/brands/images', '')                          
                    elif 'logos' in config:
                        logos = config['logos'] 
                        for logo in logos:
                            if 'image_url' in logo:
                                if logo['image_url'] != '':
                                    logo['image_url'] = cdn_baseurl + '/brands' + logo['image_url'].replace('/brands/images', '')

    return active_version