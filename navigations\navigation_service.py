from bson import ObjectId
from new_mongodb import fetchall_documents_from_storefront_collection \
    ,insert_document_in_storefront_collection \
    ,update_document_in_storefront_collection \
    ,delete_documents_from_storefront_collection
from new_mongodb.storefront_db import NAVIGATIONS
from utils.common import processList
from datetime import datetime
from utils.common import parse_json
from mongo_db import cms_brands_db
from fields.navigation_fields import brands_fields, categories_fields, webpages_fields
from mongo_db import cms_db
from utils import bc 

def get_all_navigations(store_id):
    
    navigations = fetchall_documents_from_storefront_collection(store_id, NAVIGATIONS)
    res = processList(navigations)

    return res

def create_navigation(store, nav_data):
    nav_data["created_at"] = int(datetime.utcnow().timestamp())
    nav_data["updated_at"] = ""
    nav_data['short_code'] = nav_data['name'][:2] + "-" + \
                    str(round(datetime.utcnow().timestamp()))
    nav_data['status'] = "active"
    
    id = insert_document_in_storefront_collection(store, NAVIGATIONS, nav_data)
    
    return id, nav_data['name'], nav_data['short_code']

def update_navigation(store_id, body, navigation_id=None):
    query = {"_id": ObjectId(str(navigation_id))}
    nav =  {
                "$set": {"name": body['name'],
                "description": body['description'],
                "status": body['status'],
                "updated_at":  int(datetime.utcnow().timestamp())
            }
        }
    id = update_document_in_storefront_collection(store_id, NAVIGATIONS, query, nav)
    
    return id

def delete_navigation_by_id(store_id, nav_id):
    query = {"_id": ObjectId(str(nav_id))}
    res = delete_documents_from_storefront_collection(store_id, NAVIGATIONS, query)
    return res.deleted_count

def get_brands(store, payload):
    data = cms_brands_db.get_cms_brands(store, payload, brands_fields)
    return parse_json(data)

def get_categories(payload, store):
    data = cms_db.get_categories(payload, categories_fields, store)
    return parse_json(data)

def get_web_pages(payload, store):
    data = cms_db.get_webpages(payload, webpages_fields, store) 
    return parse_json(data)

def sync_all_brands(store):
        store_id = store["store_id"]
        brands = fetchall_documents_from_storefront_collection()
        bc_brands = bc.get_bc_brands(store)
        
        bc_brand_ids = {brand['id'] for brand in bc_brands}
        for brand in brands:
          if brand['id'] in bc_brand_ids:
            if True:
              new_list={} 
              seo_details={}
              seo_details["page_name"]=brand["name"]
              
              seo_details["page_url"]=brand["custom_url"]["url"] if brand['custom_url'] and 'url' in brand['custom_url'] else ''
              seo_details["meta_title"]=brand["page_title"]
              seo_details["meta_description"]=brand["meta_description"]

              user = {}
              user["user_id"]= "" 
              user["user_name"]= ""              

              
              new_list["id"]=brand["id"]
              new_list["name"]=brand["name"]
              new_list["type"]="brand"            
              new_list["url"]=brand["custom_url"]["url"] if brand['custom_url'] and 'url' in brand['custom_url'] else ''                                         
              new_list["default_layout"] = {}
              new_list["versions"]= [
                   { 
                    "name":brand["name"],
                    "status":"active", 
                    "class":"",
                    "type": "brand",
                    "seo_details":seo_details,
                    "components":[
                        {
                        "id": 1,
                        "name": "HTML Block",
                        "code": "html_block",  
                        "variant": {
                          "id": "1",
                          "name": "HTML Block",
                          "admin_layout": "style1",
                          "class": [],                          
                          "config": {
                               "data": ""   
                                 }
                        },  
                        "children": []
                        }
                    ],
                    "created_at":int(datetime.utcnow().timestamp()),
                    "created_by": user,                                                                              
                    "version":0,
             } ]
              new_list["preview_state"] = {} 
              new_list["updated_at"]=""
              new_list["created_at"]=int(datetime.utcnow().timestamp())
              new_list["status"] = "active"
              new_list["image_url"]=brand['image_url']
              res = cms_brands_db.create(new_list)
          else:
            query = {"_id": ObjectId(str(brand['id']))}
            delete_documents_from_storefront_collection(store_id, 'brands', query) 
              
        result='success'
        return result