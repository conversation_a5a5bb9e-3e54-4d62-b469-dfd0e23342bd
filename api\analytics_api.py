import datetime
from flask import request
import logging
import traceback
from api import APIResource
from analytics import product_sales, replenishment, sold_product_analytics, analytics_dashboard, consignment_dashboard, top_sales_rep,soldproducts_analytics
from utils import product_util, store_util,order_util
import asyncio

from threading import current_thread, get_ident, get_native_id

logger = logging.getLogger()

class SoldProductsAnalytics(APIResource):
    def get_executor(self, request, token_payload, store):
        query_params = request.args.to_dict()
        res = soldproducts_analytics.get_sold_products(store, query_params)
        if res['status'] == 200:
            return res['data'], res['status']
        else:
            return {"message": res['message']}, res['status']   
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ProductsConsignmentAnalytics(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering consignment Products GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            search_term = query_params.get('search_term', '')
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            res = consignment_dashboard.get_consignment_order_listing(store, page, limit, search_term, sort_array)
            if res['status'] == 200:
                return res['data'], res['status']
            else:
                return {"message": res['message']}, res['status']                        
        finally:
            logger.debug("Exiting consignment Products GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class ProductsOnHoldAnalytics(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering on-hold Products GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            search_term = query_params.get('search_term', '')
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            res = consignment_dashboard.get_on_hold_order_listing(store, page, limit, search_term, sort_array)
            if res['status'] == 200:
                return res['data'], res['status']
            else:
                return {"message": res['message']}, res['status']                        
        finally:
            logger.debug("Exiting on-hold Products GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)


class HideProductFromStoreFront(APIResource):

    def post_executor(self, request, token_payload, store):
        logger.debug("Entering HideProductFromStoreFront POST")
        try:
            product_ids = request.args.get("product_ids")
            process_list = [int(num_str) for num_str in product_ids.split(',')]

            visible = request.args.get("visible")

            res = product_util.change_products_visibility(store_util.get_bc_api_creds(store), process_list, visible)

            # Return response ...
            return res, 200
        finally:
            logger.debug("Exiting HideProductFromStoreFront POST")
    
    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
class ProductSales(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering ProductSales GET")
        try:
            query_params = request.args.to_dict()
            res = product_sales.get_product_sale(store, query_params['product_id'], 
                    query_params['start_date'], query_params['end_date'], 
                    query_params['period'])
            res = {
                'data': res
            }
            return res, 200
        finally:
            logger.debug("Exiting ProductSales GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ProductList(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Product List GET")
        try:
            query_params = request.args.to_dict()
            res = order_util.get_product_details(store, query_params)
            res = {
                'data': res
            }
            return res, 200
        finally:
            logger.debug("Exiting Product List GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ProductInOrderConsignment(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Product exist in consignment GET")
        try:
            query_params = request.args.to_dict()
            res = order_util.is_product_available(store, query_params)
            res = {
                'data': res
            }
            return res, 200
        finally:
            logger.debug("Exiting Product exist in consignment GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ProductInfo(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering ProductSales GET")
        try:
            query_params = request.args.to_dict()
            res = order_util.get_product_info(store,query_params)
            if res['status'] == 200:
                return {'data': res['data']}, res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting ProductSales GET")       

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class VariantReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Variant Report GET")
        try:
            query_params = request.args.to_dict()
            res = order_util.get_variant_report(store, query_params)
            res = {
                'data': res
            }
            return res, 200
        finally:
            logger.debug("Exiting variant report GET")
       

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class SoldProductSummary(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering SoldProductSummary GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)

            if product_id and start_date and end_date:
                res = sold_product_analytics.get_sold_product_analytics_summary(store, product_id, start_date, end_date)
                return res, 200
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting SoldProductSummary GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class SoldProductPriceReport(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering SoldProductPriceReport GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)

            if product_id and start_date and end_date:
                res = sold_product_analytics.get_product_price_report(store, product_id, start_date, end_date)
                res = {
                    'data': res
                }
                return res, 200
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting SoldProductPriceReport GET {end_time-start_time}")
       

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class AggregatedProductReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering AggregatedProductReport GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []

            if start_date and end_date:
                res = sold_product_analytics.get_aggregated_product_report(store, start_date, end_date, page, limit, sort_array)
                return res, 200
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting AggregatedProductReport GET {end_time-start_time}")
       

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class SoldProductSkuReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering SoldProductSkuReport GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort = query_params.get('sort', "")
            state = query_params.get('state', None)
            thread = current_thread()
            logging.info(f'Main thread: name={thread.name}, idnet={get_ident()}, id={get_native_id()}')
            res = sold_product_analytics.get_sku_report(store=store, product_id=product_id, start_date=start_date,
                                                        end_date=end_date, sort=sort,state=state)
            return res, 200
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting SoldProductSkuReport GET {end_time-start_time}")
       

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class VariantSalesByStates(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering VariantSalesByStates GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort = query_params.get('sort', "desc")
            thread = current_thread()
            logging.info(f'Main thread: name={thread.name}, idnet={get_ident()}, id={get_native_id()}')
            res = sold_product_analytics.get_variant_sales_by_states(store=store, product_id=product_id, start_date=start_date, 
                                                                                 end_date=end_date, sort=sort)
            return res, 200
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting VariantSalesByStates GET {end_time-start_time}")
       

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class VariantSalesByCustomers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering VariantSalesByCustomers GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort = query_params.get('sort', "desc")
            state = query_params.get('state', None)
            thread = current_thread()
            logging.info(f'Main thread: name={thread.name}, idnet={get_ident()}, id={get_native_id()}')
            res = sold_product_analytics.get_variant_sales_by_customers(store=store, product_id=product_id, start_date=start_date, 
                                                                                 end_date=end_date, sort=sort, state=state)
            return res, 200
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting VariantSalesByCustomers GET {end_time-start_time}")
       

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class AllStatesForFilter(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering AllStatesForFilter GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            res = sold_product_analytics.get_product_sold_states(store, product_id, start_date, end_date)
            res = {
                'data': res
            }
            return res, 200
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting AllStatesForFilter GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ProductPerformanceTable(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering ProductPerformanceTable GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            res = sold_product_analytics.get_product_performance_data(store, product_id, start_date, end_date)
            res = {
                'data': res
            }
            return res, 200
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting ProductPerformanceTable GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class AnalyticsDashboardSummary(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering summary GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            res = analytics_dashboard.get_summary(store, start_date, end_date)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting summary GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)

class AnalyticsDashboardTopProducts(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Top products GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            res = analytics_dashboard.get_top_products(store, start_date, end_date)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting Top products GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class AnalyticsDashboardTopCustomers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Top customers GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            res = analytics_dashboard.get_top_customers(store, start_date, end_date)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting Top customers GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class AnalyticsDashboardTopStates(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Top states GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            res = analytics_dashboard.get_top_states(store, start_date, end_date)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting Top states GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class ConsignmentTopProducts(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Top products consignment GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort = query_params.get('sort', 'desc')
            res = consignment_dashboard.get_top_products_consignment(store, start_date, end_date, sort)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting Top products consignment GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class OnHoldTopProducts(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Top products on hold GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort = query_params.get('sort', 'desc')
            res = consignment_dashboard.get_top_products_onhold(store, start_date, end_date, sort)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting Top products on hold GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class TopDistributors(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Top distributors GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            # query_params = request.args.to_dict()
            # start_date = query_params.get('start_date', None)
            # end_date = query_params.get('end_date', None)
            res = consignment_dashboard.get_top_distributors(store)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting Top distributors GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ConsignmentSummary(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Consignment summary GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            # query_params = request.args.to_dict()
            # start_date = query_params.get('start_date', None)
            # end_date = query_params.get('end_date', None)
            res = consignment_dashboard.get_consignment_summary(store)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting Consignment summary GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class TopSalesRep(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering TopSalesRep GET")
        try:
            query_params = request.args.to_dict()
            start_date=query_params['start_date']
            end_date=query_params['end_date']
            sort_order=query_params['sort_order']
            res = top_sales_rep.get_top_sales_rep( 
                    start_date,end_date, sort_order)
            res = {
                'data': res
            }
            return res, 200
        finally:
            logger.debug("Exiting TopSalesRep GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ConsignmentTopCustomers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering AggregatedCustomerReport GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            # start_date = query_params.get('start_date', None)
            # end_date = query_params.get('end_date', None)
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            
            res = consignment_dashboard.get_top_customers_consignment(store, page, limit, sort_array)
            return  res['data'], res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting AggregatedCustomerReport GET {end_time-start_time}")
       

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ReorderRateReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Reorder Rate Report GET")
        try:
            query_params = request.args.to_dict()
            res = sold_product_analytics._fetch_reorder_rate_report(store, query_params['product_id'])
            if res['status'] == 200:
                return  res['data'], res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Reorder Rate Report GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

# class RevenueReport(APIResource):
#     def get_executor(self, request, token_payload, store):
#         logger.debug("Entering Revenue Report GET")
#         try:
#             query_params = request.args.to_dict()
#             res = sold_product_analytics._fetch_revenue_report(store, query_params['product_id'])
#             if res['status'] == 200:
#                 return  res['data'], res['status']
#             else:
#                 return {'message': res['message']}, res['status']
#         finally:
#             logger.debug("Exiting Revenue Report GET")

#     def get(self):
#         return self.execute_store_request(request, self.get_executor)

# class QuantitySoldReport(APIResource):
#     def get_executor(self, request, token_payload, store):
#         logger.debug("Entering Quantity Sold Report GET")
#         try:
#             query_params = request.args.to_dict()
#             res = sold_product_analytics._fetch_quantity_sold_report(store, query_params['product_id'])
#             if res['status'] == 200:
#                 return  res['data'], res['status']
#             else:
#                 return {'message': res['message']}, res['status']
#         finally:
#             logger.debug("Exiting Quantity Sold Report GET")

#     def get(self):
#         return self.execute_store_request(request, self.get_executor)

# class NewCustomersTable(APIResource):
#     def get_executor(self, request, token_payload, store):
#         logger.debug("Entering New Customers Report GET")
#         try:
#             query_params = request.args.to_dict()
#             res = sold_product_analytics._fetch_new_customers_table(store, query_params['product_id'])
#             if res['status'] == 200:
#                 return  res['data'], res['status']
#             else:
#                 return {'message': res['message']}, res['status']
#         finally:
#             logger.debug("Exiting New Customers Report GET")

#     def get(self):
#         return self.execute_store_request(request, self.get_executor)

class NewCustomersReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering New Customers Report GET")
        try:
            query_params = request.args.to_dict()
            res = sold_product_analytics._fetch_new_customers_report(store, query_params['product_id'])
            if res['status'] == 200:
                return  res['data'], res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting New Customers Report GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)       

class ProductPriceReportOrderDetails(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Product Price Report Order Details GET")
        try:
            query_params = request.args.to_dict()
            product_id = query_params.get('product_id')
            start_date = query_params.get('start_date')
            end_date = query_params.get('end_date')
            price = query_params.get('price')
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                res = sold_product_analytics.get_product_price_report_order_details(store['id'], product_id, start_date, end_date, price)
                if res['status'] == 200:
                    return  res['data'], res['status']
            else:
                return {'message': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Product Price Report Order Details GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)



class ProductReportDetails(APIResource):
    def get_executor(self, request, token_payload, store):
        query_params = request.args.to_dict()
        product_id = query_params.get('product_id')
        start_date = query_params.get('start_date')
        end_date = query_params.get('end_date')
        sort = query_params.get('sort', 'desc')
        top_customers_state = query_params.get('top_customers_state')
        period = query_params.get('period', 'month')
        sku_report_state = query_params.get('sku_report_state')

        if not all([product_id, start_date, end_date]):
            return {'message': 'product_id, start_date, and end_date are required.'}, 400

        async def get_all_data():
            # NOTE: We are calling these functions with slightly different arguments.
            # You might need to adjust them based on the exact function definitions
            # in sold_product_analytics.py, especially for functions not fully visible
            # in the context, like get_variant_sales_by_states.
            price_report_task = asyncio.to_thread(
                sold_product_analytics.get_product_price_report, store, product_id, start_date, end_date)
            summary_task = asyncio.to_thread(
                sold_product_analytics.get_sold_product_analytics_summary, store, product_id, start_date, end_date)
            sku_report_task = asyncio.to_thread(
                sold_product_analytics.get_sku_report, store, product_id, start_date, end_date, sort, sku_report_state)
            
            # Assuming these functions exist with similar signatures
            variant_states_task = asyncio.to_thread(
                sold_product_analytics.get_variant_sales_by_states, store, product_id, start_date, end_date, sort)
            variant_customers_task = asyncio.to_thread(
                sold_product_analytics.get_variant_sales_by_customers, store, product_id, start_date, end_date, sort, top_customers_state)
            
            all_states_task = asyncio.to_thread(
                sold_product_analytics.get_product_sold_states, store, product_id, start_date, end_date)
            performance_task = asyncio.to_thread(
                sold_product_analytics.get_product_performance_data, store, product_id, start_date, end_date)
            
            product_sales_task = asyncio.to_thread(product_sales.get_product_sale, store, product_id, start_date, end_date, period)

            results = await asyncio.gather(
                price_report_task,
                summary_task,
                sku_report_task,
                variant_states_task,
                variant_customers_task,
                all_states_task,
                performance_task,
                product_sales_task,
                return_exceptions=True
            )
            
            (price_report, summary, sku_report, variant_states, variant_customers, all_states, performance, product_sales_data) = results

            # Basic error handling for gathered results
            def process_result(r):
                return r if not isinstance(r, Exception) else {"error": str(r)}

            return {
                'price_report': process_result(price_report),
                'summary': process_result(summary),
                'sku_report': process_result(sku_report),
                'variant_sales_by_states': process_result(variant_states),
                'variant_sales_by_customers': process_result(variant_customers),
                'all_states': process_result(all_states),
                'product_performance': process_result(performance),
                'product_sales': process_result(product_sales_data),
            }

        try:
            dashboard_data = asyncio.run(get_all_data())
            return {'data': dashboard_data}, 200
        except Exception as e:
            logger.error(f"Error fetching state-wise stats dashboard: {traceback.format_exc()}")
            return {"message": "Failed to fetch dashboard data.", "error": str(e)}, 500
        
    def get(self):
        return self.execute_store_request(request, self.get_executor)   

class ReplenishmentReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Replenishment Report Data GET")
        try:
            query_params = request.args.to_dict()
            res = sold_product_analytics.get_replenishment_report(store, query_params)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Replenishment Report GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)   

class AnalyticsReplenishmentOutOfStockOccurenceDetails(APIResource):
    def get_executor(self, request, token_payload, store, parent_sku):
        logger.debug(f"Entering AnalyticsReplenishmentOutOfStockOccurenceDetails GET for parent_sku: {parent_sku}")
        try:
            if not parent_sku:
                return {"message": "Please enter parent_sku as a path parameter"}, 400
            
            query_params = request.args.to_dict()
            variant_sku = query_params.get('variant_sku', '')
            
            res = replenishment.get_replenishment_out_of_stock_occurence_details(store['id'], parent_sku, variant_sku)
            
            if res['status'] == 200:
                return {'data': res['data']}, res['status']
            else:
                return {'message': res['message']}, res['status']
            
        except Exception as e:
            logger.error(f"Error in AnalyticsReplenishmentOutOfStockOccurenceDetails GET: {str(e)}")
            return {'message': 'Internal server error'}, 500
        finally:
            logger.debug(f"Exiting ReplenishmentOutOfStockOccurenceDetails GET for parent_sku: {parent_sku}")

    def get(self, parent_sku):
        return self.execute_store_request(request, self.get_executor, parent_sku)

class AnalyticsZohoReturnsData(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Zoho Returns Data GET")
        try:
            query_params = request.args.to_dict()
            variant_sku = query_params.get('variant_sku', '')
            returns_type = query_params.get('returns_type', '')
            if variant_sku:
                res = replenishment.get_zoho_returns_data(store, variant_sku, returns_type)
                if res['status'] == 200:
                    return {"data": res['data']}, 200
                else:
                    return {"message": res['message']}, res['status']
            else:
                return {"message": "Please pass variant_sku in the query parameters."}, 400
        finally:
            logger.debug("Exiting Zoho Returns Data GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor) 

class AnalyticsZohoReturnsParentData(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Zoho Returns Parent Data GET")
        try:
            query_params = request.args.to_dict()
            parent_sku = query_params.get('parent_sku', '')
            if parent_sku:
                res = replenishment.get_zoho_returns_parent_data(store['id'], parent_sku)
                if res['status'] == 200:
                    return {"data": res['data']}, 200
                else:
                    return {"message": res['message']}, res['status']
            else:
                return {"message": "Please pass parent_sku in the query parameters."}, 400
        finally:
            logger.debug("Exiting Zoho Returns Parent Data GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)