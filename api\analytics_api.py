import datetime
from flask import request
import logging
import traceback
from api import APIResource
from analytics import product_sales, sold_product_analytics, analytics_dashboard, consignment_dashboard, top_sales_rep,soldproducts_analytics
from utils import product_util, store_util,order_util
import asyncio

from threading import current_thread, get_ident, get_native_id

logger = logging.getLogger()

class SoldProductsAnalytics(APIResource):
    def get_executor(self, request, token_payload, store):
        query_params = request.args.to_dict()
        res = soldproducts_analytics.get_sold_products(store, query_params)
        if res['status'] == 200:
            return res['data'], res['status']
        else:
            return {"message": res['message']}, res['status']   
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ProductsConsignmentAnalytics(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering consignment Products GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            search_term = query_params.get('search_term', '')
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            res = consignment_dashboard.get_consignment_order_listing(store, page, limit, search_term, sort_array)
            if res['status'] == 200:
                return res['data'], res['status']
            else:
                return {"message": res['message']}, res['status']                        
        finally:
            logger.debug("Exiting consignment Products GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class ProductsOnHoldAnalytics(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering on-hold Products GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            search_term = query_params.get('search_term', '')
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            res = consignment_dashboard.get_on_hold_order_listing(store, page, limit, search_term, sort_array)
            if res['status'] == 200:
                return res['data'], res['status']
            else:
                return {"message": res['message']}, res['status']                        
        finally:
            logger.debug("Exiting on-hold Products GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)


class HideProductFromStoreFront(APIResource):

    def post_executor(self, request, token_payload, store):
        logger.debug("Entering HideProductFromStoreFront POST")
        try:
            product_ids = request.args.get("product_ids")
            process_list = [int(num_str) for num_str in product_ids.split(',')]

            visible = request.args.get("visible")

            res = product_util.change_products_visibility(store_util.get_bc_api_creds(store), process_list, visible)

            # Return response ...
            return res, 200
        finally:
            logger.debug("Exiting HideProductFromStoreFront POST")
    
    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
class ProductSales(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering ProductSales GET")
        try:
            query_params = request.args.to_dict()
            res = product_sales.get_product_sale(store, query_params['product_id'], 
                    query_params['start_date'], query_params['end_date'], 
                    query_params['period'])
            res = {
                'data': res
            }
            return res, 200
        finally:
            logger.debug("Exiting ProductSales GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ProductList(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Product List GET")
        try:
            query_params = request.args.to_dict()
            res = order_util.get_product_details(store, query_params)
            res = {
                'data': res
            }
            return res, 200
        finally:
            logger.debug("Exiting Product List GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ProductInOrderConsignment(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Product exist in consignment GET")
        try:
            query_params = request.args.to_dict()
            res = order_util.is_product_available(store, query_params)
            res = {
                'data': res
            }
            return res, 200
        finally:
            logger.debug("Exiting Product exist in consignment GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ProductInfo(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering ProductSales GET")
        try:
            query_params = request.args.to_dict()
            res = order_util.get_product_info(store,query_params)
            if res['status'] == 200:
                return {'data': res['data']}, res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting ProductSales GET")       

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class VariantReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Variant Report GET")
        try:
            query_params = request.args.to_dict()
            res = order_util.get_variant_report(store, query_params)
            res = {
                'data': res
            }
            return res, 200
        finally:
            logger.debug("Exiting variant report GET")
       

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class SoldProductSummary(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering SoldProductSummary GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)

            if product_id and start_date and end_date:
                res = sold_product_analytics.get_sold_product_analytics_summary(store, product_id, start_date, end_date)
                return res, 200
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting SoldProductSummary GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class SoldProductPriceReport(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering SoldProductPriceReport GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)

            if product_id and start_date and end_date:
                res = sold_product_analytics.get_product_price_report(store, product_id, start_date, end_date)
                res = {
                    'data': res
                }
                return res, 200
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting SoldProductPriceReport GET {end_time-start_time}")
       

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class AggregatedProductReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering AggregatedProductReport GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []

            if start_date and end_date:
                res = sold_product_analytics.get_aggregated_product_report(store, start_date, end_date, page, limit, sort_array)
                return res, 200
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting AggregatedProductReport GET {end_time-start_time}")
       

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class SoldProductSkuReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering SoldProductSkuReport GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort = query_params.get('sort', "")
            state = query_params.get('state', None)
            thread = current_thread()
            logging.info(f'Main thread: name={thread.name}, idnet={get_ident()}, id={get_native_id()}')
            res = sold_product_analytics.get_sku_report(store=store, product_id=product_id, start_date=start_date,
                                                        end_date=end_date, sort=sort,state=state)
            return res, 200
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting SoldProductSkuReport GET {end_time-start_time}")
       

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class VariantSalesByStates(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering VariantSalesByStates GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort = query_params.get('sort', "desc")
            thread = current_thread()
            logging.info(f'Main thread: name={thread.name}, idnet={get_ident()}, id={get_native_id()}')
            res = sold_product_analytics.get_variant_sales_by_states(store=store, product_id=product_id, start_date=start_date, 
                                                                                 end_date=end_date, sort=sort)
            return res, 200
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting VariantSalesByStates GET {end_time-start_time}")
       

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class VariantSalesByCustomers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering VariantSalesByCustomers GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort = query_params.get('sort', "desc")
            state = query_params.get('state', None)
            thread = current_thread()
            logging.info(f'Main thread: name={thread.name}, idnet={get_ident()}, id={get_native_id()}')
            res = sold_product_analytics.get_variant_sales_by_customers(store=store, product_id=product_id, start_date=start_date, 
                                                                                 end_date=end_date, sort=sort, state=state)
            return res, 200
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting VariantSalesByCustomers GET {end_time-start_time}")
       

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class AllStatesForFilter(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering AllStatesForFilter GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            res = sold_product_analytics.get_product_sold_states(store, product_id, start_date, end_date)
            res = {
                'data': res
            }
            return res, 200
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting AllStatesForFilter GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ProductPerformanceTable(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering ProductPerformanceTable GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            product_id = query_params.get('product_id', None)
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            res = sold_product_analytics.get_product_performance_data(store, product_id, start_date, end_date)
            res = {
                'data': res
            }
            return res, 200
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting ProductPerformanceTable GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class AnalyticsDashboardSummary(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering summary GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            res = analytics_dashboard.get_summary(store, start_date, end_date)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting summary GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)

class AnalyticsDashboardTopProducts(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Top products GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            res = analytics_dashboard.get_top_products(store, start_date, end_date)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting Top products GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class AnalyticsDashboardTopCustomers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Top customers GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            res = analytics_dashboard.get_top_customers(store, start_date, end_date)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting Top customers GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class AnalyticsDashboardTopStates(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Top states GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            res = analytics_dashboard.get_top_states(store, start_date, end_date)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting Top states GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class ConsignmentTopProducts(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Top products consignment GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort = query_params.get('sort', 'desc')
            res = consignment_dashboard.get_top_products_consignment(store, start_date, end_date, sort)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting Top products consignment GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class OnHoldTopProducts(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Top products on hold GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort = query_params.get('sort', 'desc')
            res = consignment_dashboard.get_top_products_onhold(store, start_date, end_date, sort)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting Top products on hold GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class TopDistributors(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Top distributors GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            # query_params = request.args.to_dict()
            # start_date = query_params.get('start_date', None)
            # end_date = query_params.get('end_date', None)
            res = consignment_dashboard.get_top_distributors(store)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting Top distributors GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ConsignmentSummary(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Consignment summary GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            # query_params = request.args.to_dict()
            # start_date = query_params.get('start_date', None)
            # end_date = query_params.get('end_date', None)
            res = consignment_dashboard.get_consignment_summary(store)
            return {'data': res['data']}, res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting Consignment summary GET {end_time-start_time}")
       
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class TopSalesRep(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering TopSalesRep GET")
        try:
            query_params = request.args.to_dict()
            start_date=query_params['start_date']
            end_date=query_params['end_date']
            sort_order=query_params['sort_order']
            res = top_sales_rep.get_top_sales_rep( 
                    start_date,end_date, sort_order)
            res = {
                'data': res
            }
            return res, 200
        finally:
            logger.debug("Exiting TopSalesRep GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ConsignmentTopCustomers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering AggregatedCustomerReport GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            # start_date = query_params.get('start_date', None)
            # end_date = query_params.get('end_date', None)
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            
            res = consignment_dashboard.get_top_customers_consignment(store, page, limit, sort_array)
            return  res['data'], res['status']
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting AggregatedCustomerReport GET {end_time-start_time}")
       

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ReorderRateReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Reorder Rate Report GET")
        try:
            query_params = request.args.to_dict()
            res = sold_product_analytics._fetch_reorder_rate_report(store, query_params['product_id'])
            if res['status'] == 200:
                return  res['data'], res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Reorder Rate Report GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

# class RevenueReport(APIResource):
#     def get_executor(self, request, token_payload, store):
#         logger.debug("Entering Revenue Report GET")
#         try:
#             query_params = request.args.to_dict()
#             res = sold_product_analytics._fetch_revenue_report(store, query_params['product_id'])
#             if res['status'] == 200:
#                 return  res['data'], res['status']
#             else:
#                 return {'message': res['message']}, res['status']
#         finally:
#             logger.debug("Exiting Revenue Report GET")

#     def get(self):
#         return self.execute_store_request(request, self.get_executor)

# class QuantitySoldReport(APIResource):
#     def get_executor(self, request, token_payload, store):
#         logger.debug("Entering Quantity Sold Report GET")
#         try:
#             query_params = request.args.to_dict()
#             res = sold_product_analytics._fetch_quantity_sold_report(store, query_params['product_id'])
#             if res['status'] == 200:
#                 return  res['data'], res['status']
#             else:
#                 return {'message': res['message']}, res['status']
#         finally:
#             logger.debug("Exiting Quantity Sold Report GET")

#     def get(self):
#         return self.execute_store_request(request, self.get_executor)

# class NewCustomersTable(APIResource):
#     def get_executor(self, request, token_payload, store):
#         logger.debug("Entering New Customers Report GET")
#         try:
#             query_params = request.args.to_dict()
#             res = sold_product_analytics._fetch_new_customers_table(store, query_params['product_id'])
#             if res['status'] == 200:
#                 return  res['data'], res['status']
#             else:
#                 return {'message': res['message']}, res['status']
#         finally:
#             logger.debug("Exiting New Customers Report GET")

#     def get(self):
#         return self.execute_store_request(request, self.get_executor)

class NewCustomersReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering New Customers Report GET")
        try:
            query_params = request.args.to_dict()
            res = sold_product_analytics._fetch_new_customers_report(store, query_params['product_id'])
            if res['status'] == 200:
                return  res['data'], res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting New Customers Report GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)       