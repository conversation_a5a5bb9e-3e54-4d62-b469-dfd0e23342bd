from sqlalchemy.dialects.postgresql import insert
import new_pgdb as db
import string
from sqlalchemy import Column, DateTime, String, Integer, Float, text, delete, update, asc, func, select
from datetime import datetime
from sqlalchemy.exc import SQLAlchemyError

class Tags(db.Base):
    __tablename__ = db.DBTables.tags_table

    id = Column(String, primary_key=True)
    tag = Column(String)   
    created_at = Column(DateTime, server_default=func.now())

    @classmethod
    def get_tag_id(cls, tag):
        tag = tag.translate({ord(c): None for c in string.whitespace})
        return tag.lower() 

    @classmethod
    def get_all_tags(cls, store, session=None):
        local_session = None
        if not session:
            session = db.get_session(store['id'])
            local_session = session
        try:
            tags = session.query(Tags).order_by(asc(Tags.id)).all()
            return tags
        finally:
            if local_session:
                local_session.close()
    
    @classmethod
    def get_tag(cls, tag_id, session=None):
        local_session = None
        if not session:
            session = db.get_session()
            local_session = session
        try:
            tag = session.query(Tags).get(tag_id)
            return tag
        finally:
            if local_session:
                local_session.close()

    @classmethod
    def add_tags(cls, tags, store, session=None):
        # First, check if any tags already exist
        existing_tags = cls.fetch_existing_tags(tags, store, session)

        # If any of the tags already exist, raise an error
        if existing_tags:
            existing_tags_str = ", ".join(existing_tags)
            raise ValueError(f"tags: {existing_tags_str} already exist in the system.")

        # If no existing tags, proceed with the insertion
        stmt = insert(Tags).on_conflict_do_nothing()  # Ensure no duplicates get inserted
        values = []
        for tag in tags:
            tag_id = cls.get_tag_id(tag)
            created_at_str = datetime.now().isoformat()
            values.append({"id": tag_id, "tag": tag, "created_at": created_at_str})

        db.execute_stmt(store, stmt, values, session)
        return values

    
    @classmethod
    def fetch_existing_tags(cls, tags, store, session=None):
        # Generate tag_ids
        tag_ids = [cls.get_tag_id(tag) for tag in tags]

        if not tag_ids:
            return []

        # Define the query to fetch existing tags
        query = select(Tags.id).where(Tags.id.in_(tag_ids))

        # Execute the query directly
        
        try:
            conn = db.get_connection(store['id'])
            # Execute the query and fetch results
            result = conn.execute(query)
            rows = result.fetchall()
            return [row[0] for row in rows]  # row[0] because `select(Tags.id)` returns tuples
        except Exception as e:
            return []
        finally:
            if conn:
                conn.close()

    
    @classmethod
    def add_tag(cls, tag, session=None):
        stmt = insert(Tags).on_conflict_do_nothing()
        values = []
        tag_id = cls.get_tag_id(tag)
        values.append({"id": tag_id, "tag": tag})
        db.execute_stmt(stmt, values, session)
        return values[0]
    
    @classmethod
    def delete_tags(cls, tagId, store, session=None):
        delete_stmt = delete(Tags).where(Tags.id.in_(tagId))
        db.execute_stmt(store, delete_stmt, session=session)
        delete_tags_in_product = delete(ProductTags).where(ProductTags.tag_id.in_(tagId))           
        db.execute_stmt(store, delete_tags_in_product, session=session)
   
    @classmethod
    def delete_tags(cls, tagId, store, session=None):
        delete_stmt = delete(Tags).where(Tags.id.in_(tagId))
        db.execute_stmt(store, delete_stmt, session=session)
        delete_tags_in_product = delete(ProductTags).where(ProductTags.tag_id.in_(tagId))           
        db.execute_stmt(store, delete_tags_in_product, session=session)

    @classmethod
    def update_tags(cls, old_tag_ids, new_tags, store, session=None):
        delete_stmt = delete(Tags).where(Tags.id.in_(old_tag_ids))
        db.execute_stmt(store, delete_stmt, session=session)
        stmt = insert(Tags).on_conflict_do_nothing()
        values = []
        for tag in new_tags:
            tag_id = cls.get_tag_id(tag)
            created_at_str = datetime.now().isoformat()
            values.append({"id": tag_id, "tag": tag, "created_at": created_at_str})
            update_stmt = update(ProductTags).where(ProductTags.tag_id.in_(old_tag_ids)).values(tag_id=tag_id)
            db.execute_stmt(store, update_stmt, session=session)
        db.execute_stmt(store, stmt, values, session) 
        return values

class ProductTags(db.Base):
    __tablename__ = db.DBTables.product_tags_table

    tag_id = Column(String, primary_key=True)
    sku = Column(String, primary_key=True)
    variant_sku = Column(String)
    created_at = Column(DateTime, server_default=func.now())
    
    @classmethod
    def get_tags_by_product_sku(cls, product_sku, store, session=None):
        local_session = None
        if not session:
            session = db.get_session(store['id'])
            local_session = session
        try:
            tags = session.query(ProductTags, Tags).filter(ProductTags.tag_id==Tags.id).filter(ProductTags.sku == product_sku).all()
            return tags
        finally:
            if local_session:
                local_session.close()
    
    @classmethod
    def add_product_tags(cls, product_tags, store, session=None):
        stmt = insert(ProductTags).on_conflict_do_nothing()
        db.execute_stmt(store, stmt, product_tags, session)

    @classmethod
    def delete_product_tags(cls, product_skus,store, session=None):
        delete_stmt = delete(ProductTags).where(ProductTags.sku.in_(product_skus))
        db.execute_stmt(store, delete_stmt, session=session)

    @classmethod
    def get_all_product_tags(cls, session):
        products = session.query(ProductTags, Tags).filter(ProductTags.tag_id==Tags.id).all()   
        return products        