from mongo_db import user_db
import new_pgdb
import new_utils
from sqlalchemy import text
import task
from utils.common import convert_to_timestamp
import traceback
import logging
from new_mongodb import fetchall_documents_from_admin_collection
from collections import defaultdict
logger = logging.getLogger()


def get_customer_product_price_list(store_id, page=1, limit=10, sort_by="", search="", customer_ids="", sales_rep_emails=""):
    # Ensure valid pagination values
    try:
        page = max(1, int(page))
        limit = max(1, int(limit))
    except ValueError:
        return {"status": 400, "message": "Invalid page or limit values"}
    # Define allowed sorting columns
    sort_columns = ["product_name", "sku", "company", "name", "product_id", "customer_id", "price", "updated_by", "updated_at"]
    sort_direction = "ASC"
    sort_column = "updated_at"  # Default sorting column
    # Validate sorting
    if sort_by:
        sort_array = sort_by.split('/')
        if len(sort_array) == 2 and sort_array[0] in sort_columns:
            sort_direction = "ASC" if sort_array[1] == "1" else "DESC"
            sort_column = sort_array[0]
    # Build search condition
    search_condition = ""
    search_params = {}
    conditions = []
    if search:
        search = search.strip()
        if search.isnumeric():
            search_condition = "WHERE (CAST(p.product_id AS TEXT) ILIKE :search)"
        else:
            search_condition = """WHERE (
                c.first_name ILIKE :search OR
                c.last_name ILIKE :search OR
                REPLACE(CONCAT(c.first_name, ' ', c.last_name), ' ', '') ILIKE :search_no_space OR
                c.email ILIKE :search OR
                p.product_name ILIKE :search OR
                p.sku ILIKE :search
            )"""
            search_params["search_no_space"] = f"%{search.replace(' ', '')}%"
        search_params["search"] = f"%{search}%"
    # Add customer_ids filter
    if customer_ids:
        id_list = customer_ids.split(",") if isinstance(customer_ids, str) else []
        conditions.append("pcp.customer_id = ANY(:customer_ids)")
        search_params["customer_ids"] = list(map(int, id_list))
    # Add sales_rep_emails filter
    if sales_rep_emails:
        sales_rep_list = sales_rep_emails.split(",") if isinstance(sales_rep_emails, str) else []
        conditions.append("scr.rep_email = ANY(:sales_rep_emails)")
        search_params["sales_rep_emails"] = sales_rep_list
    if conditions:
        prefix = "WHERE" if not search_condition else "AND"
        search_condition += f" {prefix} " + " AND ".join(conditions)
    # SQL queries (COUNT & DATA)
    query_count = text(f"""
        SELECT COUNT(*)
        FROM product_customer_price pcp
        LEFT JOIN customers c ON pcp.customer_id = c.customer_id
        LEFT JOIN products p ON pcp.product_id = p.product_id
        LEFT JOIN salesforce_customer_rep scr ON c.customer_id = scr.customer_id
        {search_condition}
    """)
    query_data = text(f"""
        SELECT
            pcp.id, pcp.customer_id, pcp.product_id, pcp.price,
            p.product_name, p.sku,
            c.email, c.company, c.first_name, c.last_name,
            CONCAT(c.first_name, ' ', c.last_name) AS name,
            c.customer_group_id, c.customer_group_name,
            scr.rep_name, scr.rep_email,
            pcp.updated_at, pcp.updated_by
        FROM product_customer_price pcp
        LEFT JOIN customers c ON pcp.customer_id = c.customer_id
        LEFT JOIN products p ON pcp.product_id = p.product_id
        LEFT JOIN salesforce_customer_rep scr ON c.customer_id = scr.customer_id
        {search_condition}
        ORDER BY {sort_column} {sort_direction}
        LIMIT :limit OFFSET :offset
    """)
    try:
        with new_pgdb.get_connection(store_id) as conn:
            # Get total count
            total_count = conn.execute(query_count, search_params).scalar()
            # Get paginated data
            result = conn.execute(query_data, {**search_params, "limit": limit, "offset": (page - 1) * limit})
            rows = result.fetchall()
            # Collect product_ids in an array
            product_ids = [row[2] for row in rows]
            vip_and_distributor_data = fetchall_documents_from_admin_collection(
                store_id,
                "product_price_lists",
                query={"parent_product_id": {"$in": product_ids}},
                projection={"parent_product_id": 1, "variants": 1, "parent_product_sku": 1}
            )
            vip_and_distributor_data = list(vip_and_distributor_data)
            sku_to_product_id = {doc["parent_product_sku"]: doc["parent_product_id"] for doc in vip_and_distributor_data}

            cost_map = _fetch_costs(store_id, sku_to_product_id)

            price_map = {}
            for record in vip_and_distributor_data:
                product_id = record.get("parent_product_id")
                variants = record.get("variants", [])

                if product_id not in price_map:
                    price_map[product_id] = {
                        "vip_prices": set(),
                        "distributor_prices": set()
                    }

                for variant in variants:
                    for price_entry in variant.get("price_list", []):
                        if price_entry.get("price_list_id") == 52 and price_entry.get("price") is not None:
                            price_map[product_id]["vip_prices"].add(price_entry["price"])
                        elif price_entry.get("price_list_id") == 13 and price_entry.get("price") is not None:
                            price_map[product_id]["distributor_prices"].add(price_entry["price"])
                
                range_map = {}
                for product_id, prices in price_map.items():
                    vip_prices = prices["vip_prices"]
                    distributor_prices = prices["distributor_prices"]

                    vip_range = (
                        f"{min(vip_prices)}-{max(vip_prices)}"
                        if len(vip_prices) > 1
                        else (str(next(iter(vip_prices))) if vip_prices else None)
                    )
                    distributor_range = (
                        f"{min(distributor_prices)}-{max(distributor_prices)}"
                        if len(distributor_prices) > 1
                        else (str(next(iter(distributor_prices))) if distributor_prices else None)
                    )
                    range_map[product_id] = {
                        "vip": vip_range,
                        "distributor": distributor_range
                    }

            # Now loop over the result rows and use the price_map
            data = []
            updated_by_users = set()
            for row in rows:
                product_id = row[2]
                price_info = range_map.get(product_id, {})

                price = float(row[3]) if row[3] is not None else 0.0
                cost = cost_map.get(product_id, 0.0)

                margin = round(((price - cost) / cost) * 100, 2) if cost > 0 and price > 0 else 0

                customer_price_data = {
                    "id": row[0],
                    "customer_id": row[1],
                    "product_id": row[2],
                    "price": row[3],
                    "product_name": row[4],
                    "sku": row[5],
                    "email": row[6],
                    "company": row[7],
                    "name": row[10],
                    "customer_group_id": row[11],
                    "customer_group_name": row[12],
                    "rep_name": row[13],
                    "rep_email": row[14],
                    "distributor": price_info.get("distributor"),
                    "vip": price_info.get("vip"),
                    "updated_at": convert_to_timestamp(row[15]),
                    "updated_by": row[16],
                    "cost": cost,
                    "margin": margin
                }
                if row[16] and row[16] != "Price Sheet":
                    updated_by_users.add(row[16])  # Collect unique usernames
                data.append(customer_price_data)
            # Fetch all usernames in one query (avoiding N+1 problem)
            if updated_by_users:
                user_data_map = user_db.fetch_users_by_usernames(updated_by_users)
                for item in data:
                    if item["updated_by"] != "Price Sheet":
                        item["updated_by"] = user_data_map.get(item["updated_by"], {}).get("name", "")
            paginated_data = new_utils.calculate_pagination(data, page, limit, total_count)
        return {"status": 200, "data": paginated_data}
    except Exception as e:
        logger.error(traceback.format_exc())
        return {"status": 422, "message": str(e)}

def _fetch_costs(store_id, sku_to_product_id):
    conn = new_pgdb.get_connection(store_id)
    try:
        if not sku_to_product_id:
            return {}
        
        query = """
            SELECT parent_sku, MIN(cost) 
            FROM skuvault_catalog 
            WHERE parent_sku IN :parent_skus
            GROUP BY parent_sku
        """
        result = conn.execute(
            text(query),
            {"parent_skus": tuple(sku_to_product_id.keys())}
        ).fetchall()

        cost_map = {}
        for parent_sku, min_cost in result:
            product_id = sku_to_product_id.get(parent_sku)
            if product_id:
                cost_map[product_id] = float(min_cost) if min_cost is not None else 0.0

        return cost_map

    except Exception:
        logger.error(traceback.format_exc())
        return {}
    finally:
        conn.close()


def add_customer_product_price(store_id, email, parent_sku, price, username):
    # Ensure price is valid
    try:
        price = float(price)  # Convert to float safely
    except ValueError:
        return {"status": 400, "message": "Invalid price value"}

    # Query to check if customer exists based on email
    query_check_exists = text("""
        SELECT 1 FROM product_customer_price WHERE parent_sku = :parent_sku AND email = :email
    """)

    query_check_customer = text("""
        SELECT customer_id FROM customers WHERE email = :email
    """)

    # Check the product based on SKU from the products table
    query_check_product = text("""
        SELECT product_id FROM products WHERE sku = :parent_sku
    """)

    query_insert = text("""
        INSERT INTO product_customer_price (product_id, parent_sku, email, customer_id, price, created_at, created_by, updated_at, updated_by)
        VALUES (:product_id, :parent_sku, :email, :customer_id, :price, NOW(), :created_by, NOW(), :updated_by)
    """)

    # Proper connection handling with "with" statement
    try:
        with new_pgdb.get_connection(store_id) as conn:
            # Check if product-customer price entry exists
            if conn.execute(query_check_exists, {"parent_sku": parent_sku, "email": email}).fetchone():
                return {"status": 409, "message": "The product was already added for the same customer."}

            # Check if customer exists based on email
            customer_result = conn.execute(query_check_customer, {"email": email}).fetchone()
            if not customer_result:
                return {"status": 409, "message": "email: Customer not found. Please enter a valid email."}

            customer_id = customer_result[0]  # Fetch the customer_id based on email

            # Get product_id from the products table based on parent_sku (sku in products table)
            product_result = conn.execute(query_check_product, {"parent_sku": parent_sku}).fetchone()
            if not product_result:
                return {"status": 409, "message": "parent_sku: Product not found. Please enter a valid parent_sku."}

            product_id = product_result[0]

            # Insert new product price for the customer
            conn.execute(query_insert, {
                "product_id": product_id,
                "parent_sku": parent_sku,
                "email": email,
                "customer_id": customer_id,  # Store customer_id as well
                "price": price,
                "created_by": username,
                "updated_by": username
            })
            conn.commit()

            data = {
                "product_id": product_id,
                "customer_id": customer_id,  # Include customer_id in the response data
                "price": price,
                "created_by": username
            }

            task.send_task(task.INSERT_CUSTOMER_PRICE_INTO_SHEET_TASK, args=(store_id, data))
            return {"status": 200, "message": "Product price for customer added successfully."}

    except Exception as e:
        return {"status": 422, "message": str(e)}
    

def update_customer_product_price(store_id, payload, customer_id, username):
    if not payload.get('product_id') or not payload.get('id'):
        return {"status": 400, "message": "product_id and id are required"}
    
    try:
        price = float(payload.get('price', 0))  # Ensure price is a float
    except ValueError:
        return {"status": 400, "message": "Invalid price value"}
    
    query = text("""
        UPDATE product_customer_price 
        SET price = :price, updated_by = :updated_by, updated_at = NOW() 
        WHERE id = :id AND customer_id = :customer_id AND product_id = :product_id
    """)

    # Using 'with' statement ensures proper resource cleanup
    try:
        with new_pgdb.get_connection(store_id) as conn:
            conn.execute(query, {
                "price": price,
                "id": payload["id"],
                "customer_id": customer_id,
                "product_id": payload["product_id"],
                "updated_by": username
            })
            conn.commit()
            data = {"product_id": payload["product_id"], "customer_id": customer_id, "price": price, "updated_by": username}
            task.send_task(task.UPDATE_CUSTOMER_PRICE_INTO_SHEET_TASK, args=(store_id, data))
        return {"status": 200, "message": "Product price for customer updated successfully."}
    except Exception as e:
        return {"status": 422, "message": str(e)}
    

def delete_customer_product_price(store_id, payload):
    # Ensure payload is valid
    if not payload:
        return {"status": 400, "message": "Invalid request. No product-customer IDs provided."}
    
    # Parse mapping IDs
    mapping_id_array = [int(id) for id in payload.split(",")]
    
    if not mapping_id_array:
        return {"status": 400, "message": "Expected 'id(s)' to delete mapping."}
    
    qhery_fetch = text("""
        SELECT product_id, customer_id FROM product_customer_price WHERE (id) IN :ids
    """)

    query_delete = text("""
        DELETE FROM product_customer_price 
        WHERE (id) IN :ids
    """)

    try:
        with new_pgdb.get_connection(store_id) as conn:
            result = conn.execute(qhery_fetch, {"ids": tuple(mapping_id_array)})
            
            conn.execute(query_delete, {"ids": tuple(mapping_id_array)})
            conn.commit()

            data = [{"product_id": row[0], "customer_id": row[1]} for row in result.fetchall()]
            task.send_task(task.DELETE_CUSTOMER_PRICE_INTO_SHEET_TASK, args=(store_id, data))
        return {"status": 200, "message": "Product price deleted successfully."}
    except Exception as e:
        return {"status": 422, "message": str(e)}