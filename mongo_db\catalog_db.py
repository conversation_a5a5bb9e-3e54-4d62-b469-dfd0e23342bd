import logging
import datetime
from bson import ObjectId
import mongo_db
from pymongo import TEXT

logger = logging.getLogger()

PRODUCTS_COLLECTION = "products"
BRANDS_COLLECTION = "brands"
CATEGORY_COLLECTION = "categories"
CATEGORY_TREE_COLLECTION = "category_tree"

def create_index(store):
    db = mongo_db.get_store_db_client(store)
    index = db[PRODUCTS_COLLECTION].create_index([('product_search_field', TEXT)], default_language='english')

def fetch_product_by_id(store, product_id):
    db = mongo_db.get_store_db_client(store)
    product = db[PRODUCTS_COLLECTION].find_one({"_id": int(product_id)})
    return product

def get_products_by_ids(store, ids):
    db = mongo_db.get_store_db_client(store)
    cur = db[PRODUCTS_COLLECTION].find({"id": {"$in": ids}}, {"id": 1, "images": 1, "name": 1, "options": 1, "variants": 1})
    
    document_list = list(cur)
    return document_list

def get_productsList_by_id(store, ids):
    db = mongo_db.get_store_db_client(store)
    cur = db[PRODUCTS_COLLECTION].find(
        {"id": {"$in": ids}},
        {
            "id": 1,
            "name": 1,
            "images": 1,
            "variants": 1  # Include the variants field
        }
    )

    document_list = []
    for doc in cur:
        product = {
            "id": doc.get("id"),
            "name": doc.get("name"),
            "images": doc.get("images", []),
            "variants": {}
        }
        # Map variant IDs to their purchasing_disabled flag
        for variant in doc.get("variants", []):
            product["variants"][variant["id"]] = {
                "purchasing_disabled": variant.get("purchasing_disabled", False),
                "calculated_price": variant.get("calculated_price", 0)
            }
        document_list.append(product)

    return document_list

def fetch_all_product_inventory(store):
    skus = {}
    db = mongo_db.get_store_db_client(store)
    cur = db[PRODUCTS_COLLECTION].find({})
    product_count = 0
    sku_count = 0
    
    for row in cur:
        sku = row['sku']
        inventory = row['inventory_level']
        skus[sku] = inventory
        product_count = product_count + 1
        variants = row['variants']
        for variant in variants:
            sku = variant['sku']
            inventory = variant['inventory_level']
            skus[sku] = inventory
            sku_count = sku_count + 1
    return skus

def fetch_all_brands(store):
    db = mongo_db.get_store_db_client(store)
    cur = db[BRANDS_COLLECTION].find({})
    brands = []
    for row in cur:
        brands.append(mongo_db.process_data(row))
    return brands

def fetch_all_categories(store):
    db = mongo_db.get_store_db_client(store)
    cur = db[CATEGORY_COLLECTION].find({})
    categories = []
    for row in cur:
        categories.append(mongo_db.process_data(row))
    return categories

def update_product_inventory(store, product_id, new_inventory):
    db = mongo_db.get_store_db_client(store)
    result = db[PRODUCTS_COLLECTION].update_one({"_id": int(product_id)},
                        { "$set": { 'inventory_level': new_inventory } })
    return result.acknowledged and result.raw_result['updatedExisting']
    
def update_sku_inventory(store, product_id, variant_id, new_inventory):
    db = mongo_db.get_store_db_client(store)
    result = db[PRODUCTS_COLLECTION].update_one({"_id": product_id},
                {"$set":{"variants.$[element].inventory_level": new_inventory}},
                array_filters=[{"element.id": variant_id}])
    return result.acknowledged and result.raw_result['updatedExisting']

def remove_product_by_id(store, product_id):
    db = mongo_db.get_store_db_client(store)
    doc = db[PRODUCTS_COLLECTION].delete_one({'_id': int(product_id)})