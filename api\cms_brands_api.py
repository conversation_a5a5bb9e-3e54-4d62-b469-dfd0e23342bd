from flask import request
import logging
import traceback
from api import APIResource
from utils import store_util, bc
from utils.common import parse_json
from cms import brand_service
from products.brands import brands_list, brands_purchaser_mapping
from products.categories import categories_list

logger = logging.getLogger()

class CmsBrands(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            query_params = request.args.to_dict()
            res = brands_list.get_brands_data(store, query_params)
            return res, 200
        finally:
            logger.debug("Exiting Cart GET")

    def get(self, tenant_id, store_id):
        return self.execute_store_request(request, self.get_executor)
    
class CmsSubcategory(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            query_params = request.args.to_dict()
            res = categories_list.addSubCategoryFlag(store, query_params, store_util.get_cdn_base_url(store))
            return res, 200
        finally:
            logger.debug("Exiting CMS category script GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class CmsCategoryChild(APIResource):
    def get_executor(self, request, token_payload, store,id):
        try:
            res = categories_list.get_child_data(store, id, store_util.get_cdn_base_url(store))
            return res, 200
        finally:
            logger.debug("Exiting category child GET")

    def get(self,id):
        return self.execute_store_request(request, self.get_executor,id)

class CreateCms(APIResource):
    def post_executor(self, request, token_payload, store, token=None):
        try:
            req_body = request.get_json(force=True)
            # validated_data = webpage_schema.validate(req_body)
            res = categories_list.create_list(req_body)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409
        finally:
            logger.debug("Exiting Dynamic Webpage POST")
 
    def post(self):
        return self.execute_store_request(request, self.post_executor)
 
class CmsBrandVersions(APIResource):
    def put_executor(self, request, token_payload, store, id):
        try:
            req_body = request.get_json(force=True)
            res = brands_list.set_version(store, req_body, id)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409
        finally:
            logger.debug("Exiting page PUT")

    def post_executor(self, request, token_payload, store, id):
        try:
            req_body = request.get_json(force=True)
            if req_body and 'is_customers_only' in req_body:
                res = brands_list.update_customer_only_flag(store, req_body, id)

                if res['status'] == 200:
                    return {"status": res['message']}, 200
                else:
                    return {"message": "Something went wrong at server!!"}, 500
            else:
                return {"message": "Please esure that your request body is correct."}, 400
        finally:
            logger.debug("Exiting CMS Category POST")

    def get_executor(self, request, token_payload, store, id):
        try:
            res = brands_list.get_brand(store, id)
            return res, 200
        finally:
            logger.debug("Exiting Webpage GET")

    def put(self, id):
        return self.execute_store_request(request, self.put_executor, id)
    
    def post(self, id):
        return self.execute_store_request(request, self.post_executor, id)
    
    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id)

class CmsBrandsVersionsOperation(APIResource):
    def get_executor(self, request, token_payload, store, id):
        try:
            res = brands_list.get_cms_versions(store, id)
            return res, 200
        finally:
            logger.debug("Exiting page GET")

    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id)
    
class CmsBrandVersionData(APIResource):
    def get_executor(self, request, token_payload, store, id):
        try:
            query_params = request.args.to_dict()
            res = brands_list.get_cms_versionData(store, query_params, id)
            return res, 200
        finally:
            logger.debug("Exiting page GET")

    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id)
    
# store and get images to server
class CmsBrandImageOperation(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            query_params = request.args.to_dict()
            res = brands_list.getImage(query_params)
            return res
        finally:
            logger.debug("Exiting CMS app builder category image GET")

    def post_executor(self, request, token_payload, store):
        try:
            req_body = request.files
            res = brands_list.setImage(req_body)

            if res['status'] == 200:
                return {"status": res['message']}, 200
            else:
                return {"status": res['message']}, 500
        finally:
            logger.debug("Exiting CMS app builder category Image Upload POST")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
class BCBrands(APIResource):
    def post_executor(self, request, token_payload, store, token=None):
        try:
            req_body = request.get_json(force=True)
            if 'created_by' in req_body:
                # STEP 1 - FETCH BC BRANDS
                res = bc.get_bc_brands(store_util.get_bc_api_creds(store))      
                bc_brand_arr=[]          

                # STEP 2 - ITERATE OVER BC BRANDS AND CREATE PAGES INTO THE DB
                for brand in res: 
                    bc_brand_arr.append(brand['id'])                   
                    brands_list.create_bc_brands(brand, req_body,store)
                brands_list.check_all_brands(bc_brand_arr, store)
                return {"message": "success"}, 200
            else:
                return {"message": "Please esure that your request body is correct."}, 400
        finally:
            logger.debug("Exiting Brands POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor) 
    
class BrandPurchaserMapping(APIResource):
    def post_executor(self, request, token_payload, store, token=None):
        logger.debug("Entering Brand Purchaser Mapping POST")
        try:
            payload = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':   
                res = brands_purchaser_mapping.create_brands_purchaser_mapping(store['id'], payload, username)
                if res['status'] == 200:
                    return {'message': res['message']}, 200
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Brand Purchaser Mapping POST")

    def get_executor(self, request, token_payload, store, token=None):
        logger.debug("Entering Brand Purchaser Mapping GET")
        try:
            req_body = request.args.to_dict()
            search = req_body.get('search', '').strip()
            sort_by = req_body.get('sort_by', '').strip()
            page = int(req_body.get('page', 1))
            limit = int(req_body.get('limit', 10))
            sort_array = sort_by.split("/") if sort_by != '' else []
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':   
                res = brands_purchaser_mapping.get_brands_purchaser_mapping(store['id'], search, page, limit, sort_array)
                if res['status'] == 200:
                    return res['data'], res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Brand Purchaser Mapping GET")

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)

class BrandPurchaserMappingDetails(APIResource):
    def get_executor(self, request, token_payload, store, email_id):
        logger.debug("Entering Brand Purchaser Mapping GET")
        try:
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':   
                res = brands_purchaser_mapping.get_brands_purchaser_mapping_details(store['id'], email_id)
                if res['status'] == 200:
                    return res['data'], res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Brand Purchaser Mapping Details GET")

    def delete_executor(self, request, token_payload, store, email_id):
        logger.debug("Entering Brand Purchaser Mapping DELETE")
        try:
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                res = brands_purchaser_mapping.delete_brands_purchaser_mapping(store['id'], email_id)
                if res['status'] == 200:
                    return {'message': res['message']}, 200
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Brand Purchaser Mapping DELETE")

    def patch_executor(self, request, token_payload, store, email_id):
        logger.debug("Entering Brand Purchaser Mapping PATCH")
        try:
            payload = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                res = brands_purchaser_mapping.update_brands_purchaser_mapping(store['id'], email_id, payload, username)
                if res['status'] == 200:
                    return {'message': res['message']}, 200
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Brand Purchaser Mapping PATCH")
    
    def get(self, email_id):
        return self.execute_store_request(request, self.get_executor, email_id)
    
    def delete(self, email_id):
        return self.execute_store_request(request, self.delete_executor, email_id)
    
    def patch(self, email_id):
        return self.execute_store_request(request, self.patch_executor, email_id)

class BrandPurchaserMappingPurchaserDropdown(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Brand Purchaser Mapping GET")
        try:
            query_params = request.args.to_dict()
            search = query_params.get('search', '').strip()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':   
                res = brands_purchaser_mapping.get_brands_purchaser_mapping_purchaser_dropdown(store['id'], search, page, limit)
                if res['status'] == 200:
                    return res['data'], res['status']
                else:
                    return {'data': res['data']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Brand Purchaser Mapping Dropdowns GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class BrandPurchaserMappingBrandDropdown(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Brand Purchaser Mapping GET")
        try:
            query_params = request.args.to_dict()
            search = query_params.get('search', '').strip()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            purchaser_email = query_params.get('purchaser_email', '').strip()
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']
            if username != '':
                res = brands_purchaser_mapping.get_brands_purchaser_mapping_brand_dropdown(store['id'], search, page, limit, purchaser_email)
                if res['status'] == 200:
                    return res['data'], res['status']
                else:
                    return {'data': res['data']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Brand Purchaser Mapping Brand Dropdowns GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
                        

