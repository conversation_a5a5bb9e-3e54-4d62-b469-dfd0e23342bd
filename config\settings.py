import os


class BaseConfig():
    HOST = '0.0.0.0'
    PORT = 8080
    API_PREFIX = ''
    TESTING = False
    DEBUG = False
    ALLOWED_ORIGIN = os.environ.get("ALLOWED_ORIGIN", None)
    SECRET_KEY = '9d398b6ed032b813d45bf24f48ee3fb7'
    PROFILE = "stage"
    TENANT_DB_NAME = "tenant_db"
    PG_CONN_STRING = '************************************************************************'
    MONGO_CONN_STRING = 'mongodb://**************:27017/'       
    ADMIN_PANEL_URL = os.environ.get("ADMIN_PANEL_URL", "http://localhost")
    DEFAULT_ADMIN_USER = os.environ.get("DEFAULT_ADMIN_USER", "admin")
    DEFAULT_ADMIN_SECRET = os.environ.get("DEFAULT_ADMIN_SECRET", "admin@123")
    DEFAULT_ADMIN_ROLE = os.environ.get("DEFAULT_ADMIN_ROLE", "owner")
    CELERY_BROKER_URL = 'redis://localhost:6379/5'
    CELERY_RESULT_BACKEND = 'redis://localhost:6379/5'
    ADMIN_DB_NAME = 'midwestgoods_admin'
    MONGO_MAX_POOL_SIZE = 200
    REDIS_HOST = 'localhost'
    REDIS_PORT = 6379
    REDIS_DB = 2
    CELERY_APP_NAME = "ad_app"

class DevConfig(BaseConfig):
    FLASK_ENV = 'development'
    API_PREFIX = '/admin/api'
    DEBUG = True
    PORT = 9090

class ProductionConfig(BaseConfig):
    FLASK_ENV = 'production'
    PROFILE = os.getenv('PROFILE', 'prod')
    API_PREFIX = os.environ.get("API_PREFIX", "/admin/api")
    CELERY_APP_NAME = os.environ.get("CELERY_APP_NAME", "ad_app")
    MONGO_CONN_STRING = os.getenv('MONGO_CONN_STRING', 'mongodb://ad-mongodb:27017')
    MONGO_MAX_POOL_SIZE = os.getenv('MONGO_MAX_POOL_SIZE', 200)
    REDIS_HOST = os.getenv('REDIS_HOST', 'ad-redis')
    REDIS_PORT = os.getenv('REDIS_PORT', 6379)
    REDIS_DB = os.getenv('REDIS_DB', 2)
    CELERY_BROKER_URL = os.getenv(
        'CELERY_BROKER_URL', f"redis://{REDIS_HOST}:{REDIS_PORT}/5")
    CELERY_RESULT_BACKEND = os.getenv(
        'CELERY_RESULT_BACKEND', f"redis://{REDIS_HOST}:{REDIS_PORT}/5")
    PG_CONN_STRING = os.getenv(
        'PG_CONN_STRING', '*********************************************************')
    ALLOWED_ORIGIN = os.getenv('ALLOWED_ORIGIN', None)
    SECRET_KEY = os.getenv('SECRET_KEY', '9d398b6ed032b813d45bf24f48ee3fb7')
    ADMIN_PANEL_URL = os.getenv('ADMIN_PANEL_URL', 'https://adminapp.midwestgoods.com')
    DEFAULT_ADMIN_USER = os.getenv('DEFAULT_ADMIN_USER', 'admin')
    DEFAULT_ADMIN_SECRET = os.getenv('DEFAULT_ADMIN_SECRET', 'admin@123')
    DEFAULT_ADMIN_ROLE = os.getenv('DEFAULT_ADMIN_ROLE', 'owner')
    ADMIN_DB_NAME = os.getenv('ADMIN_DB_NAME', 'admin')


class TestConfig(BaseConfig):
    FLASK_ENV = 'development'
    TESTING = True
    DEBUG = True
