import new_utils
from new_mongodb import get_store_db_client_for_store_id
from new_mongodb import StoreDBCollections
from fields.products import product_limit_report_fields


def get_product_limit_report(store_id, payload):
    sort_by = payload.get("sort_by", "")

    # Extract field and sort order from sort_by
    if '/' in sort_by:
        field, order = sort_by.split('/')
        sort_order = 'asc' if order == '1' else 'desc'
    else:
        # Default sort order if format is not correct
        field = 'date_created'
        sort_order = 'desc'

    payload['sort_by'] = field
    payload['sort_order'] = sort_order
    additional_query = {"$and": [{"$or":[{"order_quantity_minimum": {"$gt": 0}},{"order_quantity_maximum": {"$gt": 0}}]}], "is_visible": True}
    #additional_query = {"$and": [{"$or":[{"order_quantity_minimum": {"$gt": 0}},{"order_quantity_maximum": {"$gt": 0}}]}]}
    payload['filterBy'] = ['name', 'sku']
    db = get_store_db_client_for_store_id(store_id)
    products, total_data_length, page, limit = new_utils.get_paginated_records_updated(db, StoreDBCollections.PRODUCTS, payload, product_limit_report_fields, additional_query)
    data = new_utils.calculate_pagination(products, page, limit, total_data_length)

    return data