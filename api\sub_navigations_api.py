from flask import request
import logging
import traceback
from api import APIResource
from navigations import sub_navigation_service

logger = logging.getLogger()

class SubNavigation(APIResource):
    def get_executor(self, request, token_payload, store, navigation_id):
        logger.debug("Entering Sub Navigations GET")
        try:
            store_id = store['id']
            res = sub_navigation_service.get_sub_nav(store_id, navigation_id)
            return res, 200
        finally:
            logger.debug("Exiting Sub Navigations GET")
    
    def put_executor(self, request, token_payload, store, navigation_id):
        try:
            req_body = request.get_json(force=True)
            store_id = store['id']
            res = sub_navigation_service.update_sub_nav(store_id, req_body, navigation_id)
            
            if res:
                return {"status": "ok"}, 200
            else:
                return {"message": "Please ensure that your request body schema is correct"}, 400
        
        finally:
            logger.debug("Exiting Sub Navigation PUT")

    def get(self, navigation_id):
        return self.execute_store_request(request, self.get_executor, navigation_id)
    
    def put(self, navigation_id):
        return self.execute_store_request(request, self.put_executor, navigation_id)

class GetSubNavigationByShortCode(APIResource):
    def get_executor(self, request, token_payload, store, short_code):
        logger.debug("Entering Navigations GET")
        try:
            navigation = sub_navigation_service.get_navigation_by_short_code(store['id'],short_code)
            return navigation, 200
        finally:
            logger.debug("Exiting Navigations GET")

    def get(self, tenant_id, store_id, short_code):
        return self.execute_store_request(request, self.get_executor, short_code)
