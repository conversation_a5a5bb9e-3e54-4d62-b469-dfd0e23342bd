from datetime import datetime
from sqlalchemy import text
import new_pgdb
import new_utils
from new_mongodb import customer_db
from utils import tag_util
from utils.common import calculatePaginationData, parse_json, convert_to_timestamp
from datetime import timezone
import logging

logger = logging.getLogger()

def get_customer_returns_data(store_id, email_id, username, page=1, limit=10):
    response = {
        'status': 400
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        # Calculate offset for pagination
        offset = (page - 1) * limit
        
        # SQL query to fetch customer returns data with product-level totals and pagination
        query = """
        WITH product_totals AS (
            SELECT 
                z.parent_sku,
                rp.product_title AS parent_product_title,
                SUM(z.order_quantity) AS total_product_returned_quantity
            FROM 
                zoho_customer_returns_data z
            LEFT JOIN 
                replenishment_products rp ON z.parent_sku = rp.parent_sku
            WHERE 
                z.customer_email = :email_id
            GROUP BY 
                z.parent_sku,
                rp.product_title
        ),
        paginated_products AS (
            SELECT 
                parent_sku,
                parent_product_title,
                total_product_returned_quantity
            FROM product_totals
            ORDER BY total_product_returned_quantity DESC
            LIMIT :limit OFFSET :offset
        )
        SELECT 
            z.variant_sku,
            z.parent_sku,
            rv.product_title AS variant_product_title,
            pp.parent_product_title,
            SUM(z.order_quantity) AS total_returned_quantity,
            pp.total_product_returned_quantity
        FROM 
            zoho_customer_returns_data z
        LEFT JOIN 
            replenishment_variants rv ON z.variant_sku = rv.sku
        INNER JOIN 
            paginated_products pp ON z.parent_sku = pp.parent_sku
        WHERE 
            z.customer_email = :email_id
        GROUP BY 
            z.variant_sku,
            z.parent_sku,
            rv.product_title,
            pp.parent_product_title,
            pp.total_product_returned_quantity
        ORDER BY 
            pp.total_product_returned_quantity DESC,
            total_returned_quantity DESC
        """
        
        # Execute the query
        result = conn.execute(text(query), {
            'email_id': email_id,
            'limit': limit,
            'offset': offset
        })
        rows = result.fetchall()
        
        # Get total count for pagination info
        count_query = """
        SELECT COUNT(DISTINCT z.parent_sku) as total_count
        FROM zoho_customer_returns_data z
        WHERE z.customer_email = :email_id
        """
        count_result = conn.execute(text(count_query), {'email_id': email_id})
        total_count = count_result.fetchone()[0]
        
        # Structure the data with parent data containing child data arrays
        parent_data = {}
        response_data = []
        
        for row in rows:
            variant_sku = row[0]
            parent_sku = row[1]
            variant_product_title = row[2]
            parent_product_title = row[3]
            total_returned_quantity = row[4]
            total_product_returned_quantity = row[5]
            
            # Create child data object
            child_data = {
                'variant_sku': variant_sku,
                'variant_product_title': variant_product_title,
                'total_returned_quantity': total_returned_quantity
            }
            
            # If parent_sku exists, group under parent
            if parent_sku:
                if parent_sku not in parent_data:
                    parent_data[parent_sku] = {
                        'parent_sku': parent_sku,
                        'parent_product_title': parent_product_title,
                        'total_product_returned_quantity': total_product_returned_quantity,
                        'child_data': []
                    }
                parent_data[parent_sku]['child_data'].append(child_data)
            else:
                # If no parent_sku, create a standalone entry
                if variant_sku not in parent_data:
                    parent_data[variant_sku] = {
                        'parent_sku': variant_sku,
                        'parent_product_title': variant_product_title,
                        'total_product_returned_quantity': total_returned_quantity,
                        'child_data': []
                    }
                parent_data[variant_sku]['child_data'].append(child_data)
        
        # Convert to list format for response
        response_data = list(parent_data.values())
        
        # Calculate pagination info
        data = calculatePaginationData(response_data, page, limit, total_count)
        
        response['status'] = 200
        response['data'] = data
        
    except Exception as e:
        logger.error(f"Error fetching customer returns data: {str(e)}")
        response['message'] = f"Error fetching customer returns data: {str(e)}"
    finally:
        conn.close()
    return response