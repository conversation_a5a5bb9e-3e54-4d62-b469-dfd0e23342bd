import requests
from flask import request
from mongo_db import builds_db
import logging
import psycopg2
import json
from datetime import datetime, timedelta
import new_pgdb
from utils.common import paginate_data_postgres, parse_json, calculatePaginationData, convert_to_timestamp
from pg_db import get_connection, run_query
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import traceback
import math
from utils import bc
from psycopg2.errors import UniqueViolation

logger = logging.getLogger()

def create_rule(store, data):
    response = {"status": 400, "message":"Something went wrong."}

    variant_sku = data.get("variant_sku", None)

    if not variant_sku:
        response['message'] = "Variant SKU is missing or invalid."
        return response
    
    with new_pgdb.get_connection(store['id']) as conn:
        try:
            out_of_stock_days = data.get('out_of_stock_days', 1)
            hide_product_name_prefix = data.get('hide_product_name_prefix')
            hide_classification = data.get('hide_classification','').strip() 
            is_active = True
            is_executed = False
            executed_at = None
            last_executed_at = None
            created_at = datetime.now()
            created_by = data.get('created_by', None)
            modified_at = None
            modified_by = None
            product_id = None

            get_data_query = """SELECT product_id, variants_id, variants_sku, product_name FROM variants WHERE variants_sku = :variant_sku;"""
            result = conn.execute(text(get_data_query).bindparams(variant_sku=variant_sku))

            if not result.rowcount:
                response["status"] = 404
                response["message"] = f"Variant SKU {variant_sku} doesn't exist."
                return response

            for columns in result:
                product_id, variants_id, variants_sku, product_name = columns

            insert_query = "INSERT INTO variants_visibility_rules (variant_sku, variant_id, product_id, product_name, out_of_stock_days, hide_classification, hide_product_name_prefix, is_active, is_executed, executed_at, last_executed_at, created_at, created_by, modified_at, modified_by) VALUES (:variant_sku, :variant_id, :product_id, :product_name, :out_of_stock_days, :hide_classification, :hide_product_name_prefix, :is_active, :is_executed, :executed_at, :last_executed_at, :created_at, :created_by, :modified_at, :modified_by);"
            conn.execute(text(insert_query).bindparams(variant_sku=variant_sku, variant_id=variants_id, product_id=product_id, product_name=product_name,  out_of_stock_days=out_of_stock_days, hide_classification=hide_classification, hide_product_name_prefix=hide_product_name_prefix, is_active=is_active, is_executed=is_executed, executed_at=executed_at, last_executed_at=last_executed_at, created_at=created_at, created_by=created_by, modified_at=modified_at, modified_by=modified_by))

            conn.commit()
            response["status"] = 200
            response["message"] = "Rule has been added successfully."
  
        except IntegrityError as e:
            if isinstance(e.orig, UniqueViolation):
                response['status'] = 422
                response['message'] = "Rule with the given vairant id is already exist."
            else:
                error_message = str(e)
                response['status'] = 422
                response['message'] = error_message
    return response



def get_rules(store, body):
    response = {"status": 400,}

    page = int(body.get("page", 1))
    limit = int(body.get("limit", 20))
    sort_by = body.get("sort_by", "created_at")
    search_keyword = body.get("search", None)
    sort_order = int(body.get("sort_order", -1))
   
    with new_pgdb.get_connection(store['id']) as conn:
        try:
            offset = (page - 1) * limit
            sort_order_str = "DESC" if sort_order == -1 else "ASC"            
            order_by_clause = f"ORDER BY {sort_by} {sort_order_str}" if sort_by else ""            
            where_clause = f"WHERE product_name ILIKE '%{search_keyword}%'" if search_keyword else ""
            
            total_records_query = f"SELECT COUNT(*) FROM variants_visibility_rules {where_clause}"                                    
            total_records_result = conn.execute(text(total_records_query))

            total_records_result = int(total_records_result.scalar())             
            
            get_data_query = f"SELECT * FROM variants_visibility_rules {where_clause} {order_by_clause} LIMIT {limit} OFFSET {offset}"            
            res = conn.execute(text(get_data_query))            
            
            data = []
            for row in res:               
                a = {
                        "variant_sku": row[0],
                        "variant_id": row[1],
                        "product_id": row[2],                                          
                        "product_name": row[3],
                        "out_of_stock_days": row[4],
                        "hide_classification": row[5],
                        "hide_product_name_prefix": row[6],
                        "is_active": row[7],
                        "is_executed": row[8],
                        # "executed_at": row[9].strftime("%Y-%m-%d %H:%M:%S") if isinstance(row[9], datetime) else str(row[9]),
                        # "last_executed_at": row[10].strftime("%Y-%m-%d %H:%M:%S") if isinstance(row[10], datetime) else str(row[10]),
                        # "created_at": row[11].strftime("%Y-%m-%d %H:%M:%S") if isinstance(row[11], datetime) else str(row[11]),
                        # "modified_at": row[12].strftime("%Y-%m-%d %H:%M:%S") if isinstance(row[12], datetime) else str(row[12]),
                        "executed_at": convert_to_timestamp(row[9]),
                        "last_executed_at": convert_to_timestamp(row[10]),
                        "created_at": convert_to_timestamp(row[11]),
                        "modified_at": convert_to_timestamp(row[12]),
                        "created_by": row[13],                    
                        "modified_by": row[14]                    
                    }      
                data.append(a)    
                                                     
            paginated_rows, current_page, total_pages, total_items = \
                paginate_data_postgres(total_records_result, data, page, limit)            

            pagination_data = calculatePaginationData(paginated_rows, current_page, limit, total_items)
            
            response['status'] = 200
            response['data'] = pagination_data

        except SQLAlchemyError as e:
                error_message = str(e)
                response['status'] = 422
                response['message'] = error_message
    return response


def update_rule(store, req_body):
    response = {"status": 400,}
    with new_pgdb.get_connection(store['id']) as conn:
        try:
            variant_sku = req_body.get('variant_sku', None)
            out_of_stock_days = req_body.get('out_of_stock_days')
            hide_product_name_prefix = req_body.get('hide_product_name_prefix')
            hide_classification = req_body.get('hide_classification','').strip()
            modified_by = req_body.get('modified_by')
            modified_at = datetime.now()

            check_query = "SELECT * FROM variants_visibility_rules WHERE variant_sku = :variant_sku;"
            res = conn.execute(text(check_query).bindparams(variant_sku=variant_sku))

            if not res.rowcount:
                response["status"] = 404
                response["message"] = f"Variant SKU {variant_sku} not found in the database."
                return response

            update_query = "UPDATE variants_visibility_rules SET modified_by = :modified_by, modified_at = :modified_at, out_of_stock_days = :out_of_stock_days, hide_classification = :hide_classification, hide_product_name_prefix = :hide_product_name_prefix  WHERE variant_sku = :variant_sku;"
            conn.execute(text(update_query).bindparams(variant_sku=variant_sku, modified_by=modified_by, modified_at=modified_at, out_of_stock_days=out_of_stock_days, hide_classification=hide_classification, hide_product_name_prefix=hide_product_name_prefix))  
            conn.commit()
                        
            response["status"] = 200            
            response["message"] = "Rule has been updated successfully."
                        
        except SQLAlchemyError as e:
                error_message = str(e)
                response['status'] = 422
                response['message'] = error_message
    return response    


def delete_rules(store, variant_sku):
    response ={"status" : 400}
    with new_pgdb.get_connection(store['id']) as conn:
        try:
            check_query = "SELECT COUNT(*) FROM variants_visibility_rules WHERE variant_sku = :variant_sku;"
            count_result = conn.execute(text(check_query).bindparams(variant_sku=variant_sku)).scalar()

            if count_result > 0:
                delete_query = "DELETE FROM variants_visibility_rules WHERE variant_sku = :variant_sku;"
                conn.execute(text(delete_query).bindparams(variant_sku=variant_sku))
                conn.commit()
                response['status'] = 200
                response['message'] = "Rule has been deleted successfully"

            else:
                 response['status'] = 404
                 response['message'] = "Variant Rule doens't exist."
                
        except SQLAlchemyError as e:
                error_message = str(e)
                response['status'] = 422
                response['message'] = error_message

    return response


