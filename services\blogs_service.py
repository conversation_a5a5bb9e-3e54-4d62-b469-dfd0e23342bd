from services import Service
from bson import ObjectId
from datetime import datetime
from flask import send_file, make_response
import os
from werkzeug.utils import secure_filename
import logging
from fields.blogs_fields import blog_fields
from utils.common import calculatePaginationData, parse_json
import mongo_db

logger = logging.getLogger()

ALLOWED_EXTENSIONS = set(['png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov', 'wmv', 'mkv'])


def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def change_file_name(filename):
    rendom = str(round(datetime.utcnow().timestamp()))
    if ('.' in filename):
        ext = filename.rsplit('.', 1)[1].lower()
        fname = rendom + '.' + ext
        return fname


class Blogs(Service):
    def get_blogs(self, body, db_name):
        result = []
        ndb = []
        db = mongo_db.get_db_client(db_name)
        collection = db['blogs_author']
        categoryCollection = db['blogs_category']
        body['filterBy'] = ['name']

        blogs, total_data_length, page, limit = super(
        ).get_paginated_records_updated(body, blog_fields, {})

        count = 0
        for blog in blogs:
            res = {}
            blogData = {}
            author = {}
            category = {}
            authorName = ''
            categoryName = ''
            if blog['author_details']:
                author = collection.find_one(
                    {'_id': ObjectId(str(blog['author_details']))})
            if blog['category_details']:
                category = categoryCollection.find_one(
                    {'_id': ObjectId(str(blog['category_details']))})

            if author and category:
                authorName = author['name']
                categoryName = category['name']

            blogData['image_url'] = blog['image_url'] 
            blogData['content'] = blog['content']
            blogData['short_desc'] = blog['short_desc']
            blogData['author_details'] = authorName
            blogData['category_details'] = categoryName
            blogData['seo_details'] = blog['seo_details']
            blogData['tags'] = blog['tags']

            res['id'] = blog['id']
            res['index'] = count
            res['name'] = blog['name']
            res['description'] = blog['description']
            res['created_at'] = blog['created_at']
            res['updated_at'] = blog['updated_at']
            res['url'] = blog['url']
            res['status'] = blog['status']
            res['data'] = blogData

            related_blogs = super().get_related_blogs(
                blog['tags'], blog['id'], db)
            related_blogs_data = []
            for related_blog in related_blogs:
                related_blog_data = {
                    'id': related_blog['id'],
                    'name': related_blog['name'],
                    'description': related_blog['description'],
                    'content': related_blog['content'],
                    'short_desc':  related_blog['short_desc'],
                    'url': related_blog['url'],
                    'image_url': related_blog['image_url']
                }
                related_blogs_data.append(related_blog_data)

            res['related_blogs'] = related_blogs_data
            count = count + 1
            result.append(res)

        data = calculatePaginationData(
            result, page, limit, total_data_length)
        return data

    def create_blog(self, body):
        response = {
            "status": 400
        }

        data = {
            "data": ''
        }
        content = {
            "config": data
        }
        url = body['name'].strip()
        # url = '/pages/' + url.replace(" ", "_").lower()
        url = '/' + url.replace(" ", "-").lower()
        seo_details = {
            "blog_name": body['name'].strip(),
            "blog_url": url,
            "meta_title": "",
            "meta_keywords": "",
            "meta_description": "",
        }
        isUniqueName = self.checkForUniqueBlogName(body['name'], '')
        if isUniqueName:
            body["created_at"] = int(datetime.utcnow().timestamp())
            body["updated_at"] = ""
            body["url"] = url
            body['image_url'] = ""
            body['content'] = [content]
            body['short_desc'] = ""
            body["author_details"] = ""
            body["category_details"] = ""
            body['tags'] = []
            body['seo_details'] = [seo_details]

            body['name'] = body['name'].strip()
            id = super().create(body)
            response['message'] = "Blog created successfully"
            response['status'] = 200
        else:
            response['message'] = "The provided blog name has already been matched with other blogs. Please provide a different blog name."
            response['status'] = 409

        return response

    def create_bc_blog(self, blog, db_name):
        authorID = ''
        db = mongo_db.get_db_client(db_name)
        collection = db['blogs_author']
        blogsCollection = db['blogs']
        if str(blog['author']) != '':
            author = collection.find_one(
                {'name': str(blog['author'])})
            if author:
                authorID = author['_id']
            else:
                new_author = {
                    "name": str(blog['author']),
                    "description": "",
                    "created_at": int(datetime.utcnow().timestamp()),
                    "updated_at": "",
                    "status": "active"
                }
                result = collection.insert_one(new_author)
                authorID = result.inserted_id
        else:
            author = collection.find_one(
                {'name': 'Author'})
            authorID = author['_id']

        body = {}
        seo_details = {
            "blog_name": blog['title'],
            "blog_url": blog['url'],
            "meta_title": blog['title'],
            "meta_keywords": blog['meta_keywords'],
            "meta_description": blog['meta_description'],
        }
        data = {
            "data": blog['body']
        }
        content = {
            "config": data
        }
        timestamp = 0
        dt = datetime.strptime(
            blog['published_date']['date'], '%Y-%m-%d %H:%M:%S.%f')
        timestamp = int(dt.timestamp())

        body['name'] = blog['title']
        body['description'] = ''
        body['created_at'] = timestamp
        body['updated_at'] = ''
        body['url'] = blog['url']
        body['image_url'] = blog['thumbnail_path']
        body['short_desc'] = blog['summary']
        body['author_details'] = str(authorID)
        body['category_details'] = ''
        body['tags'] = blog['tags']
        body['seo_details'] = [seo_details]
        body['content'] = [content]
        body['status'] = "active"

        db_blog = super().find_one({"name": str(blog['title'])})

        # If Blog not exists then create
        if not db_blog:
            return blogsCollection.insert_one(body).inserted_id

        if db_blog and db_blog['updated_at'] == '':
            body['updated_at'] = int(datetime.utcnow().timestamp())
            return blogsCollection.update_one({"_id": ObjectId(str(db_blog['id']))}, {"$set": body})

    def update_blog(self, body, blog_id=None):
        response = {
            "status": 400
        }
        old_blog = super().find_one({"_id": ObjectId(str(blog_id))})
        seo_details = []
        if old_blog:
            seo_details = old_blog['seo_details']
        url = body['name'].strip()
        url = '/' + url.replace(" ", "-").lower()
        seo_details[0]['blog_name'] = body['name'].strip()
        if body['status_update'] == 'false':
            isUniqueName = self.checkForUniqueBlogName(body['name'], blog_id)
        else:
            isUniqueName = True

        if isUniqueName:
            id = super().update_one({"_id": ObjectId(str(blog_id))}, {"$set":
                                                                      {
                                                                          "name": body['name'].strip(),
                                                                          "description": body['description'],
                                                                          "status": body['status'],
                                                                          "seo_details": seo_details,
                                                                          "updated_at":  int(datetime.utcnow().timestamp())
                                                                      }
                                                                      })
            response['message'] = "Blog Updated successfully"
            response['status'] = 200
        else:
            response['message'] = "The provided blog name has already been matched with other blogs. Please provide a different blog name."
            response['status'] = 409

        return response

    def delete_by_id(self, blog_id):
        return super().delete({"_id": ObjectId(str(blog_id))})

    def checkForUniqueBlogName(self, name, blog_id):
        url = name.strip()
        url = '/' + url.replace(" ", "-").lower()
        blogs = super().find_all()
        blogs = parse_json(self.processList(blogs))
        for blog in blogs:
            if not blog['id'] == blog_id:
                if blog['url'] == url:
                    return False
        return True


class BlogData(Service):
    def set_data(self, body, blog_id=None):
        response = {
            "status": 400
        }
        new_page_url = body["seo_details"][0]["blog_url"].strip()
        if new_page_url.startswith('/'):
            new_page_url = new_page_url[1:]

        if not new_page_url == '':
            new_page_url = '/' + new_page_url
            isUrlUnique = self.checkForUniqueUrl(new_page_url, blog_id)
            if isUrlUnique:
                new_name = body["seo_details"][0]["blog_name"]
                if not new_name == '':
                    isNameUnique = self.checkForUniqueName(
                        new_name.strip(), blog_id)
                    if isNameUnique:
                        seo_details = body['seo_details']
                        # Update blog_url value
                        seo_details[0]["blog_url"] = new_page_url
                        blog = super().find_one(
                            {"_id": ObjectId(str(blog_id))})

                        id = super().update_one({"_id": ObjectId(str(blog_id))}, {"$set":
                                                                                  {
                                                                                      "url": new_page_url,
                                                                                      "name": new_name.strip(),
                                                                                      "image_url": body['image_url'],
                                                                                      "seo_details": seo_details,
                                                                                      "author_details": body['author_details'],
                                                                                      "category_details": body['category_details'],
                                                                                      "short_desc": body['short_desc'],
                                                                                      "content": body['content'],
                                                                                      "tags": body['tags'],
                                                                                      "updated_at":  int(datetime.utcnow().timestamp())
                                                                                  }})
                        response['message'] = "changes saved sucessfuly"
                        response['status'] = 200
                    else:
                        response['message'] = "The provided blog name has already been matched with other blogs. Please provide a different blog Name."
                        response['status'] = 409
                else:
                    response['message'] = "Blog name is required."
                    response['status'] = 409
            else:
                response['message'] = "The provided blog URL has already been matched with other blogs. Please provide a different blog URL."
                response['status'] = 409
        else:
            response['message'] = "Please provide blog url."
            response['status'] = 409

        return response

    def get_blog(self, blog_id=None):
        result = {}
        blogData = {}
        blog = super().find_one({"_id": ObjectId(str(blog_id))})

        blogData['author_details'] = blog['author_details']
        blogData['category_details'] = blog['category_details']
        blogData['seo_details'] = blog['seo_details']
        blogData['content'] = blog['content']
        blogData['short_desc'] = blog['short_desc']
        blogData['tags'] = blog['tags']
        blogData['image_url'] = blog['image_url']

        result['id'] = blog['id']
        result['name'] = blog['name']
        result['description'] = blog['description']
        result['created_at'] = blog['created_at']
        result['updated_at'] = blog['updated_at']
        result['url'] = blog['url']
        result['status'] = blog['status']
        result['data'] = blogData

        return result

    def checkForUniqueUrl(self, url, blog_id):
        blogs = super().find_all()
        blogs = parse_json(self.processList(blogs))
        for blog in blogs:
            if not blog['id'] == blog_id:
                if blog['url'].strip() == url.strip():
                    return False
        return True

    def checkForUniqueName(self, name, blog_id):
        name = name.replace(" ", "").lower()
        blogs = super().find_all()
        blogs = parse_json(self.processList(blogs))
        for blog in blogs:
            if not blog['id'] == blog_id:
                if blog['name'].replace(" ", "").lower().strip() == name:
                    return False
        return True


class SetImages():
    def setImage(self, body):
        response = {
            "status": 400
        }

        if not os.path.exists('images/blogs/images'):
            os.makedirs('images/blogs/images')

        file = body['image']
        UPLOAD_FOLDER = os.path.join('images/blogs/images')

        if (file.filename == ''):
            response['message'] = "No image selected for uploading"
            response['status'] = 500

        if (file and allowed_file(file.filename)):
            newName = change_file_name(file.filename)
            fname = secure_filename(newName)
            file.save(os.path.join(UPLOAD_FOLDER, fname))
            base_path = os.path.join(os.path.abspath(
                os.getcwd()), UPLOAD_FOLDER, newName)
            
            if '/app/images' in base_path:
                base_path = base_path.replace('/app/images', '')  
            
            response['message'] = base_path
            response['status'] = 200
        else:
            response['message'] = "Allowed image types are -> png, jpg, jpeg, gif, mp4, avi, mov, wmv, mkv"
            response['status'] = 500

        return response

    def getImage(self, body):
        url = './images/blogs/images/' + body['image']
        if not os.path.exists(url):
            return make_response({'error': 'Image not found'}, 404)

        return send_file(url, as_attachment=True)