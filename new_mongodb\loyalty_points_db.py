import logging
from pymongo.collation import Collation
from bson import ObjectId
import new_mongodb
from utils.common import parse_json
import datetime

logger = logging.getLogger()

PRODUCT_COLLECTION = "products"
REWARDS_COLLECTION = "loyalty_rewards"

def get_rewards(store):
    db = new_mongodb.get_store_db_client(store)
    customers = db[REWARDS_COLLECTION].find()
    return customers

def create(store, payload):
    db = new_mongodb.get_store_db_client(store)
    result=db[REWARDS_COLLECTION].insert_one(payload)
    return result

def update(store, query, payload):
    db = new_mongodb.get_store_db_client(store)
    result=db[REWARDS_COLLECTION].update_one(query, payload)
    return result