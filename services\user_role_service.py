from services import Service
from bson import ObjectId
from utils.common import parse_json
from mongo_db.user_db import query_users, empty_user_permission, update_user_role_name

class Role(Service):
    def create(self, tenant_id, data):
        data["tenant_id"] = tenant_id
        id = super().create(data)
        return id

class UserRole(Service):
    def create(self, data, store, store_permissions):
        request = {
            "role": data['role'],
            "is_administrator_access": data['is_administrator_access'],
            "is_super_admin": data['is_super_admin'],
            "permissions": {},
            "total_users": 0,
            "store_id": store['id'],
        }

        del data['role']
        del data['is_super_admin']
        del data['is_administrator_access']

        for item_key, item_value in data.items():
            if item_key in store_permissions:
                request['permissions'][item_key] = self.handle_children(item_key, item_value, store_permissions)
        
        id = super().create(request)
        return id

    def handle_children(self, item_key, item_value, store_permissions):
        obj = self.handle_operations(item_key, item_value, store_permissions)
        obj['children'] = {}
        
        if 'children' in item_value and len(item_value['children']) > 0:
            for child_key, child_value in item_value['children'].items():
                child_obj = self.handle_children(child_key, child_value, store_permissions[item_key]['children'])
                obj['children'][child_key] = child_obj

        return obj
    
    def handle_operations(self, item_key, item_value, store_permissions):
        obj = {}
        permission = store_permissions.get(item_key, None)
        if permission:
            obj['api'] = permission.get('api', '')
            obj['name'] = permission.get('name', None)
            if obj['name']:
                obj[obj['name']] = {}
                if item_value and "operations" in item_value:
                    for op_key, op_value in item_value['operations'].items():
                        obj[item_key][op_key] = op_value
        
        return obj

    def get_all(self, store):
        return super().get_all_records_with_selected_fields(
        {
            "store_id": store['id'],
            "status": "active"
        },
        {
            "id": 1,
            "role": 1,
            "total_users": 1,
            "created_at": 1
        })

    def update_role(self, data, store_permissions):
        role_id = data['id']
        query = {"_id": ObjectId(role_id)}        
        request = {
            "role": data['role'],
            "is_administrator_access": data['is_administrator_access'],
            "is_super_admin": data['is_super_admin'],
            "permissions": {},
        }

        del data['id']
        del data['role']
        del data['is_super_admin']
        del data['is_administrator_access']

        for item_key, item_value in data.items():
            # find in parent items ...
            if item_key in store_permissions:
                request['permissions'][item_key] = self.handle_children(item_key, item_value, store_permissions)
                # obj = {}
                # obj['api'] = store_permissions[item_key]['api']
                # obj['name'] = store_permissions[item_key]['name']
                # obj[obj['name']] = {}
                
                # for op_key, op_value in item_value['operations'].items():
                #     obj[item_key][op_key] = op_value

                # request['permissions'][item_key] = obj
                # obj['children'] = {}
                
                # if 'children' in item_value and len(item_value['children']) > 0:
                #     for child_key, child_value in item_value['children'].items():
                #         child_obj = {}
                #         child_obj['api'] = store_permissions[item_key]['children'][child_key]['api']
                #         child_obj['name'] = store_permissions[item_key]['children'][child_key]['name']
                #         child_obj[child_obj['name']] = {}

                #         for op_key, op_value in child_value['operations'].items():
                #             child_obj[child_key][op_key] = op_value
                        
                #         obj['children'][child_key] = child_obj
                #         child_obj['children'] = {}        

        res =  super().update_one(query, {"$set": request})

        if res:
            # update user's role name according to new role name
            all_users = query_users({'role_id': role_id})
            for i in all_users:
                update_user_role_name(i['username'], request['role'])
            return res
    
    def delete_by_id(self, role_id):
        users_cursor = query_users({'role_id': role_id})
        users = list(users_cursor)
        num_users = len(users)

        if num_users > 0:
            return False
        else:
            for i in users:
                empty_user_permission(i['username'])

            query = { "$set": {"status": 'deleted'}}
            return super().update_one({"_id": ObjectId(role_id)}, query)

    def get_one(self, role_id):
        res = super().find_one({"_id": ObjectId(role_id)})
        return parse_json(res)

    def get_all_roles(self, store):
        return super().find({
            "store_id": store['id'],
            "status": "active"
        })