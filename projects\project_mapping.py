import new_pgdb
from sqlalchemy import text
import pg_db
from mongo_db import user_db
from datetime import datetime
from flask import request
import psycopg2.errors as errors
from utils.common import calculatePaginationData
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import logging
import traceback

logger = logging.getLogger()

def get_project_mapping_tables(store_id):
    response = {
        "status": 400       
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        query = text (f"""SELECT pdt.id, pdt.pipeline_module_id, pdt.table_name, pdt.title, pdt.description, pm.name, pm.description from {pg_db.pipeline_db_tables} as pdt join {pg_db.pipeline_modules} as pm on pdt.pipeline_module_id = pm.id;""")

        result = conn.execute(query)
        data = []
        for row in result.fetchall():   
            row_data = {
                'id': row[0],
                'pipeline_module_id': row[1],
                'db_table_name': row[2],           
                'lable': row[3],
                'description': row[4],
                'module_name': row[5], 
                'module_description': row[6]
            }
            data.append(row_data)
             
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
    finally:
        if conn:
            conn.close()
    return response

def get_module_table_statuses(store_id, db_table_id, column_mapping):
    response = {
        "status": 400       
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        table_query = text (f"""SELECT * from {pg_db.pipeline_db_tables} where id = '{int(db_table_id)}';""")
        table_result = conn.execute(table_query).fetchone()
        related_tables = []
        if table_result:
            table_name = table_result[2]
            if column_mapping == 'false':
                related_tables = [table_result[idx] for idx in (6, 8, 10) if table_result[idx]]
            query = text (f"""SELECT * from {table_name} limit 1;""") 
            result = conn.execute(query).keys()
            
            table_name_uppercase = ' '.join(word.capitalize() for word in table_name.split('_'))
            data = []
            if result:        
                for col in result:
                    formatted_name = ' '.join(word.capitalize() for word in col.split('_'))
                    if column_mapping == 'false' and len(related_tables):
                        col = table_name + '.' + col
                        formatted_name = table_name_uppercase + '.' + formatted_name
                    col_data = {
                        'column_key': col,
                        'column_label': formatted_name
                    }
                    data.append(col_data)
            if len(related_tables):
                for t_name in related_tables:
                    query = text (f"""SELECT * from {t_name} limit 1;""") 
                    result = conn.execute(query).keys()
                    t_name_uppercase = ' '.join(word.capitalize() for word in t_name.split('_'))
                    if result:
                        for col in result:
                            formatted_name = ' '.join(word.capitalize() for word in col.split('_'))
                            if column_mapping == 'false':
                                col = t_name + '.' + col
                                formatted_name = t_name_uppercase + '.' + formatted_name
                            col_data = {
                                'column_key': col,
                                'column_label': formatted_name
                            }
                            data.append(col_data)
             
            if data:
                response['data'] = data
                response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
    finally:
        conn.close()
    return response

def set_module_table_status_value(store_id, db_table_id, column_key, project_id, default_assignee, ticket_name, project_type):
    response = {
        "status": 400       
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        if project_id and db_table_id and column_key:
            #check if mapping exists in the table_column_mapping
            query = text (f"""SELECT * from {pg_db.pipeline_project_table_mapping} where pipeline_db_tables_id = '{int(db_table_id)}' and db_table_column = '{column_key}' and project_id != '{int(project_id)}';""")
            result = conn.execute(query).fetchone()
            if result:
                response['status'] = 400
                response['message'] = "Mapping for this column already exists with another project."
                return response
        
            query = text (f"""SELECT * from {pg_db.pipeline_project_table_mapping} where project_id = '{int(project_id)}';""")
            result = conn.execute(query).fetchone()
            if result:
                project_table_mapping_id = result[0]
                old_column_key = result[5]
                old_db_table_id = result[2]
                update_query = text(f"""UPDATE {pg_db.pipeline_project_table_mapping} SET db_table_column = :column_key, pipeline_db_tables_id = :db_table_id, ticket_name = :ticket_name, default_assignee = :default_assignee WHERE project_id = :project_id;""")
                update_query = update_query.params(column_key=column_key, db_table_id=db_table_id, ticket_name=ticket_name, default_assignee=default_assignee, project_id=project_id)
                conn.execute(update_query)
                conn.commit()
                
                if str(column_key) != str(old_column_key) or int(db_table_id) != int(old_db_table_id): 
                    delete_query = text(f"""DELETE FROM {pg_db.pipeline_column_mapping} WHERE project_table_mapping_id = :project_table_mapping_id and project_id = :project_id;""")
                    delete_query = delete_query.params(project_table_mapping_id=project_table_mapping_id, project_id=project_id)
                    conn.execute(delete_query)

                    delete_query = text(f"""DELETE FROM {pg_db.pipeline_custom_field_mapping} WHERE project_table_mapping_id = :project_table_mapping_id and project_id = :project_id;""")
                    delete_query = delete_query.params(project_table_mapping_id=project_table_mapping_id, project_id=project_id)
                    conn.execute(delete_query)
            else:
                insert_query = text(f"""INSERT INTO {pg_db.pipeline_project_table_mapping} (project_id, pipeline_db_tables_id, ticket_name, default_assignee, db_table_column) VALUES (:project_id, :db_table_id, :ticket_name, :default_assignee, :column_key);""")
                insert_query = insert_query.params(project_id=project_id, db_table_id=db_table_id, ticket_name=ticket_name, default_assignee=default_assignee, column_key=column_key)
                conn.execute(insert_query)
                conn.commit()
        else:
            delete_queries = [
                f"""DELETE FROM {pg_db.pipeline_column_mapping} WHERE project_id = :id;""",
                f"""DELETE FROM {pg_db.pipeline_custom_field_mapping} WHERE project_id = :id;""",
                f"""DELETE FROM {pg_db.pipeline_project_table_mapping} WHERE project_id = :id;"""
            ]

            for delete_query in delete_queries:
                conn.execute(text(delete_query), {'id': int(project_id)})
            
            conn.commit()
        
        query = text (f"""Update {pg_db.projects} set project_type = :project_type where id = :project_id;""")
        query = query.params(project_type=project_type, project_id=project_id)
        conn.execute(query)
        conn.commit()

        response['status'] = 200
        response['message'] = "Mapping successfully updated."

    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
    finally:
        conn.close()
    return response

def get_unique_column_values_for_mapping(store_id, db_table_id, column, page, limit, search, project_id):
    response = {
        "status": 400       
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        page = int(page)
        limit = int(limit)
        offset = (page - 1) * limit
        table_query = text (f"""SELECT * from {pg_db.pipeline_db_tables} where id = '{int(db_table_id)}';""")
        table_result = conn.execute(table_query).fetchone()
        total_count = 0
        if table_result:
            table_name = table_result[2]
            appended_search = ''
            if search != '':
                search = search.strip()
                appended_search = f" WHERE {column}::TEXT ILIKE '%{search}%' "
            
            # fetch the mapped value with columns 
            mapped_value_query = text(f"""SELECT pcm.table_column_value FROM {pg_db.pipeline_column_mapping} AS pcm
                                    LEFT JOIN {pg_db.project_columns} AS pm ON pm.id = pcm.project_column_id AND pm.project_id = pcm.project_id
                                    WHERE pm.project_id = {project_id} GROUP BY pcm.table_column_value""")   
            
            mapped_value_res = conn.execute(mapped_value_query).fetchall()
            mapped_values = [row[0] for row in mapped_value_res]
            mapped_values_str = ', '.join(f"'{value}'" for value in mapped_values)

            # fetch the total count
            count_query = text(f"""SELECT COUNT(DISTINCT({column})) from {table_name} {appended_search}""")   
            count_rs = conn.execute(count_query)
            total_count = int(count_rs.scalar())
            # fetch the unique values
            query = ''
            if not mapped_values_str:
                # If mapped_values_str is empty, run the simple query without CASE and sorting
                query = text(f"""SELECT DISTINCT({column}) FROM {table_name} {appended_search} ORDER BY {column} OFFSET :offset LIMIT :limit""")
            else:
                # If mapped_values_str is not empty, include CASE for priority sorting
                query = text(f"""WITH prioritized_values AS (SELECT DISTINCT {column},
                                    CASE WHEN {column} IN ({mapped_values_str}) THEN 0 ELSE 1 END AS priority
                                    FROM {table_name} {appended_search} )
                                SELECT DISTINCT({column}) FROM prioritized_values ORDER BY {column} ASC OFFSET :offset LIMIT :limit""")
            query = query.bindparams(offset=offset, limit=limit)
            result = conn.execute(query).fetchall()   
            data = []
            if result:        
                for col in result:
                    col_data = {
                        'label': col[0]
                    }
                    data.append(col_data)

            if data:
                final_result = {
                    "data": data,
                    "meta": {
                        "current_page": page,
                        "next_page": page + 1 if offset + limit < total_count else None,
                        "total_count": total_count
                    }
                }    
                response['data'] = final_result
                response['status'] = 200
            else:
                response['status'] = 200
                response['data'] = {
                    "data": [],
                    "meta": {
                        "current_page": page,
                        "next_page": page + 1 if offset + limit < total_count else None,
                        "total_count": total_count
                    }
                }
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
    finally:
        conn.close()
    return response

def map_status_to_column(store, project_id, column_id, column_value):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store['id'])
    
    try:
        query = text (f"""SELECT * from {pg_db.pipeline_project_table_mapping} where project_id = '{int(project_id)}';""")
        result = conn.execute(query).fetchone()
        project_table_mapping_id = None
        if result:
            project_table_mapping_id = result[0]
            db_table_id = result[2]
           

        if column_value is None or column_value == '':
            delete_query = text(f"""DELETE FROM {pg_db.pipeline_column_mapping} WHERE project_id = :project_id AND project_column_id = :column_id""")
            delete_query = delete_query.params(project_id=project_id, column_id=column_id)
            conn.execute(delete_query)
            conn.commit()
            response['status'] = 200    
            response['message'] = "Mapping successfully updated."
            return response

        # Check if the mapping for project_id, column_id, and module_status_id already exists
        check_query = text(f"""SELECT COUNT(*) 
                               FROM {pg_db.pipeline_column_mapping} WHERE project_id = :project_id 
                               AND project_table_mapping_id = :project_table_mapping_id AND table_column_value = :column_value""")
        check_query = check_query.params(project_id=project_id, project_table_mapping_id=project_table_mapping_id, column_value=column_value)
        check_result = conn.execute(check_query)
        existing_count = check_result.scalar()

        if existing_count > 0:
            response['status'] = 409
            response['message'] = "column_value: This status is already assigned to another column."
            return response

        record_check_query = text(f"""SELECT COUNT(*) 
                                       FROM {pg_db.pipeline_column_mapping} 
                                       WHERE project_id = :project_id 
                                       AND project_column_id = :column_id""")
        record_check_query = record_check_query.params(project_id=project_id, column_id=column_id)
        record_check_result = conn.execute(record_check_query)
        record_existing_count = record_check_result.scalar()
        
        if record_existing_count > 0:
            update_query = text(f"""UPDATE {pg_db.pipeline_column_mapping} 
                                    SET table_column_value = :column_value 
                                    WHERE project_id = :project_id 
                                    AND project_column_id = :column_id""")
            update_query = update_query.params(column_value=column_value,  project_id=project_id, column_id=column_id)
            conn.execute(update_query)
            conn.commit()
            
            response['status'] = 200
            response['message'] = "Mapping successfully updated."
        
        # If no existing mapping, proceed with insert
        else:
            insert_query = text(f"""INSERT INTO {pg_db.pipeline_column_mapping} 
                                    (project_id, project_column_id, project_table_mapping_id, table_column_value, table_column_value_type) 
                                    VALUES (:project_id, :column_id, :project_table_mapping_id, :column_value, '')""")
            insert_query = insert_query.params(project_id=project_id, column_id=column_id, project_table_mapping_id=project_table_mapping_id, column_value=column_value)
            conn.execute(insert_query)
            conn.commit()
            
            response['status'] = 200
            response['message'] = "Mapping successfully created."
    
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = f"Error: {error_message}"
    
    finally:
        conn.commit()
        conn.close()
    
    return response

def map_status_to_custom_field(store, project_id, field_id, column_value):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store['id'])
    
    try:
        query = text (f"""SELECT * from {pg_db.pipeline_project_table_mapping} where project_id = '{int(project_id)}';""")
        result = conn.execute(query).fetchone()
        project_table_mapping_id = None
        if result:
            project_table_mapping_id = result[0]
            db_table_id = result[2]
           

        if column_value is None or column_value == '':
            delete_query = text(f"""DELETE FROM {pg_db.pipeline_custom_field_mapping} WHERE project_id = :project_id AND project_customfield_id = :field_id""")
            delete_query = delete_query.params(project_id=project_id, field_id=field_id)
            conn.execute(delete_query)
            conn.commit()
            response['status'] = 200    
            response['message'] = "Mapping successfully updated."
            return response

        # Check if the mapping for project_id, field_id, and column_value already exists
        check_query = text(f"""SELECT COUNT(*) 
                               FROM {pg_db.pipeline_custom_field_mapping} WHERE project_id = :project_id 
                               AND project_table_mapping_id = :project_table_mapping_id AND db_table_column = :column_value AND project_customfield_id != :field_id""")
        check_query = check_query.params(project_id=project_id, project_table_mapping_id=project_table_mapping_id, column_value=column_value, field_id=field_id)
        check_result = conn.execute(check_query)
        existing_count = check_result.scalar()

        if existing_count > 0:
            response['status'] = 409
            response['message'] = "column: This column is already assigned to another custom field."
            return response

        record_check_query = text(f"""SELECT COUNT(*) 
                                       FROM {pg_db.pipeline_custom_field_mapping} 
                                       WHERE project_id = :project_id 
                                       AND project_customfield_id = :field_id""")
        record_check_query = record_check_query.params(project_id=project_id, field_id=field_id)
        record_check_result = conn.execute(record_check_query)
        record_existing_count = record_check_result.scalar()
        
        if record_existing_count > 0:
            update_query = text(f"""UPDATE {pg_db.pipeline_custom_field_mapping} 
                                    SET db_table_column = :column_value 
                                    WHERE project_id = :project_id 
                                    AND project_customfield_id = :field_id""")
            update_query = update_query.params(column_value=column_value,  project_id=project_id, field_id=field_id)
            conn.execute(update_query)
            conn.commit()
            
            response['status'] = 200
            response['message'] = "Mapping successfully updated."
        
        # If no existing mapping, proceed with insert
        else:
            insert_query = text(f"""INSERT INTO {pg_db.pipeline_custom_field_mapping} 
                                    (project_id, project_customfield_id, project_table_mapping_id, db_table_column) 
                                    VALUES (:project_id, :field_id, :project_table_mapping_id, :column_value)""")
            insert_query = insert_query.params(project_id=project_id, field_id=field_id, project_table_mapping_id=project_table_mapping_id, column_value=column_value)
            conn.execute(insert_query)
            conn.commit()
            
            response['status'] = 200
            response['message'] = "Mapping successfully created."
    
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = f"Error: {error_message}"
    
    finally:
        conn.commit()
        conn.close()
    
    return response
