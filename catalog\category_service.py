from new_mongodb import delete_documents_from_storefront_collection, fetchall_documents_from_storefront_collection
from new_mongodb import get_admin_db_client_for_store_id
from new_mongodb import StoreDBCollections, StoreAdminDBCollections,  cms_db
import datetime
import new_utils
import mongo_db
import new_mongodb

def get_category(store, category_id):
    new_list={} 

    res= cms_db.get_page_by_id(store['id'], category_id)
    
    if (len(res['versions']) == 0):
        activeVersion = res['default_layout']
    else:
        versions = res['versions']
        for version in versions:
            if (version['status'] == 'active'):
                activeVersion = version
                break

    new_list['id'] = res['id']
    new_list['name'] = res['name']
    new_list['created_at'] = res['created_at']
    new_list['updated_at'] = res['updated_at']
    new_list['url'] = res['url']
    new_list['versions'] = activeVersion
    return new_list

def delete_category(store, category_id):
    res = delete_documents_from_storefront_collection(store['id'], {"id": int(category_id)})
    return res

def get_all_categories(store, payload):
    fields = {
        "_id": 1,
        "id": 1,
        "parent_id": 1,
        "name": 1,
        "type": 1,
        "views": 1,
        "sort_order": 1,
        "is_visible": 1,
        "url": 1,    
        "created_at": 1,
        "updated_at": 1,
        "versions": 1,
        "default_layout": 1,
        "status": 1,
        "is_customers_only": 1,
        "has_sub_child":1
    }
    store_id = store['id']
    payload['filterBy'] = ['name', 'page_title']
    db_client = get_admin_db_client_for_store_id(store_id)
    categories, total_data_length, page, limit = \
        new_utils.get_paginated_records_updated(db_client=db_client, payload=payload, \
                                                collection_name=StoreAdminDBCollections.CMS_COLLECTION, \
                                                fields=fields, additional_query='')
    
    for category in categories:
        active_versions = [version for version in category.get('versions', []) if version.get('status') == 'active']
        if active_versions:
            category['versions'] = active_versions[0]
        else:
            category['versions'] = {}

    data = new_utils.calculate_pagination(categories, page, limit, total_data_length)
    return data

def sync_all_categories(store):
    categories = fetchall_documents_from_storefront_collection(store['id'], StoreDBCollections.CATEGORIES)
    current_time_utc = int(datetime.datetime.now(datetime.timezone.utc).timestamp())
    for category in categories:
        new_list={} 
        seo_details={}
        seo_details["page_name"]=category["name"]
        seo_details["page_url"]=category["custom_url"]["url"]
        seo_details["meta_title"]=category["page_title"]
        seo_details["meta_description"]=""

        user = {}
        user["user_id"]= "" 
        user["user_name"]= ""              
        
        new_list["id"]=category["id"]
        new_list["parent_id"] = category["parent_id"]
        new_list["name"]=category["name"]
        new_list["type"]="category"
        new_list["views"] = category["views"]
        new_list["sort_order"] = category["sort_order"]
        new_list["is_visible"] = category["is_visible"]              
        new_list["url"]=category["custom_url"]["url"]                                          
        new_list["default_layout"] = {}
        new_list["versions"]= [
            { 
                "name":category["name"],
                "status":"active", 
                "class":"",
                "type": "category",
                "seo_details":seo_details,
                "components":[
                    {
                    "id": 1,
                    "name": "HTML Block",
                    "code": "html_block",  
                    "variant": {
                        "id": "1",
                        "name": "HTML Block",
                        "admin_layout": "style1",
                        "class": [],                          
                        "config": {
                            "data": category["description"]    
                                }
                    },  
                    "children": []
                    }
                ],
                "created_at":current_time_utc,
                "created_by": user,                                                                              
                "version":0,
            } 
        ]
        new_list["preview_state"] = {} 
        new_list["updated_at"]=""
        new_list["created_at"] = current_time_utc
        new_list["status"] = "active"
        
        res = cms_db.create(store['id'], new_list)
    return 'success'


def category_treeview(store, search=None, distributor_id=None):
    categories = fetch_all_categories(store, search, distributor_id)
    
    # Create a dictionary to map categories by their IDs
    categories_dict = {category['id']: category for category in categories}
    
    # If there's no search query, we build the parent-child relationship
    if not search:
        root_categories = []
        for category in categories:
            if category['parent_id'] == 0:  # Root category
                root_categories.append(category)
            else:
                # Find the parent category in the dictionary
                parent_category = categories_dict.get(category['parent_id'])

                if parent_category:
                    # Initialize the 'children' key if not already present
                    if 'children' not in parent_category:
                        parent_category['children'] = []
                    # Add the current category as a child to its parent
                    parent_category['children'].append(category)
        return root_categories

    # If search is applied, return the flat list of matching categories
    return categories


def fetch_all_categories(store, search=None, distributor_id=None):
    db = new_mongodb.get_admin_db_client_for_store_id(store['id'])
    
    # Fetch category IDs used in price_list_rules with the given distributor_id
    used_category_ids = set()
    if distributor_id:
        pipeline = [
            {"$match": {"distributor_id": int(distributor_id)}},
            {"$project": {"categories": 1, "_id": 0}},
            {"$unwind": "$categories"}
        ]
        used_categories = db.price_list_rules.aggregate(pipeline)
        used_category_ids = {doc['categories'] for doc in used_categories}

    # Building the pipeline for MongoDB aggregation
    pipeline = []

    # If search query is provided, add a match stage to filter by name
    if search:
        search_filter = {"name": {"$regex": search, "$options": "i"}}  # Case-insensitive search
        pipeline.append({"$match": search_filter})

    # Exclude categories used in price_list_rules with the given distributor_id
    if used_category_ids:
        exclude_filter = {"id": {"$nin": list(used_category_ids)}}
        pipeline.append({"$match": exclude_filter})

    # Add sorting and projection stages
    pipeline.extend([
        {"$sort": {"name": 1}},  
        {"$project": {
            "_id": 0,
            "id": 1,
            "name": 1,
            "parent_id": 1,
            "sort_order": 1,
            "status": 1
        }}
    ])

    # Execute the aggregation pipeline
    categories = list(db.cms.aggregate(pipeline))
    categories = mongo_db.processList(categories)

    return categories
