from flask import request
import logging
from api import APIResource
from products.my_products import my_products_list
from new_mongodb import store_admin_db
from io import BytesIO
from utils.common import resize_image
from flask import make_response, send_file

logger = logging.getLogger()

class MyProducts(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering MY Products GET")
        try:
          query_params = request.args.to_dict()
          res = my_products_list.get_products(store, query_params)
          # Return response ...
          return res, 200
        finally:
            logger.debug("Exiting MY Products GET")
    
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Create MY Product Details POST")
        try:
            req_body = request.get_json(force=True)
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 

            if user is None:
                return {"message": "Unauthorized access."}, 401
            res, status = my_products_list.create_product(store, req_body, user)
            if status == 200:
                return {'data': res['data']}, status
            else:
                return {'message': res['data']}, status
        finally:
            logger.debug("Exiting Create MY Product Details POST")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
class MyProduct(APIResource):
    def get_executor(self, request, token_payload, store, product_id):
        logger.debug("Entering MY Product Details GET")
        try:
            if not product_id:
                return {"message": "Please enter product_id as a query params in request"}, 400
            res = my_products_list.get_product(store, product_id)
            if res['status'] == 200:
                return {'data': res['data']}, res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting MY Product Details GET")

    def put_executor(self, request, token_payload, store, product_id):
        logger.debug("Entering Update Product Details PUT")
        try:
            if not product_id:
                return {"message": "Product ID not valid for the request."}, 400
            
            req_body = request.get_json(force=True)
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            
            if user is None:
                return {"message": "Unauthorized access."}, 401
            
            res, status = my_products_list.update_product(store, req_body, product_id, user)
            if status == 200:
                return {'data': res['data']}, status
            else:
                return {'message': res['data']}, status
            
        finally:
            logger.debug("Exiting Update Product Details PUT")

    def delete_executor(self, request, token_payload, store, product_id):
        logger.debug("Entering MY Product Details Delete")
        try:            
            res =  my_products_list.delete_product(store, product_id)
            if res['status'] == 200:                
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting MY Product Details Delete")

    def delete(self, product_id):
        return self.execute_store_request(request, self.delete_executor, product_id)

    def get(self, product_id):
        return self.execute_store_request(request, self.get_executor, product_id)
    
    def put(self, product_id):
        return self.execute_store_request(request, self.put_executor, product_id)
    
# store and get images to server
class ProductImages(APIResource):
    def post_executor(self, request, token_payload, store):
        try:
            images = request.files.getlist("images")
            # Check if no images were selected
            if not images or len(images) == 0 or images[0].filename == '':
                return {"data": "No images selected for uploading."}, 400
            
            res = my_products_list.setImage(images)
            if res['status'] == 200:
                return {"data": res}, 200
            else:
                return {"data": res}, 500
        finally:
            logger.debug("Exiting Image Upload POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)

class ProductStaticImages(APIResource):
    def get_executor(self, request, image_type, month, file_name):
        if image_type and file_name and month:
            file_path = '/app/images/'
            
            # Build the file path
            if image_type == "products":
                file_path = file_path + image_type + "/" + month + "/" + file_name
            else:
                file_path = file_path + image_type + "/" + file_name

            # Check if month is "page-builder"
            if month == 'page-builder':
                # If month is "page-builder", return the original image without resizing
                try:
                    return send_file(file_path, mimetype='image/png')
                except Exception as e:
                    return make_response({'error': 'Failed to retrieve image'}, 500)
            
            # Call resize_image only if month is not "page-builder"
            res = resize_image(file_path, new_width=100)

            if res:
                img_byte_arr = BytesIO()
                res.save(img_byte_arr, format='PNG')
                img_byte_arr.seek(0)
                
                return send_file(img_byte_arr, mimetype='image/png')
            else:
                return make_response({'error': 'Failed to process image'}, 500)
                
        return make_response({'error': 'Image not found'}, 404)


    def get(self, image_type, month, file_name):
        return self.execute_open_api_request(request, self.get_executor, image_type, month, file_name)
    
class SkuvaultBrands(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Skuvault Brands GET")
        try:
            res = my_products_list.get_skuvault_brands(store)
            if res['status'] == 200:
                return {'data': res['data']}, res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Skuvault Brands GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class SkuvaultClassification(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Skuvault Classification GET")
        try:
            res = my_products_list.get_skuvault_classification(store)
            if res['status'] == 200:
                return {'data': res['data']}, res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Skuvault Classification GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class SkuvaultSuppliers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Skuvault Suppliers GET")
        try:
            res = my_products_list.get_skuvault_suppliers(store)
            if res['status'] == 200:
                return {'data': res['data']}, res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Skuvault Suppliers GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)