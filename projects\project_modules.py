from projects import check_project_access
from sqlalchemy import text
import pg_db
from mongo_db import user_db
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from datetime import datetime
import logging
import traceback

logger = logging.getLogger()

def _fetch_project_modules(conn, project_id):
    data = []
    query = text(
        f"""SELECT pm.id as module_id, pm.name AS module_name, COUNT(pc.module_id) AS card_count, pm.sort_id, pm.description, pm.is_visible, pm.is_default
            FROM {pg_db.project_modules} AS pm
            LEFT JOIN {pg_db.project_cards} AS pc ON pm.id = pc.module_id
            WHERE pm.project_id = :project_id
            GROUP BY pm.id, pm.name
            ORDER BY pm.sort_id ASC;
        """
    )
    query = query.params(project_id=project_id)
    result = conn.execute(query)
    for row in result.fetchall():
        row_data = {
            'module_id': row[0],
            'name':row[1].split('_')[0],
            'cards': row[2],
            'sort_id': row[3],
            'description': row[4],
            'is_visible': row[5],
            'is_default': row[6]
        }
        data.append(row_data)
    
    return data  

def get_modules(project_id, username):
    response = {
        "status" :400       
    }
    conn = pg_db.get_connection()
    try:
        project_access = check_project_access(project_id, username) 
        if not project_access:
            response['status'] = 403
            response['message'] = "You don't have access for this project."
            return response   
        data = _fetch_project_modules(conn, project_id)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response

def _insert_new_module(conn, project_id, name, module_description, is_archived, username, is_visible, call_from_project=False):
    try:
        max_sort_id_query = text(
            f"""SELECT MAX(sort_id) AS max_sort_id FROM {pg_db.project_modules} WHERE project_id = :project_id;"""
        )
        max_sort_id_result = conn.execute(max_sort_id_query, {'project_id': project_id}).fetchone()
        max_sort_id = max_sort_id_result[0] or 0
        
        new_sort_id = max_sort_id + 1
        if call_from_project:
            query = text(
            f"""INSERT INTO {pg_db.project_modules} (project_id, name, description, is_archived, created_by, updated_by, updated_at, sort_id, is_visible, is_default)
                VALUES (:project_id, :name, :description, :is_archived, :created_by, :created_by, CURRENT_TIMESTAMP, :sort_id, :is_visible, :is_default);
            """
            )
            query = query.params(project_id=project_id, name=name, description=module_description, is_archived=is_archived, created_by=username, sort_id = -1, is_visible=is_visible, is_default=True)
            conn.execute(query)
        else:
            query = text(
                f"""INSERT INTO {pg_db.project_modules} (project_id, name, description, is_archived, created_by, updated_by, updated_at, sort_id, is_visible, is_default)
                    VALUES (:project_id, :name, :description, :is_archived, :created_by, :created_by, CURRENT_TIMESTAMP, :sort_id, :is_visible, :is_default);
                """
            )
            query = query.params(project_id=project_id, name=name, description=module_description, is_archived=is_archived, created_by=username, sort_id = new_sort_id, is_visible=is_visible, is_default=False)
            conn.execute(query)

        return True
    except Exception as e:
        logger.error(traceback.format_exc())
        return False

def add_new_module(payload, username, project_id):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:
        module_name = payload.get('name', '')
        module_description = payload.get('description', '')
        is_archived = payload.get('is_archived', False)
        is_visible = payload.get('is_visible', True)

        if module_name and module_name != '':
            # module_name = module_name + '_' + str(project_id)
            data = _insert_new_module(conn, project_id, module_name, module_description, is_archived, username, is_visible)
            if data:
                response['status'] = 200
                response['message'] = "Data inserted successfully."
            else:
                response['status'] = 409
                response['message'] = "module_name: This module already exists"
        else:
            response['status'] = 400
            response['message'] = "Invalid payload, fields are missing."
        
    except IntegrityError as e:
            logger.error(traceback.format_exc())
            if isinstance(e.orig, UniqueViolation):                
                response['status'] = 409
                response['message'] = "Duplicate key violation: This module already exists in the rules."
            else:
                response['status'] = 500
                response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response


def _modify_module_sort_id(conn, project_id, module_ids):
    try:
        for index, id in enumerate(module_ids, start=1):
            query = text (
                f"""UPDATE {pg_db.project_modules} SET sort_id = :sort_id WHERE project_id = :project_id AND id = :id"""
            )
            query = query.params(sort_id=index, project_id=project_id, id=id)
            conn.execute(query)
        return True
    except Exception as e:
        return False

def update_modules_sort_id(project_id, module_ids):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        if not isinstance(module_ids, list):
            module_ids = [module_ids]

        data = _modify_module_sort_id(conn, project_id, module_ids)
        if data:
            response['status'] = 200
            response['message'] = "Data updated successfully."
        else:
            response['status'] = 400
            response['message'] = "Data updation failed."
    except IntegrityError as e:
        logger.error(traceback.format_exc())
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 409
            response['message'] = "Duplicate key violation: This project already exists."
        else:
            response['status'] = 500
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response