from flask import request
from api import APIResource
from analytics import po_util
import logging
import traceback
from products.all_products import liquidated_products

logger = logging.getLogger()

class ProductsLiquidation(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Liquidated products report GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            sort_by = query_params.get('sort_by', '').strip()
            search = query_params.get('search', None).strip()
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                res = liquidated_products.get_liquidated_product_report(store, page, limit, sort_by, search)
                return res, 200
            else:
                return {'message': 'Unauthorized'}, 401
        finally:
            logger.debug("Exiting Liquidated products report GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ProductsLiquidationCsv(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Liquidated products report CSV GET")
        try:
            query_params = request.args.to_dict()
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']
                query_params['username'] = username            
            if username != '':
                res = liquidated_products.get_liquidated_product_report_csv(store, query_params)
                return res, 200
            else:
                return {'message': 'Unauthorized'}, 401
        finally:
            logger.debug("Exiting Liquidated products report CSV GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
class ProductsLiquidationUpdate(APIResource):
    def patch_executor(self, request, token_payload, store, product_id):
        logger.debug("Entering Liquidated products report PATCH")
        try:
            payload = request.get_json()
            new_price = payload.get('new_price', None)
            is_liquidated = payload.get('is_liquidated', None)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                res = liquidated_products.update_liquidated_product(store, product_id, username, new_price, is_liquidated)
                if res['status'] == 200:
                    return {"message": res['message']}, 200
                else:
                    return {"message": res['message']}, 400
            else:
                return {'message': 'Unauthorized'}, 401
        finally:
            logger.debug("Exiting Liquidated products report PATCH")

    def patch(self, product_id):
        return self.execute_store_request(request, self.patch_executor, product_id)
    
class ProductsLiquidationLogs(APIResource):
    def get_executor(self, request, token_payload, store, product_id):
        logger.debug("Entering Liquidated products price change logs GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            sort_by = query_params.get('sort_by', '').strip()
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                res = liquidated_products.get_liquidated_product_logs(store, product_id, page, limit, sort_by)
                return res, 200
            else:
                return {'message': 'Unauthorized'}, 401
        finally:
            logger.debug("Exiting Liquidated products price change logs GET")

    def get(self, product_id):
        return self.execute_store_request(request, self.get_executor, product_id)
    