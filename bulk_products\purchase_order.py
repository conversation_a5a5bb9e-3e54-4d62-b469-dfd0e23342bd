from typing import Optional
from graphql import products_variant_query
import new_mongodb
import pg_db
from pydantic import BaseModel
from plugin import bc_customers, bc_order, bc_products
import logging
from sqlalchemy import text
from datetime import datetime, timezone
import traceback
import task
from utils import bc, store_util

logger = logging.getLogger()

class LineItem(BaseModel):
    product_id: int
    variant_id: int
    quantity: int
    price: float

class Order(BaseModel):
    order_id: int
    order_status: int
    customer_id: int
    cart_id:Optional[str]
    total_inc_tax:Optional[str]

def format_address(address, email):
    """Format address from BigCommerce API response."""
    return {
        "first_name": address.get("first_name", ""),
        "last_name": address.get("last_name", ""),
        "company": address.get("company", ""),
        "address1": address.get("address1", ""),
        "city": address.get("city", ""),
        "state_or_province": address.get("state_or_province", ""),
        "postal_code": address.get("postal_code", ""),
        "country": address.get("country", ""),
        "country_code": address.get("country_code", ""),
        "email": email,
        "phone": address.get("phone", "")
    }

# to create order in big commerce without calculating igen tax   
def create_purchase_order_without_igen_tax(store: object,customer_id: int, customer_email_id: str, line_items: list[LineItem],  bulling_address_id: int, shipping_address_id: int, payment_method: str,  shipping_cost: float, shipping_method: str, order_status: int = 7) -> Optional[Order]:
    billing_address = {}
    shipping_address = {}
    default_address = {}
    if not bulling_address_id or not shipping_address_id:
        addresses = bc_customers.fetch_customer_addresses(store, customer_id)
        if not addresses and len(addresses) == 0:
            raise Exception("Customer doesn't have billing address")
        address = addresses[0]
        default_address = {
            "first_name": address.get("first_name", ""),
            "last_name": address.get("last_name", ""),
            "company": address.get("company", ""),
            "street_1": address.get("address1", ""),
            "city": address.get("city", ""),
            "state": address.get("state_or_province", ""),
            "zip": address.get("postal_code", ""),
            "country": address.get("country", ""),
            "country_iso2": address.get("country_code", ""),
            "email": customer_email_id
        }

    if bulling_address_id:
        addresses = bc_customers.fetch_customer_addresses(store, customer_id, bulling_address_id)
        if not addresses and len(addresses) == 0:
            raise Exception("Customer doesn't have billing address")
        address = addresses[0]
        billing_address = {
            "first_name": address.get("first_name", ""),
            "last_name": address.get("last_name", ""),
            "company": address.get("company", ""),
            "street_1": address.get("address1", ""),
            "city": address.get("city", ""),
            "state": address.get("state_or_province", ""),
            "zip": address.get("postal_code", ""),
            "country": address.get("country", ""),
            "country_iso2": address.get("country_code", ""),
            "email": customer_email_id
        }
    
    if shipping_address_id:
        s_addresses = bc_customers.fetch_customer_addresses(store, customer_id, shipping_address_id)
        if not s_addresses and len(s_addresses) == 0:
            raise Exception("Customer doesn't have shipping address")
        s_address = s_addresses[0]
        shipping_address = {
            "first_name": s_address.get("first_name", ""),
            "last_name": s_address.get("last_name", ""),
            "company": s_address.get("company", ""),
            "street_1": s_address.get("address1", ""),
            "city": s_address.get("city", ""),
            "state": s_address.get("state_or_province", ""),
            "zip": s_address.get("postal_code", ""),
            "country": s_address.get("country", ""),
            "country_iso2": s_address.get("country_code", ""),
            "email": customer_email_id,
            "shipping_method": shipping_method
        }

    products = []
    for _line_item in line_items:
        products.append({
            "product_id": _line_item['product_id'],
            "variant_id": _line_item['variant_id'],
            "quantity": _line_item['quantity'],
            "price_inc_tax": _line_item['price'],
            "price_ex_tax": _line_item['price']
        })
    order_payload = {
        "status_id": order_status,
        "customer_id": customer_id,
        "billing_address": billing_address if billing_address else default_address,
        "shipping_addresses": [shipping_address] if shipping_address else [default_address],
        "products": products,
        "payment_method": payment_method,
        "base_shipping_cost": shipping_cost,
        "shipping_cost_ex_tax": shipping_cost,
        "shipping_cost_inc_tax": shipping_cost
    }
    res = bc_order.create_order(store, order_payload)
    order = None
    if res:
        status_code = res.status_code
        if res.status_code == 201:
            new_order = res.json()
            order = Order(order_id=new_order["id"], customer_id=customer_id, order_status=new_order["status_id"],cart_id=new_order["cart_id"],total_inc_tax=new_order["total_inc_tax"])
            task.send_task(task.SEND_ORDER_PLACED_EMAIL_TO_COMPLIANCE_TASK, args=(store['id'], new_order['id']))
        else:
            logger.error(order_payload)
            logger.error(f"status_code: {status_code}")
            logger.error(res.json())
            raise Exception(f"Order creation failed with status_code: {status_code}")
    else:
        logger.error(order_payload)
        logger.error("Bigcommerce api didn't return response")
        raise Exception("Bigcommerce api didn't return response")

    return order

# to create order in big commerce with calculating igen tax
def create_purchase_order_with_igen_tax(conn, store: object, po_id: str, customer_id: int, customer_email_id: str, bulling_address_id: int, payment_method: str, shipping_option_id: str, consignment_id: str, cart_id: str, line_items: list[LineItem], order_status: int = 7) -> Optional[Order]:
    conn = pg_db.get_connection()
    try:
        billing_address = {}
        default_address = {}
        #address section
        if not bulling_address_id:
            addresses = bc_customers.fetch_customer_addresses(store, customer_id)
            if not addresses and len(addresses) == 0:
                raise Exception("Customer doesn't have billing address")
            address = addresses[0]
            default_address = format_address(address, customer_email_id)

        if bulling_address_id:
            addresses = bc_customers.fetch_customer_addresses(store, customer_id, bulling_address_id)
            if not addresses and len(addresses) == 0:
                raise Exception("Customer doesn't have billing address")
            address = addresses[0]
            billing_address = format_address(address, customer_email_id)

        billing_address = billing_address if billing_address else default_address

        # create line items array
        check_line_items = {}
        products = []
        affected_product_ids = []
        if line_items and len(line_items) > 0:
            products, check_line_items, is_product_with_zero_total = process_line_items_for_cart(line_items)

        if len(products):  
            affected_product_ids = check_min_max_rule_for_lineitems(store, check_line_items)

        #update consignment shipping option
        if not update_consignment_shipping_option(store, cart_id, shipping_option_id, consignment_id):
            logger.error("Failed to update consignment shipping option")
            return None
        
        #set billing address
        if not set_billing_address(store, billing_address, cart_id):
            logger.error("Failed to set billing address")
            return None

        #create order
        order_data = create_order_from_cart_id(store, cart_id)
        if not order_data:
            logger.error("Failed to create order from cart ID")
            return None
        
        #update order status and payment method
        if not update_order(store, order_data['id'], order_status, payment_method):
            logger.error("Failed to update order")
            return None

        #fetch order details
        res = bc_order.fetch_order(store, order_data['id'])
        order = None
        if res and res.status_code == 200:
            new_order = res.json()
            order = Order(order_id=new_order["id"], customer_id=customer_id, order_status=new_order["status_id"],cart_id=new_order["cart_id"],total_inc_tax=new_order["total_inc_tax"])

            query = (f"""DELETE FROM bo_purchase_order_cart WHERE po_id = '{po_id}' AND cart_id = '{cart_id}' AND customer_id = '{customer_id}'""")
            
            conn.execute(text(query))
            conn.commit()

            task.send_task(task.SEND_ORDER_PLACED_EMAIL_TO_COMPLIANCE_TASK, args=(store['id'], new_order['id']))

        # logger.error(f"Failed to create order from cart ID: {res.status_code} - {res.text}")
        
        if affected_product_ids:
            res = set_min_max_rule_for_lineitems_in_bc(store, affected_product_ids)

        return order
    except Exception as e:
        logger.error(e)
    finally:
        if conn:
            conn.close()

#create cart and consignment
def create_cart_and_consignment(store, po_id, customer_name, customer_rep_email, customer_id, customer_email_id, line_items, shipping_address_id, username):
    conn = pg_db.get_connection()
    try:
        products = []
        shipping_address = {}
        default_address = {}
        result = {}
        all_affected_product_ids = []

        #check for existing cart and if exists delete it.
        query = (f"""SELECT po_id, cart_id, consignment_id, customer_id FROM bo_purchase_order_cart WHERE po_id = '{po_id}' AND customer_id = '{customer_id}'""")
        query_result = conn.execute(text(query)).fetchone()
        if query_result:
            cart_id = query_result[1]
            del_res = delete_cart(store, cart_id)
            query = (f"""DELETE FROM bo_purchase_order_cart WHERE po_id = '{po_id}' AND customer_id = '{customer_id}'""")
            conn.execute(text(query))
            conn.commit()

        # create line items array
        check_line_items = {}
        if line_items and len(line_items) > 0:
            products, check_line_items, is_product_with_zero_total = process_line_items_for_cart(line_items)
        
        if len(is_product_with_zero_total):
            add_log_for_product_with_zero_total(store['id'], line_items, products, customer_id, po_id, username)
            return {"status": 409, "message": "Cart amount or one of the product's total amount is less than or equal to 0"}

       
        # address section 
        if not shipping_address_id:
            addresses = bc_customers.fetch_customer_addresses(store, customer_id)
            if not addresses and len(addresses) == 0:
                raise Exception("Customer doesn't have billing address")
            address = addresses[0]
            default_address = format_address(address, customer_email_id)

        if shipping_address_id:
            s_addresses = bc_customers.fetch_customer_addresses(store, customer_id, shipping_address_id)
            if not s_addresses and len(s_addresses) == 0:
                raise Exception("Customer doesn't have shipping address")
            s_address = s_addresses[0]
            shipping_address = format_address(s_address, customer_email_id)

        shipping_address = shipping_address if shipping_address else default_address
        
        if len(products):  
            affected_product_ids = check_min_max_rule_for_lineitems(store, check_line_items)
            # affected_complex_rules_product_ids = check_complex_rules_for_lineitems(store, check_line_items) 
            # all_affected_product_ids = list(set(affected_product_ids + affected_complex_rules_product_ids)) 
            all_affected_product_ids = list(set(affected_product_ids)) 
            #create cart          
            cart_data = create_cart(store, products, customer_id)
            if not cart_data:
                logger.error("Failed to create cart")
                return {"status": 500, "message": "Failed to create cart"}
            
            cart_amount = cart_data['base_amount'] if 'base_amount' in cart_data else 0
            if cart_amount <= 0:
                cart_id = cart_data['id'] if cart_data else None
                add_log_for_product_with_zero_total(store['id'], line_items, products, customer_id, po_id, username, cart_id)
                return {"status": 500, "message": "Cart amount or one of the product's total amount is less than or equal to 0"}

            #create consignment
            consignment_data = create_consignment(store, cart_data, shipping_address)
            if not consignment_data:
                logger.error("Failed to create consignment")
                return {"status": 500, "message": "Failed to create consignment"}

            cart_id = consignment_data['cart']['id']
            cart_amount = consignment_data['cart']['base_amount'] if 'base_amount' in consignment_data['cart'] else 0
            if cart_amount <= 0:
                consignment_id = consignment_data['consignments'][0]['id'] if consignment_data['consignments'] else None
                add_log_for_product_with_zero_total(store['id'], line_items, products, customer_id, po_id, username, cart_id, consignment_id)
                return {"status": 500, "message": "Cart amount or one of the product's total amount is less than or equal to 0"}
            
            consignment = consignment_data['consignments'][0]
            shipping_options = consignment.get('available_shipping_options', []) 

            available_shipping_options = [
                {"id": shipping_option["id"], "label": shipping_option["description"]}
                for shipping_option in shipping_options
            ]    
            result['cart_id'] = cart_id
            result['consignment_id'] = consignment['id']
            result['shipping_options'] = available_shipping_options

            #add details to bo_purchase_order_cart
            query = text(
                f"""INSERT INTO bo_purchase_order_cart (po_id, cart_id, consignment_id, customer_id, customer_email, created_by)
                    VALUES (:po_id, :cart_id, :consignment_id, :customer_id, :customer_email, :created_by);
                """
            )
            query = query.params(po_id=po_id, cart_id = cart_id, consignment_id = consignment['id'], customer_id = customer_id,  customer_email = customer_email_id, created_by = username)
            conn.execute(query)

            if all_affected_product_ids:
                res = set_min_max_rule_for_lineitems_in_bc(store, all_affected_product_ids)
            return {"status": 200, "data": result}
        else:
            return {"status": 400, "message": "Please select at least one product with quantity > 0"}
    except Exception as e:
        logger.error(traceback.format_exc())
        if all_affected_product_ids:
            res = set_min_max_rule_for_lineitems_in_bc(store, all_affected_product_ids)
        return {"status": 500, "message": str(e)}
    finally:
        conn.commit()
        conn.close()

#add log for product with zero total
def add_log_for_product_with_zero_total(store_id, line_items, products, customer_id, po_id, username, cart_id = None, consignment_id = None):
    order_log = {
                'line_items': line_items,
                'processed_line_items': products,
                'customer_id': customer_id,
                'po_id': po_id,
                'created_by': username,
                'consignment_id': consignment_id,
                'cart_id': cart_id,
                'created_at': datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S'),
            }
    new_mongodb.insert_document_in_admin_collection(store_id, 'bo_zero_total_order_logs', order_log)

#process the line items array and convert it into payload for cart creation
def process_line_items_for_cart(line_items):
    check_line_items = {}
    products = []
    is_product_with_zero_total = {}
    if line_items and len(line_items) > 0:
        for _line_item in line_items:
            product_id = _line_item['product_id']
            is_marketing_product = _line_item['is_marketing_product']
            if int(product_id) not in check_line_items:
                check_line_items[int(product_id)] = []
            if 'variants' in _line_item:
                for variant in _line_item['variants']:
                    if variant['approved_qty'] !=0:
                        if not is_marketing_product and variant.get('price') == 0:
                            if int(product_id) not in is_product_with_zero_total:
                                is_product_with_zero_total[int(product_id)] = []
                            is_product_with_zero_total[int(product_id)].append(variant.get('bc_variant_id'))

                        check_line_items[int(product_id)].append(variant.get('bc_variant_id'))
                        product_variant_list = {}
                        product_variant_list['product_id'] =product_id
                        product_variant_list['variant_id'] = variant.get('bc_variant_id', '')
                        product_variant_list['quantity'] = variant.get('approved_qty',0) * variant.get('case_qty',0)
                        product_variant_list['list_price'] = variant.get('price',0)

                        products.append(product_variant_list)
    
    return products, check_line_items, is_product_with_zero_total

def create_cart(store, line_items, customer_id):
    try:
        cart_payload = {
            "customer_id": customer_id,
            "line_items": line_items
        }
        res = bc_order.create_cart(store, cart_payload)
        if res and res.status_code == 201:
            data = res.json()
            return data['data']
        logger.error(f"Failed to create cart: {res.status_code} - {res.text}")
    except Exception as e:
        logger.exception("Exception in create_cart")
    return None

def delete_cart(store, cart_id):
    try:
        bc_api = store_util.get_bc_api_creds(store)
        api = f"v3/carts/{cart_id}"    
        res = bc.call_api(api_data=bc_api, method="DELETE", url=api)
        if res and res.status_code == 204:
            return True
        logger.error(f"Failed to delete cart: {res.status_code} - {res.text}")
    except Exception as e:
        logger.exception("Exception in delete_cart")
    return False

def create_consignment(store, cart_data, shipping_address):
    try:
        cart_id = cart_data['id']
        line_items = [
            {"item_id": item["id"], "quantity": item["quantity"]}
            for item_list in cart_data['line_items'].values()
            for item in item_list
        ]
        payload = {
            "address": shipping_address,
            "line_items": line_items,
            "version": cart_data['version']
        }
        res = bc_order.create_consignment(store, [payload], cart_id)
        if res and res.status_code == 200:
            data = res.json()
            return data['data']
        logger.error(f"Failed to create consignment: {res.status_code} - {res.text}")
    except Exception as e:
        logger.exception("Exception in create_consignment")
    return None

def update_consignment_shipping_option(store, cart_id, shipping_option_id, consignment_id):
    try:
        payload = {"shipping_option_id": shipping_option_id}
        res = bc_order.update_consignment_shipping_option(store, payload, cart_id, consignment_id)
        if res and res.status_code == 200:
            return True
        logger.error(f"Failed to update shipping option: {res.status_code} - {res.text}")
    except Exception as e:
        logger.exception("Exception in update_consignment_shipping_option")
    return False    
            
def set_billing_address(store, billing_address, cart_id):
    try:
        bc_api = store_util.get_bc_api_creds(store)
        api = f"v3/checkouts/{cart_id}/billing-address"
        res = bc.call_api(api_data=bc_api, method="POST", url=api, req_body=billing_address)
        if res and res.status_code == 200:
            return True
        logger.error(f"Failed to set billing address: {res.status_code} - {res.text}")
    except Exception as e:
        logger.exception("Exception in set_billing_address")
    return False

def create_order_from_cart_id(store, cart_id):
    try:
        bc_api = store_util.get_bc_api_creds(store)
        api = f"v3/checkouts/{cart_id}/orders"
        res = bc.call_api(api_data=bc_api, method="POST", url=api)
        if res and res.status_code == 200:
            data = res.json()
            return data['data']
        logger.error(f"Failed to create order from cart ID: {res.status_code} - {res.text}")
    except Exception as e:
        logger.exception("Exception in create_order_from_cart_id")
    return None

def update_order(store, order_id, order_status, payment_method):
    try:
        order_payload = {
            "status_id": order_status,
            "payment_method": payment_method 
        }
        res = bc_order.update_order(store, order_payload, order_id)
        if res and res.status_code == 200:
            return True
        logger.error(f"Failed to update order: {res.status_code} - {res.text}")
    except Exception as e:
        logger.exception("Exception in update_order")
    return False

def check_min_max_rule_for_lineitems(store, line_items):
    try:
        # db = new_mongodb.get_admin_db_client(store)
        bc_api = store_util.get_bc_api_creds(store)
        affected_product_ids = []
        product_ids = [] 
        variant_ids = []
        products = []
        #fetch product info from bc using graphql
        if len(line_items):
            for product_id, v_ids in line_items.items():
                if len(product_ids) < 50:
                    product_ids.append(product_id)
                
                    for variant_id in v_ids:
                        if variant_id:
                            variant_ids.append(variant_id)

                if len(product_ids) == 50 or len(variant_ids) == 250:
                    graphql_query = products_variant_query.get_query([product_ids], [variant_ids])
                    gql_status, gql_res = bc.process_bc_graphql_request(store, graphql_query)
                    if gql_status == 200:
                        for item in gql_res['data']['site']['products']['edges']:
                            products.append(item['node'])    
                    
                    product_ids = []
                    variant_ids = []
            
            if(len(product_ids) > 0 and len(variant_ids) > 0):
                graphql_query = products_variant_query.get_query(product_ids, variant_ids)
                gql_status, gql_res = bc.process_bc_graphql_request(store, graphql_query)
                if gql_status == 200:
                    for item in gql_res['data']['site']['products']['edges']:
                        products.append(item['node'])   
        
        # process the product information if products found
        product_payload = []
        if products:
            for product in products:
                min_purchase_quantity = product['minPurchaseQuantity']
                max_purchase_quantity = product['maxPurchaseQuantity']
                if (max_purchase_quantity and int(max_purchase_quantity) > 0) or (min_purchase_quantity and int(min_purchase_quantity) > 0):
                    exists_product = new_mongodb.fetch_one_document_from_admin_collection(store['id'], 'bulk_product_order_logs', {'product_id': product['entityId']})
                    if exists_product:
                        new_mongodb.delete_documents_from_admin_collection(store['id'], 'bulk_product_order_logs', {'product_id': product['entityId']})
                    
                    product_logs = {
                        'product_id': product['entityId'],
                        'min_purchase_quantity': int(min_purchase_quantity) if min_purchase_quantity else 0,    
                        'max_purchase_quantity': int(max_purchase_quantity) if max_purchase_quantity else 0,
                        'sku': product['sku'],
                        'available_quantity': product['inventory']['aggregated']['availableToSell'],
                        'status': product['availabilityV2']['status'],
                        'variants': product['variants']['edges']
                    }
                    new_mongodb.insert_document_in_admin_collection(store['id'], 'bulk_product_order_logs', product_logs)
                    product_payload.append({'id': int(product['entityId']), 'order_quantity_minimum': 0, 'order_quantity_maximum': 0})
                    affected_product_ids.append(product['entityId'])
        
        # reset the product information in bc if products found
        if len(product_payload) > 0:
            # If the length of product_payload is greater than 10, break it into sets of 10 products
            if len(product_payload) > 10:
                # Split the product_payload into chunks of 10
                for i in range(0, len(product_payload), 10):
                    product_chunk = product_payload[i:i+10]
                    
                    req_data = {
                        "query_params": {},
                        "method": "PUT",
                        "url": "v3/catalog/products",
                        "body": product_chunk
                    }
                    res = bc.process_api(bc_api, req_data)
            else:
                # If the length of product_payload is 10 or less, process it as is
                req_data = {
                    "query_params": {},
                    "method": "PUT",
                    "url": "v3/catalog/products",
                    "body": product_payload
                }
                res = bc.process_api(bc_api, req_data)

        return affected_product_ids
    except Exception as e:
        logger.exception("Exception in check_min_max_rule_for_lineitems")

def check_complex_rules_for_lineitems(store, line_items):
    try:
        db = new_mongodb.get_admin_db_client(store)
        bc_api = store_util.get_bc_api_creds(store)
        affected_product_ids = []
        if len(line_items):
            for product_id, v_ids in line_items.items():
                print(product_id, "product_id")
                response = bc_products.fetch_product_complex_rules(store, product_id)
                if response:
                    complex_rules = response.get('data', [])
                    if complex_rules:
                        print(len(complex_rules), "complex_rules")
                        rules_to_save = []
                        for rule in complex_rules:
                            rule_id = rule.get("id")
                            status = rule.get("enabled")
                            purchasing_disabled = rule.get("purchasing_disabled")
                            if purchasing_disabled and status:
                                rules_to_save.append(rule)

                                if product_id not in affected_product_ids:
                                    affected_product_ids.append(product_id) 
                                
                                url =  f"v3/catalog/products/{product_id}/complex-rules/{rule_id}"
                                req_body = {
                                   "enabled": False
                                }
                                res = bc.call_api(bc_api, "PUT", url, {}, req_body)
                                print(res.status_code, 'res.status_code')
                                print(res.json(), 'res.text')
                                # if res and res.status_code == 200:

                        exists_product = new_mongodb.fetch_one_document_from_admin_collection(store['id'], 'bulk_product_order_logs', {'product_id': int(product_id)})
                        if exists_product:
                            existing_rules = exists_product.get('complex_rules', [])
                            existing_rules.extend(rules_to_save)
                            update_data = {'$set': {'complex_rules': existing_rules}}
                            new_mongodb.update_document_in_admin_collection(store['id'], 'bulk_product_order_logs', {'product_id': int(product_id)}, update_data)
                        else:
                            product_logs = {
                                'product_id': int(product_id),
                                'complex_rules': rules_to_save
                            }
                            new_mongodb.insert_document_in_admin_collection(store['id'], 'bulk_product_order_logs', product_logs)
                                
        return affected_product_ids
    except Exception as e:
        logger.exception("Exception in check_complex_rules_for_lineitems")

def set_min_max_rule_for_lineitems_in_bc(store, product_ids):
    try:
        # db = new_mongodb.get_admin_db_client(store)
        bc_api = store_util.get_bc_api_creds(store)
        req_data = {
                "query_params": {},
                "method": "PUT",
                "url": "v3/catalog/products",
                "body": []
            }
        payload = []
        for product_id in product_ids:
            exists_product = new_mongodb.fetch_one_document_from_admin_collection(store['id'], 'bulk_product_order_logs', {'product_id': product_id})
            if exists_product:
                min_purchase_quantity = exists_product['min_purchase_quantity']
                max_purchase_quantity = exists_product['max_purchase_quantity']
                # complex_rules = exists_product['complex_rules']
                if (max_purchase_quantity and int(max_purchase_quantity) > 0) or (min_purchase_quantity and int(min_purchase_quantity) > 0):
                   #update bulk products in bc
                    data = {
                        "id": int(product_id),
                        "order_quantity_minimum": int(min_purchase_quantity) if min_purchase_quantity else 0,
                        "order_quantity_maximum": int(max_purchase_quantity) if max_purchase_quantity else 0,
                    }
                    payload.append(data)
                    if len(payload) == 10:
                        req_data['body'] = payload
                        res = bc.process_api(bc_api, req_data)
                        if res and res['status_code'] == 200:
                            payload = []
                            req_data['body'] = []
                
                # if len(complex_rules) > 0:
                #     for rule in complex_rules:
                #         rule_id = rule.get("id")
                #         url =  f"v3/catalog/products/{product_id}/complex-rules/{rule_id}"
                #         req_body = {
                #             "prdocut_id": int(product_id),  
                #             "enabled": True
                #         }
                #         res = bc.call_api(bc_api, "PUT", url, {}, req_body)
        
        if len(payload) > 0:
            req_data['body'] = payload
            res = bc.process_api(bc_api, req_data)
        new_mongodb.delete_documents_from_admin_collection(store['id'], 'bulk_product_order_logs', {'product_id': {'$in': product_ids}})

        return True
    except Exception as e:
        logger.exception("Exception in check_min_max_rule_for_lineitems")
        return False