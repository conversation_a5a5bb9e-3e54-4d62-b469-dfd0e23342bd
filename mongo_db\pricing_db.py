import logging
import datetime
from bson import ObjectId
import mongo_db

logger = logging.getLogger()

ASSIGNMENT_COLLECTION = "price_list_assignment"
PRICE_LIST_COLLECTION = "price_list_"

def get_price_list_collection(price_list_id):
    return PRICE_LIST_COLLECTION + str(price_list_id)

def find_price_list_for_customer_group(store, customer_group_id):
    query = {"customer_group_id": int(customer_group_id)}
    db = mongo_db.get_store_db_client(store)
    cur = db[ASSIGNMENT_COLLECTION].find(query)
    price_list = None
    for row in cur:
        price_list = row
    return price_list

def fetch_price_for_skus(store, price_list_id, sku_list):
    price_list = {}
    db = mongo_db.get_store_db_client(store)
    cur = db[get_price_list_collection(price_list_id)].find({"_id": { "$in": sku_list}})
    
    for row in cur:
        price_list[row['_id']] = row

    return price_list

def fetch_customer_price_for_skus(store, customer_group_id, sku_list):
    price_list = {}
    price_list_assignment = find_price_list_for_customer_group(store, customer_group_id)
    if price_list_assignment:
        price_list = fetch_price_for_skus(store, price_list_assignment['_id'], sku_list)
    return price_list