from bson import ObjectId
from new_mongodb import get_admin_db_client_for_store_id, StoreAdminDBCollections
# import datetime
from datetime import datetime
import logging
from fields.cms_fields import cms_fields_navigation

from bson import ObjectId
import mongo_db
import new_mongodb
from utils.common import parse_json, processList, processListCategory


logger = logging.getLogger()

CMS_COLLECTION = "cms"
SUB_NAVIGATION_COLLECTION = "sub_navigations"
WEB_PAGES="web_pages"
DYNAMIC_PAGES="pages"
BRANDS_COLLECTION = "cms_brands"
BLOGS_COLLECTION = "blogs"
BLOGS_AUTHOR_COLLECTION = "blogs_author"
BLOGS_CATEGORIES_COLLECTION = "blogs_category"
BRANDS_PURCHASER_MAPPING_COLLECTION = "brands_purchaser_mapping"


def create_webpage(store, payload):
    db = new_mongodb.get_store_db_client(store)
    result=db[DYNAMIC_PAGES].insert_one(payload)
    return result

def delete_webpage(store, webpage_id):
    db = new_mongodb.get_store_db_client(store)
    result=db[DYNAMIC_PAGES].delete_one({"_id": ObjectId(str(webpage_id))})
    return result

def get_page_by_id(store, id):
    db = new_mongodb.get_store_db_client(store)
    result= db[DYNAMIC_PAGES].find_one({"_id": ObjectId(id)})
    return result

def get_page_by_bc_id(store, id):
    db = new_mongodb.get_store_db_client(store)
    result= db[DYNAMIC_PAGES].find_one({"bc_id": int(id)})
    return result

def update_webpage(store, query, payload):
    db = new_mongodb.get_store_db_client(store)
    result=db[DYNAMIC_PAGES].update_one(query, payload)
    return result


# blogs
def create_blogs(store, payload):
    db = new_mongodb.get_store_db_client(store)
    result=db[BLOGS_COLLECTION].insert_one(payload)
    return result

def get_all_blogs(store):
    db = new_mongodb.get_store_db_client(store)
    result= db[BLOGS_COLLECTION].find()
    return result

def get_blog_by_id(store, id):
    db = new_mongodb.get_store_db_client(store)
    result= db[BLOGS_COLLECTION].find_one({"_id": ObjectId(id)})
    return result

def get_blog_by_name(store, name):
    db = new_mongodb.get_store_db_client(store)
    result= db[BLOGS_COLLECTION].find_one({"name": name})
    return result

def update_blog(store, query, payload):
    db = new_mongodb.get_store_db_client(store)
    result=db[BLOGS_COLLECTION].update_one(query, payload)
    return result

def delete_blog_by_id(store, blog_id):
    db = new_mongodb.get_store_db_client(store)
    doc = db[BLOGS_COLLECTION].delete_one({"_id": ObjectId(str(blog_id))})
    return doc

#blogs authors
def get_all_blogs_author(store):
    db = new_mongodb.get_store_db_client(store)
    result= db[BLOGS_AUTHOR_COLLECTION].find()
    return result

def get_blog_author_by_id(store, id):
    db = new_mongodb.get_store_db_client(store)
    result= db[BLOGS_AUTHOR_COLLECTION].find_one({"_id": ObjectId(id)})
    return result

def create_blogs_author(store, payload):
    db = new_mongodb.get_store_db_client(store)
    result=db[BLOGS_AUTHOR_COLLECTION].insert_one(payload)
    return result

def update_blog_author(store, query, payload):
    db = new_mongodb.get_store_db_client(store)
    result=db[BLOGS_AUTHOR_COLLECTION].update_one(query, payload)
    return result

def delete_author_by_id(store, author_id):
    db = new_mongodb.get_store_db_client(store)
    doc = db[BLOGS_AUTHOR_COLLECTION].delete_one({"_id": ObjectId(str(author_id))})
    return doc

#blogs categories
def get_all_blogs_category(store):
    db = new_mongodb.get_store_db_client(store)
    result= db[BLOGS_CATEGORIES_COLLECTION].find()
    return result

def get_blog_category_by_id(store, id):
    db = new_mongodb.get_store_db_client(store)
    result= db[BLOGS_CATEGORIES_COLLECTION].find_one({"_id": ObjectId(id)})
    return result

def create_blogs_category(store, payload):
    db = new_mongodb.get_store_db_client(store)
    result=db[BLOGS_CATEGORIES_COLLECTION].insert_one(payload)
    return result

def update_blog_category(store, query, payload):
    db = new_mongodb.get_store_db_client(store)
    result=db[BLOGS_CATEGORIES_COLLECTION].update_one(query, payload)
    return result

def delete_blog_category_by_id(store, category_id):
    db = new_mongodb.get_store_db_client(store)
    doc = db[BLOGS_CATEGORIES_COLLECTION].delete_one({"_id": ObjectId(str(category_id))})
    return doc

def get_category_by_id(store, id):
    db = new_mongodb.get_admin_db_client(store)
    result= db[CMS_COLLECTION].find_one({"_id": ObjectId(id)})
    return result

def get_category_by_bc_id(store, id):
    db = new_mongodb.get_admin_db_client(store)
    result= db[CMS_COLLECTION].find_one({"id": int(id)})
    return result

def get_brand_by_bc_id(store, id):
    db = new_mongodb.get_admin_db_client(store)
    result= db[BRANDS_COLLECTION].find_one({"id": int(id)})
    return result

def get_brand_by_id(store, id):
    db = new_mongodb.get_admin_db_client(store)
    result= db[BRANDS_COLLECTION].find_one({"_id": ObjectId(str(id))})
    return result

def get_all_categories(store):
    db = new_mongodb.get_admin_db_client(store)
    result= db[CMS_COLLECTION].find()
    return result

def get_all_webpages(store):
    db = new_mongodb.get_store_db_client(store)
    result= db[DYNAMIC_PAGES].find()
    return result

def get_all_brands(store):
    db = new_mongodb.get_admin_db_client(store)
    result= db[BRANDS_COLLECTION].find()
    return result

def delete_brand_by_id(store, brand_id):
    db = new_mongodb.get_admin_db_client(store)
    doc = db[BRANDS_COLLECTION].delete_one({'id': int(brand_id)})

def get_page_by_name(store, url):
    db = new_mongodb.get_admin_db_client(store)
    result= db[CMS_COLLECTION].find_one({"url": url},{"id":1})
    return result

def delete_category_by_id(store, category_id):
    db = new_mongodb.get_admin_db_client(store)
    doc = db[CMS_COLLECTION].delete_one({'id': int(category_id)})

def processDocumentCms(obj):
        if obj:
            if 'id' in obj:
                obj['id']=obj['id']
            if '_id' in obj:
                # obj['id'] = str(obj['_id'])
                del obj['_id']
        
            for key, value in obj.items():
                if isinstance(value, datetime) or isinstance(value, ObjectId):
                    obj[key] = str(value)

        return obj

def processListCms(data):
        result = []
        if data:
            for _obj in data:
                result.append(processDocumentCms(_obj))
        return result

def processDocumentPages(obj):
        if obj:
            if 'bc_id' in obj:
                obj['id']=obj['bc_id']
                del obj['bc_id']
            else:
                 obj['id']=str(obj['_id'])
            if '_id' in obj:
                # obj['id'] = str(obj['_id'])
                del obj['_id']
        
            for key, value in obj.items():
                if isinstance(value, datetime) or isinstance(value, ObjectId):
                    obj[key] = str(value)

        return obj

def processListPages( data):
        result = []
        if data:
            for _obj in data:
                result.append(processDocumentPages(_obj))
        return result

def get_categories(store, payload, fields):
        db = new_mongodb.get_admin_db_client(store)
            
        def get_query(search_result):
            # return all products if search params is empty.
            if search_result == "":
                return {"is_visible": True}

            # return text based search if search param's exists.        
            return {"$or": [{'name':{"$regex": search_result, "$options": "i"}}]}                                  

        def set_limit(limit):
            page_limit = 0
            skips = 0
            
            if int(limit) != 0 and int(limit) > 0:
                page_limit = int(limit)

            return page_limit, skips
        
        page_limit , skips = set_limit(payload['limit'] if 'limit' in payload else 0)
        
        query = get_query(payload["search"]) if "search" in payload else {"is_visible": True} 
        result= db[CMS_COLLECTION].find(query, fields).skip(skips).limit(page_limit)
        result=processListCms(result)
        return result

def get_webpages(payload,fields,store):
        db = new_mongodb.get_store_db_client(store)
            
        def get_query(search_result):
            # return all products if search params is empty.
            if search_result == "":
                return {"is_visible": True}

            # return text based search if search param's exists.        
            return {"$or": [{'name':{"$regex": search_result, "$options": "i"}}]}                                  

        def set_limit(limit):
            page_limit = 0
            skips = 0
            
            if int(limit) != 0 and int(limit) > 0:
                page_limit = int(limit)

            return page_limit, skips
        
        page_limit , skips = set_limit(payload['limit'] if 'limit' in payload else 0)
        
        query = get_query(payload["search"]) if "search" in payload else {"is_visible": True} 
        result= db[DYNAMIC_PAGES].find(query, fields).skip(skips).limit(page_limit)
        result=processListPages(result)
        return result 

def create(store, payload):
    db = new_mongodb.get_admin_db_client(store)
    result=db[CMS_COLLECTION].insert_one(payload)
    return result

def update(store, query, payload):
    db = new_mongodb.get_admin_db_client(store)
    result=db[CMS_COLLECTION].update_one(query, payload)
    return result

def update_brands(store, query, payload):
    db = new_mongodb.get_admin_db_client(store)
    result=db[BRANDS_COLLECTION].update_one(query, payload)
    return result

def has_categories_with_parent_id(store, parent_id):
    db = new_mongodb.get_admin_db_client(store)
    result= db[CMS_COLLECTION].find_one({"parent_id": int(parent_id)})
    return result is not None

def add_has_sub_child_flag(store, id, has_sub_child):
    db = new_mongodb.get_admin_db_client(store)
    if id:
        db[CMS_COLLECTION].update_one(
            {"_id":ObjectId(id)},
            {"$set": {"has_sub_child": has_sub_child}}
        )
    
def get_categories_by_parent_id(store, parent_id):
    db = new_mongodb.get_admin_db_client(store)
    pipeline = [
        {"$match": {"parent_id": int(parent_id)}},  # Filter documents by parent_id
        {"$addFields": {"versions": {"$filter": {
            "input": "$versions",  # Input array to filter
            "as": "version",  # Variable name for each element
            "cond": {"$eq": ["$$version.status", "active"]}  # Condition to filter by status
        }}}},
        {"$project": {"_id":1,"id":1,"name":1,"created_at":1,"updated_at":1,"url":1,"status":1,"is_visible":1,"parent_id":1,"views":1,"sort_order":1,"type":1,"is_customers_only":1,"versions": "$versions","has_sub_child":1}}  # Project only the active_versions field
    ]
    result= db[CMS_COLLECTION].aggregate(pipeline)
    result=processListCategory(result)
    return result

def get_sub_nav(store):
    db = new_mongodb.get_store_db_client(store)
    result=db[SUB_NAVIGATION_COLLECTION].find()
    return result

def update_sub_nav_by_id(store, data):
    customer_id = data['id']
    db = new_mongodb.get_store_db_client(store)
    doc = db[SUB_NAVIGATION_COLLECTION].update_one({"_id": ObjectId(customer_id)}, { "$set": data }, upsert=True)

def get_webpage_by_name(store,url):
    db = new_mongodb.get_store_db_client(store)
    result= db[WEB_PAGES].find_one({"url": url},{"id":1})
    return result

def get_brand_by_name(store,url):
    db = new_mongodb.get_store_db_client(store)
    result= db[BRANDS_COLLECTION].find_one({"custom_url.url": url},{"id":1})
    return result

def get_paginated_records_updated(store, payload, fields, additionalQuery):
        
        db = new_mongodb.get_store_db_client(store)
        collection = db[DYNAMIC_PAGES]
        sort_by = payload.get('sort_by', 'date_created')  # Default sort_by to 'date_created' if not provided
        sort_order = 1 if payload.get('sort_order', 'asc') == 'asc' else -1  # Default sort_order to ascending if not provided

        def create_reg_ex_query(filterBy, filter):
            query = {"$or": []}

            for i in filterBy:
                query['$or'].append({i: {"$regex": filter, "$options": "i"}})

            if "type" not in query and "type" in payload:
                query["type"] = payload["type"]

            if "status" in payload:
                if payload["status"] == "active":
                    query["is_visible"] = True
                elif payload["status"] == "inactive":
                    query["is_visible"] = False
                elif payload["status"] == "out_of_stock":
                    query["inventory_level"] = 0

            query.update(additionalQuery)
            return query

        limit = int(payload.get("limit", 10))
        page = int(payload.get("page", 1))
        skips = payload.get('skips', 0)

        query = create_reg_ex_query(payload.get("filterBy", []), payload.get('filter', ''))
        skips = limit * (page - 1)
        # Aggregation pipeline to sort the documents with empty values for the specified field at the end
        aggregation_pipeline = [
            {"$match": query},  # Match documents based on the query
            {"$addFields": {sort_by + "_exists": {"$ne": ["$" + sort_by, ""]}}},  # Add a field to indicate whether sort_by exists
            {"$addFields": {sort_by + "_is_empty": {"$eq": ["$" + sort_by, ""]}}},  # Add a field to indicate whether sort_by is empty
            {"$sort": {sort_by + "_is_empty": 1, sort_by: sort_order}},  # Sort documents based on sort_by and sort_by_is_empty
            {"$skip": skips},  # Skip documents
            {"$limit": limit},  # Limit the number of documents returned
            {"$project": {sort_by + "_exists": 0, sort_by + "_is_empty": 0}}  # Exclude the added fields from the final result
        ]

        data = collection.aggregate(aggregation_pipeline)

        # ProcessList ...
        data = processList(data)

        document_length = collection.count_documents(query)

        return parse_json(data), document_length, page, limit
