import logging
from datetime import datetime, timezone
import new_utils
import task
from utils.common import conver_to_json, paginate_data_postgres, parse_json, calculatePaginationData,get_six_month_data_using_sku, get_table_record_count, get_search_query, get_six_month_data_using_parent_sku, get_month_array_for_meta, convert_to_timestamp, get_day_array_for_meta
import new_pgdb
from sqlalchemy import text
import traceback
import os
from flask import send_file, make_response
from new_mongodb import StoreAdminDBCollections, get_admin_db_client_for_store_id, fetch_one_document_from_admin_collection, get_store_db_client_for_store_id
from new_mongodb import store_admin_db
from products.all_products import products_list
from new_utils import store_util
from utils import bc
from new_pgdb.analytics_db import AnalyticsDB
from appconfig import is_pgdb_read_only_enabled
import json
import time
import pytz
from dateutil import parser

logger = logging.getLogger()

# PG_CONN_STRING = '******************************************************************/stage'  # production
skuvault_catalog = ['sku', 'part_number', 'title', 'cost', 'quantity_available', 'quantity_incoming',
                    'incremental_quantity', 'reorder_point', 'brand', 'classification', 'primary_supplier', 'created_date', 'modified_date', 'parent_sku']
analytics_variants_trend_array = ['quantity', 'month_1', 'month_2', 'month_3', 'month_4', 'month_5', 'month_6', 'month_7'
                                  'order_month', 'order_year', 'order_date_time', 'total_sold', 'total_sold_30', 'suggested_order_qty', 'suggested_order_qty_45', 'suggested_order_qty_60', 'weeks_on_hand', 'turn_rate', 'item_order_qty', 'to_order_qty']


def get_replenishment_filters(store, body):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        primary_supplier_arr = body['primary_supplier']
        filters = ['brand', 'classification', 'primary_supplier']
        result = {}
        user_name = body['user']
        hide_discontinued = body['hide_discontinued']

        if hide_discontinued == 'true':
            hide_discontinued = True
        else:
            hide_discontinued = False
        
        if len(primary_supplier_arr) == 0 and user_name != '':
            user_supplier_query = """
                SELECT suppliers
                FROM user_supplier_mapping
                WHERE user_name = :user_name;
            """
            rs = conn.execute(text(user_supplier_query), {'user_name': user_name})

            primary_supplier_arr = []
            for row in rs:
                primary_supplier_arr.append(row[0])

        if len(primary_supplier_arr) == 0:
            for filter in filters:
                if hide_discontinued == True and filter == 'classification':
                    query = "SELECT distinct(" + filter + ") from skuvault_catalog WHERE (" + filter + ") NOT LIKE '%Discontinued%'"
                else:
                    query = "SELECT distinct(" + filter + ") from skuvault_catalog"
                
                rs = conn.execute(text(query))
                res = []
                
                for row in rs:
                    res.append(row[0])

                result[filter] = res
        else:
            # data cleaning...
            primary_supplier_arr = [value.strip()
                                    for value in primary_supplier_arr]  # Trim each value
            
            for filter in filters:
                query = ''
                if filter != 'primary_supplier':
                    if filter == 'classification' and hide_discontinued == True:
                        query = "SELECT distinct (" + \
                            filter + \
                            ") from skuvault_catalog WHERE primary_supplier = ANY(:suppliers) AND (" + filter + ") NOT LIKE '%Discontinued%'"
                    else:                            
                        query = "SELECT distinct (" + \
                            filter + \
                            ") from skuvault_catalog WHERE primary_supplier = ANY(:suppliers)"
                    
                    rs = conn.execute(text(query), {"suppliers": primary_supplier_arr})
                    
                    res = []
                    
                    for row in rs:
                        res.append(row[0])

                    result[filter] = res
                else:
                    result[filter] = primary_supplier_arr
        query = f"""
            SELECT DISTINCT EXTRACT(YEAR FROM p.date_created) AS year
            FROM {AnalyticsDB.get_replenishment_products_table()} rp
            LEFT JOIN products p ON rp.product_id = p.product_id
            WHERE rp.product_id IS NOT NULL 
            ORDER BY year;"""       
        year_result = conn.execute(text(query))
        years = []
        for row in year_result:
            year = row[0]
            if year is not None:
                years.append(int(year))
        classified_as_query = """
            SELECT id, name
            FROM replenishment_classified_as
            ORDER BY created_at DESC;
        """
        classified_as_result = conn.execute(text(classified_as_query))
        classified_as_list = [{
            "id": "Unassigned",
            "name": "Unassigned"
        }]
        for row in classified_as_result:
            classified_as_list.append({
                "id": row[0],
                "name": row[1]
            })
        
        response['message'] = 'data retrived successfully'
        response['data'] = parse_json(result)
        response['data']['years'] = years
        response['data']['classified_as'] = classified_as_list
        response['status'] = 200
    finally:
        conn.close()

    return response

def get_replenishment_aggregate_data(store, query_params):
    response = {
        "status": 400,
    }
    limit = int(query_params.get('limit', 20))
    page = int(query_params.get('page', 1))
    offset = (page - 1) * limit
    query_brand = query_params.get('brand','').strip()
    query_classification = query_params.get('classification','').strip()
    query_tags = query_params.get('tags','').strip()
    hide_discontinued = query_params.get('hide_discontinued', 'true')    
    show_zero_qty_products = query_params.get('show_zero_qty_products', 'true')    
    query_primary_supplier = query_params.get('primary_supplier', None) 
    sort_by = query_params.get('sort_by','').strip()
    sort_array = sort_by.split("/") if sort_by != '' else []
    search_key = query_params.get('searchKey', '').strip()
    search_value = query_params.get('searchValue', None)
    if query_primary_supplier and query_primary_supplier.strip() != '':
        query_primary_supplier = query_primary_supplier.split(";")
    else:
        query_primary_supplier = []   
    user = query_params.get('user', None)

    sale_history_months = query_params.get('sale_history_months', 6)
    days_to_replenishment = query_params.get('days_to_replenishment', 30)
    no_sold_days = query_params.get('no_sold_days', False)
    classified_as = query_params.get('classified_as', None)

    column_query = ''   
    
    if days_to_replenishment:
        column_query = column_query + 'sum(rp.to_order_qty_' + str(days_to_replenishment) + ')'
        column_query = column_query + ', sum(rp.total_sold_' + str(days_to_replenishment) + ')'
        column_query = column_query + ', sum(rp.suggested_order_qty_' + str(days_to_replenishment) + ') AS suggested_order_qty'        


    if sale_history_months:
        for i in range(int(sale_history_months), 0, -1):
            index = i + 1
            column_query = column_query + ', sum(rp.month_' + str(index) + ')'

    append_query = ''
    
    if query_brand != '':
        values_list = query_brand.split(';')
        trimmed_values_list = [value.strip()
                                for value in values_list]  # Trim each value 
        formatted_values = "','".join(trimmed_values_list)
        formatted_values = f"'{formatted_values}'"
        append_query = ' AND sv.brand IN(' + formatted_values + ')'

    if query_classification != '':
        values_list = query_classification.split(';')
        trimmed_values_list = [value.strip() for value in values_list]  # Trim spaces

        # Escape single quotes inside classification names if necessary
        escaped_values = [v.replace("'", "''") for v in trimmed_values_list]

        # Build a string like: 'value1','value2','value3'
        formatted_values = "','".join(escaped_values)

        # Build the SQL array literal with type cast
        append_query += f"""
        AND (
            ca.classifications && ARRAY['{formatted_values}']::varchar[]
        )
        """
   
    if search_value and search_value != '':
        append_query = append_query + " AND  (rp.product_title ILIKE '%" + search_value + "%' or rp.parent_sku ILIKE '%" + search_value + "%') "     

    if hide_discontinued == 'true':        
        append_query = append_query + " AND ((LOWER(rp.product_title) NOT ILIKE '%discontinued%') AND (LOWER(rp.product_title) NOT ILIKE '%discountinued%')) AND ((LOWER(sv.classification) NOT ILIKE '%discontinued%') AND (LOWER(sv.classification) NOT ILIKE '%discountinued%'))"

    if show_zero_qty_products == 'false':
        append_query = append_query + " AND rp.quantity_available > 0"
        

    if no_sold_days == 'true':
        append_query = append_query + " AND rp.total_sold_" + str(days_to_replenishment) + " = 0"

    join_query = "LEFT JOIN"
    append_conditions = []
    if classified_as:
        parts = [part.strip() for part in classified_as.split(",")]
        ids = [int(part) for part in parts if part.isdigit()]
        
        if "Unassigned" in parts:
            append_conditions.append("rd.classified_as_id IS NULL")
            join_query = "INNER JOIN"  # As per your original logic

        if ids:
            placeholders = ','.join(map(str, ids))
            append_conditions.append(f"rd.classified_as_id IN ({placeholders})")

        if append_conditions:
            append_query += " AND (" + " OR ".join(append_conditions) + ")"


    order_by = ""
    if len(sort_array):
        sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'  
        nulls_order = "NULLS FIRST" if sort_direction == "ASC" else "NULLS LAST"
        if sort_array[0] == 'total_return_quantity':
            sort_array[0] = 'total_zero_quantity'
        order_by = " ORDER BY " + sort_array[0] + " " + sort_direction + " " + nulls_order

    month_names, day_difference = get_month_array_for_meta(sale_history_months)    
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        if len(query_primary_supplier) == 0 and user:
            query = f"SELECT suppliers FROM user_supplier_mapping where user_name='{user.strip()}'"
            rs = conn.execute(text(query))

            for _supplier in rs:
                query_primary_supplier.append(_supplier[0])
            if len(query_primary_supplier) == 0:
                response['message'] = 'Not found'
                response['data'] = {'data':[]}
                response['status'] = 200
                return response 
                           
        if len(query_primary_supplier):
            trimmed_values_list = [value.strip() for value in query_primary_supplier]  # Trim spaces

            # Escape single quotes inside classification names if necessary
            escaped_values = [v.replace("'", "''") for v in trimmed_values_list]

            # Build a string like: 'value1','value2','value3'
            formatted_values = "','".join(escaped_values)
            append_query += f"""
            AND (
                ca.primary_suppliers && ARRAY['{formatted_values}']::varchar[]
            )
            """
            # Add the suppliers parameter to the query parameters
            query_params = {'suppliers': query_primary_supplier}

        final_count_query = ''            
        if query_tags != '':
            values_list = query_tags.split(',')
            trimmed_values_list = [value.strip() for value in values_list]  # Trim each value
            formatted_values = "','".join(trimmed_values_list)
            formatted_values = f"'{formatted_values}'"             
            final_count_query = f"""WITH classifications_agg AS (
                                    SELECT
                                        sc.parent_sku,
                                        ARRAY_AGG(DISTINCT COALESCE(rc.new_classification, sc.classification)) AS classifications,
                                        ARRAY_AGG(DISTINCT sc.primary_supplier) AS primary_suppliers
                                    FROM skuvault_catalog sc
                                    LEFT JOIN replenishment_classifications rc ON sc.sku = rc.sku
                                    WHERE sc.parent_sku IS NOT NULL
                                    GROUP BY sc.parent_sku
                                )
                                SELECT COUNT(*) FROM (select rp.parent_sku, ca.primary_suppliers, ca.classifications, max(sv.brand), p.out_of_stock_date, rp.total_rtv_quantity, rp.total_return_quantity AS total_zero_quantity from {AnalyticsDB.get_replenishment_products_table()} rp left join product_tags pt ON rp.parent_sku = pt.sku left join products p ON rp.parent_sku = p.sku left join skuvault_catalog sv ON sv.parent_sku = rp.parent_sku {join_query} replenishment_dashboard rd ON rp.parent_sku = rd.parent_sku LEFT JOIN classifications_agg ca ON rp.parent_sku = ca.parent_sku where pt.sku = rp.parent_sku AND pt.tag_id IN {formatted_values} AND rp.product_title <> '' AND sv.primary_supplier <> 'Unknown' {append_query} group by rp.parent_sku, ca.primary_suppliers, ca.classifications, p.out_of_stock_date, rd.classified_as_id, rd.classified_as, rp.total_rtv_quantity, rp.total_return_quantity) AS subquery
                                """                                             
        else:
            final_count_query = f"""WITH classifications_agg AS (
                                    SELECT
                                        sc.parent_sku,
                                        ARRAY_AGG(DISTINCT COALESCE(rc.new_classification, sc.classification)) AS classifications,
                                        ARRAY_AGG(DISTINCT sc.primary_supplier) AS primary_suppliers
                                    FROM skuvault_catalog sc
                                    LEFT JOIN replenishment_classifications rc ON sc.sku = rc.sku
                                    WHERE sc.parent_sku IS NOT NULL
                                    GROUP BY sc.parent_sku
                                )
                                SELECT COUNT(*) FROM (select rp.parent_sku, ca.primary_suppliers, max(sv.brand), p.out_of_stock_date, rp.total_rtv_quantity, rp.total_return_quantity AS total_zero_quantity from {AnalyticsDB.get_replenishment_products_table()} rp left join products p ON rp.parent_sku = p.sku left join skuvault_catalog sv ON sv.parent_sku = rp.parent_sku {join_query} replenishment_dashboard rd ON rp.parent_sku = rd.parent_sku LEFT JOIN classifications_agg ca ON rp.parent_sku = ca.parent_sku where rp.product_title <> '' AND sv.primary_supplier <> 'Unknown' {append_query } group by rp.parent_sku, ca.primary_suppliers,ca.classifications, p.out_of_stock_date, rd.classified_as_id, rd.classified_as, rp.total_rtv_quantity, rp.total_return_quantity) AS subquery
                                """         
        rs = conn.execute(text(final_count_query), {"suppliers": query_primary_supplier})
        total_count = int(rs.scalar())

        base_query = ''
        if query_tags != '':
            values_list = query_tags.split(',')
            trimmed_values_list = [value.strip() for value in values_list]  # Trim each value
            formatted_values = "','".join(trimmed_values_list)
            formatted_values = f"'{formatted_values}'"
            base_query = f"""WITH consolidated_skuvault AS (
                                SELECT DISTINCT primary_supplier, classification, max(brand) as brand, parent_sku
                                FROM skuvault_catalog GROUP BY parent_sku, primary_supplier, classification
                            ),
                            classifications_agg AS (
                                SELECT
                                    sc.parent_sku,
                                        ARRAY_AGG(DISTINCT COALESCE(rc.new_classification, sc.classification)) AS classifications,
                                        ARRAY_AGG(DISTINCT sc.primary_supplier) AS primary_suppliers
                                FROM skuvault_catalog sc
                                LEFT JOIN replenishment_classifications rc ON sc.sku = rc.sku
                                WHERE sc.parent_sku IS NOT NULL
                                GROUP BY sc.parent_sku
                            )
                            select MAX(rp.product_title) AS product_title, MAX(rp.product_id) AS product_id, rp.parent_sku, ca.primary_suppliers, ca.classifications, MAX(sv.brand) AS brand, MAX(rp.cost) AS cost, MAX(rp.retail_price) AS retail_price, MAX(rp.sale_price) AS sale_price, MAX(rp.reorder_point) AS reorder_point, MAX(rp.incremental_quantity) AS incremental_quantity,
                            sum(DISTINCT rp.quantity_on_hand), sum(DISTINCT rp.quantity_pending), sum(DISTINCT rp.quantity_incoming), sum(DISTINCT rp.quantity_available), sum(DISTINCT rp.quantity_on_hold), 
                            sum(DISTINCT rp.suggested_order_qty_45) AS suggested_order_qty_45, sum(DISTINCT rp.suggested_order_qty_60) AS suggested_order_qty_60, sum(DISTINCT rp.month_1), p.out_of_stock_date, rp.out_of_stock_date AS last_out_of_stock_date, rp.out_of_stock_end_date AS last_received_date, rp.oldest_expiry_date AS expiry_date, rd.classified_as_id, rd.classified_as, rp.total_rtv_quantity, rp.total_return_quantity AS total_zero_quantity, {column_query}
                            from {AnalyticsDB.get_replenishment_products_table()} rp left join product_tags pt ON rp.parent_sku = pt.sku left join products p ON rp.parent_sku = p.sku
                            left join consolidated_skuvault sv ON sv.parent_sku = rp.parent_sku
                            left join replenishment_dashboard rd ON rp.parent_sku = rd.parent_sku
                            left join classifications_agg ca ON rp.parent_sku = ca.parent_sku
                            where pt.tag_id IN ({formatted_values}) AND sv.primary_supplier <> 'Unknown' {append_query}              
                            group by rp.parent_sku, ca.primary_suppliers, ca.classifications, p.out_of_stock_date, rp.out_of_stock_date, rp.out_of_stock_end_date, rp.oldest_expiry_date, rd.classified_as_id, rd.classified_as, rp.total_rtv_quantity, rp.total_return_quantity
                            {order_by}"""            
        else: 
            base_query = f"""WITH consolidated_skuvault AS (
                                SELECT DISTINCT primary_supplier, classification, max(brand) as brand, parent_sku
                                FROM skuvault_catalog GROUP BY parent_sku, primary_supplier, classification
                            ),
                            classifications_agg AS (
                                SELECT
                                    sc.parent_sku,
                                        ARRAY_AGG(DISTINCT COALESCE(rc.new_classification, sc.classification)) AS classifications,
                                        ARRAY_AGG(DISTINCT sc.primary_supplier) AS primary_suppliers
                                FROM skuvault_catalog sc
                                LEFT JOIN replenishment_classifications rc ON sc.sku = rc.sku
                                WHERE sc.parent_sku IS NOT NULL
                                GROUP BY sc.parent_sku
                            )
                            select MAX(rp.product_title) AS product_title, MAX(rp.product_id) AS product_id, rp.parent_sku, ca.primary_suppliers, ca.classifications, MAX(sv.brand) AS brand, MAX(rp.cost) AS cost, MAX(rp.retail_price) AS retail_price, MAX(rp.sale_price) AS sale_price, MAX(rp.reorder_point) AS reorder_point, MAX(rp.incremental_quantity) AS incremental_quantity,
                            sum(DISTINCT rp.quantity_on_hand), sum(DISTINCT rp.quantity_pending), sum(DISTINCT rp.quantity_incoming), sum(DISTINCT rp.quantity_available), sum(DISTINCT rp.quantity_on_hold), 
                            sum(DISTINCT rp.suggested_order_qty_45) AS suggested_order_qty_45, sum(DISTINCT rp.suggested_order_qty_60) AS suggested_order_qty_60, sum(DISTINCT rp.month_1), p.out_of_stock_date, rp.out_of_stock_date AS last_out_of_stock_date, rp.out_of_stock_end_date AS last_received_date, rp.oldest_expiry_date AS expiry_date, rd.classified_as_id, rd.classified_as, rp.total_rtv_quantity, rp.total_return_quantity AS total_zero_quantity, {column_query} 
                            from {AnalyticsDB.get_replenishment_products_table()} rp left join products p ON rp.parent_sku = p.sku
                            left join consolidated_skuvault sv ON sv.parent_sku = rp.parent_sku
                            left join replenishment_dashboard rd ON rp.parent_sku = rd.parent_sku 
                            left join classifications_agg ca ON rp.parent_sku = ca.parent_sku
                            where sv.primary_supplier <> 'Unknown' {append_query}              
                            group by rp.parent_sku, ca.primary_suppliers, ca.classifications, p.out_of_stock_date, rp.out_of_stock_date, rp.out_of_stock_end_date, rp.oldest_expiry_date, rd.classified_as_id, rd.classified_as, rp.total_rtv_quantity, rp.total_return_quantity
                            {order_by}"""               
    
        final_query = base_query +  " OFFSET " + str(offset) + " LIMIT " + str(limit)
        query_result = conn.execute(text(final_query), {"suppliers": query_primary_supplier})

        formated_data = [] 
        sku_count = 1
        parent_sku_str = ''                     
        if query_result:         
            for data in query_result:
                query_unique_classifications = """
                    SELECT STRING_AGG(DISTINCT COALESCE(rc.new_classification, sc.classification), ', ') AS classifications
                    FROM skuvault_catalog sc
                    LEFT JOIN replenishment_classifications rc ON sc.sku = rc.sku
                    WHERE sc.parent_sku = :parent_sku;
                """

                result = conn.execute(text(query_unique_classifications), {"parent_sku": data[2]})
                classifications = result.scalar()  # This will return the comma-separated string of classifications

                obj = {}   
                parent_sku_str = parent_sku_str + "'" + str(data[2]) + "', " 
                obj["product_title"] = data[0]
                obj["parent_sku"] = data[2]                
                obj["primary_supplier"] = data[3]
                obj["classification"] = data[4]
                # obj["unique_classification"] = classifications
                obj["brand"] = data[5]
                obj["cost"] = float(data[6])
                obj["retail_price"] = float(data[7])
                obj["sale_price"] = float(data[8])
                # obj["reorder_point"] = float(data[9])
                obj["incremental_quantity"] = int(data[10])
                obj["quantity_on_hand"] = int(data[11])
                obj["quantity_pending"] = int(data[12])
                obj["quantity_incoming"] = int(data[13])
                obj["quantity_available"] = int(data[14])
                obj["quantity_on_hold"] = int(data[15])                                         
                obj["suggested_order_qty_45"] = int(data[16])
                obj["suggested_order_qty_60"] = int(data[17])                
                obj["month_1"] = int(data[18]) if data[18] != None else 'NA'                              
                obj["to_order_qty"] = int(data[27])
                obj["total_sold_30"] = int(data[28]) if data[28] != None else 0 
                obj["suggested_order_qty"] = int(data[29])  
                obj["case_qty"] = "-"
                out_of_stock_date = data[19]
                obj["last_out_of_stock_date"] = convert_to_timestamp(data[20])
                obj["last_received_date"] = convert_to_timestamp(data[21])
                obj["classified_as_id"] = data[23]
                obj["classified_as"] = data[24]
                obj["total_rtv_quantity"] = data[25]
                obj["total_zero_quantity"] = data[26]

                expiry = data[22]  # e.g., "2029-04"
                try:
                    parsed_date = datetime.strptime(expiry, "%Y-%m")
                    obj["expiry_date"] = parsed_date.strftime("%b-%Y").upper()  # "APR-2029"
                except Exception as e:
                    obj["expiry_date"] = expiry  # fallback if format is invalid

                if out_of_stock_date:
                    if isinstance(out_of_stock_date, datetime):
                        # Make datetime.now() timezone-aware
                        now = datetime.now(timezone.utc) if out_of_stock_date.tzinfo else datetime.now()
                        days_out_of_stock = (now - out_of_stock_date).days
                    else:
                        days_out_of_stock = 0  # Handle cases where the date is invalid or cannot be parsed
                else:
                    days_out_of_stock = 0  # Set to 0 if no out_of_stock_date

                obj["days_out_of_stock"] = days_out_of_stock             

                count = 1    
                total_sum = 0                                                         
                for i in range(int(sale_history_months), 0, -1):
                    month_index = i + 1
                    key = "month_" + str(month_index)
                    value_key = 29 + count                                       
                    obj[key] = int(data[value_key]) if data[value_key] != None else 'NA'
                    total_sum = total_sum + int(data[value_key]) if data[value_key] != None else 0
                    count = count+1 
                                    
                turn_rate = (((int(total_sum) / int(data[14]) if int(data[14]) != 0 else 1) * 365) / day_difference)                           
                weeks_on_hand = 52 / turn_rate if turn_rate != 0 else 1 
                obj["total_sold"] = total_sum 
                obj["turn_rate"] = float(turn_rate)
                obj["weeks_on_hand"] = float(weeks_on_hand)
                obj["days_to_replenishment"] = days_to_replenishment
                obj['sale_history_months'] = sale_history_months
                obj['child_data'] = []

                formated_data.append(obj)                                                                                                  
                                    
            paginated_rows, current_page, total_pages, total_items = paginate_data_postgres(
                total_count, formated_data, page, limit)
            
            data = new_utils.calculate_pagination(
                paginated_rows, current_page, limit, total_items)  

            parent_sku_str = parent_sku_str.rstrip(', ')
            
            child_data_array = get_on_load_child_data(store, parent_sku_str, '', sale_history_months, days_to_replenishment, hide_discontinued, query_tags)
            
            if child_data_array['status'] == 200:
                child_array = child_data_array['data']['data']
                reserved_qty_array = child_data_array['data']['reserved_qty_data']
                backorder_qty_array = child_data_array['data']['backorder_qty_data']
                safetystock_qty_array = child_data_array['data']['safetystock_qty_data']
                total_cost_array = child_data_array['data']['total_cost_data']
                case_qty_array = child_data_array['data']['case_qty_data']
                parent_sku_list = [sku.strip().strip("'") for sku in parent_sku_str.split(',')]
                
                for p_sku in parent_sku_list:                                        
                    for item in data['data']:                        
                        if p_sku == item.get('parent_sku'):     
                            item['reserved_quantity'] = reserved_qty_array.get(p_sku, 0)     
                            item['backorder_quantity'] = backorder_qty_array.get(p_sku, 0)
                            item['safetystock_quantity'] = safetystock_qty_array.get(p_sku, 0)
                            item['total_cost'] = total_cost_array.get(p_sku, 0)
                            item['case_qty'] = case_qty_array.get(p_sku, 0)
                            child_variants = child_array.get(p_sku, [])
                            item['child_data'] = child_array.get(p_sku, {})
                            # ✅ Add latest PO date at product level
                            latest_po_date = max(
                                [child.get('latest_po_date', 0) for child in child_variants if child.get('latest_po_date')],
                                default=0
                            )
                            item['latest_po_date'] = latest_po_date
                            break   
                            # item['reserved_quantity'] = reserved_qty_array[p_sku]     
                            # item['backorder_quantity'] = backorder_qty_array[p_sku]
                            # item['safetystock_quantity'] = safetystock_qty_array[p_sku]      
                            # item['child_data'] = child_array[p_sku]
                            # break


            data['meta']['month_rows'] = month_names            
            response['message'] = 'data retrived successfully'
            response['data'] = data
            response['status'] = 200
    finally:
        conn.close()

    return response

def get_on_load_child_data(store, parent_sku_str, append_query, sale_history_months, days_to_replenishment, hide_discontinued, query_tags):
    response = {
        "status": 400,
        "data": {}
    }
    column_query = ''   
    
    if days_to_replenishment:        
        column_query = column_query + 'rp.to_order_qty_' + str(days_to_replenishment)
        column_query = column_query + ', rp.total_sold_' + str(days_to_replenishment) 
        column_query = column_query + ', rp.suggested_order_qty_' + str(days_to_replenishment)        

    if sale_history_months:
        for i in range(int(sale_history_months), 0, -1):
            index = i + 1
            column_query = column_query + ', rp.month_' + str(index) 

    # if hide_discontinued == 'true':
    #         append_query = append_query + " AND rp.quantity_available > 0 "

    month_names, day_difference = get_month_array_for_meta(sale_history_months) 
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        child_skus = []
        query_result = None
        if parent_sku_str != '':
            if query_tags != '':
                values_list = query_tags.split(',')
                trimmed_values_list = [value.strip() for value in values_list]  # Trim each value
                formatted_values = "','".join(trimmed_values_list)
                formatted_values = f"'{formatted_values}'"
                base_query = f"""
                        select rp.product_title, rp.sku, rp.parent_sku, sv.primary_supplier, COALESCE(rc.new_classification, sv.classification) AS classification, sv.brand, rp.cost, rp.retail_price, rp.sale_price, rp.reorder_point, rp.incremental_quantity,
                        rp.quantity_on_hand, rp.quantity_pending, rp.quantity_incoming, rp.quantity_available, rp.quantity_on_hold,
                        rp.suggested_order_qty_45, rp.suggested_order_qty_60, CASE WHEN po.quantity is NULL THEN 0 ELSE po.quantity END as item_order_qty, max(v.out_of_stock_date) as out_of_stock_date, rp.month_1, sum(rrv.quantity) as reserved_quantity, rbv.qty, 
                        rss.qty, sv.attribute4_value, rp.out_of_stock_end_date AS last_received_date, rp.total_out_of_stock_days AS total_out_of_stock_days, rp.restocked_inventory_level, rp.out_of_stock_start_date, rp.expiry_dates AS expiry_dates, 
                        rp.oldest_expiry_date AS expiry_date, MAX(rp.rtv_quantity) AS rtv_quantity, MAX(rp.order_quantity) AS zero_quantity, {column_query}
                        from {AnalyticsDB.get_replenishment_variants_table()} rp left join po_reorders po ON rp.sku = po.sku
                        left join variants v ON rp.sku = v.variants_sku
                        left join replenishment_reserved_variants rrv ON rp.variant_id = rrv.variant_id
                        left join replenishment_backorder_variants rbv ON rp.sku = rbv.child_sku
                        left join replenishment_safety_stock rss ON rp.sku = rss.child_sku
                        left join replenishment_classifications rc ON rp.sku = rc.sku
                        left join product_tags pt ON rp.sku = pt.variant_sku
                        left join skuvault_catalog sv ON sv.sku = rp.sku
                        where (pt.tag_id IN ({formatted_values}) AND pt.variant_sku IS NOT NULL) AND rp.parent_sku IN ({parent_sku_str}) {append_query} GROUP BY rp.product_title, rp.sku, rp.parent_sku, sv.primary_supplier, sv.classification, rc.new_classification, sv.brand, rp.cost, rp.retail_price, rp.sale_price, 
                        rp.reorder_point, rp.incremental_quantity, rp.quantity_on_hand, rp.quantity_pending, rp.quantity_incoming, rp.quantity_available, rp.quantity_on_hold, rp.suggested_order_qty_45, rp.suggested_order_qty_60, po.quantity, rp.month_1, rbv.qty, rss.qty, sv.attribute4_value, rp.out_of_stock_end_date, rp.total_out_of_stock_days, rp.restocked_inventory_level, rp.out_of_stock_start_date, rp.expiry_dates, rp.oldest_expiry_date {column_query}   
                        ORDER BY rp.product_title ASC """
            else:
                base_query = f"""
                            select rp.product_title, rp.sku, rp.parent_sku, sv.primary_supplier, COALESCE(rc.new_classification, sv.classification) AS classification, sv.brand, rp.cost, rp.retail_price, rp.sale_price, rp.reorder_point, rp.incremental_quantity,
                            rp.quantity_on_hand, rp.quantity_pending, rp.quantity_incoming, rp.quantity_available, rp.quantity_on_hold,
                            rp.suggested_order_qty_45, rp.suggested_order_qty_60, CASE WHEN po.quantity is NULL THEN 0 ELSE po.quantity END as item_order_qty, max(v.out_of_stock_date) as out_of_stock_date, rp.month_1, sum(rrv.quantity) as reserved_quantity, rbv.qty, 
                            rss.qty, sv.attribute4_value, rp.out_of_stock_end_date AS last_received_date, rp.total_out_of_stock_days AS total_out_of_stock_days, rp.restocked_inventory_level, rp.out_of_stock_start_date, rp.expiry_dates AS expiry_dates, 
                            rp.oldest_expiry_date AS expiry_date, MAX(rp.rtv_quantity) AS rtv_quantity, MAX(rp.order_quantity) AS zero_quantity, {column_query}
                            from {AnalyticsDB.get_replenishment_variants_table()} rp left join po_reorders po ON rp.sku = po.sku
                            left join variants v ON rp.sku = v.variants_sku
                            left join replenishment_reserved_variants rrv ON rp.variant_id = rrv.variant_id
                            left join replenishment_backorder_variants rbv ON rp.sku = rbv.child_sku
                            left join replenishment_safety_stock rss ON rp.sku = rss.child_sku
                            left join replenishment_classifications rc ON rp.sku = rc.sku
                            left join skuvault_catalog sv ON sv.sku = rp.sku
                            where rp.parent_sku IN ({parent_sku_str}) {append_query} GROUP BY rp.product_title, rp.sku, rp.parent_sku, sv.primary_supplier, sv.classification, rc.new_classification, sv.brand, rp.cost, rp.retail_price, rp.sale_price, 
                            rp.reorder_point, rp.incremental_quantity, rp.quantity_on_hand, rp.quantity_pending, rp.quantity_incoming, rp.quantity_available, rp.quantity_on_hold, rp.suggested_order_qty_45, rp.suggested_order_qty_60, po.quantity, rp.month_1, rbv.qty, rss.qty, sv.attribute4_value, rp.out_of_stock_end_date, rp.total_out_of_stock_days, rp.restocked_inventory_level, rp.out_of_stock_start_date, rp.expiry_dates, rp.oldest_expiry_date, {column_query}   
                            ORDER BY rp.product_title ASC                                              
                            """
            
            query_result = conn.execute(text(base_query))
            query_result = list(query_result)
        
        formatted_data = {} 
        reserved_qty_data = {} 
        backorder_qty_data = {}  
        safetystock_qty_data = {}   
        total_cost_data = {}        
        case_qty_data = {}    
        final_result = {
            'data': [],
            'reserved_qty_data': [],
            'backorder_qty_data': [],
            'safetystock_qty_data': [],
            'case_qty_data': [],
            'meta': {}
        }
        if query_result:
            child_skus = [data[1] for data in query_result]
            latest_po_dates = _fetch_latest_po_date(store['id'], child_skus)
            for data in query_result:
                obj = {}                                                   
                obj["product_title"] = data[0]
                obj["sku"] = data[1]
                obj["parent_sku"] = data[2]                
                obj["primary_supplier"] = data[3]
                obj["classification"] = data[4]
                obj["brand"] = data[5]
                obj["cost"] = float(data[6])
                obj["retail_price"] = float(data[7])
                obj["sale_price"] = float(data[8])
                # obj["reorder_point"] = float(data[9])
                obj["incremental_quantity"] = int(data[10])
                obj["quantity_on_hand"] = int(data[11])
                obj["quantity_pending"] = int(data[12])
                obj["quantity_incoming"] = int(data[13])
                obj["quantity_available"] = int(data[14])
                obj["quantity_on_hold"] = int(data[15])                                         
                obj["suggested_order_qty_45"] = int(data[16])
                obj["suggested_order_qty_60"] = int(data[17])                
                obj["item_order_qty"] = int(data[18])
                obj["out_of_stock_date"] = convert_to_timestamp(data[19]) if data[19] != None else convert_to_timestamp(data[28])
                obj["month_1"] = int(data[20]) if data[20] != None else 'NA' 
                obj["reserved_quantity"] = int(data[21]) if data[21] != None else 0 
                obj["backorder_quantity"] = int(data[22]) if data[22] != None else 0 
                obj["safetystock_quantity"] = int(data[23]) if data[23] != None else 0
                obj["case_qty"] = data[24] if data[24] != "" else "-"
                obj["last_received_date"] = convert_to_timestamp(data[25])
                obj["total_out_of_stock_days"] = int(data[26]) if data[26] != None else 0
                obj["restocked_inventory_level"] = data[27]
                obj["to_order_qty"] = int(data[33])
                obj["total_sold_30"] = int(data[34]) if data[34] != None else 0   
                obj["suggested_order_qty"] = int(data[35])

                # obj["rtv_number"] = data[31]
                # obj["rtv_date"] = convert_to_timestamp(data[32])
                # obj["rtv_status"] = data[33]
                obj["rtv_quantity"] = int(data[31] or 0)
                obj["zero_quantity"] = int(data[32] or 0)
                
                # Safe handling assuming data[29] is already a list
                obj["expiry_dates"] = [
                    datetime.strptime(d.strip(), "%Y-%m").strftime("%b-%Y").upper()
                    for d in (data[29] or []) if d and d.strip()
                ]

                # oldest_expiry_date conversion with null/empty check
                obj["oldest_expiry_date"] = (
                    datetime.strptime(data[30], "%Y-%m").strftime("%b-%Y").upper()
                    if data[30] else None
                )
                # Check if out_of_stock_date is present and calculate out_of_stock_days
                out_of_stock_date = data[19]

                if out_of_stock_date:
                    if isinstance(out_of_stock_date, datetime):
                        days_out_of_stock = (datetime.now() - out_of_stock_date).days
                    else:
                        days_out_of_stock = 0  # Handle cases where the date is invalid or cannot be parsed
                else:
                    days_out_of_stock = 0  # Set to 0 if no out_of_stock_date

                obj["days_out_of_stock"] = days_out_of_stock

                obj["total_cost"] = float(data[6]) * int(data[18])

                count = 1  
                total_sum = 0                                                           
                for i in range(int(sale_history_months), 0, -1):
                    month_index = i + 1
                    key = "month_" + str(month_index)
                    value_key = 35 + count                                
                    obj[key] = int(data[value_key]) if data[value_key] != None else 'NA'
                    total_sum = total_sum + int(data[value_key]) if data[value_key] != None else 0
                    count = count + 1      
                                
                turn_rate = (((int(total_sum) / int(data[14]) if int(data[14]) != 0 else 1) * 365) / day_difference)                           
                weeks_on_hand = 52 / turn_rate if turn_rate != 0 else 1 
                obj["total_sold"] = total_sum 
                obj["turn_rate"] = float(turn_rate)
                obj["weeks_on_hand"] = float(weeks_on_hand)
                obj["days_to_replenishment"] = days_to_replenishment
                obj['sale_history_months'] = sale_history_months

                latest_po_date = latest_po_dates.get(data[1], None)
                if latest_po_date:
                    obj["latest_po_date"] = convert_to_timestamp(latest_po_date)
                else:
                    obj["latest_po_date"] = None

                reserved_qty = int(data[21]) if data[21] != None else 0   
                backorder_qty = int(data[22]) if data[22] != None else 0
                safetystock_qty = int(data[23]) if data[23] != None else 0 
                total_cost = obj['total_cost'] if obj['total_cost'] != None else 0   
                case_qty = int(data[24]) if str(data[24]).isdigit() else 0                                       
                if data[2] in formatted_data:
                    if formatted_data[data[2]] is not None:
                        formatted_data[data[2]].append(obj)
                        reserved_qty_data[data[2]] = reserved_qty_data[data[2]] + int(reserved_qty)
                        backorder_qty_data[data[2]] = backorder_qty_data[data[2]] + int(backorder_qty)
                        safetystock_qty_data[data[2]] = safetystock_qty_data[data[2]] + int(safetystock_qty)
                        total_cost_data[data[2]] = total_cost_data[data[2]] + total_cost
                        case_qty_data[data[2]] = case_qty_data[data[2]] + int(case_qty)
                    else:
                        formatted_data[data[2]] = [obj]
                        reserved_qty_data[data[2]] = reserved_qty_data[data[2]] + int(reserved_qty) 
                        backorder_qty_data[data[2]] = backorder_qty_data[data[2]] + int(backorder_qty)
                        safetystock_qty_data[data[2]] = safetystock_qty_data[data[2]] + int(safetystock_qty)
                        total_cost_data[data[2]] = total_cost_data[data[2]] + total_cost
                        case_qty_data[data[2]] = case_qty_data[data[2]] + int(case_qty)
                else:
                    formatted_data[data[2]] = [obj]         
                    reserved_qty_data[data[2]] = int(reserved_qty)  
                    backorder_qty_data[data[2]] = int(backorder_qty)
                    safetystock_qty_data[data[2]] = int(safetystock_qty)  
                    total_cost_data[data[2]] = total_cost  
                    case_qty_data[data[2]] = int(case_qty)                                                                                    
            
            final_result['data'] = formatted_data 
            final_result['reserved_qty_data'] = reserved_qty_data
            final_result['backorder_qty_data'] = backorder_qty_data
            final_result['safetystock_qty_data'] = safetystock_qty_data
            final_result['total_cost_data'] = total_cost_data
            final_result['case_qty_data'] = case_qty_data
            final_result['meta'] = month_names   

            response['data'] = final_result
            response['status'] = 200           
    finally:
        conn.close()
    
    return response

def _fetch_latest_po_date(store_id, skus):
    db = get_store_db_client_for_store_id(store_id)
    latest_po_dates = {}
    try:
        # Fetch only documents for the provided SKUs
        cursor = db["skuvault_pos"].find(
            {"_id": {"$in": skus}},
            {"pos": 1}
        )

        for doc in cursor:
            sku = doc["_id"]
            pos_entries = doc.get("pos", {})
            if not pos_entries:
                continue

            latest_date = None
            for po_info in pos_entries.values():
                created_at_str = po_info.get("created_at")
                if created_at_str:
                    try:
                        created_at = parser.isoparse(created_at_str)    
                        if not latest_date or created_at > latest_date:
                            latest_date = created_at
                    except Exception as e:
                        logger.warning(f"Exception for SKU {sku}, created_at: {created_at_str}")
                        logger.warning(traceback.format_exc())

            if latest_date:
                latest_po_dates[sku] = latest_date
        return latest_po_dates

    except Exception as e:
        logger.error(traceback.format_exc())
        return None

def get_zoho_returns_data(store, variant_sku, returns_type):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        data = []
        if returns_type == 'rtv':
            base_query = f"""
            select id, variant_sku, parent_sku, rtv_number, rtv_date, rtv_status, quantity, supplier_company_name from zoho_rtv_data where variant_sku = :variant_sku
            """
            results = conn.execute(text(base_query), {"variant_sku": variant_sku}).fetchall()
            for row in results:
                obj = {
                    "id": row[0],
                    "variant_sku": row[1],
                    "parent_sku": row[2],
                    "rtv_number": row[3],
                    "rtv_date": convert_to_timestamp(row[4]),
                    "rtv_status": row[5],
                    "quantity": row[6],
                    "supplier_company_name": row[7]
                }
                data.append(obj)
        elif returns_type == 'customer_returns':
            base_query = f"""
            select id, customer_name, variant_sku, return_date, order_quantity from zoho_customer_returns_data where variant_sku = :variant_sku
            """
            results = conn.execute(text(base_query), {"variant_sku": variant_sku}).fetchall()
            for row in results:
                obj = {
                    "id": row[0],
                    "customer_name": row[1],
                    "variant_sku": row[2],
                    "return_date": convert_to_timestamp(row[3]),
                    "order_quantity": row[4]
                }
                data.append(obj)
        else:
            response['message'] = 'Invalid returns type'
            response['status'] = 400
            return response
            
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['data'] = []
            response['status'] = 200
    except Exception as e:
        logger.error(traceback.format_exc())
    finally:
        conn.close()

    return response

def get_replenishment_aggregate_child_data(store, query_params):
    response = {
        "status": 400,
    }
    parent_sku = str(query_params.get('sku', ''))
    if not parent_sku:
        response['status'] = 204
        response['message'] = 'There are no child sku available.'
        return response

    sale_history_months = query_params.get('sale_history_months', 6)
    days_to_replenishment = query_params.get('days_to_replenishment', 30)   
    hide_discontinued = query_params.get('hide_discontinued', 'true')    

    column_query = ''   
    
    if days_to_replenishment:        
        column_query = column_query + 'rp.to_order_qty_' + str(days_to_replenishment)
        column_query = column_query + ', rp.total_sold_' + str(days_to_replenishment) 
        column_query = column_query + ', rp.suggested_order_qty_' + str(days_to_replenishment)        

    if sale_history_months:
        for i in range(int(sale_history_months), 0, -1):
            index = i + 1
            column_query = column_query + ', rp.month_' + str(index)           
        
    month_names, day_difference = get_month_array_for_meta(sale_history_months)   
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:        
        append_query = ''
        if hide_discontinued != 'true':
            append_query = " AND rp.quantity_available > 0 "       
        
        base_query = f"""
                select rp.product_title, rp.sku, rp.parent_sku, sv.primary_supplier, sv.classification, sv.brand, rp.cost, rp.retail_price, rp.sale_price, rp.reorder_point, rp.incremental_quantity,
                rp.quantity_on_hand, rp.quantity_pending, rp.quantity_incoming, rp.quantity_available, rp.quantity_on_hold, 
                rp.suggested_order_qty_45, rp.suggested_order_qty_60, CASE WHEN po.quantity is NULL THEN 0 ELSE po.quantity END as item_order_qty, v.out_of_stock_date as out_of_stock_date, rp.month_1, sum(rrv.quantity) as reserved_quantity, {column_query} 
                from {AnalyticsDB.get_replenishment_variants_table()} rp left join po_reorders po ON rp.sku = po.sku
                left join variants v ON rp.sku = v.variants_sku
                left join replenishment_reserved_variants rrv ON rp.variant_id = rrv.variant_id
                left join skuvault_catalog sv ON sv.sku = rp.sku
                where rp.parent_sku = '{parent_sku}' {append_query} GROUP BY rp.product_title, rp.sku, rp.parent_sku, sv.primary_supplier, sv.classification, sv.brand, rp.cost, rp.retail_price, rp.sale_price, 
                rp.reorder_point, rp.incremental_quantity, rp.quantity_on_hand, rp.quantity_pending, rp.quantity_incoming, rp.quantity_available, rp.quantity_on_hold, rp.suggested_order_qty_45, rp.suggested_order_qty_60, v.out_of_stock_date, po.quantity, rp.month_1, {column_query}
                ORDER BY rp.product_title ASC                                              
                """               
        query_result = conn.execute(text(base_query))
        
        formatted_data = []  
        reserved_qty_data = 0                   
        final_result = {
            'data': [],
            'reserved_qty_data': 0,
            'meta': {}
        }
        if query_result:
            for data in query_result:
                obj = {}                                                   

                obj["product_title"] = data[0]
                obj["sku"] = data[1]
                obj["parent_sku"] = data[2]                
                obj["primary_supplier"] = data[3]
                obj["classification"] = data[4]
                obj["brand"] = data[5]
                obj["cost"] = float(data[6])
                obj["retail_price"] = float(data[7])
                obj["sale_price"] = float(data[8])
                obj["reorder_point"] = float(data[9])
                obj["incremental_quantity"] = int(data[10])
                obj["quantity_on_hand"] = int(data[11])
                obj["quantity_pending"] = int(data[12])
                obj["quantity_incoming"] = int(data[13])
                obj["quantity_available"] = int(data[14])
                obj["quantity_on_hold"] = int(data[15])                                         
                obj["suggested_order_qty_45"] = int(data[16])
                obj["suggested_order_qty_60"] = int(data[17])                
                obj["item_order_qty"] = int(data[18])
                obj["out_of_stock_date"] = convert_to_timestamp(data[19])
                obj["month_1"] = int(data[20]) if data[20] != None else 'NA'   
                obj["reserved_quantity"] = int(data[21]) if data[21] != None else 0               
                obj["to_order_qty"] = int(data[22])
                obj["total_sold_30"] = int(data[23]) if data[23] != None else 0   
                obj["suggested_order_qty"] = int(data[24])                

                count = 1  
                total_sum = 0                                                           
                for i in range(int(sale_history_months), 0, -1):
                    month_index = i + 1
                    key = "month_" + str(month_index)
                    value_key = 24 + count                     
                    obj[key] = int(data[value_key]) if data[value_key] != None else 'NA'
                    total_sum = total_sum + int(data[value_key]) if data[value_key] != None else 0
                    count = count + 1      
                                
                turn_rate = (((int(total_sum) / int(data[14]) if int(data[14]) != 0 else 1) * 365) / day_difference)                           
                weeks_on_hand = 52 / turn_rate if turn_rate != 0 else 1 
                obj["total_sold"] = total_sum 
                obj["turn_rate"] = float(turn_rate)
                obj["weeks_on_hand"] = float(weeks_on_hand)
                obj["days_to_replenishment"] = days_to_replenishment
                obj['sale_history_months'] = sale_history_months

                reserved_qty = int(data[21]) if data[21] != None else 0                                                
                formatted_data.append(obj)  
                reserved_qty_data = reserved_qty_data + int(reserved_qty)                                                                                                                                
            
            final_result['data'] = formatted_data
            final_result['reserved_qty_data'] = reserved_qty_data
            final_result['meta'] = month_names        
            
        response['message'] = 'data retrived successfully'
        response['data'] = final_result
        response['status'] = 200
    finally:
        conn.close()

    return response

def get_replenishment_aggregate_data_csv(store, query_params):
    response = {
        "status": 400,
    }
    if query_params:
        task_id = task.send_task(task.SEND_REPLENISHMENT_CSV_MAIL_TASK, args=(store['id'], query_params))
        if task_id:
            response['status'] = 200
            response['message'] = f"We are generating the data you requested for download as a CSV file. This process may take a few minutes. Once complete, the CSV will be sent to your email: {query_params['username']}."
        else:
            response['status'] = 400
            response['message'] = 'Something went wrong to generate the CSV file.'
    else:
        response['status'] = 400
        response['message'] = 'query params are required'

    return response

def get_replenishment_dashboard_data_csv(store, query_params):
    response = {
        "status": 400,
    }
    if query_params:
        task_id = task.send_task(task.SEND_REPLENISHMENT_DASHBOARD_CSV_MAIL_TASK, args=(store['id'], query_params))
        if task_id:
            response['status'] = 200
            response['message'] = f"We are generating the data you requested for download as a CSV file. This process may take a few minutes. Once complete, the CSV will be sent to your email: {query_params['username']}."
        else:
            response['status'] = 400
            response['message'] = 'Something went wrong to generate the CSV file.'
    else:
        response['status'] = 400
        response['message'] = 'query params are required'

    return response

def get_replenishment_daily_sold_aggregate_data_csv(store, query_params):
    response = {
        "status": 400,
    }
    if query_params:
        task_id = task.send_task(task.DAILY_SALES_REPLENISHMENT_CSV_MAIL_TASK, args=(store['id'], query_params))
        if task_id:
            response['status'] = 200
            response['message'] = f"We are generating the data you requested for download as a CSV file. This process may take a few minutes. Once complete, the CSV will be sent to your email: {query_params['username']}."
        else:
            response['status'] = 400
            response['message'] = 'Something went wrong to generate the CSV file.'
    else:
        response['status'] = 400
        response['message'] = 'query params are required'

    return response

def get_discontinued_products_data_csv(store, query_params):
    response = {
        "status": 400,
    }
    if query_params:
        task_id = task.send_task(task.DISCONTINUED_PRODUCTS_DATA_CSV_MAIL_TASK, args=(store['id'], query_params))
        if task_id:
            response['status'] = 200
            response['message'] = f"This process may take few minutes. Once complete, the CSV will be sent to your email: {query_params['username']}."
        else:
            response['status'] = 400
            response['message'] = 'Something went wrong to generate the CSV file.'
    else:
        response['status'] = 400
        response['message'] = 'query params are required'

    return response

def getCSV(store):
    store_id = store['id']
    if not store_id:
        return make_response({'error': 'Store ID is required'}, 400)
    file_path = os.path.join('/app', 'reports', 'replenishment', str(store_id), 'replenishment.csv')
    if not os.path.exists(file_path):
        return make_response({'error': 'File not found'}, 404)
    return send_file(file_path, as_attachment=True)


def get_replenishment_daily_sold_aggregate_data(store, query_params):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        limit = int(query_params.get('limit', 20))
        page = int(query_params.get('page', 1))
        offset = (page - 1) * limit
        query_brand = query_params.get('brand','').strip()
        query_classification = query_params.get('classification','').strip()
        query_tags = query_params.get('tags','').strip()
        hide_discontinued = query_params.get('hide_discontinued', 'true')    
        show_zero_qty_products = query_params.get('show_zero_qty_products', 'true')    
        query_primary_supplier = query_params.get('primary_supplier', None) 
        sort_by = query_params.get('sort_by','').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []
        search_value = query_params.get('searchValue', None)
        no_sold_days = query_params.get('no_sold_days', False) 
        if query_primary_supplier and query_primary_supplier.strip() != '':
            query_primary_supplier = query_primary_supplier.split(";")
        else:
            query_primary_supplier = []   
        user = query_params.get('user', None)

        # Get current day of month
        # today = datetime.now(pytz.timezone('US/Central')).day
        today = datetime.now().day
        # Dynamically create SQL part for days
        day_columns = []
        for i in range(1, today + 1):
            day_columns.append(f"rp.day_{i}")

        day_columns_query = ", ".join(day_columns)

        append_query = ''
        join_query = ''
        conditions = []
        join_conditions = []
        params = {}
        
        if query_brand != '':
            values_list = [value.strip() for value in query_brand.split(';')]
            conditions.append("sv.brand = ANY(:brands)")
            params['brands'] = values_list

        if query_classification != '':
            values_list = [value.strip() for value in query_classification.split(';')]
            conditions.append("COALESCE(rc.new_classification, sv.classification) = ANY(:classifications)")
            params['classifications'] = values_list

        if query_tags != '':
            value_list = [value.strip() for value in query_tags.split(',')]    
            formatted_values = "','".join(value_list)
            formatted_values = f"'{formatted_values}'"
            conditions.append(f"pt.tag_id IN ({formatted_values})")
            join_conditions.append(" left join product_tags pt ON rp.parent_sku = pt.sku ")
            

        if search_value and search_value != '':
            conditions.append(" (rp.product_title ILIKE :search or rp.parent_sku ILIKE :search) ")
            params['search'] = '%' + search_value + '%'

        if hide_discontinued == 'true':        
            conditions.append(" ((LOWER(rp.product_title) NOT ILIKE '%discontinued%') AND (LOWER(rp.product_title) NOT ILIKE '%discountinued%')) AND ((LOWER(sv.classification) NOT ILIKE '%discontinued%') AND (LOWER(sv.classification) NOT ILIKE '%discountinued%'))")

        if show_zero_qty_products == 'false':
            conditions.append("rp.quantity_available > 0")

        if no_sold_days == 'true':
            conditions.append("rp.total_month_quantity = 0")

        order_by = ""
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'     
            nulls_order = "NULLS FIRST" if sort_direction.lower() == "asc" else "NULLS LAST"   
            order_by = " ORDER BY " + sort_array[0] + " " + sort_direction + " " + nulls_order

        day_names = get_day_array_for_meta()   
        
    
        if len(query_primary_supplier) == 0 and user:
            query = f"SELECT suppliers FROM user_supplier_mapping where user_name='{user.strip()}'"
            rs = conn.execute(text(query))

            for _supplier in rs:
                query_primary_supplier.append(_supplier[0])
            if len(query_primary_supplier) == 0:
                response['message'] = 'Not found'
                response['data'] = {'data':[]}
                response['status'] = 200
                return response 

        if len(query_primary_supplier):
            conditions.append("sv.primary_supplier = ANY(:suppliers)")
            params['suppliers'] = query_primary_supplier

        if conditions:
            append_query = ' AND (' + ' AND '.join(conditions) + ')'
        
        if join_conditions:
            join_query = ' ' + ' '.join(join_conditions) + ' '

        final_count_query = f"SELECT COUNT(*) FROM (select rp.parent_sku, sv.primary_supplier, sv.classification, max(sv.brand) from {AnalyticsDB.get_current_month_daily_sales_table()} rp {join_query} left join replenishment_classifications rc ON rp.parent_sku = rc.sku left join skuvault_catalog sv ON sv.parent_sku = rp.parent_sku where rp.product_title <> '' AND sv.primary_supplier <> 'Unknown'" + append_query + " group by rp.parent_sku, sv.primary_supplier, sv.classification, rc.new_classification) AS subquery"                     
                
        rs = conn.execute(text(final_count_query), params)
        total_count = int(rs.scalar())

        base_query = f"""WITH consolidated_skuvault AS (
                            SELECT DISTINCT primary_supplier, classification, max(brand) as brand, parent_sku
                            FROM skuvault_catalog GROUP BY parent_sku, primary_supplier, classification
                        )
                        select MAX(rp.product_title) AS product_title, rp.parent_sku AS parent_sku,
                        sum(rp.quantity_incoming) AS quantity_incoming, sum(rp.quantity_available)  AS quantity_available, sum(rp.quantity_on_hold) AS quantity_on_hold, rp.total_month_quantity AS total_month_quantity, {day_columns_query}
                        from {AnalyticsDB.get_current_month_daily_sales_table()} rp {join_query} left join replenishment_classifications rc ON rp.parent_sku = rc.sku 
                        left join consolidated_skuvault sv ON sv.parent_sku = rp.parent_sku 
                        where sv.primary_supplier <> 'Unknown' {append_query}              
                        group by rp.parent_sku, sv.primary_supplier, sv.classification, rc.new_classification, rp.total_month_quantity, {day_columns_query}
                        {order_by}"""               
        
        final_query = base_query + f" OFFSET {offset} LIMIT {limit}"

        query_result = conn.execute(text(final_query), params)

        formated_data = [] 
        parent_sku_str = ''                     
        if query_result:         
            for data in query_result:
                obj = {}   
                parent_sku_str = parent_sku_str + "'" + str(data[1]) + "', " 
                obj["product_title"] = data[0] 
                obj["parent_sku"] = data[1]  
                obj["quantity_incoming"] = int(data[2])
                obj["quantity_available"] = int(data[3])
                obj["quantity_on_hold"] = int(data[4]) 
                count = 1
                for i in range(1, today + 1):
                    day_key = f"day_{i}"
                    value_key = 5 + count 
                    day_value = int(data[value_key]) if data[value_key] is not None else 0
                    obj[day_key] = day_value
                    count += 1
                obj["total_month_quantity"] = int(data[5]) if data[5] is not None else 0 

                obj['child_data'] = []

                formated_data.append(obj)                                                                                                  


            paginated_rows, current_page, total_pages, total_items = paginate_data_postgres(
                total_count, formated_data, page, limit)
            
            data = new_utils.calculate_pagination(paginated_rows, current_page, limit, total_items)  

            parent_sku_str = parent_sku_str.rstrip(', ')
            child_data_array = get_on_load_daily_sold_child_data(conn, parent_sku_str, '', hide_discontinued, query_tags, params)   
        
            if child_data_array['status'] == 200:
                child_array = child_data_array['data']['data']
                reserved_qty_array = child_data_array['data']['reserved_qty_data']
                parent_sku_list = [sku.strip().strip("'") for sku in parent_sku_str.split(',')]
                
                for p_sku in parent_sku_list:                                        
                    for item in data['data']:                        
                        if p_sku == item.get('parent_sku'):     
                            item['reserved_quantity'] = reserved_qty_array.get(p_sku, 0) 
                            item['child_data'] = child_array.get(p_sku, {})
                            break   
            data['meta']['day_labels'] = day_names          
            response['message'] = 'data retrived successfully'
            response['data'] = data
            response['status'] = 200
    except Exception as e:
        response['message'] = 'Something went wrong'
        response['status'] = 500
    finally:
        conn.close()

    return response

def get_on_load_daily_sold_child_data(conn, parent_sku_str, append_query, hide_discontinued, query_tags, params):
    response = {
        "status": 400,
        "data": {}
    }
    try:
        # Get current day of month
        today = datetime.now().day

        # Dynamically create SQL part for days
        day_columns = []
        for i in range(1, today + 1):
            day_columns.append(f"SUM(rp.day_{i}) as day_{i}")

        day_columns_query = ", ".join(day_columns)
        day_names = get_day_array_for_meta()
        join_query = ''
        if query_tags != '':
            value_list = [value.strip() for value in query_tags.split(',')]    
            formatted_values = "','".join(value_list)
            formatted_values = f"'{formatted_values}'"
            append_query = f" AND (pt.tag_id IN ({formatted_values}) AND pt.variant_sku IS NOT NULL)"
            join_query = join_query + " left join product_tags pt ON rp.sku = pt.variant_sku "

        # conn = new_pgdb.get_connection(store['id'])
        query_result = None
        if parent_sku_str != '':
            base_query = f"""
                            select rp.product_title, rp.sku, rp.parent_sku,
                            rp.quantity_incoming, rp.quantity_available, rp.quantity_on_hold, rp.total_month_quantity, sum(rrv.quantity) as reserved_quantity, {day_columns_query}
                            from {AnalyticsDB.get_current_month_daily_sales_variants_table()} rp
                            left join replenishment_reserved_variants rrv ON rp.sku = rrv.sku
                            {join_query}
                            where rp.parent_sku IN ({parent_sku_str}) {append_query} GROUP BY rp.product_title, rp.sku, rp.parent_sku, rp.total_month_quantity, 
                            rp.quantity_incoming, rp.quantity_available, rp.quantity_on_hold
                            ORDER BY rp.product_title ASC                                              
                            """
            query_result = conn.execute(text(base_query), params)

            formatted_data = {} 
            reserved_qty_data = {}
            final_result = {
                'data': [],
                'reserved_qty_data': [],
                'meta': {}
            }
            if query_result:
                for data in query_result:
                    obj = {}                                                   
                    obj["product_title"] = data[0]
                    obj["sku"] = data[1]
                    obj["parent_sku"] = data[2]  
                    obj["quantity_incoming"] = int(data[3])
                    obj["quantity_available"] = int(data[4])
                    obj["quantity_on_hold"] = int(data[5]) 
                    obj["total_month_quantity"] = int(data[6]) if data[6] is not None else 0 
                    obj["reserved_quantity"] = int(data[7]) if data[7] != None else 0 
                    count = 1
                    for i in range(1, today + 1):
                        day_key = f"day_{i}"
                        value_key = 7 + count
                        day_value = int(data[value_key]) if data[value_key] is not None else 0
                        obj[day_key] = day_value
                        count += 1

                    reserved_qty = int(data[7]) if data[7] != None else 0                                      
                    if data[2] in formatted_data:
                        if formatted_data[data[2]] is not None:
                            formatted_data[data[2]].append(obj)
                            reserved_qty_data[data[2]] = reserved_qty_data[data[2]] + int(reserved_qty)
                        else:
                            formatted_data[data[2]] = [obj]
                            reserved_qty_data[data[2]] = reserved_qty_data[data[2]] + int(reserved_qty)
                    else:
                        formatted_data[data[2]] = [obj]         
                        reserved_qty_data[data[2]] = int(reserved_qty)                                                                              
                
                final_result['data'] = formatted_data 
                final_result['reserved_qty_data'] = reserved_qty_data
                final_result['meta'] = day_names
                response['data'] = final_result
                response['status'] = 200 
    except Exception as e:
        print(e)
        response['message'] = 'Something went wrong'
        response['status'] = 500
    
    return response

# ================ UNUSED ===============

def get_replenishment_products_data(store, query_params):
    response = {
        "status": 400,
    }

    page = int(query_params.get('page', 1))
    limit = int(query_params.get('limit', 20))
    offset = (page - 1) * limit

    query_brand = query_params.get('brand', '').strip()
    query_classification = query_params.get('classification', '').strip()
    query_primary_supplier = query_params.get('primary_supplier', '').strip()
    sale_history_months = query_params.get('sale_history_months', 6)
    days_to_replenishment = query_params.get('days_to_replenishment', 30)
    current_month_name = datetime.now().strftime("%b")
    sort_by = query_params.get('sort_by', '').strip()
    sort_array = sort_by.split("/") if sort_by != '' else []

    append_query = ''

    if query_brand != '':
        append_query += get_search_query(query_brand, 'sv.brand')

    if query_classification != '':
        append_query += get_search_query(
            query_classification, 'sv.classification')

    if query_primary_supplier != '':
        append_query += get_search_query(
            query_classification, 'sv.primary_supplier')

    # search filter append query...
    search_key = query_params.get('searchKey','').strip()
    search_value = query_params.get('searchValue',None)
    if search_key != '' and search_value:
        if search_key in skuvault_catalog:
            append_query = append_query + " AND  sv." + \
                search_key + " LIKE '%" + \
                search_value + "%'"
    
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        # calculate new total records count for search results...
        if append_query != '':
            total_count = get_table_record_count(conn,
                'skuvault_catalog as sv', "sv.title != '' AND  sv.primary_supplier != 'Unknown'" + append_query)
            
        # sort append query
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            if sort_array[0] in skuvault_catalog:
                if append_query != '':
                    append_query = append_query + " ORDER BY sv." + \
                        sort_array[0] + " " + sort_direction
                else:
                    append_query = " ORDER BY sv." + \
                        sort_array[0] + " " + sort_direction

        append_query = append_query + " OFFSET " + \
            str(offset) + " LIMIT " + str(limit)
        
        query = "SELECT sv.sku, sv.part_number, sv.title, sv.cost, sv.quantity_available, sv.quantity_incoming, sv.incremental_quantity, sv.reorder_point,  sv.brand, sv.classification, sv.primary_supplier, sv.created_date, sv.modified_date, CASE WHEN po.quantity is NULL THEN 0 ELSE po.quantity END as item_order_qty FROM skuvault_catalog as sv left join po_reorders as po ON sv.sku = po.sku WHERE sv.title != '' AND  sv.primary_supplier != 'Unknown'" + append_query

        # total number of records...
        total_count = get_table_record_count(
            conn, 'skuvault_catalog', "title != '' and  primary_supplier != 'Unknown'")

        rs = conn.execute(text(query))

        columns = [col.strip() for col in query.split("SELECT", 1)
                    [1].split("FROM", 1)[0].split(",")]

        replacements = ['sv.', 'CASE WHEN po.quantity is NULL THEN 0 ELSE po.quantity END as ', 'po.']

        for i, col in enumerate(columns):
            for replace_str in replacements:
                col = col.replace(replace_str, '')
            columns[i] = col.strip()

        results = conver_to_json(rs, columns)

        sku_string = ''

        for res in results:
            sku_string = sku_string + "'" + res['sku'] + "', "

        sku_string = sku_string.rstrip(', ')
        test_month_names = {}

        # sort append query
        if sku_string != '':
            monthly_result, month_names, day_difference = get_six_month_data_using_sku(
                sku_string, conn, sale_history_months, True)
            test_month_names = month_names

            for res in results:
                res['suggested_order_qty'] = 0
                res['to_order_qty'] = 0
                res['total_sold'] = 0
                res['weeks_on_hand'] = 0
                res['turn_rate'] = 0
                res['sale_history_months'] = query_params.get('sale_history_months', 6)
                res['days_to_replenishment'] = query_params.get('days_to_replenishment', 30)

                for month_key, month_value in month_names.items():
                    res[month_key] = 'NA'

                for mdata in monthly_result:
                    res['product_id'] = mdata[1]

                    if str(mdata[0]) == str(res['sku']):
                        for month_key, month_value in month_names.items():
                            if month_value == mdata[3]:
                                res[month_key] = mdata[2]
                                if month_value != current_month_name:
                                    res['total_sold'] = res['total_sold'] + mdata[2]

            for res in results:
                future_sales = (res['total_sold'] /
                                day_difference) * float(days_to_replenishment)
                res['suggested_order_qty'] = (
                    future_sales - res['quantity_available']) - res['quantity_incoming']
                res['to_order_qty'] = res['incremental_quantity'] if res['suggested_order_qty'] > 0 else int(
                    res['suggested_order_qty'])
                res['turn_rate'] = (
                    ((res['total_sold'] / res['quantity_available'] if res['quantity_available'] != 0 else 1) * 365) / day_difference)
                res['weeks_on_hand'] = (
                    52 / res['turn_rate'] if res['turn_rate'] != 0 else 1)

            if len(sort_array):
                if sort_array[0] in analytics_variants_trend_array:
                    if sort_array[1] == '1':
                        results = sorted(
                            results, key=lambda x: custom_sort(x[sort_array[0]]))
                    else:
                        results = sorted(
                            results, key=lambda x: custom_sort(x[sort_array[0]]), reverse=True)

        paginated_rows, current_page, total_pages, total_items = paginate_data_postgres(
            total_count, results, page, limit)

        data = new_utils.calculate_pagination(
            paginated_rows, current_page, limit, total_items)

        data['meta']['month_rows'] = test_month_names
        response['message'] = 'data retrived successfully'
        response['data'] = data
        response['status'] = 200
    finally:
        conn.close()

    return response


def get_replenishment_data(store, query_params):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        total_count = 0
        count_query = "SELECT COUNT(*) FROM skuvault_catalog WHERE title != '' and  primary_supplier != 'Unknown'"
        rs = conn.execute(text(count_query))
        total_count = int(rs.scalar())

        limit = int(query_params.get('limit', 20))
        page = int(query_params.get('page', 1))

        offset = (page - 1) * limit
        
        query_brand = query_params.get('brand', '').strip()
        query_classification = query_params.get('classification', '').strip()
        query_primary_supplier = query_params.get('primary_supplier', '').strip()
        sale_history_months = query_params.get('sale_history_months', 6)
        days_to_replenishment = query_params.get('days_to_replenishment', 30)
        date_range = query_params.get('date_range', '').strip()
        dateArray = date_range.split("/") if date_range != '' else []
        current_month_name = datetime.now().strftime("%b")

        append_query = ''
        sort_by = query_params.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []

        if query_brand != '':
            values_list = query_brand.split(',')
            trimmed_values_list = [value.strip()
                                    for value in values_list]  # Trim each value
            formatted_values = "','".join(trimmed_values_list)
            formatted_values = f"'{formatted_values}'"
            append_query = ' AND sv.brand IN(' + formatted_values + ')'

        if query_classification != '':
            values_list = query_classification.split(',')
            trimmed_values_list = [value.strip()
                                    for value in values_list]  # Trim each value
            formatted_values = "','".join(trimmed_values_list)
            formatted_values = f"'{formatted_values}'"
            append_query = append_query + ' AND sv.classification IN(' + \
                formatted_values + ')'

        if query_primary_supplier != '':
            values_list = query_primary_supplier.split(',')
            trimmed_values_list = [value.strip()
                                    for value in values_list]  # Trim each value
            formatted_values = "','".join(trimmed_values_list)
            formatted_values = f"'{formatted_values}'"
            append_query = append_query + ' AND sv.primary_supplier IN(' + \
                formatted_values + ')'

        # search filter append query
        search_key = query_params.get('searchKey', '').strip()
        search_value = query_params.get('searchValue', None)
        if search_key != '' and search_value:
            if search_key in skuvault_catalog:
                append_query = append_query + " AND  sv." + \
                    search_key + " LIKE '%" + \
                    search_value + "%'"
        
        if append_query != '':
            count_query = "SELECT COUNT(*) FROM skuvault_catalog as sv WHERE sv.title != '' AND  sv.primary_supplier != 'Unknown'" + \
                append_query
            rs = conn.execute(text(count_query))
            total_count = int(rs.scalar())

        # sort append query
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            if sort_array[0] in skuvault_catalog:
                if append_query != '':
                    append_query = append_query + " ORDER BY sv." + \
                        sort_array[0] + " " + sort_direction
                else:
                    append_query = " ORDER BY sv." + \
                        sort_array[0] + " " + sort_direction

        append_query = append_query + " OFFSET " + \
            str(offset) + " LIMIT " + str(limit)

        query = "SELECT sv.sku, sv.part_number, sv.title, sv.cost, sv.quantity_available, sv.quantity_incoming, sv.incremental_quantity, sv.reorder_point,  sv.brand, sv.classification, sv.primary_supplier, sv.created_date, sv.modified_date, CASE WHEN po.quantity is NULL THEN 0 ELSE po.quantity END as item_order_qty FROM skuvault_catalog as sv left join po_reorders as po ON sv.sku = po.sku WHERE sv.title != '' AND  sv.primary_supplier != 'Unknown'" + append_query
        

        rs = conn.execute(text(query))
        columns = [col.strip() for col in query.split("SELECT", 1)
                    [1].split("FROM", 1)[0].split(",")]
        replacements = [
            'sv.', 'CASE WHEN po.quantity is NULL THEN 0 ELSE po.quantity END as ', 'po.']
        for i, col in enumerate(columns):
            for replace_str in replacements:
                col = col.replace(replace_str, '')
            columns[i] = col.strip()
        results = conver_to_json(rs, columns)
        
        sku_string = ''
        for res in results:
            sku_string = sku_string + "'" + res['sku'] + "', "
        sku_string = sku_string.rstrip(', ')
        test_month_names = {}
        
        if sku_string != '':
            monthly_result, month_names, day_difference = get_six_month_data_using_sku(sku_string, conn, sale_history_months, True)
            test_month_names = month_names
           
            for res in results:
                res['suggested_order_qty'] = 0
                res['to_order_qty'] = 0
                res['total_sold'] = 0
                res['weeks_on_hand'] = 0
                res['turn_rate'] = 0
                res['sale_history_months'] = query_params.get('sale_history_months', 6)
                res['days_to_replenishment'] = query_params.get('days_to_replenishment', 30)
                for month_key, month_value in month_names.items():
                    res[month_key] = 'NA'
                for mdata in monthly_result:
                    res['product_id'] = mdata[1]

                    if str(mdata[0]) == str(res['sku']):
                        res['product_id'] = mdata[1]
                        for month_key, month_value in month_names.items():
                            if month_value == mdata[3]:
                                res[month_key] = mdata[2]
                                if month_value != current_month_name:
                                    res['total_sold'] = res['total_sold'] + mdata[2]

            for res in results:
                future_sales = (res['total_sold'] /
                                day_difference) * float(days_to_replenishment)
                res['suggested_order_qty'] = (
                    future_sales - res['quantity_available']) - res['quantity_incoming']
                res['to_order_qty'] = res['incremental_quantity'] if res['suggested_order_qty'] > 0 else int(
                    res['suggested_order_qty'])
                res['turn_rate'] = (
                    ((res['total_sold'] / res['quantity_available'] if res['quantity_available'] != 0 else 1) * 365) / day_difference)
                res['weeks_on_hand'] = (
                    52 / res['turn_rate'] if res['turn_rate'] != 0 else 1)

            if len(sort_array):
                if sort_array[0] in analytics_variants_trend_array:
                    if sort_array[1] == '1':
                        results = sorted(
                            results, key=lambda x: custom_sort(x[sort_array[0]]))
                    else:
                        results = sorted(
                            results, key=lambda x: custom_sort(x[sort_array[0]]), reverse=True)

        paginated_rows, current_page, total_pages, total_items = paginate_data_postgres(
            total_count, results, page, limit)

        data = new_utils.calculate_pagination(
            paginated_rows, current_page, limit, total_items)

        data['meta']['month_rows'] = test_month_names
        response['message'] = 'data retrived successfully'
        response['data'] = data
        response['status'] = 200
    finally:
        conn.close()

    return response

def custom_sort(val):
    if val == 'NA':
        return float('inf')  # Use a large value (positive infinity) for '-'
    try:
        return int(val)  # Convert other numeric values to integers
    except ValueError:
        return val  # For non-numeric values, return the original string


def get_data_query(store, query_params):
    response = {
        "status": 400,
    }
    query = query_params.get('query', '').strip()
    page = int(query_params.get('page', 1))
    limit = int(query_params.get('limit', 20))
    if query != '':
        conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
        try:                
            rs = conn.execute(text(query))
            column_titles = rs.keys()
            columns = [col.strip() for col in query.split("SELECT", 1)
                    [1].split("FROM", 1)[0].split(",")]
            column_list = []
            if '*' in columns:
                column_list = column_titles
            else:
                column_list = columns

            results = conver_to_json(rs, column_list)

            paginated_rows, current_page, total_pages, total_items = paginate_data_postgres(results, page, limit)

            data = new_utils.calculate_pagination(
                paginated_rows, current_page, limit, total_items)

            response['message'] = 'data retrived successfully'
            response['data'] = data
            response['status'] = 200
        finally:
            conn.close()

    return response

def format_columns(columns):
    if isinstance(columns, list):
        return columns  # Already in correct format
    if isinstance(columns, str):
        try:
            parsed_columns = json.loads(columns)
            if isinstance(parsed_columns, list):
                return parsed_columns  # Convert JSON string to list
        except json.JSONDecodeError:
            pass
    return []  # Default 

def save_table_columns(store, query_params, user, call_from_inquiries=False):
    
    if call_from_inquiries:
        columns = format_columns(query_params.get('columns', []))
    else:
        # Extract columns from the payload, defaulting to an empty list if not provided
        columns = query_params.get('columns', '')
        columns = [col.strip() for col in columns.split(',') if col.strip()] if columns else []

    type = query_params.get('type', '')

    created_by = {
        "user_id": str(user['_id']),
        "user_name": user['name']
    }

     # Prepare the document to be inserted or updated
    update_fields = {
        "store_id": store['id'],  # Assuming store['id'] is the store's ObjectId
        "tenant_id": store['tenant_id'],
        "columns": columns,  # Insert the columns list, even if it's empty
        "updated_by": created_by,
        "type": type,
        "updated_at": int(datetime.now().timestamp())
    }

    insert_fields = {
        "user_id": str(user['_id']),  # Assuming user['_id'] is the user's ObjectId
        "created_by": created_by,
        "created_at": int(datetime.now().timestamp())
    }

    try:
        # Perform an upsert operation: update if exists, insert if not
        db_client = get_admin_db_client_for_store_id(store['id'])
        result = db_client[StoreAdminDBCollections.USER_PREFERENCE].update_one(
            {"user_id": insert_fields["user_id"], "type": type},
            {"$set": update_fields, "$setOnInsert": insert_fields},  # Update fields, set created_at only on insert
            upsert=True  # This will insert the document if no matching document is found
        )

        if result.upserted_id is not None:
            message = "Data saved successfully."
            document_id = str(result.upserted_id)
        else:
            message = "Data updated successfully."
            document_id = insert_fields["user_id"]

        return {"status": 200, "message": message, "document_id": document_id}
    except Exception as e:
        return {"status": 409, "message": f"Failed to save data: {str(e)}"}

def save_table_columns_global_filters(store, query_params, user):
    
    # Extract columns from the payload, defaulting to an empty list if not provided
    columns = query_params.get('columns', [])
    type = query_params.get('type', '')

    created_by = {
        "user_id": str(user['_id']),
        "user_name": user['name']
    }

     # Prepare the document to be inserted or updated
    update_fields = {
        "store_id": store['id'],  # Assuming store['id'] is the store's ObjectId
        "tenant_id": store['tenant_id'],
        "columns": columns,  # Insert the columns list, even if it's empty
        "updated_by": created_by,
        "type": type,
        "updated_at": int(datetime.now().timestamp())
    }

    insert_fields = {
        "user_id": str(user['_id']),  # Assuming user['_id'] is the user's ObjectId
        "created_by": created_by,
        "created_at": int(datetime.now().timestamp())
    }

    try:
        # Perform an upsert operation: update if exists, insert if not
        db_client = get_admin_db_client_for_store_id(store['id'])
        result = db_client[StoreAdminDBCollections.USER_PREFERENCE].update_one(
            {"user_id": insert_fields["user_id"], "type": type},
            {"$set": update_fields, "$setOnInsert": insert_fields},  # Update fields, set created_at only on insert
            upsert=True  # This will insert the document if no matching document is found
        )

        if result.upserted_id is not None:
            message = "Data saved successfully."
            document_id = str(result.upserted_id)
        else:
            message = "Data updated successfully."
            document_id = insert_fields["user_id"]

        return {"status": 200, "message": message, "document_id": document_id}
    except Exception as e:
        return {"status": 409, "message": f"Failed to save data: {str(e)}"}


def get_table_columns(store, user, query_params):
    user_id = str(user['_id'])
    store_id = store['id']
    type = query_params.get('type', '')

    # Define the query to fetch the record based on user_id and store_id
    query = {"user_id": user_id, "store_id": store_id, "type": type}

    projection = {"columns": 1, "type": 1, "_id": 0}

    # Use fetch_one_document_from_admin_collection to get the record
    record = fetch_one_document_from_admin_collection(store_id, StoreAdminDBCollections.USER_PREFERENCE, query, projection)

    if record:
        record['is_filter_applied'] = True
        return {"status": 200, "data": record}
    else:
        return {"status": 200, "data": {'columns': [], 'type': '', 'is_filter_applied': False}}


def validate_sku(store, sku, table):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    
    try:
        # Check if the SKU already exists in the backorder or safety stock table
        query_backorder = text(
            f"""SELECT EXISTS(
                    SELECT 1
                    FROM {table}
                    WHERE child_sku = :sku
                ) AS sku_exists;
            """
        )
        result_backorder = conn.execute(query_backorder.params(sku=sku)).fetchone()

        sku_exists_in_backorder = result_backorder[0] if result_backorder else False
        
        # If SKU exists in the backorder/safety stock table, return an error
        if sku_exists_in_backorder:
            response.update({
                "status": 409,
                "message": f"sku: '{sku}' already exists in the system.",
                "sku_valid": False
            })
            return response
        
        # Check if SKU exists in replenishment_variants and whether it is disabled in BigCommerce
        query_variants = text(
            f"""SELECT product_id, product_title
               FROM {AnalyticsDB.get_replenishment_variants_table()}
               WHERE sku = :sku;
            """
        )
        variant_record = conn.execute(query_variants.params(sku=sku)).fetchone()

        if variant_record:
            # product_id, product_title = variant_record

            # # If product_id is None, SKU is disabled in BigCommerce
            # if product_id is None:
            #     response.update({
            #         "status": 409,
            #         "message": f"sku: '{sku}' is disabled in BigCommerce.",
            #         "sku_valid": False
            #     })
            #     return response
            
            # If product_id is not None, SKU is valid
            response.update({
                "status": 200,
                "message": "Valid SKU",
                "sku_valid": True
            })
            return response
        else:
            query_is_parent = text(
                f"""SELECT EXISTS(
                        SELECT 1
                        FROM {AnalyticsDB.get_replenishment_products_table()}
                        WHERE parent_sku = :sku
                    ) AS is_parent_sku;
                """
            )
            is_parent_sku = conn.execute(query_is_parent.params(sku=sku)).scalar()

            if is_parent_sku:
                # Check if the parent SKU has any associated variants in replenishment_variants
                query_child_variants = text(
                    f"""SELECT EXISTS(
                            SELECT 1
                            FROM {AnalyticsDB.get_replenishment_variants_table()}
                            WHERE parent_sku = :sku
                        ) AS has_child_variants;
                    """
                )
                has_child_variants = conn.execute(query_child_variants.params(sku=sku)).scalar()

                if has_child_variants:
                    response.update({
                        "status": 409,
                        "message": f"sku: '{sku}' is a Parent SKU.Only Child SKUs are accepted.",
                        "sku_valid": False
                    })
                    return response
                else:
                    # Allow the SKU if no variants are found for the parent SKU
                    response.update({
                        "status": 200,
                        "message": "Valid Parent SKU with no variants.",
                        "sku_valid": True
                    })
                    return response
            else:
                response.update({
                    "status": 404,
                    "message": "sku: SKU is not valid, please try again.",
                    "sku_valid": False
                })
    finally:
        if conn:
            conn.close()
    return response
    


def save_replenishment_sku_qty(store, payload, user, table):
    response = {
        "status": 400,
        "errors": [],
        "message": "",
        "records_processed": 0
    }
    
    # Get the database connection
    conn = new_pgdb.get_connection(store['id'])

    try:
        data = payload.get('data', [])
        
        # If no data provided
        if not data:
            response['message'] = "No SKU data provided."
            return response
        

        # Function to add errors
        def add_error(message):
            response['errors'].append(message)

        # Function to handle database queries
        def fetch_from_db(query, params):
            return conn.execute(query.params(**params)).fetchone()

        # Iterate over each SKU object in the payload
        for item in data:
            sku = item.get('sku')
            qty = item.get('qty')

            if not sku or not qty:
                add_error(f"SKU or quantity missing for item: {item}")
                continue

            # Fetch parent_sku from replenishment_variants to compare with sku
            sku_check = fetch_from_db(
                text(f"SELECT parent_sku FROM {AnalyticsDB.get_replenishment_variants_table()} WHERE sku = :sku"),
                {'sku': sku}
            )

            # If SKU is the same as parent_sku, skip the is_parent_sku check
            if sku_check and sku_check[0] == sku:
                # Skip is_parent_sku check and continue processing the SKU
                pass
            else:
                # Check if SKU is a parent SKU
                is_parent_sku = fetch_from_db(
                    text(f"SELECT EXISTS(SELECT 1 FROM {AnalyticsDB.get_replenishment_products_table()} WHERE parent_sku = :sku)"),
                    {'sku': sku}
                )
                if is_parent_sku and is_parent_sku[0]:
                    # Check if parent SKU has any variants associated with it
                    variants_exist = fetch_from_db(
                        text(f"SELECT EXISTS(SELECT 1 FROM {AnalyticsDB.get_replenishment_variants_table()} WHERE parent_sku = :sku)"),
                        {'sku': sku}
                    )

                    if variants_exist and variants_exist[0]:
                        # Parent SKU has associated variants, so we reject it
                        add_error(f"sku: '{sku}' is a Parent SKU. Only Child SKUs are accepted.")
                        continue
                    else:
                        # Parent SKU has no variants, so allow it
                        pass

            # Check if SKU already exists in table
            existing_backorder = fetch_from_db(
                text(f"SELECT qty FROM {table} WHERE child_sku = :sku"),
                {'sku': sku}
            )
            if existing_backorder:
                # Update quantity for multiple records
                conn.execute(text(
                    f"""UPDATE {table} 
                        SET qty = :new_qty, updated_by = :updated_by, updated_at = CURRENT_TIMESTAMP
                        WHERE child_sku = :sku"""
                ).params(new_qty=int(qty), updated_by=user['username'], sku=sku))
                conn.commit()
                response['records_processed'] += 1
                continue

            # Fetch product details from replenishment_variants
            product_info = fetch_from_db(
                text(f"""SELECT product_id, parent_sku, variant_id, product_title 
                        FROM {AnalyticsDB.get_replenishment_variants_table()} WHERE sku = :sku"""),
                {'sku': sku}
            )

            if not product_info:
                add_error(f"sku: '{sku}' is an invalid SKU.")
                continue

            product_id, parent_sku, variant_id, product_name = product_info

            # Check if product is disabled (product_id is null)
            # if product_id is None:
            #     add_error(f"sku: '{sku}' is disabled in BigCommerce.")
            #     continue

            # Insert new SKU into table
            conn.execute(text(
                f"""INSERT INTO {table} 
                   (product_id, parent_sku, variant_id, product_name, child_sku, qty, created_by, updated_by) 
                   VALUES (:product_id, :parent_sku, :variant_id, :product_name, :child_sku, :qty, :created_by, :updated_by)"""
            ).params(
                product_id=product_id,
                parent_sku=parent_sku,
                variant_id=variant_id,
                product_name=product_name,
                child_sku=sku,
                qty=qty,
                created_by=user['username'],
                updated_by=user['username']
            ))
            conn.commit()
            response['records_processed'] += 1

        # Set message and status based on errors
        if response['errors']:
            response['status'] = 409
            response['message'] = response['errors']  
        elif response['message']:
            response['status'] = 409
            response['message'] = response['message']
        else:
            response['status'] = 200
            response['message'] = "SKU(s) processed successfully."

    finally:
        if conn:
            conn.close()
    
    return response


def get_stock_and_backorder_products(store, filter, limit, page, sort_array, table):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        # Step 1: Initialize base query
        base_query = f"""
            SELECT id, product_name, child_sku, qty, updated_by
            FROM {table}
        """
        # Step 2: Add search condition for both product_name and child_sku
        if filter:
            base_query += f" WHERE product_name ILIKE '%{filter}%' OR child_sku ILIKE '%{filter}%'"

        # Step 3: Handle sorting based on the sort_array
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            sort_field = sort_array[0]
            if sort_field in ["product_name", "child_sku", "qty", "created_at"]:
                base_query += f" ORDER BY {sort_field} {sort_direction}"

        # Step 4: Check if page and limit are both 0 (no pagination required)
        if page == 0 and limit == 0:
            # Execute the full query without LIMIT and OFFSET
            results = conn.execute(text(base_query))
            data = []
            for row in results:
                if row[4] is not None:
                    user = store_admin_db.fetch_user_by_username(store['id'], row[4])
                else:
                    user = None
                row_data = {
                    'id': row[0],
                    'product_name': row[1],
                    'child_sku': row[2],
                    'qty': row[3],
                    'updated_by': user['name'] if user else None
                }
                data.append(row_data)
            
            response['data'] = {"data": data}  # Return all data without pagination
            response['status'] = 200

        else:
            # Step 5: Fetch the total number of records for pagination
            count_query = f"SELECT COUNT(*) FROM ({base_query}) AS total"
            total_count = conn.execute(text(count_query)).scalar()

            # Step 6: Apply pagination (LIMIT and OFFSET)
            offset = (page - 1) * limit
            paginated_query = base_query + f" LIMIT {limit} OFFSET {offset}"
            results = conn.execute(text(paginated_query))

            # Step 7: Format the data
            data = []
            for row in results:
                if row[4] is not None:
                    user = store_admin_db.fetch_user_by_username(store['id'], row[4])
                else:
                    user = None
                row_data = {
                    'id': row[0],
                    'product_name': row[1],
                    'child_sku': row[2],
                    'qty': row[3],
                    'updated_by': user['name'] if user else None
                }
                data.append(row_data)

            # Step 8: Calculate pagination
            paginated_rows, current_page, total_pages, total_items = paginate_data_postgres(
                total_count, data, page, limit)                

            paginated_data = new_utils.calculate_pagination(paginated_rows, current_page, limit, total_items)

            # Step 9: Prepare the paginated response
            response['data'] = paginated_data
            response['status'] = 200

    except Exception as e:
        response['status'] = 422
        response['message'] = str(e)
        raise e
    finally:
        if conn:
            conn.close()

    return response



def get_backorder_product_details(store, id):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        query = text(
            """SELECT id, product_name, child_sku, qty
               FROM replenishment_backorder_variants
               WHERE id = :id
            """
        )
        result = conn.execute(query.params(id=id)).fetchone()
        if result:
            response['data'] = [dict(result)]
            response['status'] = 200
        else:
            response['status'] = 404
            response['message'] = 'SKU not found.'

    except Exception as e:
        response['status'] = 422
        response['message'] = str(e)
        raise e
    finally:
        if conn:
            conn.close()
    return response

def update_backorder_and_stock_qty(store, user, id, qty, table):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        user = user['username']
        query = text(
            f"""UPDATE {table}
               SET qty = :qty,
               updated_by = :updated_by,
               updated_at = CURRENT_TIMESTAMP
               WHERE id = :id
            """
        )
        result = conn.execute(query.params(id=id, qty=qty, updated_by=user))
        conn.commit()
        if result.rowcount > 0:
            response['status'] = 200
            response['message'] = 'SKU updated successfully.'
        else:
            response['status'] = 404
            response['message'] = 'SKU is not valid, please try again.'
    except Exception as e:
        response['status'] = 422
        response['message'] = str(e)
        raise e
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response

def delete_stock_and_backorder_products(store, ids, table):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        # Use the IN clause for bulk deletion
        query = text(
            f"""DELETE FROM {table}
               WHERE id = ANY(:ids)
            """
        )
        result = conn.execute(query.params(ids=ids))
        conn.commit()

        if result.rowcount > 0:
            response['status'] = 200
            response['message'] = f'{result.rowcount} SKU(s) deleted successfully.'
        else:
            response['status'] = 404
            response['message'] = 'No valid SKU(s) found for deletion.'

    except Exception as e:
        response['status'] = 422
        response['message'] = str(e)
        raise e
    finally:
        if conn:
            conn.close()

    return response

def get_replenishment_dashboard_data(store, query_params):
    response = {
        "status": 400,
    }
    limit = int(query_params.get('limit', 20))
    page = int(query_params.get('page', 1))
    offset = (page - 1) * limit
    query_brand = query_params.get('brand','').strip()
    query_classification = query_params.get('classification','').strip()
    query_tags = query_params.get('tags','').strip()
    hide_discontinued = query_params.get('hide_discontinued', 'true')    
    show_zero_qty_products = query_params.get('show_zero_qty_products', 'true')    
    query_primary_supplier = query_params.get('primary_supplier', None) 
    sort_by = query_params.get('sort_by','').strip()
    sort_array = []
    if sort_by != 'ratio/1' and sort_by != 'ratio/-1' and sort_by != 'total_cost/1' and sort_by != 'total_cost/-1':
        sort_array = sort_by.split("/") if sort_by != '' else []
    
    search_key = query_params.get('searchKey', '').strip()
    search_value = query_params.get('searchValue', None)
    ratio_filter = query_params.get('ratio_filter', None)
    year = query_params.get('year', None)
    if query_primary_supplier and query_primary_supplier.strip() != '':
        query_primary_supplier = query_primary_supplier.split(";")
    else:
        query_primary_supplier = []   
    user = query_params.get('user', None)

    sale_history_months = query_params.get('sale_history_months', 6)
    days_to_replenishment = query_params.get('days_to_replenishment', 30)      
    no_sold_days = query_params.get('no_sold_days', False) 
    classified_as = query_params.get('classified_as', None)

    price_list_data = {}

    column_query = ''   

    if days_to_replenishment:
        column_query = column_query + 'sum(rp.to_order_qty_' + str(days_to_replenishment) + ')'
        column_query = column_query + ', sum(rp.total_sold_' + str(days_to_replenishment) + ')'
        column_query = column_query + ', sum(rp.suggested_order_qty_' + str(days_to_replenishment) + ') AS suggested_order_qty'        

    if sale_history_months:
        for i in range(int(sale_history_months), 0, -1):
            index = i + 1
            column_query = column_query + ', sum(rp.month_' + str(index) + ')'                   

    append_query = ''
    
    if query_brand != '':
        values_list = query_brand.split(';')
        trimmed_values_list = [value.strip()
                                for value in values_list]  # Trim each value 
        formatted_values = "','".join(trimmed_values_list)
        formatted_values = f"'{formatted_values}'"
        append_query = ' AND sv.brand IN(' + formatted_values + ')'

    if query_classification != '':
        values_list = query_classification.split(';')
        trimmed_values_list = [value.strip() for value in values_list]  # Trim spaces

        # Escape single quotes inside classification names if necessary
        escaped_values = [v.replace("'", "''") for v in trimmed_values_list]

        # Build a string like: 'value1','value2','value3'
        formatted_values = "','".join(escaped_values)

        # Build the SQL array literal with type cast
        append_query += f"""
        AND (
            ca.classifications && ARRAY['{formatted_values}']::varchar[]
        )
        """
        # append_query = append_query + ' AND rp.classification IN(' + \
        #     formatted_values + ')'

    if year and year != None:
        append_query = append_query + f" AND EXTRACT(YEAR FROM p.date_created) = {int(year)}"

    if user and user.strip() != '':
        append_query = append_query + " AND usm.user_name ILIKE '%" + user.strip() + "%'"
   
    if search_value and search_value != '':
        append_query = append_query + " AND  (rp.product_title ILIKE '%" + search_value + "%' or rp.parent_sku ILIKE '%" + search_value + "%') "  

    if hide_discontinued == 'true':        
        append_query = append_query + " AND ((LOWER(rp.product_title) NOT ILIKE '%discontinued%') AND (LOWER(rp.product_title) NOT ILIKE '%discountinued%')) AND ((LOWER(sv.classification) NOT ILIKE '%discontinued%') AND (LOWER(sv.classification) NOT ILIKE '%discountinued%'))"
    
    if show_zero_qty_products == 'false':
        append_query = append_query + " AND rp.quantity_available > 0"
    
    if no_sold_days == 'true':
        append_query = append_query + " AND rp.total_sold_" + str(days_to_replenishment) + " = 0"

    join_query = "LEFT JOIN"
    append_conditions = []
    if classified_as:
        parts = [part.strip() for part in classified_as.split(",")]
        ids = [int(part) for part in parts if part.isdigit()]
        
        if "Unassigned" in parts:
            append_conditions.append("rd.classified_as_id IS NULL")
            join_query = "INNER JOIN"  # As per your original logic

        if ids:
            placeholders = ','.join(map(str, ids))
            append_conditions.append(f"rd.classified_as_id IN ({placeholders})")

        if append_conditions:
            append_query += " AND (" + " OR ".join(append_conditions) + ")"

    order_by = ""
    if len(sort_array):
        sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
        if sort_array[0] in ["created_at"]:                
            order_by += f" ORDER BY {sort_array[0]} {sort_direction}" 
        else:       
            order_by = " ORDER BY " + sort_array[0] + " " + sort_direction

    month_names, day_difference = get_month_array_for_meta(sale_history_months)    
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        if len(query_primary_supplier) == 0 and user:
            query = f"SELECT suppliers FROM user_supplier_mapping where user_name='{user.strip()}'"
            rs = conn.execute(text(query))

            for _supplier in rs:
                query_primary_supplier.append(_supplier[0])
            if len(query_primary_supplier) == 0:
                response['message'] = 'Not found'
                response['data'] = {'data':[]}
                response['status'] = 200
                return response 
                           
        if len(query_primary_supplier):
            append_query = append_query + ' AND sv.primary_supplier = ANY(:suppliers)'

        final_count_query = ''            
        if query_tags != '':
            values_list = query_tags.split(',')
            trimmed_values_list = [value.strip() for value in values_list]  # Trim each value
            formatted_values = "','".join(trimmed_values_list)
            formatted_values = f"'{formatted_values}'"             
            final_count_query = f"""WITH classifications_agg AS (
                                    SELECT
                                        sc.parent_sku,
                                        ARRAY_AGG(DISTINCT COALESCE(rc.new_classification, sc.classification)) AS classifications,
                                        ARRAY_AGG(DISTINCT sc.primary_supplier) AS primary_suppliers,
                                        ARRAY_AGG(DISTINCT usm.user_name ORDER BY usm.user_name) AS purchasers
                                    FROM skuvault_catalog sc
                                    LEFT JOIN replenishment_classifications rc ON sc.sku = rc.sku
                                    LEFT JOIN user_supplier_mapping usm ON sc.primary_supplier = usm.suppliers
                                    WHERE sc.parent_sku IS NOT NULL
                                    GROUP BY sc.parent_sku
                                )
                                SELECT COUNT(*) FROM (select rp.parent_sku, ca.primary_suppliers, ca.classifications, ca.purchasers, max(sv.brand), p.out_of_stock_date, p.date_created, rd.classified_as_id, rd.terms_consignment, rd.action_taken from {AnalyticsDB.get_replenishment_products_table()} rp left join product_tags pt ON rp.parent_sku = pt.sku left join products p ON rp.parent_sku = p.sku {join_query} replenishment_dashboard rd ON rp.parent_sku = rd.parent_sku left join skuvault_catalog sv ON sv.parent_sku = rp.parent_sku LEFT JOIN user_supplier_mapping usm ON sv.primary_supplier = usm.suppliers LEFT JOIN classifications_agg ca ON rp.parent_sku = ca.parent_sku where pt.sku = rp.parent_sku AND pt.tag_id IN ({formatted_values}) AND (pt.variant_sku IS NULL OR pt.variant_sku = pt.sku) AND rp.product_title <> '' AND sv.primary_supplier <> 'Unknown' AND rp.parent_sku IS NOT NULL AND rp.parent_sku != '' {append_query} group by rp.parent_sku, ca.primary_suppliers, ca.classifications, ca.purchasers, p.out_of_stock_date, p.date_created, rd.classified_as_id, rd.terms_consignment, rd.action_taken) AS subquery
                                """                                             
        else:
            final_count_query = f"""WITH classifications_agg AS (
                                    SELECT
                                        sc.parent_sku,
                                        ARRAY_AGG(DISTINCT COALESCE(rc.new_classification, sc.classification)) AS classifications,
                                        ARRAY_AGG(DISTINCT sc.primary_supplier) AS primary_suppliers,
                                        ARRAY_AGG(DISTINCT usm.user_name ORDER BY usm.user_name) AS purchasers
                                    FROM skuvault_catalog sc
                                    LEFT JOIN replenishment_classifications rc ON sc.sku = rc.sku
                                    LEFT JOIN user_supplier_mapping usm ON sc.primary_supplier = usm.suppliers
                                    WHERE sc.parent_sku IS NOT NULL
                                    GROUP BY sc.parent_sku
                                )
                                SELECT COUNT(*) FROM (select rp.parent_sku, ca.primary_suppliers, ca.classifications, ca.purchasers, max(sv.brand), p.out_of_stock_date, p.date_created, rd.classified_as_id, rd.terms_consignment, rd.action_taken from {AnalyticsDB.get_replenishment_products_table()} rp left join products p ON rp.parent_sku = p.sku {join_query} replenishment_dashboard rd ON rp.parent_sku = rd.parent_sku left join skuvault_catalog sv ON sv.parent_sku = rp.parent_sku LEFT JOIN user_supplier_mapping usm ON sv.primary_supplier = usm.suppliers LEFT JOIN classifications_agg ca ON rp.parent_sku = ca.parent_sku where rp.product_title <> '' AND sv.primary_supplier <> 'Unknown' AND rp.parent_sku IS NOT NULL AND rp.parent_sku != '' {append_query} group by rp.parent_sku, ca.primary_suppliers, ca.classifications, ca.purchasers, p.out_of_stock_date, p.date_created, rd.classified_as_id, rd.terms_consignment, rd.action_taken) AS subquery
                                """                     
                 
        rs = conn.execute(text(final_count_query), {"suppliers": query_primary_supplier})
        total_count = int(rs.scalar()) 
                 
        # ...
        base_query = ''
        if query_tags != '':
            values_list = query_tags.split(',')
            trimmed_values_list = [value.strip() for value in values_list]  # Trim each value
            formatted_values = "','".join(trimmed_values_list)
            formatted_values = f"'{formatted_values}'"
            base_query = f"""WITH consolidated_skuvault AS (
                                SELECT DISTINCT primary_supplier, classification, max(brand) as brand, parent_sku
                                FROM skuvault_catalog GROUP BY parent_sku, primary_supplier, classification
                            ),
                            classifications_agg AS (
                                SELECT
                                    sc.parent_sku,
                                        ARRAY_AGG(DISTINCT COALESCE(rc.new_classification, sc.classification)) AS classifications,
                                        ARRAY_AGG(DISTINCT sc.primary_supplier) AS primary_suppliers,
                                        ARRAY_AGG(DISTINCT usm.user_name ORDER BY usm.user_name) AS purchasers
                                FROM skuvault_catalog sc
                                LEFT JOIN replenishment_classifications rc ON sc.sku = rc.sku
                                LEFT JOIN user_supplier_mapping usm ON sc.primary_supplier = usm.suppliers
                                WHERE sc.parent_sku IS NOT NULL
                                GROUP BY sc.parent_sku
                            )
                            select MAX(rp.product_title) AS product_title, MAX(rp.product_id) AS product_id, rp.parent_sku, ca.primary_suppliers, ca.classifications, MAX(sv.brand) AS brand, MAX(rp.cost) AS cost, MAX(rp.retail_price) AS retail_price, MAX(rp.sale_price) AS sale_price, MAX(rp.reorder_point) AS reorder_point, MAX(rp.incremental_quantity) AS incremental_quantity,
                            sum(DISTINCT rp.quantity_on_hand), sum(DISTINCT rp.quantity_pending), sum(DISTINCT rp.quantity_incoming), sum(DISTINCT rp.quantity_available), sum(DISTINCT rp.quantity_on_hold), 
                            sum(DISTINCT rp.suggested_order_qty_45) AS suggested_order_qty_45, sum(DISTINCT rp.suggested_order_qty_60) AS suggested_order_qty_60, sum(DISTINCT rp.month_1), p.out_of_stock_date, p.date_created AS created_at, 
                            rd.classified_as_id AS classified_as, 
                            COALESCE(rd.terms_consignment, NULL) AS terms_consignment, 
                            COALESCE(rd.action_taken, NULL) AS action_taken,
                            ca.purchasers,
                            p.price AS wholesale_price,
                            {column_query}
                            from {AnalyticsDB.get_replenishment_products_table()} rp left join product_tags pt ON rp.parent_sku = pt.sku left join products p ON rp.parent_sku = p.sku left join replenishment_dashboard rd ON rp.parent_sku = rd.parent_sku 
                            left join consolidated_skuvault sv ON sv.parent_sku = rp.parent_sku left join user_supplier_mapping usm ON sv.primary_supplier = usm.suppliers left join classifications_agg ca ON rp.parent_sku = ca.parent_sku
                            where pt.tag_id IN ({formatted_values}) AND (pt.variant_sku IS NULL OR pt.variant_sku = pt.sku) AND sv.primary_supplier <> 'Unknown' AND rp.parent_sku IS NOT NULL AND rp.parent_sku != '' {append_query}              
                            group by rp.parent_sku, ca.primary_suppliers, ca.classifications, p.out_of_stock_date, p.date_created, rd.classified_as_id, rd.terms_consignment, rd.action_taken, ca.purchasers, p.price
                            {order_by}"""            
        else: 
            base_query = f"""WITH consolidated_skuvault AS (
                                SELECT DISTINCT primary_supplier, classification, max(brand) as brand, parent_sku
                                FROM skuvault_catalog GROUP BY parent_sku, primary_supplier, classification
                            ),
                            classifications_agg AS (
                                SELECT
                                    sc.parent_sku,
                                        ARRAY_AGG(DISTINCT COALESCE(rc.new_classification, sc.classification)) AS classifications,
                                        ARRAY_AGG(DISTINCT sc.primary_supplier) AS primary_suppliers,
                                        ARRAY_AGG(DISTINCT usm.user_name ORDER BY usm.user_name) AS purchasers
                                FROM skuvault_catalog sc
                                LEFT JOIN replenishment_classifications rc ON sc.sku = rc.sku
                                LEFT JOIN user_supplier_mapping usm ON sc.primary_supplier = usm.suppliers
                                WHERE sc.parent_sku IS NOT NULL
                                GROUP BY sc.parent_sku
                            )
                            select MAX(rp.product_title) AS product_title, MAX(rp.product_id) AS product_id, rp.parent_sku, ca.primary_suppliers, ca.classifications, MAX(sv.brand) AS brand, MAX(rp.cost) AS cost, MAX(rp.retail_price) AS retail_price, MAX(rp.sale_price) AS sale_price, MAX(rp.reorder_point) AS reorder_point, MAX(rp.incremental_quantity) AS incremental_quantity,
                            sum(DISTINCT rp.quantity_on_hand), sum(DISTINCT rp.quantity_pending), sum(DISTINCT rp.quantity_incoming), sum(DISTINCT rp.quantity_available), sum(DISTINCT rp.quantity_on_hold), 
                            sum(DISTINCT rp.suggested_order_qty_45) AS suggested_order_qty_45, sum(DISTINCT rp.suggested_order_qty_60) AS suggested_order_qty_60, sum(DISTINCT rp.month_1), p.out_of_stock_date, p.date_created AS created_at,
                            rd.classified_as_id AS classified_as, 
                            COALESCE(rd.terms_consignment, NULL) AS terms_consignment, 
                            COALESCE(rd.action_taken, NULL) AS action_taken,
                            ca.purchasers,
                            p.price AS wholesale_price,
                            {column_query}
                            from {AnalyticsDB.get_replenishment_products_table()} rp left join replenishment_dashboard rd ON rp.parent_sku = rd.parent_sku left join products p ON rp.parent_sku = p.sku 
                            left join consolidated_skuvault sv ON sv.parent_sku = rp.parent_sku left join user_supplier_mapping usm ON sv.primary_supplier = usm.suppliers left join classifications_agg ca ON rp.parent_sku = ca.parent_sku where sv.primary_supplier <> 'Unknown' AND rp.parent_sku IS NOT NULL AND rp.parent_sku != '' {append_query}         
                            group by rp.parent_sku, ca.primary_suppliers, ca.classifications, p.out_of_stock_date, p.date_created, rd.classified_as_id, rd.terms_consignment, rd.action_taken, ca.purchasers, p.price
                            {order_by}"""               
        
        final_query = base_query +  " OFFSET " + str(offset) + " LIMIT " + str(limit)
        query_result = conn.execute(text(final_query), {"suppliers": query_primary_supplier})

        formated_data = [] 
        sku_count = 1
        parent_sku_str = ''                     
        if query_result:         
            for data in query_result:
                obj = {}   
                if sku_count <= 10:
                    parent_sku_str = parent_sku_str + "'" + str(data[2]) + "', " 
                    sku_count = sku_count + 1           
                obj["product_title"] = data[0]
                obj["product_id"] = data[1]
                obj["parent_sku"] = data[2]                
                obj["primary_supplier"] = data[3]
                obj["purchaser_name"] = data[24]
                obj["classification"] = data[4]
                obj["cost"] = float(data[6])
                obj["quantity_on_hand"] = int(data[11])
                obj["quantity_pending"] = int(data[12])
                obj["quantity_incoming"] = int(data[13])
                obj["quantity_available"] = int(data[14])
                obj["quantity_on_hold"] = int(data[15])                                                       
                obj["month_1"] = int(data[18]) if data[18] != None else 'NA'                              
                obj["total_sold_30"] = int(data[26]) if data[26] != None else 0 
                # obj["suggested_order_qty"] = int(data[22])   
                obj["created_at"] = convert_to_timestamp(data[20])
                obj["year"] = data[20].year if isinstance(data[20], datetime) else 0
                obj["classified_as"] = data[21]
                obj["terms_consignment"] = data[22]
                obj["action_taken"] = data[23]
                obj["wholesale_price"] = data[25]

                out_of_stock_date = data[19]

                if data[2] != '' or data[2] != None:
                    # Call the get_product_price_list_replenishment_dashboard function
                    price_list_data = products_list.get_product_price_list_replenishment_dashboard(store, data[2])

                # Integrating price list data into the existing object
                if price_list_data and "data" in price_list_data:
                    for price_list_item in price_list_data["data"]:
                        # Add price list details to the object
                        obj["price_lists"] = price_list_item.get("price_list", [])

                if out_of_stock_date:
                    if isinstance(out_of_stock_date, datetime):
                        # Make datetime.now() timezone-aware
                        now = datetime.now(timezone.utc) if out_of_stock_date.tzinfo else datetime.now()
                        days_out_of_stock = (now - out_of_stock_date).days
                    else:
                        days_out_of_stock = 0  # Handle cases where the date is invalid or cannot be parsed
                else:
                    days_out_of_stock = 0  # Set to 0 if no out_of_stock_date

                obj["days_out_of_stock"] = days_out_of_stock             

                count = 1    
                total_sum = 0                                                   
                for i in range(int(sale_history_months), 0, -1):
                    month_index = i + 1
                    key = "month_" + str(month_index)
                    value_key = 28 + count                                       
                    obj[key] = int(data[value_key]) if data[value_key] != None else 'NA'
                    total_sum = total_sum + int(data[value_key]) if data[value_key] != None else 0
                    count = count+1 

                turn_rate = (((int(total_sum) / int(data[14]) if int(data[14]) != 0 else 1) * 365) / day_difference)                           
                weeks_on_hand = 52 / turn_rate if turn_rate != 0 else 1 
                obj["total_sold"] = total_sum 
                obj["turn_rate"] = float(turn_rate)
                obj["weeks_on_hand"] = float(weeks_on_hand)
                obj["days_to_replenishment"] = days_to_replenishment
                obj['sale_history_months'] = sale_history_months
                # obj['child_data'] = []
                
                if ratio_filter:
                    count = 1
                    # Extract the month number from the ratio_filter (e.g., "month_5" -> 5)
                    try:
                        month_limit = int(ratio_filter.split('_')[1])  # Extract the number after 'month_'
                    except (IndexError, ValueError):
                        month_limit = 1  # Default to month_1 if ratio_filter is invalid

                    specified_months_sum = 0  # Initialize sum for the specified months

                    # Always include month_1 (which is at index 18)
                    if month_limit >= 1:
                        obj["month_1"] = int(data[18]) if data[18] is not None else 'NA'
                        specified_months_sum += obj["month_1"] if obj["month_1"] != 'NA' else 0

                    # Loop through the months from sale_history_months down to month_2, month_3, etc.
                    for i in range(int(sale_history_months), 0, -1):
                        month_index = i + 1  # Generate the month number (e.g., month_2, month_3)
                        key = "month_" + str(month_index)  # Form the key, e.g., "month_2"
                        value_key = 28 + count  # Adjust value_key based on the count

                        # Only sum the values if the current month is within the limit (e.g., month_2, month_3, etc.)
                        if month_index <= month_limit:
                            obj[key] = int(data[value_key]) if data[value_key] is not None else 'NA'
                            specified_months_sum += obj[key] if obj[key] != 'NA' else 0

                        count += 1  # Increment the count

                    # Calculate total quantity (available + incoming)
                    total_qty = obj["quantity_available"] + obj["quantity_incoming"]

                    # Calculate the ratio and update the object
                    if total_qty > 0:
                        ratio = (specified_months_sum / total_qty)
                        obj["ratio"] = str(round(ratio, 2)) + '%'
                    else:
                        obj["ratio"] = "0%"
                else:
                    obj["ratio"] = "0%"


                   
                obj["total_cost"] = int(data[14]) * float(data[6])

                formated_data.append(obj)

            # Sort formated_data based on ratio if 'sort_by' is set to 'total_cost/1' or 'total_cost/-1'
            if sort_by == "total_cost/1":  # Ascending order
                formated_data = sorted(formated_data, key=lambda x: x["total_cost"])
            elif sort_by == "total_cost/-1":  # Descending order
                formated_data = sorted(formated_data, key=lambda x: x["total_cost"], reverse=True)

            # Sort formated_data based on ratio if 'sort_by' is set to 'ratio/1' or 'ratio/-1'
            if sort_by == "ratio/1":  # Ascending order
                formated_data = sorted(formated_data, key=lambda x: x["ratio"])
            elif sort_by == "ratio/-1":  # Descending order
                formated_data = sorted(formated_data, key=lambda x: x["ratio"], reverse=True)
                                                                                                           
            paginated_rows, current_page, total_pages, total_items = paginate_data_postgres(
                total_count, formated_data, page, limit)
            
            
            data = new_utils.calculate_pagination(
                paginated_rows, current_page, limit, total_items)  

            parent_sku_str = parent_sku_str.rstrip(', ')

            data['meta']['month_rows'] = month_names
            data['meta'].update(price_list_data.get('meta', {}))    
            response['message'] = 'data retrived successfully'
            response['data'] = data
            response['status'] = 200
    finally:
        conn.close()

    return response

def save_replenishment_dashboard_data(store, payload, parent_sku, user):
    response = {
        "status": 400,
        "message": ""
    }
    
    # Get database connection
    conn = new_pgdb.get_connection(store['id'])
    
    try:
        classified_as = None

        # Extract the data from the payload
        terms_consignment = payload.get('terms_consignment')
        action_taken = payload.get('action_taken')

        # Get current timestamp
        current_time = datetime.utcnow()

        # Check if the parent_sku already exists in the replenishment_dashboard table
        query = "SELECT parent_sku FROM replenishment_dashboard WHERE parent_sku = :parent_sku"
        existing_record = conn.execute(text(query), {"parent_sku": parent_sku}).fetchone()

        query = f"""select MAX(rp.product_id) AS product_id, usm.user_name, usm.email_id FROM {AnalyticsDB.get_replenishment_products_table()} rp LEFT JOIN skuvault_catalog sv ON sv.parent_sku = rp.parent_sku LEFT JOIN user_supplier_mapping usm ON sv.primary_supplier = usm.suppliers 
                WHERE sv.primary_supplier <> 'Unknown' AND rp.parent_sku IS NOT NULL AND rp.parent_sku != '' AND rp.parent_sku = '{parent_sku}' group by usm.user_name, usm.email_id"""
        assignee_result = conn.execute(text(query)).fetchone()

        purchaser_email_id = assignee_result[2] if assignee_result else ''

        if existing_record:
            # Build dynamic update query
            update_fields = ["updated_at = :updated_at", "updated_by = :updated_by"]
            update_params = {
                "parent_sku": parent_sku,
                "updated_at": current_time,
                "updated_by": user
            }

            # if classified_as is not None:
            #     update_fields.append("classified_as = :classified_as")
            #     update_params['classified_as'] = classified_as

            if "classified_as_id" in payload:
                classified_as_id = payload.get('classified_as_id')
                if classified_as_id:
                    classified_as_query = f"SELECT name FROM replenishment_classified_as WHERE id = {classified_as_id}"
                    classified_as_result = conn.execute(text(classified_as_query)).fetchone()
                    classified_as = classified_as_result[0] if classified_as_result else ""
                else:
                    classified_as = ""
                update_fields.append("classified_as_id = :classified_as_id")
                update_fields.append("classified_as = :classified_as")
                update_params['classified_as_id'] = classified_as_id
                update_params['classified_as'] = classified_as

            if terms_consignment is not None:
                update_fields.append("terms_consignment = :terms_consignment")
                update_params['terms_consignment'] = terms_consignment

            if action_taken is not None:
                update_fields.append("action_taken = :action_taken")
                update_params['action_taken'] = action_taken

            if update_fields:
                # Construct the update SQL query for updating only provided fields
                update_query = f"""
                    UPDATE replenishment_dashboard 
                    SET {', '.join(update_fields)}
                    WHERE parent_sku = :parent_sku
                """
                update_res = conn.execute(text(update_query), update_params)
                conn.commit()
            
                if update_res.rowcount > 0:  # Check if any rows were updated
                    # Update resource column value using custom field mapping
                    data = {
                        'resource_id': parent_sku,
                        'table_name': 'analytics.replenishment_products',
                        'custom_field_value': classified_as or terms_consignment or action_taken,
                        'column_name': 'replenishment_dashboard.classified_as' if classified_as is not None else 'replenishment_dashboard.terms_consignment' if terms_consignment is not None else 'replenishment_dashboard.action_taken',
                        'assignee': purchaser_email_id,
                        'updated_by': user
                    }
                    task.send_task(task.UPDATE_CUSTOM_FIELD_TASK, args=(store['id'], data))
                    response['status'] = 200
                    response['message'] = "Record updated successfully"
                else:
                    response['status'] = 409
                    response['message'] = "Update failed, no rows affected."

        else:
            # Build dynamic insert query
            insert_fields = ["parent_sku", "updated_at", "updated_by"]
            insert_values = [":parent_sku", ":updated_at", ":updated_by"]
            insert_params = {
                "parent_sku": parent_sku,
                "updated_at": current_time,
                "updated_by": user
            }

            # if classified_as is not None:
            #     insert_fields.append("classified_as")
            #     insert_values.append(":classified_as")
            #     insert_params['classified_as'] = classified_as

            if "classified_as_id" in payload:
                classified_as_id = payload.get('classified_as_id')
                insert_fields.append("classified_as_id")
                insert_fields.append("classified_as")
                insert_values.append(":classified_as_id")
                insert_values.append(":classified_as")
                insert_params['classified_as_id'] = classified_as_id
                insert_params['classified_as'] = classified_as

            if terms_consignment is not None:
                insert_fields.append("terms_consignment")
                insert_values.append(":terms_consignment")
                insert_params['terms_consignment'] = terms_consignment

            if action_taken is not None:
                insert_fields.append("action_taken")
                insert_values.append(":action_taken")
                insert_params['action_taken'] = action_taken

            # Construct dynamic insert query
            insert_query = f"""
                INSERT INTO replenishment_dashboard ({', '.join(insert_fields)})
                VALUES ({', '.join(insert_values)})
            """
            insert_res = conn.execute(text(insert_query), insert_params)
            conn.commit()

            if insert_res.rowcount > 0:  # Check if any rows were inserted
                # Update resource column value using custom field mapping
                data = {
                    'resource_id': parent_sku,
                    'table_name': 'analytics.replenishment_products',
                    'custom_field_value': classified_as or terms_consignment or action_taken,
                    'column_name': 'replenishment_dashboard.classified_as' if classified_as is not None else 'replenishment_dashboard.terms_consignment' if terms_consignment is not None else 'replenishment_dashboard.action_taken',
                    'assignee': purchaser_email_id,
                    'updated_by': user
                }
                task.send_task(task.UPDATE_CUSTOM_FIELD_TASK, args=(store['id'], data))
                response['status'] = 200
                response['message'] = "New record created successfully"
            else:
                response['status'] = 409
                response['message'] = "Insert failed, no rows affected."

    except Exception as e:
        # Handle any errors that occur
        response['message'] = str(e)
    
    finally:
        # Close the database connection
        conn.close()
    
    return response

def save_replenishment_classifications(store, sku, new_classification, username):
    response = {"status": 400}
    conn = new_pgdb.get_connection(store['id'])

    def fetch_old_classification(sku, is_parent_sku):
        """Fetch the old classification from the skuvault_catalog table."""
        if is_parent_sku:
            query = text(f"SELECT classification FROM skuvault_catalog WHERE parent_sku = :sku")
        else:
            query = text("SELECT classification FROM skuvault_catalog WHERE sku = :sku")
        result = conn.execute(query, {"sku": sku}).fetchone()
        return result[0] if result else None

    def process_classification(sku, old_classification, is_parent_sku):
        """Handle the logic to update, insert, or delete a classification record."""
        if old_classification == new_classification:
            # Skip processing if the old and new classifications are the same
            # Delete the record if it returned to the original state
            query_delete = text("""
                DELETE FROM replenishment_classifications 
                WHERE sku = :sku
            """)
            conn.execute(query_delete, {"sku": sku})
            conn.commit()
            return
        query_check = text("""
            SELECT old_classification, new_classification 
            FROM replenishment_classifications 
            WHERE sku = :sku
        """)
        existing_record = conn.execute(query_check, {"sku": sku}).fetchone()

        if existing_record:
            query_update = text("""
                UPDATE replenishment_classifications 
                SET old_classification = :old_classification, 
                    new_classification = :new_classification, 
                    is_parent_sku = :is_parent_sku,
                    updated_by = :updated_by,
                    updated_at = CURRENT_TIMESTAMP
                WHERE sku = :sku
            """)
            conn.execute(query_update, {
                "sku": sku,
                "old_classification": old_classification,
                "new_classification": new_classification,
                "is_parent_sku": is_parent_sku,
                "updated_by": username
            })
            conn.commit()
        else:
            # Insert a new record
            query_insert = text("""
                INSERT INTO replenishment_classifications 
                (sku, old_classification, new_classification, is_parent_sku, created_by) 
                VALUES (:sku, :old_classification, :new_classification, :is_parent_sku, :created_by)
            """)
            conn.execute(query_insert, {
                "sku": sku,
                "old_classification": old_classification,
                "new_classification": new_classification,
                "is_parent_sku": is_parent_sku,
                "created_by": username
            })
            conn.commit()

    try:
        # Check if the SKU exists in the replenishment_variants table as a parent_sku
        query_variants = text(f"SELECT sku FROM {AnalyticsDB.get_replenishment_variants_table()} WHERE parent_sku = :sku")
        variants = conn.execute(query_variants, {"sku": sku}).fetchall()

        # Process parent_sku
        flag = True if variants else False
        old_classification = fetch_old_classification(sku, flag)
        process_classification(sku, old_classification, flag)  # True for is_parent_sku

        # Process variant SKUs
        for variant in variants:
            variant_sku = variant[0]
            old_classification = fetch_old_classification(variant_sku, False)
            process_classification(variant_sku, old_classification, False)  # False for is_parent_sku

        conn.commit()
        response['status'] = 200
        response['message'] = "Classification processed successfully"
    finally:
        conn.close()

    return response


def get_replenishment_classifications(store, search, page, limit, sort_array, classification_filter):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        count_query = f"""SELECT COUNT(*) FROM replenishment_classifications rc 
                LEFT JOIN {AnalyticsDB.get_replenishment_variants_table()} rv ON rv.sku = rc.sku"""
        

        offset = (page - 1) * limit
        query = f"""SELECT rc.id, rv.product_title, rc.sku, rc.old_classification, rc.new_classification, rc.created_by, rc.created_at 
                FROM replenishment_classifications rc 
                LEFT JOIN {AnalyticsDB.get_replenishment_variants_table()} rv ON rv.sku = rc.sku"""
        
        conditions = []
        
        # Handle search condition
        if search:
            conditions.append(f"(rc.sku ILIKE '%{search}%' OR rv.product_title ILIKE '%{search}%')")
        
        # Handle classification filters
        if classification_filter:
            classifications = classification_filter.split(',')
            classifications_str = ', '.join(f"'{classification.strip()}'" for classification in classifications)
            conditions.append(f"rc.new_classification IN ({classifications_str})")
        
        conditions.append("rc.is_parent_sku = false")
        # Combine all conditions
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
            count_query += " WHERE " + " AND ".join(conditions)

        if len(sort_array) > 0:
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            if sort_array[0] in ["product_title", "sku", "old_classification", "new_classification", "created_by", "created_at"]:
                query += f" ORDER BY {sort_array[0]} {sort_direction}"
        
        query += f" LIMIT {limit} OFFSET {offset}"
        
        total_records = conn.execute(text(count_query)).scalar()
        result = conn.execute(text(query))
        
        classifications = result.fetchall()
        data = []
        for row in classifications:
            if row[4] is not None:
                user = store_admin_db.fetch_user_by_username(store['id'], row[5])
                user = user['name']
            else:
                user = None
            data.append({
                "id": row[0],
                "product_title": row[1],
                "sku": row[2],
                "old_classification": row[3],
                "new_classification": row[4],
                "created_by": user,
                "created_at": convert_to_timestamp(row[6])
            })
        paginated_data = calculatePaginationData(data, page, limit, total_records)
        response['status'] = 200
        response['data'] = paginated_data
    finally:
        conn.close()
    return response


def update_replenishment_classifications(store, new_classification, approved, username, request_ids):
    response = {"status": 400}
    conn = new_pgdb.get_connection(store['id'])
    api_info = store_util.get_skuvault_api_info(store['id'])
    tenant_token = api_info['tenant_token']
    user_token = api_info['user_token']
    url = "products/updateProduct"
    
    try:
        for id in request_ids:
            # Update new_classification if provided
            if new_classification and approved is None:
                query_update = """
                    UPDATE replenishment_classifications 
                    SET new_classification = :new_classification, 
                        updated_by = :updated_by, 
                        updated_at = CURRENT_TIMESTAMP 
                    WHERE id = :id
                """
                res = conn.execute(text(query_update), {
                    "new_classification": new_classification,
                    "updated_by": username,
                    "id": id
                })
                conn.commit()
                if res.rowcount > 0:
                    response['status'] = 200
                    response['message'] = "Classification updated successfully"
                    return response

            
            # Handle approval logic
            if approved:
                # Fetch new_classification and sku from replenishment_classifications
                query_fetch = """
                    SELECT new_classification, sku, is_parent_sku 
                    FROM replenishment_classifications 
                    WHERE id = :id
                """
                record = conn.execute(text(query_fetch), {"id": id}).fetchone()

                if record:
                    fetched_classification, fetched_sku, is_parent_sku = record

                    # Update classification in skuvault_catalog
                    query_update_catalog = """
                        UPDATE skuvault_catalog 
                        SET classification = :classification 
                        WHERE sku = :sku
                    """
                    conn.execute(text(query_update_catalog), {
                        "classification": fetched_classification,
                        "sku": fetched_sku
                    })
                    conn.commit()

                    if is_parent_sku:
                        # Update the replenishment_products table for parent SKU
                        query_update_product = f"""
                            UPDATE skuvault_catalog
                            SET classification = :classification 
                            WHERE parent_sku = :sku
                        """
                        conn.execute(text(query_update_product), {
                            "classification": fetched_classification,
                            "sku": fetched_sku
                        })
                    else:
                        # Check if the fetched_sku exists as a parent_sku in replenishment_products
                        query_check_product = f"""
                            SELECT * FROM {AnalyticsDB.get_replenishment_products_table()} 
                            WHERE parent_sku = :sku
                        """
                        result = conn.execute(text(query_check_product), {"sku": fetched_sku}).fetchone()

                        if result:
                            # Update the classification in replenishment_products if parent_sku = fetched_sku exists
                            query_update_product = f"""
                                UPDATE skuvault_catalog
                                SET classification = :classification 
                                WHERE parent_sku = :sku
                            """
                            conn.execute(text(query_update_product), {
                                "classification": fetched_classification,
                                "sku": fetched_sku
                            })
                        
                        # Update the classification in replenishment_variants
                        # query_update_variant = f"""
                        #     UPDATE {AnalyticsDB.get_replenishment_variants_table()} 
                        #     SET classification = :classification 
                        #     WHERE sku = :sku
                        # """
                        # conn.execute(text(query_update_variant), {
                        #     "classification": fetched_classification,
                        #     "sku": fetched_sku
                        # })


                    body = {
                        "TenantToken": tenant_token,
                        "UserToken": user_token,
                        "Sku": fetched_sku,
                        "Classification": fetched_classification
                    }
                    bc.call_api_skuvault("POST", url, {}, body, False)
                    
                    # Delete the record from replenishment_classifications
                    query_delete = """
                        DELETE FROM replenishment_classifications 
                        WHERE id = :id
                    """
                    conn.execute(text(query_delete), {"id": id})
                    conn.commit()
                    
                    response['status'] = 200
                    response['message'] = "Classification change request approved successfully"
                else:
                    response['status'] = 404
                    response['message'] = "Record not found in replenishment_classifications"
            else:
                # Delete the record from replenishment_classifications
                query_delete = """
                    DELETE FROM replenishment_classifications 
                    WHERE id = :id
                """
                conn.execute(text(query_delete), {"id": id})
                conn.commit()
                response['status'] = 200
                response['message'] = "Classification change request rejected successfully"
    
    finally:
        conn.close()

    return response

def get_unique_classifications(store_id):
    response = {
        "status": 400,
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        query = text("SELECT DISTINCT classification FROM skuvault_catalog WHERE classification IS NOT NULL AND classification != ''")
        result = conn.execute(query)
        classifications  = result.fetchall()
        if classifications:
            data = [row[0] for row in classifications]
            response["status"] = 200
            response["data"] = data
        else:
            response["status"] = 200
            response["data"] = []
    except Exception as e:
        logger.error(traceback.format_exc())
    finally:
        conn.close()
    return response