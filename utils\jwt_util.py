import string
import secrets
import traceback
from jwt import encode
import jwt
import appconfig
import logging

logger = logging.getLogger("jwt_util")

alphabet = string.ascii_letters + string.digits

def secret_generator(n=64):
    return ''.join(secrets.choice(alphabet) for i in range(n))

def encode_jwt(payload, secret, jwt_token_algorithm='HS256'):
    encode(payload, secret, algorithm=jwt_token_algorithm)

def validate_jwt(jwt_token, client_id, secret):
    payload = None
    try:
        if jwt_token:
            tokenAlgo = jwt.get_unverified_header(jwt_token)['alg']
            jwtAlgoList = []
            if tokenAlgo:
                jwtAlgo = tokenAlgo
            
            jwtAlgoList.append(jwtAlgo)
            if not appconfig.is_debug_enabled():
                payload = jwt.decode(jwt=jwt_token, key=secret, algorithms=jwtAlgoList)
            else:
                payload = jwt.decode(jwt=jwt_token, options={"verify_signature": False}, algorithms=jwtAlgoList)
        else:
            raise Exception("Authorization header is empty")
    except Exception as e:
        logger.error(traceback.format_exc())
        raise Exception("Unauthorized!!")
    return payload