FROM us-central1-docker.pkg.dev/wise-perception-343614/ad-headless-services/python:3.12

# Install required system dependencies for OpenCV
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0

RUN pip install flask flask-restful flask-cors requests pymongo redis celery bcrypt wheel bigcommerce gunicorn gevent pyJwt schema gspread google-api-python-client oauth2client
RUN pip install pandas sqlalchemy psycopg2 pillow flask-socketio
RUN pip install pydantic
RUN pip install tzlocal bs4
RUN pip install opencv-python

RUN mkdir -p /app
COPY ./ /app/
RUN mkdir -p /app/logs
RUN mkdir -p /app/images
RUN chmod +x /app/entrypoint.sh
ENV APP_ENV=Production
ENV S247_LICENSE_KEY=in_9d4a6e6cc31b4f3d2e8d65c788be77d2
ENV APM_APP_NAME=midwest_admin_service
ENV APM_APP_PORT=8080 

EXPOSE 8080

WORKDIR /app

ENTRYPOINT ["/bin/sh", "entrypoint.sh"]
