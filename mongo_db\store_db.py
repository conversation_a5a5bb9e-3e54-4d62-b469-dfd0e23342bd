import logging
import datetime
from bson import ObjectId
import mongo_db

logger = logging.getLogger()

STORE_COLLECTION = "store"
EMAIL_TEMPLATE_COLLECTION = "email_templates"

def fetch_active_stores():
    db = mongo_db.get_admin_db_client()
    coll = db[STORE_COLLECTION]
    stores = []
    cur = coll.find({"status": "active"})
    for row in cur:
        data = mongo_db.process_data(row)
        stores.append(data)
    return stores

def fetch_store_by_storehash(store_hash):
    db = mongo_db.get_admin_db_client()
    store = db[STORE_COLLECTION].find_one({"bc_info.store_hash": store_hash, "status": "active"})
    return store

def fetch_store_by_id(store_id):
    db = mongo_db.get_admin_db_client()
    store = db[STORE_COLLECTION].find_one({"_id": ObjectId(str(store_id))})
    return store

def fetch_email_template(store_id, template_id):
    db = mongo_db.get_admin_db_client()
    template = db[EMAIL_TEMPLATE_COLLECTION].find_one({"_id": template_id})
    return template

def fetch_app_api_data(store_id, app_name):
    app_data = None
    store = fetch_store_by_id(store_id)
    if store:
        app_data = store.get("apps", {}).get(app_name, None)
    return app_data