from utils import bc, store_util

CART_API_URL = "v3/carts"
CART_ITEMS_API_URL = "v3/carts/{cart_id}/items/{item_id}"
ADD_CART_ITEMS_API_URL = "v3/carts/{cart_id}/items"
ADD_COUPON_API = "v3/checkouts/{cart_id}/coupons"

query_param = {
    "include": "redirect_urls"
}

def generate_bc_line_items(line_item):
    return {
            "quantity": line_item["quantity"],
            "product_id": line_item["product_id"],
            "variant_id": line_item["variant_id"],
            "list_price": line_item["price"],
        }

def process_bc_cart_data(local_cart, bc_cart):
    variant_sku_mapping = {}
    cart_line_items = local_cart["line_items"]
    for sku, item in cart_line_items.items():
        variant_sku_mapping[str(item["variant_id"])] = sku

    for _line_item in bc_cart["line_items"]["physical_items"]:
        sku = variant_sku_mapping[str(_line_item["variant_id"])]

        cart_line_item = cart_line_items[sku]
        cart_line_item["bc_id"] = _line_item["id"]
        cart_line_item["extended_list_price"] = _line_item["extended_list_price"]
        cart_line_item["extended_sale_price"] = _line_item["extended_sale_price"]

    if "redirect_urls" in bc_cart:
        local_cart["redirect_urls"] = bc_cart["redirect_urls"]

    local_cart["bc_cart_id"] = bc_cart["id"]
    local_cart["base_amount"] = bc_cart["base_amount"]
    local_cart["tax_included"] = bc_cart["tax_included"]
    local_cart["discount_amount"] = bc_cart["discount_amount"]
    local_cart["cart_amount"] = bc_cart["cart_amount"]
    local_cart["coupons"] = bc_cart["coupons"]
    local_cart["discounts"] = bc_cart["discounts"]
    return local_cart

def remove_coupon(store, bc_cart_id, coupon_code):
    url = ADD_COUPON_API.format(cart_id=bc_cart_id) + "/" + coupon_code
    bc_api = store_util.get_bc_api_creds(store)
    res = bc.call_api(bc_api, "DELETE", url)
    return res

def add_coupon(store, bc_cart_id, coupon_code):
    req_body = {
        "coupon_code": coupon_code
    }
    url = ADD_COUPON_API.format(cart_id=bc_cart_id)
    bc_api = store_util.get_bc_api_creds(store)
    res = bc.call_api(bc_api, "POST", url, req_body=req_body)
    return res

def add_line_items_to_bc_cart(store, customer_id, bc_cart_id, line_items):
    bc_api = store_util.get_bc_api_creds(store)
    _line_items = []
    for item in line_items:
        _line_items.append({
            "product_id": item["product_id"],
            "variant_id": item["variant_id"],
            "quantity": item["quantity"]
        })
    res = None
    if bc_cart_id:
        request_body = {
            "line_items": _line_items
        }
        url = CART_API_URL + "/" + bc_cart_id + "/items"
        res = bc.call_api(bc_api, "POST", url, query_params=query_param, req_body=request_body)
    else:
        request_body = {
            "customer_id": int(customer_id),
            "line_items": _line_items
        }
        res = bc.call_api(bc_api, "POST", CART_API_URL, query_params=query_param, req_body=request_body)

    return res

def create_bc_cart(store, cart):
    customer_id = cart["customer_id"]
    cart_line_items = []
    for item in cart["line_items"].values():
        cart_line_items.append(generate_bc_line_items(item))

    bc_api = store_util.get_bc_api_creds(store)
    request_body = {
        "channel_id": int(bc_api['channel_id']),
        "customer_id": int(customer_id),
        "line_items": cart_line_items
    }
    
    res = bc.call_api(bc_api, "POST", CART_API_URL, query_params=query_param, req_body=request_body)
    return res

def create_cart(store, cart):

    res = create_bc_cart(store, cart)
    #request = bc.get_bc_api_request_object(CART_API_URL, method="POST", query_param=query_param, request_body=request_body)
    #status_code, data = bc.process_bc_api_request(store, request)
    status_code = res.status_code
    if status_code == 200 or status_code == 201:
        data = res.json()["data"]
        cart = process_bc_cart_data(cart, data)

    return cart

def add_line_items(store, cart):
    bc_cart_id = cart["bc_cart_id"]
    if bc_cart_id:
        new_line_items = []
        for sku, line_item in cart["line_items"].items():
            if not "bc_id" in line_item or line_item["bc_id"]:
                new_line_items.append(generate_bc_line_items(line_item))
        if len(new_line_items) > 0:
            request_body = {
                "line_items": new_line_items
            }
            request = bc.get_bc_api_request_object(CART_API_URL.format(cart_id=bc_cart_id), 
                        method="POST", query_param=query_param, request_body=request_body)
            status_code, data = bc.process_bc_api_request(store, request)
            if status_code == 200 or status_code == 201:
                cart = process_bc_cart_data(cart, data)
    else:
        cart = create_cart(store, cart)
    return cart

def update_line_items(store, cart, line_items):
    bc_cart_id = cart["bc_cart_id"]
    if not bc_cart_id or len(line_items) == 0:
        return cart
    
    bc_line_items = []
    for _line_item in line_items:
        bc_line_items.append(generate_bc_line_items(_line_item))

    request_body = {
        "line_items": new_line_items
    }
    request = bc.get_bc_api_request_object(CART_API_URL.format(cart_id=bc_cart_id), 
                method="POST", query_param=query_param, request_body=request_body)
    status_code, data = bc.process_bc_api_request(store, request)
    if status_code == 200 or status_code == 201:
        cart = process_bc_cart_data(cart, data)

def delete_cart(store, cart_id):
    url = CART_API_URL + "/" + str(cart_id)
    bc_api = store_util.get_bc_api_creds(store)
    res = bc.call_api(bc_api, "DELETE", url, query_params=query_param)

def fetch_cart(store, cart_id):
    url = CART_API_URL + "/" + cart_id
    bc_api = store_util.get_bc_api_creds(store)
    return bc.call_api(bc_api, "GET", url, query_params=query_param)

def delete_line_item(store, cart_id, line_item_id):
    url = CART_ITEMS_API_URL.format(cart_id=cart_id, item_id=line_item_id)
    bc_api = store_util.get_bc_api_creds(store)
    return bc.call_api(bc_api, "DELETE", url, query_params=query_param)

def update_line_item(store, cart_id, line_item):
    res = None
    if line_item['quantity'] == 0:
        res = delete_line_item(store, cart_id, line_item['id'])
    else:
        url = CART_ITEMS_API_URL.format(cart_id=cart_id, item_id=line_item['id'])
        req_body = {
            "line_item": {
                "product_id": line_item['product_id'],
                "variant_id": line_item['variant_id'],
                "quantity": line_item['quantity']
            }
        }
        bc_api = store_util.get_bc_api_creds(store)
        res = bc.call_api(bc_api, "PUT", url, req_body=req_body, query_params=query_param)
    return res

def add_line_items_to_cart(store, cart_id, line_items):
    res = None
    url = ADD_CART_ITEMS_API_URL.format(cart_id=cart_id)
    req_body = {
        "line_items": line_items
    }
    bc_api = store_util.get_bc_api_creds(store)
    res = bc.call_api(bc_api, "POST", url, req_body=req_body)
    return res
