from flask import request
import logging
import traceback
from api import APIResource
from analytics import replenishment
from new_mongodb import store_admin_db

logger = logging.getLogger()


class Columns(APIResource):
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Table Columns POST")
        try:
            payload = request.get_json(force=True)
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            if user is None:
                return {"message": "Unauthorized access."}, 401
            if payload:
                result = replenishment.save_table_columns(store, payload, user)
                if result['status'] == 200:
                    return {"message": result['message']}, 200
                else:
                    return {"message": result['message']}, result['status']
            else:    
                return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Table Columns POST")

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering user specific columns GET")
        try:
            query_params = request.args.to_dict()
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            if user is None:
                return {"message": "Unauthorized access."}, 401
            res = replenishment.get_table_columns(store, user, query_params)
            
            if res['status'] == 200:
                return res['data'], 200
        finally:
            logger.debug("Exiting user specific columns GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def post(self):
        return self.execute_store_request(request, self.post_executor)
