import json
from flask import request
from api import APIResource
from appbuilder import appbuilder
import logging
import traceback
from utils import bc
logger = logging.getLogger()


class AppBuilderApi(APIResource):

    def post_executor(self, request, token_payload, store):
        logger.debug("Entering App Builder POST")
        try:
            payload = request.get_json(force=True)
            if payload:
                result = appbuilder.CreateMainApp(
                    store, payload)
                if result['status'] == 200:
                    return {"message": result['message']}, 200
                else:
                    return {"message": result['message']}, result['status']
            return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting App Builder POST")

    def put_executor(self, request, token_payload, store):
        logger.debug("Entering App Builder PUT")
        try:
            payload = request.get_json(force=True)
            if payload:
                result = appbuilder.UpdateMainApp(payload)
                if result['status'] == 200:
                    return {"message": result['message']}, 200
                else:
                    return {"message": result['message']}, result['status']
            return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting App Builder PUT")

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering App Builder data GET")
        try:
            query_params = request.args.to_dict()
            if query_params:
                res = appbuilder.get_all_main_app(query_params)
                if res['status'] == 200:
                    return res['data'], 200
                else:
                    return {"message": res['message']}, 200
            else:
                return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting App Builder data GET")

    def delete_executor(self, request, token_payload, store):
        logger.debug("Entering Delete Main Section")
        try:
            query_params = request.args.to_dict()
            if query_params:
                result = appbuilder.DeleteMainApp(query_params)
                if result['status'] == 200:
                    return {"message": result['message']}, 200
                else:
                    return {"message": result['message']}, result['status']
            else:
                return {"error": "Invalid request."}, 400
            # return {"message": "Cleared successfully."}, 200
        finally:
            logger.debug("Exiting Delete Main Section")

    def post(self):
        return self.execute_store_request(request, self.post_executor)

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def delete(self):
        return self.execute_store_request(request, self.delete_executor)

    def put(self):
        return self.execute_store_request(request, self.put_executor)

class AppBuilderImageOperation(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            query_params = request.args.to_dict()
            res = appbuilder.getAppBuilderImage(query_params)
            return res
        finally:
            logger.debug("Exiting Webpage GET")

    def post_executor(self, request, token_payload, store):
        try:
            req_body = request.files
            res = appbuilder.setAppBuilderImage(req_body)

            if res['status'] == 200:
                return {"status": res['message']}, 200
            else:
                return {"status": res['message']}, 500
        finally:
            logger.debug("Exiting Image Upload POST")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def post(self):
        return self.execute_store_request(request, self.post_executor)

        
class SubSectionBuilderApi(APIResource):
    def post_executor(self, request, token_payload, store, parent_section):
        logger.debug("Entering Sub Section Builder POST")
        try:
            payload = request.get_json(force=True)
            if payload:
                result = appbuilder.CreateSubSection(
                    store, payload, parent_section)
                if result['status'] == 200:
                    return {"message": result['message']}, 200
                else:
                    return {"message": result['message']}, result['status']
            return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Sub Section Builder POST")

    def delete_executor(self, request, token_payload, store, parent_section):
        logger.debug("Entering Delete Sub Section")
        try:
            
            query_params = request.args.to_dict()
            if query_params:
                result = appbuilder.DeleteSubSection(
                    query_params, parent_section)
                if result['status'] == 200:
                    return {"message": result['message']}, 200
                else:
                    return {"message": result['message']}, result['status']
            else:
                return {"error": "Invalid request."}, 400
            # return {"message": "Cleared successfully."}, 200
        finally:
            logger.debug("Exiting Delete Sub Section")

    def get_executor(self, request, token_payload, store, parent_section):
        logger.debug("Entering sub section data GET")
        try:
            query_params = request.args.to_dict()
            if query_params:
                res = appbuilder.get_all_subsection(query_params,parent_section)
                if res['status'] == 200:
                    return res['data'], 200
                else:
                    return {"message": res['message']}, 422
            else:
                return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Sub Section data GET")
 
    def put_executor(self, request, token_payload, store, parent_section):
        logger.debug("Entering Sub Section PUT")
        try:
            payload = request.get_json(force=True)
            if payload:
                result = appbuilder.UpdateSubSection(payload, parent_section)
                if result['status'] == 200:
                    return {"message": result['message']}, 200
                else:
                    return {"message": result['message']}, result['status']
            return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Sub Section PUT") 

    def post(self, parent_section):
        return self.execute_store_request(request, self.post_executor, parent_section)

    def delete(self, parent_section):
        return self.execute_store_request(request, self.delete_executor, parent_section)
    def get(self, parent_section):
        return self.execute_store_request(request, self.get_executor, parent_section)
    def put(self, parent_section):
        return self.execute_store_request(request, self.put_executor, parent_section)


class LeafBuilderApi(APIResource):
    def post_executor(self, request, token_payload, store, grand_parent, parent):
        logger.debug("Entering Leaf Section Builder POST")
        try:
            payload = request.get_json(force=True)
            if payload:
                result = appbuilder.CreateLeafSection(
                    store, payload, grand_parent, parent)
                if result['status'] == 200:
                    return {"message": result['message']}, 200
                else:
                    return {"message": result['message']}, result['status']
            return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Leaf Section Builder POST")

    def delete_executor(self, request, token_payload, store, grand_parent, parent):
        logger.debug("Entering Delete Leaf Section")
        try:
            query_params = request.args.to_dict()
            if query_params:
                result = appbuilder.DeleteLeafSection(
                    query_params, grand_parent, parent)
                if result['status'] == 200:
                    return {"message": result['message']}, 200
                else:
                    return {"message": result['message']}, result['status']
            else:
                return {"error": "Invalid request."}, 400
            # return {"message": "Cleared successfully."}, 200
        finally:
            logger.debug("Exiting Delete Leaf Section")
    
    def get_executor(self, request, token_payload, store,grand_parent, parent):
        logger.debug("Entering leaf section data GET")
        try:
            query_params = request.args.to_dict()
            if query_params:
                res = appbuilder.get_all_leaf(query_params,grand_parent, parent)
                if res['status'] == 200:
                    return res['data'], 200
            else:
                return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Sub Section data GET")
    
    def put_executor(self, request, token_payload, store, grand_parent, parent):
        logger.debug("Entering leaf Section PUT")
        try:
            payload = request.get_json(force=True)
            if payload:
                result = appbuilder.UpdateLeafSection(payload, grand_parent, parent)
                if result['status'] == 200:
                    return {"message": result['message']}, 200
                else:
                    return {"message": result['message']}, result['status']
            return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Sub Section PUT")    

    def post(self, grand_parent, parent):
        return self.execute_store_request(request, self.post_executor, grand_parent, parent)
    
    def get(self, grand_parent, parent):
        return self.execute_store_request(request, self.get_executor, grand_parent, parent)
    
    def put(self, grand_parent, parent):
        return self.execute_store_request(request, self.put_executor, grand_parent, parent)
    
    def delete(self, grand_parent, parent):
        return self.execute_store_request(request, self.delete_executor, grand_parent, parent)

class GetBCData(APIResource):
    def post_executor(self, request, token_payload, store):
        try:
            customer_id = None
            req_body = request.get_json(force=True)
            s = req_body['query_params']
            key, value = s.split(':')
            if value.isdigit():
               value = int(value)
            result = {key: value}
            req_body['query_params']=result
            status, res_body = bc.bc_api_request(store, req_body, customer_id)
            return res_body, status
        finally:
            logger.debug("Exiting LoginAPI POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
class GetZohoformData(APIResource):
    def post_executor(self, request, token_payload, store):
        try:
            customer_id = None
            req_body = request.get_json(force=True)
            status, res_body = bc.zoho_api_request(store, req_body, customer_id)
            return res_body, status
        finally:
            logger.debug("Exiting LoginAPI POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)