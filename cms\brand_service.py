import new_mongodb
from cms import process_cdn_image_url
import new_utils
from utils import store_util
from bson import ObjectId
from pymongo.collation import Collation
from fields.cms_fields import cms_brand_fields


def _create_reg_ex_query(payload, additionalQuery):
    query = {
        "$or": [],
    }    

    for i in payload["filterBy"]:
        query['$or'].append({i: {"$regex": payload['filter'], "$options": "i"}},)

    if "type" not in query and "type" in payload:
            query["type"] = payload["type"]

    if "status" in payload:
        if payload["status"] == "active":
            query["is_visible"] = True
        elif payload["status"] == "inactive":
            query["is_visible"] = False
        elif payload["status"] == "out_of_stock":
            query["inventory_level"] = 0              
    query.update(additionalQuery)
    return query

def get_paginated_records_brands(store, payload, fields, additionalQuery):
    sort = {
        'sort_by': payload['sort_by'] or 'date_created'
    }

    if payload['sort_order'] == 'asc':
        sort['sort_order'] = 1
    else:
        sort['sort_order'] = -1

    limit = int(payload["limit"]) if payload.__contains__("limit") else 10
    page = int(payload["page"]) if payload.__contains__("page") else 1
    skips = payload['skips'] if payload.__contains__('skips') else 0

    query = _create_reg_ex_query(payload, additionalQuery) if len(payload["filterBy"]) else {}
    
    skips = limit * (page - 1)
    collation = Collation(locale='en', strength=2)
    cms_brand_collection = new_mongodb.StoreAdminDBCollections.CMS_BRANDS_COLLECTION
    data = new_mongodb.fetchall_documents_from_admin_collection(store['id'], cms_brand_collection, query, fields) \
            .collation(collation).sort(sort['sort_by'], sort['sort_order']).skip(skips).limit(limit)
    
    data = new_mongodb.process_documents(data)
    
    document_length = new_mongodb.count_documents_admin_collection(store['id'], cms_brand_collection, query)

    return new_utils.parse_json(data), document_length, page, limit

def get_data(store, body):    
    cdn_baseurl = store_util.get_cdn_base_url(store)    
    result = []        
    if 'webpage_id' in body:
        res = {}
        webpage_id = body['webpage_id']
        query = {"_id": ObjectId(str(webpage_id))}
        webPage = new_mongodb.fetch_one_document_from_admin_collection(store['id'], \
                    new_mongodb.StoreAdminDBCollections.CMS_BRANDS_COLLECTION, query)
        
        if 'preview_state' in webPage and webPage['preview_state']:               
            preview_state = webPage['preview_state']
        else:
            preview_state = webPage['default_layout']

        preview_state = process_cdn_image_url(cdn_baseurl, preview_state)

        res['id'] = webPage['id']
        res['name'] = webPage['name']
        res['created_at'] = webPage['created_at']
        res['updated_at'] = webPage['updated_at']
        res['url'] = webPage['url']
        res['is_customers_only'] = webPage['is_customers_only']
        res['versions'] = preview_state
        result.append(res)
    else:            
        body['filterBy'] = ['name']            
        cms_brand_collection = new_mongodb.StoreAdminDBCollections.CMS_BRANDS_COLLECTION
        pages, total_data_length, paginationPage, limit = get_paginated_records_brands(store, body, cms_brand_fields,'')
        brands = new_mongodb.fetchall_documents_from_admin_collection(store['id'], cms_brand_collection)
        nameResult = []
        for page in brands:
            test = {}
            test['Name'] = page['name']
            test['URL'] = page['url']
            nameResult.append(test)

        for page in pages:
            res = {}
            
            if page['url'] is None or page['url'] == "":
                continue
            product_count = new_mongodb.fetchall_documents_from_storefront_collection(store['id'], new_mongodb.StoreDBCollections.PRODUCTS, {"brand_id": page['bc_id']})
            versions = page['versions']
            if (len(versions) == 0):
                activeVersion = page['default_layout']
            else:
                for version in versions:
                    if (version['status'] == 'active'):
                        activeVersion = version
                        break
            
            activeVersion = process_cdn_image_url(cdn_baseurl, activeVersion)
            
            res['id'] = page['id']
            res['name'] = page['name']
            res['created_at'] = page['created_at']
            res['updated_at'] = page['updated_at']
            res['url'] = page['url']
            res['status'] = page['status']
            res["image_url"]=page['image_url']
            res["product_count"]=product_count
            res["type"] = page["type"]
            res['versions'] = activeVersion

            result.append(res)
        result = new_utils.calculate_pagination(result, paginationPage, limit, total_data_length)
        result['meta']['name_info'] = nameResult
    return result  