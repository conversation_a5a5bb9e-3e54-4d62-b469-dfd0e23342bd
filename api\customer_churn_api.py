from flask import request
import logging
import traceback
from api import APIResource
from analytics import customer_churn_report
from new_mongodb import store_admin_db
logger = logging.getLogger()


class CustomerChurnReport(APIResource):
    def get_executor(self, request, token_payload, store):
      logger.debug("Entering Customer Churn Report GET")
      try:
        query_params = request.args.to_dict()
        username = token_payload['username'] 
        user = None
        if username and username != '':
            user = store_admin_db.fetch_user_by_username(store['id'], username) 
        if user is None:
            return {"message": "Unauthorized access."}, 401
        page = query_params.get('page', 1)
        limit = query_params.get('limit', 10)
        sort_by = query_params.get('sort_by', '').strip()
        sort_array = sort_by.split("/") if sort_by != '' else []
        search = query_params.get('search', '').strip()
        res = customer_churn_report.get_customer_churn_report(store['id'], page, limit, sort_array, search)
        if res['status'] == 200:
            return res['data'], 200
      finally:
        logger.debug("Exiting Customer Churn Report GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

