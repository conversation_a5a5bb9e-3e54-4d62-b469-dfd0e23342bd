import logging
import new_pgdb
from sqlalchemy import text
import traceback
from sqlalchemy import text
from new_pgdb.analytics_db import AnalyticsDB
from appconfig import is_pgdb_read_only_enabled


def get_inventory_report(store):
    response = {
        "status": 400,
        "data": []
    }
    conn = new_pgdb.get_connection(store['id'], read_only=is_pgdb_read_only_enabled())
    try:
        query = text(
            f"""
            SELECT SUM(rp.quantity_available) AS total_quantity, EXTRACT(YEAR FROM p.date_created) AS year
            FROM {AnalyticsDB.get_replenishment_products_table()} rp
            JOIN products p ON rp.product_id = p.product_id
            GROUP BY year
            ORDER BY year
            """
        )
        
        results = conn.execute(query)
        report_data = []

        # Iterate over query results and format them into a list of dictionaries
        for row in results:
            report_data.append({
                "total_quantity": int(row[0]),
                "year": int(row[1])
            })

        response['status'] = 200
        response['data'] = {"data" : report_data}

    except Exception as e:
        response['status'] = 400
        response['message'] = str(e)
        logging.error(traceback.format_exc())
    finally:
        conn.close()

    return response
