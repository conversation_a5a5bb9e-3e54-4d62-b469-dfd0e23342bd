from datetime import datetime, timezone
from new_mongodb import StoreAdminDBCollections, StoreDBCollections, liquidated_products_db, store_admin_db, update_document_in_admin_collection, fetchall_documents_from_storefront_collection, fetch_one_document_from_admin_collection, fetch_one_document_from_storefront_collection, get_store_db_client_for_store_id, process_documents, delete_documents_from_storefront_collection
from utils.common import calculatePaginationData
import task
import new_pgdb
from utils import store_util, bc
from sqlalchemy import text
from collections import defaultdict


def get_liquidated_product_report(store, page, limit, sort_by, search):
    # Step 1: Fetch liquidated product IDs from the store_info collection
    store_info_query = {"type": "liquidated_products"}
    store_info_projection = {"product_ids": 1}
    store_info_record = fetch_one_document_from_admin_collection(
        store_id=store['id'],
        collection_name=StoreAdminDBCollections.STORE_INFO_COLLECTION,
        query=store_info_query,
        projection=store_info_projection
    )

    if not store_info_record or "product_ids" not in store_info_record:
        return {
            "data": [],
            "total_count": 0,
            "message": "No liquidated products found."
        }

    product_ids = store_info_record["product_ids"]
    if not product_ids:
        return {
            "data": [],
            "total_count": 0,
            "message": "No liquidated products found."
        }
    
    # Step 2: Fetch products data based on the retrieved product IDs
    products_query = {"_id": {"$in": product_ids}}
    products_projection = {
        "id": 1, "name": 1, "sku": 1, "price": 1, "calculated_price": 1, "inventory_level": 1, "variants": 1, "custom_fields": 1, "images": 1
    }
    products_data = fetchall_documents_from_storefront_collection(
        store_id=store['id'],
        collection_name=StoreDBCollections.PRODUCTS,
        query=products_query,
        projection=products_projection
    )
    products = list(products_data)

    blocked_qty_dict = get_blocked_qty(store, product_ids)

    if search:
        search_lower = search.lower()
        products = [
            product for product in products
            if (
                "name" in product and search_lower in product["name"].lower()
            ) or (
                "sku" in product and search_lower in product["sku"].lower()
            )
        ]

    # Step 3: Process products with inline logic
    processed_products = []
    for product in products:
        # Fetch latest new_price from LIQUIDATE_PRODUCT_PRICE_LOGS
        price_log_query = {"product_id": product["_id"]}
        price_log_projection = {"log_history": 1}
        price_log_record = fetch_one_document_from_storefront_collection(
            store_id=store['id'],
            collection_name=StoreDBCollections.LIQUIDATE_PRODUCT_PRICE_LOGS,
            query=price_log_query,
            projection=price_log_projection
        )

        latest_price = None
        if price_log_record and "log_history" in price_log_record:
            log_history = price_log_record["log_history"]
            latest_log = max(log_history, key=lambda log: log.get("updated_at", 0), default=None)
            if latest_log and "new_price" in latest_log:
                latest_price = latest_log["new_price"]

        # Extract `pack_count` from `custom_field`
        custom_fields = product.get("custom_fields", [])
        pack_count = next(
            (
                int(field["value"]) 
                for field in custom_fields 
                if field["name"].lower().strip() == "pack count" and field["value"].isdigit()
            ),
            None
        )

        # Variant-level calculations
        variants_info = []
        total_blocked_qty = 0
        for variant in product.get("variants", []):
            inventory_level = variant.get("inventory_level", 0)
            variant_pack_count = pack_count  # Use the same `pack_count` for variants

            piece_count = inventory_level * variant_pack_count if variant_pack_count else None

            price_per_piece = (
                latest_price
                if latest_price is not None and latest_price != ""
                # else (variant.get("price", 0) / variant_pack_count if variant_pack_count else None)
                else (
                    (variant.get("calculated_price", 0) / variant_pack_count)
                    if variant_pack_count is not None and variant.get("calculated_price", 0) else 0
                )
            )

            option_values = variant.get("option_values", [])
            variant_name = " - ".join(option.get("label", "") for option in option_values) if option_values else ""

            # Get blocked quantity for this variant
            blocked_qty = blocked_qty_dict.get(variant.get("id"), 0)
            total_blocked_qty += blocked_qty  # Sum for product level

            variants_info.append({
                "id": variant.get("id"),
                "product_name": product.get("name"),
                "variant_name": variant_name,
                "sku": variant.get("sku"),
                "inventory_level": inventory_level,
                "pack_count": variant_pack_count,
                "piece_count": piece_count,
                "price_per_piece": round(price_per_piece, 2),
                "calculated_price": variant.get("calculated_price", 0),
                "blocked_qty": blocked_qty
            })

        # Parent-level calculations
        inventory_level = product.get("inventory_level", 0)
        piece_count = inventory_level * pack_count if pack_count else None
        
        price_per_piece = (
            latest_price
            if latest_price is not None and latest_price != ""
            # else (product.get("price", 0) / pack_count if pack_count else None)
            else (
                (product.get("calculated_price", 0) / pack_count)
                if pack_count is not None and product.get("calculated_price", 0) else 0
            )
        )

        # Fetch thumbnail image URL
        images = product.get("images", [])
        thumbnail_url = next(
            (img["url_thumbnail"] for img in images if img.get("is_thumbnail") is True),
            images[0]["url_thumbnail"] if images else None
        )


        processed_products.append({
            "id": product.get("id"),
            "name": product.get("name"),
            "sku": product.get("sku"),
            "inventory_level": inventory_level,
            "variant_count": len(product.get("variants", [])),
            "pack_count": pack_count,
            "piece_count": piece_count,
            "price_per_piece": round(price_per_piece, 2),
            "calculated_price": product.get("calculated_price", 0),
            "thumbnail_url": thumbnail_url,
            "total_blocked_qty": total_blocked_qty,
            "variants_info": variants_info
        })

    # Step 4: Extract field and sort order from sort_by
    if '/' in sort_by:
        field, order = sort_by.split('/')
        sort_order = order == '1'  # True for ascending, False for descending
    else:
        field = "name"
        sort_order = True  # Default to ascending order

    # Sort the products list with explicit handling for None
    processed_products.sort(
        key=lambda x: (
            x.get(field) is not None,  # False (None values first), True (others)
            x.get(field, float('-inf') if isinstance(x.get(field), (int, float)) else "")  # Handle numbers and strings separately
        ),
        reverse=not sort_order  # Reverse if sorting is descending
    )

    # Step 5: Apply pagination
    total_count = len(processed_products)
    start_index = (page - 1) * limit
    end_index = start_index + limit
    paginated_products = processed_products[start_index:end_index]

    data = calculatePaginationData(paginated_products, page, limit, total_count)

    # Step 6: Return the final response
    return data


def get_blocked_qty(store, product_ids):
    if not product_ids:
        return {}

    conn = new_pgdb.get_connection(store['id'])
    try:
        api = store_util.get_bc_api_creds(store)

        # Step 1: Fetch all unique blocked order_ids for the given product_ids
        orders_query = """
            SELECT DISTINCT bo.order_id
            FROM blocked_orders bo
            JOIN order_line_items oli ON bo.order_id = oli.order_id
            WHERE oli.product_id IN :product_ids
        """
        result = conn.execute(text(orders_query), {'product_ids': tuple(product_ids)})
        order_ids = [row[0] for row in result.fetchall()]  # Extract order_ids directly


        blocked_qty_dict = defaultdict(int)  # {variant_id: quantity}

        # Step 2: Process each order and fetch blocked quantities
        for order_id in order_ids:
            res = bc.get_order_products(api, str(order_id))
            if res:
                for item in res:
                    variant_id = item.get('variant_id')
                    quantity = item.get('quantity', 0)
                    if variant_id:
                        blocked_qty_dict[variant_id] += quantity  # Accumulate quantity per variant

        return dict(blocked_qty_dict)  # Convert defaultdict to regular dict
    except Exception as e:
        print(e)
        return {}
    finally:
        if conn:
            conn.close()
        

def get_liquidated_product_report_csv(store, query_params):
    response = {
        "status": 400,
    }
    if query_params:
        task_id = task.send_task(task.SEND_LIQUIDATED_PRODUCTS_CSV_MAIL_TASK, args=(store['id'], query_params))
        if task_id:
            response['status'] = 200
            response['message'] = f"We are generating the data you requested for download as a CSV file. This process may take a few minutes. Once complete, the CSV will be sent to your email: {query_params['username']}."
        else:
            response['status'] = 400
            response['message'] = 'Something went wrong to generate the CSV file.'
    else:
        response['status'] = 400
        response['message'] = 'query params are required'

    return response
    

def update_liquidated_product(store, product_id, username, new_price=None, is_liquidated=None):
    response = {
        "status": 400
    }
    # If `is_liquidated` is provided, update the `store_info` collection
    if is_liquidated is not None:
        if not is_liquidated:
            # Remove the product_id from the product_ids array where type = 'liquidated_products'
            query = {"type": "liquidated_products"}
            update_data = {"$pull": {"product_ids": product_id}}
            result = update_document_in_admin_collection(
                store_id=store['id'],
                collection_name=StoreAdminDBCollections.STORE_INFO_COLLECTION,
                query=query,
                update_data=update_data
            )
            delete_documents_from_storefront_collection(store_id=store['id'], collection_name=StoreDBCollections.LIQUIDATE_PRODUCT_PRICE_LOGS, query={"product_id": product_id})
            if result.modified_count == 0:
                response["status"] = 400
                response["message"] = "Product was not removed from liquidated_products."
            else:
                response["status"] = 200
                response["message"] = "Product removed from liquidated_products."

    # If `new_price` is provided, update the `liquidate_product_price_logs` collection
    if new_price is not None:
        # Fetch the existing record for the product_id
        price_log_query = {"product_id": product_id}
        existing_record = fetch_one_document_from_storefront_collection(
            store_id=store['id'],
            collection_name=StoreDBCollections.LIQUIDATE_PRODUCT_PRICE_LOGS,
            query=price_log_query,
        )
        
        # Determine the old price
        if existing_record and "log_history" in existing_record and existing_record["log_history"]:
            # If there is an entry in the log history, get the most recent log based on updated_at
            log_history = sorted(existing_record["log_history"], key=lambda x: x["updated_at"], reverse=True)
            latest_log = log_history[0]
            old_price = latest_log["new_price"]
        else:
            # If no entry exists in log_history, calculate the old price using the product data
            product_query = {"_id": product_id}
            products_projection = {
                "id": 1, "calculated_price": 1, "inventory_level": 1, "custom_fields": 1
            }
            product = fetch_one_document_from_storefront_collection(
                store_id=store['id'],
                collection_name=StoreDBCollections.PRODUCTS,
                query=product_query,
                projection=products_projection
            )

            custom_fields = product.get("custom_fields", [])
            pack_count = next(
                (
                    int(field["value"]) 
                    for field in custom_fields 
                    if field["name"].lower() == "pack count" and field["value"].isdigit()
                ),
                None
            )
            inventory_level = product.get("inventory_level", 0)
            piece_count = inventory_level * pack_count if pack_count else None
            
            # Calculate price per piece based on inventory_level and calculated_price
            calculated_price = product.get("calculated_price", 0)
            if calculated_price and piece_count:
                old_price = calculated_price / pack_count
            else:
                old_price = None

        # Prepare the log entry
        updated_by = username
        updated_at = updated_at = datetime.now(timezone.utc)
        log_entry = {
            "updated_by": updated_by,
            "updated_at": updated_at,
            "old_price": old_price if old_price != "" else None,
            "new_price": new_price if new_price != "" else None
        }

        # Update or insert the record with the new log
        update_data = {
            "$setOnInsert": {"product_id": product_id},
            "$push": {"log_history": log_entry}
        }
        update_result = liquidated_products_db.upsert_document_in_collection(
            store_id=store['id'],
            collection_name=StoreDBCollections.LIQUIDATE_PRODUCT_PRICE_LOGS,
            query=price_log_query,
            update_data=update_data
        )

        if update_result.upserted_id or update_result.modified_count > 0:
            response["status"] = 200
            response["message"] = "Product price log updated."
        else:
            response["status"] = 400
            response["message"] = "Failed to update product price log."

    return response


def get_liquidated_product_logs(store, product_id, page, limit, sort_by):
    db = get_store_db_client_for_store_id(store['id'])

    # Extract field and sort order from sort_by
    if '/' in sort_by:
        field, order = sort_by.split('/')
        sort_order = 'asc' if order == '1' else 'desc'
    else:
        # Default sort order if format is not correct
        field = 'updated_at'
        sort_order = 'desc'

    # MongoDB aggregation pipeline for filtering and sorting
    pipeline = [
        {"$match": {"product_id": product_id}},
        {"$unwind": "$log_history"},  # Unwind the log_history array
        {
            "$project": {
                "_id": 0,
                "updated_by": "$log_history.updated_by",
                "updated_at": "$log_history.updated_at",
                "old_price": "$log_history.old_price",
                "new_price": "$log_history.new_price",
            }
        },
        {"$sort": {field: 1 if sort_order == 'asc' else -1}},  # Apply sorting
    ]

    # Add pagination to the pipeline
    skips = limit * (page - 1)
    pipeline.extend([
        {"$skip": skips},
        {"$limit": limit}
    ])
    # Fetch logs and total count
    logs = db[StoreDBCollections.LIQUIDATE_PRODUCT_PRICE_LOGS].aggregate(pipeline)
    logs = process_documents(logs)

    for log in logs:
        if "updated_by" in log:
            user_info = store_admin_db.fetch_user_by_username(store['id'], log['updated_by'])
            if user_info:
                log["updated_by_name"] = user_info["name"]
            else:
                log["updated_by_name"] = None

    # total_data_length = db[StoreDBCollections.LIQUIDATE_PRODUCT_PRICE_LOGS].count_documents({"product_id": product_id})

    # Process logs and calculate pagination data
    total_data_length = db[StoreDBCollections.LIQUIDATE_PRODUCT_PRICE_LOGS].count_documents({"product_id": product_id})

    product = fetch_one_document_from_storefront_collection(store['id'], StoreDBCollections.PRODUCTS, {"_id": product_id})
    product_name = product['name'] if product else None

    # Process logs and calculate pagination data
    data = calculatePaginationData(logs, page, limit, total_data_length)
    data["meta"]["product_name"] = product_name

    return data
