from flask import request
import logging
import traceback
from api import APIResource
from schemas.loyalty_app import reward_schema, reward_update_schema
from utils import store_util
from customers.loyalty_points import loyalty_points_list

logger = logging.getLogger()


class CustomersWithPoints(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Customers With Points GET")
        try:
            query_params = request.args.to_dict()
            res = loyalty_points_list.get_all_loyalty_list(store, query_params)
            if res['status'] == 200:
                return  res['data'], res['status']
            else:
                return {'message': res['message']}, res['status']
            # Return response ...            
        finally:
            logger.debug("Exiting Customers With Points GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class Rewards(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Loyalty Rewards GET")
        try:
            query_params = request.args.to_dict()
            res = loyalty_points_list.get_all_rewards(store, query_params)
            # Return response ...
            return res, 200
        finally:
            logger.debug("Exiting Loyalty Rewards GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

# create rewards


class CreateReward(APIResource):
    def post_executor(self, request, token_payload, store):
        try:
            req_body = request.get_json(force=True)
            validated_data = reward_schema.validate(req_body)
            res = loyalty_points_list.create_reward(store,validated_data)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409
        finally:
            logger.debug("Exiting Loyalty Rewards POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)

# upadate and delete webpage


class RewardOperation(APIResource):
    def put_executor(self, request, token_payload, store, reward_id):
        try:
            req_body = request.get_json(force=True)
            validated_data = reward_update_schema.validate(req_body)
            res = loyalty_points_list.update_reward(store, validated_data, reward_id)

            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409
        finally:
            logger.debug("Exiting Loyalty Rewards PUT")

    def put(self, reward_id):
        return self.execute_store_request(request, self.put_executor, reward_id)
    
class UpdateWelcomePoint(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Loyalty Point GET")
        try:
            res = loyalty_points_list.get_welcome_point(store['id'])

            if res['status'] == 200:
                return  res['data'], res['status']
            else:
                return {"message": res['message']}, 404
        finally:
            logger.debug("Exiting Loyalty Point GET")

    def put_executor(self, request, token_payload, store):
        logger.debug("Entering Loyalty Point PUT")
        try:
            req_body = request.get_json(force=True)
            res = loyalty_points_list.update_welcome_point(store['id'], req_body)

            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409
        finally:
            logger.debug("Exiting Loyalty Point PUT")

    def put(self):
        return self.execute_store_request(request, self.put_executor)
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
