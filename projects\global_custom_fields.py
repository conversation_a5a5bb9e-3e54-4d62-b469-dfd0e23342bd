from sqlalchemy import text
import pg_db
from mongo_db import user_db
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from datetime import datetime
import logging
import traceback

logger = logging.getLogger()  

def _fetch_custom_fields(conn, project_id, search_value=None):
    data = []
    query = text (
        f"""SELECT 
                meta.id AS custom_field_id,
                meta.name,
                meta.datatype,
                COUNT(DISTINCT pc.project_id) AS project_counts,
                meta.is_multiselect_group,
                meta.group_values,
                meta.sort_id,
                STRING_AGG(DISTINCT pc.project_id::TEXT, ', ') AS project_ids
            FROM 
                {pg_db.agile_customfield_meta} AS meta
            LEFT JOIN 
                {pg_db.agile_project_customfield} AS pc 
            ON 
                meta.id = pc.customfield_meta_id
            WHERE 
                meta.project_id IS NULL AND meta.status = 'active'
        """
    )

    if search_value:
        query = text(query.text + f" AND meta.name ILIKE '%{search_value}%' OR meta.datatype ILIKE '%{search_value}%'")
    
    query = text(query.text + " GROUP BY meta.id, meta.name, meta.datatype")
    query = text(query.text + " ORDER BY meta.sort_id ASC;")

    result = conn.execute(query)
    for row in result.fetchall():
        row_data = {
            'custom_field_id': row[0],
            'custom_field_name':row[1],
            'field_type': row[2],
            'used_in_projects': row[3],
            'is_multiselect_group': row[4],
            'group_values': row[5],
            'sort_id': row[6]
        }
        if project_id:
            p_ids = []
            if row[7]:
                p_ids = row[7].split(',')
                p_ids = [value.strip() for value in p_ids]
            row_data['is_disabled'] = True if str(project_id) in p_ids else False
        data.append(row_data)
    
    return data



def get_custom_fields(search_value, project_id):
    response = {
        "status": 400 
    }
    conn = pg_db.get_connection()
    try:
        data = _fetch_custom_fields(conn, project_id, search_value)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response

def _insert_new_custom_field(conn, name, datatype, is_multiselect_group, group_values, is_mandatory, username):
    try:
        check_query = text(f"""SELECT COUNT(*) FROM {pg_db.agile_customfield_meta} WHERE LOWER(name) = LOWER(:name) AND datatype = :datatype AND status = 'active';""")
        check_result = conn.execute(check_query, {'name': name, 'datatype': datatype}).scalar()
        
        if check_result > 0:
            # logger.error(f"Custom field with name '{name}' and datatype '{datatype}' already exists.")
            return False  # Field already exists, do not insert
        
        max_sort_id_query = text(
            f"""SELECT MAX(sort_id) AS max_sort_id FROM {pg_db.agile_customfield_meta};"""
        )
        max_sort_id_result = conn.execute(max_sort_id_query).fetchone()
        max_sort_id = max_sort_id_result[0] or 0
        
        new_sort_id = max_sort_id + 1

        if datatype not in ["select", "multi select"]:
            group_values = None
        
        query = text(
            f"""INSERT INTO {pg_db.agile_customfield_meta} (name, datatype, is_multiselect_group, group_values, sort_id, is_mandatory, created_by, updated_by)
                VALUES (:name, :datatype, :is_multiselect_group, :group_values, :sort_id, :is_mandatory, :created_by, :created_by);
            """
        )
        query = query.params(name=name, datatype=datatype, is_multiselect_group=is_multiselect_group, group_values=group_values, sort_id = new_sort_id, is_mandatory=is_mandatory, created_by=username)
        conn.execute(query)

        return True
    except Exception as e:
        logger.error(traceback.format_exc())
        return False

def add_new_field(name, datatype, is_multiselect_group, group_values, is_mandatory, username):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:          
        if datatype == "select" or datatype == "multi select":
            # Check if required parameters for group datatype are provided
            if group_values is None or group_values == '':
                response['status'] = 422
                response['message'] = "For select datatype, select_values are required."
                return response

        data = _insert_new_custom_field(conn, name, datatype, is_multiselect_group, group_values, is_mandatory, username)
        if data:
            response['status'] = 200
            response['message'] = "Data inserted successfully."
        else:
            response['status'] = 409
            response['message'] = "name: Data insertion failed, field with the same name already exists."
    except IntegrityError as e:
            logger.error(traceback.format_exc())
            if isinstance(e.orig, UniqueViolation):
                response['status'] = 409
                response['message'] = "name: This field already exists."
            else:
                response['status'] = 500
                response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response


def _modify_sort_id(conn, custom_field_ids):
    try:
        for index, id in enumerate(custom_field_ids, start=1):
            query = text (
                """UPDATE agile_customfield_meta SET sort_id = :sort_id WHERE id = :id"""
            )
            query = query.params(sort_id=index, id=id)
            conn.execute(query)
        return True
    except Exception as e:
        return False

def update_sort_id(custom_field_ids):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        if not isinstance(custom_field_ids, list):
            custom_field_ids = [custom_field_ids]

        data = _modify_sort_id(conn, custom_field_ids)
        if data:
            response['status'] = 200
            response['message'] = "Data updated successfully."
        else:
            response['status'] = 400
            response['message'] = "Data updation failed."
    except IntegrityError as e:
            error_message = str(e)
            response['status'] = 500
            response['message'] = f"Something went wrong. {error_message}"
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response