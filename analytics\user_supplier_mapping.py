from distutils.dep_util import newer_group
from new_mongodb import StoreAdminDBCollections, UserKeys, fetchall_documents_from_admin_collection, process_documents
import new_pgdb
from new_pgdb import user_supplier_mapping_db
from datetime import datetime
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import text
from utils.common import conver_to_json
import traceback
import logging

logger = logging.getLogger()

def build_supplier_mapping_model(user_name, suppliers_list, email_id):
    models = []

    for supplier in suppliers_list:
        model = user_supplier_mapping_db.UserCustomerMappingSchema(
            user_name=user_name,
            suppliers=supplier,
            modified_at=datetime.now(),
            email_id=email_id
        )
        models.append(model)

    return models

def _check_user_exists(conn, user_name):
    query = text(
        f"SELECT EXISTS (SELECT 1 FROM {new_pgdb.DBTables.user_supplier_mapping_table} WHERE user_name = :user_name)"
    )
    result = conn.execute(query.params(user_name=user_name))
    exists = result.scalar()
    return exists
    
def _remove_existing_suppliers(conn, user_name):
    query = text(
                f"DELETE FROM {new_pgdb.DBTables.user_supplier_mapping_table} WHERE user_name = :user_name"
            )
    result = conn.execute(query.params(user_name=user_name))
    rs = result.rowcount
    conn.commit()
    return rs

def _create_mapping(store_id, user_name, suppliers_list, email_id):
    session = new_pgdb.get_session(store_id)
    try:
        model = build_supplier_mapping_model(user_name, suppliers_list, email_id)
        session.add_all(model)
        session.commit()
        return True
    finally:
        session.close()

def save_user_supplier_mapping_data(store, payload, is_edit):
    result = {}      
    conn = new_pgdb.get_connection(store['id'])
    try:
        # code execution start from here.
        if payload and 'user_name' in payload:
            user_name = payload['user_name']
            email_id = payload['email_id']
            suppliers_list = payload['suppliers']

            # while adding user
            if not is_edit:
                # check if user is already added
                user_exists = _check_user_exists(conn, user_name)
            
                if user_exists:
                    result['status'] = 409
                    result['message'] = 'user_name: User with the given name already exits.'
                    return result
                
                # create new user
                else:
                    _create_mapping(store['id'], user_name, suppliers_list, email_id)
                    result['status'] = 200
                    return result
            
            # while updating existing user.
            else:
                rs = _remove_existing_suppliers(conn,user_name)
                
                # update only if existing deleted.
                if (rs):
                    _create_mapping(store['id'], user_name, suppliers_list, email_id)
                    result['status'] = 200
                    return result
    finally:
        conn.close()
    
    return result

def get_user_supplier_mapping_data(store):
    response = {
        "status": 400,
    }
    with new_pgdb.get_connection(store['id']) as conn:
        query = f"""
            SELECT user_name, suppliers, modified_at 
            FROM user_supplier_mapping
            ORDER BY modified_at DESC
            """
        rs = conn.execute(text(query))
        columns = [col.strip() for col in query.split("SELECT", 1)
                    [1].split("FROM", 1)[0].split(",")]
        
        results = conver_to_json(rs, columns)
        
        grouped_data = {}

        for entry in results:
            
            user_name = entry["user_name"]
            supplier = entry["suppliers"]
                
            if user_name not in grouped_data:
                grouped_data[user_name] = {"user_name": user_name, "suppliers": []}
                
            grouped_data[user_name]["suppliers"].append(supplier)

        result = list(grouped_data.values())
        
        response['message'] = 'data retrieved successfully'
        response['data'] = {
            'data': result
        }
        response['status'] = 200
    
    return response

def get_supplier_list(store):
    response = {
        "status": 400,
    }
    
    with new_pgdb.get_connection(store['id']) as conn:
        query = f"""
        SELECT distinct primary_supplier FROM skuvault_catalog WHERE primary_supplier_is_active = true
        """
        rs = conn.execute(text(query))
        
        def convert_to_json(result_set):
            results = []
            for row in result_set:                  
                results.append({'name':  row[0]})
            return results

        # Convert the query result to JSON
        results = convert_to_json(rs)
        
        response['message'] = 'data retrieved successfully'
        response['data'] = {
            'data': results
        }
        response['status'] = 200
    
    return response


def delete_user_record(store, body):
    with new_pgdb.get_connection(store['id']) as conn:
        query = "DELETE FROM user_supplier_mapping WHERE user_name = :user_name"
        conn.execute(text(query).params(user_name=body['user_name']))
        conn.commit()

def get_mapped_users(store):
    response = {
        "status": 400,
    }
    
    with new_pgdb.get_connection(store['id']) as conn:
        query = f"""SELECT DISTINCT user_name, email_id FROM user_supplier_mapping"""
        result_set = conn.execute(text(query))

        results = []
        for row in result_set:                  
            results.append({'name':  row[0], "email_id": row[1]})

        response['message'] = 'data retrieved successfully'
        response['data'] = {
            'data': results
        }
        response['status'] = 200
    return response

def get_single_user(store, user_name):
    response = {
        "status": 400,
    }
    user_supplier_mapping_table = new_pgdb.DBTables.user_supplier_mapping_table

    with new_pgdb.get_connection(store['id']) as conn:
        query = text(
            f"SELECT user_name, suppliers, email_id FROM {user_supplier_mapping_table} WHERE user_name = :user_name"
        ).params(user_name=user_name)

        rs = conn.execute(query)
    
        results = {
            "name": "",
            "suppliers": [],
            "email_id": ""
        }
        
        suppliers = []
        for row in rs:              
            results['name']= row[0]
            suppliers.append(row[1])
            results['email_id'] = row[2]
        
        results['suppliers'] = suppliers

        response['message'] = 'data retrieved successfully'
        response['data'] = {
            'data': results
        }

        response['status'] = 200
    
    return response

def get_all_admin_app_users(store):
    response = {
        "status": 400,
        "message": "No data found",
        "data": []
    }
    fields = {
        UserKeys.USERNAME: 1,
        UserKeys.NAME: 1
    }
    users = fetchall_documents_from_admin_collection(store['id'], StoreAdminDBCollections.USERS_COLLECTION, {'type': 'admin_app_default_user', 'status': {'$in': ['active', 'inactive']}}, fields)
    users = process_documents(users)
    # Sort users by 'name' in ascending order
    users.sort(key=lambda x: x.get('name', '').lower())
    response['message'] = 'data retrieved successfully'
    response['data'] = users
    response['status'] = 200
    return response