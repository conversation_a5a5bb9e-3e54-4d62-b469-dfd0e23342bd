from projects.project_card import add_card_history_log
from sqlalchemy import text
import pg_db
from mongo_db import user_db
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from datetime import datetime
from projects.card_custom_field_detail import delete_field_from_card
from decimal import Decimal
import logging
import traceback

import task

logger = logging.getLogger()

def _fetch_field_from_card(conn, project_id, card_id):
    combined_data = []
    
    # First query
    query_customfield = text (
        f"""SELECT 
                apc.id,
                apc.name,
                acm.datatype,
                acm.is_multiselect_group,
                acm.group_values,
                acm.is_mandatory,
                apc.sort_id,
                apc.project_id,
                apc.is_visible,
                apc.read_only
            FROM 
                {pg_db.agile_project_customfield} apc
            JOIN 
                {pg_db.agile_customfield_meta} acm ON apc.customfield_meta_id = acm.id
            WHERE 
                apc.project_id = :project_id and apc.is_visible = true and apc.status = 'active'
            ORDER BY apc.sort_id ASC;
        """
    )
    query_customfield = query_customfield.params(project_id=project_id)
    result_customfield = conn.execute(query_customfield)
    
    for row in result_customfield.fetchall():
        row_data = {
            'id': row[0],
            'name': row[1],
            'datatype': row[2],
            'is_multiselect_group': row[3],
            'group_values': row[4],
            'is_mandatory': row[5],
            'sort_id': row[6],
            'str_value': None,
            'number_value': None,
            'created_by': None,
            'updated_by': None,
            'read_only': row[9]
        }
        combined_data.append(row_data)
    
    # Second query
    query_customfield_value = text (
        f"""SELECT 
                acv.id,
                acv.project_customfield_id,
                acv.project_id,
                acv.str_value,
                acv.number_value,
                acv.created_by,
                acv.updated_by
            FROM 
                {pg_db.agile_customfield_value} acv
            WHERE 
                acv.project_id = :project_id
                AND acv.card_id = :card_id;
        """
    )
    query_customfield_value = query_customfield_value.params(project_id=project_id, card_id=card_id)
    result_customfield_value = conn.execute(query_customfield_value)
    
    for row in result_customfield_value.fetchall():
        for data in combined_data:
            if data["id"] == row[1]:
                data['str_value'] = row[3]
                data['number_value'] = float(row[4]) if isinstance(row[4], Decimal) else row[4]
                data['created_by'] = row[5]
                data['updated_by'] = row[6]
    
    return combined_data


def get_fields_from_card(project_id, card_id):
    response = {
        "status": 400 
    }
    conn = pg_db.get_connection()
    try:
        data = _fetch_field_from_card(conn, project_id, card_id)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response




def _insert_new_custom_field_to_card(conn, str_value, number_value, username, project_id, card_id, project_customfield_id):
    query = text(
            f"""INSERT INTO {pg_db.agile_customfield_value} (project_customfield_id, card_id, project_id, str_value, number_value, created_by, updated_by)
                VALUES (:project_customfield_id, :card_id, :project_id, :str_value, :number_value, :created_by, :created_by);
            """
        )
    query = query.params(project_customfield_id=project_customfield_id, card_id=card_id, project_id=project_id, str_value=str_value, number_value=number_value, created_by=username)
    conn.execute(query)
    return True

def _update_custom_field_to_card(conn, str_value, number_value, username, project_id, card_id, card_customfield_id):
    set_clause = "str_value = :str_value, number_value = :number_value"
    params = {'str_value': str_value, 'number_value': number_value}
    query = text(
        f"""UPDATE {pg_db.agile_customfield_value}
            SET 
                {set_clause},
                updated_by = :updated_by,
                updated_at = CURRENT_TIMESTAMP
            WHERE 
                project_id = :project_id
                AND card_id = :card_id
                AND id = :card_customfield_id;"""
    )
    params.update({'updated_by': username, 'project_id': project_id, 'card_id': card_id, 'card_customfield_id': card_customfield_id})
    result = conn.execute(query, params)
    return result.rowcount > 0
    
    
def get_data_type(conn, project_id, project_customfield_id):
    query = text(
        f"""SELECT acm.datatype
            FROM agile_project_customfield apc
            JOIN agile_customfield_meta acm ON apc.customfield_meta_id = acm.id
            WHERE apc.id = :id
            AND apc.project_id = :project_id;
        """
    )
    query = query.params(id=project_customfield_id, project_id=project_id)
    result = conn.execute(query).fetchone()
    if result:
        return result[0]
    else:
        return None

def _fetch_field_detail_from_card(conn, project_id, card_id, project_customfield_id):
    data = []
    query = text (
        f"""SELECT * FROM {pg_db.agile_customfield_value} WHERE project_id = :project_id AND card_id = :card_id AND project_customfield_id = :project_customfield_id;
        """
    )
    query = query.params(project_id=project_id, card_id=card_id, project_customfield_id=project_customfield_id)
    result = conn.execute(query)
    for row in result.fetchall():
        row_data = {
            'id': row[0],
            'project_customfield_id': row[1],
            'card_id':row[2],
            'project_id': row[3],
            'str_value': row[4],
            'number_value': float(row[5]) if isinstance(row[5], Decimal) else row[5]
        }
        data.append(row_data)
    
    return data

def add_new_field_to_card(store, str_value, number_value, date_value, select_value, multi_select_value, username, project_id, card_id, project_customfield_id):
    response = {"status": 400}
    conn = pg_db.get_connection()
    try:
        data_type = get_data_type(conn, project_id, project_customfield_id)
        is_exists_data = _fetch_field_detail_from_card(conn, project_id, card_id, project_customfield_id)
        data = False
       
        if not len(is_exists_data):
            log_value = None
            if data_type in ['select', 'text', 'date', 'multi select']:
                if data_type == 'text':
                    store_value = str_value
                elif data_type == 'date':
                    store_value = date_value
                elif data_type == 'select' or data_type == 'multi select':
                    if select_value == None:
                        store_value = ', '.join(multi_select_value)
                    if multi_select_value == None:
                        store_value = select_value
                        
                if number_value is not None:
                    response['status'] = 400
                    response['message'] = "Invalid value provided. Expected string, select, multi select, or date, but received a number."
                    return response
                else:
                    log_value = store_value
                    data = _insert_new_custom_field_to_card(conn, store_value, None, username, project_id, card_id, project_customfield_id)
            elif data_type == 'number':
                if str_value is not None:
                    response['status'] = 400
                    response['message'] = "Invalid value provided. Expected a number, but received a string."
                    return response
                else:
                    log_value = number_value
                    data = _insert_new_custom_field_to_card(conn, None, number_value, username, project_id, card_id, project_customfield_id)

            elif str_value is None and number_value is None:
                data = _insert_new_custom_field_to_card(conn, None, None, username, project_id, card_id, project_customfield_id)

            if data:
                # Add comment history log    
                log_payload_array = []
                data = {}            
                data['field_name'] = 'custom_field'
                data['old_value'] = ''
                data['new_value'] = log_value
                data['created_by'] = username
                data['ticket_id'] = card_id
                data['custom_field_id'] = project_customfield_id
                log_payload_array.append(data)

                if log_payload_array:
                    add_card_history_log(log_payload_array)
                
                # Update resource column value using custom field mapping
                data = {
                    'ticket_id': card_id,
                    'custom_field_value': log_value,
                    'custom_field_id': project_customfield_id,
                    'project_id': project_id,
                    'updated_by': username
                }
                task.send_task(task.UPDATE_CUSTOM_FIELD_RESOURCE_TASK, args=(store['id'], data))
                response['status'] = 200
                response['message'] = "Data inserted successfully."
            else:
                response['status'] = 409
                response['message'] = "Data insertion failed."
        else:
            card_customfield_id = is_exists_data[0]['id']
            old_log_value = (is_exists_data[0]['str_value'] or is_exists_data[0]['number_value'])
            log_value = None
            if str_value is None and number_value is None and select_value is None and multi_select_value is None and date_value is None:
                dele = delete_field_from_card(project_id, card_id, card_customfield_id)
                if dele['status'] == 200:
                    data = True
               
            else:
                if data_type in ['select', 'text', 'date', 'multi select']:
                    old_log_value = is_exists_data[0]['str_value']
                    if data_type == 'text':
                        store_value = str_value
                    elif data_type == 'date':
                        store_value = date_value
                    elif data_type == 'select' or data_type == 'multi select':
                        if select_value == None:
                            store_value = ', '.join(multi_select_value)
                        if multi_select_value == None:
                            store_value = select_value
                    if number_value is not None:
                        response['status'] = 400
                        response['message'] = "Invalid value provided. Expected string, select, multi select, or date, but received a number."
                        return response
                    else:
                        log_value = store_value
                        data = _update_custom_field_to_card(conn, store_value, None, username, project_id, card_id, card_customfield_id)
                elif data_type == 'number':
                    if str_value is not None:
                        response['status'] = 400
                        response['message'] = "Invalid value provided. Expected an number, but received a string."
                        return response
                    else:
                        old_log_value = is_exists_data[0]['number_value']
                        log_value = number_value
                        data = _update_custom_field_to_card(conn, None, number_value, username, project_id, card_id, card_customfield_id)
            
                
            if data:
                # Add comment history log    
                log_payload_array = []
                data = {}            
                data['field_name'] = 'custom_field'
                data['old_value'] = old_log_value
                data['new_value'] = log_value
                data['created_by'] = username
                data['ticket_id'] = card_id
                data['custom_field_id'] = project_customfield_id
                log_payload_array.append(data)

                if log_payload_array:
                    add_card_history_log(log_payload_array)
                
                # Update resource column value using custom field mapping
                data = {
                    'ticket_id': card_id,
                    'custom_field_value': log_value,
                    'custom_field_id': project_customfield_id,
                    'project_id': project_id,
                    'updated_by': username
                }
                task.send_task(task.UPDATE_CUSTOM_FIELD_RESOURCE_TASK, args=(store['id'], data))
                response['status'] = 200
                response['message'] = "Data updated successfully."
            else:
                response['status'] = 409
                response['message'] = "Data updation failed."
    except IntegrityError as e:
        logger.error(traceback.format_exc())
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 409
            response['message'] = "Duplicate key violation: This field already exists."
        else:
            response['status'] = 500
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response