from datetime import datetime, timezone
from bson import ObjectId
from fields.products import price_list_rules_fields
from new_mongodb import StoreAdminDBCollections, AdminAppNotification, store_catalog_db, task_db, store_info_db, StoreDBCollections, get_admin_db_client_for_store_id, update_document_in_storefront_collection, get_store_db_client_for_store_id, insert_document_in_admin_collection, delete_documents_from_admin_collection
from new_mongodb import fetchall_documents_from_storefront_collection, fetch_one_document_from_admin_collection, process_documents, insert_document_in_storefront_collection, fetchall_documents_from_admin_collection
from new_mongodb import count_documents_storefront_collection, update_document_in_admin_collection
import new_utils
from pymongo.collation import Collation
from plugin import bc_products, bc_price_list, bc_product
import new_pgdb
import csv
import io
import os
from werkzeug.utils import secure_filename
from sqlalchemy import text
import logging
import traceback
from mongo_db import user_db, product_db
from PIL import Image
from utils.common import get_paginated_records_updated, calculatePaginationData, paginate_data_postgres, convert_to_timestamp
from fields.products import product_price_list_fields
from new_mongodb import store_admin_db
from pymongo import UpdateOne
from analytics import replenishment
import task
from new_mongodb.cms_db import get_category_by_bc_id
import re
from utils.google_sheet_util import update_price_in_sheet, process_google_sheet_request


def get_product_price_list_distributors(store_id):
    # db = get_admin_db_client_for_store_id(store_id)
    query = {"derived": "true"}
    res = fetchall_documents_from_admin_collection(store_id, StoreAdminDBCollections.PRICE_LIST_DISTRIBUTORS, query)
    data = []
    for item in res:
        item_data = {
            'id': item.get('id'),
            'title': item.get('title'),
            'status': item.get('status')
        }
        data.append(item_data)
    return data


def get_price_group_dropdown(store):
    # Fetch active price lists
    price_lists_response, _ = bc_price_list.fetch_price_lists(store)
    active_price_lists = [
        {"id": item["id"], "name": item["name"]}
        for item in price_lists_response.get("data", [])
        if item.get("active")
    ]
    
    # Fetch price list rules
    query = {"derived": "false"}
    price_list_distributors = fetchall_documents_from_admin_collection(store["id"], StoreAdminDBCollections.PRICE_LIST_DISTRIBUTORS, query)
    formatted_price_list_distributors = [
        {"id": rule.get("id"), "name": rule.get("title")}
        for rule in price_list_distributors
    ]
    
    # Combine the two lists
    combined_list = active_price_lists + formatted_price_list_distributors
    
    return combined_list

def get_price_list_rules(store, page, limit, sort_by, filter):
    db = get_admin_db_client_for_store_id(store['id'])
    store_db = get_store_db_client_for_store_id(store['id'])

    if '/' in sort_by:
        field, order = sort_by.split('/')
        sort_order = 'asc' if order == '1' else 'desc'
    else:
        # Default sort order if format is not correct
        field = 'created_at'
        sort_order = 'desc'

    payload = {
        "page": page,
        "limit": limit,
        "sort_by": field,
        "sort_order": sort_order,
        "filterBy": ["rule_name", "distributor_name"],
        "filter": filter
    }

    rules, total_data_length, page, limit = get_paginated_records_updated(
        db, StoreAdminDBCollections.PRICE_LIST_RULES, payload, price_list_rules_fields, {}
    )

    cms_collection = db["cms"]  # Assuming the CMS collection is named "cms"
    products_collection = store_db["products"]  # Assuming the products collection is named "products"

    # Enhance rules with category names and product counts
    for rule in rules:
        rule['updated_at'] = convert_to_timestamp(rule.get('updated_at'))
        rule['created_at'] = convert_to_timestamp(rule.get('created_at'))
        rule['last_synced_at'] = convert_to_timestamp(rule.get('last_synced_at'))
        category_ids = rule.get("categories", [])
        if category_ids:
            # Fetch category names
            category_docs = cms_collection.find({"id": {"$in": category_ids}})
            category_names = [doc["name"] for doc in category_docs]
            rule["category_names"] = ", ".join(category_names)

            # Count products for each category
            product_count = products_collection.count_documents({"categories": {"$in": category_ids}})
            rule["product_count"] = product_count
        else:
            rule["category_names"] = ""
            rule["product_count"] = 0

    data = calculatePaginationData(rules, page, limit, total_data_length)
    return data


def create_price_list_rule(store, payload, username):
    db = get_admin_db_client_for_store_id(store['id'])

    payload['rule_name'] = payload['rule_name'].strip()
    # Check for duplicate rule name
    existing_rule = db[StoreAdminDBCollections.PRICE_LIST_RULES].find_one({
        "rule_name": {"$regex": f"^{re.escape(payload['rule_name'])}$", "$options": "i"}
    })
    
    if existing_rule:
        return {
            'status': 409,
            'message': f"rule_name: A price list rule with the name '{payload['rule_name']}' already exists."
        }
    payload['last_synced_at'] = datetime.now(timezone.utc)
    payload['sync_status'] = "updating"
    payload['error_message'] = None
    payload['created_by'] = username
    payload['created_at'] = datetime.now(timezone.utc)
    payload['updated_at'] = datetime.now(timezone.utc)
    rule_id = insert_document_in_admin_collection(store['id'], StoreAdminDBCollections.PRICE_LIST_RULES, payload)
    if rule_id:
        task.send_task(task.GENERATE_ADMIN_APP_NOTIFICATION_TASK, args=(store['id'], AdminAppNotification.PRICE_LIST_RULE_CREATED, rule_id))
        task.send_task(task.UPDATE_PRICE_LIST_RULE_TASK, args=(store['id'], rule_id))
        return {'status': 200, 'message': 'Price list rule is applying to products', 'rule_id': rule_id}
    else:
        return {'status': 400, 'message': 'Failed to create price list rule'}


def get_price_list_rule_details(store, rule_id):
    db = get_admin_db_client_for_store_id(store['id'])
    projection = {"last_synced_at": 0, "created_at": 0, "updated_at": 0, "created_by": 0, "updated_by": 0}
    rule = fetch_one_document_from_admin_collection(store['id'], StoreAdminDBCollections.PRICE_LIST_RULES, {"_id": ObjectId(rule_id)}, projection)
    cms_collection = db["cms"]
    if rule:
        rule['id'] = str(rule.pop('_id'))

        # Get price groups and match the name
        price_groups = get_price_group_dropdown(store)
        if price_groups:
            price_group_id = rule.get("price_group")
            matching_group = next((group for group in price_groups if group["id"] == price_group_id), None)
            if matching_group:
                rule["price_group_name"] = matching_group["name"]
            else:
                rule["price_group_name"] = None  # If no matching group is found
            
        category_ids = rule.get("categories", [])
        rule.pop('categories')
        if category_ids:
            # Fetch category names
            category_docs = cms_collection.find({"id": {"$in": category_ids}})
            categories = [{"id": doc["id"], "name": doc["name"]} for doc in category_docs]
            rule["categories"] = categories  # Add categories with id and name

        return {'status': 200, 'data': rule}
    else:
        return {'status': 200, 'data': []}

def update_price_list_rule(store, rule_id, payload, username):
    db = get_admin_db_client_for_store_id(store['id'])
    payload['updated_by'] = username
    payload['updated_at'] = datetime.now(timezone.utc)
    payload['sync_status'] = "updating"
    payload['error_message'] = None

    if 'rule_name' in payload:
        payload['rule_name'] = payload['rule_name'].strip()

        # Check for duplicate rule name
        existing_rule = db[StoreAdminDBCollections.PRICE_LIST_RULES].find_one({
            "rule_name": {"$regex": f"^{re.escape(payload['rule_name'])}$", "$options": "i"}
        })
    
        if existing_rule and str(existing_rule['_id']) != rule_id:
            return {
                'status': 409,
                'message': f"rule_name: A price list rule with the name '{payload['rule_name']}' already exists."
            }
    update_data = {"$set": payload}
    result = update_document_in_admin_collection(store['id'], StoreAdminDBCollections.PRICE_LIST_RULES, {"_id": ObjectId(rule_id)}, update_data)
    if result.modified_count > 0:
        task.send_task(task.GENERATE_ADMIN_APP_NOTIFICATION_TASK, args=(store['id'], AdminAppNotification.PRICE_LIST_RULE_UPDATED, rule_id))
        task.send_task(task.UPDATE_PRICE_LIST_RULE_TASK, args=(store['id'], rule_id, True))
        return {'status': 200, 'message': 'Price updation started successfully'}
    else:
        return {'status': 400, 'message': 'Failed to update price list rule'}
    
def delete_price_list_rule(store, rule_id):
    result = delete_documents_from_admin_collection(store['id'], StoreAdminDBCollections.PRICE_LIST_RULES, {"_id": ObjectId(rule_id)})
    if result.deleted_count > 0:
        return {'status': 200, 'message': 'Price list rule deleted successfully'}
    else:
        return {'status': 400, 'message': 'Failed to delete price list rule'}

def sync_price_list_rule(store, rule_id, is_normal_sync=False):
    update_data = {"$set": {"last_synced_at": datetime.now(timezone.utc), "sync_status": "updating"}}
    result = update_document_in_admin_collection(store['id'], StoreAdminDBCollections.PRICE_LIST_RULES, {"_id": ObjectId(rule_id)}, update_data)
    task.send_task(task.GENERATE_ADMIN_APP_NOTIFICATION_TASK, args=(store['id'], AdminAppNotification.PRICE_LIST_RULE_UPDATED, rule_id))
    task.send_task(task.UPDATE_PRICE_LIST_RULE_TASK, args=(store['id'], rule_id, is_normal_sync))
    if result.modified_count > 0:
        return {'status': 200, 'message': 'Price list rule sync started successfully'}
    else:
        return {'status': 400, 'message': 'Failed to sync price list rule'}