from db import DB
import argparse
from bson import ObjectId

class PagesScript:
    def __init__(self, db):      
        self.db = db

    def get_dynamic_pages_service(self, db_name):
            repository = self.db.get_dynamic_pages_repository(db_name)
            return repository
    
    def get_blogs_service(self, db_name):
            repository = self.db.get_blogs_repository(db_name)
            return repository
    
    def get_cms_categories_service(self):
        repository = self.db.get_cms_repository()
        return repository
    
    def find_all(self, repository):
        data = repository.find({})
        result = []
        for document in data:
            result.append(document)
        return result
    
    def find_all_categories(self, repository):
        data = repository.find({})
        result = []
        for document in data:
            result.append(document)
        return result
    
    def find_all_blogs(self, repository):
        data = repository.find({})
        result = []
        for document in data:
            result.append(document)
        return result
    
    def update_one(self, repository, query, updated_data):
        return repository.update_one(query, updated_data)
    
    def update_category(self, repository, query, updated_data):
        return repository.update_one(query, updated_data)
    
    def update_blog(self, repository, query, updated_data):
        return repository.update_one(query, updated_data)

    def change_seo_details(self, db_name):
         # dynamic pages service ...
        repo = self.get_dynamic_pages_service(db_name)
        count = 0
        webpages = self.find_all(repo)        
        for page in webpages: 
            # load_iframe = False
            webpage_id = page['_id']           
            versions = page['versions']
            previewState = page.get('preview_state', {})
            if isinstance(previewState, list):
                previewState = {}

            for version in versions:
                # if 'seo_details' in version:
                #     if isinstance(version['seo_details'], list) and version['seo_details']:
                #         version['seo_details'] = version['seo_details'][0]   
                
                for component in version['components']:                                   
                    if component['name'] == "HTML Block" or component['code'] == "html_block": 
                        html_content = component['variant']['config']['data']                       
                        # if '<script type="text/javascript">' in component['variant']['config']['data']:                            
                        #     load_iframe = True  
                        # Replace the specified strings
                        # if '<a href="%%GLOBAL_ShopPathSSL%%' in html_content:
                        #     html_content = html_content.replace('<a href="%%GLOBAL_ShopPathSSL%%', '<a href="')
                        if '%%GLOBAL_CdnStorePath%%' in html_content:
                            html_content = html_content.replace("%%GLOBAL_CdnStorePath%%", "https://cdn11.bigcommerce.com/s-964anr")
                        if '<a href="%%GLOBAL_CdnStorePath%%' in html_content:
                            html_content = html_content.replace('<a href="%%GLOBAL_CdnStorePath%%', '<a href="')
                        # if '<a href="https://cdn11.bigcommerce.com/s-964anr' in html_content:
                        #     html_content = html_content.replace('<a href="https://cdn11.bigcommerce.com/s-964anr', '<a href="') 

                        component['variant']['config']['data'] = html_content                                                                         
                    

            # if 'seo_details' in previewState and isinstance(previewState['seo_details'], list) and previewState['seo_details']:
            #     previewState['seo_details'] = page['preview_state']['seo_details'][0]

            self.update_one(repo, {"_id": ObjectId(str(webpage_id))}, {"$set":
                                                                         {
                                                                            #  "preview_state": previewState,
                                                                             "versions": versions,  
                                                                            #  "load_iframe": load_iframe                                                                                                                                                        
                                                                         }
                                                                         }) 
            #print(webpage_id, '  ') 
            count += 1   

        return count    

    def change_image_path_pages(self, db_name):
        repo = self.get_dynamic_pages_service(db_name)
        count = 0
        webpages = self.find_all(repo)
        for page in webpages:             
            webpage_id = page['_id']           
            versions = page['versions']
            previewState = page.get('preview_state', {})

            if 'components' in previewState:
                 for component in previewState['components']: 
                    if 'variant' in component:
                        variant = component['variant']
                        if 'config' in variant:
                            config = variant['config']
                            if 'image_url' in config:
                                if config['image_url'] != '':
                                    config['image_url'] = config['image_url'].replace('/app/images', '')  
                            if 'mobile_image_url' in config:
                                if config['mobile_image_url'] != '':
                                    config['mobile_image_url'] = config['mobile_image_url'].replace('/app/images', '')  

                            elif 'slider' in  config and 'side_images' in config:                            
                                sliders = config['slider']
                                side_images = config['side_images']
                                for slider in sliders:
                                    if 'image_url' in slider:
                                        if slider['image_url'] != '':
                                            slider['image_url'] = slider['image_url'].replace('/app/images', '')

                                for side_image in side_images:
                                    if 'image_url' in side_image:
                                        if side_image['image_url'] != '':
                                            side_image['image_url'] = side_image['image_url'].replace('/app/images', '')
                            elif 'banners' in  config:
                                banners = config['banners']
                                for banner in banners:
                                    if 'image_url' in banner:
                                        if banner['image_url'] != '':
                                            banner['image_url'] = banner['image_url'].replace('/app/images', '') 
                                    if 'mobile_image_url' in banner:
                                        if banner['mobile_image_url'] != '':
                                            banner['mobile_image_url'] = banner['mobile_image_url'].replace('/app/images', '')                           
                            elif 'logos' in config:
                                logos = config['logos'] 
                                for logo in logos:
                                    if 'image_url' in logo:
                                        if logo['image_url'] != '':
                                            logo['image_url'] = logo['image_url'].replace('/app/images', '')                                                                                                                                     


            for version in versions:                 
                for component in version['components']: 
                    if 'variant' in component:
                        variant = component['variant']
                        if 'config' in variant:
                            config = variant['config']
                            if 'image_url' in config:
                                if config['image_url'] != '':
                                    config['image_url'] = config['image_url'].replace('/app/images', '')  
                            if 'mobile_image_url' in config:
                                if config['mobile_image_url'] != '':
                                    config['mobile_image_url'] = config['mobile_image_url'].replace('/app/images', '')  

                            elif 'slider' in  config and 'side_images' in config:                            
                                sliders = config['slider']
                                side_images = config['side_images']
                                for slider in sliders:
                                    if 'image_url' in slider:
                                        if slider['image_url'] != '':
                                            slider['image_url'] = slider['image_url'].replace('/app/images', '')

                                for side_image in side_images:
                                    if 'image_url' in side_image:
                                        if side_image['image_url'] != '':
                                            side_image['image_url'] = side_image['image_url'].replace('/app/images', '')
                            elif 'banners' in  config:
                                banners = config['banners']
                                for banner in banners:
                                    if 'image_url' in banner:
                                        if banner['image_url'] != '':
                                            banner['image_url'] = banner['image_url'].replace('/app/images', '') 
                                    if 'mobile_image_url' in banner:
                                        if banner['mobile_image_url'] != '':
                                            banner['mobile_image_url'] = banner['mobile_image_url'].replace('/app/images', '')                           
                            elif 'logos' in config:
                                logos = config['logos'] 
                                for logo in logos:
                                    if 'image_url' in logo:
                                        if logo['image_url'] != '':
                                            logo['image_url'] = logo['image_url'].replace('/app/images', '')                                                                                                                                     

            self.update_one(repo, {"_id": ObjectId(str(webpage_id))}, {"$set":
                                                                         {
                                                                             "preview_state": previewState,
                                                                             "versions": versions,                                                                              
                                                                         }
                                                                         }) 
            #print(webpage_id, '  ') 
            count += 1 

        return count    

    def change_image_path_categories(self):
        repo = self.get_cms_categories_service()
        count = 0
        categories = self.find_all_categories(repo)
        for category in categories:             
            category_id = category['_id']           
            versions = category['versions']
            previewState = category.get('preview_state', {})

            if 'components' in previewState:
                 for component in previewState['components']: 
                    if 'variant' in component:
                        variant = component['variant']
                        if 'config' in variant:
                            config = variant['config']
                            if 'image_url' in config:
                                if config['image_url'] != '':
                                    config['image_url'] = config['image_url'].replace('/app/images', '')  
                            if 'mobile_image_url' in config:
                                if config['mobile_image_url'] != '':
                                    config['mobile_image_url'] = config['mobile_image_url'].replace('/app/images', '')  

                            elif 'slider' in  config and 'side_images' in config:                            
                                sliders = config['slider']
                                side_images = config['side_images']
                                for slider in sliders:
                                    if 'image_url' in slider:
                                        if slider['image_url'] != '':
                                            slider['image_url'] = slider['image_url'].replace('/app/images', '')

                                for side_image in side_images:
                                    if 'image_url' in side_image:
                                        if side_image['image_url'] != '':
                                            side_image['image_url'] = side_image['image_url'].replace('/app/images', '')
                            elif 'banners' in  config:
                                banners = config['banners']
                                for banner in banners:
                                    if 'image_url' in banner:
                                        if banner['image_url'] != '':
                                            banner['image_url'] = banner['image_url'].replace('/app/images', '') 
                                    if 'mobile_image_url' in banner:
                                        if banner['mobile_image_url'] != '':
                                            banner['mobile_image_url'] = banner['mobile_image_url'].replace('/app/images', '')                           
                            elif 'logos' in config:
                                logos = config['logos'] 
                                for logo in logos:
                                    if 'image_url' in logo:
                                        if logo['image_url'] != '':
                                            logo['image_url'] = logo['image_url'].replace('/app/images', '')                                                                                                                                     

            for version in versions:                 
                for component in version['components']: 
                    if 'variant' in component:
                        variant = component['variant']
                        if 'config' in variant:
                            config = variant['config']
                            if 'image_url' in config:
                                if config['image_url'] != '':
                                    config['image_url'] = config['image_url'].replace('/app/images', '')  
                            if 'mobile_image_url' in config:
                                if config['mobile_image_url'] != '':
                                    config['mobile_image_url'] = config['mobile_image_url'].replace('/app/images', '')  

                            elif 'slider' in  config and 'side_images' in config:                            
                                sliders = config['slider']
                                side_images = config['side_images']
                                for slider in sliders:
                                    if 'image_url' in slider:
                                        if slider['image_url'] != '':
                                            slider['image_url'] = slider['image_url'].replace('/app/images', '')

                                for side_image in side_images:
                                    if 'image_url' in side_image:
                                        if side_image['image_url'] != '':
                                            side_image['image_url'] = side_image['image_url'].replace('/app/images', '')
                            elif 'banners' in  config:
                                banners = config['banners']
                                for banner in banners:
                                    if 'image_url' in banner:
                                        if banner['image_url'] != '':
                                            banner['image_url'] = banner['image_url'].replace('/app/images', '') 
                                    if 'mobile_image_url' in banner:
                                        if banner['mobile_image_url'] != '':
                                            banner['mobile_image_url'] = banner['mobile_image_url'].replace('/app/images', '')                           
                            elif 'logos' in config:
                                logos = config['logos'] 
                                for logo in logos:
                                    if 'image_url' in logo:
                                        if logo['image_url'] != '':
                                            logo['image_url'] = logo['image_url'].replace('/app/images', '')                                                                                                                                     

            self.update_category(repo, {"_id": ObjectId(str(category_id))}, {"$set":
                                                                         {
                                                                             "preview_state": previewState,
                                                                             "versions": versions,                                                                              
                                                                         }
                                                                         }) 
            #print(category_id, '  ') 
            count += 1 

        return count    

    def change_image_path_blogs(self, db_name):
        repo = self.get_blogs_service(db_name)
        count = 0
        blogs = self.find_all_blogs(repo)
        for blog in blogs:             
            blog_id = blog['_id']           
            image_url = ''

            if 'image_url' in blog:
                if blog['image_url'] != '': 
                    image_url = blog['image_url'].replace('/app/images', '')              
            
            self.update_blog(repo, {"_id": ObjectId(str(blog_id))}, {"$set":
                                                                         {
                                                                             "image_url": image_url,                                                                                                                                                          
                                                                         }
                                                                         }) 
            #print(blog_id, '  ') 
            count += 1 

        return count

def main():
     # Create an argument parser
    parser = argparse.ArgumentParser(description='Change SEO details')
    parser.add_argument('--db_name', type=str, help='Database name', required=True)
    args = parser.parse_args()

    db = DB()    
    services = PagesScript(db)    
    # dynamic_pages = services.change_image_path_pages(args.db_name)
    # dynamic_categories = services.change_image_path_categories()
    dynamic_blogs = services.change_image_path_blogs(args.db_name)
    
    
    #print("Updated blogs:", dynamic_blogs)

if __name__ == '__main__':
    main() 