from sqlalchemy.dialects.postgresql import insert
import pg_db as db
from sqlalchemy import <PERSON>olean, Column, DateTime, String, Integer, ForeignKey, Float
from sqlalchemy.sql import func


class OrderAuditReport(db.Base):
    __tablename__ = "order_audit_report"

    id = Column(Integer, primary_key=True, autoincrement=True)
    order_date = Column(DateTime)
    order_id = Column(String())
    customer_id = Column(Integer)
    order_status_id = Column(Integer)
    order_status = Column(String())
    updated_by = Column(String())
    updated_at = Column(DateTime, onupdate=func.now())

