from flask import request
import logging
from api import APIResource
import traceback
from redirects import redirect_service

logger = logging.getLogger()

class AllRedirects(APIResource):
    def get_executor(self, request, token_payload, store, tenant_id, store_id):
        try:
            query_params = request.args.to_dict()
            res = redirect_service.get_all_redirects(store, query_params)
            return res, 200
        finally:
            logger.debug("Exiting Redirects GET")

    def get(self, tenant_id, store_id):
        return self.execute_store_request(request, self.get_executor, tenant_id, store_id)
    