from flask import request
import logging
import traceback
from api import APIResource
from analytics import replenishment
logger = logging.getLogger()
from analytics import user_supplier_mapping
from utils import whatsapp_util

    
class ReceiveMessage(APIResource):
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Whatsapp ReceiveMessage POST")
        try:
            payload = request.get_json(silent=True)
            if payload:
                  whatsapp_util.receive_message(payload)
                  return {"message": "Added successfully."}, 200
            return {"message": "Invalid request"}, 400
        finally:
            logger.debug("Exiting Whatsapp ReceiveMessage POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
class SendMessage(APIResource):
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Whatsapp Send POST")
        try:
            payload = request.get_json(silent=True)
            if payload:
                  whatsapp_util.send_message(payload)
                  return {"message": "Added successfully."}, 200
            return {"message": "Invalid request"}, 400
        finally:
            logger.debug("Exiting Whatsapp Send POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
class GetAllMessages(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Whatsapp GetAllMessages ")
        try:
            payload = request.args.to_dict()
            if payload:
                 result= whatsapp_util.get_all_messages(payload)
                 return result, 200
            return {"message": "Invalid request"}, 400
        finally:
            logger.debug("Exiting Whatsapp GetAllMessages")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class GetMessageDetail(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Whatsapp GetMessageDetail")
        try:
            payload = request.args.to_dict()
            if payload:
                 result= whatsapp_util.get_message_detail(payload)
                 return result, 200
            return {"message": "Invalid request"}, 400
        finally:
            logger.debug("Exiting Whatsapp GetMessageDetail")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class AssignRepresentative(APIResource):
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Whatsapp AssignRepresentative")
        try:
            payload = request.get_json(silent=True)
            if payload:
                  whatsapp_util.assign_representative(payload)
                  return {"message": "Added successfully."}, 200
            return {"message": "Invalid request"}, 400
        finally:
            logger.debug("Exiting Whatsapp AssignRepresentative")

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
class GetVendorRepresentativeList(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Whatsapp GetVendorRepresentativeList")
        try:
            payload = request.args.to_dict()
            result= whatsapp_util.get_vendor_and_representative(payload)
            return result, 200
        finally:
            logger.debug("Exiting Whatsapp GetVendorRepresentativeList")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class GetVendorFromID(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Whatsapp GetVendorFromID")
        try:
            payload = request.args.to_dict()
            result= whatsapp_util.get_vendor_from_id(payload)
            return result, 200
        finally:
            logger.debug("Exiting Whatsapp GetVendorFromID")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

# store and get images to server
class SaveDocuments(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            query_params = request.args.to_dict()            
            res = whatsapp_util.getImage(query_params)
            return res
        finally:
            logger.debug("Exiting Document Whatsapp GET")

    def post_executor(self, request, token_payload, store):
        try:
            uploaded_file = request.files
            type = request.form.get('type')
            vendor_id = request.form.get('vendor_id')
            rep_id = request.form.get('rep_id')           
            res = whatsapp_util.setImage(uploaded_file, type, vendor_id, rep_id)
            
            if res['status'] == 200:
                return  res['message'], 200
            else:
                return {"status": res['message']}, 500
        finally:
            logger.debug("Exiting Document Upload Whatsapp POST")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def post(self):
        return self.execute_store_request(request, self.post_executor)