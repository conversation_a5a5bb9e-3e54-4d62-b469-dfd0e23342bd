from flask import request
import logging
from api import APIResource
import traceback
from schemas.blogs import category_schema, category_update_schema
from utils import store_util
from cms.blogs import blogs_list

logger = logging.getLogger()


#get all webpages
class Categories(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            query_params = request.args.to_dict()             
            res = blogs_list.get_categories(store, query_params)
            return res, 200
        finally:
            logger.debug("Exiting Categories GET")    
    
    def get(self, tenant_id, store_id):
        return self.execute_store_request(request, self.get_executor)

#create blogs
class CreateCategories(APIResource):
    def post_executor(self, request, token_payload, store, token=None):
        try:
            req_body = request.get_json(force=True)
            validated_data = category_schema.validate(req_body)
            res = blogs_list.create_category(store, validated_data)                
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409
        finally:
            logger.debug("Exiting Category POST")        


    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
#upadate and delete webpage
class CategoryOperations(APIResource):
    def put_executor(self, request, token_payload, store, category_id):
        try:
            req_body = request.get_json(force=True)
            validated_data = category_update_schema.validate(req_body)
            res = blogs_list.update_category(store, validated_data, category_id)                
            
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409   
        finally:
            logger.debug("Exiting Category PUT")
                
    def get_executor(self, request, token_payload, store, category_id):
        try:
            res = blogs_list.get_category(store, category_id)    
            return res, 200
        finally:
            logger.debug("Exiting Category GET") 


    def delete_executor(self, request, token_payload, store, category_id):
        try:            
            success = blogs_list.delete_category_by_id(store, category_id)
            if success:
                return {"status": "ok"}, 200
            return {"status": "failed"}, 500   
        finally:
            logger.debug("Exiting Category DELETE")


    def put(self, category_id):
        return self.execute_store_request(request, self.put_executor, category_id)
    
    def get(self, category_id):
        return self.execute_store_request(request, self.get_executor, category_id)

    def delete(self, category_id):
        return self.execute_store_request(request, self.delete_executor, category_id)