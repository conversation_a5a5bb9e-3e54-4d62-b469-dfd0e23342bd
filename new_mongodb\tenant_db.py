from bson import ObjectId
import datetime
from new_mongodb import get_tenant_db_client, get_store_by_id, TenantDBCollections, UserKeys, StoreKeys, ClientAppsKeys, CommonConfigKeys

def fetch_store_features(store_id):
    tenant_db = get_tenant_db_client()
    features_coll = tenant_db[TenantDBCollections.FEATURES_COLLECTION]
    return features_coll.find_one({"_id":ObjectId(str(store_id))})

def get_user_id(username):
    tenant_db = get_tenant_db_client()
    users_coll = tenant_db[TenantDBCollections.USERS_COLLECTION]
    return users_coll.find_one({UserKeys.USERNAME:username}, {UserKeys.USERNAME: 1})  

def fetch_user_by_username(username):
    tenant_db = get_tenant_db_client()
    users_coll = tenant_db[TenantDBCollections.USERS_COLLECTION]
    return users_coll.find_one({UserKeys.USERNAME:username})  

def create_user(user):
    tenant_db = get_tenant_db_client()
    user['updated_at'] = int(datetime.datetime.now(datetime.timezone.utc).timestamp())
    return tenant_db[TenantDBCollections.USERS_COLLECTION].replace_one({UserKeys.USERNAME: user[UserKeys.USERNAME]}, user, upsert=True)

def update_user(query, user):
    tenant_db = get_tenant_db_client()
    user['updated_at'] = int(datetime.datetime.now(datetime.timezone.utc).timestamp())
    update = {
        "$set": user
    }
    tenant_db[TenantDBCollections.USERS_COLLECTION].update_one(query, update)

def fetch_role_by_id(role_id):
    tenant_db = get_tenant_db_client()
    roles_coll = tenant_db[TenantDBCollections.ROLES_COLLECTION]
    return roles_coll.find_one({"_id", ObjectId(str(role_id))})

def fetch_client_app_by_id(client_id):
    tenant_db = get_tenant_db_client()
    client_apps_coll = tenant_db[TenantDBCollections.CLIENT_APPS_COLLECTION]
    return client_apps_coll.find_one({ClientAppsKeys.CLIENT_ID: client_id})

def fetch_store_admin_by_username(username):
    tenant_db = get_tenant_db_client()
    users_coll = tenant_db[TenantDBCollections.USERS_COLLECTION]
    user = users_coll.find_one({UserKeys.USERNAME:username}, {UserKeys.STORES: 1})  
    result = {}
    if user:
        user_stores = user[UserKeys.STORES]
        if user_stores:
            for user_store_id in user_stores.keys():
                store = get_store_by_id(user_store_id)
                result[user_store_id] = {
                    StoreKeys.NAME: store[StoreKeys.NAME],
                    StoreKeys.ADMIN_URL: store[StoreKeys.ADMIN_URL]
                }
    return result

def fetch_google_creds():
    tenant_db = get_tenant_db_client()
    common_config = tenant_db[TenantDBCollections.COMMON_CONFIG_COLLECTION]
    return common_config.find_one({"_id": CommonConfigKeys.GOOGLE_OAUTH_CREDS})

def fetch_igen_tax_flag():
    tenant_db = get_tenant_db_client()
    common_config = tenant_db[TenantDBCollections.COMMON_CONFIG_COLLECTION]
    return common_config.find_one({"_id": CommonConfigKeys.IGEN_TAX_FLAG})
