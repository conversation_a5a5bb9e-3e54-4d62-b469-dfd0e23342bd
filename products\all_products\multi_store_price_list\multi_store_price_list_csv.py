from products.all_products.multi_store_products_list import _get_store_ids_from_store_name
import task
from new_mongodb import get_admin_db_client_for_store_id, StoreAdminDBCollections

def upload_multi_store_price_list_csv(store, payload, username):
    response = {"status": 409}
    store_name = 'both'
    store_ids = _get_store_ids_from_store_name([store['id']], store_name)
    
    file_data = payload.get("data", [])
    filename = payload.get("filename", "uploaded_price_list.csv")

    filename = "uploaded_price_list.csv" if filename == "" else filename
    
    if not file_data:
        response["message"] = "No data provided."
        return response
    
    # Extract SKUs from the payload
    payload_skus = {row["SKU"] for row in file_data}

    found_skus_by_store = {}
    missing_skus_by_store = {}

    for store_id in store_ids:
        db = get_admin_db_client_for_store_id(store_id)
        found_skus = set()
        query = {
            "$or": [
                {"parent_product_sku": {"$in": list(payload_skus)}},
                {"variants.variant_sku": {"$in": list(payload_skus)}}
            ]
        }
        results = db[StoreAdminDBCollections.PRODUCT_PRICE_LISTS].find(query, {"parent_product_sku": 1, "variants.variant_sku": 1})
        
        for doc in results:
            if doc.get("parent_product_sku"):
                found_skus.add(doc.get("parent_product_sku"))
            found_skus.update(variant["variant_sku"] for variant in doc.get("variants", []))

        found_skus_by_store[store_id] = found_skus
        missing_skus = payload_skus - found_skus
        missing_skus_by_store[store_id] = missing_skus
    # If any store has missing SKUs, return them
    # any_missing = any(missing_skus_by_store[store_id] for store_id in store_ids)
    # if any_missing:
    #     response["message"] = {
    #         store_id: [
    #             f"SKU does not exist in the system for this store. Please enter a valid SKU: {sku}"
    #             for sku in missing_skus_by_store[store_id]
    #         ]
    #         for store_id in store_ids if missing_skus_by_store[store_id]
    #     }
    #     return response

    # Build store-specific arrays for found SKUs
    store_specific_data = {}
    for store_id in store_ids:
        if store_id == "63da3e98b702e324567f76f9":
            prefix = "Midwest_"
        elif store_id == "661239751b9ce4bd7f85237c":
            prefix = "CBD_"
        else:
            prefix = None
        found_skus = found_skus_by_store[store_id]
        store_array = []
        for row in file_data:
            if row["SKU"] in found_skus:
                new_row = {"SKU": row["SKU"]}
                for key, value in row.items():
                    if key == "SKU":
                        continue
                    if prefix and key.startswith(prefix):
                        clean_key = key[len(prefix):]
                        new_row[clean_key] = value
                store_array.append(new_row)
        store_specific_data[store_id] = store_array

    # If all SKUs are valid, send the task
    task_id = task.send_task(task.UPDATE_MULTI_STORE_PRICE_LIST_FROM_CSV, args=(store['id'], store_specific_data, username, filename))
    if task_id:
        response["status"] = 200
        response["message"] = f"CSV validated successfully. Price update process has started. You will receive an email once it's completed."
        db[StoreAdminDBCollections.USER_PREFERENCE].update_one({"type": "multi_store_price_list_import"}, {"$set": {"status": "running"}}, upsert=True)
    else:
        response["message"] = "Something went wrong while updating the product prices."

    return response

def get_multi_store_csv_upload_status(store_id):
    response = {
        "status": 400
    }
    db = get_admin_db_client_for_store_id(store_id)
    result = db[StoreAdminDBCollections.USER_PREFERENCE].find_one({"type": "multi_store_price_list_import"})
    if result:
        response['status'] = 200
        response['data'] = result.get("status", None)
    else:
        response['status'] = 200
        response['data'] = {}
    return response