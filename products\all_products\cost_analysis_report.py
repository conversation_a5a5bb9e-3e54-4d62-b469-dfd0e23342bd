from new_mongodb import get_admin_db_client_for_store_id, StoreAdminDBCollections, get_store_db_client_for_store_id, StoreDBCollections
import traceback
import logging
from utils.common import get_paginated_records_price_list, calculatePaginationData, convert_to_timestamp
from pymongo import DESCENDING
from datetime import datetime
from dateutil import parser

logger = logging.getLogger()

cost_analysis_report_fields = {
    "parent_product_id": 1,
    "parent_product_name": 1,
    "parent_product_sku": 1,
    "cost_margin": 1,
    "avg_po_cost_of_all_variants": 1,
    "min_variant_cost": 1,
    "min_latest_po": 1,
    "created_at": 1,
    "variants.variant_name": 1,
    "variants.variant_sku": 1,
    "variants.variant_cost": 1,
    "variants.variant_po_average_cost": 1,
    "variants.variant_latest_po_cost": 1
}


def get_cost_analysis_report(store_id, product_id, page, limit, sort_by, search):
    db = get_admin_db_client_for_store_id(store_id)
    try:
        # Extract field and sort order from sort_by
        field, sort_order = ('created_at', 'desc')

        # Mapping of sort aliases to actual internal fields
        sort_field_map = {
            'cost_range': 'min_variant_cost',
            'po_avg_cost_range': 'avg_po_cost_of_all_variants',
            'latest_po_cost_range': 'min_latest_po'
        }

        if '/' in sort_by:
            sort_key, order = sort_by.split('/')
            field = sort_field_map.get(sort_key, sort_key)  # fallback to sort_key if not in map
            sort_order = 'asc' if order == '1' else 'desc'

        payload = {
            "page": page,
            "limit": limit,
            "sort_by": field,
            "sort_order": sort_order,
            "filterBy": ["parent_product_name", "parent_product_sku"],
            "filter": search
        }

        # Apply cost_margin filter
        additional_query = {
            "syncing": False
        }
        if product_id:
            additional_query["parent_product_id"] = int(product_id)

        products, total_data_length, page, limit = get_paginated_records_price_list(
            db, StoreAdminDBCollections.PRODUCT_PRICE_LISTS, payload, cost_analysis_report_fields, additional_query
        )

        all_variant_skus = []
        for product in products:
            variant_costs = []
            latest_po_costs = []
            po_avg_costs = []
            all_variant_skus.extend([variant.get('variant_sku') for variant in product['variants']])
            latest_pos_data = _get_latest_pos_for_sku(store_id, all_variant_skus)
            for variant in product['variants']:
                cost = variant.get('variant_cost', None)
                po_avg_cost = variant.get('variant_po_average_cost', None)
                latest_po_cost = variant.get('variant_latest_po_cost', None)

                product['created_at'] = convert_to_timestamp(product.get('created_at'))

                if cost is not None:
                    variant_costs.append(cost)

                if latest_po_cost is not None:
                    latest_po_costs.append(latest_po_cost)

                if po_avg_cost is not None:
                    po_avg_costs.append(po_avg_cost)

                variant["parent_product_id"] = product['parent_product_id']
                variant["parent_product_name"] = product['parent_product_name']

                variant.update(latest_pos_data.get(variant.get('variant_sku'), {}))

            def format_range(values):
                if not values:
                    return None
                min_val = round(min(values), 2)
                max_val = round(max(values), 2)
                return f"{min_val}-{max_val}" if min_val != max_val else str(min_val)
            
            product['cost_range'] = format_range(variant_costs)
            product['latest_po_cost_range'] = format_range(latest_po_costs)
            product['po_avg_cost_range'] = format_range(po_avg_costs)
            product['cost_margin'] = product.get('cost_margin', None)

        data = calculatePaginationData(products, page, limit, total_data_length)

        return data
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e


def _get_latest_pos_for_sku(store_id, all_variant_skus):
    db = get_store_db_client_for_store_id(store_id)
    po_collection = db[StoreDBCollections.SKUVAULT_POS]

    result = {}

    # Fetch all documents for the given SKUs
    matching_docs = list(po_collection.find(
        {"_id": {"$in": all_variant_skus}}
    ))

    for sku in all_variant_skus:
        doc = next((d for d in matching_docs if d['_id'] == sku), None)
        latest_pos_list = []

        if doc:
            pos_entries = doc.get("pos", {})
            for po_number, po_data in pos_entries.items():
                created_at_str = po_data.get("created_at")
                if created_at_str:
                    latest_pos_list.append({
                        "po_number": po_number,
                        "created_at": parser.isoparse(created_at_str.replace("Z", "+00:00")),
                        "cost": po_data.get("cost", 0)
                    })

            # Sort and take top 3
            latest_pos_list = sorted(latest_pos_list, key=lambda x: x["created_at"], reverse=True)[:3]

        # Build PO fields for this SKU
        latest_pos_data = {}
        for i in range(1, 4):
            if i <= len(latest_pos_list):
                po = latest_pos_list[i - 1]
                latest_pos_data[f"po_{i}"] = f"{po['po_number']}/{po['cost']}"
                latest_pos_data[f"po_{i}_date"] = convert_to_timestamp(po['created_at'])
            else:
                latest_pos_data[f"po_{i}"] = ""
                latest_pos_data[f"po_{i}_date"] = ""

        result[sku] = latest_pos_data

    return result
