from sqlalchemy.dialects.postgresql import insert
import pg_db as db
from sqlalchemy import Boolean, Column, DateTime, Numeric, String, Integer, ForeignKey, Enum, Interval
from sqlalchemy.sql import func

class ReplenishmentBackorderVariants(db.Base):
    __tablename__ = "replenishment_backorder_variants"

    id = Column(Integer, primary_key=True, autoincrement=True)
    product_id = Column(Integer)
    parent_sku = Column(String(100), nullable=False)
    child_sku = Column(String(100), unique=True, nullable=False)
    variant_id = Column(Integer)
    qty = Column(Integer, nullable=False)
    product_name = Column(String(300), nullable=False)
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100))
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())


class ReplenishmentSafetyStock(db.Base):
    __tablename__ = "replenishment_safety_stock"

    id = Column(Integer, primary_key=True, autoincrement=True)
    product_id = Column(Integer)
    parent_sku = Column(String(100), nullable=False)
    child_sku = Column(String(100), unique=True, nullable=False)
    variant_id = Column(Integer)
    qty = Column(Integer, nullable=False)
    product_name = Column(String(300), nullable=False)
    created_by = Column(String(100), nullable=False)
    updated_by = Column(String(100))
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

class ReplenishmentDashboard(db.Base):
    __tablename__ = "replenishment_dashboard"

    parent_sku = Column(String(150), primary_key=True)
    classified_as = Column(String(100))
    terms_consignment = Column(String(50))
    action_taken = Column(String(500))
    updated_by = Column(String(100))
    updated_at = Column(DateTime, onupdate=func.now())

class ReplenishmentClassifications(db.Base):
    __tablename__ = "replenishment_classifications"

    id = Column(Integer, primary_key=True, autoincrement=True)
    sku = Column(String(), nullable=False)
    old_classification = Column(String())
    new_classification = Column(String(), nullable=False)
    is_parent_sku = Column(Boolean, default=False)
    created_by = Column(String(), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_by = Column(String())
    updated_at = Column(DateTime, onupdate=func.now())

