import requests
import time
from mongo_db import builds_db
import logging
from utils import store_util
from datetime import timezone
from datetime import datetime
from dateutil import parser
from utils.common import convert_to_timestamp
import task
from new_mongodb import AdminAppNotification, store_admin_db


logger = logging.getLogger()


# api call function
def call_netlify_api(api_data, method, url, query_params=None, is_trigger_build=False):
    headers = {}
    # headers["HTTP_ORIGIN"] = env_variables["REACT_APP_NETLIFY_APP_API_URL"]
    headers["Authorization"] = "Bearer " + \
        api_data["auth_token"]
    headers["Accept"] = "application/json"
    api_url = ""
    if is_trigger_build:
        api_url = url
    else:
        api_url = api_data["api_baseurl"] + url
    method = method.upper()
    res = None
    if "GET" == method:
        res = requests.get(url=api_url, params=query_params, headers=headers)
    elif "POST" == method or "PUT" == method:
        if "POST" == method:
            res = requests.post(
                url=api_url, params=query_params, headers=headers)
        else:
            res = requests.put(
                url=api_url, params=query_params, headers=headers)
    elif "DELETE" == method:
        res = requests.delete(url=api_url, headers=headers)

    return res

# intermediate function


def fetch_netlify_builds_by_rest_api(store, api, method, limit_per_request=0, query_params={}, db_collection=None, is_trigger_build=False):
    api_info = store_util.get_netlify_api_info(store)
    response = call_netlify_api(api_info, method, api, query_params, is_trigger_build)
    data = None
    if response.status_code == 200:
        if is_trigger_build:
            data = response
        else:
            data = response.json()
    return response.status_code, data

# get all builds from netlify with db data


def get_all_netlify_builds(store):
    desired_states = ['error', 'ready', 'building', 'enqueued', 'processing']
    filtered_builds = []
    response = {
        "status": 400,
    }
    db_builds = builds_db.fetch_all_db_builds(store)
    status_code, netlify_builds = fetch_all_netlify_builds(store)

    if status_code == 200:
        
        for netlify_build in netlify_builds:
            if netlify_build['deploy_state'] in desired_states:
                
                netlify_build['description'] = ''
                netlify_build['title'] = ''
                netlify_build['created_at'] = convert_to_timestamp(netlify_build['created_at'])

                for db_build in db_builds:
                    
                    if db_build['deploy_id'] == netlify_build['deploy_id']:
                        netlify_build['description'] = db_build['description']
                        netlify_build['title'] = db_build['title']
                        break
                    
                filtered_builds.append(netlify_build)

        response['status'] = 200
        response['message'] = "builds retrived successfully"
        response['builds'] = filtered_builds[:50]

    elif status_code == 401:
        response['status'] = 401
        response['message'] = "Unauthorized"

    elif status_code == 404:
        response['status'] = 404
        response['message'] = "Builds not found"
        
    else:
        response['status'] = 400
        response['message'] = "Bad request"
    return response

# get published build from netlify


def get_published__build(store):
    response = {
        "status": 400,
    }
    status_code, site = fetch_published_build(store)
    if status_code == 200:
        published_build = site['published_deploy']
        response['status'] = 200
        response['message'] = "Published build retrived successfully"
        response['published_deploy'] = published_build
    elif status_code == 401:
        response['status'] = 401
        response['message'] = "Unauthorized"
    elif status_code == 404:
        response['status'] = 404
        response['message'] = "Site details not found"
    else:
        response['status'] = 400
        response['message'] = "Bad request"
    return response

# activate selected deploy


def activate_selected_deploy(store, body):
    response = {
        "status": 400,
    }
    deploy_id = body['deploy_id']
    status_code, res = activate_build(store, deploy_id)
    if status_code == 200:
        response['status'] = 200
        response['message'] = "Build Activated successfully"
    elif status_code == 401:
        response['status'] = 401
        response['message'] = "Unauthorized"
    elif status_code == 404:
        response['status'] = 404
        response['message'] = "Build details not found"
    else:
        response['status'] = 400
        response['message'] = "Bad request"
    return response

# tirgger netlify build


def trigger_new_build(store, body, username):
    response = {
        "status": 400,
    }
    description = body['description']    
    status_code, res = trigger_build(store, description)
    if status_code == 200:
        if username != '':
            user = store_admin_db.fetch_user_by_username(store['id'], username)
            if user:
                user_id = str(user['_id'])
                task.send_task(task.GENERATE_ADMIN_APP_NOTIFICATION_TASK, args=(store['id'], AdminAppNotification.USER_THEMES_UPDATED, user_id))
        response['status'] = 200
        response['message'] = "Build Triggered successfully"
    elif status_code == 401:
        response['status'] = 401
        response['message'] = "Unauthorized"
    else:
        response['status'] = 400
        response['message'] = "Bad request"
    return response

# get current build


def get_current__build(store, build_id):
    response = {
        "status": 400,
    }
    status_code, res = current_build(store, build_id)
    if status_code == 200:
        response['status'] = 200
        response['message'] = "Build data retrived successfully"
        response['current_build'] = res
    elif status_code == 401:
        response['status'] = 401
        response['message'] = "Unauthorized"
    elif status_code == 404:
        response['status'] = 404
        response['message'] = "Build details not found"
    else:
        response['status'] = 400
        response['message'] = "Bad request"
    return response

# cancel running build


def set_cancel_build(store, body):
    response = {
        "status": 400,
    }
    deploy_id = body['deploy_id']
    status_code, res = cancel_build(store, deploy_id)
    if status_code == 200:
        response['status'] = 200
        response['message'] = "Build Cancelled successfully"
    elif status_code == 401:
        response['status'] = 401
        response['message'] = "Unauthorized"
    else:
        response['status'] = 400
        response['message'] = "Bad request"
    return response

# create api call data ready for get netlify builds


def fetch_all_netlify_builds(store):
    query_params = {
        "page": 0,
        "per_page": 250,
    }
    api_info = store_util.get_netlify_api_info(store)
    api = "/sites/" + api_info["site_id"] + "/builds"
    return fetch_netlify_builds_by_rest_api(store, api, 'GET', limit_per_request=250, query_params=query_params, db_collection=builds_db.BUILDS_COLLECTION, is_trigger_build=False)

# create api call data ready for get published build


def fetch_published_build(store):
    api_info = store_util.get_netlify_api_info(store)
    api = "/sites/" + api_info["site_id"]
    return fetch_netlify_builds_by_rest_api(store, api, 'GET', limit_per_request=0, query_params={}, db_collection=builds_db.BUILDS_COLLECTION, is_trigger_build=False)

# create api call data ready for activate selacted build


def activate_build(store, deploy_id):
    api_info = store_util.get_netlify_api_info(store)
    api = "/sites/" + \
        api_info["site_id"] + \
        "/deploys/" + deploy_id + "/restore"
    return fetch_netlify_builds_by_rest_api(store, api, 'POST', limit_per_request=0, query_params={}, db_collection=builds_db.BUILDS_COLLECTION, is_trigger_build=False)

# create api call data ready for trigger build


def trigger_build(store, description):
    api_info = store_util.get_netlify_api_info(store)
    title = description.replace(' ', '+')
    branch_name = api_info['branch_name']
    api = api_info['build_hook'] + \
        "?trigger_branch=" + branch_name +"&trigger_title=" + title + "&clear_cache=true"
    return fetch_netlify_builds_by_rest_api(store, api, 'POST', limit_per_request=0, query_params={}, db_collection=builds_db.BUILDS_COLLECTION, is_trigger_build=True)

# create api call data ready for get current build


def current_build(store, build_id):
    api = "/builds/" + build_id
    return fetch_netlify_builds_by_rest_api(store, api, 'GET', limit_per_request=0, query_params={}, db_collection=builds_db.BUILDS_COLLECTION, is_trigger_build=False)

# create api call data ready for cancel build


def cancel_build(store, deploy_id):
    api = "/deploys/" + deploy_id + "/cancel"
    return fetch_netlify_builds_by_rest_api(store, api, 'POST', limit_per_request=0, query_params={}, db_collection=builds_db.BUILDS_COLLECTION, is_trigger_build=False)
