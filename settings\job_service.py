from settings import get_paginated_records_updated
from utils.common import calculatePaginationData, format_seconds
from mongo_db import task_db

TASK_DB = "task_master"

def get_jobs(store, payload):
    payload['filterBy'] = ['name']
    task_list_fields = {
        'name': 1,
        'description': 1,
        'interval_seconds': 1,
        "run_at": 1,
        'is_periodic_task': 1,
        'is_active': 1,
        'store_run_status': {
            store['id']: {
                'status': 1,
                'last_run_end_time': 1,
                'last_run_outcome': 1
            }
        }
    }

    tasks, total_data_length, page, limit = get_paginated_records_updated(store, TASK_DB,
        payload, task_list_fields, {}
    )

    for task in tasks:
        if task.get('run_at', None) is not None:
            task['interval_time'] = "24h"
        elif task.get('interval_seconds', None) == -1:
            task['interval_time'] = "-"
        else:
            task['interval_time'] = format_seconds(task.get('interval_seconds', 0))

    data = calculatePaginationData(tasks, page, limit, total_data_length)
    return data



def get_job_details(payload):
    task_name = payload.get('taskName', None)
    limit = int(payload.get("limit", 5))
    page = int(payload.get("page", 1))

    start_index = (page - 1) * limit

    paginated_data = task_db.fetch_details_by_taskName(
        task_name, start_index, limit)
    data = calculatePaginationData(
        paginated_data, page, limit, len(paginated_data))

    return data


def get_job(job_id, store):
      response = {
         'status': 400
      }
      job = task_db.fetch_task_by_id(job_id, store['id'])
      job_data = {}
      if job:
         job_status = {}
         store_run_status = job.get('store_run_status', {})
         if store_run_status:
            job_status = store_run_status.get(store['id'], {})
         job_data = {
            'name': job.get('name'),   
            'description': job.get('description'),
            'interval_seconds': job.get('interval_seconds'),
            'is_periodic_task': job.get('is_periodic_task'),
            'is_active': job.get('is_active'),
            'status': job_status.get('status'),
            'last_run_end_time': job_status.get('last_run_end_time'),
            'last_run_outcome': job_status.get('last_run_outcome'),
         }
      
      response['data'] = job_data
      response['status'] = 200

      return response
