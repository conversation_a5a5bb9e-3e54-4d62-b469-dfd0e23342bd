from new_mongodb import fetch_one_document_from_storefront_collection
from sqlalchemy import text
import pg_db
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import IntegrityError
from plugin import bc_products
import logging
import traceback
from utils.common import convert_to_timestamp, get_order_status_name
from new_pgdb.analytics_db import AnalyticsDB

logger = logging.getLogger()


def get_bulk_product_info(store, sku):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:
        query = text(
                f"""SELECT * FROM {pg_db.bo_bulk_order_products} where bc_sku = :sku and type = 'bulkorder'""")
        query = query.params(sku=sku)
        result = conn.execute(query).fetchone()                  
        if result:
            response['status'] = 422
            response['message'] = "The product is already exist."
            return response

        res = bc_products.fetch_bc_product_by_sku(store, [sku])        
        if len(res['data']):
            if 'data' in res:
                
                data = res['data'][0]               
                parent_info = {
                    'product_id': data['id'],
                    'name': data['name'],
                    'sku': data['sku'],
                    'parent_upc': data['upc'],
                    'image': None,  # Initialize thumbnail_url to None by default
                    'display_qty_pack': None,
                    'case_qty_pack': None,
                    'variants': [],
                    'is_parent': False,
                    'current_stock': data['inventory_level'] if 'inventory_level' in data else 0,
                }

                for custom_fields in data['custom_fields']:
                    if custom_fields["name"] == 'display qty pack':
                        parent_info['display_qty_pack'] = custom_fields["value"]
                        break
                    else:
                        parent_info['display_qty_pack'] = 0

                for custom_fields in data['custom_fields']:
                    if custom_fields["name"] == 'case qty pack':
                        parent_info['case_qty_pack'] = custom_fields["value"]
                        break
                    else:
                        parent_info['case_qty_pack'] = 0


                for image in data['images']:
                    if image.get('is_thumbnail', False):
                        parent_info['image'] = image.get('url_thumbnail', None)
                        break

                if 'variants' in data:
                    variant_info = []
                    allow_flag = False
                    if len(data['variants']) == 1:
                        allow_flag = True
                        parent_info['is_parent'] = True
                    for variant in data['variants']:
                        if allow_flag:
                            option_values_dict = {}
                            option_values_dict['id'] = variant.get('id', '')
                            option_values_dict['sku'] = variant.get('sku', '')
                            option_values_dict['variant_upc'] = variant.get('upc', '')
                            option_values_dict['current_stock'] = variant.get('inventory_level', '')
                            option_values_dict['option'] = 'Marketing Product'
                            variant_info.append(option_values_dict)  

                        if variant['sku'] != data['sku']: 
                            option_values_dict = {}
                            option_values_dict['id'] = variant.get('id', '')
                            option_values_dict['sku'] = variant.get('sku', '')
                            option_values_dict['variant_upc'] = variant.get('upc', '')
                            # option_values_dict['new_upc'] = '',
                            option_values_dict['current_stock'] = variant.get('inventory_level', '')
                            option_values_dict['option'] = ''

                            if 'option_values' in variant:
                                for index, option_value in enumerate(variant['option_values']):                                
                                    option_values_dict['option'] = option_values_dict['option'] + ' | ' + option_value['label']
                                option_values_dict['option'] = option_values_dict['option'].lstrip(' |')
                            variant_info.append(option_values_dict)       

                    parent_info['variants'] = variant_info

                if len(parent_info['variants']) == 0:
                    parent_info['case_qty_pack'] = 1
                    parent_info['display_qty_pack'] = 1

                if parent_info is not None:
                    response['data'] = [parent_info]
                    response['status'] = 200
                else:
                    response['status'] = 404
                    response['message'] = 'No data found.'
        else:
            response['status'] = 422
            response['message'] = 'Please enter valid SKU.'
    finally:
        if conn:
            conn.close()

    return response
    
def get_bulk_order_info(store,bop_id):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:
        if bop_id:
            query = text(
                f"""SELECT
                        bp.bc_sku AS product_bc_sku,
                        bp.bc_product_id AS product_bc_product_id,
                        bp.bc_name AS product_bc_name,
                        bp.created_at AS product_created_at,
                        bp.name AS product_name,
                        bp.product_image AS product_image,
                        bp.display_qty AS product_display_qty,
                        bp.case_qty AS product_case_qty,
                        bp.status AS product_status,
                        bp.orders AS product_orders,
                        bp.type AS product_type,
                        bopv.id AS variant_id,
                        bopv.bc_sku AS variant_bc_sku,
                        bopv.bc_upc AS variant_bc_upc,
                        bopv.bo_upc AS variant_bo_upc,
                        bopv.bc_variant_id AS bc_variant_id,
                        bopv.option AS option,
                        bopv.po_option AS po_option,
                        bopv.current_stock AS current_stock,
                        bp.brand_id AS product_brand_id,
                        bp.min_market_price AS product_min_market_price,
                        bp.is_marketing_product AS is_marketing_product,
                        bopv.is_active,
                        rv.total_sold_30 AS total_sold_30,
                        rv.total_sold_60 AS total_sold_60
                    FROM
                        {pg_db.bo_bulk_order_products} AS bp
                    LEFT JOIN
                        {pg_db.bo_product_variants} AS bopv ON bp.bop_id = bopv.bop_id
                    LEFT JOIN 
                        {AnalyticsDB.get_replenishment_variants_table()} rv
                        ON (bopv.bc_sku IS NOT NULL AND bopv.bc_sku = rv.sku) 
                        OR (bopv.bc_sku IS NULL AND bopv.bc_variant_id = rv.variant_id)
                    WHERE
                        bp.bop_id = :your_bop_id
                    order by bopv.id;     
                """
            )
            query = query.params(your_bop_id=bop_id)
            result = conn.execute(query).fetchall()           
            formatted_data = {}
            if result: 
                formatted_data['variants'] = []   
                is_marketing_product = False  
                image_url = result[0][5]
                if result[0][10] == 'bulkorder':
                    query = {
                        "id": int(result[0][1]),   
                    }
                    projection = {
                        "images": {
                            "$elemMatch": {
                                "is_thumbnail": True
                            }
                        }
                    }
                    image_res = fetch_one_document_from_storefront_collection(store['id'], 'products', query, projection)
                    if image_res:
                        images = image_res.get('images', [])
                        if len(images) > 0:
                            image = images[0].get('url_thumbnail', '')
                            if image != '':
                                image_url = image      
                for item in result:
                    if 'bc_sku' not in formatted_data:
                        formatted_data['bc_sku'] = item[0]
                        formatted_data['bc_product_id'] = item[1]
                        formatted_data['bc_name'] = item[2]
                        formatted_data['created_at'] = convert_to_timestamp(item[3])
                        formatted_data['name'] = item[4]
                        formatted_data['product_image'] = image_url
                        formatted_data['display_qty'] = item[6]
                        formatted_data['case_qty'] = item[7]
                        formatted_data['status'] = item[8]
                        formatted_data['orders'] = item[9]
                        formatted_data['type'] = item[10]
                        formatted_data['brand_id'] = item[19]
                        formatted_data['min_market_price'] = item[20]
                        formatted_data['is_marketing_product'] = item[21]

                        is_marketing_product = item[21]

                    variant_data = {
                        'variant_id': item[11],
                        'bc_variant_id': item[15],
                        'bc_sku': item[12],
                        'bc_upc': item[13],
                        'bo_upc': item[14],
                        'option': item[16],
                        'po_option': item[17],
                        'current_stock': item[18],
                        'is_active': item[22],
                        'is_deleted': False,  
                        'is_added': False,
                        'total_sold_30_actual_qty': item[23],
                        'total_sold_60_actual_qty': item[24],
                        'total_sold_30': (0 if item[23] is None else item[23]) / (1 if item[7] in [None, 0] else item[7]),
                        'total_sold_60': (0 if item[24] is None else item[24]) / (1 if item[7] in [None, 0] else item[7])
                    }
                    formatted_data['variants'].append(variant_data)
        
                if not is_marketing_product:          
                    res=bc_products.fetch_bc_product_by_sku(store, [formatted_data['bc_sku']])
                    if len(res['data']):
                        if 'data' in res:
                            data = res['data'][0]
                            if 'variants' in data:
                                bc_new_variants = data['variants']
                                first_sku_set = {item['sku'] for item in bc_new_variants if item['sku'] != formatted_data['bc_sku']}
                                second_sku_set = {item['bc_sku'] for item in formatted_data['variants']}

                                for item in bc_new_variants:
                                    if item['sku'] not in second_sku_set and item['sku'] != formatted_data['bc_sku']:
                                        option_set = ''
                                        if 'option_values' in item:
                                            for index, option_value in enumerate(item['option_values']):                                        
                                                option_set = option_set + ' | ' + option_value['label']
                                            option_set = option_set.lstrip(' | ')

                                        formatted_data['variants'].append({
                                            "bc_variant_id": item['id'],
                                            "bc_sku": item['sku'],
                                            'bc_upc': item['upc'],
                                            'bo_upc': item['upc'],
                                            'option': option_set,
                                            'po_option': '',
                                            'current_stock': item['inventory_level'],
                                            'is_active': True,
                                            "is_deleted": False,  
                                            "is_added": True      
                                        })
                                    else:
                                        for variant in formatted_data['variants']:
                                            if variant['bc_sku'] == item['sku']:
                                                variant['current_stock'] = item['inventory_level']
                                                break
                                
                                for item in formatted_data['variants']:
                                    if item['bc_sku'] and item['bc_sku'] not in first_sku_set:
                                        item['is_deleted'] = True    
                if len(formatted_data['variants']) > 0:
                    formatted_data['variants'] = sorted(formatted_data['variants'], key=lambda x: x['is_deleted'])                                 
                response['data']=formatted_data
                response['status'] = 200 
            else:
                response['message']= "No data found for given bulk order product id."   
                response['status'] = 404
           
    except IntegrityError as e:
                error_message = str(e)
                response['status'] = 422
                response['message'] = f"Something went wrong. {error_message}"
    finally:
        if conn:            
            conn.close()
    return response
            
def post_bulk_product_info(bc_sku, bc_product_id, bc_name, created_at, name, product_image, display_qty, case_qty, status, product_type, orders, variant_bc_sku, bc_upc, bc_variant_id, bo_upc, option, po_option, current_stock, username, brand_id, min_market_price, is_marketing_product, is_active, is_bulk_order=False):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        if is_bulk_order:
            max_bop_id_query = text(f"SELECT MAX(bop_id) FROM {pg_db.bo_bulk_order_products};")
            max_bop_id_result = conn.execute(max_bop_id_query).fetchone()
            max_bop_id = max_bop_id_result[0] if max_bop_id_result[0] else 240000
            bop_id = max_bop_id + 1

            query = text(
                f"""INSERT INTO {pg_db.bo_bulk_order_products} (bop_id, bc_sku, bc_product_id, bc_name, name, product_image, display_qty, case_qty, status, type, is_qty_locked, orders, created_by, created_at, updated_by, brand_id, min_market_price, is_marketing_product)
                    VALUES (:bop_id, :bc_sku, :bc_product_id, :bc_name, :name, :product_image, :display_qty, :case_qty, :status, :type, :is_qty_locked, :orders, :created_by, :created_at,:updated_by, :brand_id, :min_market_price, :is_marketing_product);
                """
            )
            query = query.params(bop_id=bop_id, bc_sku=bc_sku, bc_product_id=bc_product_id, bc_name=bc_name, name=name, product_image=product_image, display_qty=display_qty, case_qty=case_qty, status=status, type=product_type, is_qty_locked=False, orders=orders, created_by=username, created_at=created_at,updated_by=username, brand_id=brand_id, min_market_price=min_market_price, is_marketing_product=is_marketing_product)
            result = conn.execute(query)
            conn.commit()

            if result.rowcount > 0:
                # _update_brands_product_count(brand_id)
                bo_query = text(f"INSERT INTO {pg_db.bo_product_variants} (bop_id, bc_sku, bc_upc, bo_upc, bc_variant_id, option, po_option, current_stock, is_active) VALUES (:bop_id, :bc_sku, :bc_upc, :bo_upc, :bc_variant_id, :option, :po_option, :current_stock, :is_active);")
                bo_query_params = {'bop_id': bop_id, 'bc_sku': variant_bc_sku, 'bc_upc': bc_upc, 'bo_upc':bo_upc, 'bc_variant_id':bc_variant_id, 'po_option': po_option, 'option': option, 'current_stock': current_stock, 'is_active': is_active}
                res = conn.execute(bo_query, bo_query_params)

                if res.rowcount < 1:
                    bop_id -= 1
                    conn.execute(f"UPDATE {pg_db.bo_bulk_order_products} SET bop_id = {bop_id}")
        
        else:
            max_bop_id_query = text(f"SELECT MAX(bop_id) FROM {pg_db.bo_bulk_order_products};")
            max_bop_id_result = conn.execute(max_bop_id_query).fetchone()
            max_bop_id = max_bop_id_result[0] if max_bop_id_result[0] else 240000

            bo_query = text(f"INSERT INTO {pg_db.bo_product_variants} (bop_id, bc_sku, bc_upc, bo_upc, bc_variant_id, option,  po_option, current_stock, is_active) VALUES (:bop_id, :bc_sku, :bc_upc, :bo_upc, :bc_variant_id, :option, :po_option, :current_stock, :is_active);")
            bo_query_params = {'bop_id': max_bop_id, 'bc_sku': variant_bc_sku, 'bc_upc': bc_upc,'bo_upc':bo_upc, 'bc_variant_id':bc_variant_id, 'po_option':  po_option, 'option': option, 'current_stock': current_stock, 'is_active': is_active}
            res = conn.execute(bo_query, bo_query_params)

            if res.rowcount < 1:
                bop_id -= 1
                conn.execute(f"UPDATE {pg_db.bo_bulk_order_products} SET bop_id = {bop_id}")

        if res.rowcount > 0:
            response['status'] = 200
            response['message'] = "Data inserted successfully."
        else:
            response['status'] = 400
            response['message'] = "Data insertion failed."
    except IntegrityError as e:
            logger.error(traceback.format_exc())
            if isinstance(e.orig, UniqueViolation):
                response['status'] = 422
                response['message'] = "Duplicate key violation: This product already exists"
            else:
                response['status'] = 500
                response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response


def update_bulk_order_info(payload, username, bop_id, store):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        query = text(
                f"""SELECT bp.bc_sku, bp.bc_product_id, bp.bc_name AS product_bc_name, bp.created_at, bp.name AS product_name                        
                    FROM {pg_db.bo_bulk_order_products} AS bp WHERE bp.bop_id = :your_bop_id;
                """
            )
        query = query.params(your_bop_id=bop_id)
        result = conn.execute(query).fetchone()                  
        if result is None:
            response['status'] = 404
            response['message'] = "Data not found."
            return response
        
        pending_query = text(
                f"""SELECT po.po_id, po.status, pol.bop_id                        
                    FROM {pg_db.bo_purchase_orders} AS po JOIN {pg_db.bo_purchase_order_lineitems} AS pol ON po.po_id = pol.po_id WHERE pol.bop_id = :your_bop_id and po.status = :status;
                """
            )
        pending_query = pending_query.params(your_bop_id=bop_id, status='pending')
        pending_result = conn.execute(pending_query).fetchone() 
        if pending_result and len(payload) == 1 and 'status' in payload:
            if payload['status'] == 'archived':
                response['status'] = 404
                response['message'] = "This product has pending purchase orders. so it can't be archived."
                return response
       
        update_order_info = {}
        for field in ['name', 'display_qty', 'case_qty', 'status', 'brand_id', 'min_market_price']:
            if field in payload:
                update_order_info[field] = payload[field]        

        if update_order_info:
            set_clause = ", ".join([f"{field} = :{field}" for field in update_order_info])
            set_clause = f"SET {set_clause},"
        else:
            set_clause = "SET "
        query = text(
                f"""UPDATE {pg_db.bo_bulk_order_products} 
                        {set_clause}
                        updated_by = :updated_by,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE 
                        bop_id = :bop_id;"""
            )
        params = update_order_info.copy()
        params.update({'updated_by': username, 'bop_id': bop_id})
        result = conn.execute(query, params)


        if 'variants' in payload and len(payload['variants']) > 0:
            for variant in payload['variants']:
                if payload['type'] == 'bulkorder':
                    variant_id = variant.get('variant_id', None)
                    if 'variant_id' in variant and variant_id is not None:
                        update_varaint_info = {}
                        for field in ['bo_upc', 'is_active']:
                            if field in variant:
                                update_varaint_info[field] = variant[field]  
        
                        if update_varaint_info:
                            set_clause = ", ".join([f"{field} = :{field}" for field in update_varaint_info])
                            set_clause = f"SET {set_clause},"

                            set_clause = set_clause.rstrip(',')
                            query = text(
                            f"""UPDATE {pg_db.bo_product_variants}
                                    {set_clause}
                                WHERE 
                                    id = :variant_id
                                    AND bop_id = :bop_id;"""
                            )
                            params = update_varaint_info.copy()
                            params.update({'variant_id': variant_id, 'bop_id': bop_id})
                            result = conn.execute(query, params)
                    else:
                        result = add_variant(variant, bop_id)   
                elif payload['type'] == 'preorder':
                    update_order_info = {}
                    variant_id = variant.get('variant_id')
                    if 'is_deleted' not in variant:
                        if variant_id is not None:
                            for field in ['po_option', 'bc_sku', 'current_stock', 'option', 'bc_variant_id', 'is_active']:
                                if field in variant:
                                    update_order_info[field] = variant[field]        

                            if update_order_info:
                                set_clause = ", ".join([f"{field} = :{field}" for field in update_order_info])
                                set_clause = f"SET {set_clause}"
                            else:
                                set_clause = ""
                            query = text(
                                    f"""UPDATE {pg_db.bo_product_variants} 
                                            {set_clause}
                                        WHERE 
                                            id = :variant_id
                                        AND bop_id = :bop_id;"""
                                )
                            params = update_order_info.copy()
                            params.update({'variant_id': variant_id, 'bop_id': bop_id})
                            result = conn.execute(query, params)

                            update_order_lineitems = {}
                            for field in ['po_option', 'bc_sku', 'option']:
                                if field in variant:
                                    update_order_lineitems[field] = variant[field]        

                            if update_order_lineitems:
                                set_clause_lineitems = ", ".join([f"{field} = :{field}" for field in update_order_lineitems])
                                set_clause_lineitems = f"SET {set_clause_lineitems}"
                            else:
                                set_clause_lineitems = ""
                            pre_query = text(
                                f"""UPDATE {pg_db.bo_purchase_order_lineitems}
                                        {set_clause_lineitems}
                                    WHERE 
                                        variant_id = :variant_id
                                        AND bop_id = :bop_id;"""
                            )
                            params = update_order_lineitems.copy()
                            params.update({
                                'variant_id': variant_id,
                                'bop_id': bop_id  
                            })
                            result = conn.execute(pre_query, params)
                        else:
                            result = add_variant(variant, bop_id)        
                    else:
                        
                        query = text(
                            f"DELETE FROM bo_product_variants WHERE id = :variant_id"
                            )
                        conn.execute(query.params(variant_id=variant_id))
                        conn.commit()
     
        if result.rowcount > 0:
            response['status'] = 200
            response['message'] = "Data updated successfully."
        else:
            response['status'] = 500
            response['message'] = "Data updation failed"
    except IntegrityError as e:
        logger.error(traceback.format_exc())
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 422
            response['message'] = "Duplicate key violation: This project already exists."
        else:
            error_message = str(e)
            response['status'] = 500
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response	

def add_variant(variant, bop_id):
    po_option = variant.get('po_option', None)
    bc_sku = variant.get('bc_sku')
    current_stock = variant.get('current_stock')
    option = variant.get('option')
    bc_variant_id = variant.get('bc_variant_id')
    bo_upc = variant.get('bo_upc', None)
    bc_upc = variant.get('bc_upc', None)
    is_active = variant.get('is_active', True)
   
    conn = pg_db.get_connection()
    try:
        bo_query = text(f"INSERT INTO {pg_db.bo_product_variants} (bop_id, bc_sku, bc_upc, bo_upc, bc_variant_id, option, po_option, current_stock, is_active) VALUES (:bop_id, :bc_sku, :bc_upc, :bo_upc, :bc_variant_id, :option, :po_option, :current_stock, :is_active);")
        bo_query_params = {'bop_id': bop_id, 'bc_sku': bc_sku, 'bc_upc': bc_upc, 'bo_upc':bo_upc, 'bc_variant_id':bc_variant_id, 'po_option': po_option, 'option': option, 'current_stock': current_stock, 'is_active': is_active}
        res = conn.execute(bo_query, bo_query_params)
    finally:
        if conn:
            conn.commit()
            conn.close()
    return res

def update_preorder_product_info(product_id, payload, username):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        name = payload.get('name', '')
        bc_sku = payload.get('bc_sku', '')
        bc_product_id = payload.get('bc_product_id', '')
        bc_name = payload.get('bc_name', '')
        product_image = payload.get('product_image', '')
        display_qty = payload.get('display_qty', 0)
        case_qty = payload.get('case_qty', 0)
        variants = payload.get('variants', [])
        type = payload.get('type', 'preorder')
        status = payload.get('status', 'active')
        brand_id = payload.get('brand_id', 0)
        min_market_price = payload.get('min_market_price', 0)

        # _update_brands_product_count(brand_id, product_id)

        bop_query = text(f"UPDATE {pg_db.bo_bulk_order_products} SET status = :status, type = :type, name = :name, bc_name = :bc_name, bc_sku = :bc_sku, bc_product_id = :bc_product_id, product_image = :product_image, display_qty = :display_qty, case_qty = :case_qty, updated_by = :username, updated_at = current_timestamp, brand_id = :brand_id, min_market_price = :min_market_price WHERE bop_id = :bop_id;") 
        bop_query_params = {'bop_id': product_id, 'status': status, 'type': type, 'name': name, 'bc_name': bc_name, 'bc_sku': bc_sku, 'bc_product_id': bc_product_id, 'product_image': product_image, 'display_qty': display_qty, 'case_qty': case_qty, 'username': username, 'brand_id': brand_id, 'min_market_price': min_market_price}
        bop_res = conn.execute(bop_query, bop_query_params)

        # _update_brands_product_count(brand_id) 

        for variant in variants:
            if 'is_deleted' not in variant:                
                variant_id = variant.get('variant_id', 0)
                variant_bc_sku = variant.get('bc_sku', '')
                variant_bc_sku = None if variant_bc_sku == '' else variant_bc_sku
                bc_upc = variant.get('bc_upc', None)
                bc_variant_id = variant.get('bc_variant_id', 0)
                bo_upc = variant.get('bo_upc', None)
                option = variant.get('option', None)
                po_option = variant.get('po_option', None)
                current_stock = variant.get('current_stock', 0)
                is_active = variant.get('is_active', True)

                if 'variant_id' in variant:
                    pop_query = text(f"UPDATE {pg_db.bo_product_variants} SET po_option = :po_option, option = :option, bc_sku = :bc_sku, bc_upc = :bc_upc, bc_variant_id = :bc_variant_id, bo_upc = :bo_upc, current_stock = :current_stock, is_active = :is_active WHERE id = :variant_id and bop_id = :bop_id;") 
                    pop_query_params = {'bop_id': product_id, 'variant_id': variant_id, 'bc_sku': variant_bc_sku, 'bc_upc': bc_upc, 'bo_upc':bo_upc, 'bc_variant_id':bc_variant_id, 'po_option':  po_option, 'option': option, 'current_stock': current_stock, 'is_active': is_active}
                    res = conn.execute(pop_query, pop_query_params)  

                    if bc_variant_id != None and option != None and po_option != None:
                        po_query = text(f"UPDATE {pg_db.bo_purchase_order_lineitems} SET po_option = :po_option, option = :option, bc_sku = :bc_sku, bc_upc = :bc_upc, bc_variant_id = :bc_variant_id WHERE variant_id = :variant_id and bop_id = :bop_id;") 
                        po_query_params = {'bop_id': product_id, 'variant_id': variant_id, 'bc_sku': variant_bc_sku, 'bc_upc': bc_upc, 'bc_variant_id':bc_variant_id, 'po_option':  po_option, 'option': option}
                        res = conn.execute(po_query, po_query_params)  
                else:
                    pop_option_query = text(f"INSERT INTO {pg_db.bo_product_variants} (bop_id, bc_sku, bc_upc, bo_upc, bc_variant_id, option,  po_option, current_stock, is_active) VALUES (:bop_id, :bc_sku, :bc_upc, :bo_upc, :bc_variant_id, :option, :po_option, :current_stock, :is_active);")
                    pop_option_query_params = {'bop_id': product_id, 'bc_sku': variant_bc_sku, 'bc_upc': bc_upc,'bo_upc':bo_upc, 'bc_variant_id':bc_variant_id, 'po_option':  po_option, 'option': option, 'current_stock': current_stock, 'is_active': is_active}
                    res = conn.execute(pop_option_query, pop_option_query_params)
            else:
                variant_id = variant.get('variant_id', 0)
                query = text(f"DELETE FROM bo_product_variants WHERE id = :variant_id and bop_id = :bop_id")
                conn.execute(query.params(variant_id=variant_id, bop_id=product_id))
                conn.commit()

                query = text(f"DELETE FROM bo_purchase_order_lineitems WHERE variant_id = :variant_id and bop_id = :bop_id")
                conn.execute(query.params(variant_id=variant_id, bop_id=product_id))
                conn.commit()
            
        response['status'] = 200
        response['message'] = 'Data Updated successfully.'    

    except IntegrityError as e:            
        error_message = str(e)
        response['status'] = 422
        response['message'] = f"Something went wrong. {error_message}"
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response
       
def get_product_variant_dashboard_data(store, bop_id):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:
        if bop_id:
            query = text(
                f"""SELECT
                        bp.bc_sku AS product_bc_sku,
                        bp.bc_product_id AS product_bc_product_id,
                        bp.bc_name AS product_bc_name,
                        bp.name AS product_name,
                        bp.product_image AS product_image,
                        bp.display_qty AS product_display_qty,
                        bp.case_qty AS product_case_qty,
                        bp.status AS product_status,
                        bp.brand_id AS product_brand_id,
                        bp.min_market_price AS product_min_market_price,
                        bp.is_marketing_product AS is_marketing_product,
                        bopv.id AS variant_id,
                        bopv.bc_sku AS variant_bc_sku,
                        bopv.bc_upc AS variant_bc_upc,
                        bopv.bo_upc AS variant_bo_upc,
                        bopv.bc_variant_id AS bc_variant_id,
                        bopv.option AS option,
                        bopv.po_option AS po_option,
                        bopv.current_stock AS current_stock,
                        (SELECT SUM(pol.remaining_qty) FROM bo_purchase_order_lineitems AS pol JOIN bo_purchase_orders AS po ON po.po_id = pol.po_id WHERE po.status NOT IN ('deleted', 'completed') AND pol.bop_id = :your_bop_id AND pol.variant_id = bopv.id AND (pol.status IS NULL OR pol.status NOT IN ('cancelled', 'completed'))) AS pending_po_count,
                        bopv.is_active
                    FROM
                        {pg_db.bo_bulk_order_products} AS bp
                    LEFT JOIN
                        {pg_db.bo_product_variants} AS bopv ON bp.bop_id = bopv.bop_id
                    WHERE
                        bp.bop_id = :your_bop_id
                    order by bopv.id;     
                """
            )
            query = query.params(your_bop_id=bop_id)
            result = conn.execute(query).fetchall()           
            formatted_data = {}
            if result: 
                formatted_data['variants'] = []           
                for item in result:
                    if 'bc_sku' not in formatted_data:
                        formatted_data['bc_sku'] = item[0]
                        formatted_data['bc_product_id'] = item[1]
                        formatted_data['bc_name'] = item[2]
                        formatted_data['name'] = item[3]
                        formatted_data['product_image'] = item[4]
                        formatted_data['display_qty'] = item[5]
                        formatted_data['case_qty'] = item[6]
                        formatted_data['status'] = item[7]
                        formatted_data['brand_id'] = item[8]
                        formatted_data['min_market_price'] = item[9]
                        formatted_data['is_marketing_product'] = item[10]

                    
                    variant_data = {
                        'variant_id': item[11],
                        'bc_variant_id': item[15],
                        'bc_sku': item[12],
                        'bc_upc': item[13],
                        'bo_upc': item[14],
                        'option': item[16],
                        'po_option': item[17],
                        'current_stock': item[18],
                        'total_req_qty': item[19] if item[19] else 0,
                        'is_active': item[20]
                    }
                    formatted_data['variants'].append(variant_data)
        
                                                   
                response['data']=formatted_data
                response['status'] = 200 
            else:
                response['message']= "No data found for given bulk order product id."   
                response['status'] = 404
           
    except IntegrityError as e:
                error_message = str(e)
                response['status'] = 422
                response['message'] = f"Something went wrong. {error_message}"
    finally:
        if conn:            
            conn.close()
    return response

def get_product_bc_orders(store, bop_id, status, filter):
    response = {
        "status" :400,
        "message": "Please provide valid bulk order product id."
    }
    conn = pg_db.get_connection()
    try:
        if bop_id:
            data = []
            customer_array = {}

            customer_query = text(f"""SELECT  DISTINCT(po.customer_name), po.customer_id, po.customer_rep_id, po.customer_rep_name, po.customer_rep_email FROM bo_purchase_orders AS po JOIN bo_purchase_order_lineitems AS pol ON po.po_id = pol.po_id WHERE pol.bop_id = :bop_id order by po.customer_name""")
            customer_query = customer_query.params(bop_id=bop_id)
            customer_result = conn.execute(customer_query).fetchall()  
            if customer_result:
                for c in customer_result:
                    customer_array[c[1]] = c[0]

            append_query = ''
            if status != '':
                append_query += f" and po.status = '{status}' "

            if filter != '':
                append_query += f" and po.customer_id = '{int(filter)}' "

            query = (f"""SELECT po.po_id, po.status, po.customer_name, po.customer_rep_name, po.customer_rep_email, pbo.bc_order_id, SUM(pbo.order_total) AS order_total, pbo.created_at, pbo.created_by, o.order_status AS bc_order_status, o.order_status_id FROM bo_purchase_order_bc_order_mapping AS pbo JOIN bo_purchase_orders AS po ON pbo.po_id = po.po_id  LEFT JOIN orders AS o ON o.order_id = pbo.bc_order_id::INT
                         WHERE po.po_id IN (SELECT DISTINCT(po.po_id) FROM bo_purchase_orders as po JOIN bo_purchase_order_lineitems as pol ON po.po_id = pol.po_id WHERE pol.bop_id = :bop_id) {append_query} GROUP BY po.po_id, po.status, pbo.bc_order_id, pbo.created_at, pbo.created_by, o.order_status_id, o.order_status ORDER BY pbo.created_at DESC""")
            query = query.format(append_query)    
            result = conn.execute(text(query), {'bop_id': bop_id}).fetchall()
                
            if result:            
                for item in result:
                    order_list={}
                    order_list['po_id'] = item[0]
                    order_list['status']=item[1]
                    order_list['customer_name']=item[2]
                    order_list['customer_rep_name']=item[3]
                    order_list['customer_rep_email']=item[4]
                    order_list['bc_order_id']=item[5]
                    order_list['order_total']=item[6]
                    order_list['created_at']=convert_to_timestamp(item[7])
                    order_list['created_by']=item[8]
                    order_list['bc_order_status']=item[9] if item[9] else get_order_status_name(item[10])
                    order_list['bc_order_status_id']=item[10]

                    data.append(order_list)           
                                                   
            final_data = {
                'data': data or [],
                'meta': {
                    'customers': customer_array or {}
                }
            }             
                                                   
            response['data'] = final_data
            response['status'] = 200  
        else:
            response['message']= "Please provide valid bulk order product id."   
            response['status'] = 404
           
    except IntegrityError as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = f"Something went wrong. {error_message}"
    finally:
        if conn:            
            conn.close()
    return response

def get_product_po_orders(store, bop_id, status, filter, rep_filter, is_from_popup, sort_array=[]):
    response = {
        "status" :400,
        "message": "Please provide valid bulk order product id."
    }
    conn = pg_db.get_connection()
    try:
        if bop_id:
            data = []
            customer_array = {}
            customer_rep_array = {}

            # Fetch the product_id using bop_id from the bo_bulk_order_products table
            product_query = text("SELECT bc_product_id FROM bo_bulk_order_products WHERE bop_id = :bop_id")
            product_result = conn.execute(product_query, {'bop_id': bop_id}).fetchone()

            if product_result:
                product_id = product_result[0]
            else:
                response['message'] = "No product found for the given bulk order product id."
                return response

            customer_query = text(f"""SELECT  DISTINCT(po.customer_name), po.customer_id, po.customer_rep_id, po.customer_rep_name, po.customer_rep_email FROM bo_purchase_orders AS po JOIN bo_purchase_order_lineitems AS pol ON po.po_id = pol.po_id WHERE pol.bop_id = :bop_id order by po.customer_name""")
            customer_query = customer_query.params(bop_id=bop_id)
            customer_result = conn.execute(customer_query).fetchall()  
            if customer_result:
                for c in customer_result:
                    customer_array[c[1]] = c[0]
                    if c[2] not in customer_rep_array:
                        customer_rep_array[c[4]] = c[3]
            
            
            append_query = ''
            if status != '' and not is_from_popup:
                append_query += f" and po.status = '{status}' "
            
            if is_from_popup:
                append_query += f" and po.status NOT IN ('deleted', 'completed') and (pol.status IS NULL OR pol.status NOT IN ('cancelled', 'completed')) "

            if filter != '':
                append_query += f" and po.customer_id = '{int(filter)}' "

            if rep_filter != '':
                append_query += f" and po.customer_rep_email = '{rep_filter}' "

            query = (
                    f"""
                    SELECT 
                        po.po_id,
                        po.status,
                        po.customer_id,
                        po.customer_name,
                        po.customer_rep_id,
                        po.customer_rep_name,
                        po.customer_rep_email,
                        po.created_by,
                        po.created_at,
                        SUM(pol.price) AS total_price,
                        SUM(pol.requested_qty) AS total_req_qty,
                        SUM(pol.remaining_qty) AS total_pending_qty,
                        c.company,
                        ca.state
                    FROM 
                        bo_purchase_orders AS po
                    JOIN 
                        bo_purchase_order_lineitems AS pol ON po.po_id = pol.po_id
                    LEFT JOIN 
                        customers AS c ON po.customer_id = c.customer_id
                    LEFT JOIN (SELECT DISTINCT ON (customer_id) customer_id, state FROM customer_addresses
						ORDER BY customer_id, customer_address_id desc) AS ca ON ca.customer_id = po.customer_id
                    WHERE 
                        pol.bop_id = :bop_id 
                        {append_query} 
                    GROUP BY 
                        po.po_id, 
                        po.status, 
                        po.customer_id, 
                        po.customer_name, 
                        po.customer_rep_id, 
                        po.customer_rep_name, 
                        po.customer_rep_email, 
                        po.created_by, 
                        po.created_at, 
                        c.company, 
                        ca.state
                    """
                )

            if len(sort_array):
                sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
                if sort_array[0] in ["total_pending_qty", "total_req_qty", "created_at", "customer_rep_name", "total_price", "state", "company"]:                
                    query += f" ORDER BY {sort_array[0]} {sort_direction}"
            
            query = query.format(append_query)    
            result = conn.execute(text(query), {'bop_id': bop_id}).fetchall()  

            if result:            
                for item in result:
                    order_list={}
                    order_list['po_id'] = item[0]
                    order_list['status']=item[1]
                    order_list['customer_id']=item[2]
                    order_list['customer_name']=item[3]
                    order_list['customer_rep_id']=item[4]
                    order_list['customer_rep_name']=item[5]
                    order_list['customer_rep_email']=item[6]
                    order_list['created_by']=item[7]
                    order_list['created_at']=convert_to_timestamp(item[8])
                    order_list['total_price']=item[9]
                    order_list['total_req_qty']=item[10]
                    order_list['total_pending_qty'] = item[11]
                    order_list['company'] = item[12]
                    order_list['state'] = item[13]

                    # Check reorder status for the customer using product_id
                    reorder_query = text("""SELECT od.customer_id 
                                            FROM orders od 
                                            JOIN order_line_items oli ON od.order_id = oli.order_id 
                                            WHERE oli.product_id = :product_id 
                                            AND od.customer_id = :customer_id 
                                            GROUP BY od.customer_id""")
                    reorder_result = conn.execute(reorder_query, {'product_id': product_id, 'customer_id': item[2]}).fetchone()
                    
                    order_list['reorder'] = True if reorder_result else False
                    data.append(order_list)

            if len(sort_array):
                sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
                if sort_array[0] in ["reorder"]:                
                    data = sorted(data, key=lambda x: x["reorder"], reverse=sort_direction == 'DESC')        

            final_data = {
                'data': data or [],
                'meta': {
                    'customers': sort_dict_by_values(customer_array) or {},
                    'customer_rep': sort_dict_by_values(customer_rep_array) or {}
                }
            }             
                                                   
            response['data'] = final_data
            response['status'] = 200 
        else:
            response['message'] = "Please provide valid bulk order product id."   
            response['status'] = 404
           
    except IntegrityError as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = f"Something went wrong. {error_message}"
    finally:
        if conn:            
            conn.close()
    return response

def sort_dict_by_values(d):
    """Sort a dictionary by its values in ascending order."""
    return {k: v for k, v in sorted(d.items(), key=lambda item: item[1].lower())}