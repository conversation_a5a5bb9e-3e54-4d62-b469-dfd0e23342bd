from flask import request
import logging
import traceback
from api import APIResource
from smart_badges import smart_badges

logger = logging.getLogger()

class SmartBadges(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Smart Badges GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 30))
            sort_by = query_params.get('sort_by', '')
            sort_array = sort_by.split("/") if sort_by != '' else []
            search = query_params.get('search', '')
            result = smart_badges.get_all_badges(store, page, limit, sort_array, search)
            if result['status'] == 200:
                return result['data'], 200
            else:
                return {'message': result['message']}, result['status']
        finally:
            logger.debug("Exiting Smart Badges GET")

    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Smart Badges POST")
        try:
            payload = request.get_json(force=True)
            username = token_payload['username'] if 'username' in token_payload else ''
            res = smart_badges.create_badge(store, payload, username)
            if res['status'] == 200:
                return {'message': res['message']}, res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Smart Badges POST")

    def delete_executor(self, request, token_payload, store):
        logger.debug("Entering Smart Badges DELETE")
        try:
            payload = request.get_json(force=True)
            ids = payload.get("ids", [])
            if not ids:
                return {"message": "No badge IDs provided for deletion."}, 400

            res = smart_badges.delete_badges(store, ids)
            return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Smart Badges DELETE")

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
    def delete(self):
        return self.execute_store_request(request, self.delete_executor)
    
class SmartBadge(APIResource):
    def get_executor(self, request, token_payload, store, id):
        logger.debug("Entering Smart Badge GET by ID")
        try:
            result = smart_badges.get_badge(store, id)
            if result['status'] == 200:
                return {'data': result['data']}, result['status']
            else:
                return {'message': result['message']}, result['status']
        finally:
            logger.debug("Exiting Smart Badge GET by ID")

    def patch_executor(self, request, token_payload, store, id):
        logger.debug("Entering Smart Badge PATCH")
        try:
            payload = request.get_json(force=True)
            username = token_payload.get("username", "")
            if username:
                result = smart_badges.update_badge(store, payload, username, id)
                return {'message': result['message']}, result['status']
            else:
                return {'message': 'Unauthorized'}, 401
        finally:
            logger.debug("Exiting Smart Badge PATCH")

    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id)
    
    def patch(self, id):
        return self.execute_store_request(request, self.patch_executor, id)
    
class AllProducts(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Smart Badge Products DropDown GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            search = query_params.get('search', '').strip()
            badge_id = query_params.get('badge_id', '').strip()
            res = smart_badges.get_products_dropdown(store, search, page, limit, badge_id)
            
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Smart Badge Products DropDown GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class AllBrands(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Smart Badge Brands DropDown GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            search = query_params.get('search', '').strip()
            badge_id = query_params.get('badge_id', '').strip()
            res = smart_badges.get_brands_dropdown(store, search, page, limit, badge_id)
            
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Smart Badge Brands DropDown GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class AllCategories(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Smart Badge Category DropDown GET")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            search = query_params.get('search', '').strip()
            badge_id = query_params.get('badge_id', '').strip()
            res = smart_badges.get_category_dropdown(store, search, page, limit, badge_id)
            
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Smart Badge Category DropDown GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class CategoryTreeview(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Smart Badge Category Treeview GET")
        try:
            query_params = request.args.to_dict()
            search = query_params.get('search', None)
            res = smart_badges.get_category_treeview(store, search=search)
            return res, 200
        finally:
            logger.debug("Exiting Smart Badge Category Treeview GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class BadgeProductDetails(APIResource):
    def get_executor(self, request, token_payload, store, id):
        logger.debug("Entering Smart Badge Product GET by ID")
        try:
            query_params = request.args.to_dict()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 30))
            sort_by = query_params.get('sort_by', '')
            sort_array = sort_by.split("/") if sort_by != '' else []
            search = query_params.get('search', '').strip()
            brand_id = query_params.get('brand_id', '').strip()
            category_id = query_params.get('category_id', '').strip()
            result = smart_badges.get_products_details(store, id, page, limit, sort_array, search, brand_id, category_id)
            if result['status'] == 200:
                return result['data'], 200
            else:
                return {'message': result['message']}, result['status']
        finally:
            logger.debug("Exiting Smart Badge Product GET by ID")

    def delete_executor(self, request, token_payload, store, id):
        logger.debug("Entering Smart Badges Product DELETE")
        try:
            payload = request.get_json(force=True)
            
            res = smart_badges.delete_badges_product(store, id, payload)
            return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Smart Badges Product DELETE")

    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id)
    
    def delete(self, id):
        return self.execute_store_request(request, self.delete_executor, id)