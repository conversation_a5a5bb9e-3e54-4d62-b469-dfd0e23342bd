from flask import request
import logging
from api import APIResource
import traceback
from schemas.dynamic_webpage import webpage_schema, version_schema, webpage_update_schema
import json
from utils import bc, store_util
from datetime import datetime
from utils.common import parse_json
from cms.webpages import webpages_list

logger = logging.getLogger()

# get all webpages 


class Pages(APIResource):
    def get_executor(self, request, token_payload, store, tenant_id, store_id):
        try:
            query_params = request.args.to_dict()
            cms_db = store_util.get_cms_db(store)
            cdn_url = store_util.get_cdn_base_url(store)
            res = webpages_list.get_web_pages(store, query_params, cdn_url)
            return res, 200
        finally:
            logger.debug("Exiting Cart GET")

    def get(self, tenant_id, store_id):
        return self.execute_store_request(request, self.get_executor, tenant_id, store_id)


class StorefrontPages(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            query_params = request.args.to_dict()        
            cms_db = store_util.get_cms_db(store)    
            cdn_url = store_util.get_cdn_base_url(store)
            res = webpages_list.get_pages_storefront(store, query_params, cdn_url)
            return res, 200
        finally:
            logger.debug("Exiting Cart GET")

    def get(self, tenant_id, store_id):
        return self.execute_store_request(request, self.get_executor)

# create webpage


class CreatePages(APIResource):
    def post_executor(self, request, token_payload, store, token=None):
        try:
            req_body = request.get_json(force=True)
            validated_data = webpage_schema.validate(req_body)
            res = webpages_list.create_page(store, validated_data)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409
        finally:
            logger.debug("Exiting Dynamic Webpage POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)

# upadate and delete webpage


class PageOperations(APIResource):
    def put_executor(self, request, token_payload, store, webpage_id):
        try:
            req_body = request.get_json(force=True)
            validated_data = webpage_update_schema.validate(req_body)
            res = webpages_list.update_page(store, validated_data, webpage_id)

            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409
        finally:
            logger.debug("Exiting webpage PUT")

    def post_executor(self, request, token_payload, store, webpage_id):
        try:
            req_body = request.get_json(force=True)
            if req_body and 'is_customers_only' in req_body:
                cms_db = store_util.get_cms_db(store)
                res = webpages_list.update_customer_only_flag(store, req_body, webpage_id)

                if res['status'] == 200:
                    return {"status": res['message']}, 200
                else:
                    return {"message": "Something went wrong at server!!"}, 500
            else:
                return {"message": "Please esure that your request body is correct."}, 400
        finally:
            logger.debug("Exiting Dynamic Webpage POST")

    def delete_executor(self, request, token_payload, store, webpage_id):
        try:
            cms_db = store_util.get_cms_db(store)
            success = webpages_list.delete_webpage_by_id(store, webpage_id)
            if success:
                return {"status": "ok"}, 200
            return {"status": "failed"}, 500
        finally:
            logger.debug("Exiting Webpage DELETE")

    def put(self, webpage_id):
        return self.execute_store_request(request, self.put_executor, webpage_id)
    
    def post(self, webpage_id):
        return self.execute_store_request(request, self.post_executor, webpage_id)

    def delete(self, webpage_id):
        return self.execute_store_request(request, self.delete_executor, webpage_id)


# operations for add webpage version and get webpage with active version
class PageComponents(APIResource):
    def put_executor(self, request, token_payload, store, webpage_id):
        try:
            req_body = request.get_json(force=True)
            res = webpages_list.set_version(store, req_body, webpage_id)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409
        finally:
            logger.debug("Exiting webpage PUT")

    def get_executor(self, request, token_payload, store, webpage_id):
        try:
            res = webpages_list.get_webpage(store, webpage_id)
            return res, 200
        finally:
            logger.debug("Exiting Webpage GET")

    def put(self, webpage_id):
        return self.execute_store_request(request, self.put_executor, webpage_id)

    def get(self, webpage_id):
        return self.execute_store_request(request, self.get_executor, webpage_id)


# set page preview data
class PagePreviewData(APIResource):
    def put_executor(self, request, token_payload, store, webpage_id):
        try:
            req_body = request.get_json(force=True)
            res = webpages_list.set_page_preview(store, req_body, webpage_id)

            if res['status'] == 200:
                return {"status": res['message']}, 200
            else:
                return {"message": res['message']}, 500
        finally:
            logger.debug("Exiting webpage Preview PUT")

    def put(self, webpage_id):
        return self.execute_store_request(request, self.put_executor, webpage_id)

# get list of webpage version ids


class PageVersionsOperation(APIResource):
    def get_executor(self, request, token_payload, store, webpage_id):
        try:
            res = webpages_list.get_webpage_versions(store, webpage_id)
            return res, 200
        finally:
            logger.debug("Exiting Webpage GET")

    def post_executor(self, request, token_payload, store, webpage_id):
        try:
            req_body = request.get_json(force=True)
            validated_data = version_schema.validate(req_body)
            res = webpages_list.set_active_version(store, validated_data, webpage_id)

            if res['status'] == 200:
                return {"status": res['message']}, 200
            else:
                return {"message": "Something went wrong at server!!"}, 500
        finally:
            logger.debug("Exiting Dynamic Webpage POST")

    def get(self, webpage_id):
        return self.execute_store_request(request, self.get_executor, webpage_id)

    def post(self, webpage_id):
        return self.execute_store_request(request, self.post_executor, webpage_id)

# get webpage with selected versions data


class PageVersionData(APIResource):
    def get_executor(self, request, token_payload, store, webpage_id):
        try:
            query_params = request.args.to_dict()
            res = webpages_list.get_webpage_versionData(store, query_params, webpage_id)
            return res, 200
        finally:
            logger.debug("Exiting Webpage GET")

    def get(self, webpage_id):
        return self.execute_store_request(request, self.get_executor, webpage_id)


# store and get images to server
class ImageOperation(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            query_params = request.args.to_dict()
            res = webpages_list.getImage(query_params)
            return res
        finally:
            logger.debug("Exiting Webpage GET")

    def post_executor(self, request, token_payload, store):
        try:
            req_body = {
                "image": request.files.get("image"),
                "type": request.form.get("type")
            }
            res = webpages_list.setImage(req_body)

            if res['status'] == 200:
                return {"status": res['message']}, 200
            else:
                return {"status": res['message']}, 500
        finally:
            logger.debug("Exiting Image Upload POST")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
#get bigcommerce product details
class BcProductDetails(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            req_body = request.args.to_dict()
            res={}
            if req_body['sku']:
               res = webpages_list.get_bc_data(store,req_body)
            return res
        finally:
            logger.debug("Exiting Webpage GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


# Big commerce page operations


class BCPageOperations(APIResource):
    def post_executor(self, request, token_payload, store, token=None):
        try:
            req_body = request.get_json(force=True)
            id = webpages_list.get_bc_page_data(req_body)
            if id:
                return {"status": "ok", "webpage_id": str(id)}, 200
        finally:
            logger.debug("Exiting Dynamic Webpage POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)


class BCWebPages(APIResource):
    def post_executor(self, request, token_payload, store, token=None):
        try:
            req_body = request.get_json(force=True)
            if 'created_by' in req_body:
                api = store_util.get_bc_api_creds(store)
                res = bc.get_bc_webpages(api)        

                # STEP 2 - ITERATE OVER BC WEBPAGES AND CREATE PAGES INTO THE DB
                for page in res:
                    if page['type'] == 'page':                                            
                        webpages_list.create_bc_page(page, req_body,store)

                return {"message": "success"}, 200
            else:
                return {"message": "Please esure that your request body is correct."}, 400
        finally:
            logger.debug("Exiting Dynamic Webpage POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)    
