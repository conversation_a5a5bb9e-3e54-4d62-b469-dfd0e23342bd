from flask import request
import logging
import traceback
from api import APIResource
from api_token import token_service

logger = logging.getLogger()

class Tokens(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering API tokens GET")
        try:
            store_id = store['id']
            res = token_service.get_all(store_id)
            return res, 200
        finally:
            logger.debug("Exiting API tokens GET")
    
    # def put_executor(self, request, token_payload, store):
    #     try:
    #         payload = request.get_json(force=True)
    #         res = token_service.update_data(store, payload)
    #         return res            
    #     finally:
    #         logger.debug("Exiting API tokens PUT")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
            
    # def put(self):
    #         return self.execute_store_request(request, self.put_executor)
