from datetime import datetime
from sqlalchemy import text
import new_pgdb
import new_utils
from new_mongodb import customer_db
from utils import tag_util
from utils.common import calculatePaginationData, parse_json, convert_to_timestamp
from datetime import timezone


def store_customer_tags(store, payload, username, customer_id):
    payload["entity_type"] = "customer"
    customer_id = int(customer_id)
    return tag_util.store_generic_tags(store, payload, username, customer_id)


def get_customer_tags(store, customer_id):
    customer_id = int(customer_id)
    entity_type = "customer"
    return tag_util.get_generic_tags(store, entity_type, customer_id)
