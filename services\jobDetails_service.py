from services import Service
from utils.common import calculatePaginationData
from mongo_db import task_db

class Details(Service):
    def get_all(self, payload, store):
        taskName = payload.get('taskName', None)
        limit = int(payload.get("limit", 5))
        page = int(payload.get("page", 1))

        start_index = (page - 1) * limit

        paginated_data = task_db.fetch_details_by_taskName(taskName, start_index, limit)
        data = calculatePaginationData(paginated_data, page, limit, len(paginated_data))

        return data
