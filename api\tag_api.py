from flask import request
import logging
import traceback
from api import APIResource
from utils import tag_util

logger = logging.getLogger()

class TagAPI(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Tag GET")
        try:
            query_params = request.args.to_dict()
            sort_by = query_params.get("sort_by", None)
            if sort_by == '': sort_by = 'created_at/-1'
            tags = tag_util.get_all_tags(store, sort_by)
            res = {"data": tags}
            return res, 200
        finally:
            logger.debug("Exiting Tag GET")
    
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Tag POST")
        try:
            request_body = request.get_json(silent=True)
            if request_body and "tags" in request_body:
                try:
                    res = tag_util.add_tags(request_body['tags'], store)
                    return {"data": res}, 200
                except ValueError as ve:
                    # Return a 400 response with the error message if duplicate tags exist
                    return {"message": str(ve)}, 409
                
            return {"message": "Invalid request"}, 400
        finally:
            logger.debug("Exiting Tag POST")

    def delete_executor(self, request, token_payload, store):
        logger.debug("Entering Tag DELETE")
        try:
            request_body = request.get_json(silent=True)
            if request_body and "tagId" in request_body:
                tag_util.clear_tag(request_body["tagId"], store)
                return {"message": "Deleted successfully."}, 200
            return {"message": "Invalid request"}, 400
        finally:
            logger.debug("Exiting Tag DELETE")

    def put_executor(self, request, token_payload, store):
        logger.debug("Entering Update Tag PUT")
        try:
            request_body = request.get_json(silent=True)
            if request_body:
                try:
                    tag_util.edit_tag(request_body, store)
                    return {"message": "Updated successfully."}, 200
                except ValueError as ve:
                    # Return a 400 response with the error message if duplicate tags exist
                    return {"message": str(ve)}, 409
            return {"message": "Invalid request"}, 400
        finally:
            logger.debug("Exiting Update Tag PUT")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
    def delete(self):
        return self.execute_store_request(request, self.delete_executor)
    
    def put(self):
        return self.execute_store_request(request, self.put_executor)


class ProductTagAPI(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Tag GET")
        try:
            query_params = request.args.to_dict()
            product_sku = query_params.get("product_sku", None)
            if product_sku:
                tags = tag_util.get_product_tags(product_sku.strip(),store)
                return tags, 200
            return {"message": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Tag GET")
    
    def put_executor(self, request, token_payload, store):
        logger.debug("Entering Tag PUT")
        try:
            request_body = request.get_json(silent=True)
            if request_body:
                tag_util.update_product_tags(request_body, store)
                return {"message": "Updated successfully."}, 200
            return {"message": "Invalid request"}, 400
        finally:
            logger.debug("Exiting Tag PUT")

    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Tag POST")
        try:
            request_body = request.get_json(silent=True)
            if request_body:
                tag_util.add_product_tags(request_body, store)
                return {"message": "Added successfully."}, 200
            return {"message": "Invalid request"}, 400
        finally:
            logger.debug("Exiting Tag POST")

    def delete_executor(self, request, token_payload, store):
        logger.debug("Entering Tag DELETE")
        try:
            req_body = request.get_json(silent=True)
            # if request_body and "skus" in request_body:
            res = tag_util.clear_product_tags(req_body, store)
            if res['status'] == 200:
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Tag DELETE")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
    def put(self):
        return self.execute_store_request(request, self.put_executor)
    
    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
    def delete(self):
        return self.execute_store_request(request, self.delete_executor)
    
class ProductValidate(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering validate parent SKU GET")
        try:
            query_params = request.args.to_dict()
            product_sku = query_params.get("product_sku", None)
            if product_sku:
                res = tag_util.validate_parent_sku(product_sku.strip(),store)
                if res['status'] == 200:
                    return {"message": res['message']}, 200
                else:
                    return {"message": res['message']}, res["status"]
            return {"message": "Invalid request."}, 400
        finally:
            logger.debug("Exiting validate parent SKU GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
    
class ProductsAdd(APIResource):
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering products POST")
        try:
            req_body = request.get_json(force=True)
            if req_body:
                result = tag_util.add_products(req_body, store)
                if result['status'] == 200:
                    return {"message": result['message'], "records_processed": result['records_processed']}, 200
                else:
                    return {"message": result['message'], "records_processed": result['records_processed']}, result['status']
            return {"message": "Invalid request"}, 400
        finally:
            logger.debug("Exiting products POST")
    
    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
class ProductsTagAPI(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Products Tag GET")
        try:
            query_params = request.args.to_dict()
            filter = query_params.get("filter", None)
            page = query_params.get("page", 1)
            limit = query_params.get("limit", 10)
            if page == '': page = 1
            if limit == '': limit = 10
            sort_by = query_params.get("sort_by", None)
            if sort_by == '': sort_by = 'created_at/-1'          
            res = tag_util.get_all_products_with_tag(store, filter, int(page), int(limit), sort_by)
            if res['status'] == 200:                
                return res['data'], 200
            else: 
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Products Tag GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class GenericTags(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Tags GET")
        try:
            query_params = request.args.to_dict()
            entity_type = query_params.get("entity_type", None)
            res = tag_util.get_all_generic_tags(store, entity_type)
            return res['data'], 200
        finally:
            logger.debug("Exiting Tags GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)