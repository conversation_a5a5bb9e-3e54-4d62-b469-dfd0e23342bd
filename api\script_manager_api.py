from flask import request
import logging
import traceback
from api import APIResource
from new_mongodb import store_admin_db
from script_manager import scripts

logger = logging.getLogger()

class Scripts(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering scripts GET")
        try:
            req_body = request.args.to_dict()
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                res = scripts.get_all_scripts(store['id'], req_body)
                if res['status'] == 200:
                    return res['data'], res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Exiting Script GET")
    
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Create Script POST")
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 

                res, status = scripts.create_script(store, req_body, user)
                if status == 200:
                    return {'message': res['message']}, status
                else:
                    return {'message': res['message']}, status
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Create Script POST")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
    def post(self):
        return self.execute_store_request(request, self.post_executor)

class ScriptDetails(APIResource):
    def get_executor(self, request, token_payload, store, script_id):
        logger.debug("Entering Script Details GET")
        try:
            res = scripts.get_script_details(store['id'], script_id)
            if res['status'] == 200:
                return {'data': res['data']}, res['status']
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Script Details GET")

    def put_executor(self, request, token_payload, store, script_id):
        logger.debug("Entering Script Details PUT")
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username)
                res = scripts.update_script(store['id'], script_id, req_body, user)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Script Details PUT")
    
    def delete_executor(self, request, token_payload, store, script_id):
        logger.debug("Entering Script Details DELETE")
        try:
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username)
                res = scripts.delete_script(store['id'], script_id, user)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Script Details DELETE")

    def get(self, script_id):
        return self.execute_store_request(request, self.get_executor, script_id)
    
    def put(self, script_id):
        return self.execute_store_request(request, self.put_executor, script_id)
    
    def delete(self, script_id):
        return self.execute_store_request(request, self.delete_executor, script_id)

class ImportLoyaltyPoints(APIResource):
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Import Loyalty Points POST")
        try:
            # req_body = request.get_json(force=True)
            file = request.files.get('file')
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']
            if username and username != '':
                # user = store_admin_db.fetch_user_by_username(store['id'], username)
                res = scripts.import_loyalty_points(store['id'], file)
                if res['status'] == 200:
                    return {'message': res['message']}, res['status']
                else:
                    return {'message': res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.debug("Exiting Import Loyalty Points POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)