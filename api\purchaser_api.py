from analytics import user_supplier_mapping
from flask import request
import logging
import traceback
from api import APIResource
from analytics.purchaser import purchaser

logger = logging.getLogger()

class NoSoldProductsAnalyticsData(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering No Sold Products Analytics Data GET")
        try:
            query_params = request.args.to_dict()
            res = purchaser.get_no_sold_products_analytics_data(store, query_params)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting No Sold Products Analytics Data GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class PurchaserMappedUsers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Mapped Users GET")
        try:
            res = user_supplier_mapping.get_mapped_users(store)
            
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Mapped Users GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)

class NoSoldProductsAnalyticsDataCsv(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Analytics No Sold Products Data CSV GET")
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            query_params = request.args.to_dict()
            query_params['username'] = username
            res = purchaser.get_no_sold_products_analytics_data_csv(store, query_params)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, res["status"]
        finally:
            logger.debug("Exiting Analytics No Sold Products Data CSV GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)