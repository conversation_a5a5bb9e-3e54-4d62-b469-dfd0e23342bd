from flask import request
import logging
import traceback
from api import APIResource
from notifications import notifications

logger = logging.getLogger()

class Notifications(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering notifications GET")
        try:
            req_body = request.args.to_dict()
            page = req_body.get('page', 1)
            limit = req_body.get('limit', 10)
            notification_filter = req_body.get('notification_filter', None)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                res = notifications.get_notifications(store['id'], page, limit, username, notification_filter)
                if res['status'] == 200:
                    return res['data'], res['status']
                else:
                    return {'data': []}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Notifications GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class NotificationDetails(APIResource):
    def patch_executor(self, request, token_payload, store, notification_id):
        logger.debug("Entering notification details PATCH")
        try:
            payload = request.get_json(force=True)
            is_read = payload.get('is_read', False)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                res = notifications.update_notification(store['id'], notification_id, username, is_read)
                return {"message": res['message']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Notification details PATCH")
    
    def patch(self, notification_id):
        return self.execute_store_request(request, self.patch_executor, notification_id)

class NotificationModules(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering notification modules GET")
        try:
            res = notifications.get_notification_modules(store['id'])
            return res
        finally:
            logger.info(f"Notification modules GET")
        

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class NotificationsGroupByDate(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering notifications group by date GET")
        try:
            req_body = request.args.to_dict()
            page = req_body.get('page', 1)
            limit = req_body.get('limit', 10)
            notification_filter = req_body.get('notification_filter', None)
            timezone = req_body.get('timezone', 'UTC')
            start_date = req_body.get('start_date', None)
            end_date = req_body.get('end_date', None)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                res = notifications.get_notifications_group_by_date(store['id'], username, page, limit, notification_filter, start_date, end_date, timezone)
                if res['status'] == 200:
                    return res['data'], res['status']
                else:
                    return {'data': []}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Notifications GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class StopEmailNotification(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering stop email notification GET")
        try:
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':
                res = notifications.get_email_notification_sections(store['id'], username)
                if res['status'] == 200:
                    return {"data": res['data']}, res['status']
                else:
                    return {'data': []}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Exiting Stop email notification GET")

    def put_executor(self, request, token_payload, store):
        try:
            req_body = request.get_json(force=True)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']
            if username != '':
                res = notifications.update_permission_for_email_notification(store['id'], username, req_body)
                if res['status'] == 200:
                    return {"message": res['message']}, res['status']
                else:
                    return {"message": res['message']}, res['status']
            else:
                return {"message": "Unauthorized."}, 401
        finally:
            logger.debug("Exiting Stop email notification PUT")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
    def put(self):
        return self.execute_store_request(request, self.put_executor)