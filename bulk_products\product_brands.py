from sqlalchemy import text
import pg_db
from datetime import datetime
from utils.common import calculatePaginationData, convert_to_timestamp
import logging
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import IntegrityError
import traceback

logger = logging.getLogger()


def get_product_brands_list(page, limit, filter, status, sort_array=[]):
    response = {
        "status": 400,
    }
    conn = pg_db.get_connection()
    try:
        count_query = """SELECT COUNT(*) FROM bo_bulk_products_brands as bpb"""

        base_query = """SELECT bpb.id, bpb.brand_name, bpb.products_count, bpb.status, bpb.created_by, bpb.created_at, bpb.updated_by, bpb.updated_at, COUNT(bop.bop_id) AS product_count FROM bo_bulk_products_brands AS bpb LEFT JOIN bo_bulk_order_products AS bop ON bop.brand_id = bpb.id """
        
        conditions = []
        if filter:
            conditions.append(f"bpb.brand_name ILIKE '%{filter}%'")

        if status == '':            
            conditions.append(f"bpb.status IN ('inactive', 'active', 'draft', 'deleted')")            
        else:
            conditions.append(f"bpb.status = '{status}'")            

        if len(conditions) > 0:
            base_query += " WHERE " + " AND ".join(conditions) + " GROUP BY bpb.id, bpb.brand_name, bpb.products_count, bpb.status, bpb.created_by, bpb.created_at, bpb.updated_by, bpb.updated_at"
            count_query += " WHERE " + " AND ".join(conditions)

        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ["brand_name", "created_at", "status"]:                
                   base_query += f" ORDER BY {sort_array[0]} {sort_direction}"

        offset = (page - 1) * limit
        if page and limit:
            base_query += f" LIMIT {limit} OFFSET {offset}"

        
        result_count = conn.execute(text(count_query))
        total_count = int(result_count.scalar())
        
        result = conn.execute(text(base_query))

        data = []    
        for row in result.fetchall():
            brands_data = {
                'id': row[0],
                'brand_name': row[1],
                'products_count': row[8],
                'status': row[3],                
                'created_by': row[4],
                'created_at': convert_to_timestamp(row[5]),
                'updated_by': row[6],
                'updated_at': convert_to_timestamp(row[7])                
            }
            data.append(brands_data)

        data = calculatePaginationData(data, page, limit, total_count)
        response['data'] = data
        response['status'] = 200
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response

def create_bulk_product_brand(store, brand_name, username):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        is_unique_name = _check_unique_brand(brand_name)
        if is_unique_name['status'] != 200:
            return is_unique_name
        query = text("""INSERT INTO bo_bulk_products_brands (brand_name, products_count, status, created_at, created_by, updated_at, updated_by) VALUES (:brand_name, :products_count, :status, :created_at, :created_by, :updated_at, :updated_by);""") 
        query = query.params(brand_name=brand_name, products_count=0, status='active', created_at=datetime.now(), created_by=username, updated_at=datetime.now(), updated_by=username)
        res = conn.execute(query)
        
        if res.rowcount > 0:
            response['status'] = 200
            response['message'] = "Data inserted successfully."
        else:
            response['status'] = 400
            response['message'] = "Data insertion failed."
    except IntegrityError as e:
        logger.error(traceback.format_exc())
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 422
            response['message'] = "Brand with same name already exists."
        else:
            response['status'] = 500
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response

def get_product_brand(brand_id):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        query = text("""select * from bo_bulk_products_brands where id = :brand_id;""")
        query = query.params(brand_id=brand_id)
        res = conn.execute(query)
        if res.rowcount > 0:
            for row in res:
                data = {
                    "id": row[0],
                    "brand_name": row[1],
                    "products_count": row[2],
                    "status": row[3],
                    "created_by": row[4],
                    "created_at": convert_to_timestamp(row[6]),
                    "updated_by": row[5],
                    "updated_at": convert_to_timestamp(row[7])
                }
            response['status'] = 200
            response['data'] = data    
        else:
            response['status'] = 400
            response['message'] = "Data not found." 
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response

def delete_bulk_product_brand(brand_id):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        query = text("""select products_count from bo_bulk_products_brands where id = :brand_id;""")
        query = query.params(brand_id=brand_id)
        res = conn.execute(query)
        if res.rowcount > 0:
            for row in res:
                if row[0] > 0:
                    response['status'] = 400
                    response['message'] = "Cannot delete brand with products attached."
                    return response

        query = text("""DELETE FROM bo_bulk_products_brands WHERE id = :brand_id;""") 
        query = query.params(brand_id=brand_id)
        res = conn.execute(query)
        conn.commit()
        if res.rowcount > 0:
            response['status'] = 200
            response['message'] = "Brand deleted successfully."
        else:
            response['status'] = 400
            response['message'] = "Brand deletion failed."
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response

def update_bulk_product_brand(brand_id, payload, username):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        if 'brand_name' in payload:
            is_unique_name = _check_unique_brand(payload['brand_name'], brand_id)
            if is_unique_name['status'] != 200:
                return is_unique_name
        update_brand = {} 
        for field in ['brand_name', 'status']:
            if field in payload:
                update_brand[field] = payload[field]  

        if update_brand:
            set_clause = ", ".join([f"{field} = :{field}" for field in update_brand])
            set_clause = f"SET {set_clause},"
        else:
            set_clause = "SET "  

        if brand_id:                                                    
            query = text(
                    f"""UPDATE bo_bulk_products_brands 
                            {set_clause}
                            updated_by = :updated_by,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE 
                            id = :brand_id;"""
                )          
            params = update_brand.copy()
            params.update({'updated_by': username, 'brand_id': brand_id})
            result = conn.execute(query, params)   
            

            if result.rowcount > 0:
                response['status'] = 200
                response['message'] = "Brand updated successfully."    
            else:
                response['status'] = 400
                response['message'] = "Brand updation failed."     
    except IntegrityError as e:
        logger.error(traceback.format_exc())
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 422
            response['message'] = "Brand with same name already exists."
        else:
            response['status'] = 422
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response

def get_all_brands():
    response = {
        "status": 400,
    }
    conn = pg_db.get_connection()
    try:
        base_query = """SELECT  id, brand_name FROM bo_bulk_products_brands where status = 'active' order by brand_name;"""                 
        result = conn.execute(text(base_query))

        data = []   
        for row in result.fetchall():
            brand_data = {
                'id': row[0],
                'name': row[1]
            }
            data.append(brand_data)
        response['data'] = data
        response['status'] = 200
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response

def _check_unique_brand(brand_name, brand_id=None):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        if brand_id:
            query = text("""SELECT brand_name FROM bo_bulk_products_brands WHERE brand_name ILIKE :brand_name AND id != :brand_id""")
            query = query.params(brand_name=brand_name, brand_id=brand_id)
        else:
            query = text("""SELECT brand_name FROM bo_bulk_products_brands WHERE brand_name ILIKE :brand_name""")
            query = query.params(brand_name=brand_name)
        res = conn.execute(query)
        if res.rowcount > 0:
            response['status'] = 422
            response['message'] = "Brand with same name already exists."
        else:
            response['status'] = 200
            response['message'] = "Brand name is unique."
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response