from flask import json
from bson import json_util, ObjectId
from pymongo.collation import Collation
import datetime
import new_mongodb

# parse json data ...
def _parse_json(data):
    return json.loads(json_util.dumps(data))

def _process_document(obj):
    if obj:
        if '_id' in obj:
            obj['id'] = str(obj['_id'])
            del obj['_id']

        for key, value in obj.items():
            if isinstance(value, ObjectId):
                obj[key] = str(value)
            elif isinstance(value, datetime.datetime):
                obj[key] = int(value.timestamp())
    return obj

def _process_list(data):
    result = []
    if data:
        for _obj in data:
            result.append(_process_document(_obj))
    return result

def _create_reg_ex_query(payload, filterBy, filter, additionalQuery):
    query = {
        "$or": [],
    }

    for i in filterBy:
        query['$or'].append({i: {"$regex": filter, "$options": "i"}},)

    if "type" not in query and "type" in payload:
        query["type"] = payload["type"]

    if "status" in payload:
        if payload["status"] == "active":
            query["is_visible"] = True
        elif payload["status"] == "inactive":
            query["is_visible"] = False
        elif payload["status"] == "out_of_stock":
            query["inventory_level"] = 0     

    query.update(additionalQuery)

    return query

# calculate pagination data for request ...
def calculate_pagination(data, page, limit, total_records):
    page = int(page)
    limit = int(limit)
    total_records = int(total_records)

    # Calculate total number of pages
    last_page = (total_records // limit) + \
        (1 if total_records % limit > 0 else 0)

    # Generate links to other pages (limit to max 5 links)
    base_url = "?page="
    links = []

    # Add first page link
    if page > 1:
        first_link = {
            "url": base_url + str(1),
            "label": "First",
            "active": False,
        }
        links.append(first_link)

   # Add previous page link if it exists
    if page > 1:
        prev_link = {
            "url": base_url + str(page - 1),
            "label": "Previous",
            "active": False,
        }
        links.append(prev_link)

    # Generate up to 5 page links
    if last_page <= 5:
        for p in range(1, last_page + 1):
            link = {
                "url": base_url + str(p),
                "label": str(p),
                "active": p == page,
            }
            links.append(link)
    else:
        if page <= 3:
            for p in range(1, 6):
                link = {
                    "url": base_url + str(p),
                    "label": str(p),
                    "active": p == page,
                }
                links.append(link)
        elif page > last_page - 3:
            for p in range(last_page - 4, last_page + 1):
                link = {
                    "url": base_url + str(p),
                    "label": str(p),
                    "active": p == page,
                }
                links.append(link)
        else:
            for p in range(page - 2, page + 3):
                link = {
                    "url": base_url + str(p),
                    "label": str(p),
                    "active": p == page,
                }
                links.append(link)

    # Add next page link if it exists
    if page < last_page:
        next_link = {
            "url": base_url + str(page + 1),
            "label": "Next",
            "active": False,
        }
        links.append(next_link)

    # Add first page link
    if page < last_page:
        last_link = {
            "url": base_url + str(last_page),
            "label": "Last",
            "active": False,
        }
        links.append(last_link)

    # Generate pagination object
    pagination = {
        "data": data or [],
        "meta": {
            "pagination": {} if int(last_page) == 1 else {
                "first_page_url": base_url + "1",
                "items_per_page": int(limit),
                "last_page": int(last_page),
                "first_page": int(1),
                "links": links,
                "next_page": int(page + 1) if page < last_page else None,
                "page": int(page),
                "prev_page": int(page - 1) if page > 1 else None,
                "total": int(total_records),
            }
        }
    }
    return pagination

def get_paginated_records_updated(store, payload, collection_name, fields, additionalQuery):
    sort = {
        'sort_by': payload['sort_by'] or 'date_created'
    }

    if payload['sort_order'] == 'asc':
        sort['sort_order'] = 1
    else:
        sort['sort_order'] = -1

    limit = int(payload["limit"]) if payload.__contains__("limit") else 10
    page = int(payload["page"]) if payload.__contains__("page") else 1
    skips = payload['skips'] if payload.__contains__('skips') else 0

    query = {}
    if len(payload["filterBy"]):
        query = _create_reg_ex_query(payload, payload["filterBy"], payload['filter'], additionalQuery)

    # Calculate number of records to skip ...
    skips = limit * (page - 1)
    collation = Collation(locale='en', strength=2)

    data = new_mongodb.fetchall_documents_from_storefront_collection(store['id'], collection_name, query, fields).collation(collation).sort(sort['sort_by'], sort['sort_order']).skip(skips).limit(limit)
    # ProcessList ...
    data = _process_list(data)
    
    document_length = new_mongodb.count_documents_storefront_collection(store['id'], collection_name, query)

    return _parse_json(data), document_length, page, limit

        