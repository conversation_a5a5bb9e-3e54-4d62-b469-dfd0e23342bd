import requests
from pymongo import MongoClient

def update_pl(pricelist_id, price_list_name):
    cli = MongoClient("mongodb://localhost:27017")
    db = cli["midwest_prod"]
    cur = db[price_list_name].find({})
    data = []
    api = f"https://api.bigcommerce.com/stores/964anr/v3/pricelists/{pricelist_id}/records"
    headers = {}
    headers["X-Auth-Client"] = "g1k1878e8gown5m9lbr4ca2h9ym2mqu"
    headers["X-Auth-Token"] = "5t4ufv56mgdwd7t8eonmv8ty7hod53q"
    error_data = []
    for pl in cur:
        data.append({"variant_id": pl["variant_id"], "price":pl["price"],"currency":pl["currency"],"retail_price":pl["retail_price"],"map_price":pl["map_price"]})
        if len(data) == 1000:
            res = requests.put(url=api,headers=headers,json=data)
            msg = res.json()
            print(f"{res.status_code} - {msg}")
            if res.status_code != 200:
                error_data.append(data) 
            data = []
            
    if len(data) > 0:
        res = requests.put(url=api,headers=headers,json=data)
        msg = res.json()
        print(f"{res.status_code} - {msg}")
        if res.status_code != 200:
            error_data.append(data) 

