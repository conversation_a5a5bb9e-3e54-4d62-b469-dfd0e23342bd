import math
from flask import json
from bson import json_util
import datetime
from bson import ObjectId
from pymongo import MongoClient
from datetime import datetime, timedelta, timezone
from sqlalchemy import text
from decimal import Decimal
import re
from utils.date_time import month_array
from pymongo.collation import Collation
from PIL import Image, ImageFilter
import logging
import requests
from io import BytesIO
from dateutil import parser
from new_pgdb.analytics_db import AnalyticsDB
from new_mongodb import get_admin_db_client_for_store_id
from dateutil.relativedelta import relativedelta
import new_mongodb

logger = logging.getLogger()

# Default limit and page values ...
limit = 10
skip = 0

def fetch_all(
    db_client, collection_name, limit, page, sort_by="id", query={}, specific_fields={}
):
    # Calculate number of records to skip ...
    skips = limit * (page - 1)

    coll = db_client[collection_name]
    return coll.find(query, specific_fields).sort(sort_by, -1).skip(skips).limit(limit)


# Use for finding document by IDs ...
def find_one_by_id(db_client, collection_name, productId):
    coll = db_client[collection_name]
    return coll.find_one({"id": int(productId)})


# parse json data ...
def parse_json(data):
    return json.loads(json_util.dumps(data))

def conver_to_json(rs, columns):
    results = []
    for row in rs:
        result_dict = dict(zip(columns, row))
        for key, value in result_dict.items():
            if isinstance(value, datetime):
                result_dict[key] = json_serial(value)
        results.append(result_dict)
    return results

def json_serial(obj):
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError("Type not serializable")

def is_valid_email(email):
    # Regular expression for email validation
    email_regex = r'^[\w\.-]+@[\w\.-]+\.\w+$'
    return re.match(email_regex, email) is not None

def extract_email(text):
    email_regex = r'#([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})'
    match = re.search(email_regex, text)
    if match:
        return match.group(1)
    else:
        return None

# calculate pagination data for request ...
def calculatePaginationData(data, page, limit, total_records):
    page = int(page)
    limit = int(limit)
    total_records = int(total_records)

    # Calculate total number of pages
    last_page = (total_records // limit) + \
        (1 if total_records % limit > 0 else 0)

    # Generate links to other pages (limit to max 5 links)
    base_url = "?page="
    links = []

    # Add first page link
    if page > 1:
        first_link = {
            "url": base_url + str(1),
            "label": "First",
            "active": False,
        }
        links.append(first_link)

   # Add previous page link if it exists
    if page > 1:
        prev_link = {
            "url": base_url + str(page - 1),
            "label": "Previous",
            "active": False,
        }
        links.append(prev_link)

    # Generate up to 5 page links
    if last_page <= 5:
        for p in range(1, last_page + 1):
            link = {
                "url": base_url + str(p),
                "label": str(p),
                "active": p == page,
            }
            links.append(link)
    else:
        if page <= 3:
            for p in range(1, 6):
                link = {
                    "url": base_url + str(p),
                    "label": str(p),
                    "active": p == page,
                }
                links.append(link)
        elif page > last_page - 3:
            for p in range(last_page - 4, last_page + 1):
                link = {
                    "url": base_url + str(p),
                    "label": str(p),
                    "active": p == page,
                }
                links.append(link)
        else:
            for p in range(page - 2, page + 3):
                link = {
                    "url": base_url + str(p),
                    "label": str(p),
                    "active": p == page,
                }
                links.append(link)

    # Add next page link if it exists
    if page < last_page:
        next_link = {
            "url": base_url + str(page + 1),
            "label": "Next",
            "active": False,
        }
        links.append(next_link)

    # Add first page link
    if page < last_page:
        last_link = {
            "url": base_url + str(last_page),
            "label": "Last",
            "active": False,
        }
        links.append(last_link)

    # Generate pagination object
    pagination = {
        "data": data or [],
        "meta": {
            "pagination": {} if int(last_page) == 1 else {
                "first_page_url": base_url + "1",
                "items_per_page": int(limit),
                "last_page": int(last_page),
                "first_page": int(1),
                "links": links,
                "next_page": int(page + 1) if page < last_page else None,
                "page": int(page),
                "prev_page": int(page - 1) if page > 1 else None,
                "total": int(total_records),
            }
        }
    }
    return pagination

def processDocument(obj):
    if obj:
        if '_id' in obj:
            obj['id'] = str(obj['_id'])
            del obj['_id']

        for key, value in obj.items():
            if isinstance(value, datetime) or isinstance(value, ObjectId):
                obj[key] = str(value)

    return obj


def processList(data):
    result = []
    if data:
        for _obj in data:
            result.append(processDocument(_obj))
    return result

def processDocumentCategory(obj):
        if obj:
            if 'id' in obj:
                obj['bc_id']=obj['id']
            if '_id' in obj:
                obj['id'] = str(obj['_id'])
                del obj['_id']
        
            for key, value in obj.items():
                if isinstance(value, datetime) or isinstance(value, ObjectId):
                    obj[key] = str(value)

        return obj

def processListCategory( data):
        result = []
        if data:
            for _obj in data:
                result.append(processDocumentCategory(_obj))
        return result

def get_paginated_records(db_client, collection_name, payload, fields):
    def create_reg_ex_query(filterBy, value):
        regex = "^" + value + ".*"

        return {
            filterBy: {
                "$regex": regex,
                "$options": 'i'
            }
        }

    coll = db_client[collection_name]

    query = {}

    if "filterBy" in payload and "filterValue" in payload:
        query = create_reg_ex_query(
            payload["filterBy"], payload['filterValue'])

    limit = int(payload["limit"]) if payload.__contains__("limit") else 10
    page = int(payload["page"]) if payload.__contains__("page") else 1
    skips = payload['skips'] if payload.__contains__('skips') else 0

    # Calculate number of records to skip ...
    skips = limit * (page - 1)

    # Sort by date item last added ...
    sort_by = "date_created"

    query['status'] = {'$in': ['active', 'inactive']}

    data = coll.find(query, fields).sort(sort_by, -1).skip(skips).limit(limit)

    # ProcessList ...
    data = processList(data)

    document_length = coll.count_documents(query)

    return parse_json(data), document_length, page, limit

def get_paginated_records_updated(db_client,collection_name, payload, fields, additionalQuery):
    coll = db_client[collection_name]
    sort = {
        'sort_by': payload.get('sort_by') or 'date_created'
    }

    if payload.get('sort_order') == 'asc':
        sort['sort_order'] = 1
    else:
        sort['sort_order'] = -1

    def create_reg_ex_query(filterBy, filter):
        query = {
            "$or": [],
        }
        if filter:
            for i in filterBy:
                query['$or'].append({i: {"$regex": filter, "$options": "i"}})
        
        # If filter is empty, remove the $or clause to avoid an empty array
        if not query['$or']:
            query = {}

        query.update(additionalQuery)
        return query

    limit = int(payload["limit"]) if payload.__contains__("limit") else 10
    page = int(payload["page"]) if payload.__contains__("page") else 1
    skips = payload['skips'] if payload.__contains__('skips') else 0

    query = create_reg_ex_query(payload["filterBy"], payload['filter']) if len(
        payload["filterBy"]) else {}
    if coll.name == 'users':
       query['status'] = {"$ne": "deleted"}
    if 'customer_id' in payload:
        query['customer_id'] = payload['customer_id']
    # Calculate number of records to skip ...
    skips = limit * (page - 1)
    collation = Collation(locale='en', strength=2)
    data = coll.find(query, fields).collation(collation).sort(
        sort['sort_by'], sort['sort_order']).skip(skips).limit(limit)
    
    # ProcessList ...
    data = processList(data)

    document_length = coll.count_documents(query)

    return parse_json(data), document_length, page, limit

def get_paginated_records_brands(db_client, collection_name, payload, fields, additional_query):
    coll = db_client[collection_name]

    sort_by = payload.get('sortBy', [])
    default_sort_field = payload.get('sort_by') or 'date_created'
    default_sort_order = 1 if payload.get('sort_order') == 'asc' else -1

    limit = int(payload["limit"]) if payload.__contains__("limit") else 10
    page = int(payload["page"]) if payload.__contains__("page") else 1
    skips = limit * (page - 1)

    # Prepare the filtering query
    def create_reg_ex_query(filterBy, filter_value):
        query = {"$or": []}
        if filter_value:
            for field in filterBy:
                query["$or"].append({field: {"$regex": filter_value, "$options": "i"}})
        if not query["$or"]:
            query = {}
        return query

    query = create_reg_ex_query(payload["filterBy"], payload['filter']) if len(payload["filterBy"]) else {}
    query.update(additional_query.get("match", {}))

    pipeline = []

    # Step 1: Add fields dynamically (like priority for brands)
    if "addFields" in additional_query:
        pipeline.append({"$addFields": additional_query["addFields"]})

    # Step 2: Filtering
    pipeline.append({"$match": query})

    # Step 3: Sorting
    sort_stage = {}
    if sort_by:
        for item in sort_by:
            sort_stage[item["field"]] = item["order"]
    else:
        sort_stage[default_sort_field] = default_sort_order

    pipeline.append({"$sort": sort_stage})

    # Step 4: Projection
    if fields:
        pipeline.append({"$project": fields})

    # Step 5: Pagination
    pipeline.append({"$skip": skips})
    pipeline.append({"$limit": limit})

    collation = Collation(locale='en', strength=2)
    data = coll.aggregate(pipeline, collation=collation)
    data = processList(data)

    # Total count
    document_length = coll.count_documents(query)

    return parse_json(data), document_length, page, limit


def get_paginated_records_price_list(db_client, collection_name, payload, fields, additionalQuery):
    coll = db_client[collection_name]
    sort = {
        'sort_by': payload.get('sort_by') or 'date_created'
    }
    sort['sort_order'] = 1 if payload.get('sort_order') == 'asc' else -1
    def create_reg_ex_query(filterBy, filter):
        query = {"$or": []}
        if filter:
            escaped_filter = re.escape(filter)  # Escape special characters
            for i in filterBy:
                regex_pattern = f".*{escaped_filter}.*"
                query['$or'].append({i: {"$regex": regex_pattern, "$options": "i"}})
        # Remove $or clause if empty
        if not query['$or']:
            query = {}
        query.update(additionalQuery)
        return query
    limit = int(payload.get("limit", 10))
    page = int(payload.get("page", 1))
    skips = int(payload.get("skips", 0))
    query = create_reg_ex_query(payload.get("filterBy", []), payload.get("filter", "")) if payload.get("filterBy") else {}
    if coll.name == 'users':
        query['status'] = {"$ne": "deleted"}
    if 'customer_id' in payload:
        query['customer_id'] = payload['customer_id']
    # Calculate number of records to skip ...
    skips = limit * (page - 1)
    collation = Collation(locale='en', strength=2)

    # Check for custom ordering
    product_ids = payload.get("product_ids")
    if product_ids:
        # Aggregation pipeline to preserve order
        pipeline = [
            {"$match": query},
            {"$addFields": {
                "sort_index": {
                    "$indexOfArray": [product_ids, "$parent_product_id"]
                }
            }},
            {"$match": {"sort_index": {"$gte": 0}}},  # Exclude products not in the list
            {"$sort": {"sort_index": 1}},
            {"$skip": skips},
            {"$limit": limit},
            {"$project": fields}
        ]

        data = list(coll.aggregate(pipeline, collation=collation))
        document_length = coll.count_documents(query)
        data = processList(data)
        return parse_json(data), document_length, page, limit
    
    data = coll.find(query, fields).collation(collation).sort(
        sort['sort_by'], sort['sort_order']).skip(skips).limit(limit)
    # ProcessList ...
    data = processList(data)
    document_length = coll.count_documents(query)
    return parse_json(data), document_length, page, limit


def get_six_month_data_using_sku(sku_string, conn, sale_history_months, six_month_flag):
    start_date_str = datetime.now().strftime("%Y-%m-%d")
    current_date = datetime.now()
    six_month_previous_date = (current_date - timedelta(days=current_date.day - 1)
                               ).replace(day=1) - timedelta(int(sale_history_months) * 30)
    days_in_current_month = current_date.day - 1
    day_difference = (
        current_date - six_month_previous_date).days - days_in_current_month + 1
    month_name_array = {}
    # logic to get months for previous six month from current month
    while current_date >= six_month_previous_date:
        month_name = current_date.strftime("%b")
        year_name = current_date.strftime("%Y")
        month_name_array[month_name] = year_name
        current_date -= timedelta(days=30)  # Move to the previous month

    month_name_array = dict(reversed(month_name_array.items()))
    
    
    if six_month_flag:
        # get first day of previous six month
        six_month_previous_date_str = get_first_date_of_month(month_name_array)
    else:
        # get data of before 30 days
        current_date = datetime.now()
        thirty_days_ago = current_date - timedelta(days=day_difference)        
        six_month_previous_date_str = thirty_days_ago.strftime('%Y-%m-%d')


    # get month names
    month_names = generate_meta_months(month_name_array)
    query = f"SELECT variants_sku, product_id, sum(quantity) as quantity, order_month, order_year FROM {AnalyticsDB.get_variants_trend_table()} WHERE variants_sku IN(" + \
        sku_string + ") AND order_date_time >= '" + six_month_previous_date_str + \
        "' and order_date_time <= '" + start_date_str + \
        "' group by variants_sku, product_id, order_month, order_year"
    monthly_rs = conn.execute(text(query))
    monthly_result = parse_json(monthly_rs)

    return monthly_result, month_names, day_difference


def get_month_array_for_meta(sale_history_months):    
    current_date = datetime.now()
    six_month_previous_date = (current_date - timedelta(days=current_date.day - 1)
                               ).replace(day=1) - timedelta(int(sale_history_months) * 30)
    days_in_current_month = current_date.day - 1
    day_difference = (
        current_date - six_month_previous_date).days - days_in_current_month + 1
    month_name_array = {}
    # logic to get months for previous six month from current month
    while current_date >= six_month_previous_date:
        month_name = current_date.strftime("%b")
        year_name = current_date.strftime("%Y")
        month_name_array[month_name] = year_name
        current_date -= timedelta(days=30)  # Move to the previous month

    month_name_array = dict(month_name_array.items())           
    
    # get month names
    month_names = generate_meta_months_new(month_name_array)
    
    return month_names, day_difference

def generate_meta_months_new(month_array):
    months = {}    
    # for index, (m_key, month) in enumerate(month_array.items(), starting_index):
    for index, (m_key, month) in enumerate(month_array.items(), 0):
        key = 'month_' + str(index + 1)
        months[key] = m_key
    return months


def get_first_date_of_month(month_array):
    first_key, first_value = next(iter(month_array.items()))
    int_month = datetime.strptime(first_key, '%b').month
    year = datetime.strptime(first_value, '%Y').year
    first_date = datetime(year, int_month, 1).strftime("%Y-%m-%d")
    return first_date


def generate_meta_months(month_array):
    months = {}
    for index, (m_key, month) in enumerate(month_array.items(), 1):
        key = 'month_' + str(index)
        months[key] = m_key
    return months


def get_table_record_count(conn, table_name, query):
    query = "SELECT COUNT(*) FROM " + table_name + " WHERE " + query
    rs = conn.execute(text(query))
    return int(rs.scalar())


def get_search_query(query_params, field):
    values_list = query_params.split(',')
    trimmed_values_list = [value.strip()
                           for value in values_list]  # Trim each value

    formatted_values = "','".join(trimmed_values_list)
    formatted_values = f"'{formatted_values}'"

    return ' AND ' + field + ' IN(' + formatted_values + ')'

def get_month_names():
    return {
        1: 'Jan',
        2: 'Feb',
        3: 'Mar',
        4: 'Apr',
        5: 'May',
        6: 'Jun',
        7: 'Jul',
        8: 'Aug',
        9: 'Sep',
        10: 'Oct',
        11: 'Nov',
        12: 'Dec',
    }

def get_state_abbreviation(state_name):
    STATE_ABBREVIATIONS = {
        "Alabama": "AL",
        "Alaska": "AK",
        "Arizona": "AZ",
        "Arkansas": "AR",
        "California": "CA",
        "Colorado": "CO",
        "Connecticut": "CT",
        "Delaware": "DE",
        "Florida": "FL",
        "Georgia": "GA",
        "Hawaii": "HI",
        "Idaho": "ID",
        "Illinois": "IL",
        "Indiana": "IN",
        "Iowa": "IA",
        "Kansas": "KS",
        "Kentucky": "KY",
        "Louisiana": "LA",
        "Maine": "ME",
        "Maryland": "MD",
        "Massachusetts": "MA",
        "Michigan": "MI",
        "Minnesota": "MN",
        "Mississippi": "MS",
        "Missouri": "MO",
        "Montana": "MT",
        "Nebraska": "NE",
        "Nevada": "NV",
        "New Hampshire": "NH",
        "New Jersey": "NJ",
        "New Mexico": "NM",
        "New York": "NY",
        "North Carolina": "NC",
        "North Dakota": "ND",
        "Ohio": "OH",
        "Oklahoma": "OK",
        "Oregon": "OR",
        "Pennsylvania": "PA",
        "Rhode Island": "RI",
        "South Carolina": "SC",
        "South Dakota": "SD",
        "Tennessee": "TN",
        "Texas": "TX",
        "Utah": "UT",
        "Vermont": "VT",
        "Virginia": "VA",
        "Washington": "WA",
        "West Virginia": "WV",
        "Wisconsin": "WI",
        "Wyoming": "WY"
    }
    return STATE_ABBREVIATIONS.get(state_name, state_name)

def get_result_data():
    result_data = {}
    result_data['message'] = "No variant data available for the specified product ID."
    result_data['meta'] = [{'month_' + str(i): '' for i in range(1, 5)}]
    return result_data

def paginate_data(total_items, rows, page_number, items_per_page):
    total_pages = (total_items + int(items_per_page) - 1)
    if int(page_number) < 1 or int(page_number) > total_pages:
        raise ValueError("Invalid page number")
    return rows, page_number, total_pages, total_items

def paginate_data_postgres(total_items, rows, page_number, items_per_page): 
    total_pages = (total_items // int(items_per_page)) + \
        (1 if total_items % int(items_per_page) > 0 else 0)
    
    return rows, page_number, total_pages, total_items

#  ========== UNUSED =========== 

# function for parent sku listing in replenishment listing screen...
def get_six_month_data_using_parent_sku(product_skus, conn, sale_history_months, six_month_flag):
    start_date_str = datetime.now().strftime("%Y-%m-%d")
    current_date = datetime.now()
    six_month_previous_date = (current_date - timedelta(days=current_date.day - 1)
                               ).replace(day=1) - timedelta(int(sale_history_months) * 30)
    days_in_current_month = current_date.day - 1
    day_difference = (
        current_date - six_month_previous_date).days - days_in_current_month + 1
    month_name_array = {}
    # logic to get months for previous six month from current month
    while current_date >= six_month_previous_date:
        month_name = current_date.strftime("%b")
        year_name = current_date.strftime("%Y")
        month_name_array[month_name] = year_name
        current_date -= timedelta(days=30)  # Move to the previous month

    month_name_array = dict(reversed(month_name_array.items()))
    
    if six_month_flag:
        # get first day of previous six month
        six_month_previous_date_str = get_first_date_of_month(month_name_array)
    else:
        # get data of before 30 days
        current_date = datetime.now()
        thirty_days_ago = current_date - timedelta(days=day_difference)        
        six_month_previous_date_str = thirty_days_ago.strftime('%Y-%m-%d')
    
    # get month names
    month_names = generate_meta_months(month_name_array)

    query = (
        "SELECT parent_sku, sum(quantity) as sold_quantity, order_month, order_year "
        f"FROM {AnalyticsDB.get_products_trend_table()} "
        "WHERE parent_sku IN (" + product_skus + ") "
        "AND order_date_time >= '" + six_month_previous_date_str + "' "
        "AND order_date_time <= '" + start_date_str + "' "
        "GROUP BY parent_sku, order_month, order_year"
    )    

    monthly_rs = conn.execute(text(query))

    def convert_to_json(rs, columns):
        results = []
        for row in rs:
            result_dict = {}
            for key, value in zip(columns, row):
                if isinstance(value, Decimal):
                    if (key == 'order_month'):
                        result_dict[key] = month_array[int(value) - 1]
                    else:
                        result_dict[key] = int(value)  # Convert Decimal to float
                else:
                    result_dict[key] = value
            results.append(result_dict)
        return results


    monthly_result = convert_to_json(monthly_rs, ['parent_sku', 'sold_quantity', 'order_month', 'order_year'])

    return monthly_result, month_names, day_difference

def get_month_array_for_meta(sale_history_months):    
    current_date = datetime.now()
    six_month_previous_date = (current_date - timedelta(days=current_date.day - 1)
                               ).replace(day=1) - timedelta(int(sale_history_months) * 30)
    days_in_current_month = current_date.day - 1
    day_difference = (
        current_date - six_month_previous_date).days - days_in_current_month + 1
    month_name_array = {}
    # logic to get months for previous six month from current month
    while current_date >= six_month_previous_date:
        month_name = current_date.strftime("%b")
        year_name = current_date.strftime("%Y")
        month_name_array[month_name] = year_name
        # current_date -= timedelta(days=30)  # Move to the previous month
        current_date -= relativedelta(months=1)

    month_name_array = dict(month_name_array.items())           
    
    # get month names
    month_names = generate_meta_months_new(month_name_array)
    
    return month_names, day_difference

def get_day_array_for_meta():
    current_date = datetime.now()
    start_date = current_date.replace(day=1)

    day_labels = {}
    count = 1

    while start_date <= current_date:
        formatted_day = start_date.strftime("%d / %B %Y")
        day_labels[f"day_{count}"] = formatted_day
        start_date += timedelta(days=1)
        count += 1

    return day_labels



def get_product_id_from_sku(sku_string, conn, results):

    query = f"SELECT variants_sku, product_id FROM {AnalyticsDB.get_variants_trend_table()}  WHERE variants_sku IN (" + \
        sku_string + ") GROUP BY variants_sku, product_id"

    query_results = conn.execute(text(query))
    query_results = parse_json(query_results)
    for res in results:
        res['product_id'] = ''
        for qRes in query_results:
            if str(res['sku']) == qRes[0]:
                res['product_id'] = qRes[1]

    return results


def get_six_month_data_using_product_id(product_ids, conn):
    start_date_str = datetime.now().strftime("%Y-%m-%d")
    current_date = datetime.now()
    # six_month_end_date = datetime.now() - timedelta(days=6*30)
    six_month_previous_date = (current_date - timedelta(days=current_date.day - 1)
                               ).replace(day=1) - timedelta(6 * 30)
    month_name_array = {}
    # logic to get months for previous six month from current month
    while current_date >= six_month_previous_date:
        month_name = current_date.strftime("%b")
        year_name = current_date.strftime("%Y")
        month_name_array[month_name] = year_name
        # month_name_array.append(month_name)
        current_date -= timedelta(days=30)  # Move to the previous month

    month_name_array = dict(reversed(month_name_array.items()))
    # get first day of previous six month
    six_month_previous_date_str = get_first_date_of_month(
        month_name_array)
    # get month names
    month_names = generate_meta_months(month_name_array)
    query = f"SELECT variants_sku, sum(quantity) as quantity, order_month, order_year FROM {AnalyticsDB.get_variants_trend_table()} WHERE product_id IN(" + \
        product_ids + ") AND order_date_time >= '" + six_month_previous_date_str + \
        "' and order_date_time <= '" + start_date_str + \
        "' group by variants_sku, order_month, order_year"
    monthly_rs = conn.execute(text(query))
    monthly_result = parse_json(monthly_rs)

    return monthly_result, month_names


def get_first_date_of_month(month_array):
    first_key, first_value = next(iter(month_array.items()))
    int_month = datetime.strptime(first_key, '%b').month
    year = datetime.strptime(first_value, '%Y').year
    first_date = datetime(year, int_month, 1).strftime("%Y-%m-%d")
    return first_date


def generate_meta_months(month_array):
    months = {}
    for index, (m_key, month) in enumerate(month_array.items(), 1):
        key = 'month_' + str(index)
        months[key] = m_key
    return months

def generate_meta_months_new(month_array):
    months = {}    
    # for index, (m_key, month) in enumerate(month_array.items(), starting_index):
    for index, (m_key, month) in enumerate(month_array.items(), 0):
        key = 'month_' + str(index + 1)
        months[key] = m_key
    return months


def get_table_record_count(conn, table_name, query):
    query = "SELECT COUNT(*) FROM " + table_name + " WHERE " + query
    rs = conn.execute(text(query))
    return int(rs.scalar())


def get_search_query(query_params, field):
    values_list = query_params.split(',')
    trimmed_values_list = [value.strip()
                           for value in values_list]  # Trim each value

    formatted_values = "','".join(trimmed_values_list)
    formatted_values = f"'{formatted_values}'"

    return ' AND ' + field + ' IN(' + formatted_values + ')'

def get_month_names():
    return {
        1: 'Jan',
        2: 'Feb',
        3: 'Mar',
        4: 'Apr',
        5: 'May',
        6: 'Jun',
        7: 'Jul',
        8: 'Aug',
        9: 'Sep',
        10: 'Oct',
        11: 'Nov',
        12: 'Dec',
    }

def get_result_data():
    result_data = {}
    result_data['message'] = "No variant data available for the specified product ID."
    result_data['meta'] = [{'month_' + str(i): '' for i in range(1, 5)}]
    return result_data

def paginate_data(total_items, rows, page_number, items_per_page):
    total_pages = (total_items + int(items_per_page) - 1)
    if int(page_number) < 1 or int(page_number) > total_pages:
        raise ValueError("Invalid page number")
    return rows, page_number, total_pages, total_items

def paginate_data_postgres(total_items, rows, page_number, items_per_page): 
    total_pages = (total_items // int(items_per_page)) + \
        (1 if total_items % int(items_per_page) > 0 else 0)
    
    return rows, page_number, total_pages, total_items

def get_order_status_name(status_id):
    status_mapping = {
        0: "Incomplete",
        1: "Pending",
        2: "Shipped",
        3: "Partially Shipped",
        4: "Refunded",
        5: "Cancelled",
        6: "Declined",
        7: "Awaiting Payment",
        8: "Awaiting Pickup",
        9: "Awaiting Shipment",
        10: "Completed",
        11: "Awaiting Fulfillment",
        12: "Manual Verification Required",
        13: "Disputed",
        14: "Partially Refunded"
    }
    
    if status_id in status_mapping:
        return status_mapping[status_id]
    else:
        return ""

def resize_image(image_path, new_width):
    # Open the image using PIL
    image = Image.open(image_path)
    
    # Calculate the aspect ratio to maintain proportions
    width, height = image.size
    aspect_ratio = height / width

    # Resize the image with width fixed at new_width and height adjusted to maintain aspect ratio
    new_height = int(new_width * aspect_ratio)
    resized_image = image.resize((new_width, new_height), Image.LANCZOS)

    return resized_image

def convert_to_timestamp(date_input):
    if date_input:
        # If the date is a string, try to parse it with flexible date parsing
        if isinstance(date_input, str):
            try:
                date_input = parser.parse(date_input)  # Parses a wide variety of date formats
            except (ValueError, TypeError):
                print("Invalid date string format.")
                return None

        # If it's already a datetime object, proceed
        if isinstance(date_input, datetime):
            # Ensure it's timezone-aware by setting UTC if no timezone info is present
            if date_input.tzinfo is None:
                date_input = date_input.replace(tzinfo=timezone.utc)
            
            # Convert to Unix timestamp
            return int(date_input.timestamp())
    # If input is neither string nor datetime, return None
    return None

def convert_to_date_string(timestamp):
    if isinstance(timestamp, (int, float)):
        try:
            # Convert the timestamp to a datetime object in UTC
            date_object = datetime.fromtimestamp(timestamp, tz=timezone.utc)
            # Format the datetime object into a date string (e.g., "YYYY-MM-DD")
            return date_object.strftime("%Y-%m-%d")
        except (ValueError, OverflowError):
            return None
    return None

def calculate_working_hours(start_date, end_date, work_hours_per_day=8):
    if not start_date or not end_date:
        return 0
    # Convert strings to datetime objects if they are not already
    if isinstance(start_date, str):
        start_date = datetime.strptime(start_date, '%Y-%m-%d %H:%M:%S')
    if isinstance(end_date, str):
        end_date = datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S')
    
    # Ensure start_date is earlier than end_date
    if start_date > end_date:
        return 0  # No hours if the range is invalid

    # Calculate total days and remainder time
    total_days = (end_date.date() - start_date.date()).days
    start_time_remaining = timedelta(hours=(24 - start_date.hour)) if total_days > 0 else (end_date - start_date)
    end_time_remaining = timedelta(hours=end_date.hour) if total_days > 0 else timedelta(0)

    # Calculate working hours
    total_working_hours = total_days * work_hours_per_day  # Hours for full days
    if total_days > 0:
        # Add hours from the partial first and last days
        total_working_hours += min(work_hours_per_day, start_time_remaining.total_seconds() / 3600)
        total_working_hours += min(work_hours_per_day, end_time_remaining.total_seconds() / 3600)

    return total_working_hours

def convert_timedelta_to_hhmmss(td):
    """Converts a timedelta object to HH:MM:SS format."""
    total_seconds = int(td.total_seconds())
    hours, remainder = divmod(total_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{hours:02}:{minutes:02}:{seconds:02}"


def parse_time_format(time_str):
    """Converts a string like '1w 2d 3h 4m' to a timedelta object."""
    time_units = {
        'w': 5 * 24 * 60 * 60,  # weeks to seconds
        'd': 8 * 60 * 60,      # days to seconds
        'h': 60 * 60,           # hours to seconds
        'm': 60                 # minutes to seconds
    }
    
    total_seconds = 0
    matches = re.findall(r'(\d+)\s*(w|d|h|m)', time_str)
    for value, unit in matches:
        total_seconds += int(value) * time_units[unit]
    
    return timedelta(seconds=total_seconds)

def convert_time_format(td, call_from_report=False):
    if td is None:
        return None
    if isinstance(td, str):
        try:
            hours, minutes, seconds = map(int, td.split(":"))
            td = timedelta(hours=hours, minutes=minutes, seconds=seconds)
        except ValueError:
            return "Invalid time format"
    total_seconds = td.total_seconds()
    
    if call_from_report:
        # Convert directly to hours and minutes only
        hours, remainder = divmod(total_seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        if total_seconds == 0:
            return "0h 0m"  # Special case for 00:00:00
        elif hours > 0:
            return f"{int(hours)}h {int(minutes)}m"  # Always show minutes even if 0
        else:
            return f"{int(minutes)}m"  # Only minutes if no hours
    
    weeks, remainder = divmod(total_seconds, 5 * 8 * 3600)
    days, remainder = divmod(remainder, 8 * 3600)
    hours, remainder = divmod(remainder, 3600)
    minutes, seconds = divmod(remainder, 60)

    time_str = ""
    if weeks > 0:
        time_str += f"{int(weeks)}w "
    if days > 0:
        time_str += f"{int(days)}d "
    if hours > 0:
        time_str += f"{int(hours)}h "
    if minutes > 0:
        time_str += f"{int(minutes)}m "
    return time_str.rstrip() if time_str else "0m"

def fetch_static_price_lists(store_id):
    db = get_admin_db_client_for_store_id(store_id)
    # Desired static price list order
    custom_sort_order = [53, 52, 51, 100054, 100055]

    static_price_lists = []
    try:
        price_list_distributors = db["price_list_distributors"]
        # Use MongoDB's `$expr` to sort by the custom order
        pipeline = [
            {"$match": {"status": "active"}},
            {"$addFields": {
                "sort_index": {
                    "$indexOfArray": [custom_sort_order, "$id"]
                }
            }},
            {"$sort": {"sort_index": 1}},
            {"$project": {"id": 1, "title": 1, "_id": 0}}
        ]
        cursor = price_list_distributors.aggregate(pipeline)
        static_price_lists = [{"id": doc["id"], "name": doc["title"], "active": True} for doc in cursor]
    except Exception as e:
        print(f"Error fetching price list distributors: {e}")

    return static_price_lists

def format_seconds(seconds):
    if seconds % 3600 == 0:
        return f"{seconds // 3600}h"
    elif seconds % 60 == 0:
        return f"{seconds // 60}m"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        if hours > 0:
            return f"{hours}h {minutes}m" if minutes > 0 else f"{hours}h"
        return f"{minutes}m"
    

def get_product_id_by_sku_from_mongo(store, sku):
    try:
        # Get MongoDB connection
        store_db = new_mongodb.get_store_db_client_for_store_id(store['id'])
        products_collection = store_db['products']
        # Query to find product by variant SKU
        query = {
            "variants.sku": sku
        }
        # Project only the fields we need
        projection = {
            "id": 1,
            "variants.id": 1,
            "variants.sku": 1
        }
        # Find the product
        product = products_collection.find_one(query, projection)
        if product:
            # Find the specific variant with matching SKU
            for variant in product.get('variants', []):
                if str(variant.get('sku')) == str(sku):
                    product_id = product.get('id', 0)
                    variant_id = variant.get('id', 0)
                    return product_id, variant_id
            # If we reach here, product was found but variant SKU didn't match (shouldn't happen)
            return 0, 0
        else:
            return 0, 0
    except Exception as e:
        error_message = str(e)
        logger.error(f"Error in get_product_id_by_sku_from_mongo: {error_message}")
        return 0, 0