from datetime import datetime, timezone
import os
from bson import ObjectId
from mongo_db import user_db, product_db
from new_mongodb import cms_db, StoreAdminDBCollections, count_documents_admin_collection, fetchall_documents_from_admin_collection
import new_pgdb
import new_utils
from utils import product_util
from utils.common import parse_json, processList, processListCategory
from pymongo.collation import Collation
from fields.cms_fields import cms_fields
from werkzeug.utils import secure_filename
from flask import send_file, make_response
import logging
from bs4 import BeautifulSoup

logger = logging.getLogger()

ALLOWED_EXTENSIONS = set(['png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov', 'wmv', 'mkv'])

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def change_file_name(filename):
    rendom = str(round(datetime.now(timezone.utc).timestamp()))
    if ('.' in filename):
        ext = filename.rsplit('.', 1)[1].lower()
        fname = rendom + '.' + ext
        return fname

def _get_paginated_records_updated_category(store, payload, fields, additionalQuery):
        sort = {
            'sort_by': payload['sort_by'] or 'date_created'
        }
        if payload['sort_order'] == 'asc':
            sort['sort_order'] = 1
        else:
            sort['sort_order'] = -1

        def create_reg_ex_query(filterBy, filter):
            query = {
                "$or": [],
            }

            for i in filterBy:
                query['$or'].append({i: {"$regex": filter, "$options": "i"}},)

            if "type" not in query and "type" in payload:
                 query["type"] = payload["type"]

            if "status" in payload:
               if payload["status"] == "active":
                  query["is_visible"] = True
               elif payload["status"] == "inactive":
                  query["is_visible"] = False
               elif payload["status"] == "out_of_stock":
                  query["inventory_level"] = 0              
            query.update(additionalQuery)
            return query

        limit = int(payload["limit"]) if payload.__contains__("limit") else 10
        page = int(payload["page"]) if payload.__contains__("page") else 1
        skips = payload['skips'] if payload.__contains__('skips') else 0

        query = create_reg_ex_query(payload["filterBy"], payload['filter']) if len(
            payload["filterBy"]) else {}
        query['parent_id'] = 0

        # Calculate number of records to skip ...
        skips = limit * (page - 1)
        collation = Collation(locale='en', strength=2)
        data = fetchall_documents_from_admin_collection(store['id'], StoreAdminDBCollections.CMS_COLLECTION, query, fields) \
                 .collation(collation).sort(sort['sort_by'], sort['sort_order']).skip(skips).limit(limit)
        # ProcessList ...
        data = processListCategory(data)
       
        document_length = count_documents_admin_collection(store['id'], StoreAdminDBCollections.CMS_COLLECTION, query)

        # document_length = self.repository.count_documents(query)

        return parse_json(data), document_length, page, limit

def addCdnToImageUrl( cdn_baseurl, activeVersion):
        if 'components' in activeVersion:           
            for component in activeVersion['components']:
                if 'variant' in component:
                    variant = component['variant']
                    if 'config' in variant:
                        config = variant['config']
                        if 'image_url' in config:
                            if config['image_url'] != '':
                                config['image_url'] = cdn_baseurl + '/categories' + config['image_url'].replace('/categories/images', '') 
                        if 'mobile_image_url' in config:
                            if config['mobile_image_url'] != '':
                                config['mobile_image_url'] = cdn_baseurl + '/categories' + config['mobile_image_url'].replace('/categories/images', '')                           
                        elif 'slider' in  config and 'side_images' in config:                            
                            sliders = config['slider']
                            side_images = config['side_images']
                            for slider in sliders:
                                if 'image_url' in slider:
                                    if slider['image_url'] != '':
                                        slider['image_url'] = cdn_baseurl + '/categories' + slider['image_url'].replace('/categories/images', '')

                            for side_image in side_images:
                                if 'image_url' in side_image:
                                    if side_image['image_url'] != '':
                                        side_image['image_url'] = cdn_baseurl + '/categories' + side_image['image_url'].replace('/categories/images', '')
                        elif 'banners' in  config:
                            banners = config['banners']
                            for banner in banners:
                                if 'image_url' in banner:
                                    if banner['image_url'] != '':
                                        banner['image_url'] = cdn_baseurl + '/categories' + banner['image_url'].replace('/categories/images', '')   
                                if 'mobile_image_url' in banner:
                                    if banner['mobile_image_url'] != '':
                                        banner['mobile_image_url'] = cdn_baseurl + '/categories' + banner['mobile_image_url'].replace('/categories/images', '')                          
                        elif 'logos' in config:
                            logos = config['logos'] 
                            for logo in logos:
                                if 'image_url' in logo:
                                    if logo['image_url'] != '':
                                        logo['image_url'] = cdn_baseurl + '/categories' + logo['image_url'].replace('/categories/images', '')

        return activeVersion             
   

def get_categories_data(store,body, cdn_baseurl):        
    result = []        
    conn = new_pgdb.get_connection(store['id'])
    try:
        if 'category_id' in body:
            res = {}
            category_id = body['category_id']
            category = cms_db.get_category_by_id(store, category_id)
            if 'preview_state' in category and category['preview_state']:               
                preview_state = category['preview_state']
            else:
                preview_state = category['default_layout']

            preview_state =  addCdnToImageUrl(cdn_baseurl, preview_state)

            res['id'] = category['id']
            res['name'] = category['name']
            res['created_at'] = category['created_at']
            res['updated_at'] = category['updated_at']
            res['url'] = category['url']
            res['is_customers_only'] = category['is_customers_only']
            res['versions'] = preview_state

            result.append(res)
            return result
        else:            
            body['filterBy'] = ['name']            
            categories, total_data_length, paginationPage, limit = _get_paginated_records_updated_category(store,body, cms_fields,'')
            categories_db = cms_db.get_all_categories(store)
            nameResult = []
            for category in categories_db:
                test = {}
                test['Name'] = category['name']
                test['URL'] = category['url']
                nameResult.append(test)
            for category in categories:
                product_count=product_util.get_products_count_for_category(conn, category['bc_id'])
                res = {}
                versions = category['versions']
                if (len(versions) == 0):
                    activeVersion = category['default_layout']
                else:
                    for version in versions:
                        if (version['status'] == 'active'):
                            activeVersion = version
                            break
                
                activeVersion =  addCdnToImageUrl(cdn_baseurl, activeVersion)
                
                res['id'] = category['id']
                res['category_id']=category['bc_id']
                res['name'] = category['name']
                res['created_at'] = category['created_at']
                res['updated_at'] = category['updated_at']
                res['url'] = category['url']
                res['status'] = category['status']
                res['is_visible'] = category['is_visible']
                res['parent_id'] = category['parent_id']
                res['views'] = category['views']
                res['sort_order'] = category['sort_order']
                res["type"] = category["type"]
                res['versions'] = activeVersion
                res['is_customers_only'] = category['is_customers_only']
                res['has_sub_child']=category['has_sub_child']
                res['product_count']=product_count

                result.append(res)
            data = new_utils.calculate_pagination(
                result, paginationPage, limit, total_data_length)
            data['meta']['name_info'] = nameResult
            return data  
    except Exception as e:
        logger.error(f"Error in get_categories_data: {e}")
        return []
    finally:
        if conn:
            conn.close()
        
def get_child_data(store, parent_id, cdn_baseurl): 
    conn = new_pgdb.get_connection(store['id'])
    try:
        categories=cms_db.get_categories_by_parent_id(store, parent_id)
        for category in categories:
            product_count=product_util.get_products_count_for_category(conn, category['bc_id'])
            category['product_count']=product_count
            category['category_id']=category['bc_id']
        if categories:
            return categories
        else:
            return {}
    except Exception as e:
        logger.error(f"Error in get_child_data: {e}")
        return {}
    finally:
        if conn:
            conn.close()
        
def create_list(body):
        response = {
            "status": 400
        }
        url = body['name']
        # url = '/pages/' + url.replace(" ", "_").lower()
        url = url.replace(" ", "_").lower()
        # isUniqueName = self.checkForUniquePageName(body['name'])
        if True:
            body["created_at"] = int(datetime.now(timezone.utc).timestamp())
            body["updated_at"] = ""
            body["type"]=body["type"]
            body["url"] = url
            body["default_layout"] = {}
            body["versions"] = []
            body["preview_state"] = {}
            body['is_customers_only'] = False
            # id = super().create(body)
            response['message'] = "Page created successfully"
            response['status'] = 200
        else:
            response['message'] = "The provided page Name has already been matched with other pages. Please provide a different page Name."
            response['status'] = 409

        return response
        # data['store_id'] = store['id']
        
        # # check if permission is already exits in DB.
        # permission = self.get_permission_by_store_id(store)

        # if permission == None:
        #     id = super().create(data)
        #     if id:
        #         return {"status": "success" }, 201
        # else:
        #     return {'message': '1 permission object already attched with the store name: ' + store['name']}, 400

def set_version(store, payload, id=None):
        response = {
            "status": 400            
        }
        created_by = {}
        if 'created_by' in payload:
            user = user_db.fetch_user_by_username(payload['created_by'])        
            created_by = {
                "user_id": str(user['_id']),
                "user_name": user['name']
            } 
        category = cms_db.get_category_by_bc_id(store, id)
        if category:  
            category_id = category['_id']                                                                            
            totalVersions = len(category['versions'])
            versions = category['versions']

            if totalVersions > 0:
                lastVersion = category['versions'][-1]['version']
                for version in versions:
                    version['status'] = 'inactive'                    
            else:
                lastVersion = 0

            if (totalVersions >= 10):
                category['versions'].pop(0)

            payload['created_by'] = created_by
            payload['created_at'] = int(datetime.now(timezone.utc).timestamp())
            payload['version'] = lastVersion + 1
            versions.append(payload)
            update_obj={                                                                            
                          "versions": category['versions'],
                          "updated_at":  int(datetime.now(timezone.utc).timestamp())
                      }
            cms_db.update(store, {"_id": ObjectId(category_id)}, {"$set": update_obj})
                   
            response['message'] = "changes saved sucessfuly"
            response['status'] = 200
        else:
            response['message'] = "Category not found."
            response['status'] = 404
        return response

def get_cms_versions(store, id=None):
        result = cms_db.get_category_by_id(store, id)
        list = {}
        versionArray = []
        if result is not None:
            if (len(result['versions']) > 0):
               versions = result['versions']
               for version in versions:
                  versionArray.append(version['version'])

               list['webpageVersions'] = versionArray

        return list  

def update_customer_only_flag(store, body, id=None):
        response = {
            "status": 400
        }
        if id:
            update_obj={                                                                             
                          "is_customers_only": body['is_customers_only'],                                                                             
                          "updated_at":  int(datetime.now(timezone.utc).timestamp())
                      }
            cms_db.update(store, {"_id": ObjectId(str(id))}, {"$set": update_obj})
            # id = super().update_one({"_id": ObjectId(str(id))}, {"$set":
            #                                                                 {                                                                             
            #                                                                     "is_customers_only": body['is_customers_only'],                                                                             
            #                                                                     "updated_at":  int(datetime.utcnow().timestamp())
            #                                                                 }
            #                                                                 })
            response['message'] = "Category Updated successfully"
            response['status'] = 200        

        return response

def get_category(store, category_id=None):
        result = {}
        category= cms_db.get_category_by_id(store, category_id)
        # category = super().find_one({"_id": ObjectId(str(category_id))})
        if (len(category['versions']) == 0):
            activeVersion = category['default_layout']
        else:
            versions = category['versions']
            for version in versions:
                if (version['status'] == 'active'):
                    activeVersion = version
                    break

        result['id'] = category['id']
        result['name'] = category['name']
        result['created_at'] = category['created_at']
        result['updated_at'] = category['updated_at']
        result['url'] = category['url']
        result['is_customers_only'] = category['is_customers_only']
        result['versions'] = activeVersion

        return result

def get_cms_versionData(store, body, id=None):
        list = {}
        result= cms_db.get_category_by_id(store, id)
        # result = super().find_one({"_id": ObjectId(id)})
        if result is not None:
           if (len(result['versions']) == 0):
               activeVersion = result['default_layout']
           else:
               versions = result['versions']
               for version in versions:
                   if (version['version'] == int(body['version'])):
                      activeVersion = version
                      break

           list['id'] = result['id']
           list['name'] = result['name']
           list['created_at'] = result['created_at']
           list['updated_at'] = result['updated_at']
           list['url'] = result['url']
           list['is_customers_only'] = result['is_customers_only'] 
           list['versions'] = activeVersion

        return list

def setImage(body):
        response = {
            "status": 400
        }

        if not os.path.exists('images/categories/images'):
            os.makedirs('images/categories/images')

        file = body['image']
        UPLOAD_FOLDER = os.path.join('images/categories/images')

        if (file.filename == ''):
            response['message'] = "No image selected for uploading"
            response['status'] = 500

        if (file and allowed_file(file.filename)):
            newName = change_file_name(file.filename)
            fname = secure_filename(newName)
            file.save(os.path.join(UPLOAD_FOLDER, fname))
            base_path = os.path.join(os.path.abspath(
                os.getcwd()), UPLOAD_FOLDER, newName)
            
            if '/app/images' in base_path:
                base_path = base_path.replace('/app/images', '')             
            response['message'] = base_path
            response['status'] = 200
        else:
            response['message'] = "Allowed image types are -> png, jpg, jpeg, gif, mp4, avi, mov, wmv, mkv"
            response['status'] = 500

        return response

def getImage(body):
    url = './images/categories/images/' + body['image']
    if not os.path.exists(url):
        return make_response({'error': 'Image not found'}, 404)

    return send_file(url, as_attachment=True)

def check_all_categories( category_arr, store):
        db_categories= cms_db.get_all_categories(store)
        for category in db_categories:

            if int(category['id']) not in category_arr:
                cms_db.delete_category_by_id(store, category['id'])
                product_db.delete_category_by_id(store, category['id'])

def create_bc_category( category, req_body,store):
        
        created_by = {}
        if 'created_by' in req_body:
            user = user_db.fetch_user_by_username(req_body['created_by'])        
            created_by = {
                "user_id": str(user['_id']),
                "user_name": user['name']
            }           
        # Find in DB if available.
        db_category = cms_db.get_category_by_bc_id(store, category['category_id'])
        # db_category = super().find_one({"id": int(category['id'])})    
        description = category["description"] 
        # if '%%GLOBAL_ShopPathSSL%%' in description or '%%GLOBAL_CdnStorePath%%' in description or '<a href="%%GLOBAL_ShopPathSSL%%' in description or '<a href="%%GLOBAL_CdnStorePath%%' in description:                                        
        #     print(category["id"], '  ||  ', category["name"], '  ||  ', category["custom_url"]["url"])

        # if '/searchresults.html?search_query=' in description:
        #     print(category["id"], '  ||  ', category["name"], '  ||  ', category["custom_url"]["url"])         
        
        description = _parse_description(description)
        
        # If webpage not exists then create
        if not db_category:            
            generate_category(store, category, description,created_by)

        # If exists then check if it's modified or not if not then replace content
        if db_category:                  
            update_category(store, category, description,created_by) 
            getNavigationForCategories(category,store)

def getNavigationForCategories(category,store):
            navigations=cms_db.get_sub_nav(store)
            navigations=processList(navigations)
            for navigation in navigations:
                navigation['navigation_id']=ObjectId(navigation['navigation_id'])
                is_updated=False
                for data in navigation['navigations']:
                    if data['type'] == 'Categories' and data['id'] == 'Categories'+'_'+str(category['category_id']) :
                        is_updated=True
                        data['url']=category['url']['path']
                if is_updated:
                   cms_db.update_sub_nav_by_id(store,navigation)
            return
      
def generate_category(store, category, description,created_by):
            new_list={} 
            seo_details={}
            # has_child_data = cms_db.has_categories_with_parent_id(store, category["category_id"])
            seo_details["page_name"]=category["name"]
            seo_details["page_url"]=category["url"]["path"]
            seo_details["meta_title"]=category["page_title"]
            seo_details["meta_description"]=category["meta_description"]           
            
            new_list["id"]=category["category_id"]
            new_list["parent_id"] = category["parent_id"]
            new_list["name"]=category["name"]
            new_list["type"]="category"
            new_list["views"] = category["views"]
            new_list["sort_order"] = category["sort_order"]
            new_list["is_visible"] = category["is_visible"]              
            new_list["url"]=category["url"]["path"]
            new_list["tree_id"] = category["tree_id"]
            new_list["is_customers_only"]= False                                        
            new_list["default_layout"] = {}
            new_list["versions"]= [
                { 
                "name":category["name"],
                "status":"active", 
                "class":"",
                "type": "category",
                "seo_details":seo_details,
                "components":[
                    {
                    "id": 1,
                    "name": "HTML Block",
                    "code": "html_block",  
                    "variant": {
                        "id": "1",
                        "name": "HTML Block",
                        "admin_layout": "style1",
                        "class": [],                          
                        "config": {
                            "data": description    
                                }
                    },  
                    "children": []
                    }
                ],
                "created_at":int(datetime.now(timezone.utc).timestamp()),
                "created_by": created_by,                                                                              
                "version":0,
            } ]
            new_list["preview_state"] = {} 
            new_list["updated_at"]=""
            new_list["created_at"]=int(datetime.now(timezone.utc).timestamp())
            new_list["status"] = "active"
            new_list["has_sub_child"] = False

            return cms_db.create(store, new_list)

def update_category(store, category, description,created_by):     
            update_obj = {
                'name': category['name'],
                'parent_id':  category["parent_id"],
                'type': 'category',
                'views': category["views"],
                'sort_order': category["sort_order"],
                'is_visible': category['is_visible'],
                'url': category["url"]["path"],  
                'tree_id': category["tree_id"],
                'is_customers_only': False,                              
                "versions": [{
                    'name': category['name'],
                    'status': 'active',
                    'class': '',  
                    'type': 'category',                  
                    'components': [{
                            'id': 1,
                            'name': 'HTML Block',                            
                            'code': 'html_block',
                            'variant': {
                                "id": '1',
                                'name': 'HTML Block',
                                'admin_layout': 'style1',
                                'class': [],                                
                                'config': {
                                    'data': description    
                                }
                            },
                            'children': []
                    }],
                    'seo_details': {
                        'page_name': category['name'],
                        'page_url': category["url"]["path"],
                        'meta_title': category['page_title'],
                        'meta_description': category['meta_description']
                    },
                    'created_by': created_by,
                    'created_at': int(datetime.now(timezone.utc).timestamp()),
                    'updated_at': int(datetime.now(timezone.utc).timestamp()),
                    'version': 1
                }]
            }                              
            return cms_db.update(store, {"id": category['category_id']}, {"$set": update_obj})

def syncAllSubNavigation(store):
        navigations=cms_db.get_sub_nav(store)
        navigations=processList(navigations)
        for navigation in navigations:
            is_updated=False
            for data in navigation['navigations']:
                if data['type'] == 'Categories':
                    category_id=cms_db.get_page_by_name(store, data['url'])
                    data['id']='Categories'+'_'+str(category_id['id'])
                    is_updated=True
                elif data['type']=='Web Pages':
                    webpage_id=cms_db.get_webpage_by_name(store,data['url'])
                    data['id']='Web Pages'+'_'+str(webpage_id['_id'])
                    is_updated=True
                elif data['type']=='Brands':
                    brand_id=cms_db.get_brand_by_name(store,data['url'])
                    data['id']='Brands'+'_'+str(brand_id['id']) 
                    is_updated=True 
            if is_updated:
                cms_db.update_sub_nav_by_id(store,navigation)
        return
       

def update_sub_category_flag(store):
    db_categories= cms_db.get_all_categories(store)
    for category in db_categories:
        has_child_data = cms_db.has_categories_with_parent_id(store, category['id'])
        if has_child_data:
            cms_db.update(store, {"id": category['id']}, {"$set": {"has_sub_child": has_child_data}})

def addSubCategoryFlag(store, body,cdn_baseurl):
        pages = cms_db.get_all_categories(store)
        for item in pages:
            category_id = item['bc_id']
            has_child_data = cms_db.has_categories_with_parent_id(store, category_id)
            cms_db.add_has_sub_child_flag(store, item['id'],has_child_data)

def _parse_description(description):
    soup = BeautifulSoup(description, 'html.parser')  
        
    # Define the replacements based on parent tags
    replacements = {
        'a': {
            '%%GLOBAL_ShopPathSSL%%': '',
            '%%GLOBAL_CdnStorePath%%': ''
        },
        'img': {
            '%%GLOBAL_ShopPathSSL%%': 'https://cdn11.bigcommerce.com/s-964anr',
            '%%GLOBAL_CdnStorePath%%': 'https://cdn11.bigcommerce.com/s-964anr'
        },
        'video': {
            '%%GLOBAL_ShopPathSSL%%': 'https://cdn11.bigcommerce.com/s-964anr',
            '%%GLOBAL_CdnStorePath%%': 'https://cdn11.bigcommerce.com/s-964anr'
        }
    }

    for placeholder in ['%%GLOBAL_ShopPathSSL%%', '%%GLOBAL_CdnStorePath%%']:
        elements = soup.find_all(string=lambda text: text and placeholder in text)
        for elem in elements:
            parent = elem.find_parent()
            if parent and parent.name in replacements and placeholder in replacements[parent.name]:
                new_value = replacements[parent.name][placeholder]
                elem.replace_with(elem.replace(placeholder, new_value))

    description = soup.prettify()

    description = description.replace('<a href="%%GLOBAL_ShopPathSSL%%', '<a href="') 
    description = description.replace('<a href="%%GLOBAL_CdnStorePath%%', '<a href="')
    description = description.replace("/searchresults.html?search_query=", "/search/?q=")    

    return description

    


