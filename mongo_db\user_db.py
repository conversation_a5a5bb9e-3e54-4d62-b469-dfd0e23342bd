import logging
import datetime
from datetime import timezone
import mongo_db
from bson import ObjectId
from utils import common
from new_mongodb import get_admin_db_client, get_store_by_id

logger = logging.getLogger()

USER_COLLECTION = "users"
ROLES_COLLECTION= 'roles'

def create_user(user):
    db = mongo_db.get_admin_db_client()
    result = db[USER_COLLECTION].find_one({"username": user['username']})
    user['updated_at'] = int(datetime.datetime.utcnow().timestamp())
    user['created_at'] = user['updated_at']
    user['name'] = user['name']
    user['role'] = user['role']

    result = db[USER_COLLECTION].insert_one(user)
    return result 

def fetch_all_users():
    db = mongo_db.get_admin_db_client()
    result = db[USER_COLLECTION].find({})
    return result 

def fetch_user_by_username(username):
    db = mongo_db.get_admin_db_client()
    result = db[USER_COLLECTION].find_one({"username": username})
    return result 

def fetch_users_by_usernames(usernames):
    if not usernames:
        return {}

    db = mongo_db.get_admin_db_client()
    cursor = db[USER_COLLECTION].find({"username": {"$in": list(usernames)}}, {"username": 1, "name": 1, "_id": 0})

    return {user["username"]: user for user in cursor}

# def fetch_user_by_name(name):
#     db = mongo_db.get_admin_db_client()
#     result = db[USER_COLLECTION].find_one({"name": name})
#     return result

def fetch_user_by_userID(userId):
    db = mongo_db.get_admin_db_client()
    result = db[USER_COLLECTION].find_one({"_id": ObjectId(userId)})
    return result 

def query_users(query):
    db = mongo_db.get_admin_db_client()
    result = db[USER_COLLECTION].find(query)
    return result 

def update_user(username, user_doc):
    db = mongo_db.get_admin_db_client()
    result = fetch_user_by_username(username)
    if result:
        created_at = result['created_at']
        doc_id = result['_id']
        del result["_id"]
        del result["username"]
        for key, value in user_doc.items():
            result[key] = value
        result['updated_at'] = int(datetime.datetime.utcnow().timestamp())
        result['created_at'] = created_at
        result = db[USER_COLLECTION].replace_one({"_id": doc_id}, result)
    return result 

def delete_user_by_username(username):
    db = mongo_db.get_admin_db_client()
    result = db[USER_COLLECTION].update_one({'username': username},{ "$set": {"status": 'deleted'}})
    return result

def update_user_last_login(store_id, id):
    store = get_store_by_id(store_id)
    db = get_admin_db_client(store)
    date_time = datetime.datetime.now(timezone.utc)
    converted_date_time = common.convert_to_timestamp(date_time)
    result = db[USER_COLLECTION].update_one(
        {"_id": ObjectId(id)}, 
        {'$set': {
            'last_login': converted_date_time
        }}
    )
    return result

def empty_user_permission(username):
    db = mongo_db.get_admin_db_client() 
    result = db[USER_COLLECTION].update_one({'username': username},{ "$set": {"role": '', "role_id": ''}})
    return result.modified_count
    
def update_user_role_name(username, role_name):
    db = mongo_db.get_admin_db_client() 
    result = db[USER_COLLECTION].update_one({'username': username}, { "$set": {"role": role_name}})
    return result.modified_count
    
def get_usr_permissions(username):
    db = mongo_db.get_admin_db_client() 
    user = db[USER_COLLECTION].find_one({"username": username})
    permissions = db[ROLES_COLLECTION].find_one({"_id": ObjectId(user['role_id'])})
    if permissions:
        return permissions
    else: 
        return False
    
def get_usr_permissions_with_role_id(role_id):
    db = mongo_db.get_admin_db_client()
    permissions = db[ROLES_COLLECTION].find_one({"_id": ObjectId(role_id)})
    if permissions:
        premissions_obje = permissions.get('permissions', {})
        if premissions_obje:
            projects_obj = premissions_obje.get('projects', {})        
            if projects_obj:
                operations_obj = projects_obj.get('operations', {})
                if operations_obj:
                    if operations_obj.get('read', True) and operations_obj.get('write', True):
                        return True
    return False

