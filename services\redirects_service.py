from services import Service
import logging
from fields.redirects_fields import redirect_fields
from utils.common import calculatePaginationData

logger = logging.getLogger()


class Redirects(Service):
    def get_all_redirects(self, body):
        result = []   
        body['filterBy'] = ['to_path', 'from_path']            
        redirects, total_data_length, paginationPage, limit = super().get_paginated_records_updated(body, redirect_fields,'')
              
        for redirect in redirects:
            res = {}            

            res['id'] = redirect['id']
            res['from_path'] = redirect['from_path']
            res['to'] = redirect['to']            
            res['to_url'] = redirect['to_url'].replace('https://www.midwestgoods.com', '')
            res['to_type'] = redirect['to_type']
            res['to_entity_id'] = redirect['to_entity_id'] if 'to_entity_id' in redirect else 0
            res['to_path'] = redirect['to_path'] if 'to_path' in redirect else''          

            result.append(res)
        data = calculatePaginationData(
            result, paginationPage, limit, total_data_length)
        
        return data       

    