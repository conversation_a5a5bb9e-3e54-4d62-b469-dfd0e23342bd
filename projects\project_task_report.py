from sqlalchemy import text
import pg_db
from mongo_db import user_db
import logging
# from projects.project_card import convert_time_format
from projects import project_card
from utils.common import calculatePaginationData, convert_time_format
from new_mongodb import get_admin_db_client_for_store_id

logger = logging.getLogger()

def get_task_report(store_id, project_id, username, page, limit, report_type, sort_array):
    response = {
        "status" :400        
    }
    db = get_admin_db_client_for_store_id(store_id)
    conn = pg_db.get_connection()
    try:
        if not project_id:
            response['status'] = 200
            response['data'] = []
            return response
        
        # Fetch user data
        user_data = user_db.fetch_user_by_username(username)
        user_id = str(user_data['_id'])

        # Fetch user preferences from MongoDB
        user_pref = db["user_preference"].find_one({"user_id": user_id, "type": report_type})

        project_ids = list(map(int, project_id.split(","))) if isinstance(project_id, str) else []

        module_ids = []
        assignees = []
        current_column_ids = []
        priorities = []
        ticket_statuses = []

        if user_pref:
            columns = user_pref.get("columns", {})
            module_ids = columns.get("modules", []) if len(project_ids) == 1 else []
            assignees = columns.get("assignee", [])
            current_column_ids = columns.get("stages", []) if len(project_ids) == 1 else []
            priorities = columns.get("priority", [])
            ticket_statuses = columns.get("ticket_status", [])

            if "unassigned" in assignees:
                assignees.append("")

        count_query = """ 
                        SELECT COUNT(*)
                        FROM agile_project_cards apc
                        JOIN agile_projects ap ON ap.id = apc.project_id
                        JOIN agile_project_access apa ON apa.project_id = ap.id
                        WHERE apa.username = :username AND apa.status = 'active'
                        """

        query = """ 
                SELECT 
                    ap.name AS project_name,
                    apm.name AS module_name,
                    apc.title AS card_title,
                    apc.card_identifier,
                    apc.assigned_to,
                    apc.spent_time,
                    apc.estimation,
                    apc.module_id,
                    TO_CHAR(
                        MAKE_INTERVAL(secs => GREATEST(EXTRACT(EPOCH FROM apc.estimation) - EXTRACT(EPOCH FROM apc.spent_time), 0)),
                        'HH24:MI:SS'
                    ) AS remaining_time,
                    CASE
                        WHEN EXTRACT(EPOCH FROM apc.estimation) = 0 AND EXTRACT(EPOCH FROM apc.spent_time) > 0
                        THEN 100
                        WHEN EXTRACT(EPOCH FROM apc.estimation) > 0 
                        THEN LEAST(ROUND((EXTRACT(EPOCH FROM apc.spent_time) / EXTRACT(EPOCH FROM apc.estimation)) * 100, 2)::INTEGER, 100)
                        ELSE 0
                    END AS progress,
                    ap.id AS project_id,
                    apc.id AS card_id,
                    apcs.is_resolved
                FROM agile_project_cards apc
                JOIN agile_projects ap ON ap.id = apc.project_id
                LEFT JOIN agile_project_modules apm ON apm.id = apc.module_id
                JOIN agile_project_access apa ON apa.project_id = ap.id
                JOIN agile_project_columns apcs ON apcs.id = apc.current_column_id
                WHERE apa.username = :username AND apa.status = 'active'
                """
        # Add filters based on user preferences
        if module_ids:
            query += " AND apc.module_id = ANY(:module_ids)"
            count_query += " AND apc.module_id = ANY(:module_ids)"
        if assignees:
            query += " AND apc.assigned_to = ANY(:assignees)"
            count_query += " AND apc.assigned_to = ANY(:assignees)"
        if current_column_ids:
            query += " AND apc.current_column_id = ANY(:current_column_ids)"
            count_query += " AND apc.current_column_id = ANY(:current_column_ids)"
        if priorities:
            query += " AND apc.priority = ANY(:priorities)"
            count_query += " AND apc.priority = ANY(:priorities)"
        if ticket_statuses:
            query += " AND apc.status = ANY(:ticket_statuses)"
            count_query += " AND apc.status = ANY(:ticket_statuses)"

        if project_id:
            query += " AND apc.project_id = ANY(:project_ids)"
            count_query += " AND apc.project_id = ANY(:project_ids)"
        else:
            response['status'] = 200
            response['data'] = []
            return response
    
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ["project_name"]:                
                query += f" ORDER BY ap.name {sort_direction}"
            elif sort_array[0] in ["module_name"]:                
                query += f" ORDER BY apm.name {sort_direction}" 
            elif sort_array[0] in ["card_title"]:                
                query += f" ORDER BY apc.title {sort_direction}"  
            elif sort_array[0] in ["assignee"]:                
                query += f" ORDER BY apc.assigned_to {sort_direction}"
            elif sort_array[0] in ["spent_time", "estimation"]:                
                query += f" ORDER BY apc.{sort_array[0]} {sort_direction}"
            elif sort_array[0] in ["remaining_time", "progress"]:                
                query += f" ORDER BY {sort_array[0]} {sort_direction}"

        if page and limit:
            offset = (page - 1) * limit
            query += " LIMIT :limit OFFSET :offset"

        filters_response = get_filters(project_ids, user_pref)
        
        count_query = text(count_query)
        count_query = count_query.params(
            username=username,
            project_ids=project_ids,
            module_ids=module_ids,
            assignees=assignees,
            current_column_ids=current_column_ids,
            priorities=priorities,
            ticket_statuses=ticket_statuses
        )
        count_result = conn.execute(count_query)
        total_records = count_result.fetchone()[0]

        query = text(query)
        query = query.params(
            username=username,
            limit=limit,
            offset=offset,
            project_ids=project_ids,
            module_ids=module_ids,
            assignees=assignees,
            current_column_ids=current_column_ids,
            priorities=priorities,
            ticket_statuses=ticket_statuses
        )
        result = conn.execute(query)

        task_report = []
        for card in result:
            user_data = user_db.fetch_user_by_username(card[4])
            if user_data:
                name = user_data.get('name', '')
            else:
                name = 'Unassigned'
            card_data = {
                'project_name': card[0],
                'module_name': card[1].split('_')[0],
                'card_title': card[2],
                'card_identifier': card[3],
                'assigned_to': card[4],
                'assignee': name,
                'spent_time': convert_time_format(card[5], call_from_report=True),
                'estimation': convert_time_format(card[6], call_from_report=True),
                'remaining_time': convert_time_format(card[8], call_from_report=True),
                'progress': f"{card[9]}%",
                'project_id': card[10],
                'card_id': card[11],
                'is_resolved': card[12]
            }
            task_report.append(card_data)

        paginated_data = calculatePaginationData(task_report, page, limit, total_records)
        response['status'] = 200
        response['data'] = {
            "task_report": paginated_data,
            "filters": filters_response["filters"]
        }

    finally:
        conn.close()
    return response


def get_filters(project_ids, user_pref):
    response = {
        "status": 400,
        "filters": []
    }
    conn = pg_db.get_connection()
    try:
        if not user_pref or "columns" not in user_pref:
            user_pref = {"columns": {}}

        # Initialize query and parameters
        if len(project_ids) == 1:
            query = """
                SELECT DISTINCT
                    m.id AS module_id, m.name AS module_name,
                    c.id AS column_id, c.name AS column_name,
                    p.id AS priority_id, p.label AS priority_label,
                    a.username AS access_username,
                    m.is_visible AS module_is_visible,
                    c.is_visible AS column_is_visible,
                    m.sort_id AS module_sort_id,
                    c.sort_id AS column_sort_id
                FROM agile_project_modules m
                LEFT JOIN agile_project_columns c ON c.project_id = m.project_id
                LEFT JOIN agile_card_priorities p ON TRUE
                LEFT JOIN agile_project_access a ON a.project_id = m.project_id
                WHERE m.project_id = :project_id OR c.project_id = :project_id
                ORDER BY m.sort_id, c.sort_id, a.username;
            """
            query_params = {"project_id": project_ids[0]}
        else:
            query = """
                SELECT DISTINCT
                    a.username AS access_username,
                    p.id AS priority_id, p.label AS priority_label
                FROM agile_project_access a
                JOIN agile_card_priorities p ON TRUE
                WHERE a.project_id = ANY(:project_id)
                ORDER BY a.username;
            """
            query_params = {"project_id": project_ids}

        # Execute the query
        result = conn.execute(text(query).params(query_params))
        rows = result.fetchall()

        # Structure data into categories
        modules, access, columns, priorities = [], [], [], []

        # Step 1: Collect all required usernames
        usernames = set()

        for row in rows:
            if len(project_ids) == 1:
                if row[6]:
                    usernames.add(row[6])  # Collect user IDs for batch fetching
            else:
                if row[0]:
                    usernames.add(row[0])

        # Step 2: Fetch all user data in one query
        users_data = user_db.fetch_users_by_usernames(usernames)  # Assume this returns a dict {username: {"name": name}}
        users_data = users_data or {}  # Ensure it’s a dictionary even if empty

        # Step 3: Process rows with a fast lookup
        for row in rows:
            if len(project_ids) == 1:  # Single project_id
                if row[0] and row[1]:
                    modules.append({"id": row[0], "label": row[1].split('_')[0], "is_visible": row[7]})

                if row[6]:
                    name = users_data.get(row[6], {}).get("name", "Unassigned")
                    access.append({"id": row[6], "label": name})

                if row[2] and row[3]:
                    columns.append({"id": row[2], "label": row[3].split('_')[0], "is_visible": row[8]})

                if row[4] and row[5]:
                    priorities.append({"id": row[4], "label": row[5]})

            else:  # Multiple project_ids
                if row[0]:
                    name = users_data.get(row[0], {}).get("name", "Unassigned")
                    access.append({"id": row[0], "label": name})

                if row[1] and row[2]:
                    priorities.append({"id": row[1], "label": row[2]})

        # Fetch card statuses from agile_card_status table
        status_query = """
            SELECT id AS status_id, label
            FROM agile_card_status
            ORDER BY id;
        """
        status_result = conn.execute(text(status_query))
        statuses = [{"id": row[0], "label": row[1]} for row in status_result]

        # Deduplicate entries
        modules = list({item["id"]: item for item in modules}.values())
        access = list({item["id"]: item for item in access}.values())
        columns = list({item["id"]: item for item in columns}.values())
        priorities = list({item["id"]: item for item in priorities}.values())

        # Include "Unassigned" entry at the bottom
        unassigned_entry = {"id": "unassigned", "label": "Unassigned"}
        if not any(item["id"] == "unassigned" for item in access):
            access.append(unassigned_entry)

        # Map to filter structure and set selected status
        response["filters"] = [
            {
                "id": "modules",
                "label": "Modules",
                "data": [{**module, "selected": module["id"] in user_pref["columns"].get("modules", []), "is_visible": module["is_visible"]} for module in modules]
            } if len(modules) > 0 else {},
            {
                "id": "assignee",
                "label": "Assignee",
                "data": [{**assignee, "selected": assignee["id"] in user_pref["columns"].get("assignee", [])} for assignee in access]
            },
            {
                "id": "stages",
                "label": "Stages",
                "data": [{**stage, "selected": stage["id"] in user_pref["columns"].get("stages", []), "is_visible": stage["is_visible"]} for stage in columns]
            } if len(columns) > 0 else {},
            {
                "id": "priority",
                "label": "Priority",
                "data": [{**priority, "selected": priority["id"] in user_pref["columns"].get("priority", [])} for priority in priorities]
            },
            {
                "id": "ticket_status",
                "label": "Ticket Status",
                "data": [{**status, "selected": status["id"] in user_pref["columns"].get("ticket_status", [])} for status in statuses]
            }
        ]

        response["status"] = 200

    except Exception as e:
        response["error"] = str(e)

    finally:
        conn.close()

    return response