from services import Service
import logging

logger = logging.getLogger()

class EmailTemplates(Service):
    def get_all_templates(self):
        email_templates = super().find_all()            
        return super().processList(email_templates)
    
    def update_mail_template(self, body):    
        if 'id' in body:    
            id = super().update_one({"_id": str(body['id'])}, {"$set":
                                                                      {
                                                                          "name": body['name'],
                                                                          "smtp_port": body['smtp_port'],
                                                                          "smtp_host": body['smtp_host'],
                                                                          "url":  body['url'],
                                                                          "password": body['password'],
                                                                          "email": body['email'],
                                                                          "subject": body['subject'],
                                                                          "body": body['body'],
                                                                          "short_code": body['id']
                                                                      }
                                                                      })       
            return True
        else:
            return False
