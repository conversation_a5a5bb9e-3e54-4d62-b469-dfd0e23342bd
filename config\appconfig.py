from flask import Flask
import logging

logger = logging.getLogger()

class ADConfigKey:
    AD_CONFIG_KEY = "ad_config"
    REDIS_CONFIG_KEY = "redis"
    DB_INFO_KEY = "db_info"
    ANALYTICS_DB_KEY = "analytics_db"
    ADMIN_DB_KEY = "admin_db"
    STORE_DB_KEY = "store_db"
    DB_NAME_KEY = "db_name"
    DB_HOST_NAME_KEY = "host_name"
    DB_PORT_KEY = "port"
    DB_USERNAME_KEY = "username"
    DB_PSWD_KEY = "pswd"
    DB_APP_USER_KEY = "app_users"
    DB_TASWORKER_APP_USER_KEY = "taskworker"

class AppConfig:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            logger.info('Creating the AppConfig')
            cls._instance = super(AppConfig, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        logger.info("Entering AppConfig")
        self.app = Flask(__name__)
        self.app.config.from_object("config")
        logger.info("Exiting AppConfig")
    
    def get_app(self):
        return self.app

def get_api_prefix():
    return appConfig.get_app().config["API_PREFIX"]

def is_debug_enabled():
    return appConfig.get_app().config["DEBUG"]

def get_pgdb_conn_str():
    return appConfig.get_app().config["PG_CONN_STRING"]

def get_celery_broker_url():
    return appConfig.get_app().config["CELERY_BROKER_URL"]

def get_celery_result_backend():
    return appConfig.get_app().config["CELERY_RESULT_BACKEND"]

def get_celery_app_name():
    return appConfig.get_app().config["CELERY_APP_NAME"]

def get_admin_store_key():
    return "admin"

def get_admin_panel_url():
    return appConfig.get_app().config["ADMIN_PANEL_URL"]

def get_redis_host():
    return appConfig.get_app().config["REDIS_HOST"]

def get_redis_port():
    return appConfig.get_app().config["REDIS_PORT"]

def get_redis_db():
    return appConfig.get_app().config["REDIS_DB"]

def get_admin_user():
    return appConfig.get_app().config["DEFAULT_ADMIN_USER"]

def get_admin_secret():
    return appConfig.get_app().config["DEFAULT_ADMIN_SECRET"]

def get_admin_user_role():
    return appConfig.get_app().config["DEFAULT_ADMIN_ROLE"]

def get_admin_db_name():
    return appConfig.get_app().config["ADMIN_DB_NAME"]

def get_mongodb_connection_str():
    return appConfig.get_app().config["MONGO_CONN_STRING"]

def get_mongodb_max_conn_pool_size():
    return appConfig.get_app().config["MONGO_MAX_POOL_SIZE"]

def get_tenant_db_name():
    return appConfig.get_app().config["TENANT_DB_NAME"]

def get_mongodb_conn_str():
    return appConfig.get_app().config["MONGO_CONN_STRING"]

def get_app_profile():
    return appConfig.get_app().config["PROFILE"]

def get_db_config_from_store(store, app_name):
    profile = get_app_profile()    
    return store.get(ADConfigKey.AD_CONFIG_KEY, {}).get(profile, {}).get(ADConfigKey.DB_INFO_KEY, {}).get(app_name, None)

def get_analytics_db_config_from_store(store):
    return get_db_config_from_store(store, ADConfigKey.ANALYTICS_DB_KEY)


appConfig = AppConfig()