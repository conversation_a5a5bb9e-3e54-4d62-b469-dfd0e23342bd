from flask import request
import logging
import traceback
from api import APIResource
from iam import permission_service, feature_service

logger = logging.getLogger()

class Permissions(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            res = feature_service.get_store_features(store_id=store['id'])
            content = {
                "permissions": res
            }
            return content, 200
        finally:
            logger.debug("Exiting Permissions GET")
    
    def post_executor(self, request, token_payload, store):
        try:
            payload = request.get_json(force=True)
            res = permission_service.create_permissions(store, payload)
            return res
        finally:
            logger.debug("Exiting Permissions GET")
    
    def put_executor(self, request, token_payload, store):
        try:
            id = request.args.get("id")
            if not id:
                return {"message": "please provide id as query params"}
            
            payload = request.get_json(force=True)
            success_id = permission_service.update_permissions(store, id, payload)
            
            if success_id:
                return {"status": "success"}, 201
        finally:
            logger.debug("Exiting Permissions GET")
    
    def delete_executor(self, request, token_payload, store):
        try:
            id = request.args.get("id")
            if id:
                store_id = store['id']
                success_id = permission_service.delete_by_id(store_id, id)
                if success_id:
                    return {'data': 'success'}, 200
            else:
                return {"status": "please provide an id as a query parameter"}, 402
        finally:
            logger.debug("Exiting Permissions Delete")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
    def put(self):
        return self.execute_store_request(request, self.put_executor)
    
    def delete(self):
        return self.execute_store_request(request, self.delete_executor)

    