from projects import check_project_access
from sqlalchemy import text
import pg_db
from mongo_db import user_db
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from datetime import datetime, timedelta
from projects.project_details import _fetch_project_detail
import logging
import traceback
import re
from utils.common import convert_to_timestamp, calculatePaginationData, convert_time_format
from new_mongodb import get_admin_db_client_for_store_id
from projects.project_task_report import get_filters
from new_mongodb import get_admin_db_client_for_store_id, AdminAppNotification, ProjectNotification
import task
from collections import OrderedDict

logger = logging.getLogger()  

def get_all_card(store_id, project_id, username, search=None):
    response = {
        'status': 400
    }
    db = get_admin_db_client_for_store_id(store_id)
    conn = pg_db.get_connection()
    try:        
        project_access = check_project_access(project_id, username) 
        if not project_access:
            response['status'] = 403
            response['message'] = "You don't have access for this project."
            return response
        
        # Fetch user data
        user_data = user_db.fetch_user_by_username(username)
        user_id = str(user_data['_id'])

        # Fetch user preferences from MongoDB
        user_pref = db["user_preference"].find_one({"user_id": user_id, "type": "project_agile_board"})

        module_ids = []
        assignees = []
        current_column_ids = []
        priorities = []
        ticket_statuses = []

        if user_pref:
            columns = user_pref.get("columns", {})
            module_ids = columns.get("modules", [])
            assignees = columns.get("assignee", [])
            current_column_ids = columns.get("stages", [])
            priorities = columns.get("priority", [])
            ticket_statuses = columns.get("ticket_status", [])

            if "unassigned" in assignees:
                assignees.append("")
           
        query = f"""SELECT pc.id, pc.project_id, pc.module_id, pc.current_column_id, pc.status, 
                    pc.priority, pc.title, pc.card_identifier, pc.assigned_to, pc.is_archived, pc.created_by, 
                    pm.name AS module_name, bc.name AS column_name, cs.label as status_label, cp.label as priority_label, pc.due_date, bc.is_resolved
                FROM agile_project_cards pc
                JOIN agile_project_modules pm ON pc.module_id = pm.id
                JOIN agile_project_columns bc ON pc.current_column_id = bc.id
                LEFT JOIN agile_card_status cs ON pc.status = cs.id
                LEFT JOIN agile_card_priorities cp ON pc.priority = cp.id
                WHERE pc.project_id = :project_id and pm.is_visible = true and bc.is_visible = true"""
        
        # Add search condition if search_value is provided
        if search:
            query += f" AND (pc.card_identifier ILIKE '%" + search + "%' OR pc.title ILIKE '%" + search + "%')"
        
        # Add filters based on user preferences
        if module_ids:
            query += " AND pc.module_id = ANY(:module_ids)"
        if assignees:
            query += " AND pc.assigned_to = ANY(:assignees)"
        if current_column_ids:
            query += " AND pc.current_column_id = ANY(:current_column_ids)"
        if priorities:
            query += " AND pc.priority = ANY(:priorities)"
        if ticket_statuses:
            query += " AND pc.status = ANY(:ticket_statuses)"

        query += " ORDER BY pc.sort_id"

        filters_response = get_filters([project_id], user_pref)

        query = text(query)
        query = query.params(
            project_id=project_id, 
            module_ids=module_ids, 
            assignees=assignees, 
            current_column_ids=current_column_ids, 
            priorities=priorities, 
            ticket_statuses=ticket_statuses)
        result = conn.execute(query)
        rows = result.fetchall()
        
        data = []
        for row in rows:
            card = {}
            card['tickedID'] = row[0]
            card['projectID'] = row[1]
            card['moduleID'] = row[2]
            card['moduleName'] = row[11]
            card['columnID'] = row[3]
            card['card_identifier'] = row[7]
            card['columnName'] = row[12]
            card['status'] = row[4]
            card['statusLabel'] = row[13]
            card['priority'] = row[5]
            card['priorityLabel'] = row[14]
            card['title'] = row[6]
            card['assignee'] = row[8]
            card['is_archived'] = row[9]
            card['created_by'] = row[10]
            card['due_date'] = convert_to_timestamp(row[15])
            card['is_resolved'] = row[16]
            data.append(card)

        converted_data = {}
        for item in data:
            column_name = item['columnName']
            module_name = item['moduleName']
            column_id = f"{module_name.lower()}_{column_name.replace(' ', '-').lower()}"
            ticket_id = item['tickedID']            

            # Check if the column entry exists in the converted data, if not create it
            if column_id not in converted_data:
                converted_data[column_id] = {
                    "columnName": column_name.split('_')[0],
                    "columnID": column_id,
                    "cID": item['columnID'],
                    "moduleName": module_name.split('_')[0],
                    "moduleID": item['moduleID'],
                    "columnItems": []
                }
            
            user_data = user_db.fetch_user_by_username(item['assignee'])
            if user_data:
                name = user_data.get('name', '')
            else:
                name = ''

            # Add the ticket under columnItems for the corresponding column
            converted_data[column_id]['columnItems'].append({
                "ticketID": ticket_id,
                "columnID": column_id,
                "cID": item['columnID'],                
                "moduleName": module_name.split('_')[0],
                "moduleID": item['moduleID'],
                "projectID": item['projectID'],
                "title": item['title'],
                "card_identifier": item['card_identifier'],
                "assignee": item['assignee'],
                "asignee_name": name,
                "status": item['status'],
                "statusLabel": item['statusLabel'],                
                "priority": item['priority'],
                "priorityLabel": item['priorityLabel'],
                "is_archived": item['is_archived'],
                "created_by": item['created_by'],
                "due_date": item['due_date'],
                "is_resolved": item['is_resolved']
            })
        converted_data_list = list(converted_data.values())
        response['status'] = 200
        response['data'] = {
            "cards": converted_data_list,
            "filters": filters_response["filters"]
        }
    finally:       
        conn.close()
    return response   

def get_all_cards_for_parent_child(store_id, project_id, username, search=None):
    response = {
        'status': 400
    }
    conn = pg_db.get_connection()
    try:        
        project_access = check_project_access(project_id, username) 
        if not project_access:
            response['status'] = 403
            response['message'] = "You don't have access for this project."
            return response
        
        # Add search condition if search_value is provided
        search_query = ""
        if search:
            search_query += f" AND (pc.card_identifier ILIKE '%" + search + "%' OR pc.title ILIKE '%" + search + "%')"
           
        query = text(f"""SELECT pc.id, pc.project_id, pc.module_id, pc.current_column_id, pc.status, 
                    pc.priority, pc.title, pc.card_identifier, pc.assigned_to, pc.is_archived,
                    pm.name AS module_name, bc.name AS column_name, cs.label as status_label, cp.label as priority_label, pc.due_date, bc.is_resolved
                FROM agile_project_cards pc
                JOIN agile_project_modules pm ON pc.module_id = pm.id
                JOIN agile_project_columns bc ON pc.current_column_id = bc.id
                LEFT JOIN agile_card_status cs ON pc.status = cs.id
                LEFT JOIN agile_card_priorities cp ON pc.priority = cp.id
                WHERE pc.project_id = :project_id and pm.is_visible = true and bc.is_visible = true {search_query} ORDER BY pc.sort_id""")

        query = query.params(project_id=project_id)
        result = conn.execute(query)
        rows = result.fetchall()
        
        data = []
        for row in rows:
            card = {}
            name = ''
            user_data = user_db.fetch_user_by_username(row[8])
            if user_data:
                name = user_data.get('name', '')

            card['tickedID'] = row[0]
            card['projectID'] = row[1]
            card['moduleID'] = row[2]
            card['moduleName'] = row[10]
            card['columnID'] = row[3]
            card['columnName'] = row[11]
            card['status'] = row[4]
            card['statusLabel'] = row[12]
            card['priority'] = row[5]
            card['priorityLabel'] = row[13]
            card['title'] = row[6]
            card['card_identifier'] = row[7]
            card['assignee'] = name
            card['is_archived'] = row[9]
            card['created_by'] = row[10]
            card['due_date'] = convert_to_timestamp(row[14])
            card['is_resolved'] = row[15]
            data.append(card)

        response['status'] = 200
        response['data'] = data

    finally:       
        conn.close()
    return response   

def get_all_card_list_view(store_id, project_id, username, page, limit, search=None, sort_array=[]):
    response = {
        'status': 400
    }
    db = get_admin_db_client_for_store_id(store_id)
    conn = pg_db.get_connection()
    try:        
        offset = (page - 1) * limit
        project_access = check_project_access(project_id, username) 
        if not project_access:
            response['status'] = 403
            response['message'] = "You don't have access for this project."
            return response
        
        # Fetch user data
        user_data = user_db.fetch_user_by_username(username)
        user_id = str(user_data['_id'])

        # Fetch user preferences from MongoDB
        user_pref = db["user_preference"].find_one({"user_id": user_id, "type": "project_list_view"})

        module_ids = []
        assignees = []
        current_column_ids = []
        priorities = []
        ticket_statuses = []

        if user_pref:
            columns = user_pref.get("columns", {})
            module_ids = columns.get("modules", [])
            assignees = columns.get("assignee", [])
            current_column_ids = columns.get("stages", [])
            priorities = columns.get("priority", [])
            ticket_statuses = columns.get("ticket_status", [])

            if "unassigned" in assignees:
                assignees.append("")

        
        count_query = f"""SELECT COUNT(*) FROM agile_project_cards pc
                JOIN agile_project_modules pm ON pc.module_id = pm.id
                JOIN agile_project_columns bc ON pc.current_column_id = bc.id
                LEFT JOIN agile_card_status cs ON pc.status = cs.id
                LEFT JOIN agile_card_priorities cp ON pc.priority = cp.id
                WHERE pc.project_id = :project_id and pm.is_visible = true and bc.is_visible = true"""

        query = f"""SELECT pc.id, pc.project_id, pc.module_id, pc.current_column_id, pc.status, 
                    pc.priority, pc.title, pc.card_identifier, pc.assigned_to, pc.is_archived, pc.created_by, 
                    pm.name AS module_name, bc.name AS column_name, cs.label as status_label, cp.label as priority_label, pc.due_date, pc.created_at, pc.updated_at, bc.is_resolved, pc.start_date
                FROM agile_project_cards pc
                JOIN agile_project_modules pm ON pc.module_id = pm.id
                JOIN agile_project_columns bc ON pc.current_column_id = bc.id
                LEFT JOIN agile_card_status cs ON pc.status = cs.id
                LEFT JOIN agile_card_priorities cp ON pc.priority = cp.id
                WHERE pc.project_id = :project_id and pm.is_visible = true and bc.is_visible = true"""
        
        if search:
            query += " AND (pc.card_identifier ILIKE :search OR pc.title ILIKE :search)"
            count_query += " AND (pc.card_identifier ILIKE :search OR pc.title ILIKE :search)"

        # Add filters based on user preferences
        if module_ids:
            query += " AND pc.module_id = ANY(:module_ids)"
            count_query += " AND pc.module_id = ANY(:module_ids)"
        if assignees:
            query += " AND pc.assigned_to = ANY(:assignees)"
            count_query += " AND pc.assigned_to = ANY(:assignees)"
        if current_column_ids:
            query += " AND pc.current_column_id = ANY(:current_column_ids)"
            count_query += " AND pc.current_column_id = ANY(:current_column_ids)"
        if priorities:
            query += " AND pc.priority = ANY(:priorities)"
            count_query += " AND pc.priority = ANY(:priorities)"
        if ticket_statuses:
            query += " AND pc.status = ANY(:ticket_statuses)"
            count_query += " AND pc.status = ANY(:ticket_statuses)"

        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ["title", "due_date", "created_at", "updated_at", "start_date"]:                
                query += f" ORDER BY pc.{sort_array[0]} {sort_direction}"                
            elif sort_array[0] in ["module_name"]: 
                query += f" ORDER BY pm.name {sort_direction}"
            elif sort_array[0] in ["assignee"]: 
                query += f" ORDER BY pc.assigned_to {sort_direction}"   
            elif sort_array[0] in ["column_name"]:
                query += f" ORDER BY bc.sort_id {sort_direction}, pc.sort_id {sort_direction}"
            elif sort_array[0] in ["priority_label"]:
                query += f" ORDER BY cp.label {sort_direction}"

        query += f" LIMIT :limit OFFSET :offset"

        filters_response = get_filters([project_id], user_pref)

        count_query = text(count_query)
        count_query = count_query.params(
            project_id=project_id,
            module_ids=module_ids,
            assignees=assignees,
            current_column_ids=current_column_ids,
            priorities=priorities,
            ticket_statuses=ticket_statuses,
            search=search
        )
        count_result = conn.execute(count_query)
        total_records = count_result.fetchone()[0]

        query = text(query)
        query = query.params(project_id=project_id, 
            limit=limit, offset=offset,
            module_ids=module_ids,
            assignees=assignees,
            current_column_ids=current_column_ids,
            priorities=priorities,
            ticket_statuses=ticket_statuses,
            search=search
        )
        result = conn.execute(query)
        rows = result.fetchall()

        # Fetch Custom Field Metadata
        meta_query = text("""
            SELECT id, name
            FROM agile_project_customfield
            WHERE project_id = :project_id
        """)
        meta_rows = conn.execute(meta_query, {"project_id": project_id}).fetchall()

        # Step 4: Map meta_id -> custom_field_1, custom_field_2, ...
        custom_field_ordered = OrderedDict()
        for idx, row in enumerate(meta_rows, start=1):
            custom_key = f"custom_field_{idx}"
            custom_field_ordered[row[0]] = {"field_key": custom_key, "field_name": row[1]}

        # Step 5: Fetch custom field values for all cards
        card_ids = [row[0] for row in rows]
        field_value_query = text("""
            SELECT card_id, project_customfield_id,
                COALESCE(NULLIF(str_value, ''), CAST(number_value AS TEXT)) AS value
            FROM agile_customfield_value
            WHERE card_id = ANY(:card_ids) AND project_customfield_id = ANY(:project_customfield_id)
        """)
        field_value_rows = conn.execute(field_value_query, {
            "card_ids": card_ids,
            "project_customfield_id": list(custom_field_ordered.keys())
        }).fetchall()

        # Step 6: Build lookup map (card_id, meta_id) -> value
        custom_field_map = {}
        for row in field_value_rows:
            card_id, project_customfield_id, value = row
            key = (card_id, project_customfield_id)
            custom_field_map[key] = value

            
        data = []
        for row in rows:
            user_data = user_db.fetch_user_by_username(row[8])
            if user_data:
                name = user_data.get('name', '')
            else:
                name = ''
            card = {}
            card['ticked_id'] = row[0]
            card['project_id'] = row[1]
            card['module_id'] = row[2]
            card['module_name'] = row[11].split('_')[0]
            card['column_id'] = row[3]
            card['card_identifier'] = row[7]
            card['column_name'] = row[12].split('_')[0]
            # card['status'] = row[4]
            # card['status_label'] = row[13]
            card['priority'] = row[5]
            card['priority_label'] = row[14]
            card['title'] = row[6]
            card['assignee'] = name
            card['assignee_email'] = row[8]
            card['is_archived'] = row[9]
            card['created_by'] = row[10]
            card['due_date'] = convert_to_timestamp(row[15])
            card['created_at'] = convert_to_timestamp(row[16])
            card['updated_at'] = convert_to_timestamp(row[17])
            card['is_resolved'] = row[18]
            card['start_date'] = convert_to_timestamp(row[19])
            
            # Add custom fields
            for project_customfield_id, meta_info in custom_field_ordered.items():
                custom_key = meta_info["field_key"]
                card[custom_key] = custom_field_map.get((row[0], project_customfield_id))
            
            data.append(card)
        
        custom_fields_meta = {
            meta_info["field_key"]: meta_info["field_name"]
            for meta_info in custom_field_ordered.values()
        }

        paginated_data = calculatePaginationData(data, page, limit, total_records)
        
        # Inject custom_field meta into pagination meta
        paginated_data['meta']['custom_fields'] = custom_fields_meta

        response['status'] = 200
        response['data'] = {
            "cards_report": paginated_data,
            "filters": filters_response["filters"]
        }
    finally:       
        conn.close()
    return response

def get_project_structure(project_id, username):
    response = {
        'status': 400
    }
    conn = pg_db.get_connection()
    try:  
        project_access = check_project_access(project_id, username) 
        if not project_access:
            response['status'] = 403
            response['message'] = "You don't have access for this project."
            return response    
        query = text(
            f"""SELECT id, project_id, name as module_name, is_archived FROM agile_project_modules WHERE project_id = :project_id and is_visible = true ORDER BY sort_id"""
        )
        query = query.bindparams(project_id=project_id)
        module_result = conn.execute(query)
        module_rows = module_result.fetchall()
        column_query = text(
            f"""SELECT ps.id as project_id, ps.name as project_name, bc.id, bc.name as column_name, bc.is_archived FROM agile_projects ps 
            JOIN agile_project_columns bc ON ps.id = bc.project_id
            WHERE ps.id = :project_id and bc.is_visible = true
            ORDER BY bc.sort_id"""
        )
        column_query = column_query.bindparams(project_id=project_id)
        column_result = conn.execute(column_query)
        column_rows = column_result.fetchall()        
        data = []
        if column_rows and module_rows:
            for item1 in column_rows:
                for item2 in module_rows:                    
                    column = {
                        "columnName": item1[3].split('_')[0],
                        "columnID": f"{item2[2].lower()}_{item1[3].replace(' ', '-').lower()}",
                        "cID": item1[2],
                        "moduleName": item2[2].split('_')[0],
                        "moduleID": item2[0],
                        "columnItems": []
                    }                   
                    data.append(column)            
            response['status'] = 200
            response['data'] = data
        else:
            response['status'] = 200
            response['data'] = []          
    finally:        
        conn.close()
    return response      

def create_card(store_id, payload, username, project_id, module_id):
    response = {
        'status': 400
    }
    conn = pg_db.get_connection()
    try:
        current_column_id = int(payload.get('current_column_id', 0))
        status = int(payload.get('status', 0)) if payload['status'] != 0 else None
        priority = int(payload.get('priority', 0)) if payload['priority'] != 0 else None
        title = payload.get('title', '')
        description = payload.get('description', '')
        assigned_to = payload.get('assigned_to', '')

        spent_time_value = payload.get('spent_time', '0h')
        spent_time_hours = parse_time(spent_time_value) 
        spent_time = f"{spent_time_hours} hour"

        estimation_value = payload.get('estimation', '0h')
        estimation_time_hours = parse_time(estimation_value)
        estimation = f"{estimation_time_hours} hour"

        # due_date_str = payload.get('due_date', None)
        # due_date = datetime.strptime(due_date_str, '%Y-%m-%d') if due_date_str else None
        due_date = payload.get('due_date', None)

        # start_date_str = payload.get('start_date', None)
        # start_date = datetime.strptime(start_date_str, '%Y-%m-%d') if start_date_str else None
        start_date = payload.get('start_date', None)

        is_archived = payload.get('is_archived', False)

        parent_card_id = payload.get('parent_card_id', None)
        child_card_id = payload.get('child_card_id', None)

        card_index_query = conn.execute(
            text(f"SELECT card_index FROM {pg_db.agile_project_cardindex} WHERE project_id = :project_id"),
            {'project_id': project_id}
        )
        card_index = card_index_query.fetchone()[0]

        project_detail = _fetch_project_detail(conn, project_id, username)

        project_initials = project_detail[0]['project_initials']
        # project_initials = ''.join(word[0].upper() for word in project_name.split())

        new_card_index = card_index + 1

        card_identifier = f"{project_initials}-{new_card_index}"

        # Fetch the highest sort_id for the given project
        sort_id_query = conn.execute(
            text(f"SELECT MAX(sort_id) FROM agile_project_cards WHERE project_id = :project_id AND module_id = :module_id AND current_column_id = :current_column_id"),
            {'project_id': project_id, 'module_id': module_id, 'current_column_id': current_column_id}
        )
        highest_sort_id = sort_id_query.scalar()
        new_sort_id = highest_sort_id + 1 if highest_sort_id is not None else 1

        query = text(
        f"""INSERT INTO agile_project_cards (project_id, module_id, current_column_id, status, priority, title, card_identifier, description, assigned_to, spent_time, estimation, due_date, is_archived, created_by, updated_by, created_at, updated_at, sort_id, start_date, parent_card_id)
            VALUES (:project_id, :module_id, :current_column_id, :status, :priority, :title, :card_identifier, :description, :assigned_to, :spent_time, :estimation, :due_date, :is_archived, :created_by, :updated_by, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, :sort_id, :start_date, :parent_card_id) 
            RETURNING id;
        """
         )
        query = query.params(project_id=project_id, module_id=module_id, current_column_id=current_column_id, status=status, priority=priority, title=title, card_identifier=card_identifier, description=description, assigned_to=assigned_to, spent_time=spent_time, estimation=estimation, due_date=due_date, is_archived=is_archived, created_by=username, updated_by=username, sort_id=new_sort_id, start_date=start_date, parent_card_id=parent_card_id)
        result = conn.execute(query)
        card_id = result.fetchone()[0]

        if result.rowcount > 0:
            if child_card_id:
                message, falg = update_card_hierarchy(conn, card_id, child_card_id)
             # Update the card_index in the agile_project_cardindex table
            conn.execute(
                text(f"UPDATE {pg_db.agile_project_cardindex} SET card_index = :new_card_index WHERE project_id = :project_id"),
                {'new_card_index': new_card_index, 'project_id': project_id}
            )

        response['status'] = 200
        response['message'] = 'Card created successfully.'

        if card_id:
            task.send_task(task.GENERATE_ADMIN_APP_NOTIFICATION_TASK, args=(store_id, AdminAppNotification.TICKET_CREATED, card_id))
            task.send_task(task.SEND_PROJECT_NOTIFICATION_TASK, args=(store_id, ProjectNotification.TICKET_CREATED, card_id))
    except IntegrityError as e:
        logger.error(str(traceback.format_exc()))  
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 409
            response['message'] = "title: Card with the same id or title already exists."
        else:          
            response['status'] = 500
            response['message'] = "Something went wrong."   
    finally:
        conn.commit()
        conn.close()
    return response 

def get_all_cards(email, project_id, search, sort_array=[], page=1, limit=20):
    response = {
        'status': 400
    }
    conn = pg_db.get_connection()
    try:
        if not project_id:
            response['status'] = 200
            response['data'] = []
            return response
        
        project_ids = list(map(int, project_id.split(","))) if isinstance(project_id, str) else []
        query = f"""
                SELECT 
                    ap.name AS project_name,
                    apm.name AS module_name,
                    apc.title AS card_title,
                    apc.card_identifier,
                    apc.assigned_to,
                    apc.spent_time,
                    apc.estimation,
                    apc.module_id,
                    TO_CHAR(
                        MAKE_INTERVAL(secs => GREATEST(EXTRACT(EPOCH FROM apc.estimation) - EXTRACT(EPOCH FROM apc.spent_time), 0)),
                        'HH24:MI:SS'
                    ) AS remaining_time,
                    CASE
                        WHEN EXTRACT(EPOCH FROM apc.estimation) = 0 AND EXTRACT(EPOCH FROM apc.spent_time) > 0
                        THEN 100
                        WHEN EXTRACT(EPOCH FROM apc.estimation) > 0 
                        THEN LEAST(ROUND((EXTRACT(EPOCH FROM apc.spent_time) / EXTRACT(EPOCH FROM apc.estimation)) * 100, 2)::INTEGER, 100)
                        ELSE 0
                    END AS progress,
                    ap.id AS project_id,
                    apc.id AS card_id
                FROM agile_project_cards apc
                JOIN agile_projects ap ON ap.id = apc.project_id
                LEFT JOIN agile_project_modules apm ON apm.id = apc.module_id
                WHERE apc.assigned_to = :username AND apc.project_id IN :project_ids
        """
        
        card_count_query = f"""
                SELECT COUNT(apc.id) AS total_cards
                FROM agile_project_cards apc
                WHERE apc.assigned_to = :username AND apc.project_id IN :project_ids
        """

        if search:
            query += f" AND (apc.card_identifier ILIKE '%" + search + "%' OR apc.title ILIKE '%" + search + "%')"
            card_count_query += f" AND (apc.card_identifier ILIKE '%" + search + "%' OR apc.title ILIKE '%" + search + "%')"

        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ["project_name"]:                
                query += f" ORDER BY ap.name {sort_direction}"
            elif sort_array[0] in ["module_name"]:                
                query += f" ORDER BY apm.name {sort_direction}" 
            elif sort_array[0] in ["card_title"]:                
                query += f" ORDER BY apc.title {sort_direction}"  
            elif sort_array[0] in ["assignee"]:                
                query += f" ORDER BY apc.assigned_to {sort_direction}"
            elif sort_array[0] in ["spent_time", "estimation"]:                
                query += f" ORDER BY apc.{sort_array[0]} {sort_direction}"
            elif sort_array[0] in ["remaining_time", "progress"]:                
                query += f" ORDER BY {sort_array[0]} {sort_direction}"

        offset = (page - 1) * limit
        query += f" LIMIT :limit OFFSET :offset"
        
        result = conn.execute(text(query).params(username=email, project_ids=tuple(project_ids), limit=limit, offset=offset))

        # Execute count query
        count_result = conn.execute(text(card_count_query).params(username=email, project_ids=tuple(project_ids)))
        card_count = count_result.scalar() if count_result else 0

        cards = []
        if result:
            for card in result:
                user_data = user_db.fetch_user_by_username(card[4])
                if user_data:
                    name = user_data.get('name', '')
                else:
                    name = 'Unassigned'
                card = {
                    'project_name': card[0],
                    'module_name': card[1].split('_')[0],
                    'card_title': card[2],
                    'card_identifier': card[3],
                    'assigned_to': card[4],
                    'assignee': name,
                    'spent_time': convert_time_format(card[5], call_from_report=True),
                    'estimation': convert_time_format(card[6], call_from_report=True),
                    'remaining_time': convert_time_format(card[8], call_from_report=True),
                    'progress': f"{card[9]}%",
                    'project_id': card[10],
                    'card_id': card[11]
                }
                cards.append(card)

            user_data = user_db.fetch_user_by_username(email)
            if user_data:
                name = user_data.get('name', '')
            else:
                name = 'Unassigned'
            data = {
                'total_cards': card_count,
                'user_name': name,
                'cards': cards
            }
            paginated_data = calculatePaginationData(data, page, limit, card_count)
            response['status'] = 200
            response['data'] = paginated_data
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        conn.close()
    return response

def update_card_hierarchy(conn, p_card_id, card_id):
    """
    :param conn: Database connection object.
    :param p_card_id: Parent card ID to set.
    :param card_id: The current card ID to process.
    """

    if not p_card_id or not card_id:
        return "Parent card ID and Card ID cannot be None or empty.", False

    # **Check 1: Circular reference (A card cannot be its own parent)**
    if p_card_id == card_id:
        return "Circular reference detected: A card cannot be its own parent.", False

    # **Check 2: Prevent Infinite Loops (Ensure parent is not a descendant)**
    def is_descendant(child_id, parent_id):
        """Checks if the parent_id is in the child's hierarchy (to avoid loops)."""
        stack = [child_id]
        while stack:
            current_card_id = stack.pop()
            res = conn.execute(
                text(f"SELECT parent_card_id FROM {pg_db.project_cards} WHERE id = :card_id"),
                {'card_id': current_card_id}
            )
            parent_card = res.fetchone()
            if parent_card:
                parent_card_id = parent_card[0]
                if parent_card_id == parent_id:
                    return True  # Cycle detected
                if parent_card_id:
                    stack.append(parent_card_id)
        return False

    if is_descendant(p_card_id, card_id):
        return "Hierarchy cycle detected: The parent card is a descendant of the child.", False

    # **Check 3: Duplicate Relationship (Check if card is already linked)**
    existing_link = conn.execute(
        text(f"SELECT id FROM {pg_db.project_cards} WHERE id = :card_id AND parent_card_id = :p_card_id"),
        {'card_id': card_id, 'p_card_id': p_card_id}
    ).fetchone()

    if existing_link:
        return "Duplicate entry detected: This parent-child relationship already exists.", False

    # **Update parent_card_id**
    conn.execute(
        text(f"""
            UPDATE {pg_db.project_cards}
            SET parent_card_id = :p_card_id WHERE id = :card_id
        """),
        {'p_card_id': p_card_id, 'card_id': card_id}
    )

    conn.commit()  # Commit changes after update
    return "Updated successfully.", True
            
def parse_time(time_str):
    total_hours = 0

    # Regular expression to extract time components (e.g., "2w", "3d", "4h", "30m")
    time_pattern = re.findall(r'(\d+)([wdhm])', time_str)

    for value, unit in time_pattern:
        value = int(value)  # Convert the numeric part to an integer
        if unit == 'w':
            total_hours += value * 5 * 8  # 1 week = 5 days, 1 day = 8 hours
        elif unit == 'd':
            total_hours += value * 8  # 1 day = 8 hours
        elif unit == 'h':
            total_hours += value  # Add hours directly
        elif unit == 'm':
            total_hours += value / 60  # Convert minutes to hours
        else:
            raise ValueError(f"Invalid time unit: {unit}")

    return total_hours

