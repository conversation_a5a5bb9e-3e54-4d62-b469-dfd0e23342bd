from new_mongodb import fetch_one_document_from_storefront_collection
from sqlalchemy import text
import pg_db
from mongo_db import user_db
from datetime import datetime
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from utils.common import calculatePaginationData
from utils import bc, redis_util, store_util
from utils.common import parse_json
import logging
import traceback
from utils.common import convert_to_timestamp

logger = logging.getLogger()


def get_bulk_product_list(store, page, limit, filter, status, sort_array=[], product_type='', pending_allocation=False):
    response = {
        "status": 400,
    }
    conn = pg_db.get_connection()
    try:
        count_query = """SELECT COUNT(*) FROM bo_bulk_order_products AS bop"""

        base_query = """
        SELECT  
                    bop_id,
                    bc_sku AS SKU,
                    bc_product_id,
                    bc_name AS product_name,
                    product_image,
                    status,
                    orders,
                    created_by,
                    is_qty_locked,
                    created_at AS date_created,
                    type,
                    min_market_price,
                    is_marketing_product,
                    (SELECT COUNT(DISTINCT po_id) FROM bo_purchase_order_lineitems as pol WHERE bop_id = bop.bop_id) AS total_po_count,
                    (SELECT COUNT(DISTINCT pol.po_id) FROM bo_purchase_order_lineitems AS pol JOIN bo_purchase_orders AS po ON po.po_id = pol.po_id WHERE po.status NOT IN ('deleted', 'completed') AND pol.bop_id = bop.bop_id AND (pol.status IS NULL OR pol.status NOT IN ('cancelled', 'completed'))) AS pending_po_count,
                    COALESCE((SELECT SUM(pol.remaining_qty) FROM bo_purchase_order_lineitems AS pol JOIN bo_purchase_orders AS po ON po.po_id = pol.po_id WHERE po.status NOT IN ('deleted', 'completed') AND pol.bop_id = bop.bop_id  AND (pol.status IS NULL OR pol.status NOT IN ('cancelled', 'completed'))), 0) AS total_pending_case_qty,
                    (SELECT inventory_level / NULLIF(bop.case_qty, 0) FROM products AS p WHERE p.product_id = bop.bc_product_id) AS total_available_case_qty
            FROM bo_bulk_order_products as bop
            """         
        
        conditions = []
        if filter:
            conditions.append(f"(bc_name ILIKE '%{filter}%' OR bc_sku ILIKE '%{filter}%' OR CAST(bop_id AS TEXT) ILIKE '%{filter}%')")

        # if status:
        #     conditions.append(f"status = '{status}'")
        if status == 'archived':            
            conditions.append(f"status IN ('archived')")            
        else:
            conditions.append(f"status IN ('inactive', 'active', 'draft')")            
        
        if product_type == '':            
            conditions.append(f"type IN ('bulkorder', 'preorder')")            
        else:            
            conditions.append(f"type = '{product_type}'")
        
        # Add condition for pending_allocation
        if pending_allocation == 'true':
            conditions.append(f"""
                (SELECT COUNT(DISTINCT pol.po_id) 
                 FROM bo_purchase_order_lineitems AS pol 
                 JOIN bo_purchase_orders AS po ON po.po_id = pol.po_id 
                 WHERE po.status NOT IN ('deleted', 'completed') 
                 AND pol.bop_id = bop.bop_id 
                 AND (pol.status IS NULL OR pol.status NOT IN ('cancelled', 'completed'))) > 0
            """)

        if len(conditions) > 0:
            base_query += " WHERE " + " AND ".join(conditions)
            count_query += " WHERE " + " AND ".join(conditions)

        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ["bop_id", "date_created", "product_name", "sku", "status", "is_qty_locked", "pending_po_count", "total_po_count", "total_pending_case_qty", "total_available_case_qty"]:                
                base_query += f" ORDER BY {sort_array[0]} {sort_direction}"

        offset = (page - 1) * limit
        if page and limit:
            base_query += f" LIMIT {limit} OFFSET {offset}"

        result_count = conn.execute(text(count_query))
        total_count = int(result_count.scalar())

        result = conn.execute(text(base_query))

        data = []
        for row in result.fetchall():
            user_data = user_db.fetch_user_by_username(row[7])
            if user_data:
                name = user_data.get('name', '')
            else:
                name = ''
            image_url = row[4]
            if row[10] == 'bulkorder':
                query = {
                    "id": int(row[2]),   
                }
                projection = {
                    "images": {
                        "$elemMatch": {
                            "is_thumbnail": True
                        }
                    }
                }
                res = fetch_one_document_from_storefront_collection(store['id'], 'products', query, projection)
                if res:
                    images = res.get('images', [])
                    if len(images) > 0:
                        image = images[0].get('url_thumbnail', '')
                        if image != '':
                            image_url = image

            products_data = {
                'bop_id': row[0],
                'sku': row[1],
                'product_id': row[2],
                'product_name': row[3],
                'product_image': image_url,
                'status': row[5],
                'orders': row[6],
                'created_by': name,
                'is_qty_locked': row[8],
                'date_created': convert_to_timestamp(row[9]),
                'type': row[10],
                'min_market_price': row[11],
                'is_marketing_product': row[12],
                'total_po_count': row[13],
                'pending_po_count': row[14],
                'total_pending_case_qty': row[15],
                'total_available_case_qty': row[16]
            }
            data.append(products_data)

        data = calculatePaginationData(data, page, limit, total_count)
        response['data'] = data
        response['status'] = 200
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response


def get_all_products():
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        query = """SELECT bop_id, bc_name AS product_name FROM bo_bulk_order_products where status NOT IN ('archived') order by bc_name;"""  
        result = conn.execute(text(query))    
        data = result.fetchall()
        products = {}   
        if data:
            for d in data:
                products[d[0]] = d[1]

        response['data'] = products
        response['status'] = 200
    finally:
        if conn:
            conn.close()
    return response