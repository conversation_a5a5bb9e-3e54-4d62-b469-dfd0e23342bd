from flask import request, make_response
import logging
from api import APIResource
import traceback
import os
from utils.common import resize_image
from flask import send_file, make_response
from io import BytesIO

class StaticImageAPI(APIResource):

    def get_executor(self, request, image_type, file_name):

        if image_type and file_name:
            file_path = '/app/images/'
            if image_type == "banner":
                file_path = file_path + file_name
            elif image_type == "blog":
                file_path = file_path + "blogs/images/" + file_name
            elif image_type == "categories":
                file_path = file_path + "categories/images/" + file_name
            elif image_type == "brands":
                file_path = file_path + "brands/images/" + file_name
            elif image_type == "appbuilder":
                file_path = file_path + "appbuilder/images/" + file_name
            elif image_type == "storeinfo":
                file_path = file_path + "storeinfo/images/" + file_name
            else:
                file_path = file_path + image_type + "/" + file_name
            
            res = resize_image(file_path, new_width=100)
           
            if res:
                img_byte_arr = BytesIO()
                res.save(img_byte_arr, format='PNG')
                img_byte_arr.seek(0)
                
                return send_file(img_byte_arr, mimetype='image/png')
            else:
                return make_response({'error': 'Failed to process image'}, 500)
            
        return make_response({'error': 'Image not found'}, 404)

    def get(self, image_type, file_name):
        return self.execute_open_api_request(request, self.get_executor, image_type, file_name)