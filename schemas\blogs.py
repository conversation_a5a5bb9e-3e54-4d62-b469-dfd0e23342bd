from schema import Schem<PERSON>, Or, Optional

blog_schema = Schema({
    'name': str,
    'description': str
})

blog_update_schema = Schema({
    'name': str,
    'description': str,
    'status': str,
    'status_update': str
})

author_schema = Schema({
    'name': str,
    'description': str
})

author_update_schema = Schema({
    'name': str,
    'description': str,
    'status': str,
    'status_update': str
})

category_schema = Schema({
    'name': str,
    'description': str
})

category_update_schema = Schema({
    'name': str,
    'description': str,
    'status': str,
    'status_update': str
})