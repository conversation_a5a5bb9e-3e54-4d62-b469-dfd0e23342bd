import json
import logging
import traceback
import new_pgdb
from new_pgdb import order_consignment_db, blocked_orders_db
from plugin import bc_customers, bc_order
from mongo_db import order_db, customer_db, user_db
from sqlalchemy import text
from utils import redis_util,product_util, auth_util
import datetime
import new_mongodb
import task
from new_mongodb import AdminAppNotification

from utils.common import parse_json

logger = logging.getLogger()

def process_order_updated(store, payload):
    try:
        if store:
            data = payload['data']
            order_id = data['id']
            res = bc_order.fetch_order(store, order_id)
            if res.status_code < 299:
                logger.info('Updating Order...')
                order = res.json()
                order_db.update_order(store, order)
                customer_id = order['customer_id']
                process_customer_points_update(
                    store, order['total_inc_tax'], customer_id)
                if 'coupons' in order:
                    if 'resource' in order['coupons']:
                        url_value = order['coupons']['resource']
                        url_value = 'v2' + url_value  
                        coupons_res = bc_order.fetch_order_coupon(store, url_value)                        
                        if coupons_res.status_code == 200:
                            coupons_array = coupons_res.json()                            
                            for coupon in coupons_array:
                                if 'coupon_id' in coupon:
                                    coupon_id_value = coupon['coupon_id']
                                    c_data = {}
                                    c_data['coupon_id'] = coupon_id_value
                                    c_data['customer_id'] = customer_id
                                    customer_db.update_customer_coupon_status(store, c_data)

    except Exception as e:
        logger.error(traceback.format_exc())

def process_customer_points_update(store, order_amount, customer_id):    
    total_amount = float(order_amount)
    total_amount = round(total_amount)
    points = total_amount * 2
    customer_res = customer_db.fetch_customer_by_id(store, customer_id)
    if customer_res:
        old_points = int(customer_res['loyalty_points']) if customer_res.__contains__("loyalty_points") else 0
        add_loyalty_reward_history(store, old_points, points, customer_id)
        customer_res['loyalty_points'] = (
            int(customer_res['loyalty_points']) if customer_res.__contains__("loyalty_points") else 0) + int(points)
        customer_db.update_customer_by_id(store, customer_res)

def add_product_details(store, body, order_details, order_id): 
  if body['type']=='consignment' or body['type']=='on-hold':
    res = order_consignment_db.OrderConsignmentSchema.get_order(store, order_id) 
    if res == 0:
      customerres=bc_customers.fetch_customer_by_id(store,order_details['customer_id'])
      customer = redis_util.get_salesforce_customer(store['id'], order_details['customer_id'])
      result = {}
      if customer:
        rep = customer['rep']
        if rep:
            result['name'] = rep['Name']
            result['email'] = rep['Email']
            
      customer=customerres.json()
      customerData=customer['data'][0]
      consignments=order_details['consignments'][0]
      lineItems=consignments['shipping'][0]['line_items']
      for orderProduct in lineItems:
        productOptions=orderProduct['product_options']
        optionlist={}
        for options in productOptions:
            optionlist[options['display_name']]=options['display_value']
        json_string = json.dumps(optionlist)
        created_by=''
        if 'created_by' in body:
            user = user_db.fetch_user_by_username(body['created_by'])        
            created_by = str(user['_id'])+':'+ user['name']
        data = {}
        data['order_id'] = order_id
        data['customer_id'] = order_details['customer_id']
        data['customer_name'] = customerData['first_name']+' '+customerData['last_name']
        data['customer_email'] = customerData['email']
        data['order_lineitem_id'] = orderProduct['id']
        data['product_id'] = orderProduct['product_id']
        data['parent_sku'] =  None
        data['variant_id'] = orderProduct['variant_id']
        data['variant_sku'] = orderProduct['sku']
        data['options'] = json_string
        data['order_type'] = body['type']
        data['quantity']=orderProduct['quantity']
        if 'name' and 'email' in result:
           data['rep_name'] =result['name']
           data['rep_user_id'] = result['email']
        else:
           data['rep_name'] =''
           data['rep_user_id'] = '' 
        data['created_by'] = created_by
        data['created_at'] = (datetime.datetime.utcnow().timestamp())
        data['modified_by'] = created_by
        data['modified_at'] = (datetime.datetime.utcnow().timestamp())
        order_consignment_db.OrderConsignmentSchema.add_products(store, data)
    else:
        order_consignment_db.OrderConsignmentSchema.update_products(store, order_id, body['type'])

  else:
        order_consignment_db.OrderConsignmentSchema.delete_products(store, order_id)

def update_product_type(store, order_id, username):
    result, role_id, user_id, current_user = auth_util.get_user_by_username(username)

    data = blocked_orders_db.get_order_details_by_order_id(store, order_id, current_user)
    
    if 'data' in data and data['data']['order_id']:
        res  = blocked_orders_db.BlockedOrdersSchema.add_blocked_order(store, data['data'])   
        if res:
            task.send_task(task.GENERATE_ADMIN_APP_NOTIFICATION_TASK, args=(store['id'], AdminAppNotification.BLOCK_ORDER_CREATED, order_id))
            return {"status": 200, "message": "Order updated successfully"}
    else:
        return {"status": 404, "message": "Order not found"}

def get_product_details(store,body):
    return order_consignment_db.OrderConsignmentSchema.get_product_by_id(store, body['id'])

def is_product_available(store,body):
    return order_consignment_db.OrderConsignmentSchema.is_product_available_by_id(store, body['id'])

def get_variant_report(store, body):
    return order_consignment_db.OrderConsignmentSchema.get_report_by_product_id(store, body['product_id'])

def format_price(price):
    if abs(price) >= 1000:
        formatted_price = "{:.1f}k".format(price / 1000)
    else:
        formatted_price = "{:.2f}".format(price)
    return formatted_price

def get_product_info(store, body):
    conn = new_pgdb.get_connection(store['id'])
    response = {
        "status": 400,
        "message": "Something went wrong."
    }
    try:
        consignment_sum = order_consignment_db.OrderConsignmentSchema.get_consignment_sum_for_product(store, body['id'])
        onhold_sum = order_consignment_db.OrderConsignmentSchema.get_onhold_sum_for_product(store, body['id'])
        product = product_util.get_product_by_id(store,body['id'])
        parent_sku=product['sku']
        incoming_qty=0
    
        query = text(
            f"SELECT SUM(quantity_incoming) AS total_quantity FROM skuvault_catalog WHERE parent_sku = :parent_sku "
            
        )
        user = conn.execute(query.params(parent_sku=parent_sku))
        result = user.fetchone()
        quantity = 0  
        if result[0] is not None:
            quantity = result[0]
            incoming_qty=quantity
    
        product_json=parse_json(product)
        variants=product_json['variants']
        inventory_level_sum=0
        for variant in variants:
            inventory_level_sum+=variant['inventory_level']
        cost=0
    
        query = text(f"SELECT cost FROM skuvault_catalog WHERE parent_sku = :parent_sku LIMIT 1 ")
        user = conn.execute(query.params(parent_sku=parent_sku))
        result = user.fetchone()
        quantity = None  
        if result:
            quantity = result[0]
            cost=quantity
        consignment_sum_cost=consignment_sum*cost
        onhold_sum_cost=onhold_sum*cost
        totalStock=consignment_sum+onhold_sum+incoming_qty+inventory_level_sum
        totalValuation=consignment_sum_cost+onhold_sum_cost+(inventory_level_sum*cost)
        incoming_cost=incoming_qty*cost
        total_orders=order_consignment_db.OrderConsignmentSchema.count_unique_orders(store, body['id'])
        total_vendors=order_consignment_db.OrderConsignmentSchema.count_unique_customers(store, body['id'])
        records={}
        records['consignment_sum']=consignment_sum
        records['onhold_sum']=onhold_sum
        records['incoming_qty']=incoming_qty
        records['inventory_level_sum']=inventory_level_sum
        records['total_stock']=totalStock
        records['total_consignment_cost']=consignment_sum_cost
        records['total_consignment_cost_display']=format_price(consignment_sum_cost)
        records['consignment_vendors']=total_vendors
        records['total_onhold_cost']=onhold_sum_cost
        records['total_onhold_cost_display']=format_price(onhold_sum_cost)
        records['onhold_orders']=total_orders
        records['total_incoming_cost']=incoming_cost
        records['total_incoming_cost_display']=format_price(incoming_cost)
        records['total_valuation']=totalValuation
        records['total_valuation_display']=format_price(totalValuation)

        response['status'] = 200
        response['data'] = records
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close() 

    return response

def add_loyalty_reward_history(store, old_points, new_points, customer_id):   
    data = {}
    data['customer_id'] = int(customer_id)
    data['balance'] = int(old_points) + int(new_points)
    data['earned/used'] = int(new_points)
    data['created_at'] = int(datetime.datetime.utcnow().timestamp())
    data['description'] = 'Earned ' + str(new_points) + ' Points with order.'
    customer_db.add_customer_loyalty_history(store, data)

def _get_variant_prices_for_ownerunused(store_id, variant_ids, price_list_id):
    variant_prices = {}
    variant_ids = [int(vid) for vid in variant_ids]

    collection_name = "static_price_lists" if price_list_id in [51, 52, 53] else "product_price_lists"

    # Query database to fetch prices for each variant in the specified price list
    query = {
        "variants.variant_id": {"$in": variant_ids}
    }
    projection = {
        "variants.variant_id": 1,
        "variants.price_list": 1
    }

    results = new_mongodb.fetchall_documents_from_admin_collection(store_id, collection_name, query, projection)

    # Iterate over the results to extract prices
    for product in results:
        for variant in product.get("variants", []):  # Safely access variants
            if variant.get("variant_id") in variant_ids:
                # Iterate over price_list to find the matching price_list_id
                for price in variant.get("price_list", []):
                    if price.get("price_list_id") == int(price_list_id):
                        # Add price to the variant_prices dictionary
                        variant_prices[variant["variant_id"]] = {
                            "price": price["price"],
                            "pricing_group": None
                        }
                        break  # Exit once price_list_id match is found for this variant

    return variant_prices


def get_variant_prices(store_id, variant_ids, price_list_id, customer_id):
    """
    Fetch product prices for multiple variant_ids based on prioritization:
    1. Check product_customer_price table in PostgreSQL using product_id.
    2. If not found, check product_price_lists collection in MongoDB using variant_id and price_list_id.
       - Special case: if price_list_id = 52, fallback to 13 if not found.

    Parameters:
    - store_id: Store identifier
    - variant_ids: List of variant identifiers
    - price_list_id: Price list identifier (for MongoDB lookup)

    Returns:
    A dictionary like:
        {
            variant_id: {
                "price": price or None,
                "pricing_group": 'CSP' or price list name or None
            },
            ...
        }
    """
    conn = new_pgdb.get_connection(store_id)
    try:
        result_dict = {}

        # Step 1: Fetch product_id for all variant_ids in a single MongoDB query
        collection = "products"
        query = {
            "variants.id": {"$in": [int(vid) for vid in variant_ids]}
        }
        projection = {
            "id": 1,
            "variants.id": 1
        }

        products = new_mongodb.fetchall_documents_from_storefront_collection(store_id, collection, query, projection)

        # Build a variant_id to product_id mapping
        variant_product_map = {}
        for product in products:
            product_id = product.get("id")
            for variant in product.get("variants", []):
                vid = variant.get("id")
                if vid in variant_ids:
                    variant_product_map[vid] = product_id

        # Step 2: Process each variant_id
        for variant_id in variant_ids:
            product_id = variant_product_map.get(variant_id)
            if not product_id:
                result_dict[variant_id] = {"price": None, "pricing_group": None}
                continue

            # Check product_customer_price in PostgreSQL
            try:
                query = """
                    SELECT price
                    FROM product_customer_price
                    WHERE product_id = :product_id AND customer_id = :customer_id
                    LIMIT 1;
                """
                result = conn.execute(text(query), {"product_id": product_id, "customer_id": customer_id}).fetchone()
            except Exception:
                result = None

            if result and result[0] is not None:
                result_dict[variant_id] = {
                    "price": result[0],
                    "pricing_group": "CSP"
                }
                continue  # No need to check Mongo if found in PostgreSQL

            # Step 3: Check product_price_lists in MongoDB
            if not price_list_id:
                result_dict[variant_id] = {"price": None, "pricing_group": None}
                continue

            price_list_query = {
                "variants.variant_id": int(variant_id),
                "variants.price_list.price_list_id": {
                    "$in": [52, 13] if int(price_list_id) == 52 else [int(price_list_id)]
                }
            }
            price_list_projection = {
                "variants.variant_id": 1,
                "variants.price_list": 1
            }

            price_doc = new_mongodb.fetch_one_document_from_admin_collection(
                store_id, "product_price_lists", price_list_query, price_list_projection
            )

            def find_price_from_variant(variant_data, target_price_list_id):
                for price_info in variant_data.get("price_list", []):
                    if price_info.get("price_list_id") == target_price_list_id:
                        return price_info.get("price"), price_info.get("name")
                return None, None

            price = None
            pricing_group = None

            if price_doc:
                for variant in price_doc.get("variants", []):
                    if variant.get("variant_id") == int(variant_id):
                        price_list_id_int = int(price_list_id)

                        # Define fallback chains
                        fallback_map = {
                            14: [14, 52, 13], # TCD fallback to VIP and then Distributor
                            15: [15, 52, 13], # MVD fallback to VIP and then Distributor
                            52: [52, 13] # VIP fallback to Distributor
                        }

                        fallback_chain = fallback_map.get(price_list_id_int, [price_list_id_int])
                        # Iterate over fallback chain
                        for pid in fallback_chain:
                            price, pricing_group = find_price_from_variant(variant, pid)
                            if price is not None:
                                break  # Found a valid price, stop here

                        break  # done with this variant
            result_dict[variant_id] = {
                "price": price,
                "pricing_group": pricing_group
            }

        return result_dict
    except Exception as e:
        logger.error(traceback.format_exc())
        return {}
    finally:
        conn.close()

def get_accepted_prices(store_id, variant_ids, price_list_ids, customer_id):
    """
    Optimized version: Fetch product prices for multiple variant_ids based on prioritization:
    1. Check product_customer_price table in PostgreSQL using product_id.
    2. Check product_price_lists collection in MongoDB using variant_id and price_list_ids.
    """
    conn = new_pgdb.get_connection(store_id)
    try:
        result_dict = {}

        variant_ids = [int(vid) for vid in variant_ids]
        price_list_ids = [int(pid) for pid in price_list_ids]

        # Step 1: Fetch product_id for all variant_ids
        collection = "products"
        query = {"variants.id": {"$in": variant_ids}}
        projection = {"id": 1, "variants.id": 1}
        products = new_mongodb.fetchall_documents_from_storefront_collection(store_id, collection, query, projection)

        variant_product_map = {}
        for product in products:
            product_id = product.get("id")
            for variant in product.get("variants", []):
                vid = variant.get("id")
                if vid in variant_ids:
                    variant_product_map[vid] = product_id

        # Step 2: Fetch all customer prices from PostgreSQL in one batch
        product_ids = list(set(variant_product_map.values()))
        customer_prices = {}

        if product_ids:
            try:
                sql = """
                    SELECT product_id, price
                    FROM product_customer_price
                    WHERE product_id = ANY(:product_ids) AND customer_id = :customer_id;
                """
                result = conn.execute(text(sql), {"product_ids": product_ids, "customer_id": customer_id}).fetchall()
                for row in result:
                    customer_prices[row[0]] = row[1]
            except Exception:
                pass  # continue silently if PostgreSQL error

        # Step 3: Fetch all needed price list docs in MongoDB at once
        price_list_query = {
            "variants.variant_id": {"$in": variant_ids},
            "variants.price_list.price_list_id": {"$in": price_list_ids}
        }
        price_list_projection = {
            "variants.variant_id": 1,
            "variants.price_list": 1
        }
        price_docs = new_mongodb.fetchall_documents_from_admin_collection(
            store_id, "product_price_lists", price_list_query, price_list_projection
        )

        # Build a quick lookup for variant_id -> price info
        variant_price_map = {}

        for doc in price_docs:
            for variant in doc.get("variants", []):
                vid = variant.get("variant_id")
                if vid in variant_ids:
                    if vid not in variant_price_map:
                        variant_price_map[vid] = []
                    for price_info in variant.get("price_list", []):
                        if price_info.get("price_list_id") in price_list_ids:
                            price = price_info.get("price")
                            if price is not None:
                                variant_price_map[vid].append(price)

        # Step 4: Build the final result
        for variant_id in variant_ids:
            accepted_prices = []
            product_id = variant_product_map.get(variant_id)

            if not product_id:
                result_dict[variant_id] = {"accepted_prices": accepted_prices}
                continue

            # Priority 1: Add PostgreSQL customer price
            customer_price = customer_prices.get(product_id)
            if customer_price is not None:
                accepted_prices.append(customer_price)

            # Priority 2: Add MongoDB price list prices
            mongo_prices = variant_price_map.get(variant_id, [])
            accepted_prices.extend(mongo_prices)

            result_dict[variant_id] = {"accepted_prices": accepted_prices}

        return result_dict
    except Exception as e:
        logger.error(traceback.format_exc())
        return {}
    finally:
        conn.close()


def get_cost_of_line_items(store_id, variant_ids, order_id):
    conn = new_pgdb.get_connection(store_id)
    try:
        query = """
            SELECT variant_id, sv_cost FROM order_line_items WHERE order_id = :order_id AND variant_id IN :variant_ids
        """
        result = conn.execute(text(query), {"order_id": order_id, "variant_ids": tuple(variant_ids)}).fetchall()
        return {
            row[0]: float(row[1]) if row[1] is not None else 0.0
            for row in result
        } if result else {}
    except Exception:
        logger.error(traceback.format_exc())
        return {}
    finally:
        conn.close()

def fetch_customer_id_from_order(store_id, order_id):
    conn = new_pgdb.get_connection(store_id)
    try:
        query = """SELECT customer_id FROM orders WHERE order_id = :order_id"""
        result = conn.execute(text(query), {"order_id": order_id}).fetchone()
        return result[0] if result else None
    finally:
        conn.close()

def get_classifications(store_id, skus):
    conn = new_pgdb.get_connection(store_id)
    try:
        query = """SELECT sku, classification FROM skuvault_catalog WHERE sku IN :skus"""
        result = conn.execute(text(query), {"skus": tuple(skus)}).fetchall()
        classifications_map = {}
        for row in result:
            classifications_map[row[0]] = row[1]
        return classifications_map
    except Exception:
        logger.error(traceback.format_exc())
        return {}
    finally:
        conn.close()

