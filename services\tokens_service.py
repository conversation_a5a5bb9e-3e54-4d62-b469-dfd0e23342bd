from services import Service
from utils.common import parse_json
from bson import ObjectId
import json


class Configs(Service):

    def get_all(self, store):
        try:
            result = super().find_all()
            result = self.processList(result)
            extracted_data = []
            for entry in result: 
                formatted_entry = {
                    "store_hash": entry["bc_config"]["store_hash"],
                    "channel_id": entry["bc_config"]["channel_id"],
                    "client_id": entry["bc_config"]["api"]["client_id"],
                    "access_token": entry["bc_config"]["api"]["access_token"],
                    "id": entry["id"]
                }
                extracted_data.append(formatted_entry)
            data = {"data": extracted_data}
            return data
        except Exception as e:
            return {"error": str(e)}
    
    def update_data(self, store, payload):
        try:
            all_data = self.get_all(store)
            extracted_id = all_data['data'][0]['id']

            result = super().update_one({"_id": ObjectId(str(extracted_id))}, { "$set": 
                    {
                        "bc_config.store_hash": payload['store_hash'],
                        "bc_config.channel_id": payload['channel_id'],
                        "bc_config.api.client_id": payload['client_id'],
                        "bc_config.api.access_token": payload['access_token']
                    }
                })
            if result.modified_count > 0:
                success_message = "Update successful."
                return {"message": success_message}
            else:
                error_message = "Did not Update anything."
                return {"message": error_message}

        except KeyError as ke:
            error_message = f"KeyError: {ke}"
            return {"error": error_message}

        except Exception as e:
            error_message = f"An unexpected error occurred: {str(e)}"
            return {"error": error_message}