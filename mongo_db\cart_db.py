
import logging
import datetime
from bson import ObjectId
from graphql import products_variant_query
import mongo_db


from utils import bc, new_cart_util
from utils.common import convert_to_timestamp

logger = logging.getLogger()

CART_COLLECTION = "cart"
COMPLEX_RULES_COLLECTION = "products_complex_rules"

def get_cart_model(customer_id=None, cart_id=None):
    model = {
        ""
        "customer_id": customer_id,
        "status": "active",
        "currency": {"code": "USD"},
        "line_items": {},
        "products": {},
        "bc_cart_id": None,
        "created_at": None,
        "modified_at": None,
        "base_amount": 0,
        "tax_included": 0,
        "discount_amount": 0,
        "cart_amount": 0,
        "coupons": "",
        "discounts": 0
    }
    return model

def create_cart(store, cart):
    db = mongo_db.get_store_db_client(store)
    result = db[CART_COLLECTION].insert_one(cart)
    return result

def create_new_cart(store, customer_id):
    current_time = int(datetime.datetime.utcnow().timestamp())
    cart = get_cart_model(customer_id=int(customer_id))
    cart["created_at"] = current_time
    cart["modified_at"] = current_time
    result = create_cart(store, cart)
    return result.inserted_id

def fetch_customer_active_cart(store, customer_id):
    cart = None
    if customer_id:
        db = mongo_db.get_store_db_client(store)
        cart = db[CART_COLLECTION].find_one({"customer_id": int(customer_id),"status": "active"})
        if cart:
            cart = mongo_db.process_data(cart)
        
    return cart

def fetch_customer_active_cart_summary(store, customer_id):
    cart_summary = {}
    
    if customer_id:
        db = mongo_db.get_store_db_client(store)
        cart = db[CART_COLLECTION].find_one({"customer_id": int(customer_id),"status": "active"})
        if cart:
            cart_summary['id'] = str(cart['_id'])
            cart_summary['customer_id'] = cart['customer_id']
            cart_summary['currency'] = cart['currency']['code']
            cart_summary['line_item_count'] = len(cart['line_items'])
            cart_summary['cart_amount'] = cart['cart_amount']

    return cart_summary

def fetch_cart_by_id(store, cart_id):
    db = mongo_db.get_store_db_client(store)
    cart = db[CART_COLLECTION].find_one({"_id": ObjectId(str(cart_id))})
    if cart:
        cart = mongo_db.process_data(cart)
    return cart

def fetch_cart_by_bc_cart_id_and_customer_id(store, customer_id, bc_cart_id):
    db = mongo_db.get_store_db_client(store)
    cart = db[CART_COLLECTION].find_one({"customer_id": customer_id, "bc_cart_id": bc_cart_id})
    if cart:
        cart = mongo_db.process_data(cart)
    return cart

def update_cart(store, cart):
    documents = []
    cart_id = cart.get("id", None)
    if not cart_id:
        cart_id = cart.get("_id", None)
    else:
        del cart["id"]
    cart["_id"] = ObjectId(str(cart_id))
    documents.append(cart)
    mongo_db.upsert_documents(store, CART_COLLECTION, documents)
    cart["id"] = str(cart["_id"])
    del cart["_id"]
    return cart

# def update_cart(store, cart):
#     db = mongo_db.get_store_db_client(store)
#     query = {"_id": ObjectId(str(cart["id"]))}
#     db[CART_COLLECTION].replace_one(query,cart,upsert=True)

def update_cart_lineitems(store, cart):
    cart_id = cart["id"]
    cart_lineitems = cart["lineitems"]
    cart_products = cart["products"]
    bc_cart_id = cart["bc_cart_id"]
    db = mongo_db.get_store_db_client(store)
    query = {"_id": ObjectId(str(cart["id"]))}
    current_time = int(datetime.datetime.utcnow().timestamp())
    update = { 
                "$set" : { 
                    "line_items": cart_line_items, 
                    "products": cart_products,
                    "bc_cart_id": bc_cart_id,
                    "modified_at": current_time
                }
            }
    db[CART_COLLECTION].update_one(query, update)

def delete_all_carts(store):
    db = mongo_db.get_store_db_client(store)
    db[CART_COLLECTION].delete_many({})

def delete_cart(store, cart_id):
    db = mongo_db.get_store_db_client(store)
    db[CART_COLLECTION].delete_many({"_id": ObjectId(str(cart_id))})

def update_bc_cart(store, cart, bc_cart, sku_id):
    db = mongo_db.get_store_db_client(store)
    query = {"_id": ObjectId(str(cart["id"]))}
    update = { 
                "$set" : { 
                    "bc_cart_id": bc_cart['id'],
                    "line_item_bc_id": sku_id
                }
            }
    db[CART_COLLECTION].update_one(query, update, upsert=True)

def update_bc_cart_data(store, cart, bc_cart):
    db = mongo_db.get_store_db_client(store)
    query = {"_id": ObjectId(str(cart["id"]))}
    update = { 
                "$set" : { 
                    "bc_cart_id": bc_cart['id'],
                    "bc_cart": bc_cart
                }
            }
    db[CART_COLLECTION].update_one(query, update, upsert=True)

     
def fetch_line_items_by_customer_id(store, customer_id, channel, bc_cart_id, filter, sort_params):
    db = mongo_db.get_store_db_client(store)

    if channel == 'express':
        cart = db[CART_COLLECTION].find_one({"customer_id": customer_id})
    elif channel == 'midwest':
        if bc_cart_id:
            cart = new_cart_util.get_bc_cart(store, bc_cart_id)
            # if not cart:
            #     cart = db[CART_COLLECTION].find_one({"customer_id": customer_id})
            #     channel = 'express'
    
    created_date = None
    updated_date = None
    line_items = []
    variant_ids = []
    product_ids = []
    grand_total = 0
    item_count = 0
    cart_id = ""
    if cart:
        if channel == 'express':
            created_date = cart['created_at'] if cart else None
            updated_date = cart['modified_at'] if cart else None
            cart_id = str(cart['_id'])
            line_items, grand_total, product_ids, variant_ids, item_count = _process_local_cart_line_items(cart, filter)
        elif channel == 'midwest':
            created_date = convert_to_timestamp(cart['created_time']) if cart else None
            updated_date = convert_to_timestamp(cart['updated_time']) if cart else None
            line_items, grand_total, product_ids, variant_ids, item_count = _process_bc_cart_line_items(cart, filter)
    
    min_max_rules = {}
    available_quantity = {}

    graphql_query = products_variant_query.get_query(product_ids, variant_ids)
    status, gql_res = bc.process_bc_graphql_request(store, graphql_query)

    if status == 200 and 'data' in gql_res and len(gql_res['data']['site']['products']['edges']):
            products = gql_res['data']['site']['products']['edges']
            for product in products:
                min_purchase_quantity = product['node']['minPurchaseQuantity']
                max_purchase_quantity = product['node']['maxPurchaseQuantity']
                min_max_rules[product['node']['entityId']] = {
                    'min_purchase_quantity': min_purchase_quantity,
                    'max_purchase_quantity': max_purchase_quantity
                }

                variants = product['node']['variants']['edges']
                for variant in variants:
                    available_quantity[variant['node']['entityId']] = variant['node']['inventory']['aggregated']['availableToSell']
    
    # Assign min and max purchase quantities to line items
    for line_item in line_items:
        product_id = line_item["product_id"]
        variant_id = line_item["variant_id"]
        if product_id in min_max_rules:
            line_item["min_purchase_quantity"] = min_max_rules[product_id].get('min_purchase_quantity', 0)
            line_item["max_purchase_quantity"] = min_max_rules[product_id].get('max_purchase_quantity', 0)
        
        if variant_id in available_quantity:
            line_item["available_quantity"] = available_quantity[variant_id]

    if sort_params:
        sorting_key = sort_params[0]
        sort_order = sort_params[1]
    
    sorting_key_funcs = {
        "sku": lambda x: x["sku"],
        "product_name": lambda x: x["product_name"],
        "quantity": lambda x: x["quantity"],
        "price": lambda x: x["price"],
        "total_cost": lambda x: x["total_cost"]
    }
    
    # Select sorting key function based on provided sorting key
    sorting_key_func = sorting_key_funcs.get(sorting_key, lambda x: x["sku"])
    
    # Sort line items using the selected sorting key function and sort order
    line_items.sort(key=sorting_key_func, reverse=(sort_order == '-1'))

    data = {
        "line_items": line_items,
        "grand_total": grand_total,
        "line_item_count": item_count,
        "created_at": created_date,
        "updated_at": updated_date, 
        "local_cart_id": cart_id
    }
        
    return data

def _process_local_cart_line_items(cart, filter):
    line_items = []
    variant_ids = []
    product_ids = []
    grand_total = 0
    item_count = 0
    cart = mongo_db.process_data(cart)
    for sku, item in cart.get("line_items", {}).items():
        product_name = item.get("name", "")
        sku = item.get("sku", "")
        item_count += 1
        line_item = {
            "bc_item_id": item.get("bc_cart_id", ''),
            "sku": sku,
            "product_name": product_name,
            "quantity": item.get("quantity", 0),
            "price": item.get("sale_price", 0),
            "total_cost": item.get("extended_sale_price", 0),
            "product_id": item.get("product_id", ''),
            "variant_id": item.get("variant_id", ''),
            "is_existing": True
        }
        if item.get("quantity", 0) > 0:
            variant_ids.append(item.get("variant_id", ''))
            product_ids.append(item.get("product_id", ''))

        if filter and filter.lower() in product_name.lower():
            line_items.append(line_item)
            grand_total += line_item["total_cost"]
        if filter and filter.lower() in sku.lower():
            line_items.append(line_item)
            grand_total += line_item["total_cost"]
        elif not filter:
            line_items.append(line_item)
            grand_total += line_item["total_cost"]
    
    return line_items, grand_total, product_ids, variant_ids, item_count

def _process_bc_cart_line_items(cart, filter):
    line_items = []
    variant_ids = []
    product_ids = []
    grand_total = 0
    item_count = 0
    cart = mongo_db.process_data(cart)
    for sku, items in cart.get("line_items", {}).items():
       if items and len(items):
        for item in items:
            item_count += 1
            product_name = item.get("name", "")
            sku = item.get("sku", "")
            line_item = {
                "bc_item_id": item.get("id", ''),
                "sku": sku,
                "product_name": product_name,
                "quantity": item.get("quantity", 0),
                "price": item.get("sale_price", 0),
                "total_cost": item.get("extended_sale_price", 0),
                "product_id": item.get("product_id", ''),
                "variant_id": item.get("variant_id", ''),
                "is_existing": True
            }
            if item.get("quantity", 0) > 0:
                variant_ids.append(item.get("variant_id", ''))
                product_ids.append(item.get("product_id", ''))

            if filter and filter.lower() in product_name.lower():
                line_items.append(line_item)
                grand_total += line_item["total_cost"]
            if filter and filter.lower() in sku.lower():
                line_items.append(line_item)
                grand_total += line_item["total_cost"]
            elif not filter:
                line_items.append(line_item)
                grand_total += line_item["total_cost"]
    
    return line_items, grand_total, product_ids, variant_ids, item_count

def fetch_products_complex_rules_by_id(store, product_ids):
    db = mongo_db.get_store_db_client(store)
    rules = db[COMPLEX_RULES_COLLECTION].find({"product_id": {"$in": product_ids}})
    processed_rules = []
    if rules:        
        processed_rules = [rule for rule in rules]  # Example processing
    
    return processed_rules    