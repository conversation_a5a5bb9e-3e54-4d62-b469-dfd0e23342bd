from bson import ObjectId
from new_mongodb import delete_documents_from_admin_collection, fetch_one_document_from_admin_collection, fetch_one_document_from_storefront_collection, fetchall_documents_from_admin_collection \
   ,fetchall_documents_from_storefront_collection \
    ,process_documents \
    ,insert_document_in_storefront_collection \
    ,delete_documents_from_storefront_collection, update_document_in_admin_collection \
    ,update_document_in_storefront_collection

from utils.common import parse_json
from datetime import datetime


MAIL_TYPE_DB = "mail_templates_type"
MAIL_TEMPLATES_DB = "email_templates"


def get_types(store_id, body):
    result = []

    if body:
        type_id = body['type_id']
        query = {"_id": ObjectId(str(type_id))}
        type = fetch_one_document_from_storefront_collection(store_id, MAIL_TYPE_DB, query)
        result.append({
            'id': type['id'],
            'name': type['name'],
            'short_code': type['short_code'],
            'created_at': type['created_at'],
            'updated_at': type['updated_at'],
            'status': type['status'],
        })

    else:
        types = fetchall_documents_from_storefront_collection(store_id, MAIL_TYPE_DB, {})
        types = parse_json(process_documents(types))

        for type in types:
            result.append({
                'id': type['id'],
                'name': type['name'],
                'short_code': type['short_code'],
                'created_at': type['created_at'],
                'updated_at': type['updated_at'],
                'status': type['status'],
            })

    return result

def get_all_templates(store_id, body):
    response = {
        "status": 400,
    }
    result = []
    if body and 'id' in body:
        id = body['id']
        query = {"_id": str(id)}
        type = fetch_one_document_from_admin_collection(store_id, MAIL_TEMPLATES_DB, query)
        if type:
            test = {
                "name": type['name'],
                "smtp_port": type['smtp_port'],
                "smtp_host": type['smtp_host'],
                "url": type['url'],
                "password": type['password'],
                "email": type['email'],
                "subject": type['subject'],
                "body": type['body'],
                "short_code": type['short_code'],
                "id": type['_id']
            }
            result.append(test)
    else:
        types = fetchall_documents_from_admin_collection(store_id, MAIL_TEMPLATES_DB, {})
        result = parse_json(process_documents(types))

    if result:
        response['status'] = 200
        response['data'] = result
    else:
        response['status'] = 404
        response['message'] = 'No data found.'
    return response


def update_mail_template(store_id, body):  
    response = {
        "status": 400,
    }  
    if 'id' in body: 
        query = {"_id": str(body['id'])}
        update_body = {"$set":
            {
                "name": body['name'],
                "smtp_port": body['smtp_port'],
                "smtp_host": body['smtp_host'],
                "url":  body['url'],
                "password": body['password'],
                "email": body['email'],
                "subject": body['subject'],
                "body": body['body'],
                "short_code": body['id']
            } 
        }
        id = update_document_in_admin_collection(store_id, MAIL_TEMPLATES_DB, query, update_body)
        
        response['status'] = 200
        response['message'] = "Template updated successfully"      
    else:
        response['status'] = 400
        response['message'] = 'Please provide valid template id.'
    
    return response

def delete_mail_template(store_id, template_code):
    response = {
        "status": 400,
        "message": ''
    }
    if template_code and template_code != '':
        id = delete_documents_from_admin_collection(store_id, MAIL_TEMPLATES_DB, {"_id": str(template_code)})
        response['status'] = 200
        response['message'] = "Template deleted sucessfully!"
    else:
        response['status'] = 404
        response['message'] = "Template not found"
    return response

def checkForUniqueTypeName(store_id, name):
        name = name.replace(" ", "_").lower()
        types = fetchall_documents_from_storefront_collection(store_id, MAIL_TYPE_DB, {})
        types = parse_json(process_documents(types))
        for type in types:
            if type['short_code'] == name:
                return False
        return True


def create_type(store, body):
    response = {
        "status": 400
    }

    store_id = store['_id']
    is_unique_name = checkForUniqueTypeName(store_id, body['name'])

    if is_unique_name:
        body["created_at"] = int(datetime.utcnow().timestamp())
        body["updated_at"] = ""
        body["short_code"] = body['name'].replace(" ", "_").lower()

        id = insert_document_in_storefront_collection(store, MAIL_TYPE_DB, body)

        response['message'] = "Type created successfully"
        response['status'] = 200
    else:
        response['message'] = (
            "The provided type name has already been matched with other "
            "types. Please provide a different type name."
        )
        response['status'] = 409

    return response


def delete_by_id(store_id, type_id):
  query = {"_id": ObjectId(str(type_id))}
  return delete_documents_from_storefront_collection(store_id, MAIL_TYPE_DB, query)


def update_template(store_id, body, type_id=None):
    response = {
        "status": 400
    }
    short_code = ''
    if body['status_update'] == 'false':
        is_unique_name = checkForUniqueTypeName(body['name'])
        short_code = body['name'].replace(" ", "_").lower()
    else:
        is_unique_name = True
        short_code = body['name'].replace(" ", "_").lower()

    if is_unique_name:
        query = {"_id": ObjectId(str(type_id))}
        data = {
            "$set": {
                "name": body['name'],
                "short_code": short_code,
                "status": body['status'],
                "updated_at": int(datetime.utcnow().timestamp())
            }
        }
        id = update_document_in_storefront_collection(store_id, MAIL_TYPE_DB, query, data)
        response['message'] = "Template Updated successfully"
        response['status'] = 200
    else:
        response['message'] = (
            "The provided template name has already been matched with other "
            "templates. Please provide a different template name."
        )
        response['status'] = 409

    return response


def get_type(store_id, type_id=None):
    type_doc = fetch_one_document_from_storefront_collection(
        store_id,
        MAIL_TYPE_DB,
        {"_id": ObjectId(str(type_id))}
    )

    return {
        'id': type_doc['id'],
        'name': type_doc['name'],
        'short_code': type_doc['short_code'],
        'created_at': type_doc['created_at'],
        'updated_at': type_doc['updated_at'],
        'status': type_doc['status'],
    }
