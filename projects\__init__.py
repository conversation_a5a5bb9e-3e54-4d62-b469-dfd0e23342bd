from sqlalchemy import text
import pg_db


def build_projects_menu(username):
    query = f"""
        SELECT pa.project_id, pr.name as project_name, pr.bu_id, bu.name as bu_name 
        FROM agile_project_access pa, agile_projects pr, business_units bu
        WHERE pa.project_id = pr.id and pr.bu_id = bu.id and pa.username='{username}'
        order by bu.name;
    """
    project_menu = {}
    side_menu = {}
    permissions = {}
    conn = pg_db.get_connection()
    try:
        result = conn.execute(text(query))
        
        for row in result:
            bu_name = row[3]
            bu_id = row[2]
            root_item = side_menu.get(bu_name, {})
            root_item['label'] = bu_name
            root_item['bu_id'] = bu_id
            root_item['route'] = f"/agile/businessunits/{str(bu_id)}"
            root_item['icon'] = "orders.svg"

            children = root_item.get("children", {})

            project_id = row[0]
            project_name = row[1]

            children[project_name] = {
                "label": project_name,
                "route": f"/agile/projects/{str(project_id)}",
                "icon": "",
                "children": {}
            }

            permissions[f"{bu_name}-read"] = True
            permissions[f"{bu_name}-write"] = True
            permissions[f"{project_name}-read"] = True
            permissions[f"{project_name}-write"] = True

            root_item["children"] = children
            side_menu[bu_name] = root_item
        if len(side_menu) > 0:
            project_menu['label'] = "Projects"
            project_menu['route'] = f"/agile/projects"
            project_menu['icon'] = "orders.svg"
            project_menu['children'] = side_menu

    finally:
        conn.close()
    
    return project_menu, permissions 

def check_project_access(project_id, username):
    conn = pg_db.get_connection()
    try:
        query = text(
        f"""SELECT p.id
        FROM agile_projects p
        LEFT JOIN agile_project_access pa ON p.id = pa.project_id
        WHERE pa.username = :username AND p.id = :project_id AND pa.status = 'active'
        """)
        query = query.params(project_id=project_id, username=username)
        result = conn.execute(query).fetchone()
        if result:
            return True
        else:
            return False
    finally:
        if conn:
            conn.close()
