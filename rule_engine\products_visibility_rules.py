import requests
from flask import request
import logging
from datetime import datetime
import new_pgdb
from utils.common import paginate_data_postgres, parse_json, calculatePaginationData, convert_to_timestamp
from pg_db import get_connection
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from psycopg2.errors import UniqueViolation
from utils.bc import get_bc_categories, get_bc_promotions
from utils import store_util
from iam import user_service

logger = logging.getLogger()

def create_product_hide_rule(store, data):
    response = {"status": 400,}

    if 'product_sku' not in data or data['product_sku'] is None:
        response['message'] = "Product ID is missing or invalid."
        return response
    
    with new_pgdb.get_connection(store['id']) as conn:
        try:
            product_sku = data.get('product_sku')
            disable_promotion = data.get('disable_promotion')
            promotion_threshold_quantity = data.get('promotion_threshold_quantity')
            promotion_id = data.get('promotion_id')
            promotion_disable_date = data.get('promotion_disable_date')
            promotion_disable_date = datetime.strptime(promotion_disable_date, '%m/%d/%Y') if promotion_disable_date != '' else None
            hide_product = data.get('hide_product')
            hide_out_of_stock_days = data.get('hide_out_of_stock_days')
            hide_product_name_prefix = data.get('hide_product_name_prefix')
            hide_category_id = data.get('hide_category_id','').strip()
            hide_category_id = hide_category_id if hide_category_id != '' else None
            customer_reps = data.get('customer_rep','').strip()
            classification = data.get('classification','').strip()

            is_active = True
            activated_at = datetime.now()                                                            
            created_at = datetime.now()
            created_by = data.get('created_by')
            modified_at = None
            modified_by = None

            get_data_query = """SELECT product_id, product_name, sku FROM products WHERE sku = :sku;"""
            result = conn.execute(text(get_data_query).bindparams(sku=product_sku))

            if not result.rowcount:
                response["status"] = 404
                response["message"] = f"Product SKU {product_sku} not found in the database."
                return response

            for columns in result:
                product_id, product_name, sku = columns


            insert_query = "INSERT INTO products_visibility_rules (product_sku, product_id, product_name, disable_promotion, promotion_threshold_quantity, promotion_id, promotion_disable_date, disable_promotion_triggered, disable_promotion_triggered_at, hide_product, hide_out_of_stock_days, hide_product_name_prefix, hide_category_id, hide_classification, customer_rep, is_hide_product_triggered, hide_product_triggered_at, last_hide_product_triggered_at, is_active, activated_at, created_at, modified_at, created_by, modified_by) VALUES (:product_sku, :product_id, :product_name, :disable_promotion, :promotion_threshold_quantity, :promotion_id, :promotion_disable_date, :disable_promotion_triggered, :disable_promotion_triggered_at, :hide_product, :hide_out_of_stock_days, :hide_product_name_prefix, :hide_category_id, :hide_classification, :customer_rep, :is_hide_product_triggered, :hide_product_triggered_at, :last_hide_product_triggered_at, :is_active, :activated_at, :created_at, :modified_at, :created_by, :modified_by);"
            conn.execute(text(insert_query).bindparams(product_sku=product_sku, product_id=product_id, product_name=product_name, disable_promotion=disable_promotion, promotion_threshold_quantity=int(promotion_threshold_quantity), promotion_id=int(promotion_id), promotion_disable_date=promotion_disable_date, disable_promotion_triggered=False, disable_promotion_triggered_at=None, hide_product=hide_product, hide_out_of_stock_days=int(hide_out_of_stock_days), hide_product_name_prefix=hide_product_name_prefix, hide_category_id=hide_category_id, hide_classification=classification, customer_rep=customer_reps, is_hide_product_triggered=False, hide_product_triggered_at=None, last_hide_product_triggered_at=None, is_active=is_active, activated_at=activated_at, created_at=created_at, modified_at=modified_at, created_by=created_by, modified_by=modified_by))

            conn.commit()
            response["status"] = 200
            response["message"] = "Product Rule added successfully."

            
        except IntegrityError as e:
            if isinstance(e.orig, UniqueViolation):
                response['status'] = 422
                response['message'] = "Duplicate key violation: This product already exists in the rules."
            else:
                error_message = str(e)
                response['status'] = 422
                response['message'] = error_message
        finally:
            conn.close()
    return response


def get_product_hide_rules(store, body):
    response = {"status": 400,}
    page = int(body.get("page"))
    limit = int(body.get("limit"))
    sort_by = body.get("sort_by")
    search_keyword = body.get("search")
    sort_order = int(body.get("sort_order"))
   
    with new_pgdb.get_connection(store['id']) as conn:
        try:

            offset = (page - 1) * limit

            sort_order_str = "DESC" if sort_order == -1 else "ASC"            
            order_by_clause = f"ORDER BY {sort_by} {sort_order_str}" if sort_by else ""            
            where_clause = f"WHERE product_name ILIKE '%{search_keyword}%'" if search_keyword else ""
            
            total_records_query = f"SELECT COUNT(*) FROM products_visibility_rules {where_clause}"                                    
            total_records_result = conn.execute(text(total_records_query))
            total_records_result = int(total_records_result.scalar())             
            
            get_data_query = f"SELECT * FROM products_visibility_rules {where_clause} {order_by_clause} LIMIT {limit} OFFSET {offset}"            
            res = conn.execute(text(get_data_query))            
            
            data = []
            for row in res:               
                a = {
                    "product_sku": row[0],
                    "product_id": row[1],
                    "product_name": row[2], 
                    "disable_promotion": row[3],                 
                    "promotion_threshold_quantity": row[4],
                    "promotion_id": row[5],
                    "promotion_disable_date": convert_to_timestamp(row[6]),
                    "disable_promotion_triggered": row[7],
                    "disable_promotion_triggered_at": convert_to_timestamp(row[8]),
                    "hide_product": row[9],
                    "hide_out_of_stock_days": row[10],
                    "hide_product_name_prefix": row[11],
                    "hide_category_id": row[12],
                    "hide_classification": row[13],
                    "customer_rep": row[14],
                    "is_hide_product_triggered": row[15],                    
                    "hide_product_triggered_at": convert_to_timestamp(row[16]),
                    "last_hide_product_triggered_at": convert_to_timestamp(row[17]),
                    "is_active": row[18],
                    # "activated_at": row[19].strftime("%Y-%m-%d %H:%M:%S") if isinstance(row[19], datetime) else str(row[19]),
                    # "created_at": row[20].strftime("%Y-%m-%d %H:%M:%S") if isinstance(row[20], datetime) else str(row[20]),
                    # "modified_at": row[21].strftime("%Y-%m-%d %H:%M:%S") if isinstance(row[21], datetime) else str(row[21]),
                    "activated_at": convert_to_timestamp(row[19]),
                    "created_at": convert_to_timestamp(row[20]),
                    "modified_at": convert_to_timestamp(row[21]),
                    "created_by": row[22],
                    "modified_by": row[23]                    
                }      
                data.append(a)    
                                                     
            paginated_rows, current_page, total_pages, total_items = paginate_data_postgres(
            total_records_result, data, page, limit)            

            pagination_data = calculatePaginationData(
            paginated_rows, current_page, limit, total_items)
            
            response['status'] = 200
            response['data'] = pagination_data
            
            
        except SQLAlchemyError as e:
                error_message = str(e)
                response['status'] = 422
                response['message'] = error_message
        finally:
            conn.close()
    return response


def update_product_hide_rule(store, req_body):
    response = {"status": 400,}

    with new_pgdb.get_connection(store['id']) as conn:
        try:
            product_sku = req_body.get('product_sku')  
            disable_promotion = req_body.get('disable_promotion')                             
            promotion_threshold_quantity = req_body.get('promotion_threshold_quantity')
            promotion_id = req_body.get('promotion_id')
            promotion_disable_date = req_body.get('promotion_disable_date')
            promotion_disable_date = datetime.strptime(promotion_disable_date, '%m/%d/%Y') if promotion_disable_date != '' else None
            hide_product = req_body.get('hide_product')
            hide_out_of_stock_days = req_body.get('hide_out_of_stock_days')
            hide_product_name_prefix = req_body.get('hide_product_name_prefix')
            hide_category_id = req_body.get('hide_category_id','').strip() 
            hide_category_id = hide_category_id if hide_category_id != '' else ''
            customer_reps = req_body.get('customer_rep','').strip()
            classification = req_body.get('classification','').strip()                                                                              
            modified_by = req_body.get('modified_by')
            modified_at = datetime.now()

            check_query = "SELECT * FROM products_visibility_rules WHERE product_sku = :product_sku;"
            res = conn.execute(text(check_query).bindparams(product_sku=product_sku))

            if not res.rowcount:
                response["status"] = 404
                response["message"] = f"Product SKU {product_sku} not found in the database."
                return response

            update_query = "UPDATE products_visibility_rules SET disable_promotion = :disable_promotion, promotion_threshold_quantity = :promotion_threshold_quantity, promotion_id= :promotion_id, promotion_disable_date = :promotion_disable_date,  hide_product = :hide_product, hide_out_of_stock_days = :hide_out_of_stock_days, hide_product_name_prefix = :hide_product_name_prefix,  hide_category_id = :hide_category_id, hide_classification = :classification, customer_rep = :customer_reps,  modified_by = :modified_by, modified_at = :modified_at  WHERE product_sku = :product_sku;"
            conn.execute(text(update_query).bindparams(product_sku=product_sku, disable_promotion=disable_promotion, promotion_threshold_quantity=promotion_threshold_quantity, promotion_id = promotion_id, promotion_disable_date = promotion_disable_date, hide_product = hide_product, hide_out_of_stock_days = hide_out_of_stock_days, hide_product_name_prefix = hide_product_name_prefix, hide_category_id = hide_category_id, classification = classification, customer_reps = customer_reps, modified_by=modified_by, modified_at=modified_at))                                      
            conn.commit()
    
            
            response["status"] = 200                
            response["message"] = "Rule updated successfully."            
            
        except SQLAlchemyError as e:
                error_message = str(e)
                response['status'] = 422
                response['message'] = error_message
        finally:
            conn.close()
    return response    

def delete_product_hide_rule(store, product_sku):
    response ={"status" : 400}
    with new_pgdb.get_connection(store['id']) as conn:
        try:           
            check_query = "SELECT * FROM products_visibility_rules WHERE product_sku = :product_sku;"
            res = conn.execute(text(check_query).bindparams(product_sku=product_sku))

            if not res.rowcount:
                response["status"] = 404
                response["message"] = f"Product SKU {product_sku} not found in the database."
                return response

            delete_query = "DELETE FROM products_visibility_rules WHERE product_sku = :product_sku;"
            conn.execute(text(delete_query).bindparams(product_sku=product_sku))
            conn.commit()

            response['status'] = 200
            response['message'] = "Data Deleted successfully"            
                
        except SQLAlchemyError as e:
                error_message = str(e)
                response['status'] = 422
                response['message'] = error_message
        finally:
            conn.close()

    return response

def get_data_for_modal(store):
    response = {
        "status": 400,
    }
    with new_pgdb.get_connection(store['id']) as conn:
        try:                       
            result = {}                      
                                                          
            query = "SELECT distinct(classification) from skuvault_catalog WHERE classification != ''"            
            classifications = conn.execute(text(query))
            clasclassifications_res = []
            
            for row in classifications:
                clasclassifications_res.append(row[0])

            result['classification'] = clasclassifications_res            

            conn.close()

            # retrive categories from bc
            api = store_util.get_bc_api_creds(store)
            categories = get_bc_categories(api)
            result['categories'] = []
            if categories:
                result['categories'] = categories

            # retrive promotions from bc
            promotions = get_bc_promotions(api)            
            promo_data = []
            if promotions:
                for promotion in promotions:
                    res = {}
                    res['id'] = promotion['id']
                    res['name'] = promotion['name']
                    res['status'] = promotion['status'] 
                    promo_data.append(res)                                                           

            result['promotions'] = promo_data            

            admin = []
            admin_users = user_service.get_all_users_for_product_rule(store)
            if admin_users:
                for user in admin_users:
                    admin.append(user['username'])            
            result['admin_user'] = admin

            response['message'] = 'data retrived successfully'
            response['data'] = parse_json(result)
            response['status'] = 200
        
        except SQLAlchemyError as e:
            error_message = str(e)
            response['status'] = 422
            response['message'] = error_message
        finally:
            conn.close()

    return response