import bson
from services import Service
from bson import ObjectId
from datetime import datetime
from utils.common import calculatePaginationData, parse_json, processList
from utils import product_util
from fields.cms_fields import cms_fields,cms_fields_navigation
from mongo_db import user_db, cms_db
import os
from werkzeug.utils import secure_filename
from flask import send_file, make_response
import logging

logger = logging.getLogger()

ALLOWED_EXTENSIONS = set(['png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov', 'wmv', 'mkv'])


def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def change_file_name(filename):
    rendom = str(round(datetime.utcnow().timestamp()))
    if ('.' in filename):
        ext = filename.rsplit('.', 1)[1].lower()
        fname = rendom + '.' + ext
        return fname
    

class Cms(Service):
    def _init_(self, repository):
        super()._init_(repository)
    
    def get_data(self, body, cdn_baseurl):        
        result = []        
        if 'webpage_id' in body:
            res = {}
            webpage_id = body['webpage_id']
            webPage = super().find_one({"_id": ObjectId(str(webpage_id))})
            if 'preview_state' in webPage and webPage['preview_state']:               
                preview_state = webPage['preview_state']
            else:
                preview_state = webPage['default_layout']

            preview_state =  self.addCdnToImageUrl(cdn_baseurl, preview_state)

            res['id'] = webPage['id']
            res['name'] = webPage['name']
            res['created_at'] = webPage['created_at']
            res['updated_at'] = webPage['updated_at']
            res['url'] = webPage['url']
            res['is_customers_only'] = webPage['is_customers_only']
            res['versions'] = preview_state

            result.append(res)
            return result
        else:            
            body['filterBy'] = ['name']            
            pages, total_data_length, paginationPage, limit = super().get_paginated_records_updated_category(body, cms_fields,'')
            webpages = super().find_all()
            nameResult = []
            for page in webpages:
                test = {}
                test['Name'] = page['name']
                test['URL'] = page['url']
                nameResult.append(test)

            for page in pages:
                product_count=product_util.get_products_count_for_category(page['bc_id'])
                res = {}
                versions = page['versions']
                if (len(versions) == 0):
                    activeVersion = page['default_layout']
                else:
                    for version in versions:
                        if (version['status'] == 'active'):
                            activeVersion = version
                            break
                
                activeVersion =  self.addCdnToImageUrl(cdn_baseurl, activeVersion)
                
                res['id'] = page['id']
                res['category_id']=page['bc_id']
                res['name'] = page['name']
                res['created_at'] = page['created_at']
                res['updated_at'] = page['updated_at']
                res['url'] = page['url']
                res['status'] = page['status']
                res['is_visible'] = page['is_visible']
                res['parent_id'] = page['parent_id']
                res['views'] = page['views']
                res['sort_order'] = page['sort_order']
                res["type"] = page["type"]
                res['versions'] = activeVersion
                res['is_customers_only'] = page['is_customers_only']
                res['has_sub_child']=page['has_sub_child']
                res['product_count']=product_count

                result.append(res)
            data = calculatePaginationData(
                result, paginationPage, limit, total_data_length)
            data['meta']['name_info'] = nameResult
            return data  
        
    def addSubCategoryFlag(self,body,cdn_baseurl):
        pages = super().find_all_categories()
        for item in pages:
            category_id = item['bc_id']
            has_child_data = cms_db.has_categories_with_parent_id(category_id)
            cms_db.add_has_sub_child_flag(item['id'],has_child_data)
              
    def get_child_data(self,parent_id,cdn_baseurl): 
        categories=cms_db.get_categories_by_parent_id(parent_id)
        for category in categories:
            product_count=product_util.get_products_count_for_category(category['bc_id'])
            category['product_count']=product_count
            category['category_id']=category['bc_id']
        if categories:
            return categories
        else:
            return {}
        

    def set_version(self, payload, id=None):
        response = {
            "status": 400            
        }
        created_by = {}
        if 'created_by' in payload:
            user = user_db.fetch_user_by_username(payload['created_by'])        
            created_by = {
                "user_id": str(user['_id']),
                "user_name": user['name']
            } 
        category = super().find_one({"_id": ObjectId(id)})
        
        if category:                                                                                           
            totalVersions = len(category['versions'])
            versions = category['versions']

            if totalVersions > 0:
                lastVersion = category['versions'][-1]['version']
                for version in versions:
                    version['status'] = 'inactive'                    
            else:
                lastVersion = 0

            if (totalVersions >= 10):
                category['versions'].pop(0)

            payload['created_by'] = created_by
            payload['created_at'] = int(datetime.utcnow().timestamp())
            payload['version'] = lastVersion + 1
            versions.append(payload)

            id = super().update_one({"_id": ObjectId(id)}, {"$set":
                                                                        {                                                                            
                                                                            "versions": category['versions'],
                                                                            "updated_at":  int(datetime.utcnow().timestamp())
                                                                        }})
                   
            response['message'] = "changes saved sucessfuly"
            response['status'] = 200
        else:
            response['message'] = "Category not found."
            response['status'] = 404
        return response

    def update_customer_only_flag(self, body, id=None):
        response = {
            "status": 400
        }
        if id:
            id = super().update_one({"_id": ObjectId(str(id))}, {"$set":
                                                                            {                                                                             
                                                                                "is_customers_only": body['is_customers_only'],                                                                             
                                                                                "updated_at":  int(datetime.utcnow().timestamp())
                                                                            }
                                                                            })
            response['message'] = "Category Updated successfully"
            response['status'] = 200        

        return response

    def create_list(self, body):
        response = {
            "status": 400
        }
        url = body['name']
        # url = '/pages/' + url.replace(" ", "_").lower()
        url = url.replace(" ", "_").lower()
        # isUniqueName = self.checkForUniquePageName(body['name'])
        if True:
            body["created_at"] = int(datetime.utcnow().timestamp())
            body["updated_at"] = ""
            body["type"]=body["type"]
            body["url"] = url
            body["default_layout"] = {}
            body["versions"] = []
            body["preview_state"] = {}
            body['is_customers_only'] = False
            id = super().create(body)
            response['message'] = "Page created successfully"
            response['status'] = 200
        else:
            response['message'] = "The provided page Name has already been matched with other pages. Please provide a different page Name."
            response['status'] = 409

        return response
        # data['store_id'] = store['id']
        
        # # check if permission is already exits in DB.
        # permission = self.get_permission_by_store_id(store)

        # if permission == None:
        #     id = super().create(data)
        #     if id:
        #         return {"status": "success" }, 201
        # else:
        #     return {'message': '1 permission object already attched with the store name: ' + store['name']}, 400

    def get_category(self, category_id=None):
        result = {}
        category = super().find_one({"_id": ObjectId(str(category_id))})
        if (len(category['versions']) == 0):
            activeVersion = category['default_layout']
        else:
            versions = category['versions']
            for version in versions:
                if (version['status'] == 'active'):
                    activeVersion = version
                    break

        result['id'] = category['id']
        result['name'] = category['name']
        result['created_at'] = category['created_at']
        result['updated_at'] = category['updated_at']
        result['url'] = category['url']
        result['is_customers_only'] = category['is_customers_only']
        result['versions'] = activeVersion

        return result

    def get_cms_versions(self, id=None):
        result = super().find_one({"_id": ObjectId(id)})
        list = {}
        versionArray = []
        if result is not None:
            if (len(result['versions']) > 0):
               versions = result['versions']
               for version in versions:
                  versionArray.append(version['version'])

               list['webpageVersions'] = versionArray

        return list
    
    def get_cms_versionData(self, body, id=None):
        list = {}
        result = super().find_one({"_id": ObjectId(id)})
        if result is not None:
           if (len(result['versions']) == 0):
               activeVersion = result['default_layout']
           else:
               versions = result['versions']
               for version in versions:
                   if (version['version'] == int(body['version'])):
                      activeVersion = version
                      break

           list['id'] = result['id']
           list['name'] = result['name']
           list['created_at'] = result['created_at']
           list['updated_at'] = result['updated_at']
           list['url'] = result['url']
           list['is_customers_only'] = result['is_customers_only'] 
           list['versions'] = activeVersion

        return list
    
    def check_all_categories(self, category_arr, store):
        db_categories = super().find_all_categories()
        for category in db_categories:
            if category['bc_id'] not in category_arr:
                # query = {"id": int(category['bc_id'])}
                # current_time = int(datetime.utcnow().timestamp())
                # update = { 
                #             "$set" : { 
                #                 "status": 'deleted', 
                #                 "updated_at": current_time
                #             }
                #         }
                # cms_db.update(query,update)
                cms_db.delete_category_by_id(category['bc_id'])

    def create_bc_category(self, category, req_body,store):
        
        created_by = {}
        if 'created_by' in req_body:
            user = user_db.fetch_user_by_username(req_body['created_by'])        
            created_by = {
                "user_id": str(user['_id']),
                "user_name": user['name']
            }           
        # Find in DB if available.
        db_category = super().find_one({"id": int(category['id'])})    
        description = category["description"] 
        # if '%%GLOBAL_ShopPathSSL%%' in description or '%%GLOBAL_CdnStorePath%%' in description or '<a href="%%GLOBAL_ShopPathSSL%%' in description or '<a href="%%GLOBAL_CdnStorePath%%' in description:                                        
        #     print(category["id"], '  ||  ', category["name"], '  ||  ', category["custom_url"]["url"])

        # if '/searchresults.html?search_query=' in description:
        #     print(category["id"], '  ||  ', category["name"], '  ||  ', category["custom_url"]["url"])


        description = description.replace('<a href="%%GLOBAL_ShopPathSSL%%', '<a href="') 
        description = description.replace('<a href="%%GLOBAL_CdnStorePath%%', '<a href="')
        description = description.replace("%%GLOBAL_ShopPathSSL%%", "https://cdn11.bigcommerce.com/s-964anr")
        description = description.replace("%%GLOBAL_CdnStorePath%%", "https://cdn11.bigcommerce.com/s-964anr")   
        description = description.replace("/searchresults.html?search_query=", "/search/?q=")             
        
        
      
        # If webpage not exists then create
        if not db_category:            
            self.generate_page(category, description,created_by)

        # If exists then check if it's modified or not if not then replace content
        if db_category:                  
            self.update_page( category, description,created_by) 
            self.getNavigationForCategories(category,store)

    def getNavigationForCategories(self,category,store):
            navigations=cms_db.get_sub_nav(store)
            navigations=processList(navigations)
            for navigation in navigations:
                navigation['navigation_id']=ObjectId(navigation['navigation_id'])
                is_updated=False
                for data in navigation['navigations']:
                    if data['type'] == 'Categories' and data['id'] == 'Categories'+'_'+str(category['id']) :
                        is_updated=True
                        data['url']=category['custom_url']['url']
                if is_updated:
                   cms_db.update_sub_nav_by_id(store,navigation)
            return
      
    def generate_page(self, category, description,created_by):
            new_list={} 
            seo_details={}
            has_child_data = cms_db.has_categories_with_parent_id(category["id"])
            seo_details["page_name"]=category["name"]
            seo_details["page_url"]=category["custom_url"]["url"]
            seo_details["meta_title"]=category["page_title"]
            seo_details["meta_description"]=category["meta_description"]           
            
            new_list["id"]=category["id"]
            new_list["parent_id"] = category["parent_id"]
            new_list["name"]=category["name"]
            new_list["type"]="category"
            new_list["views"] = category["views"]
            new_list["sort_order"] = category["sort_order"]
            new_list["is_visible"] = category["is_visible"]              
            new_list["url"]=category["custom_url"]["url"]  
            new_list["is_customers_only"]= False                                        
            new_list["default_layout"] = {}
            new_list["versions"]= [
                { 
                "name":category["name"],
                "status":"active", 
                "class":"",
                "type": "category",
                "seo_details":seo_details,
                "components":[
                    {
                    "id": 1,
                    "name": "HTML Block",
                    "code": "html_block",  
                    "variant": {
                        "id": "1",
                        "name": "HTML Block",
                        "admin_layout": "style1",
                        "class": [],                          
                        "config": {
                            "data": description    
                                }
                    },  
                    "children": []
                    }
                ],
                "created_at":int(datetime.utcnow().timestamp()),
                "created_by": created_by,                                                                              
                "version":0,
            } ]
            new_list["preview_state"] = {} 
            new_list["updated_at"]=""
            new_list["created_at"]=int(datetime.utcnow().timestamp())
            new_list["status"] = "active"
            new_list["has_sub_child"] = has_child_data

            return cms_db.create(new_list)

    def update_page(self, category, description,created_by):     
            update_obj = {
                'name': category['name'],
                'parent_id':  category["parent_id"],
                'type': 'category',
                'views': category["views"],
                'sort_order': category["sort_order"],
                'is_visible': category['is_visible'],
                'url': category["custom_url"]["url"],  
                'is_customers_only': False,                              
                "versions": [{
                    'name': category['name'],
                    'status': 'active',
                    'class': '',  
                    'type': 'category',                  
                    'components': [{
                            'id': 1,
                            'name': 'HTML Block',                            
                            'code': 'html_block',
                            'variant': {
                                "id": '1',
                                'name': 'HTML Block',
                                'admin_layout': 'style1',
                                'class': [],                                
                                'config': {
                                    'data': description    
                                }
                            },
                            'children': []
                    }],
                    'seo_details': {
                        'page_name': category['name'],
                        'page_url': category["custom_url"]["url"],
                        'meta_title': category['page_title'],
                        'meta_description': category['meta_description']
                    },
                    'created_by': created_by,
                    'created_at': int(datetime.utcnow().timestamp()),
                    'updated_at': int(datetime.utcnow().timestamp()),
                    'version': 1
                }]
            }                              
            return cms_db.update({"id": category['id']}, {"$set": update_obj})

    def syncAllSubNavigation(self,store):
        navigations=cms_db.get_sub_nav(store)
        navigations=processList(navigations)
        for navigation in navigations:
            is_updated=False
            for data in navigation['navigations']:
                if data['type'] == 'Categories':
                    category_id=cms_db.get_page_by_name(data['url'])
                    data['id']='Categories'+'_'+str(category_id['id'])
                    is_updated=True
                elif data['type']=='Web Pages':
                    webpage_id=cms_db.get_webpage_by_name(store,data['url'])
                    data['id']='Web Pages'+'_'+str(webpage_id['_id'])
                    is_updated=True
                elif data['type']=='Brands':
                    brand_id=cms_db.get_brand_by_name(store,data['url'])
                    data['id']='Brands'+'_'+str(brand_id['id']) 
                    is_updated=True 
            if is_updated:
                cms_db.update_sub_nav_by_id(store,navigation)
        return
        
    def addCdnToImageUrl(self, cdn_baseurl, activeVersion):
        if 'components' in activeVersion:           
            for component in activeVersion['components']:
                if 'variant' in component:
                    variant = component['variant']
                    if 'config' in variant:
                        config = variant['config']
                        if 'image_url' in config:
                            if config['image_url'] != '':
                                config['image_url'] = cdn_baseurl + '/categories' + config['image_url'].replace('/categories/images', '') 
                        if 'mobile_image_url' in config:
                            if config['mobile_image_url'] != '':
                                config['mobile_image_url'] = cdn_baseurl + '/categories' + config['mobile_image_url'].replace('/categories/images', '')                           
                        elif 'slider' in  config and 'side_images' in config:                            
                            sliders = config['slider']
                            side_images = config['side_images']
                            for slider in sliders:
                                if 'image_url' in slider:
                                    if slider['image_url'] != '':
                                        slider['image_url'] = cdn_baseurl + '/categories' + slider['image_url'].replace('/categories/images', '')

                            for side_image in side_images:
                                if 'image_url' in side_image:
                                    if side_image['image_url'] != '':
                                        side_image['image_url'] = cdn_baseurl + '/categories' + side_image['image_url'].replace('/categories/images', '')
                        elif 'banners' in  config:
                            banners = config['banners']
                            for banner in banners:
                                if 'image_url' in banner:
                                    if banner['image_url'] != '':
                                        banner['image_url'] = cdn_baseurl + '/categories' + banner['image_url'].replace('/categories/images', '')   
                                if 'mobile_image_url' in banner:
                                    if banner['mobile_image_url'] != '':
                                        banner['mobile_image_url'] = cdn_baseurl + '/categories' + banner['mobile_image_url'].replace('/categories/images', '')                          
                        elif 'logos' in config:
                            logos = config['logos'] 
                            for logo in logos:
                                if 'image_url' in logo:
                                    if logo['image_url'] != '':
                                        logo['image_url'] = cdn_baseurl + '/categories' + logo['image_url'].replace('/categories/images', '')

        return activeVersion             
        

    def get_user_by_username(self, username):
        res = user_db.fetch_user_by_username(username)
        res['id'] = str(res['_id'])

        del res["yoyo"]
        del res['_id']
        del res['created_at']
        del res['updated_at']

        return res

class SetImages():
    def setImage(self, body):
        response = {
            "status": 400
        }

        if not os.path.exists('images/categories/images'):
            os.makedirs('images/categories/images')

        file = body['image']
        UPLOAD_FOLDER = os.path.join('images/categories/images')

        if (file.filename == ''):
            response['message'] = "No image selected for uploading"
            response['status'] = 500

        if (file and allowed_file(file.filename)):
            newName = change_file_name(file.filename)
            fname = secure_filename(newName)
            file.save(os.path.join(UPLOAD_FOLDER, fname))
            base_path = os.path.join(os.path.abspath(
                os.getcwd()), UPLOAD_FOLDER, newName)
            
            if '/app/images' in base_path:
                base_path = base_path.replace('/app/images', '')             
            response['message'] = base_path
            response['status'] = 200
        else:
            response['message'] = "Allowed image types are -> png, jpg, jpeg, gif, mp4, avi, mov, wmv, mkv"
            response['status'] = 500

        return response

    def getImage(self, body):
        url = './images/categories/images/' + body['image']
        if not os.path.exists(url):
            return make_response({'error': 'Image not found'}, 404)

        return send_file(url, as_attachment=True)