from flask import request
import logging
import traceback
from apiv2 import APIResource, AUTH_TOKEN_HEADER_KEY
from iam import build_side_menu, auth_service, user_service, feature_service
from exceptions.common_exceptions import InvalidInputException, ResourceNotFoundException, InactiveResourceException
from exceptions.auth_exceptions import InvalidCredentialsException

logger = logging.getLogger()

class LoginV2(APIResource):

    def post_executor(self, request):
        logger.debug("Entering Login POST")
        try:
            auth_header = request.headers.get("Authorization", None) 
            if auth_header:
                content = auth_service.login(auth_header)
                if content:
                    return content, 200
                else:
                    return {"message": "You are not authorized to login"}, 401                
            return {"message": "Invalid credentials."}, 401               
        except InvalidInputException as e:
            logger.error("Login POST: Exception caught: " + traceback.format_exc())
            return {"message": "Invalid input."}, 400
        except ResourceNotFoundException as e:
            logger.error("Login POST: Exception caught: " + traceback.format_exc())
            return {"message": "Invalid username."}, 404
        except InactiveResourceException as e:
            logger.error("Login POST: Exception caught: " + traceback.format_exc())
            return {"message": "User is not active."}, 409
        except InvalidCredentialsException as e:
            logger.error("Login POST: Exception caught: " + traceback.format_exc())
            return {"message": "Invalid credentials."}, 401
        finally:
            logger.debug("Exiting Login POST")

    def post(self):
        return self.execute_open_api_request(request, self.post_executor)
    

class LogoutV2(APIResource):

    def post_executor(self, request, token_payload):
        logger.debug("Entering Logout POST")
        try:
            if token_payload:
                token = request.headers.get(AUTH_TOKEN_HEADER_KEY, None)
                result = auth_service.logout(token_payload['username'], token)
                if result:
                    return {"status": 200}, 200
            return {"message": "Unauthorized"}, 401
        finally:
            logger.debug("Exiting Logout POST")

    def post(self):
        return self.execute_request(request, self.post_executor)
    

class GoogleLoginV2(APIResource):
    def post_executor(self, request):
        logger.debug("Entering GoogleLogin POST")
        try:
            req_body = request.get_json(force=True)  
            if req_body and "accessToken" in req_body:
                access_token = req_body['accessToken']
                content = auth_service.google_login(access_token)
                return {"data":content}, 200
        except InvalidInputException as e:
            logger.error("GoogleLogin POST: Exception caught: " + traceback.format_exc())
            return {"message": "Invalid input."}, 400
        except ResourceNotFoundException as e:
            logger.error("GoogleLogin POST: Exception caught: " + traceback.format_exc())
            return {"message": "Invalid username."}, 404
        except InactiveResourceException as e:
            logger.error("GoogleLogin POST: Exception caught: " + traceback.format_exc())
            return {"message": "User is not active."}, 409
        except InvalidCredentialsException as e:
            logger.error("GoogleLogin POST: Exception caught: " + traceback.format_exc())
            return {"message": "Invalid credentials."}, 401
        finally:
            logger.debug("Exiting GoogleLogin POST")

    def post(self):
        return self.execute_open_api_request(request, self.post_executor)

class StoreAdminAPI(APIResource):

    def get_executor(self, request, payload):
        logger.debug("Entering TenantStoreAPI POST")
        try:
            tenant_id = self.get_tenant_id(payload)
            username = self.get_username(payload)
            content = user_service.get_user_admin_info(tenant_id, username)
            return {"data": content}, 200
        finally:
            logger.debug("Exiting TenantStoreAPI POST")

    def get(self):
        return self.execute_request(request, self.get_executor)
    
class UserPermissionAPI(APIResource):

    def get_executor(self, request, payload):
        logger.debug("Entering TenantStoreAPI POST")
        try:
            tenant_id = self.get_tenant_id(payload)
            username = self.get_username(payload)
            store_id = self.get_store_id(request)
            if username and store_id:
                features = feature_service.get_store_features(store_id)
                user_role = user_service.get_store_user_role(store_id=store_id, username=username)
                if features and user_role:
                    content = build_side_menu(features, user_role["permissions"])
                    return {"data": content}, 200
                return {"message": "Invalid store or user"}, 409
            return {"message": "Invalid input"}, 400
        finally:
            logger.debug("Exiting TenantStoreAPI POST")

    def get(self):
        return self.execute_request(request, self.get_executor)
    
class AdminUserLoginV2(APIResource):

    def post_executor(self, request):
        logger.debug("Entering Admin User Login POST")
        try:
            payload = request.get_json()

            if payload:
                token = payload.get("X-AUTH-TOKEN", None)
                tenant_id = payload.get("X-TENANT-ID", None)
                if token and tenant_id:
                    content = auth_service.admin_user_login(tenant_id, token)
                else:
                    return {"message": "Invalid credentials."}, 400    
            else:
                return {"message": "Invalid credentials."}, 400
            
            if content:
                return content, 200
            else:
                return {"message": "You are not authorized to login"}, 401                
        except InvalidInputException as e:
            logger.error("Admin User Login POST: Exception caught: " + traceback.format_exc())
            return {"message": "Invalid input."}, 400
        except ResourceNotFoundException as e:
            logger.error("Admin User Login POST: Exception caught: " + traceback.format_exc())
            return {"message": "Invalid username."}, 404
        except InactiveResourceException as e:
            logger.error("Admin User Login POST: Exception caught: " + traceback.format_exc())
            return {"message": "User is not active."}, 409
        except InvalidCredentialsException as e:
            logger.error("Admin User Login POST: Exception caught: " + traceback.format_exc())
            return {"message": "Invalid credentials."}, 401
        finally:
            logger.debug("Exiting Login POST")

    def post(self):
        return self.execute_open_api_request(request, self.post_executor)