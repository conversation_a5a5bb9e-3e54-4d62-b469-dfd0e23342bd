from datetime import datetime, date
from decimal import Decimal
import json
from sqlalchemy import Column,  DateTime,String, Integer, delete, text, update
from sqlalchemy.dialects.postgresql import insert
import new_pgdb as db
from utils.common import parse_json, paginate_data, calculatePaginationData, get_month_names, convert_to_timestamp
import logging

logger = logging.getLogger()

skuvault_catalog = ['sku', 'part_number', 'title', 'cost', 'quantity_available', 'quantity_incoming',
                    'incremental_quantity', 'reorder_point', 'brand', 'classification', 'primary_supplier', 
                    'created_date', 'modified_date', 'parent_sku']

class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, date):
            return obj.isoformat()
        return super().default(obj)
    
class OrderConsignmentSchema(db.Base):
    __tablename__ = db.DBTables.order_consignment_table

    id = Column(Integer, primary_key=True)
    order_id = Column(Integer)
    customer_id = Column(Integer)
    customer_name = Column(String)
    customer_email = Column(String)
    order_lineitem_id = Column(Integer)
    product_id = Column(Integer)
    parent_sku = Column(String)
    variant_id = Column(Integer)
    variant_sku = Column(String)
    options = Column(String)
    order_type = Column(String)
    quantity = Column(Integer)
    rep_name = Column(String)
    rep_user_id = Column(String)
    created_at= Column(DateTime)
    created_by= Column(String)
    modified_at =Column(DateTime)
    modified_by=Column(String)

    @classmethod
    def add_products(cls, store, data, session=None):
       stmt = insert(OrderConsignmentSchema).on_conflict_do_nothing()
       values = []
       values.append({
        "order_id": data['order_id'],
        "customer_id":data['customer_id'], 
        "customer_name": data['customer_name'], 
        "customer_email":data['customer_email'], 
        "order_lineitem_id":data['order_lineitem_id'], 
        "product_id":data['product_id'] ,
        "parent_sku":data['parent_sku'] ,
        "variant_id":data['variant_id'] ,
        "variant_sku":data['variant_sku'], 
        "options":data['options'] ,
        "order_type":data['order_type'],
        "quantity":data['quantity'],
        "rep_name":data['rep_name'] ,
        "rep_user_id":data['rep_user_id'],
        "created_by":data['created_by'],
        "created_at":datetime.now() ,
        "modified_by":data['modified_by'], 
        "modified_at":datetime.now(),
       })
       res = db.execute_stmt(store, stmt, values, session)
       return values
    
    @classmethod
    def update_products(cls, store, order_id, order_type):
        try:
            conn = db.get_connection(store['id'])
            update_query = text(
                f"UPDATE {db.DBTables.order_consignment_table} SET order_type = :new_type WHERE order_id = :order_id"
            )
            conn.execute(update_query.params(new_type=order_type, order_id=order_id))
            conn.commit()
        except Exception as e:
            logger.error(f"something went wrong: {e}")
            raise 
        finally:
            if conn:
                conn.close()

    @classmethod
    def get_order(cls, store, order_id):
        try:
            conn = db.get_connection(store['id'])
            query = text(
                f"SELECT COUNT(order_id) FROM {db.DBTables.order_consignment_table} WHERE order_id = :order_id"
               
            )
            user = conn.execute(query.params(order_id=order_id))
            result=user
            exists = result.scalar()
            return exists
        except Exception as e:
            logger.error(f"something went wrong: {e}")
            raise 
        finally:
            if conn:
                conn.close()
        
    @classmethod
    def get_order_type(cls, store, order_id):
        try:
            conn = db.get_connection(store['id'])
            query = text(
                f"SELECT order_type FROM {db.DBTables.order_consignment_table} WHERE order_id = :order_id LIMIT 1"
            )
            user = conn.execute(query.params(order_id=order_id))
            result = user.fetchone()
            order_type = ""  
            if result:
               order_type = result[0]
            return order_type
        except Exception as e:
            logger.error(f"something went wrong: {e}")
            raise 
        finally:
            if conn:
                conn.close()
        
    @classmethod
    def delete_products(cls, store, order_id):
        try:
            conn = db.get_connection(store['id'])
            query = text(
                f"DELETE FROM {db.DBTables.order_consignment_table} WHERE order_id = :orderID"
                )
            res = conn.execute(query.params(orderID=order_id))
            conn.commit()
        except Exception as e:
            logger.error(f"Error deleting the product {order_id}: {e}")
            raise 
        finally:
            if conn:
                conn.close()


    @classmethod
    def get_product_by_id(cls, store, product_id):
        conn = None
        try:
            conn = db.get_connection(store['id'])
            query = text(f"""
                    SELECT COUNT(DISTINCT order_id) AS unique_order_count, SUM(quantity) AS total_quantity,customer_name,
                    rep_name FROM {db.DBTables.order_consignment_table} 
                    WHERE product_id = :product_id AND order_type = 'consignment' 
                    GROUP BY customer_name,rep_name ORDER BY total_quantity DESC
                """)
            user = conn.execute(query.params(product_id=product_id))
            result=user.fetchall()
            userDataList=[]
            total_consignment_sum = OrderConsignmentSchema.get_consignment_sum_for_product(store, product_id)
            
            for record in result:
                userData={}
                percentage=(record[1]/total_consignment_sum)*100
                percentage = round(percentage, 2)
                userData['customer_name']=record[2]
                userData['quantity']=record[1]
                userData['customer_rep']=record[3]
                userData['percentage']=percentage
                userData['order_count']=record[0]
                userDataList.append(userData)
            
            return userDataList
        except Exception as e:
            logger.error(f"Error fetching product by ID {product_id}: {e}")
            raise 
        finally:
            if conn:
                conn.close()
        
    @classmethod
    def is_product_available_by_id(cls, store, product_id):
        try:
            conn = db.get_connection(store['id'])
            query = text(
            f"SELECT EXISTS (SELECT 1 FROM {db.DBTables.order_consignment_table} WHERE product_id = :product_id) AS product_exists"
        )
            result = conn.execute(query.params(product_id=product_id))
            product_exists = result.scalar()
            return product_exists
        except Exception as e:
            logger.error(f"something went wrong: {e}")
            raise 
        finally:
            if conn:
                conn.close()
        
    @classmethod
    def get_consignment_sum_for_product(cls, store, product_id):
        try:
            conn = db.get_connection(store['id'])
            query = text(f"""
                SELECT SUM(quantity) AS total_quantity FROM {db.DBTables.order_consignment_table} 
                WHERE product_id = :product_id AND order_type = 'consignment'
            """)
            user = conn.execute(query.params(product_id=product_id))
            result = user.fetchone()
            quantity = 0  
            if result[0] is not None:
               quantity = result[0]
            return quantity
        except Exception as e:
            logger.error(f"something went wrong: {e}")
            raise 
        finally:
            if conn:
                conn.close()
        
    @classmethod
    def get_onhold_sum_for_product(cls,store, product_id):
        try:
            conn = db.get_connection(store['id'])
            query = text(f"""
                SELECT SUM(quantity) AS total_quantity FROM {db.DBTables.order_consignment_table} 
                WHERE product_id = :product_id AND order_type = 'on-hold'
                """)
            user = conn.execute(query.params(product_id=product_id))
            result = user.fetchone()
            quantity = 0 
            if result[0] is not None:
               quantity = result[0]
            return quantity
        except Exception as e:
            logger.error(f"something went wrong: {e}")
            raise 
        finally:
            if conn:
                conn.close()
        
    @classmethod   
    def count_unique_orders(cls, store, product_id):
        try:
            conn = db.get_connection(store['id'])
            query = text(f"""
                SELECT COUNT(DISTINCT order_id) AS unique_order_count FROM {db.DBTables.order_consignment_table} 
                WHERE product_id = :product_id AND order_type = 'on-hold'
            """)
            result = conn.execute(query.params(product_id=product_id))
            unique_order_count = result.scalar()
            return unique_order_count
        except Exception as e:
            logger.error(f"something went wrong: {e}")
            raise 
        finally:
            if conn:
                conn.close()

    @classmethod  
    def count_unique_customers(cls, store, product_id):
        try:
            conn = db.get_connection(store['id'])
            query = text(f"""
                    SELECT COUNT(DISTINCT customer_name) AS unique_customer_count FROM {db.DBTables.order_consignment_table} 
                    WHERE product_id = :product_id AND order_type = 'consignment'
                """)
            result = conn.execute(query.params(product_id=product_id))
            unique_customer_count = result.scalar()

            return unique_customer_count
        except Exception as e:
            logger.error(f"something went wrong: {e}")
            raise 
        finally:
            if conn:
                conn.close()


    @classmethod
    def get_report_by_product_id(cls, store, product_id):
        try:
            conn = db.get_connection(store['id'])        
            query = text (
                """SELECT
                    customer_name,
                    COALESCE(
                        options::json->>'Choose Your Flavor',
                        options::json->>'Choose Your Flavor:',
                        ''
                    ) AS flavor,
                    COALESCE(
                        options::json->>'Choose Your Nicotine',
                        options::json->>'Choose Your Nicotine:',
                        options::json->>'NICOTINE',
                        ''
                    ) AS nicotine,
                    quantity
                FROM
                    order_consignment
                WHERE
                    product_id = :product_id
                ORDER BY
                    customer_name ASC;
                """
            )
            user = conn.execute(query.params(product_id=product_id))
            result = user.fetchall()
            user_data_dict = {}
            for record in result:
                customer_name = record[0].lower()
                flavor = record[1].lower()
                nicotine = record[2].lower()
                quantity = record[3]

                # Group by customer_name, flavor, and nicotine
                key = (customer_name, flavor, nicotine)
                if key in user_data_dict:
                    user_data_dict[key]['consignment_qty'] += quantity
                else:
                    user_data_dict[key] = {
                        'customer_name': customer_name,
                        'variants': flavor,
                        'nicotine' : nicotine,
                        'consignment_qty': quantity
                    }

            user_data_list = list(user_data_dict.values())
            return user_data_list
        except Exception as e:
            logger.error(f"something went wrong: {e}")
            raise 
        finally:
            if conn:
                conn.close()
        
    @classmethod
    def get_order_customer_details_by_order_id(store, order_id):
        response = {'status': 400}

        try:
            conn = db.get_connection(store['id'])
            # SQL query to get data from the orders and customer tables
            query = f"""
                SELECT 
                    o.order_created_date_time,
                    o.order_status,
                    o.total_including_tax,
                    o.order_id,
                    c.customer_name
                FROM 
                    orders AS o
                JOIN 
                    customer AS c ON o.customer_id = c.customer_id
                WHERE 
                    o.order_id = {order_id}
            """

            # Execute the query
            result = conn.execute(query)
            res = result.fetchone()  # Fetch only one record since we expect one order

            # If result exists, format the data into a dictionary
            if res:
                order_data = {
                    'order_created_date_time': convert_to_timestamp(res[0]),
                    'order_status': res[1],
                    'total_including_tax': res[2],
                    'order_id': res[3],
                    'customer_name': res[4]
                }

                response['data'] = order_data
                response['status'] = 200
            else:
                response['message'] = "Order not found"
                response['status'] = 404
            return response
        except Exception as e:
            logger.error(f"something went wrong: {e}")
            raise 
        finally:
            if conn:
                conn.close()

