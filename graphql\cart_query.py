cart_query = """
query ProductsWithOptionSelections(
  $productIds: [Int!] = product_ids
  $variantIds: [Int!] = varaint_ids
) {
  site {
    products: products(entityIds: $productIds, first: 50) {
      edges {
        node {
          ...ProductFields
        }
      }
    }
  }
}

fragment ProductFields on Product {  
  entityId
  sku
  name
  availabilityV2 {
    status
  }
  path
  minPurchaseQuantity
  maxPurchaseQuantity
  variants(entityIds: $variantIds, first: 250) {
    edges {
      node {
        id
        entityId
        sku
        upc               
        inventory {
          aggregated {
            warningLevel
            availableToSell
          }
        }        
      }
    }
  }
}
"""


def get_query(product_ids, varaint_ids):
    x = cart_query.replace("product_ids", str(product_ids))
    x = x.replace("varaint_ids", str(varaint_ids))

    return x
