from datetime import datetime, timezone
from bson import ObjectId
from fields.products import product_price_list_fields
from new_mongodb import StoreAdminDBCollections, StoreDBCollections, get_admin_db_client_for_store_id, get_store_by_id, get_store_db_client_for_store_id
from new_mongodb import fetchall_documents_from_admin_collection, process_documents
import new_utils
from products.all_products.products_list import update_product_price_list
from pymongo.collation import Collation
from plugin import bc_price_list
import new_pgdb
from sqlalchemy import text
import logging
import traceback
from mongo_db import user_db
from utils import store_util
from utils.common import get_paginated_records_price_list, calculatePaginationData, convert_to_timestamp, fetch_static_price_lists, get_month_array_for_meta
from new_pgdb.analytics_db import AnalyticsDB
from collections import defaultdict
from decimal import Decimal, ROUND_HALF_UP
from utils import store_util
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from fields.products import price_list_products_dropdown_fields

logger = logging.getLogger()

def _get_store_ids_from_store_name(store_ids, store_name):
    if store_name == 'both':
        if "63da3e98b702e324567f76f9" in store_ids:
            store_ids.append("661239751b9ce4bd7f85237c")
        elif "661239751b9ce4bd7f85237c" in store_ids:
            store_ids.append("63da3e98b702e324567f76f9")
    elif store_name == 'midwest':
        if "63da3e98b702e324567f76f9" not in store_ids:
            store_ids.append("63da3e98b702e324567f76f9")
        if "661239751b9ce4bd7f85237c" in store_ids:
            store_ids.remove("661239751b9ce4bd7f85237c")
    elif store_name == 'cbd':
        if "661239751b9ce4bd7f85237c" not in store_ids:
            store_ids.append("661239751b9ce4bd7f85237c")
        if "63da3e98b702e324567f76f9" in store_ids:
            store_ids.remove("63da3e98b702e324567f76f9")
    return store_ids

def get_multi_store_product_price_lists(store_ids, page, limit, sort_by, filter, user, tag_filter=None, classification_filter=None, classified_as_filter=None, products_filter=None, user_filter=None, supplier_filter=None, top_products_filter=None, cost_margin_filter=None, store_name='both', primary_sort_store=None):
    store_ids = _get_store_ids_from_store_name(store_ids, store_name)

    primary_sort_store_id = '63da3e98b702e324567f76f9' if not primary_sort_store or primary_sort_store == 'midwest' else '661239751b9ce4bd7f85237c'
    if not store_ids or len(store_ids) == 0:
        return {"data": [], "meta": {"price_lists": {}, "months": {}}}
    
    # If only one store, return multi-store format (same structure)
    if len(store_ids) == 1:
        return _get_single_store_data_original_format(store_ids[0], page, limit, sort_by, filter, user, tag_filter, classification_filter, classified_as_filter, products_filter, user_filter, supplier_filter, top_products_filter, cost_margin_filter)

    # Multiple stores logic
    return _get_multi_store_data(store_ids, page, limit, sort_by, filter, user, tag_filter, classification_filter, classified_as_filter, products_filter, user_filter, supplier_filter, top_products_filter, cost_margin_filter, primary_sort_store_id)

def _get_single_store_data_original_format(store_id, page, limit, sort_by, filter, user, tag_filter=None, classification_filter=None, classified_as_filter=None, products_filter=None, user_filter=None, supplier_filter=None, top_products_filter=None, cost_margin_filter=None):
    """
    Get data from single store with same response structure as multi-store.
    """
    try:
        # Setup connections for single store
        store_connections = _setup_store_connections([store_id])
        store_conn = store_connections[store_id]

        # Extract field and sort order from sort_by
        field, sort_order = ('created_at', 'desc')
        if '/' in sort_by:
            field, order = sort_by.split('/')
            sort_order = 'asc' if order == '1' else 'desc'

        payload = {
            "page": page,
            "limit": limit,
            "sort_by": field,
            "sort_order": sort_order,
            "filterBy": ["parent_product_name", "parent_product_sku", "variant_name", "variant_sku"],
            "filter": filter
        }

        # Get role_id and setup price lists (keep original flow)
        role_id = user.get('role_id')
        static_price_lists = fetch_static_price_lists(store_id)
        store_obj = _get_store_object_safely(store_id)
        price_lists = _get_price_lists_safely(store_obj, store_id)

        # Build price list mappings (keep original flow)
        price_list_meta, price_list_map = _build_price_list_mappings(store_id, price_lists, static_price_lists, role_id)

        # Apply filters (all filters enabled for single store)
        filter_query, unique_sku_count, sku_list = _apply_filters_single_store(store_conn['pg_conn'], tag_filter, classification_filter, classified_as_filter, products_filter, user_filter, supplier_filter, top_products_filter, cost_margin_filter)

        # Suppress unused variable warnings
        _ = unique_sku_count, sku_list

        # Get products data (keep original data retrieval flow)
        additional_query = {"$and": [{"syncing": False}] + filter_query} if filter_query else {"syncing": False}

        products, total_data_length, page, limit = get_paginated_records_price_list(
            store_conn['admin_db'], StoreAdminDBCollections.PRODUCT_PRICE_LISTS, payload, product_price_list_fields, additional_query
        )

        # Get price list assignments for sales percentage calculation (keep original flow)
        assignments = list(store_conn['store_db'][StoreDBCollections.PRICE_LIST_ASSIGNMENT].find({
            "price_list_id": {"$exists": True},
            "customer_group_id": {"$exists": True}
        }))

        price_list_to_customer_group = {assignment['price_list_id']: assignment['customer_group_id'] for assignment in assignments}

        # Process products for single store (keep original processing)
        processed_products = _process_single_store_products_original_format(store_id, products, store_conn, price_list_meta, price_list_map, role_id, price_list_to_customer_group)

        # Convert single store products to multi-store format
        converted_products = _convert_single_store_to_multi_store_format(processed_products, store_id)

        # Create pagination data (keep original pagination)
        data = calculatePaginationData(converted_products, page, limit, total_data_length)

        # Create unified price list mapping (same as multi-store)
        price_list_mappings = {store_id: price_list_meta}
        unified_price_list_mapping = _map_price_lists_between_stores(price_list_mappings)

        # Add metadata (same structure as multi-store)
        month_names, _ = get_month_array_for_meta(6)
        data['meta']['price_lists'] = unified_price_list_mapping
        data['meta']['months'] = month_names if role_id != "67fd12676af694b36923ce09" else {}
        data['meta']['stores'] = {
            store_id: {
                'id': store_id,
                'name': store_connections[store_id]['store_name'],
                'suffix': _get_store_suffix(store_id)
            }
        }

        return data

    except Exception as e:
        logger.error(f"Error in _get_single_store_data_original_format: {traceback.format_exc()}")
        raise e
    finally:
        _close_store_connections(store_connections)

def _convert_single_store_to_multi_store_format(products, store_id):
    """
    Convert single store product format to multi-store format.
    """
    converted_products = []

    for product in products:
        # Extract store-specific data
        store_data = {
            'store_id': store_id,
            'store_name': "Midwest" if store_id == "63da3e98b702e324567f76f9" else "CBD",
            'inventory_level': product.get('inventory_level', 0),
            'turn_rate': product.get('turn_rate', 0),
            'weeks_on_hand': product.get('weeks_on_hand', 0),
            'default_price': product.get('default_price'),
            'price_list': product.get('price_list', []),
            'default_price_margin': product.get('default_price_margin', 0),
            'cost_range': product.get('cost_range'),
            'cost_margin': product.get('cost_margin', 0),
            'customer_specific_price': product.get('customer_specific_price', ""),
            'default_price_sales': product.get('default_price_sales', 0),
        }

        # Add monthly data if present
        for i in range(1, 8):
            month_field = f'month_{i}'
            if month_field in product:
                store_data[month_field] = product.get(month_field)

        # Create multi-store format product
        converted_product = {
            # Common fields at parent level
            'parent_product_id': product.get('parent_product_id'),
            'parent_product_name': product.get('parent_product_name'),
            'parent_product_sku': product.get('parent_product_sku'),
            'classification': product.get('classification', ''),
            'primary_supplier': product.get('primary_supplier', ''),
            'purchaser': product.get('purchaser', ''),
            'updated_at': product.get('updated_at'),
            'created_at': product.get('created_at'),
            'updated_by': product.get('updated_by'),
            # Store-specific data in stores array
            'stores': [store_data]
        }

        converted_products.append(converted_product)

    return converted_products

def _get_multi_store_data(store_ids, page, limit, sort_by, filter, user, tag_filter=None, classification_filter=None, classified_as_filter=None, products_filter=None, user_filter=None, supplier_filter=None, top_products_filter=None, cost_margin_filter=None, primary_sort_store=None):
    try:
        # Setup connections for all stores
        store_connections = _setup_store_connections(store_ids)

        # Extract field and sort order from sort_by
        field, sort_order = ('created_at', 'desc')
        if '/' in sort_by:
            field, order = sort_by.split('/')
            sort_order = 'asc' if order == '1' else 'desc'

        # Get role_id
        role_id = user.get('role_id')

        # Set primary sort store (defaults to first store if not specified)
        if not primary_sort_store or primary_sort_store not in store_ids:
            primary_sort_store = store_ids[0]

        # Step 1: Collect filtered SKUs from all stores and merge them
        all_filtered_skus = set()
        all_unique_skus = set()
        total_unique_sku_count = 0
        cost_margin_query = None
        store_names = {}

        for store_id in store_ids:
            store_conn = store_connections[store_id]
            store_names[store_id] = store_conn['store_name']

            # Apply filters using the single store function with multi-store flag
            filtered_skus, store_cost_margin_query, unique_sku_count, sku_list = _apply_filters_single_store(
                store_conn['pg_conn'],
                tag_filter, classification_filter, classified_as_filter, products_filter,
                user_filter, supplier_filter, top_products_filter, cost_margin_filter,
                is_multi_store=True, page=page, limit=limit, filter=filter
            )

            # Collect filtered SKUs from this store
            all_filtered_skus.update(filtered_skus)

            # Collect cost_margin query if present
            if not cost_margin_query and store_cost_margin_query:
                cost_margin_query = store_cost_margin_query

            # Collect SKU information for metadata
            all_unique_skus.update(sku_list)
            if unique_sku_count > total_unique_sku_count:
                total_unique_sku_count = unique_sku_count


        # Step 2: Create unified filter query from merged SKUs
        unified_filter_query = []
        if all_filtered_skus:
            unified_filter_query.append({"parent_product_sku": {"$in": list(all_filtered_skus)}})

        # Append cost_margin query if available
        if cost_margin_query:
            unified_filter_query.append(cost_margin_query)

        # Step 3: Get ALL filtered data from all stores first (without pagination)
        all_store_data = {}
        price_list_mappings = {}

        for store_id in store_ids:
            store_conn = store_connections[store_id]

            # Get all filtered data without pagination to get accurate total count
            store_data = _get_store_products_data_with_filters(
                store_id, store_conn, unified_filter_query,
                1, 50000, field, sort_order, filter, role_id, apply_sorting=False
            )

            all_store_data[store_id] = store_data
            price_list_mappings[store_id] = store_data['price_list_meta']

        # Map price lists between stores by title
        unified_price_list_mapping = _map_price_lists_between_stores(price_list_mappings)

        # Suppress unused variable warnings
        _ = all_unique_skus

        # Create separate objects for each store with common values at parent level
        multi_store_response = _create_multi_store_response(all_store_data, store_ids, unified_price_list_mapping, page, limit, role_id, store_names, primary_sort_store, field, sort_order)

        return multi_store_response

    except Exception as e:
        logger.error(f"Error in _get_multi_store_data: {traceback.format_exc()}")
        raise e
    finally:
        _close_store_connections(store_connections)



def _create_multi_store_response(all_store_data, store_ids, unified_price_list_mapping, page, limit, role_id, store_names, primary_sort_store, field, sort_order):
    """
    Create multi-store response with separate objects for each store.
    Common values at parent level, store-specific values in separate objects.
    Proper pagination applied after combining all store data.
    """
    # Group products by parent_product_sku
    products_by_sku = defaultdict(dict)
    for store_id in store_ids:
        store_data = all_store_data[store_id]
        for product in store_data['products']:
            parent_sku = product.get('parent_product_sku')
            if not parent_sku:
                continue

            if parent_sku not in products_by_sku:
                # Initialize with common fields
                products_by_sku[parent_sku] = {
                    'parent_product_id': product.get('parent_product_id'),
                    'parent_product_name': product.get('parent_product_name'),
                    'parent_product_sku': parent_sku,
                    'classification': product.get('classification', ''),
                    'primary_supplier': product.get('primary_supplier', ''),
                    'purchaser': product.get('purchaser', ''),
                    'updated_at': product.get('updated_at'),
                    'created_at': product.get('created_at'),
                    'updated_by': product.get('updated_by'),
                    'stores': {},
                    'primary_sort_value': None  # For sorting by primary store
                }

            # Create store-specific object
            store_object = {
                'store_id': store_id,
                'store_name': "Midwest" if store_id == "63da3e98b702e324567f76f9" else "CBD",
                'inventory_level': product.get('inventory_level', 0),
                'turn_rate': product.get('turn_rate', 0),
                'weeks_on_hand': product.get('weeks_on_hand', 0),
                'default_price': product.get('default_price'),
                'price_list': [],
                'default_price_margin': product.get('default_price_margin', 0),
                'cost_range': product.get('cost_range'),
                'cost_margin': product.get('cost_margin', 0),
                'customer_specific_price': product.get('customer_specific_price', ""),
                'default_price_sales': product.get('default_price_sales', 0),
            }

            if role_id != "67fd12676af694b36923ce09":
                for i in range(1, 8):
                    month_field = f'month_{i}'
                    store_object[month_field] = product.get(month_field)

            for price_item in product.get('price_list', []):
                store_price_item = {
                    'price_list_id': price_item.get('price_list_id'),
                    'margin_percentage': price_item.get('margin_percentage', 0),
                    'sales_percentage': price_item.get('sales_percentage', 0)
                }
                for key, value in price_item.items():
                    if key.startswith('price_'):
                        store_price_item[key] = value
                store_object['price_list'].append(store_price_item)

            products_by_sku[parent_sku]['stores'][store_id] = store_object

            # Set primary sort value for sorting (use primary store's value)
            if store_id == primary_sort_store:
                products_by_sku[parent_sku]['primary_sort_value'] = product.get(field, '')

    # Convert to final format
    all_products = []
    for parent_sku, product_data in products_by_sku.items():
        final_product = {
            # Common fields at parent level
            'parent_product_id': product_data['parent_product_id'],
            'parent_product_name': product_data['parent_product_name'],
            'parent_product_sku': parent_sku,
            'classification': product_data['classification'],
            'primary_supplier': product_data['primary_supplier'],
            'purchaser': product_data['purchaser'],
            'updated_at': product_data['updated_at'],
            'created_at': product_data['created_at'],
            'updated_by': product_data['updated_by'],
            # Store-specific objects
            'stores': list(product_data['stores'].values()),
            'primary_sort_value': product_data['primary_sort_value']
        }
        all_products.append(final_product)

    # Sort by primary store's field value
    reverse_sort = (sort_order == 'desc')
    all_products.sort(key=lambda x: x.get('primary_sort_value', ''), reverse=reverse_sort)

    # Remove the temporary sort field
    for product in all_products:
        product.pop('primary_sort_value', None)

    # Apply pagination to the combined and sorted results
    total_count = len(all_products)
    start_idx = (page - 1) * limit
    end_idx = start_idx + limit
    paginated_products = all_products[start_idx:end_idx]

    # Create response
    data = calculatePaginationData(paginated_products, page, limit, total_count)

    # Add metadata
    month_names, _ = get_month_array_for_meta(6)
    data['meta']['price_lists'] = unified_price_list_mapping
    data['meta']['months'] = month_names if role_id != "67fd12676af694b36923ce09" else {}
    data['meta']['stores'] = {
        store_id: {
            'id': store_id,
            'name': store_names[store_id],
            'suffix': _get_store_suffix(store_id)
        } for store_id in store_ids
    }

    return data

def _setup_store_connections(store_ids):
    """
    Setup database connections for multiple stores.
    
    Returns:
        Dict with store_id as key and connection objects as values
    """
    connections = {}
    
    for store_id in store_ids:
        try:
            store = get_store_by_id(store_id)
            connections[store_id] = {
                'admin_db': get_admin_db_client_for_store_id(store_id),
                'store_db': get_store_db_client_for_store_id(store_id),
                'pg_conn': new_pgdb.get_connection(store_id),
                'store_name': 'Midwest' if store_id == "63da3e98b702e324567f76f9" else 'CBD'
                
            }
        except Exception as e:
            logger.error(f"Failed to setup connections for store {store_id}: {e}")
            # Close any connections that were successfully opened
            _close_store_connections(connections)
            raise e
    
    return connections

def _close_store_connections(connections):
    """
    Close all database connections.
    """
    for store_id, conn_dict in connections.items():
        try:
            if 'pg_conn' in conn_dict and conn_dict['pg_conn']:
                conn_dict['pg_conn'].close()
        except Exception as e:
            logger.error(f"Error closing connections for store {store_id}: {e}")


def _build_price_list_mappings(store_id, price_lists, static_price_lists, role_id):
    """
    Build price list mappings using a static reference price list for sorting by name and store_id.
    """
    refrence_price_lists = {
        "Value Tier Price List": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 7,
                "661239751b9ce4bd7f85237c": 4
            }
        },
        "Tier 1 Price List": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 1,
                "661239751b9ce4bd7f85237c": 1
            }
        },
        "Tier 2 Price List": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 4,
                "661239751b9ce4bd7f85237c": 2
            }
        },
        "Tier Pro Price List": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 8,
                "661239751b9ce4bd7f85237c": 3
            }
        },
        "Distributor": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 13,
                "661239751b9ce4bd7f85237c": 5
            }
        },
        "MVD Warehouse": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 15,
                "661239751b9ce4bd7f85237c": 7
            }
        },
        "TCD Warehouse": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 14,
                "661239751b9ce4bd7f85237c": 6
            }
        },
        "VIP": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 52,
                "661239751b9ce4bd7f85237c": 52
            }
        },
    }

    # Merge static price lists
    price_lists['data'].extend([{
        'id': pl['id'], 'name': pl['name'], 'date_created': None, 'date_modified': None, 'active': pl['active']
    } for pl in static_price_lists])

    # Role-specific filtering
    if role_id == '67f5f9c43c97938e59357472':
        price_lists['data'] = [pl for pl in price_lists['data'] if pl['id'] not in (14, 15)]
    elif role_id == '67fd12676af694b36923ce09':
        price_lists['data'] = [pl for pl in price_lists['data'] if pl['id'] in (13, 15, 52)]

    # Build a lookup for all price lists by id
    id_to_price_list = {pl['id']: pl for pl in price_lists['data'] if pl['active']}

    # Now, build the sorted meta and map using the reference order and store_id mapping
    price_list_meta = {}
    price_list_map = {}
    idx = 1
    for name, ref_entry in refrence_price_lists.items():
        price_list_id = ref_entry['store_mappings'].get(store_id)
        if price_list_id is None:
            continue
        pl = id_to_price_list.get(price_list_id)
        if not pl:
            continue
        price_list_key = f"price_{idx}"
        price_list_meta[price_list_key] = {
            'id': pl['id'],
            'name': pl['name'],
            'active': pl['active']
        }
        price_list_map[pl['id']] = price_list_key
        idx += 1
    return price_list_meta, price_list_map

def _apply_filters_single_store(pg_conn, tag_filter=None, classification_filter=None, classified_as_filter=None, products_filter=None, user_filter=None, supplier_filter=None, top_products_filter=None, cost_margin_filter=None, is_multi_store=False, page=1, limit=30, filter=''):
    all_skus = set()  # To collect all unique SKUs
    cost_margin_query = None

    def fetch_skus(query, param_name, values):
        result = pg_conn.execute(text(query), {param_name: tuple(values)}).fetchall()
        return [row[0] for row in result]

    # For multi-store, only allow certain filters that use skuvault_catalog
    if is_multi_store:
        if classification_filter:
            classification_skus = fetch_skus("SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE classification IN :classification_filter_list AND parent_sku IS NOT NULL AND parent_sku != ''", 'classification_filter_list', classification_filter.split(','))
            all_skus.update(classification_skus)

        if user_filter:
            query = """
                SELECT ARRAY_AGG(DISTINCT suppliers)
                FROM user_supplier_mapping
                WHERE user_name = :user_name
            """
            res = pg_conn.execute(text(query), {'user_name': user_filter})
            suppliers_list = res.fetchone()[0] or []

            if suppliers_list:
                query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier = ANY(:suppliers_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
                result = pg_conn.execute(text(query), {'suppliers_list': suppliers_list}).fetchall()
                user_skus = [row[0] for row in result]
                all_skus.update(user_skus)

        if supplier_filter:
            supplier_filter_list = [supplier.strip() for supplier in supplier_filter.split(';')]
            query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier ILIKE ANY(:supplier_filter_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
            result = pg_conn.execute(text(query), {'supplier_filter_list': supplier_filter_list}).fetchall()
            supplier_skus = [row[0] for row in result]
            all_skus.update(supplier_skus)

        # Handle cost_margin filter for multi-store
        if cost_margin_filter:
            cost_margin_filter_val = int(cost_margin_filter)
            if cost_margin_filter_val > 0:
                cost_margin_query = {"cost_margin": {"$gte": 0, "$lte": cost_margin_filter_val}}
            else:
                cost_margin_query = {"cost_margin": {"$gte": cost_margin_filter_val, "$lte": 0}}

        # If no filters applied for multi-store, get all SKUs from skuvault_catalog
        if not all_skus and filter == '':
            # Get all SKUs (no pagination here, will be handled in MongoDB)
            query = """
                SELECT DISTINCT parent_sku
                FROM skuvault_catalog
                WHERE parent_sku IS NOT NULL AND parent_sku != ''
            """
            result = pg_conn.execute(text(query)).fetchall()
            all_skus_list = [row[0] for row in result]
            all_skus.update(all_skus_list)

            # Return for multi-store with no filters
            return list(all_skus), cost_margin_query, len(all_skus), list(all_skus)

        # Return for multi-store with filters
        return list(all_skus), cost_margin_query, len(all_skus), list(all_skus)

    else:
        # Single store - all filters enabled (original logic)
        filter_query = []

        if tag_filter:
            tag_skus = fetch_skus("SELECT sku FROM product_tags WHERE tag_id IN :tag_filter_list", 'tag_filter_list', tag_filter.split(','))
            tag_query = {"parent_product_sku": {"$in": tag_skus}}
            filter_query.append(tag_query)
            all_skus.update(tag_skus)

        if classified_as_filter:
            classified_skus = fetch_skus("SELECT parent_sku FROM replenishment_dashboard WHERE classified_as_id IN :classified_as_filter_list", 'classified_as_filter_list', classified_as_filter.split(','))
            classified_as_query = {"parent_product_sku": {"$in": classified_skus}}
            filter_query.append(classified_as_query)
            all_skus.update(classified_skus)

        if classification_filter:
            classification_skus = fetch_skus("SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE classification IN :classification_filter_list AND parent_sku IS NOT NULL AND parent_sku != ''", 'classification_filter_list', classification_filter.split(','))
            classification_query = {"parent_product_sku": {"$in": classification_skus}}
            filter_query.append(classification_query)
            all_skus.update(classification_skus)

        if user_filter:
            query = """
                SELECT ARRAY_AGG(DISTINCT suppliers)
                FROM user_supplier_mapping
                WHERE user_name = :user_name
            """
            res = pg_conn.execute(text(query), {'user_name': user_filter})
            suppliers_list = res.fetchone()[0] or []

            if suppliers_list:
                query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier = ANY(:suppliers_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
                result = pg_conn.execute(text(query), {'suppliers_list': suppliers_list}).fetchall()
                user_skus = [row[0] for row in result]
                user_query = {"parent_product_sku": {"$in": user_skus}}
                filter_query.append(user_query)
                all_skus.update(user_skus)

        if supplier_filter:
            supplier_filter_list = [supplier.strip() for supplier in supplier_filter.split(';')]
            query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier ILIKE ANY(:supplier_filter_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
            result = pg_conn.execute(text(query), {'supplier_filter_list': supplier_filter_list}).fetchall()
            supplier_skus = [row[0] for row in result]
            supplier_query = {"parent_product_sku": {"$in": supplier_skus}}
            filter_query.append(supplier_query)
            all_skus.update(supplier_skus)

        if products_filter:
            query = """SELECT DISTINCT sku FROM products WHERE product_id IN :products_filter_list"""
            result = pg_conn.execute(text(query), {'products_filter_list': list(map(int, products_filter.split(',')))})
            products_skus = [row[0] for row in result]
            products_query = {"parent_product_sku": {"$in": products_skus}}
            filter_query.append(products_query)
            all_skus.update(products_skus)

        if top_products_filter:
            top_n = int(top_products_filter)
            query = text(f"""
                SELECT parent_sku
                FROM {AnalyticsDB.get_products_revenue_table()}
                WHERE order_date >= NOW() - INTERVAL '30 days'
                GROUP BY product_id
                ORDER BY SUM(revenue) DESC
                LIMIT :top_n
            """)

            result = pg_conn.execute(query, {'top_n': top_n}).fetchall()
            top_product_skus = [row[0] for row in result]

            if top_product_skus:
                top_products_query = {"parent_product_sku": {"$in": top_product_skus}}
                filter_query.append(top_products_query)
                all_skus.update(top_product_skus)

        if cost_margin_filter:
            cost_margin_filter_val = int(cost_margin_filter)
            if cost_margin_filter_val > 0:
                filter_query.append({
                    "cost_margin": {"$gte": 0, "$lte": cost_margin_filter_val}
                })
            else:
                filter_query.append({
                    "cost_margin": {"$gte": cost_margin_filter_val, "$lte": 0}
                })

        # Return for single store
        return filter_query, len(all_skus), list(all_skus)

def _get_store_products_data_with_filters(store_id, store_conn, unified_filter_query, page, limit, field, sort_order, filter, role_id, apply_sorting=True):
    """
    Get products data for a specific store with pre-applied filters.

    Args:
        apply_sorting: If True, apply sorting and pagination. If False, get all data without sorting.
    """
    try:
        # Setup payload based on whether sorting should be applied
        if apply_sorting:
            payload = {
                "page": page,
                "limit": limit,
                "sort_by": field,
                "sort_order": sort_order,
                "filterBy": ["parent_product_name", "parent_product_sku", "variant_name", "variant_sku"],
                "filter": filter
            }
        else:
            # For secondary stores, get all data without pagination/sorting
            payload = {
                "page": 1,
                "limit": 10000,  # Large limit to get all matching products
                "sort_by": field,
                "sort_order": sort_order,
                "filterBy": ["parent_product_name", "parent_product_sku", "variant_name", "variant_sku"],
                "filter": filter
            }
        # Build additional query from unified_filter_query
        additional_query = {}
        if unified_filter_query:
            if len(unified_filter_query) == 1:
                additional_query = unified_filter_query[0]
            else:
                additional_query = {"$and": unified_filter_query}

        # Get products
        products, total_data_length, _, _ = get_paginated_records_price_list(
            store_conn['admin_db'], StoreAdminDBCollections.PRODUCT_PRICE_LISTS, payload, product_price_list_fields, additional_query
        )
        # Get price lists
        static_price_lists = fetch_static_price_lists(store_id)
        store_obj = _get_store_object_safely(store_id)
        price_lists = _get_price_lists_safely(store_obj, store_id)

        # Build price list mappings
        price_list_meta, price_list_map = _build_price_list_mappings(store_id, price_lists, static_price_lists, role_id)

        # Get price list assignments for sales percentage calculation
        assignments = list(store_conn['store_db'][StoreDBCollections.PRICE_LIST_ASSIGNMENT].find({
            "price_list_id": {"$exists": True},
            "customer_group_id": {"$exists": True}
        }))

        price_list_to_customer_group = {assignment['price_list_id']: assignment['customer_group_id'] for assignment in assignments}

        # Process products
        processed_products = _process_single_store_products_with_sales_percentage(store_id, products, store_conn, price_list_meta, price_list_map, role_id, price_list_to_customer_group)

        return {
            'products': processed_products,
            'total_count': total_data_length,
            'price_list_meta': price_list_meta,
            'price_list_map': price_list_map
        }

    except Exception as e:
        logger.error(f"Error getting store products data with filters for store {store_id}: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            'products': [],
            'total_count': 0,
            'price_list_meta': {},
            'price_list_map': {}
        }


def _process_single_store_products_original_format(store_id, products, store_conn, price_list_meta, price_list_map, role_id, price_list_to_customer_group):
    """
    Process products for single store in original format (same as get_product_price_lists).
    """
    # Get all parent SKUs for classification lookup
    all_parent_skus = [product['parent_product_sku'] for product in products]
    all_parent_product_ids = [product['parent_product_id'] for product in products]

    # Fetch margin and sales percentage data
    product_data_map_for_margin_and_sales = {}
    if all_parent_product_ids:
        try:
            product_data = _fetch_margin_and_sales_percentage(store_id, all_parent_product_ids)
            product_data_map_for_margin_and_sales = {
                (item['product_id'], item['customer_group_id']): {
                    "margin_percentage": item['margin_percentage'],
                    "sales_percentage": item['sales_percentage']
                }
                for item in product_data
            }
        except Exception as e:
            logger.error(f"Error fetching margin and sales percentage for store {store_id}: {e}")
            product_data_map_for_margin_and_sales = {}

    # Get classification data
    classification_map = {}
    if all_parent_skus:
        classification_query = text("""
            SELECT DISTINCT
                sc.parent_sku,
                sc.classification,
                sc.primary_supplier,
                usm.user_name
            FROM skuvault_catalog sc
            LEFT JOIN user_supplier_mapping usm
                ON sc.primary_supplier = usm.suppliers
            WHERE sc.parent_sku IN :parent_product_skus
        """)

        classification_results = store_conn['pg_conn'].execute(classification_query, {'parent_product_skus': tuple(all_parent_skus)}).fetchall()

        for parent_sku, classification, primary_supplier, user_name in classification_results:
            if parent_sku in classification_map:
                classification_map[parent_sku]['classifications'].add(classification)
            else:
                classification_map[parent_sku] = {
                    'classifications': {classification},
                    'primary_supplier': primary_supplier,
                    'user_name': user_name
                }

        # Convert classifications set to comma-separated string
        for sku in classification_map:
            classification_map[sku]['classifications'] = ", ".join(classification_map[sku]['classifications'])

    # Get user data for updated_by field
    updated_by_users = {product.get('updated_by') for product in products if product.get('updated_by')}
    user_data_map = user_db.fetch_users_by_usernames(updated_by_users) if updated_by_users else {}

    # Process each product (original format)
    for product in products:
        parent_product_sku = product.get('parent_product_sku', None)

        # Convert timestamps
        product['updated_at'] = convert_to_timestamp(product.get('updated_at'))
        product['created_at'] = convert_to_timestamp(product.get('created_at'))
        product['inventory_level'] = product.get('inventory_level', 0)

        # Add classification data
        product['classification'] = classification_map.get(parent_product_sku, {}).get('classifications', "")
        product['primary_supplier'] = classification_map.get(parent_product_sku, {}).get('primary_supplier', "")
        product['purchaser'] = classification_map.get(parent_product_sku, {}).get('user_name', "")

        # Assign user-friendly name for updated_by
        updated_by = product.get('updated_by')
        product['updated_by'] = user_data_map.get(updated_by, {}).get("name", updated_by)

        # Add monthly data if role allows
        if role_id != "67fd12676af694b36923ce09":
            for i in range(1, 8):
                product[f'month_{i}'] = product.get(f'month_{i}', None)

        product['turn_rate'] = product.get('turn_rate', 0)
        product['weeks_on_hand'] = product.get('weeks_on_hand', 0)

        # Process pricing data (original format)
        _process_product_pricing_original_format(product, price_list_meta, price_list_map, product_data_map_for_margin_and_sales, price_list_to_customer_group)

        # Remove variants from final output
        if 'variants' in product:
            del product['variants']

    return products

def _process_product_pricing_original_format(product, price_list_meta, price_list_map, product_data_map_for_margin_and_sales, price_list_to_customer_group):
    """
    Process pricing data for a product in original format (same as get_product_price_lists).
    """
    # Initialize pricing data
    price_list_data = {key: {"min": None, "max": None} for key in price_list_meta}
    variant_prices = []
    variant_costs = []

    for variant in product.get('variants', []):
        variant_price = product['default_price'] if variant.get('variant_price') is None else variant.get('variant_price')
        cost = variant.get('variant_cost', None)

        if variant_price is not None:
            variant_prices.append(variant_price)
        if cost is not None:
            variant_costs.append(cost)

        # Process price list efficiently
        for price in variant.get('price_list', []):
            price_list_key = price_list_map.get(price['price_list_id'])
            if price_list_key:
                price_value = float(price['price'])

                current_min = price_list_data[price_list_key]["min"]
                current_max = price_list_data[price_list_key]["max"]

                if current_min is None or price_value < current_min:
                    price_list_data[price_list_key]["min"] = price_value
                if current_max is None or price_value > current_max:
                    price_list_data[price_list_key]["max"] = price_value

    # Format the result as required (string range or single value)
    for key in price_list_data:
        min_price = price_list_data[key]["min"]
        max_price = price_list_data[key]["max"]
        if min_price is None:
            price_list_data[key] = ""
        elif min_price == max_price:
            price_list_data[key] = str(min_price)
        else:
            price_list_data[key] = f"{min_price}-{max_price}"

    # Set default_price as a range or single value
    if variant_prices:
        product_default_price = product['default_price']
        product['default_price'] = f"{product['default_price']}-{max(variant_prices)}" if len(set(variant_prices)) > 1 else str(product['default_price'])

    # Set cost_range as a range or single value
    product_cost_price = 0
    if variant_costs:
        product['cost_range'] = f"{min(variant_costs)}-{max(variant_costs)}" if len(set(variant_costs)) > 1 else str(min(variant_costs))
        product_cost_price = min(variant_costs)
    else:
        product['cost_range'] = None

    # Calculate margins
    if product_cost_price > 0 and product_default_price > 0:
        try:
            product['default_price_margin'] = round(((product_default_price - product_cost_price) / product_cost_price) * 100, 2)
        except:
            product['default_price_margin'] = 0
    else:
        product['default_price_margin'] = 0

    # Build price list array for product (original format)
    product["price_list"] = []
    for key, value in price_list_meta.items():
        price_list_id = value["id"]
        customer_group_id = price_list_to_customer_group.get(price_list_id)

        # Get margin and sales data for this product and customer group
        margin_sales = product_data_map_for_margin_and_sales.get(
            (product["parent_product_id"], customer_group_id),
            {"margin_percentage": 0.0, "sales_percentage": 0.0}
        )

        price_list_price = price_list_data.get(key, '')

        if '-' in str(price_list_price):
            min_price, max_price = map(float, str(price_list_price).split('-'))
            price_list_price = min_price
        if not price_list_price or price_list_price == '':
            price_list_price = 0

        try:
            price_list_price = float(price_list_price)
        except:
            price_list_price = 0

        margin_per = 0
        if price_list_price > 0 and product_cost_price > 0:
            margin_per = round(((price_list_price - product_cost_price) / product_cost_price) * 100, 2)

        product["price_list"].append({
            key: price_list_data[key],
            "price_list_id": price_list_id,
            "margin_percentage": margin_per,
            "sales_percentage": margin_sales["sales_percentage"]
        })


def _process_single_store_products_with_sales_percentage(store_id, products, store_conn, price_list_meta, price_list_map, role_id, price_list_to_customer_group):
    """
    Process products for single store with sales percentage calculation (for multi-store).
    """
    # Get all parent SKUs for classification lookup
    all_parent_skus = [product['parent_product_sku'] for product in products]
    all_parent_product_ids = [product['parent_product_id'] for product in products]

    # Fetch margin and sales percentage data
    product_data_map_for_margin_and_sales = {}
    if all_parent_product_ids:
        try:
            product_data = _fetch_margin_and_sales_percentage(store_id, all_parent_product_ids)
            product_data_map_for_margin_and_sales = {
                (item['product_id'], item['customer_group_id']): {
                    "margin_percentage": item['margin_percentage'],
                    "sales_percentage": item['sales_percentage']
                }
                for item in product_data
            }
        except Exception as e:
            logger.error(f"Error fetching margin and sales percentage for store {store_id}: {e}")
            product_data_map_for_margin_and_sales = {}

    # Get classification data
    classification_map = {}
    if all_parent_skus:
        classification_query = text("""
            SELECT DISTINCT
                sc.parent_sku,
                sc.classification,
                sc.primary_supplier,
                usm.user_name
            FROM skuvault_catalog sc
            LEFT JOIN user_supplier_mapping usm
                ON sc.primary_supplier = usm.suppliers
            WHERE sc.parent_sku IN :parent_product_skus
        """)

        classification_results = store_conn['pg_conn'].execute(classification_query, {'parent_product_skus': tuple(all_parent_skus)}).fetchall()

        for parent_sku, classification, primary_supplier, user_name in classification_results:
            if parent_sku in classification_map:
                classification_map[parent_sku]['classifications'].add(classification)
            else:
                classification_map[parent_sku] = {
                    'classifications': {classification},
                    'primary_supplier': primary_supplier,
                    'user_name': user_name
                }

        # Convert classifications set to comma-separated string
        for sku in classification_map:
            classification_map[sku]['classifications'] = ", ".join(classification_map[sku]['classifications'])

    # Get user data for updated_by field
    updated_by_users = {product.get('updated_by') for product in products if product.get('updated_by')}
    user_data_map = user_db.fetch_users_by_usernames(updated_by_users) if updated_by_users else {}

    # Process each product
    for product in products:
        parent_product_sku = product.get('parent_product_sku', None)

        # Convert timestamps
        product['updated_at'] = convert_to_timestamp(product.get('updated_at'))
        product['created_at'] = convert_to_timestamp(product.get('created_at'))
        product['inventory_level'] = product.get('inventory_level', 0)

        # Add classification data
        product['classification'] = classification_map.get(parent_product_sku, {}).get('classifications', "")
        product['primary_supplier'] = classification_map.get(parent_product_sku, {}).get('primary_supplier', "")
        product['purchaser'] = classification_map.get(parent_product_sku, {}).get('user_name', "")

        # Assign user-friendly name for updated_by
        updated_by = product.get('updated_by')
        product['updated_by'] = user_data_map.get(updated_by, {}).get("name", updated_by)

        # Add monthly data if role allows
        if role_id != "67fd12676af694b36923ce09":
            for i in range(1, 8):
                product[f'month_{i}'] = product.get(f'month_{i}', None)

        product['turn_rate'] = product.get('turn_rate', 0)
        product['weeks_on_hand'] = product.get('weeks_on_hand', 0)

        # Process pricing data with sales percentage
        _process_product_pricing_with_sales_percentage(product, price_list_meta, price_list_map, product_data_map_for_margin_and_sales, price_list_to_customer_group)

        # Remove variants from final output
        if 'variants' in product:
            del product['variants']

    return products

def _process_product_pricing_with_sales_percentage(product, price_list_meta, price_list_map, product_data_map_for_margin_and_sales, price_list_to_customer_group):
    """
    Process pricing data for a product with sales percentage calculation.
    """
    # Initialize pricing data
    price_list_data = {key: {"min": None, "max": None} for key in price_list_meta}
    variant_prices = []
    variant_costs = []

    for variant in product.get('variants', []):
        variant_price = product['default_price'] if variant.get('variant_price') is None else variant.get('variant_price')
        cost = variant.get('variant_cost', None)

        if variant_price is not None:
            variant_prices.append(variant_price)
        if cost is not None:
            variant_costs.append(cost)

        # Process price list efficiently
        for price in variant.get('price_list', []):
            price_list_key = price_list_map.get(price['price_list_id'])
            if price_list_key:
                price_value = float(price['price'])

                current_min = price_list_data[price_list_key]["min"]
                current_max = price_list_data[price_list_key]["max"]

                if current_min is None or price_value < current_min:
                    price_list_data[price_list_key]["min"] = price_value
                if current_max is None or price_value > current_max:
                    price_list_data[price_list_key]["max"] = price_value

    # Format the result as required (string range or single value)
    for key in price_list_data:
        min_price = price_list_data[key]["min"]
        max_price = price_list_data[key]["max"]
        if min_price is None:
            price_list_data[key] = ""
        elif min_price == max_price:
            price_list_data[key] = str(min_price)
        else:
            price_list_data[key] = f"{min_price}-{max_price}"

    # Set default_price as a range or single value
    if variant_prices:
        product_default_price = product['default_price']
        product['default_price'] = f"{product['default_price']}-{max(variant_prices)}" if len(set(variant_prices)) > 1 else str(product['default_price'])

    # Set cost_range as a range or single value
    product_cost_price = 0
    if variant_costs:
        product['cost_range'] = f"{min(variant_costs)}-{max(variant_costs)}" if len(set(variant_costs)) > 1 else str(min(variant_costs))
        product_cost_price = min(variant_costs)
    else:
        product['cost_range'] = None

    # Calculate margins
    if product_cost_price > 0 and product_default_price > 0:
        try:
            product['default_price_margin'] = round(((product_default_price - product_cost_price) / product_cost_price) * 100, 2)
        except:
            product['default_price_margin'] = 0
    else:
        product['default_price_margin'] = 0

    # Build price list array for product with sales percentage
    product["price_list"] = []
    for key, value in price_list_meta.items():
        price_list_id = value["id"]
        customer_group_id = price_list_to_customer_group.get(price_list_id)

        # Get margin and sales data for this product and customer group
        margin_sales = product_data_map_for_margin_and_sales.get(
            (product["parent_product_id"], customer_group_id),
            {"margin_percentage": 0.0, "sales_percentage": 0.0}
        )

        price_list_price = price_list_data.get(key, '')

        if '-' in str(price_list_price):
            min_price, max_price = map(float, str(price_list_price).split('-'))
            price_list_price = min_price
        if not price_list_price or price_list_price == '':
            price_list_price = 0

        try:
            price_list_price = float(price_list_price)
        except:
            price_list_price = 0

        margin_per = 0
        if price_list_price > 0 and product_cost_price > 0:
            margin_per = round(((price_list_price - product_cost_price) / product_cost_price) * 100, 2)

        product["price_list"].append({
            key: price_list_data[key],
            "price_list_id": price_list_id,
            "margin_percentage": margin_per,
            "sales_percentage": margin_sales["sales_percentage"]
        })

def _get_store_suffix(store_id):
    """
    Get store suffix for naming store-specific fields.
    """
    # You can customize this mapping based on your store naming convention
    store_suffixes = {
        'store1': 'midwest',
        'store2': 'cbd',
        # Add more mappings as needed
    }
    return store_suffixes.get(store_id, store_id.lower())


def _get_store_object_safely(store_id):
    """
    Safely get store object by ID with error handling.
    """
    try:
        store_obj = store_util.get_store_by_id(store_id)
        return store_obj

    except Exception as e:
        logger.error(f"Error getting store object for {store_id}: {e}")
        return None

def _get_price_lists_safely(store_obj, store_id):
    """
    Safely get price lists from BigCommerce with error handling.
    """
    try:
        if store_obj:
            price_lists, _ = bc_price_list.fetch_price_lists(store_obj)
            return price_lists
        else:
            logger.warning(f"No store object provided for store {store_id}")
            return {'data': []}
    except Exception as e:
        logger.error(f"Error fetching BigCommerce price lists for store {store_id}: {e}")
        logger.error(f"Store object keys: {list(store_obj.keys()) if store_obj else 'None'}")
        return {'data': []}

def _map_price_lists_between_stores(price_list_mappings):
    """
    Map price lists between stores by title, handling different price_list_ids, using the reference price list order.
    Returns a dict with keys price_1, price_2, ...
    """
    refrence_price_lists = {
        "Value Tier Price List": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 7,
                "661239751b9ce4bd7f85237c": 4
            }
        },
        "Tier 1 Price List": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 1,
                "661239751b9ce4bd7f85237c": 1
            }
        },
        "Tier 2 Price List": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 4,
                "661239751b9ce4bd7f85237c": 2
            }
        },
        "Tier Pro Price List": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 8,
                "661239751b9ce4bd7f85237c": 3
            }
        },
        "Distributor": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 13,
                "661239751b9ce4bd7f85237c": 5
            }
        },
        "MVD Warehouse": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 15,
                "661239751b9ce4bd7f85237c": 7
            }
        },
        "TCD Warehouse": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 14,
                "661239751b9ce4bd7f85237c": 6
            }
        },
        "VIP": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 52,
                "661239751b9ce4bd7f85237c": 52
            }
        },
    }
    # Get the set of store_ids actually present in the mappings
    store_ids = set(price_list_mappings.keys())
    unified_mapping = {}
    for idx, (name, ref_entry) in enumerate(refrence_price_lists.items(), 1):
        # Only include if at least one store_id is present in store_mappings
        store_mappings = {sid: plid for sid, plid in ref_entry['store_mappings'].items() if sid in store_ids}
        if not store_mappings:
            continue
        unified_mapping[f"price_{idx}"] = {
            "name": name,
            "active": ref_entry["active"],
            "store_mappings": store_mappings
        }
    return unified_mapping

# Debug function to test store connections
def debug_store_connection(store_id):
    """
    Debug function to test store connections and identify issues.
    """
    try:
        logger.info(f"Testing store connection for store_id: {store_id}")

        # Test store object retrieval
        store_obj = store_util.get_store_by_id(store_id)
        logger.info(f"Store object retrieved: {store_obj is not None}")
        if store_obj:
            logger.info(f"Store object keys: {list(store_obj.keys())}")
            logger.info(f"Has client_id: {'client_id' in store_obj}")
            logger.info(f"Has access_token: {'access_token' in store_obj}")

        # Test database connections
        try:
            admin_db = get_admin_db_client_for_store_id(store_id)
            logger.info(f"Admin DB connection: {admin_db is not None}")
        except Exception as e:
            logger.error(f"Admin DB connection failed: {e}")

        try:
            store_db = get_store_db_client_for_store_id(store_id)
            logger.info(f"Store DB connection: {store_db is not None}")
        except Exception as e:
            logger.error(f"Store DB connection failed: {e}")

        try:
            pg_conn = new_pgdb.get_connection(store_id)
            logger.info(f"PostgreSQL connection: {pg_conn is not None}")
            if pg_conn:
                pg_conn.close()
        except Exception as e:
            logger.error(f"PostgreSQL connection failed: {e}")

        # Test BigCommerce API call
        if store_obj:
            try:
                price_lists, _ = bc_price_list.fetch_price_lists(store_obj)
                logger.info(f"BigCommerce price lists fetched successfully: {len(price_lists.get('data', []))} price lists")
            except Exception as e:
                logger.error(f"BigCommerce API call failed: {e}")
                logger.error(f"Error type: {type(e).__name__}")

        return True

    except Exception as e:
        logger.error(f"Debug store connection failed: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def _fetch_margin_and_sales_percentage(store_id, product_ids):
    """
    Fetch margin and sales percentage data from analytics.profitability_product_customer_groups table.
    """
    conn = new_pgdb.get_connection(store_id)
    try:
        query = text("""WITH product_filter AS (
                        SELECT unnest(CAST(:product_ids AS int[])) AS product_id
                        )
                        SELECT
                            li.product_id,
                            li.customer_group_id,
                            SUM(li.revenue) AS revenue,
                            SUM(li.total_cost) AS total_cost,
                            SUM(li.profit) AS profit
                        FROM analytics.profitability_product_customer_groups li
                        JOIN product_filter pf ON pf.product_id = li.product_id
                        WHERE li.order_date >= current_date - interval '30 day'
                        GROUP BY li.product_id, li.customer_group_id;
                """)
        result = conn.execute(query, {"product_ids": list(product_ids)}).fetchall()

        # Step 1: Group data and calculate total revenue per product
        grouped_data = [
            {
                'product_id': row[0],
                'customer_group_id': row[1],
                'revenue': Decimal(str(row[2])) if row[2] else Decimal('0'),
                'total_cost': Decimal(str(row[3])) if row[3] else Decimal('0'),
                'profit': Decimal(str(row[4])) if row[4] else Decimal('0')
            }
            for row in result
        ]

        total_revenue_per_product = defaultdict(Decimal)
        for item in grouped_data:
            total_revenue_per_product[item['product_id']] += item['revenue']

        # Step 2: Build final list with percentages (minimal loop)
        product_data = []
        for item in grouped_data:
            revenue = item['revenue']
            product_id = item['product_id']

            sales_pct = (revenue * 100 / total_revenue_per_product[product_id]).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP) if total_revenue_per_product[product_id] > 0 else Decimal('0.00')

            product_data.append({
                'product_id': product_id,
                'customer_group_id': item['customer_group_id'],
                'margin_percentage': 0,
                'sales_percentage': float(sales_pct)
            })

        return product_data

    except Exception as e:
        _ = e  # Suppress unused variable warning
        logger.error("Error in _fetch_margin_and_sales_percentage:\n" + traceback.format_exc())
        return []

    finally:
        conn.close()

def get_multi_store_product_price_list_details(store_ids, product_sku, user, tag_filter=None, store_name=None):
    store_ids = _get_store_ids_from_store_name(store_ids, store_name)
    if not store_ids or len(store_ids) == 0:
        return {"data": [], "meta": {"price_lists": {}, "months": {}}}

    # If only one store, return original format
    if len(store_ids) == 1:
        return _get_single_store_product_details_original_format(store_ids[0], product_sku, user, tag_filter)

    # Multiple stores logic
    return _get_multi_store_product_details(store_ids, product_sku, user, tag_filter)

def _get_single_store_product_details_original_format(store_id, product_sku, user, tag_filter=None):
    """
    Get product details from single store with same response structure as multi-store.
    """
    try:
        # Setup connections for single store
        store_connections = _setup_store_connections([store_id])
        store_conn = store_connections[store_id]

        # Get role_id and setup price lists
        role_id = user.get('role_id')
        static_price_lists = fetch_static_price_lists(store_id)
        store_obj = _get_store_object_safely(store_id)
        price_lists = _get_price_lists_safely(store_obj, store_id)

        # Build price list mappings for details (keep original flow)
        price_list_meta, price_list_map, active_price_lists = _build_price_list_mappings_for_details(store_id, price_lists, static_price_lists, role_id)

        # Get price list assignments for sales percentage calculation
        assignments = list(store_conn['store_db'][StoreDBCollections.PRICE_LIST_ASSIGNMENT].find({
            "price_list_id": {"$exists": True},
            "customer_group_id": {"$exists": True}
        }))

        price_list_to_customer_group = {assignment['price_list_id']: assignment['customer_group_id'] for assignment in assignments}

        # Handle tag filter for variant filtering
        variant_sku_list = None
        if tag_filter:
            variant_sku_list = _get_variant_skus_from_tags(store_conn['pg_conn'], tag_filter)

        # Get product data
        product = _get_product_by_id_with_variants(store_conn['admin_db'], product_sku, variant_sku_list)

        if not product:
            # Create unified price list mapping for empty response
            price_list_mappings = {store_id: price_list_meta}
            unified_price_list_mapping = _map_price_lists_between_stores(price_list_mappings)

            month_names, _ = get_month_array_for_meta(6)
            return {
                "data": [],
                "meta": {
                    "price_lists": unified_price_list_mapping,
                    "months": month_names,
                    "stores": {
                        store_id: {
                            'id': store_id,
                            'name': store_conn['store_name'],
                            'suffix': _get_store_suffix(store_id)
                        }
                    }
                }
            }

        # Process product details (keep original processing)
        processed_product = _process_product_details_original_format(
            product, store_conn, price_list_meta, price_list_map, active_price_lists,
            price_list_to_customer_group, role_id, store_id
        )

        # Convert single store product details to multi-store format with nested structure
        converted_product = _convert_single_store_product_details_to_nested_multi_store_format(processed_product, store_id, role_id)

        # Create unified price list mapping (same as multi-store)
        price_list_mappings = {store_id: price_list_meta}
        unified_price_list_mapping = _map_price_lists_between_stores(price_list_mappings)

        # Create response (same structure as multi-store)
        month_names, _ = get_month_array_for_meta(6)
        data = {
            "data": [converted_product],
            "meta": {
                "price_lists": unified_price_list_mapping,
                "months": month_names if role_id != "67fd12676af694b36923ce09" else {},
                "stores": {
                    store_id: {
                        'id': store_id,
                        'name': store_conn['store_name'],
                        'suffix': _get_store_suffix(store_id)
                    }
                }
            }
        }

        return data

    except Exception as e:
        logger.error(f"Error in _get_single_store_product_details_original_format: {traceback.format_exc()}")
        raise e
    finally:
        _close_store_connections(store_connections)

def _convert_single_store_product_details_to_nested_multi_store_format(product, store_id, role_id):
    """
    Convert single store product details format to nested multi-store format.
    """
    # Extract store-specific data for parent product
    store_data = {
        'store_id': store_id,
        'store_name': "Midwest" if store_id == "63da3e98b702e324567f76f9" else "CBD",
        'default_price': product.get('default_price'),
        'default_price_margin': product.get('default_price_margin'),
        'default_price_sales': product.get('default_price_sales'),
        'cost_range': product.get('cost_range'),
        'cost_margin': product.get('cost_margin'),
        'minimum_cost': product.get('minimum_cost'),
        'price_list': product.get('price_list', [])
    }

    # Add monthly data if role allows
    if role_id != "67fd12676af694b36923ce09":
        for i in range(1, 8):
            month_field = f'month_{i}'
            store_data[month_field] = product.get(month_field)

    # Process variants to have store-specific data
    variants_with_stores = []
    for variant in product.get('variants', []):
        # Common variant details (non-store-specific)
        variant_common = {
            'variant_name': variant.get('variant_name'),
            'variant_sku': variant.get('variant_sku'),
            'classification': variant.get('classification', ''),
            'parent_product_id': variant.get('parent_product_id'),
            'parent_product_name': variant.get('parent_product_name'),
            'show_parent_product': product.get('show_parent_product'),
            'stores': []
        }

        # Store-specific variant details
        store_variant_data = {
            'store_id': store_id,
            'store_name': "Midwest" if store_id == "63da3e98b702e324567f76f9" else "CBD",
            'variant_id': variant.get('variant_id'),
            'variant_price': variant.get('variant_price'),
            'variant_cost': variant.get('variant_cost'),
            'variant_po_average_cost': variant.get('variant_po_average_cost'),
            'variant_latest_po_cost': variant.get('variant_latest_po_cost'),
            'inventory_level': variant.get('inventory_level'),
            'turn_rate': variant.get('turn_rate', 0),
            'weeks_on_hand': variant.get('weeks_on_hand', 0),
            'cost': variant.get('cost'),
            'price_list': variant.get('price_list', []),
            'date_modified': variant.get('date_modified')
        }

        # Add monthly data if role allows
        if role_id != "67fd12676af694b36923ce09":
            for i in range(1, 8):
                month_field = f'month_{i}'
                store_variant_data[month_field] = variant.get(month_field)

        variant_common['stores'].append(store_variant_data)
        variants_with_stores.append(variant_common)

    # Create multi-store format product
    converted_product = {
        # Common fields at parent level
        'parent_product_id': product.get('parent_product_id'),
        'parent_product_name': product.get('parent_product_name'),
        'parent_product_sku': product.get('parent_product_sku'),
        'show_parent_product': product.get('show_parent_product'),

        # Store-specific data in stores array
        'stores': [store_data],
        
        # Variants array with store-specific data
        'variants': variants_with_stores
    }

    return converted_product

def _get_multi_store_product_details(store_ids, product_sku, user, tag_filter=None):
    """
    Get product details from multiple stores with nested structure.
    """
    try:
        # Setup connections for all stores
        store_connections = _setup_store_connections(store_ids)

        # Get role_id
        role_id = user.get('role_id')

        # Get data from each store
        all_store_data = {}
        price_list_mappings = {}
        store_names = {}

        for store_id in store_ids:
            store_names[store_id] = store_connections[store_id]['store_name']
            store_data = _get_store_product_details_data(store_id, store_connections[store_id], product_sku, user, tag_filter)
            all_store_data[store_id] = store_data
            if store_data['product']:
                price_list_mappings[store_id] = store_data['price_list_meta']

        # Map price lists between stores by title
        unified_price_list_mapping = _map_price_lists_between_stores(price_list_mappings)

        # Create multi-store response for product details with nested structure
        multi_store_response = _create_nested_multi_store_product_details_response(all_store_data, store_ids, unified_price_list_mapping, role_id, store_names)

        return multi_store_response

    except Exception as e:
        logger.error(f"Error in _get_multi_store_product_details: {traceback.format_exc()}")
        raise e
    finally:
        _close_store_connections(store_connections)

def _create_nested_multi_store_product_details_response(all_store_data, store_ids, unified_price_list_mapping, role_id, store_names):
    """
    Create multi-store response for product details with nested structure.
    """
    try:
        # Find the first valid product to get common data
        common_product_data = None
        for store_id in store_ids:
            if all_store_data[store_id]['product']:
                common_product_data = all_store_data[store_id]['product']
                break

        if not common_product_data:
            month_names, _ = get_month_array_for_meta(6)
            return {"data": [], "meta": {"price_lists": unified_price_list_mapping, "months": month_names}}

        # Create store-specific objects for parent product
        stores_data = []
        for store_id in store_ids:
            store_data = all_store_data[store_id]
            if store_data['product']:
                store_object = {
                    'store_id': store_id,
                    'store_name': "Midwest" if store_id == "63da3e98b702e324567f76f9" else "CBD",
                    'default_price': store_data['product']['default_price'],
                    'default_price_margin': store_data['product']['default_price_margin'],
                    'default_price_sales': store_data['product']['default_price_sales'],
                    'cost_range': store_data['product']['cost_range'],
                    'cost_margin': store_data['product'].get('cost_margin'),
                    'minimum_cost': store_data['product']['minimum_cost'],
                    'price_list': store_data['product'].get('price_list', [])
                }

                # Add monthly data if role allows
                if role_id != "67fd12676af694b36923ce09":
                    for i in range(1, 8):
                        month_field = f'month_{i}'
                        store_object[month_field] = store_data['product'].get(month_field)

                stores_data.append(store_object)

        # Create variants with store-specific data
        variants_with_stores = []
        
        # Get all unique variant SKUs from all stores
        all_variant_skus = set()
        for store_id in store_ids:
            if all_store_data[store_id]['product'] and all_store_data[store_id]['product'].get('variants'):
                for variant in all_store_data[store_id]['product']['variants']:
                    all_variant_skus.add(variant.get('variant_sku'))

        # Process each variant
        for variant_sku in all_variant_skus:
            # Find the first store that has this variant to get common data
            common_variant_data = None
            for store_id in store_ids:
                if all_store_data[store_id]['product'] and all_store_data[store_id]['product'].get('variants'):
                    for variant in all_store_data[store_id]['product']['variants']:
                        if variant.get('variant_sku') == variant_sku:
                            common_variant_data = variant
                            break
                    if common_variant_data:
                        break

            if not common_variant_data:
                continue

            # Common variant details (non-store-specific)
            variant_common = {
                'variant_name': common_variant_data.get('variant_name'),
                'variant_sku': variant_sku,
                'classification': common_variant_data.get('classification', ''),
                'parent_product_id': common_variant_data.get('parent_product_id'),
                'parent_product_name': common_variant_data.get('parent_product_name'),
                'show_parent_product': common_product_data.get('show_parent_product'),
                'stores': []
            }

            # Store-specific variant details
            for store_id in store_ids:
                store_variant_data = None
                if all_store_data[store_id]['product'] and all_store_data[store_id]['product'].get('variants'):
                    for variant in all_store_data[store_id]['product']['variants']:
                        if variant.get('variant_sku') == variant_sku:
                            store_variant_data = variant
                            break

                if store_variant_data:
                    store_variant_object = {
                        'store_id': store_id,
                        'store_name': "Midwest" if store_id == "63da3e98b702e324567f76f9" else "CBD",
                        'variant_id': store_variant_data.get('variant_id'),
                        'variant_price': store_variant_data.get('variant_price'),
                        'variant_cost': store_variant_data.get('variant_cost'),
                        'variant_po_average_cost': store_variant_data.get('variant_po_average_cost'),
                        'variant_latest_po_cost': store_variant_data.get('variant_latest_po_cost'),
                        'inventory_level': store_variant_data.get('inventory_level'),
                        'turn_rate': store_variant_data.get('turn_rate', 0),
                        'weeks_on_hand': store_variant_data.get('weeks_on_hand', 0),
                        'cost': store_variant_data.get('cost'),
                        'price_list': store_variant_data.get('price_list', []),
                        'date_modified': store_variant_data.get('date_modified')
                    }

                    # Add monthly data if role allows
                    if role_id != "67fd12676af694b36923ce09":
                        for i in range(1, 8):
                            month_field = f'month_{i}'
                            store_variant_object[month_field] = store_variant_data.get(month_field)

                    variant_common['stores'].append(store_variant_object)

            variants_with_stores.append(variant_common)

        # Build final response
        final_product = {
            # Common fields at parent level
            'parent_product_id': common_product_data['parent_product_id'],
            'parent_product_name': common_product_data['parent_product_name'],
            'parent_product_sku': common_product_data['parent_product_sku'],
            'show_parent_product': common_product_data['show_parent_product'],

            # Store-specific objects for parent product
            'stores': stores_data,
            
            # Variants array with store-specific data
            'variants': variants_with_stores
        }

        month_names, _ = get_month_array_for_meta(6)
        data = {
            "data": [final_product],
            "meta": {
                "price_lists": unified_price_list_mapping,
                "months": month_names if role_id != "67fd12676af694b36923ce09" else {},
                "stores": {
                    store_id: {
                        'id': store_id,
                        'name': store_names[store_id],
                        'suffix': _get_store_suffix(store_id)
                    } for store_id in store_ids
                }
            }
        }

        return data

    except Exception as e:
        logger.error(f"Error creating nested multi-store product details response: {traceback.format_exc()}")
        raise e

def _build_price_list_mappings_for_details(store_id, price_lists, static_price_lists, role_id):
    """
    Build price list mappings for product details (similar to original function),
    now using refrence_price_lists for ordering and mapping.
    """
    refrence_price_lists = {
        "Value Tier Price List": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 7,
                "661239751b9ce4bd7f85237c": 4
            }
        },
        "Tier 1 Price List": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 1,
                "661239751b9ce4bd7f85237c": 1
            }
        },
        "Tier 2 Price List": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 4,
                "661239751b9ce4bd7f85237c": 2
            }
        },
        "Tier Pro Price List": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 8,
                "661239751b9ce4bd7f85237c": 3
            }
        },
        "Distributor": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 13,
                "661239751b9ce4bd7f85237c": 5
            }
        },
        "MVD Warehouse": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 15,
                "661239751b9ce4bd7f85237c": 7
            }
        },
        "TCD Warehouse": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 14,
                "661239751b9ce4bd7f85237c": 6
            }
        },
        "VIP": {
            "active": True,
            "store_mappings": {
                "63da3e98b702e324567f76f9": 52,
                "661239751b9ce4bd7f85237c": 52
            }
        },
    }

    # Merge static price lists into price_lists['data']
    if 'data' not in price_lists:
        price_lists['data'] = []
    for static_price_list in static_price_lists:
        price_lists['data'].append({
            'id': static_price_list['id'],
            'name': static_price_list['name'],
            'date_created': None,
            'date_modified': None,
            'active': static_price_list['active']
        })

    # Role-specific filtering
    if role_id == '67f5f9c43c97938e59357472':
        price_lists['data'] = [pl for pl in price_lists['data'] if pl['id'] not in (14, 15)]
    elif role_id == '67fd12676af694b36923ce09':
        price_lists['data'] = [pl for pl in price_lists['data'] if pl['id'] in (13, 15, 52)]

    # Build price_list_map and active_price_lists (no sorting, just mapping by id)
    price_list_map = {}
    active_price_lists = []
    for price_list in price_lists['data']:
        entry = {
            'id': price_list['id'],
            'key': None,  # will be set in sorting step
            'name': price_list['name'],
            'active': price_list['active']
        }
        if price_list['active']:
            active_price_lists.append(entry)
        price_list_map[price_list['id']] = entry

    # Sorting logic using refrence_price_lists order
    def sort_price_lists_by_static_order(refrence_price_lists, store_id, price_list_map, active_price_lists):
        ordered_names = list(refrence_price_lists.keys())
        id_to_active = {pl['id']: pl for pl in active_price_lists}
        id_to_map = {pl_id: pl_map for pl_id, pl_map in price_list_map.items()}
        sorted_active_price_lists = []
        sorted_price_list_meta = {}
        sorted_price_list_map = {}
        idx = 1
        for name in ordered_names:
            static_entry = refrence_price_lists[name]
            price_list_id = static_entry['store_mappings'].get(store_id)
            if price_list_id is None:
                continue
            active_entry = id_to_active.get(price_list_id)
            map_entry = id_to_map.get(price_list_id)
            if active_entry and map_entry:
                price_list_key = f"price_{idx}"
                active_entry_sorted = active_entry.copy()
                active_entry_sorted['key'] = price_list_key
                sorted_active_price_lists.append(active_entry_sorted)
                sorted_price_list_meta[price_list_key] = {
                    'id': active_entry['id'],
                    'name': active_entry['name'],
                    'active': active_entry['active']
                }
                map_entry_sorted = map_entry.copy()
                map_entry_sorted['key'] = price_list_key
                sorted_price_list_map[active_entry['id']] = map_entry_sorted
                idx += 1
        return sorted_price_list_meta, sorted_price_list_map, sorted_active_price_lists

    sorted_price_list_meta, sorted_price_list_map, sorted_active_price_lists = sort_price_lists_by_static_order(
        refrence_price_lists, store_id, price_list_map, active_price_lists
    )

    return sorted_price_list_meta, sorted_price_list_map, sorted_active_price_lists

def _get_variant_skus_from_tags(pg_conn, tag_filter):
    """
    Get variant SKUs from tag filter.
    """
    try:
        tag_filter_list = [tag.strip() for tag in tag_filter.split(',')]
        query = text("SELECT variant_sku FROM product_tags WHERE tag_id = ANY(:tag_filter_list)")
        result = pg_conn.execute(query, {"tag_filter_list": tag_filter_list}).fetchall()
        tag_skus = [row[0] for row in result]
        return [sku for sku in tag_skus if sku is not None]
    except Exception as e:
        logger.error(f"Error getting variant SKUs from tags: {e}")
        return []

def _get_product_by_id_with_variants(admin_db, product_sku, variant_sku_list=None):
    """
    Get product by SKU with optional variant filtering.
    """
    try:
        if variant_sku_list:
            product = admin_db[StoreAdminDBCollections.PRODUCT_PRICE_LISTS].aggregate([
                {
                    "$match": {
                        "parent_product_sku": product_sku
                    }
                },
                {
                    "$addFields": {
                        "variants": {
                            "$filter": {
                                "input": "$variants",
                                "as": "v",
                                "cond": {"$in": ["$$v.variant_sku", variant_sku_list]}
                            }
                        }
                    }
                }
            ])
            return next(product, None)
        else:
            return admin_db[StoreAdminDBCollections.PRODUCT_PRICE_LISTS].find_one({
                "parent_product_sku": product_sku
            })
    except Exception as e:
        logger.error(f"Error getting product by SKU: {e}")
        return None

def _process_product_details_original_format(product, store_conn, price_list_meta, price_list_map, active_price_lists, price_list_to_customer_group, role_id, store_id):
    """
    Process product details in original format (same as get_product_price_list_details).
    """
    try:
        # Collect all variant SKUs
        variant_skus = [variant['variant_sku'] for variant in product['variants'] if variant.get('variant_sku')]

        # Get classification data for variants
        classification_map = {}
        if variant_skus:
            classification_query = text("""
                SELECT DISTINCT
                    sc.sku,
                    sc.classification
                FROM skuvault_catalog sc
                WHERE sc.sku IN :variant_skus
            """)

            classification_results = store_conn['pg_conn'].execute(classification_query, {'variant_skus': tuple(variant_skus)}).fetchall()

            for sku, classification in classification_results:
                if sku in classification_map:
                    classification_map[sku]['classifications'].add(classification)
                else:
                    classification_map[sku] = {
                        'classifications': {classification},  # use a set for uniqueness
                    }

            # Convert classifications set to a comma-separated string
            for sku in classification_map:
                classification_map[sku]['classifications'] = ", ".join(classification_map[sku]['classifications'])

        parent_product_id = product['parent_product_id']

        # Fetch margin and sales percentage data
        product_data_map = {}
        if parent_product_id:
            product_data = _fetch_margin_and_sales_percentage(store_id, [parent_product_id])
            # Build a map based on customer_group_id only
            product_data_map = {
                item['customer_group_id']: {
                    "margin_percentage": item['margin_percentage'],
                    "sales_percentage": item['sales_percentage']
                }
                for item in product_data
            }

        # Process variants and calculate price ranges
        variant_costs = []
        price_list_data = {key: "" for key in price_list_meta}
        minimum_price_data = {}

        for variant in product['variants']:
            # Process price list efficiently
            for price in variant['price_list']:
                price_list_key = price_list_map.get(price['price_list_id'])
                if price_list_key:
                    price_list_key = price_list_key['key']
                    price_value = float(price['price'])
                    if price_list_data[price_list_key]:  # Already has a value
                        min_price, max_price = map(float, price_list_data[price_list_key].split('-')) if '-' in price_list_data[price_list_key] else (float(price_list_data[price_list_key]), float(price_list_data[price_list_key]))
                        price_list_data[price_list_key] = f"{min(min_price, price_value)}-{max(max_price, price_value)}" if min_price != max_price else str(min_price)
                    else:
                        price_list_data[price_list_key] = str(price_value)

                    # Track minimum price for each price list
                    minimum_price_key = f"minimum_{price_list_key}"
                    if minimum_price_key in minimum_price_data:
                        current_min = float(minimum_price_data[minimum_price_key])  # convert string to float
                        minimum_price_data[minimum_price_key] = str(min(current_min, price_value))
                    else:
                        minimum_price_data[minimum_price_key] = str(price_value)

            # Process variant data
            variant['classification'] = classification_map.get(variant['variant_sku'], {}).get('classifications', "")
            variant['variant_price'] = product['default_price'] if variant.get('variant_price') is None else variant.get('variant_price')
            variant_price_lists = []
            default_price_lists = {price_list['id']: "" for price_list in active_price_lists}

            for price in variant['price_list']:
                price_list_id = price['price_list_id']
                if price_list_id in price_list_map and price_list_map[price_list_id]['active']:
                    default_price_lists[price_list_id] = str(price.pop('price'))

            for price_list in active_price_lists:
                variant_price_lists.append({
                    "price_list_id": price_list['id'],
                    price_list['key']: default_price_lists[price_list['id']]
                })

            variant['price_list'] = variant_price_lists
            variant["parent_product_id"] = product['parent_product_id']
            variant["parent_product_name"] = product['parent_product_name']
            variant["inventory_level"] = variant.get("inventory_level")

            # Add monthly data if role allows
            if role_id != "67fd12676af694b36923ce09":
                for i in range(1, 8):
                    variant[f'month_{i}'] = variant.get(f'month_{i}', None)

            variant['turn_rate'] = variant.get('turn_rate', 0)
            variant['weeks_on_hand'] = variant.get('weeks_on_hand', 0)
            variant['cost'] = variant.get('variant_cost', None)
            variant['variant_cost'] = variant.get('variant_cost', None)
            variant['variant_po_average_cost'] = variant.get('variant_po_average_cost', None)
            variant['variant_latest_po_cost'] = variant.get('variant_latest_po_cost', None)
            variant['date_modified'] = convert_to_timestamp(variant.get('date_modified'))

            if variant['cost'] is not None:
                variant_costs.append(variant['cost'])

        # Calculate cost range and margins
        product_cost_price = 0
        if len(set(variant_costs)) > 1:
            product['cost_range'] = f"{min(variant_costs)}-{max(variant_costs)}"
            product_cost_price = min(variant_costs)
        elif variant_costs:
            product['cost_range'] = str(variant_costs[0])
            product_cost_price = min(variant_costs)
        else:
            product['cost_range'] = None

        if product_cost_price > 0 and product['default_price'] > 0:
            product['default_price_margin'] = round(((product['default_price'] - product_cost_price) / product_cost_price) * 100, 2)
        else:
            product['default_price_margin'] = 0

        # Get default price sales percentage
        default_price_sales = product_data_map.get(
            1,
            {"margin_percentage": 0.0, "sales_percentage": 0.0}
        )
        product['default_price_sales'] = default_price_sales["sales_percentage"]

        # Set the flag based on variants length and variant_name condition
        show_parent_product = not(len(product['variants']) == 1 and product['variants'][0]['variant_name'] == "Parent Product")
        product["price_list"] = []

        if not product_cost_price or product_cost_price == '':
            product_cost_price = 0

        product_cost_price = float(product_cost_price)

        # Build product-level price list
        for key, value in price_list_meta.items():
            price_list_id = value["id"]
            customer_group_id = price_list_to_customer_group.get(price_list_id)
            margin_sales = product_data_map.get(
                customer_group_id,
                {"margin_percentage": 0.0, "sales_percentage": 0.0}
            )

            price_list_price = minimum_price_data.get(f"minimum_{key}", None)
            if not price_list_price or price_list_price == '':
                price_list_price = 0
            price_list_price = float(price_list_price)
            margin_per = 0
            if price_list_price > 0 and product_cost_price > 0:
                margin_per = round(((price_list_price - product_cost_price) / product_cost_price) * 100, 2)

            product["price_list"].append({
                key: minimum_price_data.get(f"minimum_{key}", None),
                "price_list_id": price_list_id,
                # f"minimum_{key}": minimum_price_data.get(f"minimum_{key}", None),
                "margin_percentage": margin_per,
                "sales_percentage": margin_sales["sales_percentage"]
            })

        # Build final response
        processed_product = {
            "parent_product_id": product['parent_product_id'],
            "parent_product_name": product['parent_product_name'],
            "parent_product_sku": product['parent_product_sku'],
            "default_price": product['default_price'],
            "default_price_margin": product['default_price_margin'],
            "default_price_sales": product['default_price_sales'],
            "cost_range": product.get('cost_range', None),
            "cost_margin": product.get('cost_margin', None),
            "minimum_cost": str(product_cost_price),
            "show_parent_product": show_parent_product,
            "price_list": product['price_list'],
            "variants": product['variants']
        }

        # Add monthly data if role allows
        if role_id != "67fd12676af694b36923ce09":
            for i in range(1, 8):
                processed_product[f"month_{i}"] = product.get(f"month_{i}", None)

        return processed_product

    except Exception as e:
        logger.error(f"Error processing product details: {traceback.format_exc()}")
        raise e

def _get_store_product_details_data(store_id, store_conn, product_sku, user, tag_filter=None):
    """
    Get product details data for a specific store.
    """
    try:
        # Get role_id and setup price lists
        role_id = user.get('role_id')
        static_price_lists = fetch_static_price_lists(store_id)
        store_obj = _get_store_object_safely(store_id)
        price_lists = _get_price_lists_safely(store_obj, store_id)

        # Build price list mappings for details
        price_list_meta, price_list_map, active_price_lists = _build_price_list_mappings_for_details(store_id, price_lists, static_price_lists, role_id)

        # Get price list assignments for sales percentage calculation
        assignments = list(store_conn['store_db'][StoreDBCollections.PRICE_LIST_ASSIGNMENT].find({
            "price_list_id": {"$exists": True},
            "customer_group_id": {"$exists": True}
        }))

        price_list_to_customer_group = {assignment['price_list_id']: assignment['customer_group_id'] for assignment in assignments}

        # Handle tag filter for variant filtering
        variant_sku_list = None
        if tag_filter:
            variant_sku_list = _get_variant_skus_from_tags(store_conn['pg_conn'], tag_filter)

        # Get product data
        product = _get_product_by_id_with_variants(store_conn['admin_db'], product_sku, variant_sku_list)

        if not product:
            return {
                'product': None,
                'price_list_meta': price_list_meta,
                'price_list_map': price_list_map,
                'active_price_lists': active_price_lists
            }

        # Process product details for multi-store
        processed_product = _process_product_details_for_multi_store(
            product, store_conn, price_list_meta, price_list_map, active_price_lists,
            price_list_to_customer_group, role_id, store_id
        )

        return {
            'product': processed_product,
            'price_list_meta': price_list_meta,
            'price_list_map': price_list_map,
            'active_price_lists': active_price_lists
        }

    except Exception as e:
        logger.error(f"Error getting store product details data for store {store_id}: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            'product': None,
            'price_list_meta': {},
            'price_list_map': {},
            'active_price_lists': []
        }

def _process_product_details_for_multi_store(product, store_conn, price_list_meta, price_list_map, active_price_lists, price_list_to_customer_group, role_id, store_id):
    """
    Process product details for multi-store (simplified version).
    """
    try:
        # Collect all variant SKUs
        variant_skus = [variant['variant_sku'] for variant in product['variants'] if variant.get('variant_sku')]

        # Get classification data for variants
        classification_map = {}
        if variant_skus:
            classification_query = text("""
                SELECT DISTINCT
                    sc.sku,
                    sc.classification
                FROM skuvault_catalog sc
                WHERE sc.sku IN :variant_skus
            """)

            classification_results = store_conn['pg_conn'].execute(classification_query, {'variant_skus': tuple(variant_skus)}).fetchall()

            for sku, classification in classification_results:
                if sku in classification_map:
                    classification_map[sku]['classifications'].add(classification)
                else:
                    classification_map[sku] = {
                        'classifications': {classification},
                    }

            # Convert classifications set to a comma-separated string
            for sku in classification_map:
                classification_map[sku]['classifications'] = ", ".join(classification_map[sku]['classifications'])

        parent_product_id = product['parent_product_id']

        # Fetch margin and sales percentage data
        product_data_map = {}
        if parent_product_id:
            product_data = _fetch_margin_and_sales_percentage(store_id, [parent_product_id])
            product_data_map = {
                item['customer_group_id']: {
                    "margin_percentage": item['margin_percentage'],
                    "sales_percentage": item['sales_percentage']
                }
                for item in product_data
            }

        # Process variants and calculate price ranges
        variant_costs = []
        price_list_data = {key: "" for key in price_list_meta}
        minimum_price_data = {}

        for variant in product['variants']:
            # Process price list efficiently for product-level calculations
            for price in variant['price_list']:
                price_list_key = price_list_map.get(price['price_list_id'])
                if price_list_key:
                    price_list_key = price_list_key['key']
                    price_value = float(price['price'])
                    if price_list_data[price_list_key]:  # Already has a value
                        min_price, max_price = map(float, price_list_data[price_list_key].split('-')) if '-' in price_list_data[price_list_key] else (float(price_list_data[price_list_key]), float(price_list_data[price_list_key]))
                        price_list_data[price_list_key] = f"{min(min_price, price_value)}-{max(max_price, price_value)}" if min_price != max_price else str(min_price)
                    else:
                        price_list_data[price_list_key] = str(price_value)

                    # Track minimum price for each price list
                    minimum_price_key = f"minimum_{price_list_key}"
                    if minimum_price_key in minimum_price_data:
                        current_min = float(minimum_price_data[minimum_price_key])  # convert string to float
                        minimum_price_data[minimum_price_key] = str(min(current_min, price_value))
                    else:
                        minimum_price_data[minimum_price_key] = str(price_value)

            # Process variant data
            variant['classification'] = classification_map.get(variant['variant_sku'], {}).get('classifications', "")
            variant['variant_price'] = product['default_price'] if variant.get('variant_price') is None else variant.get('variant_price')
            variant_price_lists = []
            default_price_lists = {price_list['id']: "" for price_list in active_price_lists}

            for price in variant['price_list']:
                price_list_id = price['price_list_id']
                if price_list_id in price_list_map and price_list_map[price_list_id]['active']:
                    default_price_lists[price_list_id] = str(price.pop('price'))

            # Build variant price list (simplified - without margin and sales percentage)
            for price_list in active_price_lists:
                price_list_id = price_list['id']
                price_value = default_price_lists[price_list_id]
                
                variant_price_item = {
                    "price_list_id": price_list_id,
                    price_list['key']: price_value
                }

                variant_price_lists.append(variant_price_item)

            variant['price_list'] = variant_price_lists
            variant["parent_product_id"] = product['parent_product_id']
            variant["parent_product_name"] = product['parent_product_name']
            variant["inventory_level"] = variant.get("inventory_level")

            # Add monthly data if role allows
            if role_id != "67fd12676af694b36923ce09":
                for i in range(1, 8):
                    variant[f'month_{i}'] = variant.get(f'month_{i}', None)

            variant['turn_rate'] = variant.get('turn_rate', 0)
            variant['weeks_on_hand'] = variant.get('weeks_on_hand', 0)
            variant['cost'] = variant.get('variant_cost', None)
            variant['variant_cost'] = variant.get('variant_cost', None)
            variant['variant_po_average_cost'] = variant.get('variant_po_average_cost', None)
            variant['variant_latest_po_cost'] = variant.get('variant_latest_po_cost', None)
            variant['date_modified'] = convert_to_timestamp(variant.get('date_modified'))

            if variant['cost'] is not None:
                variant_costs.append(variant['cost'])

        # Calculate cost range and margins
        product_cost_price = 0
        if len(set(variant_costs)) > 1:
            product['cost_range'] = f"{min(variant_costs)}-{max(variant_costs)}"
            product_cost_price = min(variant_costs)
        elif variant_costs:
            product['cost_range'] = str(variant_costs[0])
            product_cost_price = min(variant_costs)
        else:
            product['cost_range'] = None

        if product_cost_price > 0 and product['default_price'] > 0:
            product['default_price_margin'] = round(((product['default_price'] - product_cost_price) / product_cost_price) * 100, 2)
        else:
            product['default_price_margin'] = 0

        # Get default price sales percentage
        default_price_sales = product_data_map.get(
            1,
            {"margin_percentage": 0.0, "sales_percentage": 0.0}
        )
        product['default_price_sales'] = default_price_sales["sales_percentage"]

        # Set the flag based on variants length and variant_name condition
        show_parent_product = not(len(product['variants']) == 1 and product['variants'][0]['variant_name'] == "Parent Product")

        # Build product-level price list
        product_price_list = []
        if not product_cost_price or product_cost_price == '':
            product_cost_price = 0

        product_cost_price = float(product_cost_price)

        for key, value in price_list_meta.items():
            price_list_id = value["id"]
            customer_group_id = price_list_to_customer_group.get(price_list_id)
            margin_sales = product_data_map.get(
                customer_group_id,
                {"margin_percentage": 0.0, "sales_percentage": 0.0}
            )

            price_list_price = minimum_price_data.get(f"minimum_{key}", None)
            if not price_list_price or price_list_price == '':
                price_list_price = 0
            price_list_price = float(price_list_price)
            margin_per = 0
            if price_list_price > 0 and product_cost_price > 0:
                margin_per = round(((price_list_price - product_cost_price) / product_cost_price) * 100, 2)

            product_price_list.append({
                key: minimum_price_data.get(f"minimum_{key}", None),
                "price_list_id": price_list_id,
                # f"minimum_{key}": minimum_price_data.get(f"minimum_{key}", None),
                "margin_percentage": margin_per,
                "sales_percentage": margin_sales["sales_percentage"]
            })

        # Build simplified product for multi-store
        processed_product = {
            "parent_product_id": product['parent_product_id'],
            "parent_product_name": product['parent_product_name'],
            "parent_product_sku": product['parent_product_sku'],
            "default_price": product['default_price'],
            "default_price_margin": product['default_price_margin'],
            "default_price_sales": product['default_price_sales'],
            "cost_range": product.get('cost_range', None),
            "cost_margin": product.get('cost_margin', None),
            "minimum_cost": str(product_cost_price),
            "show_parent_product": show_parent_product,
            "price_list": product_price_list,
            "variants": product['variants']
        }

        # Add monthly data if role allows
        if role_id != "67fd12676af694b36923ce09":
            for i in range(1, 8):
                processed_product[f"month_{i}"] = product.get(f"month_{i}", None)

        return processed_product

    except Exception as e:
        logger.error(f"Error processing product details for multi-store: {traceback.format_exc()}")
        raise e

def update_multistore_product_price_list(store_ids, parent_sku, req_body, username):
    try:
        store_name = req_body['store_name']
        store_ids = _get_store_ids_from_store_name(store_ids, store_name)
        
        variant_sku_to_id_mapping = {}
        product_ids = {}

        if parent_sku:
            variant_sku_to_id_mapping, product_ids = _fetch_variant_mapping_from_mongo(parent_sku, store_ids)

        store_arrays = convert_variant_price_data_to_store_arrays(req_body['price_list_data'], variant_sku_to_id_mapping, store_ids)

        # Function to be executed in parallel for each store
        def update_store_price_list(store_id):
            try:
                store_obj = store_arrays.get(store_id, {}).get("store")
                data = store_arrays.get(store_id, {}).get("data")
                product_id = product_ids.get(store_id, None)
                default_price = store_arrays.get(store_id, {}).get("default_price")
                payload = {
                    "default_price": default_price,
                    'data': data
                }
                if product_id:
                    update_product_price_list(store_obj, product_id, payload, username)
                    return {"store_id": store_id, "status": "success"}
                else:
                    return {"store_id": store_id, "status": "error", "error": "Product ID not found"}
            except Exception as e:
                logger.error(f"Error updating price list for store {store_id}: {str(e)}")
                return {"store_id": store_id, "status": "error", "error": str(e)}

        # Create a thread pool and execute updates in parallel
        results = []
        with ThreadPoolExecutor(max_workers=min(10, len(store_ids))) as executor:
            # Submit all tasks and collect futures
            future_to_store = {
                executor.submit(update_store_price_list, store_id): store_id 
                for store_id in store_ids
            }
            
            # Process completed futures as they complete
            for future in as_completed(future_to_store):
                store_id = future_to_store[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    logger.error(f"Thread execution failed for store {store_id}: {str(e)}")
                    results.append({
                        "store_id": store_id,
                        "status": "error",
                        "error": str(e)
                    })

        # Check if any store updates failed
        successful_updates = [r for r in results if r["status"] == "success"]
        failed_updates = [r for r in results if r["status"] == "error"]

        # If all store updates failed, raise an exception
        if len(failed_updates) == len(results) and len(results) > 0:
            error_details = "; ".join([f"Store {f['store_id']}: {f['error']}" for f in failed_updates])
            raise Exception(f"Failed to update price lists for all stores. Errors: {error_details}")

        # If some or all succeeded, return a success message with details
        success_count = len(successful_updates)
        failure_count = len(failed_updates)
        
        message = f"Price list update process completed. {success_count} store(s) updated successfully."
        
        if failed_updates:
            failed_store_ids = ", ".join([str(f["store_id"]) for f in failed_updates])
            message += f" {failure_count} store(s) failed: {failed_store_ids}."
        
        return {"status": 200, "message": message}

    except Exception as e:
        logger.error(f"Error updating multi-store product price list: {traceback.format_exc()}")
        raise e
    
# Fetch variant mapping from MongoDB
def _fetch_variant_mapping_from_mongo(parent_sku, store_ids):
    variant_mappings = {}
    product_ids = {}
    try:
        store_connections = _setup_store_connections(store_ids)
        
        for store_id in store_ids:
            try:   
                variant_mapping = {}             
                store_conn = store_connections[store_id]
                # Get price list assignments for sales percentage calculation
                product = store_conn['store_db'][StoreDBCollections.PRODUCTS].find_one(
                    {"sku": str(parent_sku)}, {"id": 1, "variants.id": 1,"variants.sku": 1})
            
                if product and "variants" in product:
                    # Extract variant mapping
                    for variant in product.get("variants", []):
                        variant_sku = variant.get("sku")
                        variant_id = variant.get("id")

                        if variant_sku and variant_id:
                            variant_mapping[variant_sku] = variant_id

                variant_mappings[store_id] = variant_mapping
                product_ids[store_id] = product.get("id")
            except Exception as e:
                logger.error(f"Error fetching variants from store {store_id}: {str(e)}")
                continue

    except Exception as e:
        logger.error(f"Error in _fetch_variant_mapping_from_mongo: {str(e)}")
    finally:
        _close_store_connections(store_connections)

    return variant_mappings, product_ids

# Convert variant price data to store arrays
def convert_variant_price_data_to_store_arrays(input_data, variant_sku_to_id_mapping=None, store_ids=None):
    if not input_data:
        return {}
    store_arrays = {}

    # Process each variant record
    for variant_record in input_data:
        variant_sku = variant_record.get("variant_sku")

        # if not variant_sku:
        #     continue
        store_data = {}
        # Process each field in the variant record
        for field_key, field_value in variant_record.items():
            if field_key == "variant_sku":
                continue

            # Parse price list and store data
            if "_" in field_key:
                parts = field_key.split("_")

                if field_key.startswith("defaultprice_"):
                    store_id = parts[1]
                    if store_id in store_ids:
                        if store_id not in store_data:
                            store_data[store_id] = {
                                "default_price": field_value,
                                "price_lists": {}
                            }
                        else:
                            store_data[store_id]["default_price"] = field_value

                elif len(parts) == 2 and parts[0].isdigit():
                    price_list_id = int(parts[0])
                    store_id = parts[1]
                    if store_id in store_ids:
                        if store_id not in store_data:
                            store_data[store_id] = {
                                "default_price": None,
                                "price_lists": {}
                            }

                        store_data[store_id]["price_lists"][price_list_id] = field_value

        # Convert store data to the required format
        for store_id, data in store_data.items():
            if store_id in store_ids:
                variant_mapping = variant_sku_to_id_mapping.get(store_id)
                if store_id not in store_arrays:
                    store_obj = store_util.get_store_by_id(store_id)
                    store_arrays[store_id] = {}
                    store_arrays[store_id]['data'] = []
                    store_arrays[store_id]['default_price'] = []
                    store_arrays[store_id]['store'] = store_obj

                # Add price list entries
                for price_list_id, price in data["price_lists"].items():
                    if variant_mapping.get(variant_sku):
                        price_entry = {
                            "variant_id": variant_mapping.get(variant_sku),
                            "variant_sku": variant_sku,
                            "price_list_id": price_list_id,
                            "price": str(price) if price is not None else None
                        }
                        store_arrays[store_id]['data'].append(price_entry)

                # You can uncomment this if you want to include default prices
                if data["default_price"] is not None:
                    default_price_entry = {
                        "variant_id": variant_mapping.get(variant_sku) if variant_mapping else None,
                        "variant_sku": variant_sku,
                        "price": str(data["default_price"]) if data["default_price"] is not None else None
                    }
                    store_arrays[store_id]['default_price'].append(default_price_entry)

    return store_arrays


def get_product_price_list_classifications_and_suppliers_multi(store_ids, user, store_name='both'):
    try:
        # Get store IDs based on store_name parameter
        store_ids = _get_store_ids_from_store_name(store_ids, store_name)
        
        # Setup connections for all stores
        store_connections = _setup_store_connections(store_ids)
        
        all_classifications = set()
        all_suppliers = set()
        
        for store_id in store_ids:
            try:
                store_conn = store_connections[store_id]
                pg_conn = store_conn['pg_conn']
                
                if user:
                    query = text("""
                        WITH user_suppliers AS (
                            SELECT DISTINCT suppliers
                            FROM user_supplier_mapping
                            WHERE user_name = :user_name
                        )
                        SELECT
                            ARRAY_AGG(DISTINCT classification) AS classifications,
                            ARRAY_AGG(DISTINCT primary_supplier) AS suppliers
                        FROM skuvault_catalog
                        WHERE classification IS NOT NULL AND classification != ''
                        AND primary_supplier IS NOT NULL AND primary_supplier != ''
                        AND primary_supplier IN (SELECT suppliers FROM user_suppliers)
                    """)
                    result = pg_conn.execute(query, {"user_name": user}).fetchone()
                else:
                    query = text("""
                        SELECT
                            ARRAY_AGG(DISTINCT classification) AS classifications,
                            ARRAY_AGG(DISTINCT primary_supplier) AS suppliers
                        FROM skuvault_catalog
                        WHERE classification IS NOT NULL AND classification != ''
                        AND primary_supplier IS NOT NULL AND primary_supplier != ''
                    """)
                    result = pg_conn.execute(query).fetchone()
                
                # Add results to the aggregated sets
                if result and result[0]:
                    all_classifications.update(result[0])
                if result and result[1]:
                    all_suppliers.update(result[1])
                    
            except Exception as e:
                logger.error(f"Error getting classifications and suppliers for store {store_id}: {e}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                # Continue with other stores even if one fails
                continue
        
        # Convert sets to sorted lists for consistent output
        classifications_list = sorted(list(all_classifications))
        suppliers_list = sorted(list(all_suppliers))
        
        response = {
            "status": 200,
            "data": {
                "classifications": classifications_list,
                "suppliers": suppliers_list
            }
        }
        
        return response
        
    except Exception as e:
        logger.error(f"Error in get_product_price_list_classifications_and_suppliers_multi: {traceback.format_exc()}")
        return {
            "status": 422,
            "message": str(e),
            "data": {
                "classifications": [],
                "suppliers": []
            }
        }
    finally:
        # Close all store connections
        _close_store_connections(store_connections)


def get_mapped_users_multi(store_ids, store_name='both'):
    try:
        # Get store IDs based on store_name parameter
        store_ids = _get_store_ids_from_store_name(store_ids, store_name)
        
        # Setup connections for all stores
        store_connections = _setup_store_connections(store_ids)
        
        all_users = set()  # Use set to avoid duplicates across stores
        
        for store_id in store_ids:
            try:
                store_conn = store_connections[store_id]
                pg_conn = store_conn['pg_conn']
                
                query = text("SELECT DISTINCT user_name, email_id FROM user_supplier_mapping")
                result_set = pg_conn.execute(query)
                
                # Add results to the aggregated set using tuples for uniqueness
                for row in result_set:
                    user_data = (row[0], row[1])  # (user_name, email_id) tuple
                    all_users.add(user_data)
                    
            except Exception as e:
                logger.error(f"Error getting mapped users for store {store_id}: {e}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                # Continue with other stores even if one fails
                continue
        
        # Convert set of tuples to list of dictionaries
        results = []
        for user_name, email_id in all_users:
            results.append({
                'name': user_name,
                'email_id': email_id
            })
        
        # Sort results by name for consistent output
        results.sort(key=lambda x: x['name'])
        
        response = {
            'status': 200,
            'message': 'data retrieved successfully',
            'data': {
                'data': results
            }
        }
        
        return response
        
    except Exception as e:
        logger.error(f"Error in get_mapped_users_multi: {traceback.format_exc()}")
        return {
            'status': 422,
            'message': str(e),
            'data': {
                'data': []
            }
        }
    finally:
        # Close all store connections
        _close_store_connections(store_connections)

def get_classified_as_data_multi(store_ids, store_name='both'):
    """
    Get classified_as data from multiple stores.
    Aggregates data from all stores and returns unified results.
    """
    try:
        # Get store IDs based on store_name parameter
        store_ids = _get_store_ids_from_store_name(store_ids, store_name)
        
        # Setup connections for all stores
        store_connections = _setup_store_connections(store_ids)
        
        all_classified_as = set()  # Use set to avoid duplicates across stores
        
        for store_id in store_ids:
            try:
                store_conn = store_connections[store_id]
                pg_conn = store_conn['pg_conn']
                
                query = text("""
                    SELECT 
                        rca.id, 
                        rca.name, 
                        rca.created_by, 
                        rca.updated_by, 
                        rca.created_at, 
                        rca.updated_at
                    FROM 
                        replenishment_classified_as rca
                """)
                
                result = pg_conn.execute(query)
                
                # Add results to the aggregated set using tuples for uniqueness
                for row in result:
                    classified_as_data = (
                        row[0],  # id
                        row[1],  # name
                        row[2],  # created_by
                        row[3],  # updated_by
                        row[4],  # created_at
                        row[5]   # updated_at
                    )
                    all_classified_as.add(classified_as_data)
                    
            except Exception as e:
                logger.error(f"Error getting classified_as data for store {store_id}: {e}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                # Continue with other stores even if one fails
                continue
        
        # Convert set of tuples to list of dictionaries
        data = []
        for id_val, name, created_by, updated_by, created_at, updated_at in all_classified_as:
            data.append({
                "id": id_val,
                "name": name,
                "created_by": created_by,
                "updated_by": updated_by,
                "created_at": convert_to_timestamp(created_at),
                "updated_at": convert_to_timestamp(updated_at)
            })
        
        # Sort results by name for consistent output
        data.sort(key=lambda x: x['name'])
        
        response = {
            "status": 200,
            "data": data
        }
        
        return response
        
    except Exception as e:
        logger.error(f"Error in get_classified_as_data_multi: {traceback.format_exc()}")
        return {
            "status": 422,
            "message": str(e),
            "data": []
        }
    finally:
        # Close all store connections
        _close_store_connections(store_connections)

def get_filtered_products_dropdown_multi(store_ids, page, limit, search, classification_filter, user_filter=None, supplier_filter=None, store_name='both', hide_products=False):
    try:
        # Get store IDs based on store_name parameter
        store_ids = _get_store_ids_from_store_name(store_ids, store_name)
        store_connections = _setup_store_connections(store_ids)

        # Validate pagination parameters (compulsory)
        if not page or not limit:
            return {
                "status": 400,
                "message": "Page and limit are required parameters"
            }

        page = int(page)
        limit = int(limit)
        # Collect filtered SKUs from all stores
        all_filtered_skus = set()
        store_filter_data = {}

        # Apply filters for each store to get filtered SKUs
        for store_id in store_ids:
            try:
                store_conn = store_connections[store_id]
                pg_conn = store_conn['pg_conn']

                # Initialize filter data for this store
                store_filter_data[store_id] = {
                    'classification_skus': set(),
                    'user_skus': set(),
                    'supplier_skus': set()
                }

                # Apply classification filter
                if classification_filter:
                    query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE classification IN :classification_filter_list AND parent_sku IS NOT NULL AND parent_sku != ''"
                    classification_skus = pg_conn.execute(text(query), {'classification_filter_list': tuple(classification_filter.split(','))}).fetchall()
                    classification_skus = {row[0] for row in classification_skus}
                    store_filter_data[store_id]['classification_skus'] = classification_skus

                # Apply user filter
                if user_filter:
                    query = """
                        SELECT ARRAY_AGG(DISTINCT suppliers)
                        FROM user_supplier_mapping
                        WHERE user_name = :user_name
                    """
                    res = pg_conn.execute(text(query), {'user_name': user_filter})
                    suppliers_list = res.fetchone()[0] or []

                    query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier = ANY(:suppliers_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
                    result = pg_conn.execute(text(query), {'suppliers_list': suppliers_list}).fetchall()
                    user_skus = {row[0] for row in result}
                    store_filter_data[store_id]['user_skus'] = user_skus

                # Apply supplier filter
                if supplier_filter:
                    supplier_filter_list = [supplier.strip() for supplier in supplier_filter.split(';')]
                    query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier ILIKE ANY(:supplier_filter_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
                    result = pg_conn.execute(text(query), {'supplier_filter_list': supplier_filter_list}).fetchall()
                    supplier_skus = {row[0] for row in result}
                    store_filter_data[store_id]['supplier_skus'] = supplier_skus

            except Exception as e:
                logger.error(f"Error applying filters for store {store_id}: {e}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                # Continue with other stores even if one fails
                continue

        # Combine filtered SKUs from all stores (union of all filtered SKUs)
        for store_id in store_filter_data:
            store_data = store_filter_data[store_id]

            # Get intersection of all applied filters for this store
            store_filtered_skus = None

            if classification_filter and store_data['classification_skus']:
                store_filtered_skus = store_data['classification_skus'] if store_filtered_skus is None else store_filtered_skus.intersection(store_data['classification_skus'])

            if user_filter and store_data['user_skus']:
                store_filtered_skus = store_data['user_skus'] if store_filtered_skus is None else store_filtered_skus.intersection(store_data['user_skus'])

            if supplier_filter and store_data['supplier_skus']:
                store_filtered_skus = store_data['supplier_skus'] if store_filtered_skus is None else store_filtered_skus.intersection(store_data['supplier_skus'])

            # Add this store's filtered SKUs to the global set
            if store_filtered_skus:
                all_filtered_skus.update(store_filtered_skus)

        # Collect all products from all stores with filters applied
        unique_products_by_sku = {}

        for store_id in store_ids:
            try:
                store_conn = store_connections[store_id]

                # Build additional query conditions
                additional_conditions = [{"syncing": False}]

                # Add SKU filter if any filters were applied
                if all_filtered_skus:
                    sku_filter = {"parent_product_sku": {"$in": list(all_filtered_skus)}}
                    additional_conditions.append(sku_filter)

                # Add hide_products filter
                if hide_products:
                    additional_conditions.append({"parent_product_sku": {"$exists": True, "$ne": ""}})

                # Add search filter if provided
                if search:
                    search_query = {
                        "$or": [
                            {"parent_product_name": {"$regex": search, "$options": "i"}},  # Case-insensitive search
                            {"parent_product_sku": {"$regex": search, "$options": "i"}},  # Case-insensitive search
                        ]
                    }
                    additional_conditions.append(search_query)

                # Build final additional query
                additional_query = {"$and": additional_conditions} if additional_conditions else {}

                # Get products without pagination first (to get all filtered products)
                products_cursor = fetchall_documents_from_admin_collection(
                    store_id,
                    StoreAdminDBCollections.PRODUCT_PRICE_LISTS,
                    additional_query,
                    price_list_products_dropdown_fields
                )
                products = list(products_cursor)

                # Process products for this store
                for product in products:
                    parent_sku = product.get('parent_product_sku')

                    if parent_sku:
                        # Use parent_product_sku as unique key
                        if parent_sku not in unique_products_by_sku:
                            # Convert ObjectId to string
                            if '_id' in product:
                                product['id'] = str(product['_id'])
                                del product['_id']
                            else:
                                product['id'] = str(product.get('parent_product_id', ''))

                            unique_products_by_sku[parent_sku] = product

            except Exception as e:
                logger.error(f"Error getting filtered products for store {store_id}: {e}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                # Continue with other stores even if one fails
                continue

        # Convert unique products to list and sort by created_at
        final_products = list(unique_products_by_sku.values())
        final_products.sort(key=lambda x: x.get('created_at', ''), reverse=True)

        # Apply pagination to the aggregated results
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paginated_products = final_products[start_idx:end_idx]

        # Calculate pagination metadata
        total_count = len(final_products)
        next_page = (page + 1) if (page * limit) < total_count else None

        response = {
            "status": 200,
            "data": {
                "data": paginated_products,
                "meta": {
                    "current_page": page,
                    "next_page": next_page,
                    "total_count": total_count
                }
            }
        }

        return response
        
    except Exception as e:
        logger.error(f"Error in get_filtered_products_dropdown_multi: {traceback.format_exc()}")
        return {
            "status": 422,
            "message": str(e),
            "data": {
                "data": [],
                "meta": {
                    "current_page": page if 'page' in locals() else 1,
                    "next_page": None,
                    "total_count": 0
                }
            }
        }
    finally:
        # Close all store connections
        _close_store_connections(store_connections)


def get_product_price_list_tags_multi(store_ids, store_name='both'):
    """
    Get product price list tags from multiple stores.
    Aggregates tag data from all stores and returns unified results.
    """
    try:
        # Get store IDs based on store_name parameter
        store_ids = _get_store_ids_from_store_name(store_ids, store_name)
        
        # Setup connections for all stores
        store_connections = _setup_store_connections(store_ids)
        
        all_tags = set()  # Use set to avoid duplicates across stores
        
        for store_id in store_ids:
            try:
                store_conn = store_connections[store_id]
                pg_conn = store_conn['pg_conn']
                
                query = text("""
                    SELECT id, tag
                    FROM tags
                """)
                
                result = pg_conn.execute(query)
                data = result.fetchall()
                
                # Add results to the aggregated set using tuples for uniqueness
                for row in data:
                    tag_data = (row[0], row[1])  # (id, tag) tuple
                    all_tags.add(tag_data)
                    
            except Exception as e:
                logger.error(f"Error getting tags for store {store_id}: {e}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                # Continue with other stores even if one fails
                continue
        
        # Convert set of tuples to list of dictionaries
        data_as_dict = []
        for tag_id, tag_name in all_tags:
            data_as_dict.append({
                "id": tag_id,
                "tag": tag_name
            })
        
        # Sort results by tag name for consistent output
        data_as_dict.sort(key=lambda x: x['tag'])
        
        response = {
            "status": 200,
            "data": data_as_dict
        }
        
        return response
        
    except Exception as e:
        logger.error(f"Error in get_product_price_list_tags_multi: {traceback.format_exc()}")
        return {
            "status": 422,
            "message": str(e),
            "data": []
        }
    finally:
        # Close all store connections
        _close_store_connections(store_connections)