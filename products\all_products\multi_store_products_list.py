from datetime import datetime, timezone
from bson import ObjectId
from fields.products import product_price_list_fields
from new_mongodb import StoreAdminDBCollections, StoreDBCollections, get_admin_db_client_for_store_id, get_store_by_id, get_store_db_client_for_store_id
from new_mongodb import fetchall_documents_from_admin_collection, process_documents
import new_utils
from pymongo.collation import Collation
from plugin import bc_price_list
import new_pgdb
from sqlalchemy import text
import logging
import traceback
from mongo_db import user_db
from utils import store_util
from utils.common import get_paginated_records_price_list, calculatePaginationData, convert_to_timestamp, fetch_static_price_lists, get_month_array_for_meta
from new_pgdb.analytics_db import AnalyticsDB
from collections import defaultdict
from decimal import Decimal, ROUND_HALF_UP
from utils import store_util

logger = logging.getLogger()

def get_multi_store_product_price_lists(store_ids, page, limit, sort_by, filter, user, tag_filter=None, classification_filter=None, classified_as_filter=None, products_filter=None, user_filter=None, supplier_filter=None, top_products_filter=None, cost_margin_filter=None, store_name='both', primary_sort_store=None):
    """
    Get product price lists from multiple stores with merged data.
    
    Args:
        store_ids: List of store IDs to fetch data from
        page, limit, sort_by, filter: Pagination and filtering parameters
        user: User object with role_id and other details
        Various filters: tag_filter, classification_filter, etc.
    
    Returns:
        Merged product data with store-specific values
    """
    if store_name == 'both':
        if "63da3e98b702e324567f76f9" in store_ids:
            store_ids.append("661239751b9ce4bd7f85237c")
        elif "661239751b9ce4bd7f85237c" in store_ids:
            store_ids.append("63da3e98b702e324567f76f9")
    elif store_name == 'midwest':
        if "63da3e98b702e324567f76f9" not in store_ids:
            store_ids.append("63da3e98b702e324567f76f9")
        if "661239751b9ce4bd7f85237c" in store_ids:
            store_ids.remove("661239751b9ce4bd7f85237c")
    elif store_name == 'cbd':
        if "661239751b9ce4bd7f85237c" not in store_ids:
            store_ids.append("661239751b9ce4bd7f85237c")
        if "63da3e98b702e324567f76f9" in store_ids:
            store_ids.remove("63da3e98b702e324567f76f9")

    primary_sort_store_id = '63da3e98b702e324567f76f9' if not primary_sort_store or primary_sort_store == 'midwest' else '661239751b9ce4bd7f85237c'
    if not store_ids or len(store_ids) == 0:
        return {"data": [], "meta": {"price_lists": {}, "months": {}}}
    
    # If only one store, return multi-store format (same structure)
    if len(store_ids) == 1:
        return _get_single_store_data_original_format(store_ids[0], page, limit, sort_by, filter, user, tag_filter, classification_filter, classified_as_filter, products_filter, user_filter, supplier_filter, top_products_filter, cost_margin_filter)

    # Multiple stores logic
    return _get_multi_store_data(store_ids, page, limit, sort_by, filter, user, tag_filter, classification_filter, classified_as_filter, products_filter, user_filter, supplier_filter, top_products_filter, cost_margin_filter, primary_sort_store_id)

def _get_single_store_data_original_format(store_id, page, limit, sort_by, filter, user, tag_filter=None, classification_filter=None, classified_as_filter=None, products_filter=None, user_filter=None, supplier_filter=None, top_products_filter=None, cost_margin_filter=None):
    """
    Get data from single store with same response structure as multi-store.
    """
    try:
        # Setup connections for single store
        store_connections = _setup_store_connections([store_id])
        store_conn = store_connections[store_id]

        # Extract field and sort order from sort_by
        field, sort_order = ('created_at', 'desc')
        if '/' in sort_by:
            field, order = sort_by.split('/')
            sort_order = 'asc' if order == '1' else 'desc'

        payload = {
            "page": page,
            "limit": limit,
            "sort_by": field,
            "sort_order": sort_order,
            "filterBy": ["parent_product_name", "parent_product_sku", "variant_name", "variant_sku"],
            "filter": filter
        }

        # Get role_id and setup price lists (keep original flow)
        role_id = user.get('role_id')
        static_price_lists = fetch_static_price_lists(store_id)
        store_obj = _get_store_object_safely(store_id)
        price_lists = _get_price_lists_safely(store_obj, store_id)

        # Build price list mappings (keep original flow)
        price_list_meta, price_list_map = _build_price_list_mappings(store_id, price_lists, static_price_lists, role_id)

        # Apply filters (all filters enabled for single store)
        filter_query, unique_sku_count, sku_list = _apply_filters_single_store(store_conn['pg_conn'], tag_filter, classification_filter, classified_as_filter, products_filter, user_filter, supplier_filter, top_products_filter, cost_margin_filter)

        # Suppress unused variable warnings
        _ = unique_sku_count, sku_list

        # Get products data (keep original data retrieval flow)
        additional_query = {"$and": [{"syncing": False}] + filter_query} if filter_query else {"syncing": False}

        products, total_data_length, page, limit = get_paginated_records_price_list(
            store_conn['admin_db'], StoreAdminDBCollections.PRODUCT_PRICE_LISTS, payload, product_price_list_fields, additional_query
        )

        # Get price list assignments for sales percentage calculation (keep original flow)
        assignments = list(store_conn['store_db'][StoreDBCollections.PRICE_LIST_ASSIGNMENT].find({
            "price_list_id": {"$exists": True},
            "customer_group_id": {"$exists": True}
        }))

        price_list_to_customer_group = {assignment['price_list_id']: assignment['customer_group_id'] for assignment in assignments}

        # Process products for single store (keep original processing)
        processed_products = _process_single_store_products_original_format(store_id, products, store_conn, price_list_meta, price_list_map, role_id, price_list_to_customer_group)

        # Convert single store products to multi-store format
        converted_products = _convert_single_store_to_multi_store_format(processed_products, store_id)

        # Create pagination data (keep original pagination)
        data = calculatePaginationData(converted_products, page, limit, total_data_length)

        # Create unified price list mapping (same as multi-store)
        price_list_mappings = {store_id: price_list_meta}
        unified_price_list_mapping = _map_price_lists_between_stores(price_list_mappings)

        # Add metadata (same structure as multi-store)
        month_names, _ = get_month_array_for_meta(6)
        data['meta']['price_lists'] = unified_price_list_mapping
        data['meta']['months'] = month_names if role_id != "67fd12676af694b36923ce09" else {}
        data['meta']['stores'] = {
            store_id: {
                'id': store_id,
                'name': store_connections[store_id]['store_name'],
                'suffix': _get_store_suffix(store_id)
            }
        }

        return data

    except Exception as e:
        logger.error(f"Error in _get_single_store_data_original_format: {traceback.format_exc()}")
        raise e
    finally:
        _close_store_connections(store_connections)

def _convert_single_store_to_multi_store_format(products, store_id):
    """
    Convert single store product format to multi-store format.
    """
    converted_products = []

    for product in products:
        # Extract store-specific data
        store_data = {
            'store_id': store_id,
            'store_name': f"Store {_get_store_suffix(store_id).title()}",
            'inventory_level': product.get('inventory_level', 0),
            'turn_rate': product.get('turn_rate', 0),
            'weeks_on_hand': product.get('weeks_on_hand', 0),
            'default_price': product.get('default_price'),
            'price_list': product.get('price_list', [])
        }

        # Add monthly data if present
        for i in range(1, 8):
            month_field = f'month_{i}'
            if month_field in product:
                store_data[month_field] = product.get(month_field)

        # Create multi-store format product
        converted_product = {
            # Common fields at parent level
            'parent_product_id': product.get('parent_product_id'),
            'parent_product_name': product.get('parent_product_name'),
            'parent_product_sku': product.get('parent_product_sku'),
            'classification': product.get('classification', ''),
            'primary_supplier': product.get('primary_supplier', ''),
            'purchaser': product.get('purchaser', ''),
            'updated_at': product.get('updated_at'),
            'created_at': product.get('created_at'),
            'updated_by': product.get('updated_by'),
            'cost_range': product.get('cost_range'),
            'default_price_margin': product.get('default_price_margin', 0),

            # Store-specific data in stores array
            'stores': [store_data]
        }

        converted_products.append(converted_product)

    return converted_products

def _get_multi_store_data(store_ids, page, limit, sort_by, filter, user, tag_filter=None, classification_filter=None, classified_as_filter=None, products_filter=None, user_filter=None, supplier_filter=None, top_products_filter=None, cost_margin_filter=None, primary_sort_store=None):
    """
    Get and merge data from multiple stores with unified filtering and store-specific sorting.

    Args:
        primary_sort_store: Store ID to use for primary sorting (optional, defaults to first store)
    """
    try:
        # Setup connections for all stores
        store_connections = _setup_store_connections(store_ids)

        # Extract field and sort order from sort_by
        field, sort_order = ('created_at', 'desc')
        if '/' in sort_by:
            field, order = sort_by.split('/')
            sort_order = 'asc' if order == '1' else 'desc'

        # Get role_id
        role_id = user.get('role_id')

        # Set primary sort store (defaults to first store if not specified)
        if not primary_sort_store or primary_sort_store not in store_ids:
            primary_sort_store = store_ids[0]

        # Step 1: Collect filtered SKUs from all stores and merge them
        all_filtered_skus = set()
        all_unique_skus = set()
        total_unique_sku_count = 0
        cost_margin_query = None
        store_names = {}

        for store_id in store_ids:
            store_conn = store_connections[store_id]
            store_names[store_id] = store_conn['store_name']

            # Apply filters using the single store function with multi-store flag
            filtered_skus, store_cost_margin_query, unique_sku_count, sku_list = _apply_filters_single_store(
                store_conn['pg_conn'],
                tag_filter, classification_filter, classified_as_filter, products_filter,
                user_filter, supplier_filter, top_products_filter, cost_margin_filter,
                is_multi_store=True, page=page, limit=limit, filter=filter
            )

            # Collect filtered SKUs from this store
            all_filtered_skus.update(filtered_skus)

            # Collect cost_margin query if present
            if not cost_margin_query and store_cost_margin_query:
                cost_margin_query = store_cost_margin_query

            # Collect SKU information for metadata
            all_unique_skus.update(sku_list)
            if unique_sku_count > total_unique_sku_count:
                total_unique_sku_count = unique_sku_count


        # Step 2: Create unified filter query from merged SKUs
        unified_filter_query = []
        if all_filtered_skus:
            unified_filter_query.append({"parent_product_sku": {"$in": list(all_filtered_skus)}})

        # Append cost_margin query if available
        if cost_margin_query:
            unified_filter_query.append(cost_margin_query)

        # Step 3: Get data from primary sort store first (with sorting and pagination)
        primary_store_conn = store_connections[primary_sort_store]
        primary_store_data = _get_store_products_data_with_filters(
            primary_sort_store, primary_store_conn, unified_filter_query,
            page, limit, field, sort_order, filter, role_id, apply_sorting=True
        )

        # Extract SKUs from primary store results for secondary stores
        primary_result_skus = [product['parent_product_sku'] for product in primary_store_data['products']]

        # Step 4: Get data from secondary stores using only the SKUs from primary store results
        all_store_data = {primary_sort_store: primary_store_data}
        price_list_mappings = {primary_sort_store: primary_store_data['price_list_meta']}

        for store_id in store_ids:
            if store_id == primary_sort_store:
                continue

            store_conn = store_connections[store_id]

            # Create filter query with only the SKUs from primary store results
            secondary_filter_query = []
            if primary_result_skus:
                secondary_filter_query.append({"parent_product_sku": {"$in": primary_result_skus}})

            # Append cost_margin query if available
            if cost_margin_query:
                secondary_filter_query.append(cost_margin_query)

            # Get data without sorting/pagination (since order is determined by primary store)
            store_data = _get_store_products_data_with_filters(
                store_id, store_conn, secondary_filter_query,
                1, 10000, field, sort_order, filter, role_id, apply_sorting=False
            )

            all_store_data[store_id] = store_data
            price_list_mappings[store_id] = store_data['price_list_meta']

        # Map price lists between stores by title
        unified_price_list_mapping = _map_price_lists_between_stores(price_list_mappings)

        # Suppress unused variable warnings
        _ = all_unique_skus

        # Create separate objects for each store with common values at parent level
        multi_store_response = _create_multi_store_response(all_store_data, store_ids, unified_price_list_mapping, page, limit, role_id, total_unique_sku_count, store_names)

        return multi_store_response

    except Exception as e:
        logger.error(f"Error in _get_multi_store_data: {traceback.format_exc()}")
        raise e
    finally:
        _close_store_connections(store_connections)



def _create_multi_store_response(all_store_data, store_ids, unified_price_list_mapping, page, limit, role_id, total_unique_sku_count, store_names):
    """
    Create multi-store response with separate objects for each store.
    Common values at parent level, store-specific values in separate objects.
    """
    # Group products by parent_product_sku
    products_by_sku = defaultdict(dict)
    # total_count = 0
    for store_id in store_ids:
        store_data = all_store_data[store_id]
        # total_count += store_data['total_count']
        for product in store_data['products']:
            parent_sku = product.get('parent_product_sku')
            if not parent_sku:
                continue

            if parent_sku not in products_by_sku:
                # Initialize with common fields
                products_by_sku[parent_sku] = {
                    'parent_product_id': product.get('parent_product_id'),
                    'parent_product_name': product.get('parent_product_name'),
                    'parent_product_sku': parent_sku,
                    'classification': product.get('classification', ''),
                    'primary_supplier': product.get('primary_supplier', ''),
                    'purchaser': product.get('purchaser', ''),
                    'updated_at': product.get('updated_at'),
                    'created_at': product.get('created_at'),
                    'updated_by': product.get('updated_by'),
                    'cost_range': product.get('cost_range'),
                    'default_price_margin': product.get('default_price_margin', 0),
                    'stores': {}
                }

            # Create store-specific object
            store_suffix = _get_store_suffix(store_id)
            store_object = {
                'store_id': store_id,
                'store_name': f"Store {store_suffix.title()}",
                'inventory_level': product.get('inventory_level', 0),
                'turn_rate': product.get('turn_rate', 0),
                'weeks_on_hand': product.get('weeks_on_hand', 0),
                'default_price': product.get('default_price'),
                'price_list': []
            }

            # Add monthly data
            if role_id != "67fd12676af694b36923ce09":
                for i in range(1, 8):
                    month_field = f'month_{i}'
                    store_object[month_field] = product.get(month_field)

            # Add price list data for this store
            for price_item in product.get('price_list', []):
                store_price_item = {
                    'price_list_id': price_item.get('price_list_id'),
                    'margin_percentage': price_item.get('margin_percentage', 0),
                    'sales_percentage': price_item.get('sales_percentage', 0)
                }

                # Add price values
                for key, value in price_item.items():
                    if key.startswith('price_'):
                        store_price_item[key] = value

                store_object['price_list'].append(store_price_item)

            products_by_sku[parent_sku]['stores'][store_id] = store_object

    # Convert to final format
    all_products = []
    for parent_sku, product_data in products_by_sku.items():
        final_product = {
            # Common fields at parent level
            'parent_product_id': product_data['parent_product_id'],
            'parent_product_name': product_data['parent_product_name'],
            'parent_product_sku': parent_sku,
            'classification': product_data['classification'],
            'primary_supplier': product_data['primary_supplier'],
            'purchaser': product_data['purchaser'],
            'updated_at': product_data['updated_at'],
            'created_at': product_data['created_at'],
            'updated_by': product_data['updated_by'],
            'cost_range': product_data['cost_range'],
            'default_price_margin': product_data['default_price_margin'],

            # Store-specific objects
            'stores': list(product_data['stores'].values())
        }

        all_products.append(final_product)

    # Apply pagination
    total_count = total_unique_sku_count
    start_idx = (page - 1) * limit
    end_idx = start_idx + limit
    paginated_products = all_products[start_idx:end_idx]
    # Create response
    data = calculatePaginationData(paginated_products, page, limit, total_count)

    # Add metadata
    month_names, _ = get_month_array_for_meta(6)
    data['meta']['price_lists'] = unified_price_list_mapping
    data['meta']['months'] = month_names if role_id != "67fd12676af694b36923ce09" else {}
    data['meta']['stores'] = {
        store_id: {
            'id': store_id,
            'name': store_names[store_id],
            'suffix': _get_store_suffix(store_id)
        } for store_id in store_ids
    }

    return data

def _setup_store_connections(store_ids):
    """
    Setup database connections for multiple stores.
    
    Returns:
        Dict with store_id as key and connection objects as values
    """
    connections = {}
    
    for store_id in store_ids:
        try:
            store = get_store_by_id(store_id)
            connections[store_id] = {
                'admin_db': get_admin_db_client_for_store_id(store_id),
                'store_db': get_store_db_client_for_store_id(store_id),
                'pg_conn': new_pgdb.get_connection(store_id),
                'store_name': store.get('name')
                
            }
        except Exception as e:
            logger.error(f"Failed to setup connections for store {store_id}: {e}")
            # Close any connections that were successfully opened
            _close_store_connections(connections)
            raise e
    
    return connections

def _close_store_connections(connections):
    """
    Close all database connections.
    """
    for store_id, conn_dict in connections.items():
        try:
            if 'pg_conn' in conn_dict and conn_dict['pg_conn']:
                conn_dict['pg_conn'].close()
        except Exception as e:
            logger.error(f"Error closing connections for store {store_id}: {e}")


def _build_price_list_mappings(store_id, price_lists, static_price_lists, role_id):
    """
    Build price list mappings similar to original function.
    """
    # Suppress unused parameter warning
    _ = store_id
    # Merge static price lists
    price_lists['data'].extend([{
        'id': pl['id'], 'name': pl['name'], 'date_created': None, 'date_modified': None, 'active': pl['active']
    } for pl in static_price_lists])

    # Role-specific filtering
    if role_id == '67f5f9c43c97938e59357472':
        price_lists['data'] = [pl for pl in price_lists['data'] if pl['id'] not in (14, 15)]
    elif role_id == '67fd12676af694b36923ce09':
        price_lists['data'] = [pl for pl in price_lists['data'] if pl['id'] in (13, 15, 52)]

    # Move price_list_id 7 to the top if active
    sorted_price_lists = sorted(price_lists['data'], key=lambda x: (x['id'] != 7, not x['active']))

    # Find positions of 14 and 15 and swap them
    index_14 = next((i for i, pl in enumerate(sorted_price_lists) if pl['id'] == 14), None)
    index_15 = next((i for i, pl in enumerate(sorted_price_lists) if pl['id'] == 15), None)

    if index_14 is not None and index_15 is not None:
        sorted_price_lists[index_14], sorted_price_lists[index_15] = sorted_price_lists[index_15], sorted_price_lists[index_14]

    # Generate price list mappings
    price_list_meta = {
        f"price_{i+1}": {'id': pl['id'], 'name': pl['name'], 'active': pl['active']}
        for i, pl in enumerate(sorted_price_lists, start=0) if pl['active']
    }
    price_list_map = {pl['id']: f"price_{i+1}" for i, pl in enumerate(sorted_price_lists, start=0) if pl['active']}

    return price_list_meta, price_list_map

def _apply_filters_single_store(pg_conn, tag_filter=None, classification_filter=None, classified_as_filter=None, products_filter=None, user_filter=None, supplier_filter=None, top_products_filter=None, cost_margin_filter=None, is_multi_store=False, page=1, limit=30, filter=''):
    """
    Apply all filters for single store (all filters enabled).
    For multi-store, only certain filters are applied and pagination is handled when no filters exist.

    Returns:
        For single store: tuple: (filter_query, unique_sku_count, sku_list)
        For multi-store: tuple: (filtered_skus, cost_margin_query, unique_sku_count, sku_list)
    """
    all_skus = set()  # To collect all unique SKUs
    cost_margin_query = None

    def fetch_skus(query, param_name, values):
        result = pg_conn.execute(text(query), {param_name: tuple(values)}).fetchall()
        return [row[0] for row in result]

    # For multi-store, only allow certain filters that use skuvault_catalog
    if is_multi_store:
        if classification_filter:
            classification_skus = fetch_skus("SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE classification IN :classification_filter_list AND parent_sku IS NOT NULL AND parent_sku != ''", 'classification_filter_list', classification_filter.split(','))
            all_skus.update(classification_skus)

        if user_filter:
            query = """
                SELECT ARRAY_AGG(DISTINCT suppliers)
                FROM user_supplier_mapping
                WHERE user_name = :user_name
            """
            res = pg_conn.execute(text(query), {'user_name': user_filter})
            suppliers_list = res.fetchone()[0] or []

            if suppliers_list:
                query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier = ANY(:suppliers_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
                result = pg_conn.execute(text(query), {'suppliers_list': suppliers_list}).fetchall()
                user_skus = [row[0] for row in result]
                all_skus.update(user_skus)

        if supplier_filter:
            supplier_filter_list = [supplier.strip() for supplier in supplier_filter.split(';')]
            query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier ILIKE ANY(:supplier_filter_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
            result = pg_conn.execute(text(query), {'supplier_filter_list': supplier_filter_list}).fetchall()
            supplier_skus = [row[0] for row in result]
            all_skus.update(supplier_skus)

        # Handle cost_margin filter for multi-store
        if cost_margin_filter:
            cost_margin_filter_val = int(cost_margin_filter)
            if cost_margin_filter_val > 0:
                cost_margin_query = {"cost_margin": {"$gte": 0, "$lte": cost_margin_filter_val}}
            else:
                cost_margin_query = {"cost_margin": {"$gte": cost_margin_filter_val, "$lte": 0}}

        # If no filters applied for multi-store, get all SKUs from skuvault_catalog
        if not all_skus and filter == '':
            # Get all SKUs (no pagination here, will be handled in MongoDB)
            query = """
                SELECT DISTINCT parent_sku
                FROM skuvault_catalog
                WHERE parent_sku IS NOT NULL AND parent_sku != ''
            """
            result = pg_conn.execute(text(query)).fetchall()
            all_skus_list = [row[0] for row in result]
            all_skus.update(all_skus_list)

            # Return for multi-store with no filters
            return list(all_skus), cost_margin_query, len(all_skus), list(all_skus)

        # Return for multi-store with filters
        return list(all_skus), cost_margin_query, len(all_skus), list(all_skus)

    else:
        # Single store - all filters enabled (original logic)
        filter_query = []

        if tag_filter:
            tag_skus = fetch_skus("SELECT sku FROM product_tags WHERE tag_id IN :tag_filter_list", 'tag_filter_list', tag_filter.split(','))
            tag_query = {"parent_product_sku": {"$in": tag_skus}}
            filter_query.append(tag_query)
            all_skus.update(tag_skus)

        if classified_as_filter:
            classified_skus = fetch_skus("SELECT parent_sku FROM replenishment_dashboard WHERE classified_as_id IN :classified_as_filter_list", 'classified_as_filter_list', classified_as_filter.split(','))
            classified_as_query = {"parent_product_sku": {"$in": classified_skus}}
            filter_query.append(classified_as_query)
            all_skus.update(classified_skus)

        if classification_filter:
            classification_skus = fetch_skus("SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE classification IN :classification_filter_list AND parent_sku IS NOT NULL AND parent_sku != ''", 'classification_filter_list', classification_filter.split(','))
            classification_query = {"parent_product_sku": {"$in": classification_skus}}
            filter_query.append(classification_query)
            all_skus.update(classification_skus)

        if user_filter:
            query = """
                SELECT ARRAY_AGG(DISTINCT suppliers)
                FROM user_supplier_mapping
                WHERE user_name = :user_name
            """
            res = pg_conn.execute(text(query), {'user_name': user_filter})
            suppliers_list = res.fetchone()[0] or []

            if suppliers_list:
                query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier = ANY(:suppliers_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
                result = pg_conn.execute(text(query), {'suppliers_list': suppliers_list}).fetchall()
                user_skus = [row[0] for row in result]
                user_query = {"parent_product_sku": {"$in": user_skus}}
                filter_query.append(user_query)
                all_skus.update(user_skus)

        if supplier_filter:
            supplier_filter_list = [supplier.strip() for supplier in supplier_filter.split(';')]
            query = "SELECT DISTINCT parent_sku FROM skuvault_catalog WHERE primary_supplier ILIKE ANY(:supplier_filter_list) AND parent_sku IS NOT NULL AND parent_sku != ''"
            result = pg_conn.execute(text(query), {'supplier_filter_list': supplier_filter_list}).fetchall()
            supplier_skus = [row[0] for row in result]
            supplier_query = {"parent_product_sku": {"$in": supplier_skus}}
            filter_query.append(supplier_query)
            all_skus.update(supplier_skus)

        if products_filter:
            query = """SELECT DISTINCT sku FROM products WHERE product_id IN :products_filter_list"""
            result = pg_conn.execute(text(query), {'products_filter_list': list(map(int, products_filter.split(',')))})
            products_skus = [row[0] for row in result]
            products_query = {"parent_product_sku": {"$in": products_skus}}
            filter_query.append(products_query)
            all_skus.update(products_skus)

        if top_products_filter:
            top_n = int(top_products_filter)
            query = text(f"""
                SELECT parent_sku
                FROM {AnalyticsDB.get_products_revenue_table()}
                WHERE order_date >= NOW() - INTERVAL '30 days'
                GROUP BY product_id
                ORDER BY SUM(revenue) DESC
                LIMIT :top_n
            """)

            result = pg_conn.execute(query, {'top_n': top_n}).fetchall()
            top_product_skus = [row[0] for row in result]

            if top_product_skus:
                top_products_query = {"parent_product_sku": {"$in": top_product_skus}}
                filter_query.append(top_products_query)
                all_skus.update(top_product_skus)

        if cost_margin_filter:
            cost_margin_filter_val = int(cost_margin_filter)
            if cost_margin_filter_val > 0:
                filter_query.append({
                    "cost_margin": {"$gte": 0, "$lte": cost_margin_filter_val}
                })
            else:
                filter_query.append({
                    "cost_margin": {"$gte": cost_margin_filter_val, "$lte": 0}
                })

        # Return for single store
        return filter_query, len(all_skus), list(all_skus)

def _get_store_products_data_with_filters(store_id, store_conn, unified_filter_query, page, limit, field, sort_order, filter, role_id, apply_sorting=True):
    """
    Get products data for a specific store with pre-applied filters.

    Args:
        apply_sorting: If True, apply sorting and pagination. If False, get all data without sorting.
    """
    try:
        # Setup payload based on whether sorting should be applied
        if apply_sorting:
            payload = {
                "page": page,
                "limit": limit,
                "sort_by": field,
                "sort_order": sort_order,
                "filterBy": ["parent_product_name", "parent_product_sku", "variant_name", "variant_sku"],
                "filter": filter
            }
        else:
            # For secondary stores, get all data without pagination/sorting
            payload = {
                "page": 1,
                "limit": 10000,  # Large limit to get all matching products
                "sort_by": field,
                "sort_order": sort_order,
                "filterBy": ["parent_product_name", "parent_product_sku", "variant_name", "variant_sku"],
                "filter": filter
            }
        # Build additional query from unified_filter_query
        additional_query = {}
        if unified_filter_query:
            if len(unified_filter_query) == 1:
                additional_query = unified_filter_query[0]
            else:
                additional_query = {"$and": unified_filter_query}

        # Get products
        products, total_data_length, _, _ = get_paginated_records_price_list(
            store_conn['admin_db'], StoreAdminDBCollections.PRODUCT_PRICE_LISTS, payload, product_price_list_fields, additional_query
        )
        # Get price lists
        static_price_lists = fetch_static_price_lists(store_id)
        store_obj = _get_store_object_safely(store_id)
        price_lists = _get_price_lists_safely(store_obj, store_id)

        # Build price list mappings
        price_list_meta, price_list_map = _build_price_list_mappings(store_id, price_lists, static_price_lists, role_id)

        # Get price list assignments for sales percentage calculation
        assignments = list(store_conn['store_db'][StoreDBCollections.PRICE_LIST_ASSIGNMENT].find({
            "price_list_id": {"$exists": True},
            "customer_group_id": {"$exists": True}
        }))

        price_list_to_customer_group = {assignment['price_list_id']: assignment['customer_group_id'] for assignment in assignments}

        # Process products
        processed_products = _process_single_store_products_with_sales_percentage(store_id, products, store_conn, price_list_meta, price_list_map, role_id, price_list_to_customer_group)

        return {
            'products': processed_products,
            'total_count': total_data_length,
            'price_list_meta': price_list_meta,
            'price_list_map': price_list_map
        }

    except Exception as e:
        logger.error(f"Error getting store products data with filters for store {store_id}: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            'products': [],
            'total_count': 0,
            'price_list_meta': {},
            'price_list_map': {}
        }


def _process_single_store_products_original_format(store_id, products, store_conn, price_list_meta, price_list_map, role_id, price_list_to_customer_group):
    """
    Process products for single store in original format (same as get_product_price_lists).
    """
    # Get all parent SKUs for classification lookup
    all_parent_skus = [product['parent_product_sku'] for product in products]
    all_parent_product_ids = [product['parent_product_id'] for product in products]

    # Fetch margin and sales percentage data
    product_data_map_for_margin_and_sales = {}
    if all_parent_product_ids:
        try:
            product_data = _fetch_margin_and_sales_percentage(store_id, all_parent_product_ids)
            product_data_map_for_margin_and_sales = {
                (item['product_id'], item['customer_group_id']): {
                    "margin_percentage": item['margin_percentage'],
                    "sales_percentage": item['sales_percentage']
                }
                for item in product_data
            }
        except Exception as e:
            logger.error(f"Error fetching margin and sales percentage for store {store_id}: {e}")
            product_data_map_for_margin_and_sales = {}

    # Get classification data
    classification_map = {}
    if all_parent_skus:
        classification_query = text("""
            SELECT DISTINCT
                sc.parent_sku,
                sc.classification,
                sc.primary_supplier,
                usm.user_name
            FROM skuvault_catalog sc
            LEFT JOIN user_supplier_mapping usm
                ON sc.primary_supplier = usm.suppliers
            WHERE sc.parent_sku IN :parent_product_skus
        """)

        classification_results = store_conn['pg_conn'].execute(classification_query, {'parent_product_skus': tuple(all_parent_skus)}).fetchall()

        for parent_sku, classification, primary_supplier, user_name in classification_results:
            if parent_sku in classification_map:
                classification_map[parent_sku]['classifications'].add(classification)
            else:
                classification_map[parent_sku] = {
                    'classifications': {classification},
                    'primary_supplier': primary_supplier,
                    'user_name': user_name
                }

        # Convert classifications set to comma-separated string
        for sku in classification_map:
            classification_map[sku]['classifications'] = ", ".join(classification_map[sku]['classifications'])

    # Get user data for updated_by field
    updated_by_users = {product.get('updated_by') for product in products if product.get('updated_by')}
    user_data_map = user_db.fetch_users_by_usernames(updated_by_users) if updated_by_users else {}

    # Process each product (original format)
    for product in products:
        parent_product_sku = product.get('parent_product_sku', None)

        # Convert timestamps
        product['updated_at'] = convert_to_timestamp(product.get('updated_at'))
        product['created_at'] = convert_to_timestamp(product.get('created_at'))
        product['inventory_level'] = product.get('inventory_level', 0)

        # Add classification data
        product['classification'] = classification_map.get(parent_product_sku, {}).get('classifications', "")
        product['primary_supplier'] = classification_map.get(parent_product_sku, {}).get('primary_supplier', "")
        product['purchaser'] = classification_map.get(parent_product_sku, {}).get('user_name', "")

        # Assign user-friendly name for updated_by
        updated_by = product.get('updated_by')
        product['updated_by'] = user_data_map.get(updated_by, {}).get("name", updated_by)

        # Add monthly data if role allows
        if role_id != "67fd12676af694b36923ce09":
            for i in range(1, 8):
                product[f'month_{i}'] = product.get(f'month_{i}', None)

        product['turn_rate'] = product.get('turn_rate', 0)
        product['weeks_on_hand'] = product.get('weeks_on_hand', 0)

        # Process pricing data (original format)
        _process_product_pricing_original_format(product, price_list_meta, price_list_map, product_data_map_for_margin_and_sales, price_list_to_customer_group)

        # Remove variants from final output
        if 'variants' in product:
            del product['variants']

    return products

def _process_product_pricing_original_format(product, price_list_meta, price_list_map, product_data_map_for_margin_and_sales, price_list_to_customer_group):
    """
    Process pricing data for a product in original format (same as get_product_price_lists).
    """
    # Initialize pricing data
    price_list_data = {key: {"min": None, "max": None} for key in price_list_meta}
    variant_prices = []
    variant_costs = []

    for variant in product.get('variants', []):
        variant_price = product['default_price'] if variant.get('variant_price') is None else variant.get('variant_price')
        cost = variant.get('variant_cost', None)

        if variant_price is not None:
            variant_prices.append(variant_price)
        if cost is not None:
            variant_costs.append(cost)

        # Process price list efficiently
        for price in variant.get('price_list', []):
            price_list_key = price_list_map.get(price['price_list_id'])
            if price_list_key:
                price_value = float(price['price'])

                current_min = price_list_data[price_list_key]["min"]
                current_max = price_list_data[price_list_key]["max"]

                if current_min is None or price_value < current_min:
                    price_list_data[price_list_key]["min"] = price_value
                if current_max is None or price_value > current_max:
                    price_list_data[price_list_key]["max"] = price_value

    # Format the result as required (string range or single value)
    for key in price_list_data:
        min_price = price_list_data[key]["min"]
        max_price = price_list_data[key]["max"]
        if min_price is None:
            price_list_data[key] = ""
        elif min_price == max_price:
            price_list_data[key] = str(min_price)
        else:
            price_list_data[key] = f"{min_price}-{max_price}"

    # Set default_price as a range or single value
    if variant_prices:
        product_default_price = product['default_price']
        product['default_price'] = f"{product['default_price']}-{max(variant_prices)}" if len(set(variant_prices)) > 1 else str(product['default_price'])

    # Set cost_range as a range or single value
    product_cost_price = 0
    if variant_costs:
        product['cost_range'] = f"{min(variant_costs)}-{max(variant_costs)}" if len(set(variant_costs)) > 1 else str(min(variant_costs))
        product_cost_price = min(variant_costs)
    else:
        product['cost_range'] = None

    # Calculate margins
    if product_cost_price > 0 and product_default_price > 0:
        try:
            product['default_price_margin'] = round(((product_default_price - product_cost_price) / product_cost_price) * 100, 2)
        except:
            product['default_price_margin'] = 0
    else:
        product['default_price_margin'] = 0

    # Build price list array for product (original format)
    product["price_list"] = []
    for key, value in price_list_meta.items():
        price_list_id = value["id"]
        customer_group_id = price_list_to_customer_group.get(price_list_id)

        # Get margin and sales data for this product and customer group
        margin_sales = product_data_map_for_margin_and_sales.get(
            (product["parent_product_id"], customer_group_id),
            {"margin_percentage": 0.0, "sales_percentage": 0.0}
        )

        price_list_price = price_list_data.get(key, '')

        if '-' in str(price_list_price):
            min_price, max_price = map(float, str(price_list_price).split('-'))
            price_list_price = min_price
        if not price_list_price or price_list_price == '':
            price_list_price = 0

        try:
            price_list_price = float(price_list_price)
        except:
            price_list_price = 0

        margin_per = 0
        if price_list_price > 0 and product_cost_price > 0:
            margin_per = round(((price_list_price - product_cost_price) / product_cost_price) * 100, 2)

        product["price_list"].append({
            key: price_list_data[key],
            "price_list_id": price_list_id,
            "margin_percentage": margin_per,
            "sales_percentage": margin_sales["sales_percentage"]
        })


def _process_single_store_products_with_sales_percentage(store_id, products, store_conn, price_list_meta, price_list_map, role_id, price_list_to_customer_group):
    """
    Process products for single store with sales percentage calculation (for multi-store).
    """
    # Get all parent SKUs for classification lookup
    all_parent_skus = [product['parent_product_sku'] for product in products]
    all_parent_product_ids = [product['parent_product_id'] for product in products]

    # Fetch margin and sales percentage data
    product_data_map_for_margin_and_sales = {}
    if all_parent_product_ids:
        try:
            product_data = _fetch_margin_and_sales_percentage(store_id, all_parent_product_ids)
            product_data_map_for_margin_and_sales = {
                (item['product_id'], item['customer_group_id']): {
                    "margin_percentage": item['margin_percentage'],
                    "sales_percentage": item['sales_percentage']
                }
                for item in product_data
            }
        except Exception as e:
            logger.error(f"Error fetching margin and sales percentage for store {store_id}: {e}")
            product_data_map_for_margin_and_sales = {}

    # Get classification data
    classification_map = {}
    if all_parent_skus:
        classification_query = text("""
            SELECT DISTINCT
                sc.parent_sku,
                sc.classification,
                sc.primary_supplier,
                usm.user_name
            FROM skuvault_catalog sc
            LEFT JOIN user_supplier_mapping usm
                ON sc.primary_supplier = usm.suppliers
            WHERE sc.parent_sku IN :parent_product_skus
        """)

        classification_results = store_conn['pg_conn'].execute(classification_query, {'parent_product_skus': tuple(all_parent_skus)}).fetchall()

        for parent_sku, classification, primary_supplier, user_name in classification_results:
            if parent_sku in classification_map:
                classification_map[parent_sku]['classifications'].add(classification)
            else:
                classification_map[parent_sku] = {
                    'classifications': {classification},
                    'primary_supplier': primary_supplier,
                    'user_name': user_name
                }

        # Convert classifications set to comma-separated string
        for sku in classification_map:
            classification_map[sku]['classifications'] = ", ".join(classification_map[sku]['classifications'])

    # Get user data for updated_by field
    updated_by_users = {product.get('updated_by') for product in products if product.get('updated_by')}
    user_data_map = user_db.fetch_users_by_usernames(updated_by_users) if updated_by_users else {}

    # Process each product
    for product in products:
        parent_product_sku = product.get('parent_product_sku', None)

        # Convert timestamps
        product['updated_at'] = convert_to_timestamp(product.get('updated_at'))
        product['created_at'] = convert_to_timestamp(product.get('created_at'))
        product['inventory_level'] = product.get('inventory_level', 0)

        # Add classification data
        product['classification'] = classification_map.get(parent_product_sku, {}).get('classifications', "")
        product['primary_supplier'] = classification_map.get(parent_product_sku, {}).get('primary_supplier', "")
        product['purchaser'] = classification_map.get(parent_product_sku, {}).get('user_name', "")

        # Assign user-friendly name for updated_by
        updated_by = product.get('updated_by')
        product['updated_by'] = user_data_map.get(updated_by, {}).get("name", updated_by)

        # Add monthly data if role allows
        if role_id != "67fd12676af694b36923ce09":
            for i in range(1, 8):
                product[f'month_{i}'] = product.get(f'month_{i}', None)

        product['turn_rate'] = product.get('turn_rate', 0)
        product['weeks_on_hand'] = product.get('weeks_on_hand', 0)

        # Process pricing data with sales percentage
        _process_product_pricing_with_sales_percentage(product, price_list_meta, price_list_map, product_data_map_for_margin_and_sales, price_list_to_customer_group)

        # Remove variants from final output
        if 'variants' in product:
            del product['variants']

    return products

def _process_product_pricing_with_sales_percentage(product, price_list_meta, price_list_map, product_data_map_for_margin_and_sales, price_list_to_customer_group):
    """
    Process pricing data for a product with sales percentage calculation.
    """
    # Initialize pricing data
    price_list_data = {key: {"min": None, "max": None} for key in price_list_meta}
    variant_prices = []
    variant_costs = []

    for variant in product.get('variants', []):
        variant_price = product['default_price'] if variant.get('variant_price') is None else variant.get('variant_price')
        cost = variant.get('variant_cost', None)

        if variant_price is not None:
            variant_prices.append(variant_price)
        if cost is not None:
            variant_costs.append(cost)

        # Process price list efficiently
        for price in variant.get('price_list', []):
            price_list_key = price_list_map.get(price['price_list_id'])
            if price_list_key:
                price_value = float(price['price'])

                current_min = price_list_data[price_list_key]["min"]
                current_max = price_list_data[price_list_key]["max"]

                if current_min is None or price_value < current_min:
                    price_list_data[price_list_key]["min"] = price_value
                if current_max is None or price_value > current_max:
                    price_list_data[price_list_key]["max"] = price_value

    # Format the result as required (string range or single value)
    for key in price_list_data:
        min_price = price_list_data[key]["min"]
        max_price = price_list_data[key]["max"]
        if min_price is None:
            price_list_data[key] = ""
        elif min_price == max_price:
            price_list_data[key] = str(min_price)
        else:
            price_list_data[key] = f"{min_price}-{max_price}"

    # Set default_price as a range or single value
    if variant_prices:
        product_default_price = product['default_price']
        product['default_price'] = f"{product['default_price']}-{max(variant_prices)}" if len(set(variant_prices)) > 1 else str(product['default_price'])

    # Set cost_range as a range or single value
    product_cost_price = 0
    if variant_costs:
        product['cost_range'] = f"{min(variant_costs)}-{max(variant_costs)}" if len(set(variant_costs)) > 1 else str(min(variant_costs))
        product_cost_price = min(variant_costs)
    else:
        product['cost_range'] = None

    # Calculate margins
    if product_cost_price > 0 and product_default_price > 0:
        try:
            product['default_price_margin'] = round(((product_default_price - product_cost_price) / product_cost_price) * 100, 2)
        except:
            product['default_price_margin'] = 0
    else:
        product['default_price_margin'] = 0

    # Build price list array for product with sales percentage
    product["price_list"] = []
    for key, value in price_list_meta.items():
        price_list_id = value["id"]
        customer_group_id = price_list_to_customer_group.get(price_list_id)

        # Get margin and sales data for this product and customer group
        margin_sales = product_data_map_for_margin_and_sales.get(
            (product["parent_product_id"], customer_group_id),
            {"margin_percentage": 0.0, "sales_percentage": 0.0}
        )

        price_list_price = price_list_data.get(key, '')

        if '-' in str(price_list_price):
            min_price, max_price = map(float, str(price_list_price).split('-'))
            price_list_price = min_price
        if not price_list_price or price_list_price == '':
            price_list_price = 0

        try:
            price_list_price = float(price_list_price)
        except:
            price_list_price = 0

        margin_per = 0
        if price_list_price > 0 and product_cost_price > 0:
            margin_per = round(((price_list_price - product_cost_price) / product_cost_price) * 100, 2)

        product["price_list"].append({
            key: price_list_data[key],
            "price_list_id": price_list_id,
            "margin_percentage": margin_per,
            "sales_percentage": margin_sales["sales_percentage"]
        })

def _get_store_suffix(store_id):
    """
    Get store suffix for naming store-specific fields.
    """
    # You can customize this mapping based on your store naming convention
    store_suffixes = {
        'store1': 'midwest',
        'store2': 'cbd',
        # Add more mappings as needed
    }
    return store_suffixes.get(store_id, store_id.lower())


def _get_store_object_safely(store_id):
    """
    Safely get store object by ID with error handling.
    """
    try:
        store_obj = store_util.get_store_by_id(store_id)
        return store_obj

    except Exception as e:
        logger.error(f"Error getting store object for {store_id}: {e}")
        return None

def _get_price_lists_safely(store_obj, store_id):
    """
    Safely get price lists from BigCommerce with error handling.
    """
    try:
        if store_obj:
            price_lists, _ = bc_price_list.fetch_price_lists(store_obj)
            return price_lists
        else:
            logger.warning(f"No store object provided for store {store_id}")
            return {'data': []}
    except Exception as e:
        logger.error(f"Error fetching BigCommerce price lists for store {store_id}: {e}")
        logger.error(f"Store object keys: {list(store_obj.keys()) if store_obj else 'None'}")
        return {'data': []}

def _map_price_lists_between_stores(price_list_mappings):
    """
    Map price lists between stores by title, handling different price_list_ids.
    """
    # Collect all unique price list names across stores
    all_price_list_names = set()
    for store_id, price_meta in price_list_mappings.items():
        for key, price_info in price_meta.items():
            _ = key  # Suppress unused variable warning
            all_price_list_names.add(price_info['name'])

    # Create unified mapping
    unified_mapping = {}
    price_counter = 1

    for name in sorted(all_price_list_names):
        unified_key = f"price_{price_counter}"
        unified_mapping[unified_key] = {
            'name': name,
            'active': True,
            'store_mappings': {}  # Maps store_id to their local price_list_id
        }

        # Find the price_list_id for this name in each store
        for store_id, price_meta in price_list_mappings.items():
            for key, price_info in price_meta.items():
                _ = key  # Suppress unused variable warning
                if price_info['name'] == name:
                    unified_mapping[unified_key]['store_mappings'][store_id] = price_info['id']
                    break

        price_counter += 1

    return unified_mapping


# Example usage function
def example_usage():
    """
    Example of how to use the multi-store product price lists function.

    This function demonstrates:
    1. Single store usage (all filters enabled)
    2. Multi-store usage (only skuvault_catalog filters enabled)
    3. Price list mapping between stores
    4. Response format with store-specific suffixes
    """

    # Example 1: Single store - all filters enabled
    single_store_example = {
        'store_ids': ['store1'],
        'page': 1,
        'limit': 10,
        'sort_by': 'created_at/desc',
        'filter': 'product_name',
        'user': {'role_id': '67f5f9c43c97938e59357472'},
        'tag_filter': '1,2,3',  # Enabled for single store
        'classification_filter': 'A,B,C',  # Enabled
        'classified_as_filter': '1,2',  # Enabled for single store
        'user_filter': 'john_doe',  # Enabled
        'supplier_filter': 'Supplier1;Supplier2',  # Enabled
        'top_products_filter': '100',  # Enabled for single store
        'cost_margin_filter': '50'  # Enabled for single store
    }

    # Example 2: Multi-store - only skuvault_catalog filters enabled
    multi_store_example = {
        'store_ids': ['store1', 'store2'],  # Multiple stores
        'page': 1,
        'limit': 10,
        'sort_by': 'created_at/desc',
        'filter': 'product_name',
        'user': {'role_id': '67f5f9c43c97938e59357472'},
        # Only these filters are allowed for multi-store:
        'classification_filter': 'A,B,C',  # Uses skuvault_catalog
        'user_filter': 'john_doe',  # Uses skuvault_catalog via user_supplier_mapping
        'supplier_filter': 'Supplier1;Supplier2',  # Uses skuvault_catalog
        # These filters are hidden/disabled for multi-store:
        'tag_filter': None,  # Disabled - doesn't use skuvault_catalog for parent_sku
        'classified_as_filter': None,  # Disabled - uses replenishment_dashboard
        'top_products_filter': None,  # Disabled - uses analytics tables
        'cost_margin_filter': None  # Disabled - uses product-specific data
    }

    # Example response format for single store (same as original get_product_price_lists):
    single_store_response = {
        'data': [
            {
                'parent_product_id': 123,
                'parent_product_name': 'Example Product',
                'parent_product_sku': 'PROD-123',
                'classification': 'Category A',
                'primary_supplier': 'Supplier1',
                'purchaser': 'john_doe',
                'updated_at': '2024-01-01T00:00:00Z',
                'created_at': '2024-01-01T00:00:00Z',
                'updated_by': 'John Doe',
                'cost_range': '10.00-15.00',
                'default_price_margin': 25.5,
                'inventory_level': 100,
                'turn_rate': 2.5,
                'weeks_on_hand': 4,
                'default_price': '20.00',
                'month_1': 10,
                'month_2': 12,
                # ... more monthly data
                'price_list': [
                    {
                        'price_1': '18.00',
                        'price_list_id': 7,
                        'margin_percentage': 20.0,
                        'sales_percentage': 45.2
                    }
                ]
            }
        ],
        'meta': {
            'price_lists': {
                'price_1': {
                    'id': 7,
                    'name': 'Wholesale',
                    'active': True
                }
            },
            'months': {
                'month_1': 'Jan 2024',
                'month_2': 'Feb 2024'
            }
        }
    }

    # Example response format for multi-store (separate objects for each store):
    multi_store_response = {
        'data': [
            {
                # Common fields at parent level
                'parent_product_id': 123,
                'parent_product_name': 'Example Product',
                'parent_product_sku': 'PROD-123',
                'classification': 'Category A',
                'primary_supplier': 'Supplier1',
                'purchaser': 'john_doe',
                'updated_at': '2024-01-01T00:00:00Z',
                'created_at': '2024-01-01T00:00:00Z',
                'updated_by': 'John Doe',
                'cost_range': '10.00-15.00',
                'default_price_margin': 25.5,

                # Separate objects for each store
                'stores': [
                    {
                        'store_id': 'store1',
                        'store_name': 'Store Midwest',
                        'inventory_level': 100,
                        'turn_rate': 2.5,
                        'weeks_on_hand': 4,
                        'default_price': '20.00',
                        'month_1': 10,
                        'month_2': 12,
                        'price_list': [
                            {
                                'price_1': '18.00',
                                'price_list_id': 7,
                                'margin_percentage': 20.0,
                                'sales_percentage': 45.2
                            }
                        ]
                    },
                    {
                        'store_id': 'store2',
                        'store_name': 'Store Cbd',
                        'inventory_level': 50,
                        'turn_rate': 1.8,
                        'weeks_on_hand': 6,
                        'default_price': '22.00',
                        'month_1': 8,
                        'month_2': 9,
                        'price_list': [
                            {
                                'price_1': '19.00',
                                'price_list_id': 7,
                                'margin_percentage': 18.5,
                                'sales_percentage': 38.7
                            }
                        ]
                    }
                ]
            }
        ],
        'meta': {
            'price_lists': {
                'price_1': {
                    'name': 'Wholesale',
                    'active': True,
                    'store_mappings': {
                        'store1': 7,
                        'store2': 7
                    }
                }
            },
            'months': {
                'month_1': 'Jan 2024',
                'month_2': 'Feb 2024'
            },
            'stores': {
                'store1': {
                    'id': 'store1',
                    'name': 'Store Midwest',
                    'suffix': 'midwest'
                },
                'store2': {
                    'id': 'store2',
                    'name': 'Store Cbd',
                    'suffix': 'cbd'
                }
            }
        }
    }

    return {
        'single_store_example': single_store_example,
        'multi_store_example': multi_store_example,
        'single_store_response': single_store_response,
        'multi_store_response': multi_store_response
    }

# Debug function to test store connections
def debug_store_connection(store_id):
    """
    Debug function to test store connections and identify issues.
    """
    try:
        logger.info(f"Testing store connection for store_id: {store_id}")

        # Test store object retrieval
        store_obj = store_util.get_store_by_id(store_id)
        logger.info(f"Store object retrieved: {store_obj is not None}")
        if store_obj:
            logger.info(f"Store object keys: {list(store_obj.keys())}")
            logger.info(f"Has client_id: {'client_id' in store_obj}")
            logger.info(f"Has access_token: {'access_token' in store_obj}")

        # Test database connections
        try:
            admin_db = get_admin_db_client_for_store_id(store_id)
            logger.info(f"Admin DB connection: {admin_db is not None}")
        except Exception as e:
            logger.error(f"Admin DB connection failed: {e}")

        try:
            store_db = get_store_db_client_for_store_id(store_id)
            logger.info(f"Store DB connection: {store_db is not None}")
        except Exception as e:
            logger.error(f"Store DB connection failed: {e}")

        try:
            pg_conn = new_pgdb.get_connection(store_id)
            logger.info(f"PostgreSQL connection: {pg_conn is not None}")
            if pg_conn:
                pg_conn.close()
        except Exception as e:
            logger.error(f"PostgreSQL connection failed: {e}")

        # Test BigCommerce API call
        if store_obj:
            try:
                price_lists, _ = bc_price_list.fetch_price_lists(store_obj)
                logger.info(f"BigCommerce price lists fetched successfully: {len(price_lists.get('data', []))} price lists")
            except Exception as e:
                logger.error(f"BigCommerce API call failed: {e}")
                logger.error(f"Error type: {type(e).__name__}")

        return True

    except Exception as e:
        logger.error(f"Debug store connection failed: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def _fetch_margin_and_sales_percentage(store_id, product_ids):
    """
    Fetch margin and sales percentage data from analytics.profitability_product_customer_groups table.
    """
    conn = new_pgdb.get_connection(store_id)
    try:
        query = text("""WITH product_filter AS (
                        SELECT unnest(CAST(:product_ids AS int[])) AS product_id
                        )
                        SELECT
                            li.product_id,
                            li.customer_group_id,
                            SUM(li.revenue) AS revenue,
                            SUM(li.total_cost) AS total_cost,
                            SUM(li.profit) AS profit
                        FROM analytics.profitability_product_customer_groups li
                        JOIN product_filter pf ON pf.product_id = li.product_id
                        WHERE li.order_date >= current_date - interval '30 day'
                        GROUP BY li.product_id, li.customer_group_id;
                """)
        result = conn.execute(query, {"product_ids": list(product_ids)}).fetchall()

        # Step 1: Group data and calculate total revenue per product
        grouped_data = [
            {
                'product_id': row[0],
                'customer_group_id': row[1],
                'revenue': Decimal(str(row[2])) if row[2] else Decimal('0'),
                'total_cost': Decimal(str(row[3])) if row[3] else Decimal('0'),
                'profit': Decimal(str(row[4])) if row[4] else Decimal('0')
            }
            for row in result
        ]

        total_revenue_per_product = defaultdict(Decimal)
        for item in grouped_data:
            total_revenue_per_product[item['product_id']] += item['revenue']

        # Step 2: Build final list with percentages (minimal loop)
        product_data = []
        for item in grouped_data:
            revenue = item['revenue']
            product_id = item['product_id']

            sales_pct = (revenue * 100 / total_revenue_per_product[product_id]).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP) if total_revenue_per_product[product_id] > 0 else Decimal('0.00')

            product_data.append({
                'product_id': product_id,
                'customer_group_id': item['customer_group_id'],
                'margin_percentage': 0,
                'sales_percentage': float(sales_pct)
            })

        return product_data

    except Exception as e:
        _ = e  # Suppress unused variable warning
        logger.error("Error in _fetch_margin_and_sales_percentage:\n" + traceback.format_exc())
        return []

    finally:
        conn.close()

def get_multi_store_product_price_list_details(store_ids, product_sku, user, tag_filter=None, store_name=None):
    if store_name == 'both':
        if "63da3e98b702e324567f76f9" in store_ids:
            store_ids.append("661239751b9ce4bd7f85237c")
        elif "661239751b9ce4bd7f85237c" in store_ids:
            store_ids.append("63da3e98b702e324567f76f9")
    elif store_name == 'midwest':
        if "63da3e98b702e324567f76f9" not in store_ids:
            store_ids.append("63da3e98b702e324567f76f9")
        if "661239751b9ce4bd7f85237c" in store_ids:
            store_ids.remove("661239751b9ce4bd7f85237c")
    elif store_name == 'cbd':
        if "661239751b9ce4bd7f85237c" not in store_ids:
            store_ids.append("661239751b9ce4bd7f85237c")
        if "63da3e98b702e324567f76f9" in store_ids:
            store_ids.remove("63da3e98b702e324567f76f9")
    if not store_ids or len(store_ids) == 0:
        return {"data": [], "meta": {"price_lists": {}, "months": {}}}

    # If only one store, return original format
    if len(store_ids) == 1:
        return _get_single_store_product_details_original_format(store_ids[0], product_sku, user, tag_filter)

    # Multiple stores logic
    return _get_multi_store_product_details(store_ids, product_sku, user, tag_filter)

def _get_single_store_product_details_original_format(store_id, product_sku, user, tag_filter=None):
    """
    Get product details from single store with same response structure as multi-store.
    """
    try:
        # Setup connections for single store
        store_connections = _setup_store_connections([store_id])
        store_conn = store_connections[store_id]

        # Get role_id and setup price lists
        role_id = user.get('role_id')
        static_price_lists = fetch_static_price_lists(store_id)
        store_obj = _get_store_object_safely(store_id)
        price_lists = _get_price_lists_safely(store_obj, store_id)

        # Build price list mappings for details (keep original flow)
        price_list_meta, price_list_map, active_price_lists = _build_price_list_mappings_for_details(store_id, price_lists, static_price_lists, role_id)

        # Get price list assignments for sales percentage calculation
        assignments = list(store_conn['store_db'][StoreDBCollections.PRICE_LIST_ASSIGNMENT].find({
            "price_list_id": {"$exists": True},
            "customer_group_id": {"$exists": True}
        }))

        price_list_to_customer_group = {assignment['price_list_id']: assignment['customer_group_id'] for assignment in assignments}

        # Handle tag filter for variant filtering
        variant_sku_list = None
        if tag_filter:
            variant_sku_list = _get_variant_skus_from_tags(store_conn['pg_conn'], tag_filter)

        # Get product data
        product = _get_product_by_id_with_variants(store_conn['admin_db'], product_sku, variant_sku_list)

        if not product:
            # Create unified price list mapping for empty response
            price_list_mappings = {store_id: price_list_meta}
            unified_price_list_mapping = _map_price_lists_between_stores(price_list_mappings)

            month_names, _ = get_month_array_for_meta(6)
            return {
                "data": [],
                "meta": {
                    "price_lists": unified_price_list_mapping,
                    "months": month_names,
                    "stores": {
                        store_id: {
                            'id': store_id,
                            'name': store_conn['store_name'],
                            'suffix': _get_store_suffix(store_id)
                        }
                    }
                }
            }

        # Process product details (keep original processing)
        processed_product = _process_product_details_original_format(
            product, store_conn, price_list_meta, price_list_map, active_price_lists,
            price_list_to_customer_group, role_id, store_id
        )

        # Convert single store product details to multi-store format
        converted_product = _convert_single_store_product_details_to_multi_store_format(processed_product, store_id, role_id)

        # Create unified price list mapping (same as multi-store)
        price_list_mappings = {store_id: price_list_meta}
        unified_price_list_mapping = _map_price_lists_between_stores(price_list_mappings)

        # Create response (same structure as multi-store)
        month_names, _ = get_month_array_for_meta(6)
        data = {
            "data": [converted_product],
            "meta": {
                "price_lists": unified_price_list_mapping,
                "months": month_names if role_id != "67fd12676af694b36923ce09" else {},
                "stores": {
                    store_id: {
                        'id': store_id,
                        'name': store_conn['store_name'],
                        'suffix': _get_store_suffix(store_id)
                    }
                }
            }
        }

        return data

    except Exception as e:
        logger.error(f"Error in _get_single_store_product_details_original_format: {traceback.format_exc()}")
        raise e
    finally:
        _close_store_connections(store_connections)

def _convert_single_store_product_details_to_multi_store_format(product, store_id, role_id):
    """
    Convert single store product details format to multi-store format.
    """
    # Extract store-specific data
    store_data = {
        'store_id': store_id,
        'store_name': f"Store {_get_store_suffix(store_id).title()}",
        'default_price': product.get('default_price'),
        'default_price_margin': product.get('default_price_margin'),
        'default_price_sales': product.get('default_price_sales'),
        'cost_range': product.get('cost_range'),
        'cost_margin': product.get('cost_margin'),
        'minimum_cost': product.get('minimum_cost'),
        'variants': product.get('variants', [])
    }

    # Add monthly data if role allows
    if role_id != "67fd12676af694b36923ce09":
        for i in range(1, 8):
            month_field = f'month_{i}'
            store_data[month_field] = product.get(month_field)

    # Create multi-store format product
    converted_product = {
        # Common fields at parent level
        'parent_product_id': product.get('parent_product_id'),
        'parent_product_name': product.get('parent_product_name'),
        'parent_product_sku': product.get('parent_product_sku'),
        'show_parent_product': product.get('show_parent_product'),

        # Store-specific data in stores array
        'stores': [store_data]
    }

    return converted_product

def _get_multi_store_product_details(store_ids, product_sku, user, tag_filter=None):
    """
    Get product details from multiple stores with separate store objects.
    """
    try:
        # Setup connections for all stores
        store_connections = _setup_store_connections(store_ids)

        # Get role_id
        role_id = user.get('role_id')

        # Get data from each store
        all_store_data = {}
        price_list_mappings = {}
        store_names = {}

        for store_id in store_ids:
            store_names[store_id] = store_connections[store_id]['store_name']
            store_data = _get_store_product_details_data(store_id, store_connections[store_id], product_sku, user, tag_filter)
            all_store_data[store_id] = store_data
            if store_data['product']:
                price_list_mappings[store_id] = store_data['price_list_meta']

        # Map price lists between stores by title
        unified_price_list_mapping = _map_price_lists_between_stores(price_list_mappings)

        # Create multi-store response for product details
        multi_store_response = _create_multi_store_product_details_response(all_store_data, store_ids, unified_price_list_mapping, role_id, store_names)

        return multi_store_response

    except Exception as e:
        logger.error(f"Error in _get_multi_store_product_details: {traceback.format_exc()}")
        raise e
    finally:
        _close_store_connections(store_connections)

def _build_price_list_mappings_for_details(store_id, price_lists, static_price_lists, role_id):
    """
    Build price list mappings for product details (similar to original function).
    """
    # Suppress unused parameter warning
    _ = store_id

    # Merge static price lists
    for static_price_list in static_price_lists:
        price_lists['data'].append({
            'id': static_price_list['id'],
            'name': static_price_list['name'],
            'date_created': None,
            'date_modified': None,
            'active': static_price_list['active']
        })

    # Role-specific filtering
    if role_id == '67f5f9c43c97938e59357472':
        price_lists['data'] = [pl for pl in price_lists['data'] if pl['id'] not in (14, 15)]
    elif role_id == '67fd12676af694b36923ce09':
        price_lists['data'] = [pl for pl in price_lists['data'] if pl['id'] in (13, 15, 52)]

    # Create price list mappings and separate active/inactive price lists
    price_list_meta = {}
    price_list_map = {}
    active_price_lists = []

    # Ensure price_list_id 7 appears at the top of active price lists
    priority_price_list = None

    for price_list in price_lists['data']:
        price_list_entry = {
            'id': price_list['id'],
            'key': f"price_{len(price_list_meta) + 1}",  # key like "price_1", "price_2", etc.
            'name': price_list['name'],
            'active': price_list['active']
        }

        if price_list['id'] == 7 and price_list['active']:
            priority_price_list = price_list_entry  # Keep reference to price_list_id 7
        elif price_list['active']:
            active_price_lists.append(price_list_entry)

        price_list_map[price_list['id']] = price_list_entry

    # Insert priority price list (id 7) at the top if it exists
    if priority_price_list:
        active_price_lists.insert(0, priority_price_list)

    # Now swap positions of price list id 14 and 15
    index_14 = next((i for i, pl in enumerate(active_price_lists) if pl['id'] == 14), None)
    index_15 = next((i for i, pl in enumerate(active_price_lists) if pl['id'] == 15), None)

    if index_14 is not None and index_15 is not None:
        active_price_lists[index_14], active_price_lists[index_15] = active_price_lists[index_15], active_price_lists[index_14]

    # Populate price_list_meta, ensuring price_list_id 7 is first in order
    for idx, price_list in enumerate(active_price_lists, 1):
        price_list_key = f"price_{idx}"
        price_list_meta[price_list_key] = {
            'id': price_list['id'],
            'name': price_list['name'],
            'active': price_list['active']
        }
        price_list_map[price_list['id']]['key'] = price_list_key

    return price_list_meta, price_list_map, active_price_lists

def _get_variant_skus_from_tags(pg_conn, tag_filter):
    """
    Get variant SKUs from tag filter.
    """
    try:
        tag_filter_list = [tag.strip() for tag in tag_filter.split(',')]
        query = text("SELECT variant_sku FROM product_tags WHERE tag_id = ANY(:tag_filter_list)")
        result = pg_conn.execute(query, {"tag_filter_list": tag_filter_list}).fetchall()
        tag_skus = [row[0] for row in result]
        return [sku for sku in tag_skus if sku is not None]
    except Exception as e:
        logger.error(f"Error getting variant SKUs from tags: {e}")
        return []

def _get_product_by_id_with_variants(admin_db, product_sku, variant_sku_list=None):
    """
    Get product by ID with optional variant filtering.
    """
    try:
        if variant_sku_list:
            product = admin_db[StoreAdminDBCollections.PRODUCT_PRICE_LISTS].aggregate([
                {
                    "$match": {
                        "parent_product_sku": product_sku
                    }
                },
                {
                    "$addFields": {
                        "variants": {
                            "$filter": {
                                "input": "$variants",
                                "as": "v",
                                "cond": {"$in": ["$$v.variant_sku", variant_sku_list]}
                            }
                        }
                    }
                }
            ])
            return next(product, None)
        else:
            return admin_db[StoreAdminDBCollections.PRODUCT_PRICE_LISTS].find_one({
                "parent_product_sku": product_sku
            })
    except Exception as e:
        logger.error(f"Error getting product by ID: {e}")
        return None

def _process_product_details_original_format(product, store_conn, price_list_meta, price_list_map, active_price_lists, price_list_to_customer_group, role_id, store_id):
    """
    Process product details in original format (same as get_product_price_list_details).
    """
    try:
        # Collect all variant SKUs
        variant_skus = [variant['variant_sku'] for variant in product['variants'] if variant.get('variant_sku')]

        # Get classification data for variants
        classification_map = {}
        if variant_skus:
            classification_query = text("""
                SELECT DISTINCT
                    sc.sku,
                    sc.classification
                FROM skuvault_catalog sc
                WHERE sc.sku IN :variant_skus
            """)

            classification_results = store_conn['pg_conn'].execute(classification_query, {'variant_skus': tuple(variant_skus)}).fetchall()

            for sku, classification in classification_results:
                if sku in classification_map:
                    classification_map[sku]['classifications'].add(classification)
                else:
                    classification_map[sku] = {
                        'classifications': {classification},  # use a set for uniqueness
                    }

            # Convert classifications set to a comma-separated string
            for sku in classification_map:
                classification_map[sku]['classifications'] = ", ".join(classification_map[sku]['classifications'])

        parent_product_id = product['parent_product_id']

        # Fetch margin and sales percentage data
        product_data_map = {}
        if parent_product_id:
            product_data = _fetch_margin_and_sales_percentage(store_id, [parent_product_id])
            # Build a map based on customer_group_id only
            product_data_map = {
                item['customer_group_id']: {
                    "margin_percentage": item['margin_percentage'],
                    "sales_percentage": item['sales_percentage']
                }
                for item in product_data
            }

        # Process variants and calculate price ranges
        variant_costs = []
        price_list_data = {key: "" for key in price_list_meta}
        minimum_price_data = {}

        for variant in product['variants']:
            # Process price list efficiently
            for price in variant['price_list']:
                price_list_key = price_list_map.get(price['price_list_id'])
                if price_list_key:
                    price_list_key = price_list_key['key']
                    price_value = float(price['price'])
                    if price_list_data[price_list_key]:  # Already has a value
                        min_price, max_price = map(float, price_list_data[price_list_key].split('-')) if '-' in price_list_data[price_list_key] else (float(price_list_data[price_list_key]), float(price_list_data[price_list_key]))
                        price_list_data[price_list_key] = f"{min(min_price, price_value)}-{max(max_price, price_value)}" if min_price != max_price else str(min_price)
                    else:
                        price_list_data[price_list_key] = str(price_value)

                    # Track minimum price for each price list
                    minimum_price_key = f"minimum_{price_list_key}"
                    if minimum_price_key in minimum_price_data:
                        current_min = float(minimum_price_data[minimum_price_key])  # convert string to float
                        minimum_price_data[minimum_price_key] = str(min(current_min, price_value))
                    else:
                        minimum_price_data[minimum_price_key] = str(price_value)

            # Process variant data
            variant['classification'] = classification_map.get(variant['variant_sku'], {}).get('classifications', "")
            variant['variant_price'] = product['default_price'] if variant.get('variant_price') is None else variant.get('variant_price')
            variant_price_lists = []
            default_price_lists = {price_list['id']: "" for price_list in active_price_lists}

            for price in variant['price_list']:
                price_list_id = price['price_list_id']
                if price_list_id in price_list_map and price_list_map[price_list_id]['active']:
                    default_price_lists[price_list_id] = str(price.pop('price'))

            for price_list in active_price_lists:
                variant_price_lists.append({
                    "price_list_id": price_list['id'],
                    price_list['key']: default_price_lists[price_list['id']]
                })

            variant['price_list'] = variant_price_lists
            variant["parent_product_id"] = product['parent_product_id']
            variant["parent_product_name"] = product['parent_product_name']
            variant["inventory_level"] = variant.get("inventory_level")

            # Add monthly data if role allows
            if role_id != "67fd12676af694b36923ce09":
                for i in range(1, 8):
                    variant[f'month_{i}'] = variant.get(f'month_{i}', None)

            variant['turn_rate'] = variant.get('turn_rate', 0)
            variant['weeks_on_hand'] = variant.get('weeks_on_hand', 0)
            variant['cost'] = variant.get('variant_cost', None)

            if variant['cost'] is not None:
                variant_costs.append(variant['cost'])

        # Calculate cost range and margins
        product_cost_price = 0
        if len(set(variant_costs)) > 1:
            product['cost_range'] = f"{min(variant_costs)}-{max(variant_costs)}"
            product_cost_price = min(variant_costs)
        elif variant_costs:
            product['cost_range'] = str(variant_costs[0])
            product_cost_price = min(variant_costs)
        else:
            product['cost_range'] = None

        if product_cost_price > 0 and product['default_price'] > 0:
            product['default_price_margin'] = round(((product['default_price'] - product_cost_price) / product_cost_price) * 100, 2)
        else:
            product['default_price_margin'] = 0

        # Get default price sales percentage
        default_price_sales = product_data_map.get(
            1,
            {"margin_percentage": 0.0, "sales_percentage": 0.0}
        )
        product['default_price_sales'] = default_price_sales["sales_percentage"]

        # Set the flag based on variants length and variant_name condition
        show_parent_product = not(len(product['variants']) == 1 and product['variants'][0]['variant_name'] == "Parent Product")
        product["price_list"] = []

        if not product_cost_price or product_cost_price == '':
            product_cost_price = 0

        product_cost_price = float(product_cost_price)

        # Build product-level price list
        for key, value in price_list_meta.items():
            price_list_id = value["id"]
            customer_group_id = price_list_to_customer_group.get(price_list_id)
            margin_sales = product_data_map.get(
                customer_group_id,
                {"margin_percentage": 0.0, "sales_percentage": 0.0}
            )

            price_list_price = minimum_price_data.get(f"minimum_{key}", None)
            if not price_list_price or price_list_price == '':
                price_list_price = 0
            price_list_price = float(price_list_price)
            margin_per = 0
            if price_list_price > 0 and product_cost_price > 0:
                margin_per = round(((price_list_price - product_cost_price) / product_cost_price) * 100, 2)

            product["price_list"].append({
                key: value["name"],
                "price_list_id": price_list_id,
                f"minimum_{key}": minimum_price_data.get(f"minimum_{key}", None),
                "margin_percentage": margin_per,
                "sales_percentage": margin_sales["sales_percentage"]
            })

        # Build final response
        processed_product = {
            "parent_product_id": product['parent_product_id'],
            "parent_product_name": product['parent_product_name'],
            "parent_product_sku": product['parent_product_sku'],
            "default_price": product['default_price'],
            "default_price_margin": product['default_price_margin'],
            "default_price_sales": product['default_price_sales'],
            "cost_range": product.get('cost_range', None),
            "cost_margin": product.get('cost_margin', None),
            "minimum_cost": str(product_cost_price),
            "show_parent_product": show_parent_product,
            "price_list": product['price_list'],
            "variants": product['variants']
        }

        # Add monthly data if role allows
        if role_id != "67fd12676af694b36923ce09":
            for i in range(1, 8):
                processed_product[f"month_{i}"] = product.get(f"month_{i}", None)

        return processed_product

    except Exception as e:
        logger.error(f"Error processing product details: {traceback.format_exc()}")
        raise e

def _get_store_product_details_data(store_id, store_conn, product_sku, user, tag_filter=None):
    """
    Get product details data for a specific store.
    """
    try:
        # Get role_id and setup price lists
        role_id = user.get('role_id')
        static_price_lists = fetch_static_price_lists(store_id)
        store_obj = _get_store_object_safely(store_id)
        price_lists = _get_price_lists_safely(store_obj, store_id)

        # Build price list mappings for details
        price_list_meta, price_list_map, active_price_lists = _build_price_list_mappings_for_details(store_id, price_lists, static_price_lists, role_id)

        # Get price list assignments for sales percentage calculation
        assignments = list(store_conn['store_db'][StoreDBCollections.PRICE_LIST_ASSIGNMENT].find({
            "price_list_id": {"$exists": True},
            "customer_group_id": {"$exists": True}
        }))

        price_list_to_customer_group = {assignment['price_list_id']: assignment['customer_group_id'] for assignment in assignments}

        # Handle tag filter for variant filtering
        variant_sku_list = None
        if tag_filter:
            variant_sku_list = _get_variant_skus_from_tags(store_conn['pg_conn'], tag_filter)

        # Get product data
        product = _get_product_by_id_with_variants(store_conn['admin_db'], product_sku, variant_sku_list)

        if not product:
            return {
                'product': None,
                'price_list_meta': price_list_meta,
                'price_list_map': price_list_map,
                'active_price_lists': active_price_lists
            }

        # Process product details for multi-store
        processed_product = _process_product_details_for_multi_store(
            product, store_conn, price_list_meta, price_list_map, active_price_lists,
            price_list_to_customer_group, role_id, store_id
        )

        return {
            'product': processed_product,
            'price_list_meta': price_list_meta,
            'price_list_map': price_list_map,
            'active_price_lists': active_price_lists
        }

    except Exception as e:
        logger.error(f"Error getting store product details data for store {store_id}: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            'product': None,
            'price_list_meta': {},
            'price_list_map': {},
            'active_price_lists': []
        }

def _process_product_details_for_multi_store(product, store_conn, price_list_meta, price_list_map, active_price_lists, price_list_to_customer_group, role_id, store_id):
    """
    Process product details for multi-store (simplified version).
    """
    # Suppress unused parameter warnings
    _ = price_list_meta, price_list_to_customer_group

    try:
        # Collect all variant SKUs
        variant_skus = [variant['variant_sku'] for variant in product['variants'] if variant.get('variant_sku')]

        # Get classification data for variants
        classification_map = {}
        if variant_skus:
            classification_query = text("""
                SELECT DISTINCT
                    sc.sku,
                    sc.classification
                FROM skuvault_catalog sc
                WHERE sc.sku IN :variant_skus
            """)

            classification_results = store_conn['pg_conn'].execute(classification_query, {'variant_skus': tuple(variant_skus)}).fetchall()

            for sku, classification in classification_results:
                if sku in classification_map:
                    classification_map[sku]['classifications'].add(classification)
                else:
                    classification_map[sku] = {
                        'classifications': {classification},
                    }

            # Convert classifications set to a comma-separated string
            for sku in classification_map:
                classification_map[sku]['classifications'] = ", ".join(classification_map[sku]['classifications'])

        parent_product_id = product['parent_product_id']

        # Fetch margin and sales percentage data
        product_data_map = {}
        if parent_product_id:
            product_data = _fetch_margin_and_sales_percentage(store_id, [parent_product_id])
            product_data_map = {
                item['customer_group_id']: {
                    "margin_percentage": item['margin_percentage'],
                    "sales_percentage": item['sales_percentage']
                }
                for item in product_data
            }

        # Process variants
        variant_costs = []
        for variant in product['variants']:
            # Process variant data
            variant['classification'] = classification_map.get(variant['variant_sku'], {}).get('classifications', "")
            variant['variant_price'] = product['default_price'] if variant.get('variant_price') is None else variant.get('variant_price')

            # Process price list for variant
            variant_price_lists = []
            default_price_lists = {price_list['id']: "" for price_list in active_price_lists}

            for price in variant['price_list']:
                price_list_id = price['price_list_id']
                if price_list_id in price_list_map and price_list_map[price_list_id]['active']:
                    default_price_lists[price_list_id] = str(price.get('price', ''))

            for price_list in active_price_lists:
                variant_price_lists.append({
                    "price_list_id": price_list['id'],
                    price_list['key']: default_price_lists[price_list['id']]
                })

            variant['price_list'] = variant_price_lists
            variant["parent_product_id"] = product['parent_product_id']
            variant["parent_product_name"] = product['parent_product_name']
            variant["inventory_level"] = variant.get("inventory_level")

            # Add monthly data if role allows
            if role_id != "67fd12676af694b36923ce09":
                for i in range(1, 8):
                    variant[f'month_{i}'] = variant.get(f'month_{i}', None)

            variant['turn_rate'] = variant.get('turn_rate', 0)
            variant['weeks_on_hand'] = variant.get('weeks_on_hand', 0)
            variant['cost'] = variant.get('variant_cost', None)

            if variant['cost'] is not None:
                variant_costs.append(variant['cost'])

        # Calculate cost range and margins
        product_cost_price = 0
        if len(set(variant_costs)) > 1:
            product['cost_range'] = f"{min(variant_costs)}-{max(variant_costs)}"
            product_cost_price = min(variant_costs)
        elif variant_costs:
            product['cost_range'] = str(variant_costs[0])
            product_cost_price = min(variant_costs)
        else:
            product['cost_range'] = None

        if product_cost_price > 0 and product['default_price'] > 0:
            product['default_price_margin'] = round(((product['default_price'] - product_cost_price) / product_cost_price) * 100, 2)
        else:
            product['default_price_margin'] = 0

        # Get default price sales percentage
        default_price_sales = product_data_map.get(
            1,
            {"margin_percentage": 0.0, "sales_percentage": 0.0}
        )
        product['default_price_sales'] = default_price_sales["sales_percentage"]

        # Set the flag based on variants length and variant_name condition
        show_parent_product = not(len(product['variants']) == 1 and product['variants'][0]['variant_name'] == "Parent Product")

        # Build simplified product for multi-store
        processed_product = {
            "parent_product_id": product['parent_product_id'],
            "parent_product_name": product['parent_product_name'],
            "parent_product_sku": product['parent_product_sku'],
            "default_price": product['default_price'],
            "default_price_margin": product['default_price_margin'],
            "default_price_sales": product['default_price_sales'],
            "cost_range": product.get('cost_range', None),
            "cost_margin": product.get('cost_margin', None),
            "minimum_cost": str(product_cost_price),
            "show_parent_product": show_parent_product,
            "variants": product['variants']
        }

        # Add monthly data if role allows
        if role_id != "67fd12676af694b36923ce09":
            for i in range(1, 8):
                processed_product[f"month_{i}"] = product.get(f"month_{i}", None)

        return processed_product

    except Exception as e:
        logger.error(f"Error processing product details for multi-store: {traceback.format_exc()}")
        raise e

def _create_multi_store_product_details_response(all_store_data, store_ids, unified_price_list_mapping, role_id, store_names):
    """
    Create multi-store response for product details with separate objects for each store.
    """
    try:
        # Find the first valid product to get common data
        common_product_data = None
        for store_id in store_ids:
            if all_store_data[store_id]['product']:
                common_product_data = all_store_data[store_id]['product']
                break

        if not common_product_data:
            month_names, _ = get_month_array_for_meta(6)
            return {"data": [], "meta": {"price_lists": unified_price_list_mapping, "months": month_names}}

        # Create store-specific objects
        stores_data = []
        for store_id in store_ids:
            store_data = all_store_data[store_id]
            if store_data['product']:
                store_suffix = _get_store_suffix(store_id)
                store_object = {
                    'store_id': store_id,
                    'store_name': f"Store {store_suffix.title()}",
                    'default_price': store_data['product']['default_price'],
                    'default_price_margin': store_data['product']['default_price_margin'],
                    'default_price_sales': store_data['product']['default_price_sales'],
                    'cost_range': store_data['product']['cost_range'],
                    'cost_margin': store_data['product'].get('cost_margin'),
                    'minimum_cost': store_data['product']['minimum_cost'],
                    'variants': store_data['product']['variants']
                }

                # Add monthly data if role allows
                if role_id != "67fd12676af694b36923ce09":
                    for i in range(1, 8):
                        month_field = f'month_{i}'
                        store_object[month_field] = store_data['product'].get(month_field)

                stores_data.append(store_object)

        # Build final response
        final_product = {
            # Common fields at parent level
            'parent_product_id': common_product_data['parent_product_id'],
            'parent_product_name': common_product_data['parent_product_name'],
            'parent_product_sku': common_product_data['parent_product_sku'],
            'show_parent_product': common_product_data['show_parent_product'],

            # Store-specific objects
            'stores': stores_data
        }

        month_names, _ = get_month_array_for_meta(6)
        data = {
            "data": [final_product],
            "meta": {
                "price_lists": unified_price_list_mapping,
                "months": month_names if role_id != "67fd12676af694b36923ce09" else {},
                "stores": {
                    store_id: {
                        'id': store_id,
                        'name': store_names[store_id],
                        'suffix': _get_store_suffix(store_id)
                    } for store_id in store_ids
                }
            }
        }

        return data

    except Exception as e:
        logger.error(f"Error creating multi-store product details response: {traceback.format_exc()}")
        raise e


[
    {
        "productSku": "SKU-1026314599",
        "defaultPrice_63da3e98b702e324567f76f9": 12,
        "defaultPrice_661239751b9ce4bd7f85237c": null,
        "1_63da3e98b702e324567f76f9": null,
        "1_661239751b9ce4bd7f85237c": null,
        "2_63da3e98b702e324567f76f9": null,
        "2_661239751b9ce4bd7f85237c": null,
        "3_63da3e98b702e324567f76f9": null,
        "3_661239751b9ce4bd7f85237c": null,
        "4_63da3e98b702e324567f76f9": null,
        "4_661239751b9ce4bd7f85237c": null,
        "5_63da3e98b702e324567f76f9": null,
        "5_661239751b9ce4bd7f85237c": null,
        "6_63da3e98b702e324567f76f9": null,
        "6_661239751b9ce4bd7f85237c": null,
        "7_63da3e98b702e324567f76f9": null,
        "7_661239751b9ce4bd7f85237c": null,
        "8_63da3e98b702e324567f76f9": null,
        "8_661239751b9ce4bd7f85237c": null
    },
    {
        "variantSku": "810051268034",
        "defaultPrice_63da3e98b702e324567f76f9": 1,
        "defaultPrice_661239751b9ce4bd7f85237c": 1,
        "1_63da3e98b702e324567f76f9": null,
        "1_661239751b9ce4bd7f85237c": null,
        "2_63da3e98b702e324567f76f9": null,
        "2_661239751b9ce4bd7f85237c": null,
        "3_63da3e98b702e324567f76f9": null,
        "3_661239751b9ce4bd7f85237c": null,
        "4_63da3e98b702e324567f76f9": null,
        "4_661239751b9ce4bd7f85237c": null,
        "5_63da3e98b702e324567f76f9": null,
        "5_661239751b9ce4bd7f85237c": null,
        "6_63da3e98b702e324567f76f9": null,
        "6_661239751b9ce4bd7f85237c": null,
        "7_63da3e98b702e324567f76f9": null,
        "7_661239751b9ce4bd7f85237c": null,
        "8_63da3e98b702e324567f76f9": null,
        "8_661239751b9ce4bd7f85237c": null
    },
    {
        "variantSku": "810132916984",
        "defaultPrice_63da3e98b702e324567f76f9": 1,
        "defaultPrice_661239751b9ce4bd7f85237c": 1,
        "1_63da3e98b702e324567f76f9": null,
        "1_661239751b9ce4bd7f85237c": null,
        "2_63da3e98b702e324567f76f9": null,
        "2_661239751b9ce4bd7f85237c": null,
        "3_63da3e98b702e324567f76f9": null,
        "3_661239751b9ce4bd7f85237c": null,
        "4_63da3e98b702e324567f76f9": null,
        "4_661239751b9ce4bd7f85237c": null,
        "5_63da3e98b702e324567f76f9": null,
        "5_661239751b9ce4bd7f85237c": null,
        "6_63da3e98b702e324567f76f9": null,
        "6_661239751b9ce4bd7f85237c": null,
        "7_63da3e98b702e324567f76f9": null,
        "7_661239751b9ce4bd7f85237c": null,
        "8_63da3e98b702e324567f76f9": null,
        "8_661239751b9ce4bd7f85237c": null
    },
    {
        "variantSku": "810132917011",
        "defaultPrice_63da3e98b702e324567f76f9": null,
        "defaultPrice_661239751b9ce4bd7f85237c": null,
        "1_63da3e98b702e324567f76f9": null,
        "1_661239751b9ce4bd7f85237c": null,
        "2_63da3e98b702e324567f76f9": null,
        "2_661239751b9ce4bd7f85237c": null,
        "3_63da3e98b702e324567f76f9": null,
        "3_661239751b9ce4bd7f85237c": null,
        "4_63da3e98b702e324567f76f9": null,
        "4_661239751b9ce4bd7f85237c": null,
        "5_63da3e98b702e324567f76f9": null,
        "5_661239751b9ce4bd7f85237c": null,
        "6_63da3e98b702e324567f76f9": null,
        "6_661239751b9ce4bd7f85237c": null,
        "7_63da3e98b702e324567f76f9": null,
        "7_661239751b9ce4bd7f85237c": null,
        "8_63da3e98b702e324567f76f9": null,
        "8_661239751b9ce4bd7f85237c": null
    },
    {
        "variantSku": "810051268041",
        "defaultPrice_63da3e98b702e324567f76f9": null,
        "defaultPrice_661239751b9ce4bd7f85237c": null,
        "1_63da3e98b702e324567f76f9": null,
        "1_661239751b9ce4bd7f85237c": null,
        "2_63da3e98b702e324567f76f9": null,
        "2_661239751b9ce4bd7f85237c": null,
        "3_63da3e98b702e324567f76f9": null,
        "3_661239751b9ce4bd7f85237c": null,
        "4_63da3e98b702e324567f76f9": null,
        "4_661239751b9ce4bd7f85237c": null,
        "5_63da3e98b702e324567f76f9": null,
        "5_661239751b9ce4bd7f85237c": null,
        "6_63da3e98b702e324567f76f9": null,
        "6_661239751b9ce4bd7f85237c": null,
        "7_63da3e98b702e324567f76f9": null,
        "7_661239751b9ce4bd7f85237c": null,
        "8_63da3e98b702e324567f76f9": null,
        "8_661239751b9ce4bd7f85237c": null
    }]