from sqlalchemy import text
import pg_db
from mongo_db import user_db
import mongo_db
import traceback
import logging
from utils import common
from datetime import datetime
from utils.common import calculatePaginationData, convert_to_timestamp

logger = logging.getLogger()

def _fetch_members_dropdown(conn, project_id, search_value=None, sort_array=[]):
    # Step 1: Build the query to select global members
    query = f"""
        SELECT pa.id, pa.username, pa.is_owner, pa.created_at,
               (SELECT COUNT(*)
                FROM {pg_db.project_access} sub_pa
                WHERE sub_pa.username = pa.username
                AND sub_pa.project_id IS NOT NULL) as usage_count, pa.status
        FROM {pg_db.project_access} pa
        WHERE pa.project_id IS NULL
        AND NOT EXISTS (
            SELECT 1
            FROM {pg_db.project_access} sub_pa2
            WHERE sub_pa2.project_id = :project_id
            AND sub_pa2.username = pa.username
            AND sub_pa2.status = 'active'
        )
        AND pa.status = 'active'
    """
    
    # Step 2: Add sorting if provided
    if len(sort_array):
        sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
        if sort_array[0] in ["username"]:                
            query += f" ORDER BY pa.{sort_array[0]} {sort_direction}"                
        elif sort_array[0] in ["usage_count"]: 
            query += f" ORDER BY usage_count {sort_direction}"
    
    # Step 3: Execute the query with the project_id parameter
    query = text(query).params(project_id=project_id)
    result = conn.execute(query)

    # Step 4: Process the result
    member_info = []
    for row in result:
        member_id = row[0]
        username = row[1]
        is_owner = row[2]
        # created_at = row[3].strftime("%Y-%m-%d %H:%M:%S") if isinstance(row[3], datetime) else str(row[3])
        created_at = convert_to_timestamp(row[3])
        usage_count = row[4]

        user_data = user_db.fetch_user_by_username(username)
        if user_data:
            role = user_data.get('role', '')
            name = user_data.get('name', '')
            username = user_data.get('username', '')
            status = user_data.get('status', '')

            # Filter based on search_value
            if search_value and search_value.lower() not in name.lower():
                continue

            # Append the member details
            member_info.append({
                'member_id': member_id,
                'role': role,
                'name': name,
                'username': username,
                'created_at': created_at,
                'usage_count': usage_count,
                'is_owner': is_owner,
                'status': status
            })
    member_info = sorted(member_info, key=lambda x: x["name"].lower())

    return member_info



def get_members_dropdown(project_id, search_value, sort_array):
    response = {
        "status" :400        
    }
    conn = pg_db.get_connection()
    try:
        data = _fetch_members_dropdown(conn, project_id, search_value, sort_array)
        
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['data'] = []
            response['status'] = 200
    finally:
        if conn:
            conn.close()
    return response


def get_global_members(page, limit, search_value=None, sort_array=[]):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        # Fetch all records to allow search and sort on all data
        query = f"""SELECT pa.id, pa.username, pa.is_owner, pa.created_at,
                       (SELECT COUNT(*)
                        FROM {pg_db.project_access} sub_pa
                        WHERE sub_pa.username = pa.username
                        AND sub_pa.project_id IS NOT NULL AND sub_pa.status = 'active') as usage_count, pa.status
                FROM {pg_db.project_access} pa
                WHERE pa.project_id IS NULL
                AND status = 'active'
            """

        result = conn.execute(text(query))
        all_members = []

        # Fetch user data and filter by name
        for row in result:
            member_id = row[0]
            username = row[1]
            is_owner = row[2]
            # created_at = row[3].strftime("%Y-%m-%d %H:%M:%S") if isinstance(row[3], datetime) else str(row[3])
            created_at = convert_to_timestamp(row[3])
            usage_count = row[4]

            user_data = user_db.fetch_user_by_username(username)
            if user_data:
                name = user_data.get('name', '')

                # Apply search filter on 'name'
                if search_value.lower() in name.lower() or search_value.lower() in username.lower():
                    all_members.append({
                        'member_id': member_id,
                        'role': user_data.get('role', ''),
                        'name': name,
                        'username': username,
                        'created_at': created_at,
                        'usage_count': usage_count,
                        'is_owner': is_owner,
                        'status': user_data.get('status', '')
                    })
        # Apply sorting based on the 'name' field
        if len(sort_array) > 0:
            sort_field = sort_array[0]
            sort_direction = sort_array[1]

            if sort_field == "name":
                all_members.sort(key=lambda x: x['name'].lower(), reverse=(sort_direction == '-1'))
            elif sort_field == "usage_count":
                all_members.sort(key=lambda x: x['usage_count'], reverse=(sort_direction == '-1'))
            elif sort_field == "created_at":
                all_members.sort(key=lambda x: x['created_at'], reverse=(sort_direction == '-1'))
            elif sort_field == "status":
                all_members.sort(key=lambda x: x['status'], reverse=(sort_direction == '-1'))
        
        # Pagination response
        if page is not None and limit is not None:
            offset = (page - 1) * limit
            paginated_members = all_members[offset:offset + limit]
            total_records = len(all_members)
            pagination_data = calculatePaginationData(paginated_members, page, limit, total_records)
        else:
            # For no pagination, just return all members
            pagination_data = {
                "members": all_members
            }
        
        if pagination_data:
            response['data'] = pagination_data
            response['status'] = 200
        else:
            response['data'] = []
            response['status'] = 200
    finally:
        if conn:
            conn.close()

    return response



def _insert_new_global_member(conn, username):
    try:
        query = text(
            f"""SELECT * FROM {pg_db.project_access} WHERE username = :username AND project_id IS NULL AND status = 'deleted';
            """
        )
        query = query.params(username=username)
        result = conn.execute(query)
        if result.rowcount > 0:
            query = text(
                f"""UPDATE {pg_db.project_access} SET status = 'active', created_at = now() WHERE username = :username AND status = 'deleted';
                """
            )
            query = query.params(username=username)
            conn.execute(query)
            return True
        query = text(
            f"""INSERT INTO {pg_db.project_access} (username, is_owner)
                VALUES (:username, :is_owner);
            """
        )
        query = query.params(username=username, is_owner=False)
        conn.execute(query)
        conn.commit()

        return True
    except Exception as e:
        logger.error(traceback.format_exc())
        return False


def add_new_global_member(usernames):
    response = {"status": 400}
    conn = pg_db.get_connection()
    try:
        success_count = 0
        for username in usernames:
            data = _insert_new_global_member(conn, username)
            if data:
                success_count += 1

        if success_count == len(usernames):
            response['status'] = 200
            response['message'] = f"All {len(usernames)} members inserted successfully."
        elif success_count > 0:
            response['status'] = 200
            response['message'] = f"Only {success_count} out of {len(usernames)} members inserted successfully. Because other users already exists"
        else:
            response['status'] = 409
            response['message'] = "Data insertion failed for all members because users already exists"
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response


def _remove_global_members(conn, username):    
    query = text(
        f"""UPDATE {pg_db.project_access} 
            SET status = 'deleted'
            WHERE username = :username;
        """
    )
    query = query.params(username=username)
    result = conn.execute(query)
    return result.rowcount > 0



def delete_global_member(username):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:
        data = _remove_global_members(conn, username)
        if data:
            response['status'] = 200
            response['message'] = "Data deleted successfully."
        else:
            response['status'] = 400
            response['message'] = "Data deletion failed, Due to this member is a owner of any project"
    finally:
        if conn:
            conn.commit()
            conn.close()

    return response

def get_member_projectlist(username):
    response = {
        "status": 400,
        "message": "Data not found"
    }
    conn = pg_db.get_connection()
    try:
        # Fetch all records to allow search and sort on all data
        query = text(f"""SELECT ap.name, ap.is_archived, pa.username, pa.project_id FROM {pg_db.projects} ap
                    JOIN {pg_db.project_access} pa ON ap.id = pa.project_id
                    WHERE pa.username = :username AND pa.project_id IS NOT NULL
                    AND pa.status = 'active'
            """)
        query = query.params(username=username)
        result = conn.execute(query).fetchall()
        if result:
            all_projects = []

            # Fetch user data and filter by name
            for row in result:
                all_projects.append({
                    'project_id': row[3],
                    'project_name': row[0],
                    'status': 'active' if not row[1] else 'archived'
                })

                response['data'] = all_projects
                response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = [] 
    finally:
        if conn:
            conn.close()

    return response