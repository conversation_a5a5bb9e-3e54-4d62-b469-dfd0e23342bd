from iam import TokenKeys, auth_service
from mongo_db import user_db
from new_mongodb import RoleKeys, process_documents, tenant_db, store_admin_db, get_admin_db_client_for_store_id
from new_mongodb import StoreAdminDBCollections, UserKeys
import bcrypt
import copy
import datetime
import mongo_db
from new_utils import get_paginated_records_updated, calculate_pagination
from bson import ObjectId
from exceptions.common_exceptions import ResourceAlreadyExistException, InvalidInputException, ResourceNotFoundException
from utils import redis_util
import task
from new_mongodb import AdminAppNotification

def get_user_admin_info(tenant_id, username):
    return tenant_db.fetch_store_admin_by_username(username=username)

def get_store_user_role(store_id, username):
    role = store_admin_db.fetch_user_role(store_id=store_id, username=username)
    if role:
        role['id'] = str(role['_id'])
        del role['_id']
    return role

def generate_yoyo(pwd):
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(pwd.encode('utf-8'), salt)
    return {
        UserKeys.YOYO_KEY: salt,
        UserKeys.YOYO_VALUE: hashed
    }

def covert_to_dto(model):
    del model[UserKeys.YOYO]
    dto = mongo_db.process_data(model)
    return dto

def upsert_tenant_user(store, user):
    tenant_user = tenant_db.fetch_user_by_username(user[UserKeys.USERNAME])        
    stores = {}
    default_store = store['id']
    store_tenant_id = store.get(UserKeys.TENANT_ID, None)
    tenant_id = store_tenant_id

    if tenant_user:
        stores = tenant_user.get(UserKeys.STORES, {})
        default_store = tenant_user.get(UserKeys.DEFAULT_STORE_ID, default_store)
        tenant_id = tenant_user.get(UserKeys.TENANT_ID, tenant_id)
    else:
        tenant_user = copy.deepcopy(user)
    
    tenant_user[UserKeys.NAME] = user[UserKeys.NAME]
    tenant_user[UserKeys.STATUS] = "active"
    tenant_user[UserKeys.TYPE] = user.get("type", "admin_app_default_user")
    tenant_user[UserKeys.ROLE] = user.get("role", None)
    tenant_user[UserKeys.ROLE_ID] = user.get("role_id", None)
    
    stores[str(store['id'])] = {
        UserKeys.TENANT_ID: store_tenant_id,
        UserKeys.ROLE: user.get('role', None),
        UserKeys.ROLE_ID: user.get('role_id', None),
        UserKeys.STATUS: user[UserKeys.STATUS]
    }

    tenant_user[UserKeys.STORES] = stores
    tenant_user[UserKeys.DEFAULT_STORE_ID] = default_store
    tenant_user[UserKeys.TENANT_ID] = tenant_id
    tenant_db.create_user(tenant_user)

def create_user(store, user, email=''):
    result = None
    store_id = store['id']

    name = user["name"]
    username = user["username"].lower()
    password = user["password"]
    role_id = user["role_id"]
    status = user.get("status", "active")
    type = user.get("type", "admin_app_default_user")

    if name and name.strip() != "" and username and username.strip() != "" and password and password.strip() != "" \
        and role_id and role_id.strip() != "":

        user_model = {
            UserKeys.NAME: name,
            UserKeys.USERNAME: username,
            UserKeys.ROLE_ID: role_id,
            UserKeys.STATUS: status,
            UserKeys.TYPE: type
        }

        existing_user = store_admin_db.fetch_user_by_username(store_id=store_id, username=username)

        if existing_user and existing_user['status'] in ['active', 'inactive']: 
            raise ResourceAlreadyExistException("User already exists.")
        elif existing_user and existing_user['status'] == 'deleted':
            query = {
                UserKeys.ID: existing_user['_id']
            }
            role = store_admin_db.fetch_role_by_id(store_id, role_id)
            if role and role['_id']:
                user_model[UserKeys.ROLE] = role['role']
                user_model[UserKeys.CREATED_AT] = int(datetime.datetime.now(datetime.timezone.utc).timestamp())
                user_model[UserKeys.YOYO] = generate_yoyo(password)
                store_admin_db.update_user(store_id=store_id, query=query, user=user_model)
                upsert_tenant_user(store, user_model)
                result = covert_to_dto(user_model)
                result["id"] = str(existing_user['_id'])
                return {"status": 200, "data": result}
            else:
                raise ResourceNotFoundException("Role doesn't exist.")
            
        role = store_admin_db.fetch_role_by_id(store_id, role_id)
        if role and role['_id']:
            store_admin_db.update_user_role_count(store_id, str(role['_id']), 1)
            user_model[UserKeys.ROLE] = role['role']
            user_model[UserKeys.CREATED_AT] = int(datetime.datetime.now(datetime.timezone.utc).timestamp())
            user_model[UserKeys.YOYO] = generate_yoyo(password)
            user_id = store_admin_db.create_user(store_id=store_id, user=user_model)
            upsert_tenant_user(store, user_model)
            result = covert_to_dto(user_model)
            result["id"] = str(user_id)
            if email != '':
                user = store_admin_db.fetch_user_by_username(store_id, email)
                if user:
                    user_name = user['name']
                    task.send_task(task.GENERATE_ADMIN_APP_NOTIFICATION_TASK, args=(store_id, AdminAppNotification.USER_CREATED, str(user_id), {"name":user_name}))

        else:
            raise ResourceNotFoundException("Role doesn't exist.")
    else:
        raise InvalidInputException("Provide valid input.")
        
    return {"status": 200, "data": result}


def remove_store_access_in_tenant_db(store_id, username):
    tenant_user = tenant_db.fetch_user_by_username(username)
    if tenant_user:
        stores = tenant_user.get(UserKeys.STORES, {})
        stores.pop(store_id, None)
        default_store_id = tenant_user.get(UserKeys.DEFAULT_STORE_ID, None)
        tenant_id = tenant_user.get(UserKeys.TENANT_ID, None)
        if store_id == default_store_id:
            default_store_id = None
            tenant_id = None
            if len(stores) > 0:
                default_store_id = next(iter(stores.keys()))
                if default_store_id:
                    tenant_id = stores[default_store_id].get(UserKeys.TENANT_ID, None)
        query = {
            UserKeys.ID: ObjectId(str(tenant_user[UserKeys.ID]))
        }
        update = {
            UserKeys.UPDATED_AT: int(datetime.datetime.now(datetime.timezone.utc).timestamp()),
            UserKeys.DEFAULT_STORE_ID: default_store_id,
            UserKeys.STORES: stores,
            UserKeys.TENANT_ID: tenant_id
        }
        tenant_db.update_user(query=query, user=update)

def add_store_access_in_tenant_db(store, username, role_id, role_name):
    store_id = str(store['id'])
    tenant_user = tenant_db.fetch_user_by_username(username)  
    if tenant_user:
        default_store_id = store.get(UserKeys.DEFAULT_STORE_ID, None)
        store_tenant_id = store.get(UserKeys.TENANT_ID, None)
        stores = tenant_user.get(UserKeys.STORES, {})  
        stores[store_id] = {
            UserKeys.TENANT_ID: store_tenant_id,
            UserKeys.ROLE: role_name,
            UserKeys.ROLE_ID: role_id
        }
        update = {
            UserKeys.UPDATED_AT: int(datetime.datetime.now(datetime.timezone.utc).timestamp()),
            UserKeys.STORES: stores
        }
        if not default_store_id:
            update[UserKeys.DEFAULT_STORE_ID] = store_id
        query = {
            UserKeys.ID: ObjectId(str(tenant_user[UserKeys.ID]))
        }
        tenant_db.update_user(query=query, user=update)

def update_user(store, user_id, user):
    result = None
    store_id = store['id']
    name = user.get("name", None)
    status = user.get("status", None)
    username = user.get("username", None)
    role_id = user.get("role_id", None)

    if not user_id:
        raise InvalidInputException("Provide valid user id.")
    
    query = {
        UserKeys.ID: ObjectId(user_id)
    }
   
    existing_user = store_admin_db.fetch_one_user(store_id=store_id, query=query)
    user_model = {}
    if not existing_user:
        raise ResourceNotFoundException("User doesn't exist.")
    
    if username and username.strip() != "" and username != existing_user[UserKeys.USERNAME]:
        user_with_same_username = store_admin_db.fetch_user_by_username(store_id=store_id, username=username)
        if user_with_same_username:
            raise ResourceAlreadyExistException("username: User with the given username is already exist.")

        user_model[UserKeys.USERNAME] = username.strip()

    if status and status.strip() != "":
        user_model[UserKeys.STATUS] = status.strip()

    if name and name.strip() != "":
        user_model[UserKeys.NAME] = name.strip()

    if role_id and role_id.strip() != "":
        role_id = role_id.strip()
        role = store_admin_db.fetch_role_by_id(store_id, role_id)
        if role and role['_id']:
            user_model[UserKeys.ROLE] = role[RoleKeys.ROLE]
            user_model[UserKeys.ROLE_ID] = role_id
        else:
            raise InvalidInputException("role_id: User role doesn't exist")

    if len(user_model) > 0:
        store_admin_db.update_user(store_id=store_id, query=query, user=user_model)
        user = store_admin_db.fetch_one_user(store_id=store_id, query=query)
        upsert_tenant_user(store=store, user=user)
        result = covert_to_dto(user)
    else:
        raise InvalidInputException("Provide valid user data to be updated.")

    return {"status": 200, "message": "User Updated successfully", "data": result}

def get_user_by_id(store, user_id):
    query = {
        UserKeys.ID: ObjectId(str(user_id))
    }
    fields = {
        UserKeys.ID: 1,
        UserKeys.USERNAME: 1,
        UserKeys.NAME: 1,
        UserKeys.ROLE_ID: 1,
        UserKeys.ROLE: 1,
        UserKeys.STATUS:1,
        UserKeys.LAST_LOGIN: 1,
        UserKeys.UPDATED_AT: 1,
        UserKeys.TYPE: 1    
    }
    res = store_admin_db.fetch_users(store['id'], query, fields)
    if res:
        res = process_documents(res)
    if len(res) > 0:
        res = res[0]
    return res

def delete_user(store, user_id):
    store_id = store['id']
    if user_id and user_id.strip() != "":
        query = {
            UserKeys.ID: ObjectId(str(user_id))
        }
        user = store_admin_db.fetch_one_user(store_id, query)
        if user:
            delete_result = store_admin_db.delete_user_by_user_id(store_id, user_id)
            store_admin_db.update_user_role_count(store_id, user[UserKeys.ROLE_ID], -1)
            remove_store_access_in_tenant_db(store_id, username=user[UserKeys.USERNAME])
            if delete_result.modified_count > 0:
                return {"message": "User deleted successfully."}
        else:
            raise ResourceNotFoundException("User doesn't exist.")
    else:
        raise InvalidInputException("Provide valid user id.")

def change_password(username, password):
    result = False    
    user = tenant_db.get_user_id(username)
    if user:
        yoyo = generate_yoyo(password)
        query = {
            UserKeys.ID: ObjectId(str(user[UserKeys.ID]))
        }
        update = {
            UserKeys.YOYO: yoyo,
            UserKeys.UPDATED_AT: int(datetime.datetime.now(datetime.timezone.utc).timestamp())
        }
        tenant_db.update_user(query=query, user=update)
        result = True
    return result

def get_all_users(store, payload):
    fields = {
        UserKeys.USERNAME: 1,
        UserKeys.NAME: 1,
        UserKeys.CREATED_AT: 1,
        UserKeys.UPDATED_AT: 1,
        UserKeys.LAST_LOGIN: 1,
        UserKeys.STATUS: 1,
        UserKeys.ROLE: 1,
        UserKeys.TYPE: 1
    }
    admin_db = get_admin_db_client_for_store_id(store['id'])
    payload['filterBy'] = [UserKeys.NAME, UserKeys.ROLE, UserKeys.USERNAME]
    user_type = payload.get('type', '')
    additional_query = ''
    if user_type == 'supplier':
        user_type = 'admin_app_supplier_user'
        additional_query = {'type': user_type}
    elif user_type == 'admin':
        user_type = 'admin_app_default_user'
        additional_query = {'type': user_type}
    elif user_type == '':
        payload.pop('type', None)
    
    users, total_data_length, page, limit = get_paginated_records_updated(db_client=admin_db, collection_name=StoreAdminDBCollections.USERS_COLLECTION,\
                                            payload=payload, fields=fields, additional_query=additional_query)

    # include pagination data in response ...
    data = calculate_pagination(users, page, limit, total_data_length)
    return data

def get_all_users_for_product_rule(store):
    fields = {
        UserKeys.USERNAME: 1        
    }
    data = store_admin_db.fetch_store_users(store['id'], query={}, project=fields)
    return data

def store_user_login(store, username):
    response = {
        "status": 401,
    }
    if username:
        user = store_admin_db.fetch_user_by_username(store_id=store['id'], username=username)
        if user:
            data = {}
            token = auth_service._generate_access_token(store['tenant_id'], user['username'])
            redis_util.add_access_token(username=user['username'], 
                                    client_id=token[TokenKeys.CLIENT_ID], 
                                    access_token=token[TokenKeys.ACCESS_TOKEN], 
                                    expires_at=token[TokenKeys.EXPIRY])
            response['status'] = 200
            data['client_id'] = token[TokenKeys.CLIENT_ID]
            data['token'] = token[TokenKeys.ACCESS_TOKEN]
            data['exp'] = token[TokenKeys.EXPIRY]
            data['tenant_id'] = store['tenant_id']
            response['data'] = data
        else:
            response['message'] = "User doesn't exist."
            response['status'] = 404
    else:
        response['status'] = 409    
        response['message'] = "Please provide valid user id."
        raise ResourceNotFoundException("Please provide valid user id.")
        
    return response