import time
from mongo_db import store_db
from utils import bc
from new_utils import store_util, cache_util

# 30 days
GRAPHQL_TOKEN_EXPIRY = 30*24*3600

def get_active_stores():    
    return store_util.get_active_stores()

def get_store_by_storehash(store_hash):
    return store_util.get_store_by_store_hash(store_hash=store_hash)

def get_store_by_id(store_id):
    return store_util.get_store_by_id(store_id)

def get_email_template(store_id, template_id):
    template = store_db.fetch_email_template(store_id, template_id)
    return template

def get_graphql_token(store):
    token = cache_util.get_graphql_token(store['id'])
    if not token:
        token = reset_graphql_token(store)
    return token

def reset_graphql_token(store):
    expiry_time = int(time.time() + GRAPHQL_TOKEN_EXPIRY)
    bc_api = get_bc_api_creds(store)
    token = bc.get_bc_graphql_token(bc_api, expiry_time, store['store_url'])
    cache_util.set_graphql_token(store['id'], token, expiry_time)
    return token

def get_bc_api_creds(store):
    api_creds = {}
    if store:
        bc_info = store.get("bc_config", None)
        api = None
        if bc_info:
            api = bc_info.get("api", None)  
            api_creds["store_hash"] = bc_info.get("store_hash", None)
            api_creds["channel_id"] = bc_info.get("channel_id", 1)
            api_creds["store_url"] = bc_info.get("store_url", None)
            if api:
                api_creds["client_id"] = api.get("client_id", None)
                api_creds["access_token"] = api.get("access_token", None)
                api_creds["secret"] = api.get("secret", None)
        else:
            bc_info = store.get("api", None)
            if bc_info:
                api = bc_info.get("bcV3Api", None) 
                api_creds["store_hash"] = bc_info.get("storeHash", None)
                api_creds["channel_id"] = bc_info.get("channelId", 1)
                api_creds["store_url"] = bc_info.get("bcStoreUrl", None)
                if api:
                    api_creds["client_id"] = api.get("clientId", None)
                    api_creds["access_token"] = api.get("accessToken", None)
                    api_creds["secret"] = api.get("secret", None)

    return api_creds

def get_bc_db(store):
    return store_util.get_store_mongodb_name(store)

def get_store_db(store):
    return store_util.get_store_mongodb_name(store)

def get_cms_db(store):
    return store_util.get_store_mongodb_name(store)

def get_cdn_base_url(store):
    base_url = None
    cdn = store.get("cdn", None)
    if cdn:
        base_url = cdn.get("base_url", None)
    
    if not base_url:
        base_url = store.get("image_cdn_baseurl", None)

    return base_url

def get_netlify_api_info(store):
    return store.get("netlify_config", None)
