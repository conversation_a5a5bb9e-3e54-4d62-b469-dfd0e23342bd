import re
from datetime import datetime, timezone
from bson import ObjectId
from sqlalchemy import text
from mongo_db import processList, process_data
from new_mongodb import StoreDBCollections, StoreAdminDBCollections, get_store_db_client_for_store_id, get_admin_db_client_for_store_id
import new_pgdb
import new_utils
import traceback
import logging

logger = logging.getLogger()


def get_all_badges(store, page, limit, sort_array, search):
    try:
        db_client = get_store_db_client_for_store_id(store['id'])
        badge_collection = db_client[StoreDBCollections.SMART_BADGES]

        # Apply search filter on badge_name and tag_lable
        filter_query = {}
        if search:
            regex_query = {"$regex": search, "$options": "i"}
            filter_query["$or"] = [
                {"badge_name": regex_query},
                {"tag_label": regex_query}
            ]
        
        sort_query = []
        if len(sort_array) == 2:
            field, direction = sort_array
            if field in ["badge_name", "tag_label", "is_active", "total_product_count", "created_at"] and direction in ['1', '-1']:
                sort_query.append((field, int(direction)))

        total_count = badge_collection.count_documents(filter_query)

        cursor = badge_collection.find(
            filter_query,
            {
                "_id": 1,
                "badge_name": 1,
                "tag_label": 1,
                "total_product_count": 1,
                "is_active": 1,
                "created_at": 1
            }
        )
        if sort_query:
            cursor = cursor.sort(sort_query)
        cursor = cursor.skip((page - 1) * limit).limit(limit)

        paginated_badges = list(cursor)
        processed_badges = processList(paginated_badges)
        data = new_utils.calculate_pagination(processed_badges, page, limit, total_count)

        return {
            "status": 200,
            "data": data
        }
    except Exception as e:
        logger.error(traceback.format_exc())
        return {
            "status": 500,
            "message": f"Failed to fetch smart badges: {str(e)}"
        }

def create_badge(store, badge_data, username):
    try:
        db_client = get_store_db_client_for_store_id(store['id'])
        badge_collection = db_client[StoreDBCollections.SMART_BADGES]
        products_collection = db_client[StoreDBCollections.PRODUCTS]
        brands_collection = db_client[StoreDBCollections.BRANDS]
        admin_db_client = get_admin_db_client_for_store_id(store['id'])
        categories_collection = admin_db_client[StoreAdminDBCollections.CMS_COLLECTION]

        if not badge_data.get("badge_name") or not badge_data.get("tag_label"):
            return {"status": 400, "message": "Badge name and tag label are required."}

        category_ids = badge_data.get("category_ids", [])
        brand_ids = badge_data.get("brand_ids", [])
        individual_products_ids = badge_data.get("individual_products", [])

        # Create brand lookup: brand_id -> brand_name
        brand_lookup = {
            brand.get("id"): brand.get("name", "")
            for brand in brands_collection.find({}, {"id": 1, "name": 1})
        }

        # Create category lookup: category_id -> category_name
        category_lookup = {
            category.get("id"): category.get("name", "")
            for category in categories_collection.find({}, {"id": 1, "name": 1})
        }

        def format_product(product):
            return {
                "id": product.get("id"),
                "name": product.get("name"),
                "sku": product.get("sku"),
                "is_visible": product.get("is_visible"),
                "brand_id": product.get("brand_id"),
                "brand": brand_lookup.get(product.get("brand_id"), ""),
                "categories": product.get("categories"),
                "category_names": [
                    category_lookup.get(cat_id, "") for cat_id in product.get("categories", [])
                ]
            }

        # Fetch products by category_ids
        category_products = []
        if category_ids:
            category_cursor = products_collection.find({"categories": {"$in": category_ids}})
            category_products = [format_product(product) for product in category_cursor if "id" in product]

        # Fetch products by brand_ids
        brand_products = []
        if brand_ids:
            brand_cursor = products_collection.find({"brand_id": {"$in": brand_ids}})
            brand_products = [format_product(product) for product in brand_cursor if "id" in product]

        individual_products = []
        if individual_products_ids:
            individual_cursor = products_collection.find({"id": {"$in": individual_products_ids}})
            individual_products = [format_product(product) for product in individual_cursor if "id" in product]

        def unique_by_id(products):
            seen = set()
            unique_list = []
            for p in products:
                if p["id"] not in seen:
                    unique_list.append(p)
                    seen.add(p["id"])
            return unique_list

        # Create a combined list and remove duplicates using set
        combined_product = unique_by_id(category_products + brand_products + individual_products)

        # Calculate total count
        total_product_count = len(combined_product)

        badge_document = {
            "badge_name": badge_data.get("badge_name", ""),
            "tag_label": badge_data.get("tag_label", ""),
            "bg_color": badge_data.get("bg_color", "#FFFFFF"),
            "font_color": badge_data.get("font_color", "#000000"),
            "is_active": badge_data.get("is_active", True),
            "product_listing":badge_data.get("product_listing", True),
            "product_details":badge_data.get("product_details", True),
            "individual_products": individual_products_ids,
            "combined_product": combined_product,
            "total_product_count": total_product_count,
            "category_ids": category_ids,
            "brand_ids": brand_ids,
            "created_by": username,
            "created_at": int(datetime.utcnow().timestamp()),
            "modified_by": username,
            "modified_at": int(datetime.utcnow().timestamp())
        }

        inserted = badge_collection.insert_one(badge_document)

        return {
            "status": 200,
            "message": f"Smart badge created successfully."
        }

    except Exception as e:
        logger.error(traceback.format_exc())
        return {
            "status": 500,
            "message": f"Failed to create smart badge: {str(e)}"
        }
    
def delete_badges(store, ids):
    try:
        db_client = get_store_db_client_for_store_id(store['id'])
        badge_collection = db_client[StoreDBCollections.SMART_BADGES]

        object_ids = [ObjectId(id_str) for id_str in ids]

        delete_result = badge_collection.delete_many({"_id": {"$in": object_ids}})

        if delete_result.deleted_count == 0:
            return {"status": 404, "message": "No badges found with provided IDs."}

        return {"status": 200, "message": f"Deleted {delete_result.deleted_count} badge(s) successfully."}

    except Exception as e:
        logger.error(traceback.format_exc())
        return {
            "status": 500,
            "message": f"Failed to delete smart badges: {str(e)}"
        }
    
def get_badge(store, id):
    try:
        db_client = get_store_db_client_for_store_id(store['id'])
        badge_collection = db_client[StoreDBCollections.SMART_BADGES]
        object_id = ObjectId(id)

        badge = badge_collection.find_one({"_id": object_id})
        if badge:
            processed_badge = process_data(badge)
            return {
                "status": 200,
                "data": processed_badge
            }
        else:
            return {
                "status": 404,
                "message": "Badge not found"
            }
    except Exception as e:
        logger.error(traceback.format_exc())
        return {
            "status": 500,
            "message": f"Failed to fetch badge: {str(e)}"
        }

def update_badge(store, payload, username, id):
    response = {
        "status": 400
    }
    try:
        db_client = get_store_db_client_for_store_id(store['id'])
        badge_collection = db_client[StoreDBCollections.SMART_BADGES]
        products_collection = db_client[StoreDBCollections.PRODUCTS]
        brands_collection = db_client[StoreDBCollections.BRANDS]
        admin_db_client = get_admin_db_client_for_store_id(store['id'])
        categories_collection = admin_db_client[StoreAdminDBCollections.CMS_COLLECTION]
        object_id = ObjectId(id)

        existing_badge = badge_collection.find_one({"_id": object_id})
        if not existing_badge:
            response['status'] = 404
            response['message'] = "Badge not found."
            return response

        allowed_fields = [
            "badge_name", "tag_label", "bg_color", "font_color",
            "is_active", "product_listing", "product_details",
            "category_ids", "brand_ids", "individual_products"
        ]
        update_fields = {field: payload[field] for field in allowed_fields if field in payload}

        # Use existing values unless explicitly provided
        category_ids = payload.get("category_ids") if "category_ids" in payload else existing_badge.get("category_ids", [])
        brand_ids = payload.get("brand_ids") if "brand_ids" in payload else existing_badge.get("brand_ids", [])
        individual_products_ids = payload.get("individual_products") if "individual_products" in payload else existing_badge.get("individual_products", [])

        # Create brand lookup: brand_id -> brand_name
        brand_lookup = {
            brand.get("id"): brand.get("name", "")
            for brand in brands_collection.find({}, {"id": 1, "name": 1})
        }

        # Create category lookup: category_id -> category_name
        category_lookup = {
            category.get("id"): category.get("name", "")
            for category in categories_collection.find({}, {"id": 1, "name": 1})
        }

        def format_product(product):
            return {
                "id": product.get("id"),
                "name": product.get("name"),
                "sku": product.get("sku"),
                "is_visible": product.get("is_visible"),
                "brand_id": product.get("brand_id"),
                "brand": brand_lookup.get(product.get("brand_id"), ""),
                "categories": product.get("categories"),
                "category_names": [
                    category_lookup.get(cat_id, "") for cat_id in product.get("categories", [])
                ]
            }

        # Fetch products by category_ids
        category_products = []
        if category_ids:
            category_cursor = products_collection.find({"categories": {"$in": category_ids}})
            category_products = [format_product(product) for product in category_cursor if "id" in product]

        # Fetch products by brand_ids
        brand_products = []
        if brand_ids:
            brand_cursor = products_collection.find({"brand_id": {"$in": brand_ids}})
            brand_products = [format_product(product) for product in brand_cursor if "id" in product]

        # Fetch individual products
        individual_products = []
        if individual_products_ids:
            individual_cursor = products_collection.find({"id": {"$in": individual_products_ids}})
            individual_products = [format_product(product) for product in individual_cursor if "id" in product]

        def unique_by_id(products):
            seen = set()
            unique_list = []
            for p in products:
                if p["id"] not in seen:
                    unique_list.append(p)
                    seen.add(p["id"])
            return unique_list

        combined_product = unique_by_id(category_products + brand_products + individual_products)
        total_product_count = len(combined_product)

        update_fields["individual_products"] = individual_products_ids
        update_fields["combined_product"] = combined_product
        update_fields["total_product_count"] = total_product_count
        update_fields["category_ids"] = category_ids
        update_fields["brand_ids"] = brand_ids
        update_fields["modified_by"] = username
        update_fields["modified_at"] = int(datetime.utcnow().timestamp())

        update_result = badge_collection.update_one({"_id": object_id}, {"$set": update_fields})

        if update_result.modified_count > 0:
            response['status'] = 200
            response['message'] = "Badge updated successfully."
        else:
            response['status'] = 200
            response['message'] = "No changes applied to the badge."

    except Exception as e:
        logger.error(traceback.format_exc())
        response['status'] = 500
        response['message'] = f"Failed to update badge: {str(e)}"

    return response

def get_products_dropdown(store, search, page, limit, badge_id): 
    response = {
        "status": 400,
        "message": "No data found",
    }

    try:
        # Convert page and limit to integers early
        page = max(1, int(page)) if page else 1
        limit = max(1, int(limit)) if limit else 10
        
        db_client = get_store_db_client_for_store_id(store['id'])
        products_collection = db_client[StoreDBCollections.PRODUCTS]
        
        # Get individual products from badge if badge_id is provided
        individual_products = []
        if badge_id:
            try:
                badge_collection = db_client[StoreDBCollections.SMART_BADGES]
                badge_doc = badge_collection.find_one(
                    {"_id": ObjectId(badge_id)},
                    {"individual_products": 1}  # Only fetch the needed field
                )
                if badge_doc and "individual_products" in badge_doc:
                    individual_products = [
                        int(prod) for prod in badge_doc["individual_products"] 
                        if isinstance(prod, int)
                    ]
            except Exception as e:
                return {
                    "status": 400,
                    "message": f"Invalid badge_id: {str(e)}"
                }

        # Build search regex once if needed
        search_regex = {"$regex": search, "$options": "i"} if search else None
        
        # Prepare projection for both queries
        projection = {
            "_id": 0,
            "id": 1,
            "sku": 1,
            "name": 1,
            "is_visible": 1
        }
        
        # Process individual products with search if they exist
        individual_product_details = []
        if individual_products:
            individual_query = {"id": {"$in": individual_products}}
            
            if search_regex:
                individual_query = {
                    "$and": [
                        individual_query,
                        {"$or": [
                            {"name": search_regex},
                            {"sku": search_regex}
                        ]}
                    ]
                }
            
            # Use list comprehension for transformation
            individual_cursor = products_collection.find(individual_query, projection)
            individual_product_details = [{
                'product_id': p.get('id'),
                'sku': p.get('sku', ''),
                'product_name': p.get('name', ''),
                'is_visible': p.get('is_visible', False)
            } for p in individual_cursor]

        # Build paginated query
        paginated_query = {}
        
        if search_regex:
            paginated_query["$or"] = [
                {"name": search_regex},
                {"sku": search_regex}
            ]
        
        if individual_products:
            exclude_query = {"id": {"$nin": individual_products}}
            if paginated_query:
                paginated_query = {"$and": [paginated_query, exclude_query]}
            else:
                paginated_query = exclude_query

        # Get total count of paginated products for pagination
        total_paginated_records = products_collection.count_documents(paginated_query)
        total_individual_records = len(individual_product_details)
        total_records = total_paginated_records + total_individual_records
        
        # Calculate adjusted limit and skip for paginated products
        individual_count = len(individual_product_details)
        adjusted_limit = limit
        adjusted_skip = (page - 1) * limit
        
        # If we have individual products, adjust the pagination for regular products
        if individual_count > 0:
            # For first page, reduce limit by number of individual products
            if page == 1:
                adjusted_limit = max(0, limit - individual_count)
                individual_product_details = individual_product_details[:limit]
            else:
                # For subsequent pages, adjust the skip to account for individual products
                adjusted_skip = (page - 1) * limit - individual_count
                if adjusted_skip < 0:
                    # Handle overlap between individual products and second page
                    individual_start = limit * (page - 1)
                    if individual_start < individual_count:
                        individual_product_details = individual_product_details[individual_start:individual_count]
                        adjusted_limit = limit - len(individual_product_details)
                        adjusted_skip = 0
                    else:
                        individual_product_details = []
                        adjusted_skip = adjusted_skip + individual_count
                else:
                    individual_product_details = []
        
        # Get paginated products with adjusted limit and skip
        paginated_product_details = []
        if adjusted_limit > 0:
            paginated_cursor = products_collection.find(
                paginated_query, 
                projection
            ).sort("name", 1).skip(adjusted_skip).limit(adjusted_limit)
            
            paginated_product_details = [{
                'product_id': p.get('id'),
                'sku': p.get('sku', ''),
                'product_name': p.get('name', ''),
                'is_visible': p.get('is_visible', False)
            } for p in paginated_cursor]

        # Combine results
        combined_data = individual_product_details + paginated_product_details

        # Build response
        response['status'] = 200
        response['message'] = 'Data retrieved successfully'
        response['data'] = {
            'data': combined_data,
            'meta': {
                'current_page': page,
                'next_page': (page + 1 if (page * limit) < total_records else None),
                'total_count': total_records
            }
        }

    except Exception as e:
        logger.error(traceback.format_exc())
        response['status'] = 500
        response['message'] = f"Failed to retrieve product dropdown: {str(e)}"

    return response

def get_brands_dropdown(store, search, page, limit, badge_id):
    try:
        db_client = get_store_db_client_for_store_id(store['id'])
        brand_collection = db_client[StoreDBCollections.BRANDS]
        smart_badge_collection = db_client[StoreDBCollections.SMART_BADGES]

        # Search filter on "name"
        filter_query = {}
        brand_ids_to_include = None

        # If badge_id is provided, filter brands based on it
        if badge_id:
            try:
                badge_object = smart_badge_collection.find_one({"_id": ObjectId(badge_id)})
                if badge_object and "combined_product" in badge_object:
                    brand_ids_to_include = list({
                        product.get("brand_id") for product in badge_object["combined_product"]
                        if product.get("brand_id") is not None
                    })

                    if brand_ids_to_include:
                        filter_query["id"] = {"$in": brand_ids_to_include}
                    else:
                        # No brands found in badge, return empty result early
                        return {
                            "status": 200,
                            "data": {
                                "data": [],
                                "meta": {
                                    "current_page": page,
                                    "next_page": None,
                                    "total_count": 0
                                }
                            }
                        }
            except Exception as e:
                logger.error(traceback.format_exc())
                return {
                    "status": 400,
                    "message": f"Invalid badge_id or badge not found: {str(e)}"
                }

        if search:
            search_filter = {
                "name": {"$regex": search, "$options": "i"}
            }
            if "id" in filter_query:
                filter_query = {
                    "$and": [
                        {"id": {"$in": brand_ids_to_include}},
                        search_filter
                    ]
                }
            else:
                filter_query.update(search_filter)

        # Pagination setup
        page = max(1, int(page))
        limit = max(1, int(limit))
        skip = (page - 1) * limit

        total_count = brand_collection.count_documents(filter_query)

        cursor = brand_collection.find(filter_query, {"_id": 0, "id": 1, "name": 1})
        cursor = cursor.sort("name", 1).skip(skip).limit(limit)

        brands = list(cursor)

        data = {
            "data": brands,
            "meta": {
                "current_page": page,
                "next_page": page + 1 if page * limit < total_count else None,
                "total_count": total_count
            }
        }

        return {
            "status": 200,
            "data": data
        }

    except Exception as e:
        logger.error(traceback.format_exc())
        return {
            "status": 500,
            "message": f"Failed to fetch brands: {str(e)}"
        }
    
def get_category_dropdown(store, search, page, limit, badge_id):
    try:
        db_client = get_store_db_client_for_store_id(store['id'])
        db = get_admin_db_client_for_store_id(store['id'])
        category_collection = db[StoreAdminDBCollections.CMS_COLLECTION]
        smart_badge_collection = db_client[StoreDBCollections.SMART_BADGES]

        filter_query = {}
        category_ids_to_include = None

        # If badge_id is passed, fetch category_ids from the combined_product
        if badge_id:
            try:
                badge_object = smart_badge_collection.find_one({"_id": ObjectId(badge_id)})

                if badge_object and "combined_product" in badge_object:
                    category_ids = set()

                    for product in badge_object["combined_product"]:
                        product_categories = product.get("categories", [])
                        for cat_id in product_categories:
                            if isinstance(cat_id, int):  # ✔️ cat_id is already int
                                category_ids.add(cat_id)

                    category_ids_to_include = list(category_ids)

                    if category_ids_to_include:
                        filter_query["id"] = {"$in": category_ids_to_include}
                    else:
                        return {
                            "status": 200,
                            "data": {
                                "data": [],
                                "meta": {
                                    "current_page": page,
                                    "next_page": None,
                                    "total_count": 0
                                }
                            }
                        }
            except Exception as e:
                logger.error(traceback.format_exc())
                return {
                    "status": 400,
                    "message": f"Invalid badge_id or badge not found: {str(e)}"
                }

        if search:
            search_filter = {
                "name": {"$regex": search, "$options": "i"}
            }
            if "id" in filter_query:
                filter_query = {
                    "$and": [
                        {"id": {"$in": category_ids_to_include}},
                        search_filter
                    ]
                }
            else:
                filter_query.update(search_filter)

        # Pagination setup
        page = max(1, int(page))
        limit = max(1, int(limit))
        skip = (page - 1) * limit

        total_count = category_collection.count_documents(filter_query)

        cursor = category_collection.find(filter_query, {"_id": 0, "id": 1, "name": 1})
        cursor = cursor.sort("name", 1).skip(skip).limit(limit)

        categories = list(cursor)

        data = {
            "data": categories,
            "meta": {
                "current_page": page,
                "next_page": page + 1 if page * limit < total_count else None,
                "total_count": total_count
            }
        }

        return {
            "status": 200,
            "data": data
        }

    except Exception as e:
        logger.error(traceback.format_exc())
        return {
            "status": 500,
            "message": f"Failed to fetch categories: {str(e)}"
        }

def get_category_treeview(store, search=None):
    categories = fetch_all_categories(store, search)

    # Build a dictionary for quick lookup
    categories_dict = {category['id']: category for category in categories}

    if not search:
        root_categories = []
        for category in categories:
            if category.get('parent_id') == 0:
                root_categories.append(category)
            else:
                parent = categories_dict.get(category.get('parent_id'))
                if parent:
                    if 'children' not in parent:
                        parent['children'] = []
                    parent['children'].append(category)
        return root_categories

    return categories

def fetch_all_categories(store, search=None):
    db = get_admin_db_client_for_store_id(store['id'])

    pipeline = []

    # Add search filter if present
    if search:
        pipeline.append({
            "$match": {"name": {"$regex": search, "$options": "i"}}
        })

    pipeline.extend([
        {"$sort": {"name": 1}},
        {"$project": {
            "_id": 0,
            "id": 1,
            "name": 1,
            "parent_id": 1,
            "sort_order": 1,
            "status": 1
        }}
    ])

    categories = list(db.cms.aggregate(pipeline))
    categories = processList(categories)  # Convert bson to dict if needed

    return categories

def get_products_details(store, id, page, limit, sort_array, search, brand_id, category_id):
    try:
        db_client = get_store_db_client_for_store_id(store['id'])
        badge_collection = db_client[StoreDBCollections.SMART_BADGES]

        object_id = ObjectId(id)
        badge = badge_collection.find_one({"_id": object_id})

        if not badge:
            return {
                "status": 404,
                "message": "Badge not found"
            }

        # Match stage to isolate specific badge
        match_stage = {"_id": object_id}

        # Prepare filters
        filter_conditions = []

        # Handle brand_id (comma-separated string or list)
        if brand_id:
            if isinstance(brand_id, str):
                brand_id = [int(b.strip()) for b in brand_id.split(",") if b.strip().isdigit()]
            elif isinstance(brand_id, list):
                brand_id = [int(b) for b in brand_id if str(b).isdigit()]
            else:
                brand_id = []

            if brand_id:
                filter_conditions.append({"combined_product.brand_id": {"$in": brand_id}})

        # Handle category_id (comma-separated string or list)
        if category_id:
            if isinstance(category_id, str):
                category_id = [int(c.strip()) for c in category_id.split(",") if c.strip().isdigit()]
            elif isinstance(category_id, list):
                category_id = [int(c) for c in category_id if str(c).isdigit()]
            else:
                category_id = []

            if category_id:
                filter_conditions.append({"combined_product.categories": {"$elemMatch": {"$in": category_id}}})

        if search:
            regex_filter = {
                "$or": [
                    {"combined_product.name": {"$regex": search, "$options": "i"}},
                    {"combined_product.sku": {"$regex": search, "$options": "i"}}
                ]
            }
            filter_conditions.append(regex_filter)

        # Sorting
        sort_stage = {}
        if sort_array and isinstance(sort_array, list) and len(sort_array) == 2:
            field, direction = sort_array
            if field in ["name", "sku"] and direction in ['1', '-1']:
                sort_stage[f"combined_product.{field}"] = int(direction)

        # Aggregation pipeline
        pipeline = [
            {"$match": match_stage},
            {"$unwind": "$combined_product"},
        ]

        if filter_conditions:
            pipeline.append({"$match": {"$and": filter_conditions}})

        total_count_pipeline = pipeline + [{"$count": "count"}]
        total_result = list(badge_collection.aggregate(total_count_pipeline))
        total_count = total_result[0]["count"] if total_result else 0

        if sort_stage:
            pipeline.append({"$sort": sort_stage})

        # Pagination
        skip = (page - 1) * limit
        pipeline += [
            {"$skip": skip},
            {"$limit": limit}
        ]

        # Project only product info
        pipeline.append({"$replaceRoot": {"newRoot": "$combined_product"}})

        products = list(badge_collection.aggregate(pipeline))

        # Wrap response
        data = new_utils.calculate_pagination(products, page, limit, total_count)
        if data.get("data") and isinstance(data["data"], list):
            if 'meta' not in data:
                data['meta'] = {}
            data['meta']['total_count'] = total_count

        return {
            "status": 200,
            "data": data
        }

    except Exception as e:
        logger.error(traceback.format_exc())
        return {
            "status": 500,
            "message": f"Failed to fetch badge: {str(e)}"
        }
    
def delete_badges_product(store, id, payload):
    try:
        product_ids = payload.get("product_ids", [])
        if not product_ids or not isinstance(product_ids, list):
            return {"status": 400, "message": "No product IDs provided or invalid format."}

        try:
            product_ids = [int(pid) for pid in product_ids]
        except ValueError:
            return {"status": 400, "message": "All product IDs must be integers."}

        db_client = get_store_db_client_for_store_id(store['id'])
        badge_collection = db_client[StoreDBCollections.SMART_BADGES]

        badge = badge_collection.find_one({"_id": ObjectId(id)})
        if not badge:
            return {"status": 404, "message": "Smart badge not found."}

        # Remove products from individual_products array
        update_result = badge_collection.update_one(
            {"_id": ObjectId(id)},
            {
                "$pull": {
                    "individual_products": {"$in": product_ids}
                }
            }
        )
        
        # For combined_product, filter out objects with matching ids
        combined_products = badge.get("combined_product", [])
        updated_combined = [p for p in combined_products if p.get('id') not in product_ids]
        
        # Check if any brand_ids need to be removed
        brand_ids = badge.get("brand_ids", [])
        category_ids = badge.get("category_ids", [])
        
        update_fields = {
            "combined_product": updated_combined,
            "total_product_count": len(updated_combined)
        }
        
        # Update brand_ids if they exist
        if brand_ids:
            # Get all remaining brand_ids in the updated combined products
            remaining_brand_ids = {p.get("brand_id") for p in updated_combined if p.get("brand_id") is not None}
            # Filter out brand_ids that no longer have any products
            updated_brand_ids = [bid for bid in brand_ids if bid in remaining_brand_ids]
            update_fields["brand_ids"] = updated_brand_ids
        
        # Update category_ids if they exist
        if category_ids:
            # Get all remaining category_ids in the updated combined products
            remaining_category_ids = set()
            for product in updated_combined:
                product_categories = product.get("categories", [])
                for cat_id in product_categories:
                    if cat_id is not None:
                        remaining_category_ids.add(cat_id)
            
            # Filter out category_ids that no longer have any products
            updated_category_ids = [cid for cid in category_ids if cid in remaining_category_ids]
            update_fields["category_ids"] = updated_category_ids
        
        # Update the badge with all changes
        badge_collection.update_one(
            {"_id": ObjectId(id)},
            {"$set": update_fields}
        )

        return {
            "status": 200,
            "message": f"Products {product_ids} removed successfully."
        }

    except Exception as e:
        logger.error(traceback.format_exc())
        return {
            "status": 500, 
            "message": f"Failed to delete smart badge products: {str(e)}"
        }