from mongo_db import cms_db
from services import Service
from fields.navigation_fields import webpages_fields
from utils.common import parse_json

class WebPages(Service):
    def get_web_pages(self, payload,store):
        # def get_query(search_result):
        #     # return all products if search params is empty.
        #     if search_result == "":
        #         return {}

        #     # return text based search if search param's exists.
        #     return {"$text": {"$search": search_result}}

        # def set_limit(limit):
        #     page_limit = 0
        #     skips = 0
            
        #     if int(limit) != 0 and int(limit) > 0:
        #         page_limit = int(limit)

        #     return page_limit, skips
        
        # page_limit , skips = set_limit(payload['limit'] if 'limit' in payload else 0)
        
        # query = get_query(payload["search"]) if "search" in payload else {}
        
        data = cms_db.get_webpages(payload,webpages_fields,store) 

        # ProcessList ...
        # data = self.processList(data)
        return parse_json(data)
