from flask import request
import logging
import traceback
from api import APIResource
from analytics import replenishment, user_supplier_mapping, classified_as
from new_mongodb import store_admin_db
from products.all_products import products_list


logger = logging.getLogger()


class ReplenishmentClassifiedAs(APIResource):
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Replenishment Classified As POST")
        try:
            payload = request.get_json(force=True)
            name = payload.get('name', None)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            if username != '':
                res = classified_as.create_classified_as(store, name, username)
                if res['status'] == 200:
                    return {"message": res['message']}, 200
                else:
                    return {"message": res['message']}, res['status']
            else:
                return {"message": "Unauthorized"}, 401
        finally:
            logger.debug("Exiting Replenishment Classified As POST")

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Replenishment Classified As GET")
        try:
            query_params = request.args.to_dict()
            page = query_params.get('page')
            limit = query_params.get('limit')
            search = query_params.get('search', '')
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            res = classified_as.get_classified_as_data(store, page, limit, search, sort_array)
            if res['status'] == 200:
                return res['data'], 200
        finally:
            logger.debug("Exiting Replenishment Classified As GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
    def post(self):
        return self.execute_store_request(request, self.post_executor)

class ReplenishmentClassifiedAsDetails(APIResource):
    def get_executor(self, request, token_payload, store, id):
        logger.debug("Entering Replenishment Classified As Details GET")
        try:
            res = classified_as.get_classified_as_details(store, id)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, res['status']
        finally:
            logger.debug("Exiting Replenishment Classified As Details GET")

    def patch_executor(self, request, token_payload, store, id):
        logger.debug("Entering Replenishment Classified As Details PATCH")
        try:
            payload = request.get_json(force=True)
            name = payload.get('name', None)
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username'] 
            res = classified_as.update_classified_as(store, id, name, username)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, res['status']
        finally:
            logger.debug("Exiting Replenishment Classified As Details PATCH")

    def delete_executor(self, request, token_payload, store, id):
        logger.debug("Entering Replenishment Classified As Details DELETE")
        try:
            res = classified_as.delete_classified_as(store, id)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, res['status']
        finally:
            logger.debug("Exiting Replenishment Classified As Details DELETE")
    
    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id)
    
    def patch(self, id):
        return self.execute_store_request(request, self.patch_executor, id)
    
    def delete(self, id):
        return self.execute_store_request(request, self.delete_executor, id)
