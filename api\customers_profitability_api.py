import datetime
from flask import request
import logging
from api import APIResource
from analytics import customers_profitability
from products.brands import brands_purchaser_mapping

logger = logging.getLogger()

class CustomersProfitability(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering CustomersProfitability GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            search = query_params.get('search', None)
            customer_group_id = query_params.get('customer_group_id', '')
            sales_rep_emails = query_params.get('sales_rep_email', '')

            res = customers_profitability.get_customers_profitability_report(store, start_date, end_date, page, limit, sort_array, search, customer_group_id, sales_rep_emails)
            return res, 200
            # return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting CustomersProfitability GET {end_time-start_time}")
       

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class CustomerGroupsProfitability(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering CustomerGroupsProfitability GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            customer_group_id = query_params.get('customer_group_id', '')

            if start_date and end_date:
                res = customers_profitability.get_customer_groups_profitability_report(store, start_date, end_date, sort_array, customer_group_id)
                return {'data': res}, 200
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting CustomerGroupsProfitability GET {end_time-start_time}")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ReportSummary(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering ReportSummary GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)

            if start_date and end_date:
                res = customers_profitability.get_report_summary(store, start_date, end_date)
                return {'data': res}, 200
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting ReportSummary GET {end_time-start_time}")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)

class CustomerTypesProfitability(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering CustomertypesProfitability GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []

            if start_date and end_date:
                res = customers_profitability.get_customer_types_profitability_report(store['id'], start_date, end_date, sort_array)
                return {'data': res}, 200
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting CustomertypesProfitability GET {end_time-start_time}")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class CustomerRepsProfitability(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering CustomerrepsProfitability GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            search = query_params.get('search', None).strip()
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            page = query_params.get('page', 1)
            limit = query_params.get('limit', 10)
            sales_rep_type = query_params.get('sales_rep_type', '').strip()

            if start_date and end_date:
                res = customers_profitability.get_customer_reps_profitability_report(store['id'], start_date, end_date, search, page, limit, sort_array, sales_rep_type)
                return res
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting CustomerrepsProfitability GET {end_time-start_time}")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class PurchasersProfitability(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering PurchasersProfitability GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
        
            if start_date and end_date:
                res = customers_profitability.get_puchasers_profitability_report(store['id'], start_date, end_date, sort_array)
                return res
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting PurchasersProfitability GET {end_time-start_time}")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ProductWiseProfitability(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering ProductwiseProfitability GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            search = query_params.get('search', None).strip()
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            classification_filter = query_params.get('classification_filter', None)
            products_filter = query_params.get('products_filter', None)
            supplier_filter = query_params.get('supplier_filter', None)

            if start_date and end_date:
                res = customers_profitability.get_product_wise_profitability_report(store['id'], start_date, end_date, search, page, limit, sort_array, classification_filter, products_filter, supplier_filter)
                return res
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting ProductWiseProfitability GET {end_time-start_time}")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ClassificationProfitability(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering ClassificationProfitability GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            search = query_params.get('search', None).strip()
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            classification_filter = query_params.get('classification_filter', None)

            if start_date and end_date:
                res = customers_profitability.get_classification_profitability_report(store['id'], start_date, end_date, search, page, limit, sort_array, classification_filter)
                return res
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting ClassificationProfitability GET {end_time-start_time}")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class SuppliersProfitability(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering SuppliersProfitability GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            search = query_params.get('search', None).strip()
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            supplier_filter = query_params.get('supplier_filter', None)

            if start_date and end_date:
                res = customers_profitability.get_suppliers_profitability_report(store['id'], start_date, end_date, search, page, limit, sort_array, supplier_filter)
                return res
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting SuppliersProfitability GET {end_time-start_time}")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class OrdersProfitability(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering OrdersProfitability GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            search = query_params.get('search', None).strip()
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            customer_group_id = query_params.get('customer_group_id', '')
            sales_rep_emails = query_params.get('sales_rep_email', '')
            customer_id = query_params.get('customer_id', None)
            customer_type = query_params.get('customer_type', '')
            supplier_name = query_params.get('supplier_name', '')
            classification = query_params.get('classification', '')
            purchaser_name = query_params.get('purchaser_name', '')
            product_id = query_params.get('product_id', None)
            rep_type = query_params.get('rep_type', '')
            report_type = query_params.get('report_type', '')
            brand_id = query_params.get('brand_id', None)
            coupon_code = query_params.get('coupon_code', '')
            coupon_code_type = query_params.get('coupon_code_type', '')
            profit_percentage = query_params.get('profit_percentage', '')
            is_profitable = query_params.get('is_profitable', '')

            if start_date and end_date:
                res = customers_profitability.get_orders_profitability_report(store['id'], start_date, end_date, search, page, limit, sort_array, customer_group_id, sales_rep_emails, customer_id, customer_type, supplier_name, classification, purchaser_name, product_id, rep_type, report_type, brand_id, coupon_code, coupon_code_type, profit_percentage, is_profitable)
                if res['status'] == 200:
                    return res['data'], 200
                else:
                    return {'message': res['message']}, res['status']
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting OrdersProfitability GET {end_time-start_time}")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)
    

class CustomersProfitabilityCsv(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering CustomersProfitabilityCsv GET")
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            
            query_params = request.args.to_dict()
            query_params['username'] = username

            res = customers_profitability.customer_profitability_csv(store, query_params)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, res["status"]
        finally:
            logger.debug("Exiting CustomersProfitabilityCsv GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ProductWiseProfitabilityCsv(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering ProductWiseProfitabilityCsv GET")
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            
            query_params = request.args.to_dict()
            query_params['username'] = username

            res = customers_profitability.product_wise_profitability_csv(store, query_params)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, res["status"]
        finally:
            logger.debug("Exiting ProductWiseProfitabilityCsv GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class SuppliersProfitabilityCsv(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering SuppliersProfitabilityCsv GET")
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            
            query_params = request.args.to_dict()
            query_params['username'] = username

            res = customers_profitability.suppliers_profitability_csv(store, query_params)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, res["status"]
        finally:
            logger.debug("Exiting SuppliersProfitabilityCsv GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ClassificationProfitabilityCsv(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering ClassificationProfitabilityCsv GET")
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            
            query_params = request.args.to_dict()
            query_params['username'] = username

            res = customers_profitability.classification_profitability_csv(store, query_params)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, res["status"]
        finally:
            logger.debug("Exiting ClassificationProfitabilityCsv GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class OrdersProfitabilityCsv(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering OrdersProfitabilityCsv GET")
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            
            query_params = request.args.to_dict()
            query_params['username'] = username

            res = customers_profitability.orders_profitability_csv(store, query_params)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, res["status"]
        finally:
            logger.debug("Exiting OrdersProfitabilityCsv GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class BrandsProfitabilityCsv(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering BrandsProfitabilityCsv GET")
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            
            query_params = request.args.to_dict()
            query_params['username'] = username

            res = customers_profitability.brands_profitability_csv(store, query_params)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, res["status"]
        finally:
            logger.debug("Exiting BrandsProfitabilityCsv GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class BrandsProfitability(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering BrandsProfitability GET")
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            
            query_params = request.args.to_dict()
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            search = query_params.get('search', '').strip()
            brand_ids = query_params.get('brand_ids', None)
            purchaser_emails = query_params.get('purchaser_emails', None)


            res = customers_profitability.get_brands_profitability_report(store, start_date, end_date, page, limit, search, sort_array, brand_ids, purchaser_emails)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, res["status"]
        finally:
            logger.debug("Exiting BrandsProfitability GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class BrandsDropdown(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering BrandsDropdown GET")
        try:
            query_params = request.args.to_dict()
            search = query_params.get('search', '').strip()
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            purchaser_email = query_params.get('purchaser_email', '').strip()
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']
            if username != '':
                res = brands_purchaser_mapping.get_brands_purchaser_mapping_brand_dropdown(store['id'], search, page, limit, purchaser_email)
                if res['status'] == 200:
                    return res['data'], res['status']
                else:
                    return {'data': res['data']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Brands Dropdown GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class PurchasersDropdown(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering BrandsDropdown GET")
        try:
            username = ''
            if token_payload and 'username' in token_payload:
                username = token_payload['username']            
            if username != '':   
                res = customers_profitability.get_mapped_purchasers(store['id'])
                if res['status'] == 200:
                    return res['data'], res['status']
                else:
                    return {'data': res['data']}, res['status']
            else:
                return {'messsage': 'Unauthorized.'}, 401
        finally:
            logger.info(f"Brands Dropdown GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class PurchaserByBrandProfitability(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering PurchaserByBrandProfitability GET")
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            
            query_params = request.args.to_dict()
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            res = customers_profitability.get_purchaser_by_brand_profitability_report(store['id'], start_date, end_date, sort_array)
            if res['status'] == 200:
                return {"data": res['data']}, 200
        finally:
            logger.debug("Exiting PurchaserByBrandProfitability GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class OrdersWiseProfitability(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering OrdersWiseProfitability GET")
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            
            query_params = request.args.to_dict()
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            res = customers_profitability.get_orders_wise_profitability(store['id'], start_date, end_date)
            if res['status'] == 200:
                return {"data": res['data']}, 200
        finally:
            logger.debug("Exiting OrdersWiseProfitability GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class ProfitabilityCharts(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering ProfitabilityCharts GET")
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            period = query_params.get('period', 'day').strip()
            report_type = query_params.get('report_type', 'customer_group').strip()
            customer_group_id = query_params.get('customer_group_id', '')
            customer_type = query_params.get('customer_type', '').strip()
            sales_rep_email = query_params.get('rep_email', '').strip()
            supplier_id = query_params.get('supplier_name', '')
            classification = query_params.get('classification', '')
            purchaser_name = query_params.get('purchaser_name', '')
            brand_id = query_params.get('brand_id', '')

            if start_date and end_date:
                res = customers_profitability.get_profitability_report_charts(store['id'], start_date, end_date, period, report_type, customer_group_id, customer_type, sales_rep_email, supplier_id, classification, purchaser_name, brand_id)
                if res["status"] == 200:
                    return {"data": res["data"]}, 200
                else:
                    return {"message": res["message"]}, res["status"]
            return {'message': 'Invalid request.'}, 400
        finally:
            logger.info(f"Exiting ProfitabilityCharts GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)