import logging
import datetime
from bson import ObjectId
import mongo_db
import new_mongodb

logger = logging.getLogger()

TASK_COLLECTION = "tasks"
TASK_DETAILS_COLLECTION = "task_log"
TASK_MASTER = "task_master"

def fetch_task_by_id(task_id, store_id):
    db = new_mongodb.get_admin_db_client_for_store_id(store_id)
    return db[TASK_MASTER].find_one({"_id": task_id})

def fetch_details_by_taskName(taskName, start_index, limit):
    db = mongo_db.get_admin_db_client()
    cursor = db[TASK_DETAILS_COLLECTION].find({"taskName": taskName}).skip(start_index).limit(limit)
    jobDetails = [doc for doc in cursor]
    return jobDetails

def create_task(task_id, parent_id, root_id, task_name, store_id):
    db = mongo_db.get_admin_db_client()
    current_time = int(datetime.datetime.utcnow().timestamp())
    task = {
        "_id": task_id,
        "taskName": task_name,
        "parentId": parent_id,
        "rootId": root_id,
        "storeId": store_id,
        "status": "running",
        "outcome": -1,
        "log": {},
        "endAt": -1,
        "createdAt": current_time,
        "modifiedAt": current_time,
    }
    db[TASK_COLLECTION].insert_one(task)

def end_task(task_id, executionTime, outcome, log):
    db = mongo_db.get_admin_db_client()
    current_time = int(datetime.datetime.utcnow().timestamp())
    task = {
        "$set" : {
            "status": "completed",
            "outcome": int(outcome),
            "log": log,
            "executionTime": executionTime,
            "endAt": current_time,
            "modifiedAt": current_time
        }   
    }
    db[TASK_COLLECTION].update_one({"_id": task_id}, task)
    
def delete_older_task(time_minutes):
    timedelta = int((datetime.datetime.utcnow() - datetime.timedelta(minutes=time_minutes)).timestamp())
    db = mongo_db.get_admin_db_client()
    db[TASK_COLLECTION].delete_many({"createdAt":{"$lte":timedelta}})