import logging
import datetime
import mongo_db

logger = logging.getLogger()


class Repository():

    def __init__(self, db_client, collection_name):
        self.db_client = db_client
        self.collection = self.db_client[collection_name]

    def create(self, model):
        model["created_at"] = int(datetime.datetime.utcnow().timestamp())
        model["status"] = "active"
        return self.collection.insert_one(model)

    def find_one(self, query):
        return self.collection.find_one(query)

    def find(self, query, fields={}):
        return self.collection.find(query, fields)

    def find_by_id(self):
        pass

    def update(self, id, model):
        model["updated_at"] = int(datetime.datetime.utcnow().timestamp())

    def update_one(self, query, updated_data):
        return self.collection.update_one(query, updated_data)

    def soft_delete(self, query):
        data = {
            'status': 'deleted'
        }
        self.collection.find_one(query, data)

    def hard_delete(self, id):
        return self.collection.delete_one({'_id': id})

    def hard_delete_by_query(self, query):
        return self.collection.delete_one(query)

    def delete(self, id):
        model['status'] = 'deleted'
        pass

    def count_documents(self, query):
        return self.collection.count_documents(query, skip=0)

    def update_one(self, query, updated_data):
        return self.collection.update_one(query, updated_data, upsert=False)


class DB():

    from db import tenant_repo, component_repo, permission_repo,cms_repo,cms_brand_repo, user_role_repo, tasks_repo, store_config_repo, job_details_repo, app_repo, mail_template_repo, store_info_repo

    def __init__(self):
        logger.info("Entering DB init...")
        logger.info("Initializing DB Pool...")
        self.tenant_repo = None
        self.store_repo = None
        self.component_repo = None
        self.mail_template_repo = None
        self.permission_repo = None
        self.cms_repo=None
        self.cms_brand_repo=None
        self.user_role_repo = None
        self.app_repo = None
        self.tasks_repo = None
        self.store_config_repo = None
        # self.job_details_repo = None
        self.store_info_repo = None
        self.store_api_repo = {}
        logger.info("Exiting DB init...")

    def get_tenant_db_client(self, db_name):
        return mongo_db.get_db_client(db_name)

    def get_component_db_client(self, db_name):
        return mongo_db.get_db_client(db_name)

    def get_admin_db_client(self):
        return mongo_db.get_admin_db_client()

    def get_store_repository(self):
        if not self.store_repo:            
            self.store_repo = tenant_repo.StoreRepository(self.get_admin_db_client())
        return self.store_repo

    def get_tenant_repository(self):
        if not self.tenant_repo:
            self.tenant_repo = tenant_repo.TenantRepository(self.get_admin_db_client())
        return self.tenant_repo

    def get_component_repository(self):
        if not self.component_repo:
            self.component_repo = component_repo.ComponentRepository(self.get_admin_db_client())
        return self.component_repo
    
    def get_email_template_repository(self):
        if not self.mail_template_repo:
            self.mail_template_repo = mail_template_repo.EmailTemplateRepository(self.get_admin_db_client())
        return self.mail_template_repo

    def get_permission_repository(self):
        if not self.permission_repo:
            self.permission_repo = permission_repo.PermissionRepository(
                self.get_admin_db_client())
        return self.permission_repo
    
    def get_cms_repository(self):
        if not self.cms_repo:
            self.cms_repo = cms_repo.CmsRepository(
                self.get_admin_db_client())
        return self.cms_repo
    
    def get_cms_brand_repository(self):
        if not self.cms_brand_repo:
            self.cms_brand_repo = cms_brand_repo.CmsBrandRepository(
                self.get_admin_db_client())
        return self.cms_brand_repo
    
    def get_storeinfo_repository(self):
        if not self.store_info_repo:
            self.store_info_repo = store_info_repo.StoreInfoRepository(
                self.get_admin_db_client())
        return self.store_info_repo


    def get_user_role_repository(self):
        if not self.user_role_repo:
            self.user_role_repo = user_role_repo.UserRoleRepository(self.get_admin_db_client())
        return self.user_role_repo
    
    def get_app_menu_repository(self):
         if not self.app_repo:
            self.app_repo = app_repo.AppRepository(self.get_admin_db_client())
            return self.app_repo

    def get_tasks_repository(self):
        if not self.tasks_repo:
            self.tasks_repo = tasks_repo.MasterTaskRepository(self.get_admin_db_client())
        return self.tasks_repo
    
    def get_tokens_repository(self):
        if not self.store_config_repo:
            self.store_config_repo = store_config_repo.StoreConfigRepository(self.get_admin_db_client())
        return self.store_config_repo
    def get_job_details_repository(self):
        if not self.job_details_repo:
            self.job_details_repo = job_details_repo.JobDetailsRepository(self.get_admin_db_client())
        return self.job_details_repo

    def get_customers_repository(self, db_name):
        store_repo = self.store_api_repo.get(db_name, None)
        if not store_repo:
            store_repo = {}
            self.store_api_repo[db_name] = store_repo

        # Customers ...
        from db.customer_repo import CustomerRepository
        customers_repository = store_repo.get(
            CustomerRepository.__name__, None)

        if not customers_repository:
            db_client = self.get_tenant_db_client(db_name)
            customers_repository = CustomerRepository(db_client)
            self.store_api_repo[db_name][
                CustomerRepository.__name__
            ] = customers_repository

        return customers_repository

    def get_products_repository(self, db_name):
        store_repo = self.store_api_repo.get(db_name, None)
        if not store_repo:
            store_repo = {}
            self.store_api_repo[db_name] = store_repo

        # Customers ...
        from db import product_repo
        product_repository = store_repo.get(
            product_repo.ProductRepository.__name__, None)

        if not product_repository:
            db_client = self.get_tenant_db_client(db_name)
            product_repository = product_repo.ProductRepository(db_client)
            self.store_api_repo[db_name][
                product_repo.ProductRepository.__name__
            ] = product_repository

        return product_repository

    def get_navigations_repository(self, db_name):
        store_repo = self.store_api_repo.get(db_name, None)
        if not store_repo:
            store_repo = {}
            self.store_api_repo[db_name] = store_repo

        # Navigations ...
        from db import navigation_repo
        navigation_repository = store_repo.get(
            navigation_repo.NavigationsRepository.__name__, None)

        if not navigation_repository:
            db_client = self.get_tenant_db_client(db_name)
            navigation_repository = navigation_repo.NavigationsRepository(
                db_client)
            self.store_api_repo[db_name][
                navigation_repo.NavigationsRepository.__name__
            ] = navigation_repository

        return navigation_repository

    def get_sub_navigations_repository(self, db_name):
        store_repo = self.store_api_repo.get(db_name, None)
        if not store_repo:
            store_repo = {}
            self.store_api_repo[db_name] = store_repo

        # Sub Navigations ...
        from db import sub_navigation_repo
        sub_navigation_repository = store_repo.get(
            sub_navigation_repo.SubNavigationsRepository.__name__, None)

        if not sub_navigation_repository:
            db_client = self.get_tenant_db_client(db_name)
            sub_navigation_repository = sub_navigation_repo.SubNavigationsRepository(
                db_client)
            self.store_api_repo[db_name][
                sub_navigation_repo.SubNavigationsRepository.__name__
            ] = sub_navigation_repository

        return sub_navigation_repository

    def get_brands_repository(self, db_name):
        store_repo = self.store_api_repo.get(db_name, None)
        if not store_repo:
            store_repo = {}
            self.store_api_repo[db_name] = store_repo

        # Brands ...
        from db import brands_repo
        brands_repository = store_repo.get(
            brands_repo.BrandsRepository.__name__, None)

        if not brands_repository:
            db_client = self.get_tenant_db_client(db_name)
            brands_repository = brands_repo.BrandsRepository(db_client)
            self.store_api_repo[db_name][
                brands_repo.BrandsRepository.__name__
            ] = brands_repository

        return brands_repository

    def get_categories_repository(self, db_name):
        store_repo = self.store_api_repo.get(db_name, None)
        if not store_repo:
            store_repo = {}
            self.store_api_repo[db_name] = store_repo

        # Categories ...
        from db import category_repo
        categories_repository = store_repo.get(
            category_repo.CategoriesRepository.__name__, None)

        if not categories_repository:
            db_client = self.get_tenant_db_client(db_name)
            categories_repository = category_repo.CategoriesRepository(
                db_client)
            self.store_api_repo[db_name][
                category_repo.CategoriesRepository.__name__
            ] = categories_repository
        return categories_repository

    def get_web_pages_repository(self, db_name):
        store_repo = self.store_api_repo.get(db_name, None)
        if not store_repo:
            store_repo = {}
            self.store_api_repo[db_name] = store_repo

        # webpages ...
        from db import web_pages_repo
        web_pages_repository = store_repo.get(
            web_pages_repo.WebPagesRepository.__name__, None)

        if not web_pages_repository:
            db_client = self.get_tenant_db_client(db_name)
            web_pages_repository = web_pages_repo.WebPagesRepository(db_client)
            self.store_api_repo[db_name][
                web_pages_repo.WebPagesRepository.__name__
            ] = web_pages_repository

        return web_pages_repository

    def get_dynamic_pages_repository(self, db_name):
        store_repo = self.store_api_repo.get(db_name, None)
        if not store_repo:
            store_repo = {}
            self.store_api_repo[db_name] = store_repo

        # storefront dynamic pages ...
        from db import dynamic_pages_repo
        dynamic_pages_repository = store_repo.get(
            dynamic_pages_repo.DynamicPagesRepository.__name__, None)

        if not dynamic_pages_repository:
            db_client = self.get_tenant_db_client(db_name)
            dynamic_pages_repository = dynamic_pages_repo.DynamicPagesRepository(
                db_client)
            self.store_api_repo[db_name][
                dynamic_pages_repo.DynamicPagesRepository.__name__
            ] = dynamic_pages_repository

        return dynamic_pages_repository

    def get_blogs_repository(self, db_name):
        store_repo = self.store_api_repo.get(db_name, None)
        if not store_repo:
            store_repo = {}
            self.store_api_repo[db_name] = store_repo

        # storefront dynamic pages ...
        from db import blogs_repo
        blogs_repository = store_repo.get(
            blogs_repo.BlogsRepository.__name__, None)

        if not blogs_repository:
            db_client = self.get_tenant_db_client(db_name)
            blogs_repository = blogs_repo.BlogsRepository(db_client)
            self.store_api_repo[db_name][
                blogs_repo.BlogsRepository.__name__
            ] = blogs_repository

        return blogs_repository

    def get_blogs_author_repository(self, db_name):
        store_repo = self.store_api_repo.get(db_name, None)
        if not store_repo:
            store_repo = {}
            self.store_api_repo[db_name] = store_repo

        # Blogs Author Repo....
        from db import blogs_author_repo
        blogs_author_repository = store_repo.get(
            blogs_author_repo.BlogsAuthorRepository.__name__, None)

        if not blogs_author_repository:
            db_client = self.get_tenant_db_client(db_name)
            blogs_author_repository = blogs_author_repo.BlogsAuthorRepository(
                db_client)
            self.store_api_repo[db_name][
                blogs_author_repo.BlogsAuthorRepository.__name__
            ] = blogs_author_repository

        return blogs_author_repository

    def get_blogs_category_repository(self, db_name):
        store_repo = self.store_api_repo.get(db_name, None)
        if not store_repo:
            store_repo = {}
            self.store_api_repo[db_name] = store_repo

        # Blogs Category Repo....
        from db import blogs_category_repo
        blogs_category_repository = store_repo.get(
            blogs_category_repo.BlogsCategoryRepository.__name__, None)

        if not blogs_category_repository:
            db_client = self.get_tenant_db_client(db_name)
            blogs_category_repository = blogs_category_repo.BlogsCategoryRepository(
                db_client)
            self.store_api_repo[db_name][
                blogs_category_repo.BlogsCategoryRepository.__name__
            ] = blogs_category_repository

        return blogs_category_repository

    def get_loyalty_rewards_repository(self, db_name):
        store_repo = self.store_api_repo.get(db_name, None)
        if not store_repo:
            store_repo = {}
            self.store_api_repo[db_name] = store_repo

        # loyalty rewards pages ...
        from db import loyalty_rewards_repo
        loyalty_rewards_repository = store_repo.get(
            loyalty_rewards_repo.LoyaltyRewardsRepository.__name__, None)

        if not loyalty_rewards_repository:
            db_client = self.get_tenant_db_client(db_name)
            loyalty_rewards_repository = loyalty_rewards_repo.LoyaltyRewardsRepository(
                db_client)
            self.store_api_repo[db_name][
                loyalty_rewards_repo.LoyaltyRewardsRepository.__name__
            ] = loyalty_rewards_repository

        return loyalty_rewards_repository

    def get_mail_template_type_repository(self, db_name):
        store_repo = self.store_api_repo.get(db_name, None)
        if not store_repo:
            store_repo = {}
            self.store_api_repo[db_name] = store_repo

        # Mail Template Type Repo....
        from db import mail_template_type_repo
        mail_template_type_repository = store_repo.get(
            mail_template_type_repo.MailTemplateTypeRepository.__name__, None)

        if not mail_template_type_repository:
            db_client = self.get_tenant_db_client(db_name)
            mail_template_type_repository = mail_template_type_repo.MailTemplateTypeRepository(
                db_client)
            self.store_api_repo[db_name][
                mail_template_type_repo.MailTemplateTypeRepository.__name__
            ] = mail_template_type_repository

        return mail_template_type_repository

    def get_theme_build_repository(self, db_name):
        store_repo = self.store_api_repo.get(db_name, None)
        if not store_repo:
            store_repo = {}
            self.store_api_repo[db_name] = store_repo

        # Builds ...
        from db import build_repo
        build_repository = store_repo.get(
            build_repo.BuildsRepository.__name__, None)

        if not build_repository:
            db_client = self.get_tenant_db_client(db_name)
            build_repository = build_repo.BuildsRepository(db_client)
            self.store_api_repo[db_name][
                build_repo.BuildsRepository.__name__
            ] = build_repository

        return build_repository

    def get_mappingtables_repository(self, db_name):
        store_repo = self.store_api_repo.get(db_name, None)
        if not store_repo:
            store_repo = {}
            self.store_api_repo[db_name] = store_repo

        # MappingTables ...
        from db import mapping_table_repo
        mapping_table_repository = store_repo.get(
            mapping_table_repo.MappingTablesRepository.__name__, None)

        if not mapping_table_repository:
            db_client = self.get_tenant_db_client(db_name)
            mapping_table_repository = mapping_table_repo.MappingTablesRepository(
                db_client)
            self.store_api_repo[db_name][
                mapping_table_repo.MappingTablesRepository.__name__
            ] = mapping_table_repository

        return mapping_table_repository

    def get_redirects_repository(self, db_name):
        store_repo = self.store_api_repo.get(db_name, None)
        if not store_repo:
            store_repo = {}
            self.store_api_repo[db_name] = store_repo

        # redirects ...
        from db import redirects_repo
        redirects_repository = store_repo.get(
            redirects_repo.RedirectsRepository.__name__, None)

        if not redirects_repository:
            db_client = self.get_tenant_db_client(db_name)
            redirects_repository = redirects_repo.RedirectsRepository(db_client)
            self.store_api_repo[db_name][
                redirects_repo.RedirectsRepository.__name__
            ] = redirects_repository

        return redirects_repository