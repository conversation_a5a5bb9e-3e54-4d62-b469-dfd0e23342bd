from sqlalchemy import Column, DateTime, String, Integer, Float, text
import pg_db as db


class ExpressOrders(db.Base):
    __tablename__ = db.express_orders

    order_id = Column(Integer, primary_key=True)
    customer_id = Column(Integer)
    cart_id=Column(String)


    @classmethod
    def get_all_express_orders(cls, session=None):
        with db.get_connection() as conn:
            query = text(
                f"SELECT * FROM {db.express_orders}"
               
            )
            user = conn.execute(query)
            result=user.fetchall()
            orderListId=[]
            for record in result:
                orderListId.append(record[0])
            return orderListId
        

    @classmethod
    def get_order_id(cls,order_id, session=None):
        with db.get_connection() as conn:
            query = text(
                f"SELECT order_id FROM {db.express_orders} WHERE order_id IN (" + order_id + ")"
                
               
            )
            user = conn.execute(query.params(order_id=order_id))
            result=user.fetchall()
            orderIds=[]
            for record in result:
               order_id_str = str(record[0])
               order_id_cleaned = int(order_id_str.rstrip(','))
               orderIds.append(order_id_cleaned)
            return orderIds
   
