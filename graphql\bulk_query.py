bulk_query = """
query ProductsWithOptionSelections(
  $productIds: [Int!] = product_ids
  $variantIds: [Int!] = varaint_ids
) {
  site {
    products: products(entityIds: $productIds, first: 50) {
      edges {
        node {
          ...ProductFields
        }
      }
    }
  }
}
fragment ProductFields on Product {  
  entityId
  variants(entityIds: $variantIds, first: 250) {
    edges {
      node {
        entityId              
        inventory {
          aggregated {
            availableToSell
          }
        }        
      }
    }
  }
}
"""
def get_query(product_ids, varaint_ids):
    x = bulk_query.replace("product_ids", str(product_ids))
    x = x.replace("varaint_ids", str(varaint_ids))
    return x