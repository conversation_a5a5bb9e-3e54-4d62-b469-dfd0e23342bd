from flask import Flask
from flask_cors import CORS
import logging
import os
import config
from api import API
import apiv2
from db import DB
from services import Services
from mongo_db import collection_check
import websocket

logging.basicConfig(
    level=logging.ERROR,
    format="[%(asctime)s]: {} %(levelname)s %(message)s".format(os.getpid()),
    datefmt="%Y-%m-%d %H:%M:%S",
    handlers=[logging.StreamHandler()],
)

logger = logging.getLogger()

def create_app():
    app = Flask(__name__)
    app.config.from_object("config")
    
    logger.info(f"Starting app in {config.APP_ENV} environment")
    if app.config['ALLOWED_ORIGIN']:
        CORS(app, origins=[app.config['ALLOWED_ORIGIN']])
    else:
        CORS(app)

    @app.route("/")
    def hello_world():
        return "It's working!"

    db = DB()
    service = Services(app, db)
    api = API(app, service)
    api_resource = api.get_api()
    apiv2.register_v2_apis(api_resource)
    
    collection_check()

    if __name__ != '__main__':
        gunicorn_logger = logging.getLogger('gunicorn.error')
        app.logger.handlers = gunicorn_logger.handlers
        app.logger.setLevel(gunicorn_logger.level)

    socketio = websocket.init_app(app)

    return app


if __name__ == "__main__":
    os.environ.setdefault('APP_ENV', 'Dev')
    app = create_app()
    app.run(host=config.HOST, port=config.PORT, debug=config.DEBUG)
    # socketio.run(app, host=config.HOST, port=config.PORT, debug=config.DEBUG)

