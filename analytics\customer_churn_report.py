import new_pgdb
import datetime
from sqlalchemy import text
import new_utils
from utils.common import convert_to_timestamp
import logging
import traceback
from decimal import Decimal
logger = logging.getLogger()

def get_customer_churn_report(store_id, page, limit, sort_array, search):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        count_query = """
            WITH customer_orders AS (
                SELECT 
                    o.customer_id, 
                    COUNT(o.order_id) AS total_orders,
                    MAX(o.order_created_date_time) AS last_order_date
                FROM orders o
                WHERE o.order_created_date_time > '2024-01-01'
                GROUP BY o.customer_id
                HAVING COUNT(o.order_id) > 3
            )
            SELECT COUNT(*) 
            FROM customer_orders co
            JOIN customers c ON c.customer_id = co.customer_id
        """

        query = """
           WITH customer_orders AS (
                SELECT 
                    o.customer_id,
                    COUNT(o.order_id) AS total_orders,
                    MAX(o.order_created_date_time) AS last_order_date,
                    ARRAY_AGG(o.order_created_date_time ORDER BY o.order_created_date_time DESC) AS all_orders
                FROM orders o
                WHERE o.order_created_date_time > '2024-01-01'
                GROUP BY o.customer_id
                HAVING COUNT(o.order_id) > 3
            )
            SELECT
                c.customer_id as customer_id,
                c.first_name,
                c.last_name,
                c.email,
                co.total_orders,
                co.last_order_date,
                o.total_including_tax AS last_order_value, -- Added column
                co.all_orders[1] AS order_1,
                co.all_orders[2] AS order_2,
                co.all_orders[3] AS order_3,
                CASE 
                    WHEN array_length(co.all_orders, 1) >= 3 THEN 
                        CEIL(
                            ((DATE(co.all_orders[1]) - DATE(co.all_orders[2])) + 
                            (DATE(co.all_orders[2]) - DATE(co.all_orders[3]))) / 3.0  -- Fixed division
                        )
                    ELSE NULL
                END AS average_order_days,

                CASE 
                    WHEN array_length(co.all_orders, 1) >= 3 THEN 
                        co.last_order_date + 
                        INTERVAL '1 day' * CEIL(
                            ((DATE(co.all_orders[1]) - DATE(co.all_orders[2])) + 
                            (DATE(co.all_orders[2]) - DATE(co.all_orders[3]))) / 3.0  -- Fixed division
                        )
                    ELSE NULL
                END AS expected_order_date,
                o.order_id
            FROM customer_orders co
            JOIN customers c ON c.customer_id = co.customer_id
            JOIN orders o ON o.customer_id = co.customer_id AND o.order_created_date_time = co.last_order_date  -- Join to get total_including_tax for the last order
        """

        if search:
            query += " WHERE (c.first_name ILIKE '%" + search + "%' OR c.last_name ILIKE '%" + search + "%' OR c.email ILIKE '%" + search + "%')"
            count_query += " WHERE (c.first_name ILIKE '%" + search + "%' OR c.last_name ILIKE '%" + search + "%' OR c.email ILIKE '%" + search + "%')"

        if len(sort_array)> 0:
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            if sort_array[0] in ["expected_order_date", "average_order_days"]:
                query += f" ORDER BY {sort_array[0]} {sort_direction}"
            elif sort_array[0] in ["customer_name"]:            
                query += f" ORDER BY c.first_name {sort_direction}"
            elif sort_array[0] in ["email"]:            
                query += f" ORDER BY c.{sort_array[0]} {sort_direction}"
            elif sort_array[0] in ["last_order_value"]:            
                query += f" ORDER BY o.total_including_tax {sort_direction}"
            elif sort_array[0] in ["total_orders", "last_order_date"]:
                query += f" ORDER BY co.{sort_array[0]} {sort_direction}"
            

        if page and limit:
            page = int(page)
            limit = int(limit)
            offset = (page - 1) * limit
            query += " LIMIT " + str(limit) + " OFFSET " + str(offset)

        total_count = conn.execute(text(count_query)).scalar()
        result = conn.execute(text(query))
        customers = []
        for row in result:
            row_data = {
                'customer_id': row[0],
                'customer_name': row[1] + " " + row[2],
                'email': row[3],
                'total_orders': row[4],
                'last_order_date': convert_to_timestamp(row[5]),
                'last_order_value': row[6],
                'order_1': row[7].strftime("%Y-%m-%d") if isinstance(row[7], datetime.datetime) else str(row[7]),
                'order_2': row[8].strftime("%Y-%m-%d") if isinstance(row[8], datetime.datetime) else str(row[8]),
                'order_3': row[9].strftime("%Y-%m-%d") if isinstance(row[9], datetime.datetime) else str(row[9]),
                'average_order_days': int(row[10]) if isinstance(row[10], Decimal) else row[10],
                'expected_order_date_formatted': row[11].strftime("%Y-%m-%d") if isinstance(row[11], datetime.datetime) else str(row[11]),
                'expected_order_date': convert_to_timestamp(row[11]),
                'order_id': row[12]
            }
            customers.append(row_data)

        if customers:
            data = new_utils.calculate_pagination(customers, page, limit, total_count)

            response['data'] = data
            response['status'] = 200
        else:
            response['data'] = customers
            response['status'] = 200
    except Exception as e:
        logger.error(traceback.format_exc())
        response['status'] = 422
        response['message'] = str(e)
        raise e
    finally:
        if conn:
            conn.close()
    return response