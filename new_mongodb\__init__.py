from enum import Enum
from pymongo import MongoClient
import logging
from bson import ObjectId
from config import appconfig
import threading
from config.appconfig import ADConfigKey
import datetime

logger = logging.getLogger()

class TenantDBCollections:
    COMMON_CONFIG_COLLECTION = "common_config"
    STORE_COLLECTION = "stores"
    STORE_TASK_MASTER = "task_master"
    USERS_COLLECTION = "users"
    FEATURES_COLLECTION = "features"
    ROLES_COLLECTION = "roles"
    CLIENT_APPS_COLLECTION = "client_apps"

class StoreAdminDBCollections:
    USERS_COLLECTION = "users"
    ROLES_COLLECTION = "roles"
    CMS_COLLECTION = "cms"
    CMS_BRANDS_COLLECTION = "cms_brands"
    STORE_INFO_COLLECTION = "store_info"
    USER_PREFERENCE = "user_preference"
    PRODUCT_PRICE_LISTS = "product_price_lists"
    STATIC_PRICE_LISTS = "static_price_lists"
    PRICE_LIST_UPDATE_DATA = "price_list_update_data"
    PRICE_LIST_RULES = "price_list_rules"
    PRICE_LIST_DISTRIBUTORS = "price_list_distributors"
    NOTIFICATIONS_ADMIN = "notifications_admin"
    NOTIFICATION_MODULES = "notification_modules"
    EMAIL_NOTIFICATION_MASTER = "email_notification_master"

class StoreDBCollections:
    PRODUCTS = "products"
    CATEGORIES = "categories"
    TASK_MASTER = "task_master"
    LOYALTY_REWARDS_COLLECTION = "loyalty_rewards"
    WEBPAGES_COLLECTION = "pages"
    BLOGS_COLLECTION = "blogs"
    BLOGS_AUTHOR = "blogs_author"
    BLOGS_CATEGORY = "blogs_category"
    MY_PRODUCTS = "my_products"
    PRICE_LIST_ASSIGNMENT = "price_list_assignment"
    BLOGS_AUTHOR_COLLECTION = "blogs_author"
    LIQUIDATE_PRODUCT_PRICE_LOGS = "liquidate_product_price_logs"
    SKUVAULT_POS = "skuvault_pos"
    MSDA_PMTA_ATTACHMENTS = "msda_pmta_attachments"

class CommonConfigKeys:
    GOOGLE_OAUTH_CREDS = "google_oauth_creds"
    IGEN_TAX_FLAG = "igen_tax_flag"

class UserKeys:
    ID = '_id'
    USERNAME = "username"
    STATUS = "status"
    YOYO = "yoyo"
    YOYO_KEY = "key"
    YOYO_VALUE = "value"
    TENANT_ID = "tenant_id"
    STORES = "stores"
    NAME = "name"
    DEFAULT_STORE_ID = "default_store_id"
    ROLE_ID = "role_id"
    CREATED_AT = "created_at"
    UPDATED_AT = "updated_at"
    LAST_LOGIN = "last_login"
    ROLE = "role"
    TYPE = "type"

class StoreKeys:
    NAME = "name"
    ADMIN_URL = "admin_url"
    GOOGLE_OAUTH_CREDS = "google_oauth_creds"    

class RoleKeys:
    PERMISSIONS = "permissions"
    STATUS = "status"
    ROLE = "role"
    IS_ADMIN_ACCESS = "is_administrator_access"
    IS_SUPER_ADMIN = "is_super_admin"

class ClientAppsKeys:
    USERNAME = "username"
    CLIENT_ID = "client_id"
    ACCESS_TOKEN = "access_token"
    TENANT_ID = "tenant_id"
    STORE_ID = "store_id"
    IS_ACTIVE = "is_active"

class UserStatus(Enum):
    active = "active"
    inactive = "inactive"


class AdminAppNotification:
    ORDER_CREATED = "order_created"
    ORDER_UPDATED = "order_updated"
    PRICE_LIST_RULE_CREATED = "price_list_rule_created"
    PRICE_LIST_RULE_UPDATED = "price_list_rule_updated"
    GUEST_PRODUCT_INQUIRY = "guest_product_inquiry"
    REGISTERED_USER_PRODUCT_INQUIRY = "registered_user_product_inquiry"
    TICKET_CREATED = "ticket_created"
    TICKET_UPDATED = "ticket_updated"
    TICKET_ASSIGNED = "ticket_assigned"
    TICKET_ASSIGNED_BY_MAPPING = "ticket_assigned_by_mapping"
    USER_CREATED = "user_created"
    USER_THEMES_UPDATED = "user_themes_updated"
    CUSTOMER_CREATED = "customer_created"
    PO_CREATED = "po_created"
    ORDER_PLACED = "order_placed"
    UNLOCK_PRODUCT = "unlock_product"
    BLOCK_ORDER_CREATED = "block_order_created"

class ProjectNotification:
    TICKET_CREATED = "ticket_created"
    TICKET_ASSIGNED = "ticket_assigned"
    COMMENT_ADDED = "comment_added"

class DBClientPool:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            logger.info('Creating the DBClientPool')
            cls._instance = super(DBClientPool, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        logger.info("Entering DBClientPool")
        self.lock = threading.Lock()
        self.connection_pool = {}
        self._stores_by_id = {}
        self._stores_by_storehash = {}
        self._stores_by_domain = {}
        self.update_stores()
        logger.info("Exiting DBClientPool")

    def get_db_client(self, conn_str, db_name):
        db_client = self.connection_pool.get(db_name, None)
        if db_client is None:
            self.lock.acquire()
            try:
                db_client = self.connection_pool.get(db_name, None)
                if db_client is None:
                    db_client = MongoClient(conn_str)[db_name]
                    self.connection_pool[db_name] = db_client
            finally:
                self.lock.release()
        return db_client
    
    def get_app_db_client(self, store, app_name):
       
        db_client = None
        if store:
            app_db_info = get_db_config_from_store(store, app_name)
            if app_db_info is not None:
                db_name = app_db_info.get(ADConfigKey.DB_NAME_KEY, None)
                host_name = app_db_info.get(ADConfigKey.DB_HOST_NAME_KEY, None)
                port = app_db_info.get(ADConfigKey.DB_PORT_KEY, None)
                if db_name and host_name and port:
                    conn_str = f"mongodb://**************:27017/"
                    db_client = self.get_db_client(conn_str, db_name)
        return db_client
    
    def get_store_db_client(self, store):
        # print(store,"store2")
        return self.get_app_db_client(store, ADConfigKey.STORE_DB_KEY)
    
    def get_admin_db_client(self, store):
        return self.get_app_db_client(store, ADConfigKey.ADMIN_DB_KEY)
    
    def get_tenant_db_client(self):
        tenant_db_name = appconfig.get_tenant_db_name()
        tenant_db_conn_str = appconfig.get_mongodb_conn_str()
        return self.get_db_client(tenant_db_conn_str, tenant_db_name)
    
    def update_stores(self):
        db_client = self.get_tenant_db_client()
        coll = db_client[TenantDBCollections.STORE_COLLECTION]
        cur = coll.find({"status": "active"})
        for row in cur:
            data = process_data(row)
            self._stores_by_id[data['id']] = data
            self._stores_by_domain[data['domain']] = data
            self._stores_by_storehash[data['bc_config']['store_hash']] = data

    def get_store_by_id(self, store_id):
        return self._stores_by_id.get(store_id, None)
    
    def get_store_by_store_hash(self, store_hash):
        return self._stores_by_storehash.get(store_hash, None)
    
    def get_store_by_domain(self, domain):
        return self._stores_by_domain.get(domain, None)
    
    def get_active_stores(self):
        return self._stores_by_id

def get_admin_db_client(store):
    db_client = None
    if store:
        db_client = db_client_pool.get_admin_db_client(store)
    return db_client

def get_store_db_client(store):
    db_client = None
    db_client = db_client_pool.get_store_db_client(store)
    return db_client

def get_admin_db_client_for_store_id(store_id):
    store = db_client_pool.get_store_by_id(store_id)
    return get_admin_db_client(store)

def get_store_db_client_for_store_id(store_id):
    store = db_client_pool.get_store_by_id(store_id)
    return get_store_db_client(store)

def get_db_config_from_store(store, app_name):
    profile = appconfig.get_app_profile()   
    return store.get(ADConfigKey.AD_CONFIG_KEY, {}).get(profile, {}).get(ADConfigKey.DB_INFO_KEY, {}).get(app_name, None)

def get_analytics_db_config_from_store(store):
    return get_db_config_from_store(store, ADConfigKey.ANALYTICS_DB_KEY)

def get_redis_config_from_store(store):
    profile = appconfig.get_app_profile()
    return store.get(ADConfigKey.AD_CONFIG_KEY, {}).get(profile, {}).get(ADConfigKey.REDIS_CONFIG_KEY, None)

def get_redis_config(store_id):
    redis_config = None
    store = get_store_by_id(store_id)
    if store:
        redis_config = get_redis_config_from_store(store)
    return redis_config
    
def get_store_by_id(store_id):
    return db_client_pool.get_store_by_id(store_id)

def get_store_by_store_hash(store_hash):
    return db_client_pool.get_store_by_store_hash(store_hash)

def get_store_by_domain(store_domain):
    return db_client_pool.get_store_by_domain(store_domain)

def get_tenant_db_client():
    return db_client_pool.get_tenant_db_client()

def process_data(obj):
    if obj:
        if '_id' in obj:
            obj['id'] = str(obj['_id'])
            del obj['_id']

        for key, value in obj.items():
            if isinstance(value, ObjectId):
                obj[key] = str(value)
            elif isinstance(value, datetime.datetime):
                obj[key] = int(value.timestamp())
    return obj

def process_documents(documents):
    result = []
    if documents:
        for _obj in documents:
             # remove attachments and add msda_count and pmta_count based on files stored in their arrays
            if "attachments" in _obj:
                msda_count = len(_obj["attachments"].get("msda", []))
                pmta_count = len(_obj["attachments"].get("pmta", []))
                _obj["msda_count"] = msda_count
                _obj["pmta_count"] = pmta_count
                del _obj["attachments"]
            result.append(process_data(_obj))
    return result

def upsert_documents(store, collection, documents):
    db_client = get_store_db_client(store)
    for document in documents:
        db_client[collection].replace_one({"_id": document['_id']},document,upsert=True)

def fetch_store_tasks():
    db_client = get_tenant_db_client()
    coll = db_client[TenantDBCollections.STORE_TASK_MASTER]
    cur = coll.find({})
    store_task_master = {}
    for row in cur:
        data = process_data(row)
        store_task_master[data['id']] = data["tasks"]
    
    return store_task_master

def get_active_stores():
    stores = db_client_pool.get_active_stores()
    result = []
    for store_id, store in stores.items():
        result.append(store)
    return result

def fetchall_documents_from_admin_collection(store_id, collection_name, query={}, projection=None):
    dbclient = get_admin_db_client_for_store_id(store_id)
    if projection:
        return dbclient[collection_name].find(query, projection)
    else:
        return dbclient[collection_name].find(query)

def fetch_one_document_from_admin_collection(store_id, collection_name, query={}, projection=None):
    dbclient = get_admin_db_client_for_store_id(store_id)
    if projection:
        return dbclient[collection_name].find_one(query, projection)
    else:
        return dbclient[collection_name].find_one(query)

def fetch_one_document_from_tenant_collection(collection_name, query={}, projection=None):
    dbclient = get_tenant_db_client()
    if projection:
        return dbclient[collection_name].find_one(query, projection)
    else:
        return dbclient[collection_name].find_one(query)
    
def update_document_in_tenant_collection(collection_name, query={}, update_data={}):
    dbclient = get_tenant_db_client()
    return dbclient[collection_name].update_one(query, update_data)

def fetch_one_document_from_storefront_collection(store_id, collection_name, query={}, projection=None):
    dbclient = get_store_db_client_for_store_id(store_id)
    if projection:
        res = dbclient[collection_name].find_one(query, projection)
        return process_data(res)
    else:
        res = dbclient[collection_name].find_one(query)
        return process_data(res)

def update_document_in_admin_collection(store_id, collection_name, query={}, update_data={}):
    dbclient = get_admin_db_client_for_store_id(store_id)
    return dbclient[collection_name].update_one(query, update_data)

def update_document_in_storefront_collection(store_id, collection_name, query={}, update_data={}):
    dbclient = get_store_db_client_for_store_id(store_id)
    return dbclient[collection_name].update_one(query, update_data)

def fetchall_documents_from_collection(db_client, collection_name, query={}, projection=None):
    if projection:
        return db_client[collection_name].find(query, projection)
    else:
        return db_client[collection_name].find(query)

def fetchall_documents_from_storefront_collection(store_id, collection_name, query={}, projection=None):
    dbclient = get_store_db_client_for_store_id(store_id)
    if projection:
        return dbclient[collection_name].find(query, projection)
    else:
        return dbclient[collection_name].find(query)

def insert_document_in_storefront_collection(store, collection, document):
    db_client = get_store_db_client(store)
    result = db_client[collection].insert_one(document)
    return str(result.inserted_id)

def insert_document_in_admin_collection(store_id, collection, document):
    db_client = get_admin_db_client_for_store_id(store_id)
    result = db_client[collection].insert_one(document)
    return str(result.inserted_id)

def delete_documents_from_admin_collection(store_id, collection_name, query={}):
    dbclient = get_admin_db_client_for_store_id(store_id)
    return dbclient[collection_name].delete_one(query)

def delete_documents_from_storefront_collection(store_id, collection_name, query={}):
    dbclient = get_store_db_client_for_store_id(store_id)
    return dbclient[collection_name].delete_one(query)

def count_documents_admin_collection(store_id, collection_name, query={}):
    dbclient = get_admin_db_client_for_store_id(store_id)
    return dbclient[collection_name].count_documents(query, skip=0)

def count_documents_from_collection(db_client, collection_name, query={}):
    return db_client[collection_name].count_documents(query, skip=0)
    
def delete_documents_from_storefront_collection(store_id, collection_name, query={}):
    dbclient = get_store_db_client_for_store_id(store_id)
    return dbclient[collection_name].delete(query)

def count_documents_admin_collection(store_id, collection_name, query={}):
    dbclient = get_admin_db_client_for_store_id(store_id)
    return dbclient[collection_name].count_documents(query, skip=0)

def delete_documents_from_storefront_collection(store_id, collection_name, query={}):
    dbclient = get_store_db_client_for_store_id(store_id)
    return dbclient[collection_name].delete_one(query)

def count_documents_admin_collection(store_id, collection_name, query={}):
    dbclient = get_admin_db_client_for_store_id(store_id)
    return dbclient[collection_name].count_documents(query, skip=0)

def delete_documents_from_storefront_collection(store_id, collection_name, query={}):
    dbclient = get_store_db_client_for_store_id(store_id)
    return dbclient[collection_name].delete(query)

def count_documents_admin_collection(store_id, collection_name, query={}):
    dbclient = get_admin_db_client_for_store_id(store_id)
    return dbclient[collection_name].count_documents(query, skip=0)

def delete_documents_from_storefront_collection(store_id, collection_name, query={}):
    dbclient = get_store_db_client_for_store_id(store_id)
    return dbclient[collection_name].delete_one(query)

def count_documents_admin_collection(store_id, collection_name, query={}):
    dbclient = get_admin_db_client_for_store_id(store_id)
    return dbclient[collection_name].count_documents(query, skip=0)

def count_documents_storefront_collection(store_id, collection_name, query={}):
    dbclient = get_store_db_client_for_store_id(store_id)
    return dbclient[collection_name].count_documents(query, skip=0)

db_client_pool = DBClientPool()

def update_tenant_users():
    admin_db = get_admin_db_client_for_store_id("63da3e98b702e324567f76f9")
    users = admin_db["users"].find({})

    tenant_db = get_tenant_db_client()["users"]
    for user in users:
        user["tenant_id"] = "63da3c4740426be7a8f14047"
        user["stores"] = {
                "63da3e98b702e324567f76f9": { 
                    "role": user["role"],
                    "role_id": user["role_id"]
                },
                # "661239751b9ce4bd7f85237c": { 
                #     "role": user["role"],
                #     "role_id": user["role_id"]
                # }
            }
        user["default_store_id"] = "63da3e98b702e324567f76f9"
        tenant_db.replace_one({"_id": user['_id']},user,upsert=True)

def update_tenant_roles():
    admin_db = get_admin_db_client_for_store_id("63da3e98b702e324567f76f9")
    roles = admin_db["roles"].find({})

    tenant_db = get_tenant_db_client()["roles"]
    for role in roles:
        if "tenant_id" in role:
            del role["tenant_id"]
        if "total_users" in role:
            role["tenants"] = {
                "63da3c4740426be7a8f14047": {
                    "stores": {
                        "63da3e98b702e324567f76f9": {
                            "total_users": role["total_users"]
                        },
                        # "661239751b9ce4bd7f85237c": {
                        #     "total_users": role["total_users"]
                        # }
                    }
                }
            }
        tenant_db.replace_one({"_id": role['_id']}, role, upsert=True)

def delete_admin_role():
    admin_db = get_admin_db_client_for_store_id("63da3e98b702e324567f76f9")
    role_coll = admin_db["roles"]
    admin_roles = role_coll.find({"role":"Admin"})
    for role in admin_roles:
        if role["total_users"] == 0:
            role_coll.delete_one({"_id": role["_id"]})
