from datetime import datetime
from sqlalchemy import Column,  DateTime,String, Integer,Float, text
from sqlalchemy.dialects.postgresql import insert
import new_pgdb as db
from analytics import calculate_pagination
from utils.common import convert_to_timestamp

class BlockedOrdersSchema(db.Base):
    __tablename__ = db.DBTables.blocked_orders

    id = Column(Integer, primary_key=True)
    order_id = Column(Integer)
    order_created_date_time = Column(DateTime)
    customer_name = Column(String)
    customer_representative = Column(String)
    blocked_by = Column(String)
    status = Column(String)
    total = Column(Float)
    restock_threshold = Column(Integer)
    restock_qty = Column(Integer)
    

    @classmethod
    def add_blocked_order(cls, store,  data, session=None):
        stmt = insert(BlockedOrdersSchema).on_conflict_do_nothing()
        
        db.execute_stmt(store, stmt, data, session)
        return data
    
    @classmethod
    def update_threshold_and_restock_qty(cls, store,  data, session=None):
        stmt = text("UPDATE blocked_orders SET restock_threshold = :restock_threshold, restock_qty = :restock_qty WHERE order_id = :order_id")
        db.execute_stmt(store, stmt, data, session)
        
        return data
    
    @classmethod
    def get_threshold_and_restock_qty(cls, store, order_id, session=None):
        try:
            conn = db.get_connection(store['id'])
            query = text("SELECT restock_threshold, restock_qty FROM blocked_orders WHERE order_id = :order_id")
            
            res = conn.execute(query, {'order_id': order_id})
            row = res.fetchone()
            
            return {'restock_threshold': row[0], 'restock_qty': row[1]} if row else {'restock_threshold': 0, 'restock_qty': 0}
        finally:
            if conn:
                conn.close()
    
    @classmethod
    def is_blocked_order_exists(cls, store, order_id, session=None):
        try:
            conn = db.get_connection(store['id'])
            query = text("SELECT order_id FROM blocked_orders WHERE order_id = :order_id")
            res = conn.execute(query, {'order_id': order_id})
            row = res.fetchone()
            
            return True if row else False
        finally:
            if conn:
                conn.close()

class BlockedOrdersLogsSchema(db.Base):
    __tablename__ = db.DBTables.blocked_orders_logs

    id = Column(Integer, primary_key=True)
    order_id = Column(Integer)
    date_time = Column(DateTime)
    sku = Column(String)
    triggered_by = Column(String)
    restock_qty = Column(Integer)
    available_qty = Column(Integer)


    @classmethod
    def add_blocked_order_logs(cls, store,  data, session=None):
        stmt = insert(BlockedOrdersLogsSchema).on_conflict_do_nothing()
        db.execute_stmt(store, stmt, data, session)
        
        return data
    
    @classmethod
    def is_order_exists(cls, store, order_id, session=None):
        try:
            conn = db.get_connection(store['id'])
            query = text("SELECT order_id FROM blocked_orders WHERE order_id = :order_id")
            res = conn.execute(query, {'order_id': order_id})
            row = res.fetchone()
            
            return True if row and row[0] else False
        finally:
            if conn:
                conn.close()
    
    @classmethod
    def get_logs(cls, store, order_id,  page, limit, search_term, sort_array, session=None):
        response = {'status': 400}
        conn = db.get_connection(store['id'])

        try:
            count_query = f"""SELECT COUNT(*) FROM blocked_order_logs WHERE order_id = {order_id}"""
        
            res = conn.execute(text(count_query))
            total_count_row = res.fetchone()
            total_count = total_count_row[0] if total_count_row else 0

            # SQL query to get data from the orders and customer tables
            query = f"""
                SELECT
                    ol.order_id,
                    ol.date_time,
                    ol.sku,
                    ol.triggered_by,
                    ol.restock_qty,
                    ol.available_qty
                FROM
                    blocked_order_logs AS ol
                WHERE
                    ol.order_id = {order_id}
            """

            if len(sort_array):
                sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            
            query += f" ORDER BY ol.{sort_array[0]} {sort_direction}"

            offset = (page - 1) * limit
            query += f" LIMIT {limit} OFFSET {offset}"

            base_query = text(query)
            res = conn.execute(base_query)
            
            logs=[]
            for row in res:
                row_data = {
                    'order_id': row[0],
                    'date_time': convert_to_timestamp(row[1]),
                    'sku': row[2],
                    'triggered_by': row[3],
                    'restock_qty': row[4],
                    'available_qty': row[5]
                }
                logs.append(row_data)

            data = calculate_pagination(logs, page, limit, total_count)
            
            if data:
                response['data'] = data
                response['status'] = 200
            else:
                response['status'] = 404
                response['message'] = 'No data found.'

        finally:
            if conn:
                conn.close()  # Make sure to close the connection

        return response
    
def get_order_details_by_order_id(store, order_id, current_user):
    response = {'status': 400}
    conn = db.get_connection(store['id'])

    try:
        # SQL query to get data from the orders and customer tables
        query = text(f"""
            SELECT 
                o.order_id,
                o.order_created_date_time,
                CONCAT(c.first_name, ' ', c.last_name) AS customer_name,  -- Combining first and last name
                o.order_status AS status,
                o.total_including_tax AS total
            FROM 
                orders AS o
            JOIN 
                customers AS c ON o.customer_id = c.customer_id
            WHERE 
                o.order_id = {order_id}
        """)

        # Execute the query
        result = conn.execute(query)
        res = result.fetchone()  # Fetch only one record since we expect one order

        # If result exists, format the data into a dictionary
        if res:
            row_data = {
                'order_id': res[0],
                'order_created_date_time': res[1].strftime('%Y-%m-%d %H:%M:%S'),
                'customer_name': res[2],
                'blocked_by': current_user,
                'status': res[3],
                'total': res[4],
                'restock_threshold': None,
                'restock_qty': None
            }

            response['data'] = row_data
            response['status'] = 200
        else:
            response['message'] = "Order not found"
            response['status'] = 404

    except Exception as e:
        response['status'] = 422
        response['message'] = str(e)

    finally:
        if conn:
            conn.close()  # Make sure to close the connection

    return response

def add_logs(store, data):
    response = {'status': 400}
    conn = db.get_connection(store['id'])

    try:
        stmt = insert(BlockedOrdersLogsSchema).values(data)
        res = conn.execute(stmt)
        conn.commit()  # Commit after executing the insert query
        
        response['data'] = res
        response['status'] = 200
    except Exception as e:
        response['status'] = 422
        response['message'] = str(e)

    finally:
        if conn:
            conn.close()  # Make sure to close the connection

    return response

def clean_up_deleted_blocked_order(store, order_id):
    response = {'status': 400}
    conn = db.get_connection(store['id'])

    try:
        # Step 1: Delete from `blocked_orders`
        delete_blocked_orders = text("DELETE FROM blocked_orders WHERE order_id = :order_id")
        res_blocked_orders = conn.execute(delete_blocked_orders, {'order_id': order_id})
        conn.commit()  # Commit after executing the delete query
        print(f"Deleted from blocked_orders: {res_blocked_orders.rowcount} rows")

        if res_blocked_orders.rowcount > 0:
            response['data'] = {'deleted_count': res_blocked_orders.rowcount}
            response['status'] = 200

            # Step 2: Delete from `blocked_order_logs`
            delete_logs = text("DELETE FROM blocked_order_logs WHERE order_id = :order_id")
            res_logs = conn.execute(delete_logs, {'order_id': order_id})
            conn.commit()  # Commit after executing the delete query
            print(f"Deleted from blocked_orders_logs: {res_logs.rowcount} rows")

        else:
            response['message'] = "Failed to delete from blocked_orders"

    except Exception as e:
        conn.rollback()  # Rollback if there's an error
        response['status'] = 422
        response['message'] = str(e)

    finally:
        if conn:
            conn.close()

    return response

def get_all_logs(store, order_id,  page, limit, search_term, sort_array):
    return BlockedOrdersLogsSchema.get_logs(store, order_id, page, limit, search_term, sort_array)


def remove_blocked_order(store, order_id, session=None):
    response = {'status': 400}
    conn = None

    try:
        # Step 1: Get the database connection
        conn = db.get_connection(store['id']) if session is None else session

        # Step 2: Delete from `blocked_orders`
        delete_blocked_orders = text("DELETE FROM blocked_orders WHERE order_id = :order_id")
        res_blocked_orders = conn.execute(delete_blocked_orders, {'order_id': order_id})
        
        conn.commit()  # Commit the transaction after executing the delete query
        print(f"Deleted from blocked_orders: {res_blocked_orders.rowcount} rows")

        if res_blocked_orders.rowcount > 0:
            # Step 3: Delete from `blocked_order_logs`
            delete_logs = text("DELETE FROM blocked_order_logs WHERE order_id = :order_id")
            res_logs = conn.execute(delete_logs, {'order_id': order_id})
            conn.commit()  # Commit the transaction after executing the delete query
            print(f"Deleted from blocked_orders_logs: {res_logs.rowcount} rows")

            if res_logs.rowcount > 0:
                response['status'] = 200
                response['message'] = "Status updated successfully."
            else:
                response['message'] = "Failed to update status."
                
        else:
            response['message'] = "Failed to update status."

    except Exception as e:
        if conn:
            conn.rollback()  # Rollback the transaction in case of an error
        response['status'] = 422
        response['message'] = str(e)

    finally:
        if conn and session is None:
            conn.close()  # Close the connection only if a session wasn't passed

    return response