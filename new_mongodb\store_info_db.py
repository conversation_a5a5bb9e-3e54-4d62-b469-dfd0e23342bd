from new_mongodb import StoreAdminDBCollections, process_data
import new_mongodb

def fetch_all_product_listing_types(store_id):
    db = new_mongodb.get_admin_db_client_for_store_id(store_id)
    coll = db[StoreAdminDBCollections.STORE_INFO_COLLECTION]
    stores_info = []
    cur = coll.find({"type": {"$in": ["featured_products", "new_products", "preorder_products", "liquidated_products"]}})
    for row in cur:
        data = process_data(row)
        stores_info.append(data)
    return stores_info