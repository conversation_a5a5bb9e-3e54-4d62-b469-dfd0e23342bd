import bcrypt
import base64
import datetime
import uuid
import logging
import projects
from mongo_db import user_db, role_db, client_apps_db
from utils import jwt_util, redis_util, bc
from iam import user_service
import appconfig
from utils.common import parse_json
import requests
from urllib.parse import urlparse
from mongo_db.side_menu import get_side_menu

logger = logging.getLogger()

jwt_token_algorithm = 'HS256'
admin_token_expiry_duration = 7*24*60*60
store_token_expiry_duration = 7*24*60*60
customer_login_token_expiry_duration = 60*2


def get_jwt_secret(store_id):
    payload = redis_util.get_store_secret(store_id)
    if not payload:
        payload = {
            "client_id": jwt_util.secret_generator(24),
            "secret": jwt_util.secret_generator(),
        }
        redis_util.update_store_secret(store_id, payload)
    return payload


def admin_validation(password):
    result = False

    if password == appconfig.get_admin_secret():
        user = {
            "username": appconfig.get_admin_user(),
            "password": appconfig.get_admin_secret(),
            "role": appconfig.get_admin_user_role()
        }
        user_service.create_user(user)
        result = True

    return result


def generate_access_token(username):
    secret_payload = get_jwt_secret(appconfig.get_admin_store_key())
    client_id = secret_payload["client_id"]
    secret = secret_payload["secret"]
    iat = int(datetime.datetime.now().timestamp())
    exp = iat + admin_token_expiry_duration
    payload = dict(iss=client_id, iat=iat, exp=exp,
                   jti=uuid.uuid4().hex, username=username)
    return client_id, jwt_util.encode(payload, secret, algorithm=jwt_token_algorithm), exp


def validate_credentials(username, password):
    result = False
    user = user_db.fetch_user_by_username(username)

    if user:
        if user['status'] != 'active':
            return False, None, None
        salt = user['yoyo']['key']
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        result = hashed == user['yoyo']['value']
    else:
        admin_user = appconfig.get_admin_user()
        if username == admin_user:
            result = admin_validation(password)
        else:
            return False, None, None

    return result, user['role_id'], user['_id']


def get_user_by_username(username):
    result = False
    user = user_db.fetch_user_by_username(username)

    if user:
        if not user['status'] == 'active':
            result = False
        result = True
    else:
        admin_user = appconfig.get_admin_user()
        if username == admin_user:
            result = True

    if result:
        return result, user['role_id'], user['_id'], user['name']
    else:
        return result, None, None


def decode_user_creds(auth_header):
    result = None
    if auth_header:
        token = auth_header.split(" ")
        auth_token = None
        if len(token) == 1:
            auth_token = token[0]
        elif len(token) == 2:
            auth_token = token[1]
        if auth_token:
            payload = base64.b64decode(auth_token).decode("utf-8")
            result = payload.split(":")

    return result


def get_user_permissions(data):
    permissions = {}

    for item in data:
        # parent permissions ...
        for key in data[item][item]:
            permissions.update(
                {data[item]['name'] + "-" + key: data[item][item][key]})

        # child permissions ...
        if data[item]['children']:
            for child in data[item]['children']:
                for key in data[item]['children'][child][child]:
                    permissions.update(
                        {child + "-" + key: data[item]['children'][child][child][key]})

    return permissions


def build_side_menu(store, username, permissions):
    store_side_menu = get_side_menu()
    # project_menu, project_permissions = projects.build_projects_menu(username)
    side_menu = {}
    user_permissions = {}
    for menu_item in store_side_menu:
        if (menu_item == '_id'
            or menu_item == 'store_id'
            or menu_item == 'created_at' 
            or menu_item == 'updated_at'):
            continue
        else:
            item = permissions[menu_item]
            children = item['children']
            item_name = item['name']
            parent_menu_item = {
                'label': store_side_menu[item_name]['label'],
                'route': store_side_menu[item_name]['route'],
                'icon': store_side_menu[item_name]['icon'],
                'children': {}
            }

            has_permission = False
        
            if len(children) > 0:
                flag = False

                for child in children:
                    child_name = child

                    for key, value in children.items():
                        if child_name == key:
                          if key in value:
                            for permission_key, permission_value in value[key].items():
                                user_permissions.update({child_name + "-" + permission_key: permission_value})
                 
                    if child_name in children and child_name in children[child_name] and children[child_name][child_name]['read']:
                        flag = True
                        if 'children' in store_side_menu[item_name] and child in store_side_menu[item_name]['children']:
                            side_menu_child = store_side_menu[item_name]['children'][child]

                            parent_menu_item['children'].update({
                                    child_name: {
                                        "label": side_menu_child['label'],
                                        "route": side_menu_child['route'],
                                        "icon": side_menu_child['icon'],
                                        'children': side_menu_child['children']
                                    }
                                })

                if flag == True:
                    has_permission = True
                    side_menu.update({item_name: parent_menu_item})

            else:
                if item[item_name]['read']:
                    has_permission = True
                    side_menu.update({item_name: parent_menu_item})

            if has_permission:
                for key, value in item[menu_item].items():
                    user_permissions.update({item['name'] + "-" + key: value})

            # if menu_item == "cms" and len(project_menu) > 0:
            #     side_menu.update({"Projects":project_menu})
            #     user_permissions.update(project_permissions)


    return side_menu, user_permissions


def login(auth_header, store):
    content = {
        "status": 401
    }
    creds = decode_user_creds(auth_header)
    if creds and len(creds) == 2:
        username = creds[0]
        secret = creds[1]
        result, role_id, user_id = validate_credentials(username, secret)
        if result:
            user_db.update_user_last_login(user_id)
            user = user_db.fetch_user_by_username(username)
            content['name']=user['name']
            user_role = role_db.fetch_role_by_id(role_id)
            content['side_menu'], content['permissions']  = build_side_menu(store, username, user_role['permissions'])
            #content['permissions'] = get_user_permissions(user_role['permissions'])
            content["status"] = 200
            client_id, token, exp = generate_access_token(username)
            content["accessToken"] = token
            content["expiresAt"] = exp
            content["username"] = username
            content['is_super_admin'] = user_role['is_super_admin']
            content['is_administrator_access'] = user_role['is_administrator_access']
            redis_util.add_access_token(username, client_id, token, exp)
    return content


def googleLogin(body, store):
    content = {
        "status": 401
    }
    validation_url = 'https://www.googleapis.com/oauth2/v3/tokeninfo'
    google_creds = store.get('google_oauth_creds', None)

    if not google_creds:
        google_creds = store.get('google_creds', None)

    google_client_id = google_creds['client_id']
    client_secret = google_creds['client_secret']

    access_token = body['accessToken']

    params = {
        'access_token': access_token,
        'client_id': google_client_id,
        'client_secret': client_secret
    }

    response = requests.get(validation_url, params=params)
    valid_response = response.json()

    if response.status_code == 200 and valid_response['aud'] == google_client_id:
        username = valid_response['email']
        result, role_id, user_id = get_user_by_username(username)
        if result:
            user_db.update_user_last_login(user_id)
            user_role = role_db.fetch_role_by_id(role_id)
            content['side_menu'], content['permissions']  = build_side_menu(store, username, user_role['permissions'])
            # content['side_menu'] = dataToSideMenu(
            #     user_role['permissions'], store)
            # content['permissions'] = dataToPermissions(
            #     user_role['permissions'])
            content["status"] = 200
            client_id, token, exp = generate_access_token(username)
            content["accessToken"] = token
            content["expiresAt"] = exp
            content["username"] = username
            content['is_super_admin'] = user_role['is_super_admin']
            content['is_administrator_access'] = user_role['is_administrator_access']
            redis_util.add_access_token(username, client_id, token, exp)

    return content


def logout(username, access_token):
    redis_util.delete_access_token(username, access_token)
    return True


def generate_store_user_access_token(store_id, customer_id, customer_email):
    secret_payload = get_jwt_secret(store_id)
    client_id = secret_payload["client_id"]
    secret = secret_payload["secret"]
    iat = int(datetime.datetime.now().timestamp())
    exp = iat + store_token_expiry_duration

    payload = dict(iss=client_id, iat=iat, exp=exp, jti=uuid.uuid4().hex,
                   store_id=str(store_id), customer_id=customer_id,
                   email=customer_email)

    return client_id, jwt_util.encode(payload, secret, algorithm=jwt_token_algorithm), exp


def generate_customer_login_token(customer_id, user_name, store_id, token_expiry):
    secret_payload = get_jwt_secret(store_id)
    client_id = secret_payload["client_id"]
    secret = secret_payload["secret"]
    iat = int(datetime.datetime.now().timestamp())
    exp = iat + token_expiry
    payload = dict(iss=client_id, iat=iat, exp=exp,
                   jti=uuid.uuid4().hex, customer_id=customer_id, username=user_name)
    return client_id, jwt_util.encode(payload, secret, algorithm=jwt_token_algorithm), exp


def store_customer_login(customer_id, store, user_name):
    result = {
        "status": 401,
    }
    if customer_id:
        client_id, token, exp = generate_customer_login_token(
            customer_id, user_name, store['id'], customer_login_token_expiry_duration)

        result['status'] = 200
        result['client_id'] = client_id
        result['token'] = token
        result['exp'] = exp

    return result


def parse_bearer_access_token(access_token):
    if access_token:
        token = access_token.split(" ")
        if len(token) == 1:
            return token[0]
        if len(token) == 2:
            return token[1]
    return access_token


def parse_access_token(store_id, access_token):
    start_time = datetime.datetime.now().timestamp()
    parse_token = None
    secret_payload = get_jwt_secret(store_id)
    client_id = None
    client_secret = None
    if secret_payload:
        client_id = secret_payload["client_id"]
        client_secret = secret_payload["secret"]
        
    parse_token = jwt_util.validate_jwt(
                access_token, client_id, client_secret)
        
    user = None
    if parse_token and "username" in parse_token:
        user = user_db.fetch_user_by_username(parse_token['username'])

    if appconfig.is_debug_enabled() or (user and user['status'] == 'active'):
        return parse_token
    else:
        return False

def operation_permission_check(target_url, method, permission):
    if method == 'GET':
        return permission.get("read", False)

    if method == 'POST' or method == 'PUT' or method == 'DELETE':
        if method == 'POST' and 'customers/login' in target_url:
            return True
        return permission.get("write", False)

def feature_access_check(api_endpoint, method, feature, permission):
    root_endpoint = permission.get("api", None)    
    if root_endpoint and api_endpoint.startswith(root_endpoint):
        root_permission =  permission.get(feature, None)
        
        if not operation_permission_check(api_endpoint, method, root_permission):
            return False
    
        children = permission.get("children", None)
        if children:
            for _sub_feature, _sub_feature_permission in children.items():
                if feature_access_check(api_endpoint, method, _sub_feature, _sub_feature_permission):
                    return True
        return True
    
    return False

def permission_check(url, method, username):
    user = user_db.fetch_user_by_username(username)

    if user and user['role_id']:
        role = role_db.fetch_role_by_id(user['role_id'])

        if not role:
            return False
        
        # when request is from admin.
        is_administrator_access = role.get('is_administrator_access', False)
        if is_administrator_access:
            return True

        # Find the position of the first "/" character after "admin/api"
        _url = urlparse(url)
        api_endpoint = _url.path

        api_base_path = appconfig.get_api_prefix()
        if api_base_path and api_base_path != "":
            api_endpoint = api_endpoint.replace(api_base_path, '')
            
        # Get the substring starting from that position
        permissions = role.get('permissions', {})
        for feature, permission in permissions.items():
            is_accessible = feature_access_check(api_endpoint, method, feature, permission)
            if is_accessible:
                return True

    return False

def validate_client(client_id, token):
    payload = None
    if client_id and token:
        client_app = client_apps_db.fetch_client_app(client_id)
        if client_app and client_app['access_token'] == token and client_app["is_active"]:
            payload = {
                "username": client_app["username"]
            }
    return payload

def build_bc_login_url(bc_api, customer_id, redirect_to=None):
    payload = dict(iss=bc_api['client_id'], iat=int(datetime.datetime.now().timestamp()), jti=uuid.uuid4().hex, 
                    operation='customer_login', store_hash=bc_api['store_hash'],
                    customer_id=customer_id, channel_id=bc_api['channel_id'])
    
    if redirect_to:
        payload['redirect_to'] = redirect_to

    return bc_api['store_url'] + "/login/token/" + jwt_util.encode(payload, bc_api['secret'], algorithm='HS256')