import datetime
from celery import Celery, current_task
from celery.schedules import crontab
from celery.signals import worker_ready
import appconfig
import logging

logger = logging.getLogger()

BUILD_STORE_TASK = "build_store"
UPDATE_PRODUCT_LISTING_ALL_STORE_CACHE_TASK = "update_all_stores_cache"
UPDATE_PRODUCT_LISTING_CACHE_TASK = "update_product_cache"
UPDATE_PRICE_LIST_TASK = "update_price_list"
FLUSH_CACHE_TASK = "flush_cache"
UPDATE_INVENTORY_CACHE_TASK = "update_inventory_cache"
DELETE_BC_CART_TASK = "delete_bc_cart_task"
BC_WEBHOOK_TASK = "webhook_update_product"
PURGE_TASK_LOG_TASK = "purge_task_log"
SEND_REPLENISHMENT_CSV_MAIL_TASK = "send_replenishment_csv_mail"
DAILY_SALES_REPLENISHMENT_CSV_MAIL_TASK = "daily_sales_replenishment_csv_mail"
NO_SOLD_PRODUCTS_ANALYTICS_CSV_MAIL_TASK = "no_sold_products_analytics_csv_mail"
SEND_REPLENISHMENT_DASHBOARD_CSV_MAIL_TASK = "send_replenishment_dashboard_csv_mail"
CREATE_PRODUCT_IN_BACKGROUND_TASK = "create_product_in_background"    
UPDATE_PROJECT_TICKET_TASK = "update_ticket"
UPDATE_RESOURCES_TASK = "update_resource"
UPDATE_CUSTOM_FIELD_RESOURCE_TASK = "update_custom_field_resource"
UPDATE_CUSTOM_FIELD_TASK = "update_custom_field"
UPDATE_PRICE_LIST_RULE_TASK = "update_price_list_rule"
GENERATE_ADMIN_APP_NOTIFICATION_TASK = "generate_admin_app_notification"
PRODUCT_CUSTOMER_TRACKING_CSV_MAIL_TASK = "send_product_customer_tracking_mail"
BULK_PRODUCTS_GLOBAL_REPORT_MAIL_TASK = "send_bulk_products_global_report_mail"
SEND_LIQUIDATED_PRODUCTS_CSV_MAIL_TASK = "send_liquidated_products_csv_mail"
PRICE_LIST_PRODUCTS_LOGS_TASK = "price_list_products_logs"
SEND_ORDER_PLACED_EMAIL_TO_COMPLIANCE_TASK  = "send_order_placed_email_to_compliance"
INSERT_CUSTOMER_PRICE_INTO_SHEET_TASK = "insert_customer_price_into_sheet"
UPDATE_CUSTOMER_PRICE_INTO_SHEET_TASK = "update_customer_price_into_sheet"
DELETE_CUSTOMER_PRICE_INTO_SHEET_TASK = "delete_customer_price_into_sheet"
UPDATE_PRICE_LIST_FROM_CSV = "update_price_list_from_csv"
PRICE_LIST_CSV_EXPORT_TASK = "price_list_csv_export"
UPDATE_PRICE_LIST_WITH_COST_PLUS_PERCENTAGE = "update_price_list_with_cost_plus_percentage"
CUSTOMER_PROFITABILITY_CSV_MAIL_TASK = "send_customer_profitability_csv_mail"
PRODUCT_WISE_PROFITABILITY_CSV_MAIL_TASK = "send_product_wise_profitability_csv_mail"
SUPPLIERS_PROFITABILITY_CSV_MAIL_TASK = "send_suppliers_profitability_csv_mail"
CLASSIFICATION_PROFITABILITY_CSV_MAIL_TASK = "send_classification_profitability_csv_mail"
ORDERS_PROFITABILITY_CSV_MAIL_TASK = "send_orders_profitability_csv_mail"
BRANDS_PROFITABILITY_CSV_MAIL_TASK = "send_brands_profitability_csv_mail"
DISCONTINUED_PRODUCTS_DATA_CSV_MAIL_TASK = "send_discontinued_products_data_csv_mail"
SEND_PROJECT_NOTIFICATION_TASK = "send_project_notification"
CHECK_PROMO_PRODUCT_AND_CREATE_TICKET_TASK = "check_promo_product_and_create_ticket"
SEND_BULK_ORDER_NOTIFICATION_TASK = "send_bulk_order_notification"

class TaskManager:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            logger.info('Creating the TaskManager')
            cls._instance = super(TaskManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        logger.info("Entering TaskManager")
        self._CELERY = Celery(appconfig.get_celery_app_name(), broker=appconfig.get_celery_broker_url(), 
                        backend=appconfig.get_celery_result_backend())
        
        logger.info("Exiting TaskManager")

    def get_celery(self):
        return self._CELERY

    def submit_task(self, task_name, args=(), queue=None):
        task = None
        if queue:
            task = self._CELERY.send_task(task_name, args, queue=queue)
        else:
            task = self._CELERY.send_task(task_name, args)

        return task

_TASK_MANAGER = TaskManager()

def send_task(task_name, args=(), queue=None):
    return _TASK_MANAGER.submit_task(task_name, args, queue=queue)

def start_periodic_tasks(store_id):
    args = (store_id,)
    send_task(BC_WEBHOOK_TASK, args)
    send_task(DELETE_BC_CART_TASK, args)

def get_scheduled_tasks():
    r = _TASK_MANAGER.get_celery().control.inspect()
    reserved_tasks = r.reserved()
    scheduled_tasks = r.scheduled()
    active_tasks = r.active()
    worker_tasks = {}
    for w, tasks in reserved_tasks.items():
        w_tasks = worker_tasks.get(w, [])
        for _task in tasks:
            _task["_type"] = "reserved"
            w_tasks.append(_task)
        worker_tasks[w] = w_tasks
    for w, tasks in scheduled_tasks.items():
        w_tasks = worker_tasks.get(w, [])
        for _task in tasks:
            _task["_type"] = "scheduled"
            w_tasks.append(_task)
        worker_tasks[w] = w_tasks

    for w, tasks in active_tasks.items():
        w_tasks = worker_tasks.get(w, [])
        for _task in tasks:
            _task["_type"] = "active"
            w_tasks.append(_task)
        worker_tasks[w] = w_tasks
    result = {}
    current_time = datetime.datetime.now()

    for w, tasks in worker_tasks.items():
        for _task in tasks:
            _task_request = _task.get("request", None)
            if _task_request:
                _request = result.get(_task_request["name"], {})
                _task_eta = _task.get('eta', None)
                t_diff = 0
                if _task_eta:
                    eta = datetime.datetime.fromisoformat(_task_eta).replace(tzinfo=None)
                    t_diff = (eta - current_time).total_seconds()
                store_id = None
                if len(_task_request['args']) > 0:
                    store_id = _task_request['args'][0]
                _request[_task["request"]["id"]] = {
                                    "id": _task_request["id"],
                                    "eta": _task.get('eta', None),
                                    "worker": w,
                                    "type": _task["_type"],
                                    "store_id": store_id,
                                    "seconds_to_run": t_diff,
                                    "min_to_run": t_diff/60,
                                    "hr_to_run": t_diff/3600
                                }
                result[_task["request"]["name"]] = _request
    return result

def cancel_duplicate_tasks():
    r = _TASK_MANAGER.get_celery().control.inspect()
    scheduled_tasks = r.scheduled()
    result = {}
    for w, tasks in scheduled_tasks.items():
        for task in tasks:
            _request = result.get(task["request"]["name"], None)
            if not _request:
                result[task["request"]["name"]] = {
                                    "id": task["request"]["id"],
                                    "eta": task['eta'],
                                    "worker": w,
                                    "obj": task
                                }
            else:
                _TASK_MANAGER.get_celery().control.revoke(task["request"]["id"], terminate=True)

    return result