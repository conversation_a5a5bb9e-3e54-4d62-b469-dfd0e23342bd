from flask_restful import Api, Resource
from iam import auth_service, TokenKeys
from utils import auth_util
import new_mongodb
import traceback
import logging

logger = logging.getLogger()

AUTH_TOKEN_HEADER_KEY = "X-AUTH-TOKEN"
TENANT_ID_HEADER_KEY = "X-TENANT-ID"
STORE_ID_HEADER_KEY = "X-STORE-ID"
ORIGIN_HEADER_KEY = "Origin"
CLIENT_ID_HEADER_KEY = "X-CLIENT-ID"

def validate_request_token(request):
    payload = None
    token = request.headers.get(AUTH_TOKEN_HEADER_KEY, None)
    client_id = request.headers.get(CLIENT_ID_HEADER_KEY, None)      
    tenant_id = request.headers.get(TENANT_ID_HEADER_KEY, None)        
    if token:
        if client_id:
            payload = auth_service.validate_client(client_id, token)
        elif tenant_id:
            payload = auth_service.validate_access_token(tenant_id, token)
    return payload

class APIResource(Resource):

    request_id = 1

    def get_store_id(self, request):
        store_id = request.headers.get(STORE_ID_HEADER_KEY, None)
        return store_id

    def get_tenant_id(self, token_payload):
        return token_payload[TokenKeys.TENANT_ID]

    def get_username(self, token_payload):        
        return token_payload[TokenKeys.USERNAME]

    def _validate_token(self, request):
        return validate_request_token(request)

    def execute_open_api_request(self, request, request_executor, *args):
        current_request_id = APIResource.request_id
        APIResource.request_id = APIResource.request_id + 1
        logger.debug("Entering execute_request")
        logger.info(f"{current_request_id} Serving {request.base_url}") 
        try:
            return request_executor(request, *args)
        except Exception as e:
            logger.error("APIResource.execute_request: Exception caught: " + traceback.format_exc())
            return {"message": "Something went wrong at server!!"}, 500
        finally:
            logger.info(f"{current_request_id} Finished Serving {request.base_url}")
            logger.debug("Exiting execute_request")

    def execute_request(self, request, request_executor, *args):
        current_request_id = APIResource.request_id
        APIResource.request_id = APIResource.request_id + 1
        logger.debug("Entering execute_request")
        logger.info(f"{current_request_id} Serving {request.base_url}") 
        try:
            payload = self._validate_token(request)
            if payload:
                return request_executor(request, payload, *args)
            return {"message": "Unauthorized. 1"}, 401
        except Exception as e:
            logger.error("APIResource.execute_request: Exception caught: " + traceback.format_exc())
            return {"message": "Something went wrong at server!!"}, 500
        finally:
            logger.info(f"{current_request_id} Finished Serving {request.base_url}")
            logger.debug("Exiting execute_request")

    def execute_store_request(self, request, request_executor, *args):
        current_request_id = APIResource.request_id
        APIResource.request_id = APIResource.request_id + 1
        logger.debug("Entering execute_request")
        logger.info(f"{current_request_id} Serving {request.base_url}") 
        try:
            payload = self._validate_token(request)    
            if payload:
                store_id = self.get_store_id(request)
                if store_id:
                    store = new_mongodb.get_store_by_id(store_id=store_id)
                    if store:
                        is_valid = auth_util.permission_check(request.url, request.method, payload['username'])
                        if is_valid:                                
                            return request_executor(request, payload, store, *args)
                        else:
                            return {'message': 'You do not have permission to perform this operation'}, 403
                return {"message": "Invalid store id."}, 404
            return {"message": "Unauthorized. 2"}, 401
        except Exception as e:
            logger.error("APIResource.execute_request: Exception caught: " + traceback.format_exc())
            return {"message": "Something went wrong at server!!"}, 500
        finally:
            logger.info(f"{current_request_id} Finished Serving {request.base_url}")
            logger.debug("Exiting execute_request")



def register_v2_apis(api):
    version = "/v2"
    global_api_prefix = version + "/global"
    
    from apiv2 import global_api
    endpoint = global_api_prefix + "/login"
    api.add_resource(global_api.LoginV2, endpoint)

    endpoint = global_api_prefix + "/login/google"
    api.add_resource(global_api.GoogleLoginV2, endpoint)

    endpoint = global_api_prefix + "/logout"
    api.add_resource(global_api.LogoutV2, endpoint)
    
    endpoint = global_api_prefix + "/store-admin"
    api.add_resource(global_api.StoreAdminAPI, endpoint)

    endpoint = global_api_prefix + "/permissions"
    api.add_resource(global_api.UserPermissionAPI, endpoint)

    endpoint = global_api_prefix + "/admin/user/login"
    api.add_resource(global_api.AdminUserLoginV2, endpoint)

    from apiv2 import callback_api
    endpoint = version + "/callback"
    api.add_resource(callback_api.CallbackAPI, endpoint)

    from apiv2 import order_api
    endpoint = version + "/orders/<string:order_id>"
    api.add_resource(order_api.OrderV2, endpoint)
