import datetime
from flask import request
import logging
from api import APIResource
from analytics import coupon_code_discount_report

logger = logging.getLogger()

class CustomerGroupsCouponDiscountReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering CustomerGroupsCouponDiscountReport GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            customer_group_id = query_params.get('customer_group_id', '')

            if start_date and end_date:
                res = coupon_code_discount_report.get_customer_groups_coupon_discount_report(store['id'], start_date, end_date, sort_array, customer_group_id)
                return res
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting CustomerGroupsCouponDiscountReport GET {end_time-start_time}")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class CustomerTypesCouponDiscountReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering CustomerTypesCouponDiscountReport GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []

            if start_date and end_date:
                res = coupon_code_discount_report.get_customer_types_coupon_discount_report(store['id'], start_date, end_date, sort_array)
                return res
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting CustomerTypesCouponDiscountReport GET {end_time-start_time}")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class SalesRepCouponDiscountReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering SalesRepReport GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            search = query_params.get('search', '').strip()
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            sales_rep_type = query_params.get('sales_rep_type', '').strip()

            if start_date and end_date:
                res = coupon_code_discount_report.get_sales_rep_coupon_discount_report(store['id'], page, limit, sort_array, search, start_date, end_date, sales_rep_type)
                return res
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting SalesRepReport GET {end_time-start_time}")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class SalesRepTypeCouponDiscountReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering SalesRepTypeCouponDiscountReport GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []

            if start_date and end_date:
                res = coupon_code_discount_report.get_sales_rep_type_coupon_discount_report(store['id'], start_date, end_date, sort_array)
                return res
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting SalesRepTypeCouponDiscountReport GET {end_time-start_time}")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class CouponCodeDiscountReport(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering CouponCodeDiscountReport GET")
        start_time = datetime.datetime.now().timestamp()
        try:
            query_params = request.args.to_dict()

            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            search = query_params.get('search', '').strip()

            if start_date and end_date:
                res = coupon_code_discount_report.get_coupon_code_discount_report(store['id'], start_date, end_date, sort_array, search, page, limit)
                return res
            return {'message': 'Invalid request.'}, 400
        finally:
            end_time = datetime.datetime.now().timestamp()
            logger.info(f"Exiting CouponCodeDiscountReport GET {end_time-start_time}")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class CouponDiscountCharts(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering CouponDiscountCharts GET")
        try:
            query_params = request.args.to_dict()

            start_date = query_params.get('start_date', None)
            end_date = query_params.get('end_date', None)
            period = query_params.get('period', 'day').strip()
            report_type = query_params.get('report_type', 'customer_group').strip()
            customer_group_id = query_params.get('customer_group_id', '')
            customer_type = query_params.get('customer_type', '').strip()
            sales_rep_email = query_params.get('rep_email', '').strip()
            sales_rep_type = query_params.get('sales_rep_type', '').strip()
            coupon_code = query_params.get('coupon_code', '').strip()

            if start_date and end_date:
                res = coupon_code_discount_report.get_coupon_discount_charts(store['id'], start_date, end_date, period, report_type, customer_group_id, customer_type, sales_rep_email, sales_rep_type, coupon_code)
                if res["status"] == 200:
                    return {"data": res["data"]}, 200
                else:
                    return {"message": res["message"]}, res["status"]
            return {'message': 'Invalid request.'}, 400
        finally:
            logger.info(f"Exiting CouponDiscountCharts GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)