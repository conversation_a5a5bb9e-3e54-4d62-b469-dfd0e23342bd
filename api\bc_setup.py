from flask import request, make_response, json
from flask_restful import Api, Resource
import logging
import traceback
from api import APIResource
from utils import webhook_util

logger = logging.getLogger()

class Webhook(APIResource):

    def put_executor(self, request, token_payload, store):
        logger.debug("Entering Webhook PUT")
        try:
            data = request.get_json(force=True)
            if data:
                webhooks = data.get("webhooks", [])
                res = webhook_util.update_registered_webhook(store=store, webhooks=webhooks)
                return res, 200
            return "Invalid request", 409
        finally:
            logger.debug("Exiting Webhook PUT")

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Webhook GET")
        try:
            res = webhook_util.get_registered_webhook(store=store)
            return res, 200
        finally:
            logger.debug("Exiting Webhook GET")

    def delete_executor(self, request, token_payload, store):
        logger.debug("Entering Webhook DELETE")
        try:
            data = request.get_json(force=True)
            if data:
                webhooks = data.get("webhooks", [])
                res = webhook_util.delete_registered_webhook(store=store, webhooks=webhooks)
                return res, 200
            else:
                return "Invalid request", 409
        finally:
            logger.debug("Exiting Webhook DELETE")

    def post_executor(self, request, token_payload, store):
        logger.debug("Entering Webhook POST")
        try:
            data = request.get_json(force=True)
            scope = None
            if data:
                scope = data.get("scope", None)
            res = webhook_util.register_webhook(store=store, scope=scope)
            return res, 200
        finally:
            logger.debug("Exiting Webhook POST")

    def post(self):
        return self.execute_store_request(request, self.post_executor)

    def put(self):
        return self.execute_store_request(request, self.put_executor)

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def delete(self):
        return self.execute_store_request(request, self.delete_executor)