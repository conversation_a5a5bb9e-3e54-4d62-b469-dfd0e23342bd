import json
import redis
from datetime import datetime
import appconfig
import logging
import threading
from new_mongodb import get_redis_config

logger = logging.getLogger()

STORE_CACHE = "store_cache"
GRAPHQL_TOKEN_HSET = "graphql_token"
ACCESS_TOKENS_HSET = "access_token:"
JWT_SECRET_HSET = "jwt_secrets_"
SKU_INVENTORY = "inventory_"
PRODUCT_UPDATE_SET = "webhook_product_update_"
PRODUCT_UPDATE_QUEUE = "webhook_product_update_queue_"
SALESFORCE_CUSTOMERS_KEY = "salesforce_customers"
WEBHOOK_STATUS = "whs_"
CART_DELETE_QUEUE = "cart_delete_queue_"
WEBSOCKET_USER_REQUESTID_HKEY = "awsursid"
WEBSOCKET_REQUESTID_USER_HKEY = "awsrsidu"
SALESFORCE_TOKEN_KEY = 'salesforce_token'

class AuthRedisClient:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            logger.info('Creating the AuthRedisClient')
            cls._instance = super(AuthRedisClient, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        logger.info("Entering AuthRedisClient")
        self.redis_client = redis.Redis(host=appconfig.get_redis_host(),
                    port=appconfig.get_redis_port(), db=appconfig.get_redis_db())
        logger.info("Exiting AuthRedisClient")

    def get_redis_client(self):
        return self.redis_client
    
class RedisCLI:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            logger.info('Creating the RedisCLI')
            cls._instance = super(RedisCLI, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        logger.info("Entering RedisCLI")
        self._lock = threading.Lock()
        self._redis_clients = {}
        logger.info("Exiting RedisCLI")
    
    def get_redis_client(self, store_id):
        redis_client = self._redis_clients.get(store_id, None)
        if not redis_client:
            try:
                self._lock.acquire()
                redis_client = self._redis_clients.get(store_id, None)
                if not redis_client:
                    redis_config = get_redis_config(store_id)
                    if redis_config:
                        _host = redis_config.get("host")
                        _port = redis_config.get("port")
                        _db = redis_config.get("cache_db")
                        redis_client = redis.Redis(host=_host, port=_port, db=_db)
                        self._redis_clients[store_id] = redis_client
            finally:
                self._lock.release()
        return redis_client

__auth_redis_client = AuthRedisClient()
__store_redis_client = RedisCLI()

def get_redis_client(store_id):
    return __store_redis_client.get_redis_client(str(store_id))

def get_hset_json(store_id, hset_name, key):
    payload = None
    content = get_redis_client(store_id).hget(hset_name, key)
    if content:
        payload = json.loads(content.decode('utf-8'))
    return payload

def get_graphql_token(store_id):
    return get_hset_json(store_id, str(store_id), GRAPHQL_TOKEN_HSET)

def set_graphql_token(store_id, token, expires_at):
    payload = {
        "token": token,
        "expires_at": expires_at
    }
    content = json.dumps(payload)
    client = get_redis_client(store_id)
    client.hset(str(store_id), GRAPHQL_TOKEN_HSET, content)
    
def update_store_secret(store_id, payload, ttl=0):
    hkey = JWT_SECRET_HSET + str(store_id)
    content = json.dumps(payload)
    client = __auth_redis_client.get_redis_client()
    client.hset(hkey, str(store_id), content)
    if ttl > 0:
        client.expire(name=hkey, time=ttl)

def get_store_secret(store_id):
    hkey = JWT_SECRET_HSET + str(store_id)
    content = __auth_redis_client.get_redis_client().hget(hkey, str(store_id))
    if content:
        content = json.loads(content.decode('utf-8'))
    return content

def get_access_token_cache_key(username, access_token):
    key = ACCESS_TOKENS_HSET + username + ":" + access_token
    return key

def add_access_token(username, client_id, access_token, expires_at):
    time_to_expire = expires_at - int(datetime.now().timestamp())    
    if time_to_expire > 0:
        rclient = __auth_redis_client.get_redis_client()
        keys = rclient.keys(get_access_token_cache_key(username, "") + "*")
        if len(keys) > 0:
            rclient.delete(*keys)
        rclient.set(get_access_token_cache_key(username, access_token), 
                                    client_id, ex=time_to_expire)

def get_access_token(username, access_token):
    rclient = __auth_redis_client.get_redis_client()
    client_id = rclient.get(get_access_token_cache_key(username, access_token))
    if client_id:
        client_id = client_id.decode('utf-8')
    return client_id

def delete_access_token(username, access_token):
    hkey = get_access_token_cache_key(username, access_token)
    rclient = __auth_redis_client.get_redis_client()
    rclient.delete(hkey)

def clear_access_token_cache():
    rclient = __auth_redis_client.get_redis_client()
    keys = rclient.keys(ACCESS_TOKENS_HSET + "*")
    if len(keys) > 0:
        rclient.delete(*keys)

def queue_webhook_product_update(store_id, product_id, payload):
    skey = PRODUCT_UPDATE_SET + str(store_id)
    cli = get_redis_client(store_id)
    count = cli.sadd(skey, product_id) 
    if count == 1:
        qkey = PRODUCT_UPDATE_QUEUE + str(store_id)
        payload['product_id'] = product_id
        content = json.dumps(payload)
        cli.lpush(qkey, content)

def dqueue_webhook_product_update(store_id):
    qkey = PRODUCT_UPDATE_QUEUE + str(store_id)
    cli = get_redis_client(store_id)
    size = cli.llen(qkey)
    result = []
    if size > 0:
        skey = PRODUCT_UPDATE_SET + str(store_id)
        elements = cli.rpop(qkey, size)
        for el in elements:
            content = json.loads(el.decode('utf-8'))
            cli.srem(skey, content['product_id']) 
            result.append(content)
    return result

def update_sku_invetory_cache(store_id, sku_inventory):
    hkey = SKU_INVENTORY + str(store_id)
    cli = get_redis_client(store_id)
    for sku, inventory in sku_inventory.items():
        cli.hset(hkey, sku, inventory)

def get_sku_invetory(store_id, sku):
    hkey = SKU_INVENTORY + str(store_id)
    cli = get_redis_client(store_id)
    inv = cli.hget(hkey, sku)
    if inv:
        inv = int(inv)
    else:
        inv = 0
    return inv

def get_skus_invetory(store_id, sku_list=[]):
    inv_list = []
    if sku_list and len(sku_list) > 0:
        hkey = SKU_INVENTORY + str(store_id)
        cli = get_redis_client(store_id)
        res = cli.hmget(hkey, sku_list)
        for inv in res:
            if inv:
                inv = int(inv)
            else:
                inv = 0
            inv_list.append(inv)
    return inv_list

def get_salesforce_customer(store_id, customer_id):
    customer = get_redis_client(store_id).hget(SALESFORCE_CUSTOMERS_KEY, str(customer_id))
    if customer:
        customer = json.loads(customer.decode('utf-8'))
    return customer

def get_salesforce_all_customer(store_id):
    customers = get_redis_client(store_id).hgetall(SALESFORCE_CUSTOMERS_KEY)
    all_customer = []    
    if customers:
        for customer in customers.values():
            c = json.loads(customer.decode('utf-8'))
            all_customer.append(c)
    return all_customer

def get_webhook_status(store, scope):
    hkey = WEBHOOK_STATUS + store['id']
    content = get_redis_client(store['id']).hget(hkey, scope)
    
    # Default webhook status in case no data is found
    default_status = {
        "count": 0,
        "last_webhook_call": None,
        "last_webhook_succeeded": None,
        "error": None
    }
    
    if content:
        try:
            # Parse and return existing status
            return json.loads(content.decode('utf-8'))
        except json.JSONDecodeError:
            # Return default status if JSON decoding fails
            return default_status
    
    return default_status

def queue_cart_delete(store_id, cart_id):
    qkey = CART_DELETE_QUEUE + str(store_id)
    get_redis_client(store_id).lpush(qkey, cart_id)

def update_user_websocket_sid(username, request_sid):
    redis_cli = __auth_redis_client.get_redis_client()
    old_request_id = redis_cli.hget(WEBSOCKET_USER_REQUESTID_HKEY, username)
    if old_request_id:
        old_request_id = old_request_id.decode('utf-8')
        redis_cli.hdel(WEBSOCKET_REQUESTID_USER_HKEY, old_request_id)

    redis_cli.hset(WEBSOCKET_USER_REQUESTID_HKEY, username, request_sid)
    redis_cli.hset(WEBSOCKET_REQUESTID_USER_HKEY, request_sid, username)

def get_websocket_request_sid_for_username(username):
    redis_cli = __auth_redis_client.get_redis_client()
    req_id = redis_cli.hget(WEBSOCKET_USER_REQUESTID_HKEY, username)
    if req_id:
        req_id = req_id.decode('utf-8')
    return req_id

def get_websocket_username_for_request_sid(request_sid):
    redis_cli = __auth_redis_client.get_redis_client()
    username = redis_cli.hget(WEBSOCKET_REQUESTID_USER_HKEY, request_sid)
    if username:
        username = username.decode('utf-8')
    return username


def delete_websocket_connection(request_sid):
    redis_cli = __auth_redis_client.get_redis_client()
    redis_cli.hdel(WEBSOCKET_REQUESTID_USER_HKEY, request_sid)
    username = redis_cli.hget(WEBSOCKET_REQUESTID_USER_HKEY, request_sid)
    if username:
        username = username.decode('utf-8')
        redis_cli.hdel(WEBSOCKET_USER_REQUESTID_HKEY, username)
    
def delete_access_tokens_for_users(usernames):
    rclient = __auth_redis_client.get_redis_client()
    keys_to_delete = []
    for username in usernames:
        keys = rclient.keys(get_access_token_cache_key(username, "") + "*")
        if keys:
            keys_to_delete.extend(keys)
    if keys_to_delete:
        rclient.delete(*keys_to_delete)    

def get_salesforce_token(store_id):
    token = get_redis_client(store_id).get(SALESFORCE_TOKEN_KEY)
    if token:
        token = token.decode('utf-8')
    return token

def update_salesforce_token(store_id, token):
    get_redis_client(store_id).set(SALESFORCE_TOKEN_KEY, token)