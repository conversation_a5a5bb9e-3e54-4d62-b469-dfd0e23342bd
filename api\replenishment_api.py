from flask import request
import logging
import traceback
from api import APIResource
from analytics import replenishment, user_supplier_mapping
from new_mongodb import store_admin_db
from products.all_products import products_list
from iam import user_service
from services import products_instock_notify_service


logger = logging.getLogger()

class PostGreSql(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Postgresql Data GET")
        try:
            query_params = request.args.to_dict()
            res = replenishment.get_data_query(store, query_params)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Postgresql GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class ReplenishmentData(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Replenishment Data GET")
        try:
            query_params = request.args.to_dict()
            res = replenishment.get_replenishment_data(store, query_params)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Replenishment GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ReplenishmentProductsData(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Replenishment Data GET")
        try:
            query_params = request.args.to_dict()
            res = replenishment.get_replenishment_products_data(store, query_params)

            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Replenishment GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class ReplenishmentFilters(APIResource):
    def get_executor(self, request, token_payload, store, token=None):
        logger.debug("Entering Replenishment Data POST")
        try:
            # req_body = request.get_json(force=True)
            req_body = request.args.to_dict()
            if req_body and 'primary_supplier' in req_body:
                res = replenishment.get_replenishment_filters(store, req_body)
                if res['status'] == 200:
                    return {"data": res['data']}, 200
                else:
                    return {"message": res['message']}, 422
            else:
                return {"message": "Please ensure that your request body is correct."}, 400
        finally:
            logger.debug("Exiting Replenishment POST")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class ReplenishmentAggregateData(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Replenishment Aggregate Data GET")
        try:
            query_params = request.args.to_dict()
            res = replenishment.get_replenishment_aggregate_data(store, query_params)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Replenishment Aggregate GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ReplenishmentProductsInstockNotifyDetails(APIResource):
    def get_executor(self, request, token_payload, store, parent_sku):
        logger.debug(f"Entering ReplenishmentProductsInstockNotifyDetails GET for parent_sku: {parent_sku}")
        try:
            if not parent_sku:
                return {"message": "Please enter parent_sku as a path parameter"}, 400
            
            query_params = request.args.to_dict()
            variant_sku = query_params.get('variant_sku', None)
            
            res = replenishment.get_products_instock_notify_details(store, parent_sku, variant_sku)
            
            if res['status'] == 200:
                return {'data': res['data']}, res['status']
            else:
                return {'message': res['message']}, res['status']
            
        except Exception as e:
            logger.error(f"Error in ReplenishmentProductsInstockNotifyDetails GET: {str(e)}")
            return {'message': 'Internal server error'}, 500
        finally:
            logger.debug(f"Exiting ReplenishmentProductsInstockNotifyDetails GET for parent_sku: {parent_sku}")

    def get(self, parent_sku):
        return self.execute_store_request(request, self.get_executor, parent_sku)

class ReplenishmentOutOfStockOccurenceDetails(APIResource):
    def get_executor(self, request, token_payload, store, parent_sku):
        logger.debug(f"Entering ReplenishmentOutOfStockOccurenceDetails GET for parent_sku: {parent_sku}")
        try:
            if not parent_sku:
                return {"message": "Please enter parent_sku as a path parameter"}, 400
            
            query_params = request.args.to_dict()
            variant_sku = query_params.get('variant_sku', '')
            
            res = replenishment.get_replenishment_out_of_stock_occurence_details(store['id'], parent_sku, variant_sku)
            
            if res['status'] == 200:
                return {'data': res['data']}, res['status']
            else:
                return {'message': res['message']}, res['status']
            
        except Exception as e:
            logger.error(f"Error in ReplenishmentOutOfStockOccurenceDetails GET: {str(e)}")
            return {'message': 'Internal server error'}, 500
        finally:
            logger.debug(f"Exiting ReplenishmentOutOfStockOccurenceDetails GET for parent_sku: {parent_sku}")

    def get(self, parent_sku):
        return self.execute_store_request(request, self.get_executor, parent_sku)

class ZohoReturnsData(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Zoho Returns Data GET")
        try:
            query_params = request.args.to_dict()
            variant_sku = query_params.get('variant_sku', '')
            returns_type = query_params.get('returns_type', '')
            if variant_sku:
                res = replenishment.get_zoho_returns_data(store, variant_sku, returns_type)
                if res['status'] == 200:
                    return {"data": res['data']}, 200
                else:
                    return {"message": res['message']}, res['status']
            else:
                return {"message": "Please pass variant_sku in the query parameters."}, 400
        finally:
            logger.debug("Exiting Zoho Returns Data GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ZohoReturnsParentData(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Zoho Returns Parent Data GET")
        try:
            query_params = request.args.to_dict()
            parent_sku = query_params.get('parent_sku', '')
            if parent_sku:
                res = replenishment.get_zoho_returns_parent_data(store['id'], parent_sku)
                if res['status'] == 200:
                    return {"data": res['data']}, 200
                else:
                    return {"message": res['message']}, res['status']
            else:
                return {"message": "Please pass parent_sku in the query parameters."}, 400
        finally:
            logger.debug("Exiting Zoho Returns Parent Data GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class DailySoldReplenishmentAggregateData(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Replenishment Aggregate Data GET")
        try:
            query_params = request.args.to_dict()
            res = replenishment.get_replenishment_daily_sold_aggregate_data(store, query_params)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Replenishment Aggregate GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class ReplenishmentAggregateChild(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Replenishment Aggregate Child GET")
        try:
            query_params = request.args.to_dict()
            res = replenishment.get_replenishment_aggregate_child_data(store, query_params)
            
            if res['status'] == 200:
                return res['data'], 200
            elif res['status'] == 204:
                return res['message'], 204
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Replenishment Aggregate Child GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ReplenishmentAggregateDataCsv(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Replenishment Aggregate Data CSV GET")
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            query_params = request.args.to_dict()
            query_params['username'] = username
            res = replenishment.get_replenishment_aggregate_data_csv(store, query_params)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, res["status"]
        finally:
            logger.debug("Exiting Replenishment Aggregate CSV GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class DailySoldReplenishmentAggregateDataCsv(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Replenishment Aggregate Data CSV GET")
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            query_params = request.args.to_dict()
            query_params['username'] = username
            res = replenishment.get_replenishment_daily_sold_aggregate_data_csv(store, query_params)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, res["status"]
        finally:
            logger.debug("Exiting Replenishment Aggregate CSV GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class DiscontinuedProductsDataCsv(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Discontinued Products Data CSV GET")
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            query_params = request.args.to_dict() or {}
            query_params['username'] = username
            res = replenishment.get_discontinued_products_data_csv(store, query_params)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, res["status"]
        finally:
            logger.debug("Exiting Discontinued Products Data CSV GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ReplenishmentDashboardDataCsv(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Replenishment Dashboard CSV GET")
        try:
            username = token_payload.get('username') if token_payload and 'username' in token_payload else ''
            if not username:
                return {"message": "Unauthorized access."}, 401
            query_params = request.args.to_dict()
            query_params['username'] = username
            res = replenishment.get_replenishment_dashboard_data_csv(store, query_params)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Replenishment Dashboard CSV GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ReplenishmentAggregateCsv(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Replenishment Aggregate CSV GET")
        try:            
            res = replenishment.getCSV(store)
            return res
        finally:
            logger.debug("Exiting Replenishment Aggregate CSV GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
class SupplierList(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Suppliers GET")
        try:
            res = user_supplier_mapping.get_supplier_list(store)
            
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Suppliers GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)

class AllUsersDropDown(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering All users dropdown GET")
        try:
            users = user_supplier_mapping.get_all_admin_app_users(store)
            if users['status'] == 200:
                return {"data": users['data']}, 200
            else:
                return {"data": users['data']}, 200
        finally:
            logger.debug("Exiting All users dropdown GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)

class UserSupplierMapping(APIResource):
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering User Supplier Mapping POST")
        try:
            payload = request.get_json(force=True)
            if payload:
                result = user_supplier_mapping.save_user_supplier_mapping_data(store, payload, False)
                
                if result['status'] == 200:
                    return {"message": "Data Saved successfully."}, 200
                else:
                    return {'message': result['message']}, result['status']
            else:    
                return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting User Supplier Mapping GET")


    def get_executor(self, request, token_payload, store):
        logger.debug("Entering User Supplier Mapping GET")
        try:
            res = user_supplier_mapping.get_user_supplier_mapping_data(store)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting User Supplier Mapping GET")
    

    def delete_executor(self, request, token_payload, store):
        logger.debug("Entering User Supplier Mapping Delete Record")
        try:
            payload = request.get_json(force=True)
            if payload:
                user_supplier_mapping.delete_user_record(store, payload)
                return {"message": "Record deleted successfully"}, 200
            return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting User Supplier Mapping Delete Record")


    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def delete(self):
        return self.execute_store_request(request, self.delete_executor)
    
class MappedUsers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Mapped Users GET")
        try:
            res = user_supplier_mapping.get_mapped_users(store)
            
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Mapped Users GET")
    
    def get(self):
        return self.execute_store_request(request, self.get_executor)

class UserMapping(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Mapped Users GET")
        try:
            query_params = request.args.to_dict()
            res = user_supplier_mapping.get_single_user(store, query_params['user'])
            
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Mapped Users GET")
    
    def put_executor(self, request, token_payload, store):
        logger.debug("Entering User Supplier Mapping PUT")
        try:
            payload = request.get_json(force=True)
            if payload:
                result = user_supplier_mapping.save_user_supplier_mapping_data(store, payload, True)
                
                if result['status'] == 200:
                    return {"message": "Data Saved successfully."}, 200
                else:
                    return {'message': result['message']}, result['status']
            else:
                return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting User Supplier Mapping PUT")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
    def put(self):
        return self.execute_store_request(request, self.put_executor)
    
class TableColumnsListing(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering user specific columns GET")
        try:
            query_params = request.args.to_dict()
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            if user is None:
                return {"message": "Unauthorized access."}, 401
            res = replenishment.get_table_columns(store, user, query_params)
            
            if res['status'] == 200:
                return res['data'], 200
        finally:
            logger.debug("Exiting user specific columns GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class TableColumns(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Table Columns SAVE")
        try:
            query_params = request.args.to_dict()
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            if user is None:
                return {"message": "Unauthorized access."}, 401
            if query_params:
                result = replenishment.save_table_columns(store, query_params, user)
                if result['status'] == 200:
                    return {"message": result['message']}, 200
                else:
                    return {"message": result['message']}, result['status']
            else:    
                return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting Table Columns SAVE")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
class BackOrderQtySkuValidate(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering backorder sku validate GET")
        try:
            query_params = request.args.to_dict()
            sku = query_params.get('sku', '')
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            if user is None:
                return {"message": "Unauthorized access."}, 401
            res = replenishment.validate_sku(store, sku, table='replenishment_backorder_variants')
            
            if res['status'] == 200:
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting backorder sku validate GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class BackOrderQty(APIResource):
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering backorder sku qty POST")
        try:
            payload = request.get_json(force=True)
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            if user is None:
                return {"message": "Unauthorized access."}, 401
            if payload:
                result = replenishment.save_replenishment_sku_qty(store, payload, user, table='replenishment_backorder_variants')
                if result['status'] == 200:
                    return {"message": result['message'], "records_processed": result['records_processed']}, 200
                else:
                    return {"message": result['message'], "records_processed": result['records_processed']}, result['status']
            else:    
                return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting backorder sku qty POST")

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering backorder list GET")
        try:
            query_params = request.args.to_dict()
            filter = query_params.get("filter", "").strip()
            limit = int(query_params.get("limit", 10)) 
            page = int(query_params.get("page", 1))
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            if user is None:
                return {"message": "Unauthorized access."}, 401
            res = replenishment.get_stock_and_backorder_products(store, filter, limit, page, sort_array, table='replenishment_backorder_variants')
            
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting backorder list GET")

    def delete_executor(self, request, token_payload, store):
        logger.debug("Entering Bulk Delete Backorder Record")
        try:
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            if user is None:
                return {"message": "Unauthorized access."}, 401
            
            ids = request.get_json().get('ids', [])
            if not ids or len(ids) == 0:
                return {'message': 'No IDs provided for deletion.'}, 400
            res = replenishment.delete_stock_and_backorder_products(store, ids, table='replenishment_backorder_variants')
            if res['status'] == 200:
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Bulk Delete Backorder Record")


    def delete(self):
        return self.execute_store_request(request, self.delete_executor)

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
class BackOrderQtyChange(APIResource):
    def get_executor(self, request, token_payload, store, id):
        logger.debug("Entering backorder sku GET")
        try:
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            if user is None:
                return {"message": "Unauthorized access."}, 401
            res = replenishment.get_backorder_product_details(store, id)
            
            if res['status'] == 200:
                return {'data': res['data']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting backorder sku GET")

    
    def patch_executor(self, request, token_payload, store, id):
        logger.debug("Entering backorder qty PATCH")
        try:
            payload = request.get_json(force=True)
            qty = payload.get('qty')
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            if user is None:
                return {"message": "Unauthorized access."}, 401
            res = replenishment.update_backorder_and_stock_qty(store, user, id, qty, table='replenishment_backorder_variants')
            if res['status'] == 200:                
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting backorder qty PATCH")

    def get(self, id):
        return self.execute_store_request(request, self.get_executor, id)
    
    def patch(self, id):
        return self.execute_store_request(request, self.patch_executor, id)
    

class SafetyStockQtySkuValidate(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering safety stock sku validate GET")
        try:
            query_params = request.args.to_dict()
            sku = query_params.get('sku', '')
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            if user is None:
                return {"message": "Unauthorized access."}, 401
            res = replenishment.validate_sku(store, sku, table='replenishment_safety_stock')
            
            if res['status'] == 200:
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting safety stock sku validate GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


class SafetyStockQty(APIResource):
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering safety stock sku qty POST")
        try:
            payload = request.get_json(force=True)
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            if user is None:
                return {"message": "Unauthorized access."}, 401
            if payload:
                result = replenishment.save_replenishment_sku_qty(store, payload, user, table='replenishment_safety_stock')
                if result['status'] == 200:
                    return {"message": result['message'], "records_processed": result['records_processed']}, 200
                else:
                    return {"message": result['message'], "records_processed": result['records_processed']}, result['status']
            else:    
                return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting safety stock sku qty POST")

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering safety stock list GET")
        try:
            query_params = request.args.to_dict()
            filter = query_params.get("filter", "").strip()
            limit = int(query_params.get("limit", 10)) 
            page = int(query_params.get("page", 1))
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            if user is None:
                return {"message": "Unauthorized access."}, 401
            res = replenishment.get_stock_and_backorder_products(store, filter, limit, page, sort_array, table='replenishment_safety_stock')
            
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting safety stock list GET")

    def delete_executor(self, request, token_payload, store):
        logger.debug("Entering Bulk Delete Backorder Record")
        try:
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            if user is None:
                return {"message": "Unauthorized access."}, 401
            
            ids = request.get_json().get('ids', [])
            if not ids or len(ids) == 0:
                return {'message': 'No IDs provided for deletion.'}, 400
            res = replenishment.delete_stock_and_backorder_products(store, ids, table='replenishment_safety_stock')
            if res['status'] == 200:
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting Bulk Delete Backorder Record")


    def delete(self):
        return self.execute_store_request(request, self.delete_executor)


    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def post(self):
        return self.execute_store_request(request, self.post_executor)
    

class SafetyStockQtyChange(APIResource):
    def patch_executor(self, request, token_payload, store, id):
        logger.debug("Entering safety stock qty PATCH")
        try:
            payload = request.get_json(force=True)
            qty = payload.get('qty')
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            if user is None:
                return {"message": "Unauthorized access."}, 401
            res = replenishment.update_backorder_and_stock_qty(store, user, id, qty, table='replenishment_safety_stock')
            if res['status'] == 200:                
                return {'message': res['message']}, 200
            else:
                return {'message': res['message']}, res['status']
        finally:
            logger.debug("Exiting safety stock qty PATCH")
    
    def patch(self, id):
        return self.execute_store_request(request, self.patch_executor, id)
    

class Dashboard(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Replenishment dashboard Data GET")
        try:
            query_params = request.args.to_dict()
            res = replenishment.get_replenishment_dashboard_data(store, query_params)
            if res['status'] == 200:
                return res['data'], 200
            else:
                return {"message": res['message']}, 422
        finally:
            logger.debug("Exiting Replenishment dashboard GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class UniqueClassifications(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Unique classifications GET")
        try:
            res = replenishment.get_unique_classifications(store['id'])
            if res['status'] == 200:
                return res['data'], 200
        finally:
            logger.debug("Exiting Unique classifications GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

class ReplenishmentClassifications(APIResource):
    def post_executor(self, request, token_payload, store):
        logger.debug("Entering classifications POST")
        try:
            payload = request.get_json(force=True)
            sku = payload.get('sku')
            new_classification = payload.get('new_classification')
            username = token_payload['username'] 
            if username is None:
                return {"message": "Unauthorized access."}, 401
            res = replenishment.save_replenishment_classifications(store, sku, new_classification, username)
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, res['status']
        finally:
            logger.debug("Exiting classifications POST")

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering classifications GET")
        try:
            query_params = request.args.to_dict()
            search = query_params.get('search', '')
            page = int(query_params.get('page', 1))
            limit = int(query_params.get('limit', 10))
            classification_filter = query_params.get('classification_filter', '')
            sort_by = query_params.get('sort_by', '').strip()
            sort_array = sort_by.split("/") if sort_by != '' else []
            res = replenishment.get_replenishment_classifications(store, search, page, limit, sort_array, classification_filter)
            return res['data'], 200
        finally:
            logger.debug("Exiting classifications GET")

    
    def patch_executor(self, request, token_payload, store):
        logger.debug("Entering classifications PATCH")
        try:
            payload = request.get_json(force=True)
            request_ids = payload.get('request_ids', [])
            new_classification = payload.get('new_classification')
            approved = bool(payload.get('approved')) if payload.get('approved') is not None and payload.get('approved') != '' else None
            username = token_payload['username'] 
            if username is None:
                return {"message": "Unauthorized access."}, 401
            if not request_ids or len(request_ids) == 0:
                return {"message": "No IDs provided for update."}, 400
            res = replenishment.update_replenishment_classifications(store, new_classification, approved, username, request_ids)
            if res['status'] == 200:
                return {"message": res['message']}, res['status']
            else:
                return {"message": res['message']}, res['status']
        finally:
            logger.debug("Exiting classifications PATCH")

    def patch(self):
        return self.execute_store_request(request, self.patch_executor)

    def get(self):
        return self.execute_store_request(request, self.get_executor)
    
    def post(self):
        return self.execute_store_request(request, self.post_executor)

class SaveDashboardData(APIResource):
    def patch_executor(self, request, token_payload, store, parent_sku):
        logger.debug("Entering Replenishment dashboard Data POST")
        try:
            payload = request.get_json(force=True)
            username = token_payload['username'] 
            user = None
            if username and username != '':
                user = store_admin_db.fetch_user_by_username(store['id'], username) 
            if user is None:
                return {"message": "Unauthorized access."}, 401
            res = replenishment.save_replenishment_dashboard_data(store, payload, parent_sku, user['username'])
            if res['status'] == 200:
                return {"message": res['message']}, res['status']
            else:
                return {"message": res['message']}, res['status']
        finally:
            logger.debug("Exiting Replenishment dashboard POST")

    def patch(self, parent_sku):
        return self.execute_store_request(request, self.patch_executor, parent_sku)
    
class DashboardFilters(APIResource):
    def get_executor(self, request, token_payload, store, token=None):
        logger.debug("Entering Replenishment Data POST")
        try:
            # req_body = request.get_json(force=True)
            req_body = request.args.to_dict()
            if req_body and 'primary_supplier' in req_body:
                res = replenishment.get_replenishment_filters(store, req_body)
                if res['status'] == 200:
                    return {"data": res['data']}, 200
                else:
                    return {"message": res['message']}, 422
            else:
                return {"message": "Please ensure that your request body is correct."}, 400
        finally:
            logger.debug("Exiting Replenishment POST")

    def get(self):
        return self.execute_store_request(request, self.get_executor)