from graphql import products_variant_query
from new_mongodb import customer_db
import new_mongodb
import new_pgdb
import new_utils
from utils import bc, new_cart_util
from mongo_db import cart_db
from plugin import bc_cart, bc_products
import logging
from datetime import datetime
from utils.common import calculatePaginationData, convert_to_timestamp, get_paginated_records_updated, get_product_id_by_sku_from_mongo
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy import text
from datetime import datetime, timedelta
from fields.products import products_suggestion_fields
import traceback
from utils import store_util

logger = logging.getLogger()

def get_carts_data(store, is_live, filter, rep_name, sort_array, page, limit):
    response = {
        "status": 400       
    }
    # with new_pgdb.get_connection(store['id']) as conn:
    conn = new_pgdb.get_connection(store['id'])
    db = new_mongodb.get_store_db_client(store)
    try:
        threshold_time_clause = ""
        cart_value_clause = ""
        if is_live == 'true':
            threshold_time = datetime.now() - timedelta(minutes=9)
            threshold_time_clause = f" AND cc.last_activity_timestamp >= '{threshold_time}'"
        else:
            cart_value_clause = " AND cc.cart_value > 0"

        count_query = f"""
        SELECT COUNT(*)
        FROM customer_carts cc
        JOIN customers c ON cc.customer_id = c.customer_id
        JOIN salesforce_customer_rep scr ON cc.customer_id = scr.customer_id
        WHERE 1=1 {threshold_time_clause} {cart_value_clause}
        """

        query = f"""
        SELECT
            cc.customer_id,
            c.first_name,
            c.last_name,
            scr.rep_name,
            cc.channel AS channel,
            cc.bc_cart_id,
            cc.cart_line_items as line_items,
            cc.cart_value,
            cc.total_quantity,
            cc.last_activity_timestamp AS updated_at,
            c.email
        FROM customer_carts cc
        JOIN customers c ON cc.customer_id = c.customer_id
        JOIN salesforce_customer_rep scr ON cc.customer_id = scr.customer_id
        WHERE 1=1 {threshold_time_clause} {cart_value_clause}
        """ 

        filter_condition = ""
        if filter:
            filter_condition = f" AND (c.first_name || ' ' || c.last_name ILIKE '%{filter}%' OR c.first_name ILIKE '%{filter}%' OR c.last_name ILIKE '%{filter}%' OR c.email ILIKE '%{filter}%')"
        if rep_name:
            filter_condition += f" AND scr.rep_name ILIKE '%{rep_name}%'"
        query += filter_condition
        count_query += filter_condition

        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
            if sort_array[0] in ["channel", "updated_at", "line_items", "cart_value"]:                
                query += f" ORDER BY {sort_array[0]} {sort_direction}"

        offset = (page - 1) * limit
        if page and limit:
            query += f" LIMIT {limit} OFFSET {offset}"

        result_count = conn.execute(text(count_query))
        total_count = int(result_count.scalar())
        
        result = conn.execute(text(query))
        data = []
        for row in result.fetchall():             
            customer_name = f"{row[1]} {row[2]}"      
            row_data = {
                'customer_id': row[0],
                'customer_name': customer_name,
                'customer_rep': row[3],
                'channel': row[4],
                'bc_cart_id': row[5],
                'line_items': row[6],
                'cart_value': round(row[7], 2),
                'total_quantity': row[8],
                'updated_at': convert_to_timestamp(row[9]),
                'email': row[10]
            }
            data.append(row_data)
        data = new_utils.calculate_pagination(data, page, limit, total_count)

        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 404
            response['message'] = "No data found."
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response


def get_cart_details(store, filter, sort_array, customer_id, channel):
    response = {
        "status": 400       
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        bc_cart_id = None
        query = f"""
        SELECT cc.customer_id, cc.channel AS channel, cc.bc_cart_id FROM customer_carts cc
        JOIN customers c ON cc.customer_id = c.customer_id WHERE cc.customer_id = {customer_id} and cc.channel = '{channel}'"""
        result = conn.execute(text(query)).fetchone()
        if result:
            bc_cart_id = result[2]

        data = cart_db.fetch_line_items_by_customer_id(store, customer_id, channel, bc_cart_id, filter, sort_array)
        reward_points = customer_db.fetch_loyalty_points_for_customer(store, customer_id) or 0
        query = f"""
        SELECT
            first_name,
            last_name,
            customer_id,
            email,
            phone,
            company,
            customer_group_id,
            customer_group_name
        FROM customers
        WHERE customer_id = '{int(customer_id)}'
        """
        result = conn.execute(text(query)).fetchone()

        if result:  # Check if the result is not None
            customer_name = f"{result[0]} {result[1]}"
            data['customer_name'] = customer_name
            data['email'] = result[3]
            data['phone'] = result[4]
            data['company'] = result[5]
            data['customer_group_id'] = result[6]
            data['customer_group_name'] = result[7]
        else:
            data['customer_name'] = ""
            data['email'] = ""
            data['phone'] = ""
            data['company'] = ""
            data['customer_group_id'] = ""
            data['customer_group_name'] = ""

        data['channel'] = channel
        data['bc_cart_id'] = bc_cart_id
        data['reward_points'] = reward_points
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 404
            response['message'] = "No data found."
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        conn.close()
    return response

def check_line_item_availability(store, skus):
    response = {
        "status": 400,
        "data": []      
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        sku_list = skus.split(",")
        for sku in sku_list:
            products_data = bc_products.get_products(store, sku)
            products_data = products_data.json()

            if products_data['data'] != []:
                # Extract the main product SKU
                product_sku = products_data['data'][0]['sku']
                
                # Extract the first variant SKU (if variants exist)
                if 'variants' in products_data['data'][0] and products_data['data'][0]['variants']:
                    variant_sku = products_data['data'][0]['variants'][0]['sku']
                else:
                    variant_sku = None

                # Compare the product SKU and variant SKU
                if product_sku != variant_sku:
                    response['data'].append({'sku': sku, 'status': 409, 'message': 'Please enter a child SKU for this product.'})
                    continue
                    # return {'status': 409, 'message': 'Please enter a child SKU for this product.'}

            product_id, variant_id = get_product_id_by_sku_from_mongo(store, sku)
            if product_id == 0 and variant_id == 0:
                return {'status': 409, 'message': 'Invalid SKU'}
                # response['data'].append({'sku': sku, 'status': 409, 'message': 'Invalid SKU'})
                # continue
                    
            # check available quantity after removing sku from order ...
            graphql_query = products_variant_query.get_query([product_id], [variant_id])
            gql_status, gql_res = bc.process_bc_graphql_request(store, graphql_query)

            min_purchase_qty = None
            max_purchase_qty = None
            available_qty = None

            if gql_status == 200 and len(gql_res['data']['site']['products']['edges']) == 0:
                return {'status': 400, 'message': 'Entered SKU is out of stock.'}
                # response['data'].append({'sku': sku, 'status': 400, 'message': 'Entered SKU is out of stock.'})
                # continue
            
            for item in gql_res['data']['site']['products']['edges']:
                product_name = item['node']['name']
                min_purchase_qty = item['node']['minPurchaseQuantity'] if item['node']['minPurchaseQuantity'] else None
                max_purchase_qty = item['node']['maxPurchaseQuantity'] if item['node']['maxPurchaseQuantity'] else None
                if item['node']['variants']['edges'][0]['node']['prices']['salePrice']:
                    price = item['node']['variants']['edges'][0]['node']['prices']['salePrice']['value']
                else:
                    price = item['node']['variants']['edges'][0]['node']['prices']['price']['value']


                # 2. When SKU is not available for purchase ...
                is_sku_available = item['node']['availabilityV2']['status']
                if is_sku_available != 'Available':
                    return {'status': 400, 'message': 'Entered SKU is not available for purchase.'}
                    # response['data'].append({'sku': sku, 'status': 400, 'message': 'Entered SKU is not available for purchase.'})
                    # continue

                available_qty = item['node']['variants']['edges'][0]['node']['inventory']['aggregated']['availableToSell']
                
                # 3. available quantity check ...
                if available_qty == 0:
                    # response['data'].append({'sku': sku, 'status': 400, 'message': 'Entered SKU is out of stock.'})
                    # continue
                    return {'status': 400, 'message': 'Entered SKU is out of stock.'}
                
                line_item = {
                    "bc_item_id": '',
                    "sku": sku,
                    "product_name": product_name,
                    "quantity": min_purchase_qty if min_purchase_qty and min_purchase_qty > 1 else 1,
                    "price": price,
                    "total_cost": price,
                    "product_id": product_id,
                    "variant_id": variant_id,
                    "is_existing": False,
                    "min_purchase_quantity": min_purchase_qty,
                    "max_purchase_quantity": max_purchase_qty,
                    "available_quantity": available_qty
                }
                response['data'].append(line_item)

            response['status'] = 200

    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        conn.close()
    return response

def update_customer_cart_old(store, customer_id, payload, username):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        cart_id = payload.get('cart_id', None)
        products = payload.get('products', [])
        channel = payload.get('channel', None)
        delete_line_items = payload.get('delete_line_items', [])
        add_success = False
        update_success = False
        delete_success = False
        add_error_message = ""
        update_error_message = ""
        delete_error_message = ""
        new_line_items = []
        existing_line_items = []

        for product in products:
            if product.get('is_existing', False):
                existing_line_items.append(product)
            else:
                new_line_items.append(product)

        if channel == 'express':
            if len(new_line_items) > 0:
                items_result = new_cart_util.add_line_items(store, customer_id, new_line_items)
                if items_result['status'] < 299:
                    add_success = True
                else:
                    add_error_message = 'Add to cart failed: '
                    if ('data' in items_result and len(items_result['data'])) or items_result['error']:
                        add_error_message += 'All available Quantity has been already added to your Cart.'

            if len(existing_line_items) > 0:
                for line_item in existing_line_items:
                    item_result = new_cart_util.update_cart_line_item(store, customer_id, line_item)
                    if item_result['status'] < 299:
                        update_success = True
                    else:
                        update_error_message = item_result.get('message', 'Update cart line item failed.')

            if len(delete_line_items) > 0:
                delete_result = new_cart_util.delete_line_items(store, customer_id, delete_line_items)
                if delete_result['status'] == 200:
                    delete_success = True
                else:
                    delete_error_message = delete_result.get('message', 'Delete cart line item failed.')

        elif channel == 'midwest':
            if len(new_line_items) > 0:
                cart_line_ites = []
                for line_item in new_line_items:
                    item = {
                        "product_id": line_item['product_id'],
                        "variant_id": line_item['variant_id'],
                        "quantity": line_item['quantity']
                    }
                    cart_line_ites.append(item)
                res = bc_cart.add_line_items_to_cart(store, str(payload['bc_cart_id']), cart_line_ites)    
                if res and res.status_code == 201:
                    add_success = True
                else:
                    add_error_message = 'Add to cart failed: '

            if len(existing_line_items) > 0:
                for line_item in existing_line_items:
                    res = bc_cart.update_line_item(store, str(payload['bc_cart_id']), line_item)
                    if res.status_code == 200:
                        update_success = True
                    else:
                        update_error_message = 'Update cart line item failed.'
            
            if len(delete_line_items) > 0:
                 for line_item in delete_line_items:
                    res = bc_cart.delete_line_item(store, str(payload['bc_cart_id']), line_item["id"])
                    if res.status_code == 200:
                        delete_success = True
                    else:
                        delete_error_message = 'Delte cart line item failed.'
            

        if add_success and update_success and delete_success:
            return {
                "message": "Cart edit operation was successful.",
                "status": 200
            }
        elif add_success or update_success or delete_success:
            partial_message = "Add operation was successful." if add_success else "Update operation was successful." if update_success else "Delete operation was successful." 
            failed_message = add_error_message if not add_success else update_error_message
            return {
                "message": f"{partial_message}",
                "status": 200
                }
        else:
            return {
                "message": f"All operations failed. Add error: {add_error_message}, Update error: {update_error_message}, Delete error: {delete_error_message}",
                "status": 422
            }
    except Exception as e:
        error_message = str(e)
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()

def update_customer_cart(store, customer_id, payload, username):
    response = {"status": 400}
    conn = new_pgdb.get_connection(store['id'])
    try:
        cart_id = payload.get('cart_id')
        products = payload.get('products', [])
        channel = payload.get('channel')
        delete_line_items = payload.get('delete_line_items', [])

        add_success = False
        update_success = False
        delete_success = False

        add_error_message = ""
        update_error_message = ""
        delete_error_message = ""

        new_line_items = [product for product in products if not product.get('is_existing')]
        existing_line_items = [product for product in products if product.get('is_existing')]

        def handle_add_to_cart():
            nonlocal add_success, add_error_message
            if new_line_items:
                items_result = {}
                if channel == 'express':
                    items_result = new_cart_util.add_line_items(store, customer_id, new_line_items)
                else: 
                    res = bc_cart.add_line_items_to_cart(store, str(payload['bc_cart_id']), [
                                    {"product_id": line_item['product_id'], "variant_id": line_item['variant_id'], "quantity": line_item['quantity']}
                                    for line_item in new_line_items
                                ])
                    if res.status_code < 299:
                        items_result['status'] = res.status_code
                        items_result['data'] = res.json()
                    else:
                        items_result['status'] = res.status_code
                        items_result['error'] = res.json()
                if items_result.get('status', 500) < 299:
                    add_success = True
                else:
                    add_error_message = 'Add to cart failed: ' + items_result['data'].get('error', 'Something went wrong while adding to cart.') if items_result.get('data') else items_result.get('error', 'Something went wrong while adding to cart.')
            else:
                add_success = True

        def handle_update_cart():
            nonlocal update_success, update_error_message
            if existing_line_items:
                for line_item in existing_line_items:
                    item_result = {}
                    if channel == 'express':
                        item_result = new_cart_util.update_cart_line_item(store, customer_id, line_item)
                    else: 
                        res = bc_cart.update_line_item(store, str(payload['bc_cart_id']), line_item)
                        if res.status_code < 299:
                            item_result['status'] = res.status_code
                            item_result['data'] = res.json()
                        else:
                            item_result['status'] = res.status_code
                            item_result['error'] = res.json()
                    
                    if item_result.get('status', 500) < 299:
                        update_success = True
                    else:
                        update_error_message = 'Update cart line item failed: ' + item_result['data'].get('error', 'Something went wrong while updating cart.') if item_result.get('data') else item_result.get('error', 'Something went wrong while updating cart.')
            else:
                update_success = True

        def handle_delete_from_cart():
            nonlocal delete_success, delete_error_message
            if delete_line_items:
                delete_result = (new_cart_util.delete_line_items(store, customer_id, delete_line_items)
                                 if channel == 'express'
                                 else [bc_cart.delete_line_item(store, str(payload['bc_cart_id']), line_item["id"]) for line_item in delete_line_items])
                if isinstance(delete_result, list):
                    if all(res.status_code == 200 for res in delete_result):
                        delete_success = True
                    else:
                        delete_error_message = 'Delete cart line item failed.'
                elif delete_result.get('status') == 200:
                    delete_success = True
                else:
                    delete_error_message = delete_result.get('error', 'Delete cart line item failed.')
            else:
                delete_success = True

        # Perform the operations
        if channel in ['express', 'midwest']:
            handle_add_to_cart()
            handle_update_cart()
            handle_delete_from_cart()

        # Return appropriate response based on the success of operations
        if add_success and update_success and delete_success:
            return {"message": "Cart edit operation was successful.", "status": 200}
        elif add_success or update_success or delete_success:
            partial_message = "Add operation was successful." if add_success else "Update operation was successful." if update_success else "Delete operation was successful."
            failed_message = ""
            if not add_success:
                failed_message += f" {add_error_message}"
            if not update_success:
                failed_message += f" {update_error_message}"
            if not delete_success:
                failed_message += f" {delete_error_message}"
            return {"message": f"{failed_message}", "status": 200}
        else:
            return {"message": f"All operations failed. Add error: {add_error_message}, Update error: {update_error_message}, Delete error: {delete_error_message}", "status": 422}

    except Exception as e:
        response['status'] = 422
        response['message'] = str(e)
        raise e
    finally:
        if conn:
            conn.close()

def get_products_suggestion(store_id, search, page, limit):
    response = {
        "status": 400       
    }
    db = new_mongodb.get_store_db_client_for_store_id(store_id)
    try:
        payload = {
            "page": page,
            "limit": limit,
            "filterBy": ["name", "sku"],
            "filter": search
        }

        products, total_data_length, page, limit = get_paginated_records_updated(
        db, new_mongodb.StoreDBCollections.PRODUCTS, payload, products_suggestion_fields, {"is_visible": True}
        )
        if products:
            final_result = {
                "data": products,
                "meta": {
                    "current_page": page,
                    "next_page": (page + 1 if page and limit and (page * limit) < total_data_length else None),
                    "total_count": total_data_length
                }
            }
            response['data'] = final_result
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = {
                "data": [],
                "meta": {
                    "current_page": page,
                    "next_page": None,
                    "total_count": total_data_length
                }
            }

    except Exception as e:
        logger.error(traceback.format_exc())

    return response


def get_product_variants(store_id, parent_sku, page, limit):
    response = {
        "status": 400       
    }
    try:
        product_data = new_mongodb.fetch_one_document_from_storefront_collection(store_id, new_mongodb.StoreDBCollections.PRODUCTS, {"sku": parent_sku})
        
        product_variants = []
        for variant in product_data.get("variants", []):  # Ensure we handle nested variants
            variant_name = " - ".join(option['label'] for option in variant.get('option_values', [])) or "Parent Product"
            variant_data = {
                "variant_id": variant["id"],
                "inventory_level": variant["inventory_level"] or 0,
                "sku": variant["sku"] or "",
                "variant_name": variant_name
            }
            product_variants.append(variant_data)

        page = int(page) if page else 1
        limit = int(limit) if limit else 10
        total_variants = len(product_variants)
        start_index = (page - 1) * limit
        end_index = start_index + limit
        paginated_variants = product_variants[start_index:end_index]

        final_result = {
            "data": paginated_variants,
            "meta": {
                "current_page": page,
                "next_page": (page + 1 if (page * limit) < total_variants else None),
                "total_count": total_variants
            }
        }
        response['data'] = final_result
        response['status'] = 200

    except Exception as e:
        logger.error(traceback.format_exc())

    return response

def get_lineitem_list(store, filter, sort_array, page, limit):
    response = {
        "status": 400       
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        page = max(1, int(page))
        limit = max(1, int(limit))

        sort_columns = ["product_name", "variant_sku", "total_cart_quantity", "inventory_level"]
        sort_direction = "ASC"
        sort_column = "total_cart_quantity"  # Default sort by total quantity

        # Validate sorting
        if sort_array and len(sort_array) == 2 and sort_array[0] in sort_columns:
            sort_direction = "ASC" if sort_array[1] == "1" else "DESC"
            sort_column = sort_array[0]

        # Build search condition
        search_condition = ""
        search_params = {}

        if filter:
            search_condition = """WHERE (
                ci.variant_sku ILIKE :search OR 
                ci.product_name ILIKE :search
            )"""
            search_params["search"] = f"%{filter}%"
        
        # Count query to get total number of distinct variants
        count_query = text(f"""
            SELECT COUNT(DISTINCT ci.variant_id)
            FROM cart_line_items_inventory ci
            JOIN variants v ON ci.variant_id = v.variants_id
            {search_condition}
        """)
        
        total_count = conn.execute(count_query, search_params).scalar() or 0
        
        query_data = text(f"""
            SELECT 
                ci.variant_id,
                ci.variant_sku,
                ci.product_id,
                ci.product_name,
                SUM(ci.cart_quantity) as total_cart_quantity,
                v.variants_inventory_level as inventory_level,
                v.variant_options
            FROM cart_line_items_inventory ci
            JOIN variants v ON ci.variant_id = v.variants_id
            {search_condition}
            GROUP BY ci.variant_id, ci.variant_sku, ci.product_id, ci.product_name, v.variants_inventory_level, v.variant_options
            ORDER BY {sort_column} {sort_direction}
            LIMIT :limit OFFSET :offset
        """)
        result = conn.execute(query_data, {**search_params, "limit": limit, "offset": (page - 1) * limit})
        
        data = []
        for row in result.fetchall():
            row_data = {
                'variant_id': row[0],
                'variant_sku': row[1],
                'product_id': row[2],
                'product_name': row[3],
                'total_cart_quantity': row[4],
                'inventory_level': row[5],
                'variant_options': row[6].replace(":", " + ") if row[6] else ""
            }
            data.append(row_data)
            
        paginated_data = new_utils.calculate_pagination(data, page, limit, total_count)
        response['status'] = 200
        response['data'] = paginated_data

    except Exception as e:
        error_message = str(e)
        logger.error(f"Error in get_lineitem_list: {error_message}")
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response


def get_lineitem_customers(store, variant_sku, sort_array):
    response = {
        "status": 400       
    }
    conn = new_pgdb.get_connection(store['id'])
    try:

        sort_columns = ["customer_name", "cart_quantity"]
        sort_direction = "DESC"
        sort_column = "cart_quantity"  # Default sort by total quantity

        # Validate sorting
        if sort_array and len(sort_array) == 2 and sort_array[0] in sort_columns:
            sort_direction = "ASC" if sort_array[1] == "1" else "DESC"
            sort_column = sort_array[0]
        
        query_data = text(f"""
            SELECT 
                ci.customer_id,
                ci.cart_quantity,
                concat(c.first_name, ' ', c.last_name) as customer_name,
                c.email
            FROM cart_line_items_inventory ci
            LEFT JOIN customers c ON ci.customer_id = c.customer_id
            WHERE ci.variant_sku = :variant_sku
            ORDER BY {sort_column} {sort_direction}
        """)
        result = conn.execute(query_data, {"variant_sku": variant_sku})
        
        data = []
        for row in result.fetchall():
            row_data = {
                'customer_id': row[0],
                'cart_quantity': row[1],
                'customer_name': row[2],
                'email': row[3],
                'channel': 'express'
            }
            data.append(row_data)
            
        response['status'] = 200
        response['data'] = data

    except Exception as e:
        error_message = str(e)
        logger.error(f"Error in get_lineitem_customers: {error_message}")
        response['status'] = 422
        response['message'] = error_message
        raise e
    finally:
        if conn:
            conn.close()
    return response