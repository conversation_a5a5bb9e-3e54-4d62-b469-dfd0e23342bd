from flask import request
import logging
from api import APIResource
from customers.all_customer import customers_list
from products.all_products import products_list

logger = logging.getLogger()


# Customer Groups
class ProfitabilityCustomerGroups(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Customer Groups GET")
        try:
            res = customers_list.get_customer_groups(store["id"])
            # Transform data to use label/value format
            transformed_data = [
                {"label": item["name"], "value": item["id"]} for item in res["data"]
            ]
            return {"data": transformed_data}, res["status"]
        finally:
            logger.debug("Exiting Customer Groups GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


# Customer Representatives
class ProfitabilityCustomerRepresentative(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering customer representative GET")
        try:
            res = customers_list.get_customer_representatives(store["id"])
            if res["status"] == 200:
                # Transform to label/value format
                transformed_data = [
                    {"label": name, "value": email}
                    for email, name in res["data"].items()
                ]
                return {"data": transformed_data}, res["status"]
            else:
                return {"message": res["message"]}, res["status"]
        finally:
            logger.debug("Exiting customer representative GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


# Product Classifications
class ProfitabilityProductClassifications(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Product price list classifications GET")
        try:
            query_params = request.args.to_dict()
            user = query_params.get("user", None)
            res = products_list.get_product_price_list_classifications_and_suppliers(
                store["id"], user
            )
            if res["status"] == 200:
                # Transform to label/value format
                transformed_data = [
                    {"label": item, "value": item}
                    for item in res["data"]["classifications"]
                ]
                return {"data": transformed_data}, 200
        finally:
            logger.debug("Exiting Product price list classifications GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


# Product Suppliers
class ProfitabilityProductSuppliers(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Product price list suppliers GET")
        try:
            query_params = request.args.to_dict()
            user = query_params.get("user", None)
            res = products_list.get_product_price_list_classifications_and_suppliers(
                store["id"], user
            )
            if res["status"] == 200:
                # Transform to label/value format
                transformed_data = [
                    {"label": item, "value": item} for item in res["data"]["suppliers"]
                ]
                return {"data": transformed_data}, 200
        finally:
            logger.debug("Exiting Product price list suppliers GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


# Products
class ProfitabilityProducts(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Price List Products Dropdown GET")
        try:
            query_params = request.args.to_dict()
            page = query_params.get("page", None)
            limit = query_params.get("limit", None)
            search = query_params.get("search", None).strip()
            classification_filter = query_params.get(
                "classification_filter", None
            ).strip()
            res = products_list.get_filtered_products_dropdown(
                store, page, limit, search, classification_filter, "", ""
            )
            if res["status"] == 200:
                # Transform to label/value format
                transformed_data = [
                    {"label": item["parent_product_name"], "value": item["parent_product_id"]}
                    for item in res["data"]["data"]
                ]
                return (
                    {
                        "data": transformed_data,
                        "meta": res["data"]["meta"],
                    },
                    200,
                )
            else:
                return {"message": res["message"]}, res["status"]
        finally:
            logger.debug("Exiting Price List Products Dropdown GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)


# Customr Representative Types
class ProfitabilityCustomerRepresentativeTypes(APIResource):
    def get_executor(self, request, token_payload, store):
        logger.debug("Entering Customer Representative Types GET")
        try:
            res = customers_list.get_customer_representative_types(store["id"])
            return {"data": res["data"]}, res["status"]
        finally:
            logger.debug("Exiting Customer Representative Types GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
