product_prices_query = """
  query productsById {
    site {
      products(entityIds: produc_ids, first: limit) {
        edges {
            node {
                entityId
                prices {
                    price {
                    ...PriceFields
                    }
                    retailPrice {
                        ...PriceFields
                    }
                    salePrice {
                        ...PriceFields
                    }
                }
            }
        }
    }
  }
  }
  fragment PriceFields on Money {
    value
    currencyCode
  }
"""

def get_query(produc_ids, limit):
    x = product_prices_query.replace("produc_ids", "[" + produc_ids + "]")
    return x.replace("limit", str(limit) )