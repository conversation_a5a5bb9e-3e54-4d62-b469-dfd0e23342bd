import trace
from projects import check_project_access
from sqlalchemy import text
import pg_db
from mongo_db import user_db
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from datetime import datetime
import logging
import traceback

logger = logging.getLogger()

def _fetch_project_columns(conn, project_id):
    data = []
    query = text(
        f"""SELECT pm.id as column_id, pm.project_id, pm.name AS module_name, pm.description, COUNT(pc.current_column_id) AS card_count, pm.sort_id,
            pcm.table_column_value, pcm.table_column_value_type, pm.is_visible, pm.is_default, pm.is_resolved
            FROM {pg_db.project_columns} AS pm
            LEFT JOIN {pg_db.project_cards} AS pc ON pm.id = pc.current_column_id
            LEFT JOIN {pg_db.pipeline_column_mapping} AS pcm ON pm.id = pcm.project_column_id AND pm.project_id = pcm.project_id
            WHERE pm.project_id = :project_id
            GROUP BY pm.id, pm.name, pcm.table_column_value, pcm.table_column_value_type
            ORDER BY pm.sort_id ASC;
        """
    )
    query = query.params(project_id=project_id)
    result = conn.execute(query)
    for row in result.fetchall():
        row_data = {
            'column_id': row[0],
            'project_id': row[1],
            'name':row[2].split('_')[0],
            'description': row[3],
            'cards': row[4],
            'sort_id': row[5],
            'mapped_column_value': row[6] if row[6] else "",
            'is_visible': row[8],
            'is_default': row[9],
            'is_resolved': row[10],
        }
        data.append(row_data)
    
    return data

def get_columns(project_id, username):
    response = {
        "status": 400 
    }
    conn = pg_db.get_connection()
    try:
        project_access = check_project_access(project_id, username) 
        if not project_access:
            response['status'] = 403
            response['message'] = "You don't have access for this project."
            return response   
        data = _fetch_project_columns(conn, project_id)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['status'] = 200
            response['data'] = []
    finally:
        if conn:
            conn.close()
    return response


def _insert_new_column(conn, project_id, name, sort_id, total_columns, description, is_archived, created_by, is_visible, is_resolved, is_first_column=False):
    try:
        # Insert the new column into the board_columns table
        result = conn.execute(
            text(
                f"""
                INSERT INTO {pg_db.project_columns} (project_id, name, sort_id, is_first_column, is_last_column, description, is_archived, created_by, updated_by, updated_at, is_visible, is_default, is_resolved)
                VALUES (:project_id, :name, :sort_id, :is_first_column, :is_last_column, :description, :is_archived, :created_by, :created_by, CURRENT_TIMESTAMP, :is_visible, :is_default, :is_resolved) RETURNING id;
                """),
            {
                'project_id': project_id,
                'name': name,
                'sort_id': sort_id,
                'is_first_column': is_first_column,  # Pass is_first_column value
                'is_last_column': sort_id == total_columns,  # Set is_last_column to True if sort_id equals total_columns
                'description': description,
                'is_archived': is_archived,
                'created_by': created_by,
                'is_visible': is_visible,
                'is_default': True if is_first_column else False,
                'is_resolved': is_resolved
            }
        )
        column_id = result.fetchone()[0]
        return True, column_id
    except Exception as e:
        logger.error(traceback.format_exc())
        return False, 0


    
def update_column_attributes(conn, min_sort_id, max_sort_id, sort_id, is_first_column, project_id, call_from_func=False):
    try:
        project_condition = ''
        if project_id is None:
            project_condition = 'WHERE project_id IS NULL'
        else:
            project_condition = 'WHERE project_id = :project_id'
        conn.execute(
            text(
                f"""
                UPDATE {pg_db.project_columns} 
                SET is_last_column = FALSE,
                is_first_column = FALSE 
                {project_condition}
                """
            ),
            {'project_id': project_id}
        )

        if max_sort_id is not None:
            conn.execute(
                text(
                    f"""
                    UPDATE {pg_db.project_columns} 
                    SET is_last_column = FALSE
                    {project_condition} AND sort_id = :max_sort_id
                    """
                ),
                {'project_id': project_id, 'max_sort_id': max_sort_id}
            )

        if min_sort_id is not None:
            conn.execute(
                text(
                    f"""
                    UPDATE {pg_db.project_columns} 
                    SET is_first_column = TRUE
                    {project_condition} AND sort_id = :min_sort_id
                    """
                ),
                {'project_id': project_id, 'min_sort_id': min_sort_id}
            )

        if call_from_func:
            conn.execute(
                text(
                    f"""
                    UPDATE {pg_db.project_columns} 
                    SET is_last_column = TRUE 
                    {project_condition} AND sort_id = :max_sort_id
                    """
                ),
                {'project_id': project_id, 'max_sort_id': max_sort_id}
            )

        conn.execute(
            text(
                f"""
                UPDATE {pg_db.project_columns} 
                SET is_last_column = TRUE
                {project_condition} AND sort_id = :sort_id
                """
            ),
            {'project_id': project_id, 'sort_id': sort_id}
        )
        return True
    except Exception as e:
        logger.error(traceback.format_exc())
        return False

def get_sort_ids(conn, project_id):
    try:
        if project_id is None:
            project_condition = 'project_id IS NULL'
        else:
            project_condition = 'project_id = :project_id'

        min_max_sort_id_query = text(
            f"""SELECT MIN(sort_id) AS min_sort_id, MAX(sort_id) AS max_sort_id 
                FROM {pg_db.project_columns} WHERE {project_condition}"""
        )

        if project_id is not None:
            min_max_sort_id_result = conn.execute(min_max_sort_id_query, {'project_id': project_id}).fetchone()
        else:
            min_max_sort_id_result = conn.execute(min_max_sort_id_query).fetchone()

        min_sort_id = min_max_sort_id_result[0] if min_max_sort_id_result[0] is not None else 0
        max_sort_id = min_max_sort_id_result[1] if min_max_sort_id_result[1] is not None else 0

        is_first_column = min_sort_id is None

        sort_id = max_sort_id + 1 if max_sort_id is not None else 1

        return min_sort_id, max_sort_id, is_first_column, sort_id, True
    except Exception as e:
        logger.error(traceback.format_exc())
        return None, None, None, None, False

    
def add_new_column(name, description, is_archived, username, project_id, is_visible, is_resolved):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        min_sort_id, max_sort_id, is_first_column, sort_id, success = get_sort_ids(conn, project_id)

        if success:
            is_inserted, new_column_id = _insert_new_column(conn, project_id, name, sort_id, max_sort_id + 1, description, is_archived,
                                        username, is_visible, is_resolved, is_first_column)
            if is_inserted:
                update_column_attributes(conn, min_sort_id, max_sort_id, sort_id, is_first_column, project_id)
                if is_resolved:
                    query = text(
                        f"""UPDATE {pg_db.project_columns} set is_resolved = FALSE where project_id = :project_id and id != :id and is_resolved = TRUE;"""
                    )
                    query = query.params(project_id=project_id, id=new_column_id)
                    conn.execute(query)
                response['status'] = 200
                response['message'] = "Data inserted successfully and column attributes updated."
            else:
                response['status'] = 409
                response['message'] = "name: This column already exists"
        else:
            response['status'] = 400
            response['message'] = "Error getting sort ids."
        
    except IntegrityError as e:
        logger.error(traceback.format_exc())
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 409
            response['message'] = "name: This column already exists."
        else:
            response['status'] = 500
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response


def _modify_column_sort_id(conn, project_id, column_ids):
    try:
        for index, id in enumerate(column_ids, start=1):
            query = text (
                f"""UPDATE {pg_db.project_columns} SET sort_id = :sort_id WHERE project_id = :project_id AND id = :id"""
            )
            query = query.params(sort_id=index, project_id=project_id, id=id)
            conn.execute(query)
        return True
    except Exception as e:
        logger.error(traceback.format_exc())
        return False

def update_columns_sort_id(project_id, column_ids):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        if not isinstance(column_ids, list):
            column_ids = [column_ids]
        data = _modify_column_sort_id(conn, project_id, column_ids)
        if data:
            min_sort_id, max_sort_id, is_first_column, sort_id, flag = get_sort_ids(conn, project_id)
            if flag:
                success = update_column_attributes(conn, min_sort_id, max_sort_id, sort_id, is_first_column, project_id, call_from_func=True)
                if success:
                    response['status'] = 200
                    response['message'] = "Data updated successfully and column attributes updated."
                else:
                    response['status'] = 400
                    response['message'] = "column_ids: Data updation failed while updating column attributes."
            else:
                response['status'] = 400
                response['message'] = "Error getting sort ids."

        else:
            response['status'] = 400
            response['message'] = "Data updation failed."
    except IntegrityError as e:
            error_message = str(e)
            response['status'] = 500
            response['message'] = f"Something went wrong. {error_message}"
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response