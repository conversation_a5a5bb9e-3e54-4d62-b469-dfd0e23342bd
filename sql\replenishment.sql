DROP VIEW IF EXISTS sell_summary_products;
DROP VIEW IF EXISTS product_sell_7days; 


create view product_sell_7days as 
	SELECT product_id, parent_sku, sum(quantity) as quantity
	FROM analytics_products_trend 
	WHERE order_date_time >= current_date - interval '7' day 
	group by product_id, parent_sku;
	
DROP VIEW IF EXISTS product_sell_15days;
  create view product_sell_15days as 
	SELECT product_id, parent_sku, sum(quantity) as quantity
	FROM analytics_products_trend 
	WHERE order_date_time >= current_date - interval '15' day 
	group by product_id, parent_sku;
	
DROP VIEW IF EXISTS product_sell_30days;
  create view product_sell_30days as 
	SELECT product_id, parent_sku, sum(quantity) as quantity
	FROM analytics_products_trend 
	WHERE order_date_time >= current_date - interval '30' day 
	group by product_id, parent_sku;
	
DROP VIEW IF EXISTS product_sell_45days;
  create view product_sell_45days as 
	SELECT product_id, parent_sku, sum(quantity) as quantity
	FROM analytics_products_trend 
	WHERE order_date_time >= current_date - interval '45' day 
	group by product_id, parent_sku;

DROP VIEW IF EXISTS product_sell_60days;
  create view product_sell_60days as 
	SELECT product_id, parent_sku, sum(quantity) as quantity
	FROM analytics_products_trend 
	WHERE order_date_time >= current_date - interval '60' day 
	group by product_id, parent_sku;
	
DROP VIEW IF EXISTS product_sell_90days;
  create view product_sell_90days as 
	SELECT product_id, parent_sku, sum(quantity) as quantity
	FROM analytics_products_trend 
	WHERE order_date_time >= current_date - interval '90' day 
	group by product_id, parent_sku;
	
DROP VIEW IF EXISTS product_sell_180days;
  create view product_sell_180days as 
	SELECT product_id, parent_sku, sum(quantity) as quantity
	FROM analytics_products_trend 
	WHERE order_date_time >= current_date - interval '180' day 
	group by product_id, parent_sku;
	
DROP VIEW IF EXISTS sell_summary_products;
create view sell_summary_products as 
SELECT d180.product_id, d180.parent_sku, d7.quantity as d7_quantity, d15.quantity as d15_quantity,
d30.quantity as d30_quantity, d45.quantity as d45_quantity, 
d60.quantity as d60_quantity, d90.quantity as d90_quantity,d180.quantity as d180_quantity
FROM product_sell_180days d180 left join product_sell_90days d90 on d180.parent_sku = d90.parent_sku 
left join product_sell_60days d60 on d180.parent_sku = d60.parent_sku
left join product_sell_45days d45 on d180.parent_sku = d45.parent_sku
left join product_sell_30days d30 on d180.parent_sku = d30.parent_sku
left join product_sell_15days d15 on d180.parent_sku = d15.parent_sku
left join product_sell_7days d7 on d180.parent_sku = d7.parent_sku;

create view product_sell_cur_month as 
	SELECT product_id, parent_sku, sum(quantity) as quantity
	FROM analytics_products_trend 
	WHERE order_year = date_part('year', (SELECT current_timestamp)) and
	order_month = date_part('month', (SELECT current_timestamp))
	group by product_id, parent_sku;
	
create view product_sell_cur_less_1_month as 
	SELECT product_id, parent_sku, sum(quantity) as quantity
	FROM analytics_products_trend 
	WHERE order_year = date_part('year', (current_date - interval '1' month)) and
	order_month = date_part('month', (current_date - interval '1' month))
	group by product_id, parent_sku;
	
	
create view product_sell_cur_less_2_month as 
	SELECT product_id, parent_sku, sum(quantity) as quantity
	FROM analytics_products_trend 
	WHERE order_year = date_part('year', (current_date - interval '2' month)) and
	order_month = date_part('month', (current_date - interval '2' month))
	group by product_id, parent_sku;
	
create view product_sell_cur_less_3_month as 
	SELECT product_id, parent_sku, sum(quantity) as quantity
	FROM analytics_products_trend 
	WHERE order_year = date_part('year', (current_date - interval '3' month)) and
	order_month = date_part('month', (current_date - interval '3' month))
	group by product_id, parent_sku;
	
create view product_sell_cur_less_4_month as 
	SELECT product_id, parent_sku, sum(quantity) as quantity
	FROM analytics_products_trend 
	WHERE order_year = date_part('year', (current_date - interval '4' month)) and
	order_month = date_part('month', (current_date - interval '4' month))
	group by product_id, parent_sku;
	
create view product_sell_cur_less_5_month as 
	SELECT product_id, parent_sku, sum(quantity) as quantity
	FROM analytics_products_trend 
	WHERE order_year = date_part('year', (current_date - interval '5' month)) and
	order_month = date_part('month', (current_date - interval '5' month))
	group by product_id, parent_sku;
	
create view product_sell_cur_less_6_month as 
	SELECT product_id, parent_sku, sum(quantity) as quantity
	FROM analytics_products_trend 
	WHERE order_year = date_part('year', (current_date - interval '6' month)) and
	order_month = date_part('month', (current_date - interval '6' month))
	group by product_id, parent_sku;


DROP VIEW IF EXISTS replenishment_product_view;
create view replenishment_product_view as 
select max(sv.title) as product_title, sv.parent_sku, sv.primary_supplier as primary_supplier, 
sv.classification as classification, sv.brand,
max(sv.cost) as cost, max(sv.retail_price) as retail_price, max(sv.sale_price) as sale_price, 
max(sv.reorder_point) as reorder_point, max(sv.incremental_quantity) as incremental_quantity,
sum(sv.quantity_on_hand) as quantity_on_hand, sum(sv.quantity_pending) as quantity_pending,
sum(sv.quantity_incoming) as quantity_incoming, sum(sv.quantity_available) as quantity_available,
sum(sv.quantity_on_hold) as quantity_on_hold           
from skuvault_catalog sv
where sv.primary_supplier <> 'Unknown'
group by sv.parent_sku,sv.primary_supplier,sv.classification,sv.brand;


----------- Variants report ----------------------
DROP VIEW IF EXISTS sell_summary_variants;
DROP VIEW IF EXISTS variant_sell_7days;
create view variant_sell_7days as 
	SELECT variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM analytics_variants_trend 
	WHERE order_date_time >= current_date - interval '7' day 
	group by variant_id, variants_sku;
	
DROP VIEW IF EXISTS variant_sell_15days;
create view variant_sell_15days as 
	SELECT variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM analytics_variants_trend 
	WHERE order_date_time >= current_date - interval '15' day 
	group by variant_id, variants_sku;
	
DROP VIEW IF EXISTS variant_sell_30days;
create view variant_sell_30days as 
	SELECT variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM analytics_variants_trend 
	WHERE order_date_time >= current_date - interval '30' day 
	group by variant_id, variants_sku;
	
DROP VIEW IF EXISTS variant_sell_45days;
create view variant_sell_45days as 
	SELECT variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM analytics_variants_trend 
	WHERE order_date_time >= current_date - interval '45' day 
	group by variant_id, variants_sku;
	
DROP VIEW IF EXISTS variant_sell_60days;
create view variant_sell_60days as 
	SELECT variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM analytics_variants_trend 
	WHERE order_date_time >= current_date - interval '60' day 
	group by variant_id, variants_sku;
	
DROP VIEW IF EXISTS variant_sell_90days;
create view variant_sell_90days as 
	SELECT variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM analytics_variants_trend 
	WHERE order_date_time >= current_date - interval '90' day 
	group by variant_id, variants_sku;
	
DROP VIEW IF EXISTS variant_sell_180days;
create view variant_sell_180days as 
	SELECT variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM analytics_variants_trend 
	WHERE order_date_time >= current_date - interval '180' day 
	group by variant_id, variants_sku;

DROP VIEW IF EXISTS sell_summary_variants;
create view sell_summary_variants as 
SELECT d180.variant_id, d180.product_id, d180.parent_sku, d180.variants_sku, d180.quantity as d180_quantity,  d90.quantity as d90_quantity, d60.quantity as d60_quantity,
d45.quantity as d45_quantity, d30.quantity as d30_quantity, d15.quantity as d15_quantity,d7.quantity as d7_quantity
FROM variant_sell_180days d180 left join variant_sell_90days d90 on d180.variants_sku = d90.variants_sku 
left join variant_sell_60days d60 on d180.variants_sku = d60.variants_sku
left join variant_sell_45days d45 on d180.variants_sku = d45.variants_sku
left join variant_sell_30days d30 on d180.variants_sku = d30.variants_sku
left join variant_sell_15days d15 on d180.variants_sku = d15.variants_sku
left join variant_sell_7days d7 on d180.variants_sku = d7.variants_sku;

DROP VIEW IF EXISTS variant_sell_cur_month;
create view variant_sell_cur_month as 
	SELECT variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM analytics_variants_trend 
	WHERE order_year = date_part('year', (SELECT current_timestamp)) and
	date_part('month', (order_date_time)) = date_part('month', (SELECT current_timestamp))
	group by variant_id, variants_sku;
	
DROP VIEW IF EXISTS variant_sell_cur_less_1_month;
create view variant_sell_cur_less_1_month as 
	SELECT variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM analytics_variants_trend 
	WHERE order_year = date_part('year', (current_date - interval '1' month)) and
	date_part('month', (order_date_time)) = date_part('month', (current_date - interval '1' month))
	group by variant_id, variants_sku;
	
DROP VIEW IF EXISTS variant_sell_cur_less_2_month;
create view variant_sell_cur_less_2_month as 
	SELECT variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM analytics_variants_trend 
	WHERE order_year = date_part('year', (current_date - interval '2' month)) and
	date_part('month', (order_date_time)) = date_part('month', (current_date - interval '2' month))
	group by variant_id, variants_sku;
	
DROP VIEW IF EXISTS variant_sell_cur_less_3_month;
create view variant_sell_cur_less_3_month as 
	SELECT variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM analytics_variants_trend 
	WHERE order_year = date_part('year', (current_date - interval '3' month)) and
	date_part('month', (order_date_time)) = date_part('month', (current_date - interval '3' month))
	group by variant_id, variants_sku;
	
DROP VIEW IF EXISTS variant_sell_cur_less_4_month;
create view variant_sell_cur_less_4_month as 
	SELECT variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM analytics_variants_trend 
	WHERE order_year = date_part('year', (current_date - interval '4' month)) and
	date_part('month', (order_date_time)) = date_part('month', (current_date - interval '4' month))
	group by variant_id, variants_sku;
	
DROP VIEW IF EXISTS variant_sell_cur_less_5_month;
create view variant_sell_cur_less_5_month as 
	SELECT variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM analytics_variants_trend 
	WHERE order_year = date_part('year', (current_date - interval '5' month)) and
	date_part('month', (order_date_time)) = date_part('month', (current_date - interval '5' month))
	group by variant_id, variants_sku;
	
DROP VIEW IF EXISTS variant_sell_cur_less_6_month;
create view variant_sell_cur_less_6_month as 
	SELECT variant_id, max(product_id) as product_id, max(parent_sku) as parent_sku, variants_sku, sum(quantity) as quantity
	FROM analytics_variants_trend 
	WHERE order_year = date_part('year', (current_date - interval '6' month)) and
	date_part('month', (order_date_time)) = date_part('month', (current_date - interval '6' month))
	group by variant_id, variants_sku;

DROP VIEW IF EXISTS replenishment_variant_view;
create view replenishment_variant_view as 
select max(sv.title) as product_title, max(sv.parent_sku) as parent_sku, sv.sku, 
sv.primary_supplier as primary_supplier, sv.classification as classification, sv.brand,
max(sv.cost) as cost, max(sv.retail_price) as retail_price, max(sv.sale_price) as sale_price, 
max(sv.reorder_point) as reorder_point, max(sv.incremental_quantity) as incremental_quantity,
sum(sv.quantity_on_hand) as quantity_on_hand, sum(sv.quantity_pending) as quantity_pending,
sum(sv.quantity_incoming) as quantity_incoming, sum(sv.quantity_available) as quantity_available,
sum(sv.quantity_on_hold) as quantity_on_hold           
from skuvault_catalog sv
where sv.primary_supplier <> 'Unknown'
group by sv.sku,sv.primary_supplier,sv.classification,sv.brand;






------------------------- Main report --------------------------------


DROP TABLE IF EXISTS replenishment_products;
create table replenishment_products as 
select rp.product_title, rp.parent_sku, su.product_id, rp.primary_supplier, rp.classification, rp.brand,
rp.cost, rp.retail_price, rp.sale_price, rp.reorder_point, rp.incremental_quantity,
rp.quantity_on_hand, rp.quantity_pending, rp.quantity_incoming, rp.quantity_available,
rp.quantity_on_hold, su.d180_quantity, su.d90_quantity, su.d60_quantity, su.d45_quantity, su.d30_quantity,
su.d15_quantity, su.d7_quantity, cur_m.quantity as cm_quantity, m1.quantity as m1_quantity,
m2.quantity as m2_quantity, m3.quantity as m3_quantity , m4.quantity as m4_quantity, 
m5.quantity as m5_quantity, m6.quantity as m6_quantity
from replenishment_product_view rp left join sell_summary_products su on rp.parent_sku = su.parent_sku
left join product_sell_cur_month cur_m on rp.parent_sku = cur_m.parent_sku
left join product_sell_cur_less_1_month m1 on rp.parent_sku = m1.parent_sku
left join product_sell_cur_less_2_month m2 on rp.parent_sku = m2.parent_sku
left join product_sell_cur_less_3_month m3 on rp.parent_sku = m3.parent_sku
left join product_sell_cur_less_4_month m4 on rp.parent_sku = m4.parent_sku
left join product_sell_cur_less_5_month m5 on rp.parent_sku = m5.parent_sku
left join product_sell_cur_less_6_month m6 on rp.parent_sku = m6.parent_sku;


DROP TABLE IF EXISTS replenishment_variants;
create table replenishment_variants as 
select rp.product_title, su.product_id, rp.parent_sku, rp.sku, su.variant_id, rp.primary_supplier, rp.classification, rp.brand,
rp.cost, rp.retail_price, rp.sale_price, rp.reorder_point, rp.incremental_quantity,
rp.quantity_on_hand, rp.quantity_pending, rp.quantity_incoming, rp.quantity_available,
rp.quantity_on_hold, su.d180_quantity, su.d90_quantity, su.d60_quantity, su.d45_quantity, su.d30_quantity,
su.d15_quantity, su.d7_quantity
from replenishment_variant_view rp left join sell_summary_variants su on rp.sku = su.variants_sku
left join variant_sell_cur_month cur_m on rp.sku = cur_m.variants_sku
left join variant_sell_cur_less_1_month m1 on rp.sku = m1.variants_sku
left join variant_sell_cur_less_2_month m2 on rp.sku = m2.variants_sku
left join variant_sell_cur_less_3_month m3 on rp.sku = m3.variants_sku
left join variant_sell_cur_less_4_month m4 on rp.sku = m4.variants_sku
left join variant_sell_cur_less_5_month m5 on rp.sku = m5.variants_sku
left join variant_sell_cur_less_6_month m6 on rp.sku = m6.variants_sku;