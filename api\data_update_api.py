from flask import request
import logging
import traceback
from api import APIResource
import mongo_db 

logger = logging.getLogger()

class DataUpdate(APIResource):
    def post_executor(self, request, token_payload, tenant_id, store_id):
        try:
            query_params = request.args.to_dict()
            db_name = query_params['db']
            collection = query_params['collection']
            operation = query_params['operation']
            req_body = request.get_json(force=True)
            
            if operation == 'upsert':
                query = {'$set': {}}

                for i in req_body:
                    query['$set'].update(i)  

                res = mongo_db.update_all(db_name, collection, query)
                return {'modified records counts': res.modified_count}, 200
            
            if operation == 'delete':
                query = {'$unset': {}}

                for i in req_body:
                    query['$unset'].update({i: ''})  

                res = mongo_db.update_all(db_name, collection, query)            
                return {'modified records counts': res.modified_count}, 200
        finally:
            logger.debug("Exiting Role GET")

    def post(self, tenant_id, store_id):
        return self.execute_request(request, self.post_executor, tenant_id, store_id)
