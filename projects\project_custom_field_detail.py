from projects.global_custom_field_details import _update_custom_field_detail
from sqlalchemy import text
import pg_db
from mongo_db import user_db
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from projects import project_custom_fields
from datetime import datetime
import logging
import traceback

logger = logging.getLogger()

def _remove_customfield_from_project(conn, project_id, id):
    # dependency_query = text(
    #     f"""SELECT COUNT(acv.card_id) AS card_count
    #         FROM {pg_db.agile_project_customfield} AS apc
    #         LEFT JOIN {pg_db.agile_customfield_value} AS acv ON apc.id = acv.project_customfield_id
    #         WHERE apc.project_id = :project_id
    #         AND apc.id = :id
    #     """
    # )
    # dependency_query = dependency_query.params(project_id=project_id, id=id)
    # result = conn.execute(dependency_query)
    # dependency_result = result.fetchone()
    # card_count = dependency_result[0]
   
    # if card_count > 0:
    #     return False
    
    # fetch the custom field data
    custom_field_query = text(f"""SELECT customfield_meta_id FROM {pg_db.agile_project_customfield} WHERE project_id = :project_id AND id = :id""")
    custom_field_query = custom_field_query.params(project_id=project_id, id=id)
    custom_field_result = conn.execute(custom_field_query).fetchone()
    custom_field_mata_id = custom_field_result[0] if custom_field_result else None    

    # Delete the custom field
    # delete_query = text(
    #     f"""DELETE FROM {pg_db.agile_project_customfield}
    #         WHERE project_id = :project_id
    #         AND id = :id
    #     """
    # )
    update_query = text(f"""UPDATE {pg_db.agile_project_customfield} SET status = 'deleted', sort_id = :sort_id WHERE project_id = :project_id AND id = :id;""")
    update_query = update_query.params(project_id=project_id, id=id, sort_id=0)
    update_result = conn.execute(update_query)

    # delete custome field meta for the project specific custom field
    if custom_field_mata_id:
        delete_meta_query = text(f"""UPDATE {pg_db.agile_customfield_meta} SET status = 'deleted' WHERE id = :id and project_id = :project_id""")
        delete_meta_query = delete_meta_query.params(id=custom_field_mata_id, project_id = project_id)
        delete_meta_result = conn.execute(delete_meta_query)

    # Fetch the list of remaining column IDs after deletion
    remaining_ids_query = text(
        f"""SELECT id FROM {pg_db.agile_project_customfield}
            WHERE project_id = :project_id and status = 'active'
        """
    )
    remaining_ids_query = remaining_ids_query.params(project_id=project_id)
    remaining_ids_result = conn.execute(remaining_ids_query)
    remaining_ids = [row[0] for row in remaining_ids_result.fetchall()]

    # Refresh the sort IDs
    project_custom_fields._modify_sort_id(conn, remaining_ids, project_id)     
    
    return update_result.rowcount > 0

def delete_field_from_project(project_id, id):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:
        data = _remove_customfield_from_project(conn, project_id, id)
        if data:
            response['status'] = 200
            response['message'] = "Data deleted successfully."
        else:
            response['status'] = 409
            response['message'] = "Data deletion failed, This custom field is used in multiple cards."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response


def _update_project_custom_field_detail(conn, update_fields, project_id, id, username):
    try:
        set_clause = ", ".join([f"{field} = :{field}" for field in update_fields])
        query = text(
            f"""UPDATE {pg_db.agile_project_customfield}
                SET 
                    {set_clause},
                    updated_by = :updated_by,
                    updated_at = CURRENT_TIMESTAMP
                WHERE 
                    project_id = :project_id 
                    AND id = :id;"""
        )
        params = update_fields.copy()
        params.update({'updated_by': username, 'id': id, 'project_id': project_id})    
        result = conn.execute(query, params) 
        return result.rowcount > 0
    except Exception as e:
        logger.error(traceback.format_exc())
        return False



def patch_project_custom_field(username, project_id, id, payload):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        name = payload.get('name', None)
        group_values = payload.get('group_values', None)
        
        # Check if the custom field exists and if yes then get the datatype from meta table
        existing_query = text (f"""SELECT acm.id, acm.name, acm.datatype, acm.group_values from {pg_db.agile_customfield_meta} acm JOIN {pg_db.agile_project_customfield} apc on acm.id = apc.customfield_meta_id where apc.project_id = :project_id and apc.id = :id;""")
        existing_query = existing_query.params(project_id=project_id, id=id)
        existing_result = conn.execute(existing_query).fetchone()
        if not existing_result:
            response['status'] = 404
            response['message'] = "Custom field not found."
            return response
        
        existing_field_datatype = existing_result[2]
        customfield_meta_id = existing_result[0]
      
        if (str(existing_field_datatype) != 'select' and str(existing_field_datatype) != 'multi select') and group_values:
            response['status'] = 400
            response['message'] = "Cannot provide group_values for a non-select datatype."
            return response

        # Check if the custom field name already exists in the project
        if name:
            unique_query = text (f"""SELECT * from {pg_db.agile_project_customfield} where LOWER(name) = LOWER(:name) and project_id = :project_id and id != :id;""")
            unique_query = unique_query.params(name=name, project_id=project_id, id=id)
            column_result = conn.execute(unique_query)
            if column_result.rowcount > 0:
                response['status'] = 409
                response['message'] = "name: Data updation failed, field with the same name already exists."
                return response
        
        # Update fields if provided in payload
        update_fields = {}
        update_fields_meta = {}
        for field in ['name', 'is_visible', 'group_values', 'read_only']:
            if field in payload:
                if field != 'group_values':
                    update_fields[field] = payload[field]
                else:
                    update_fields_meta[field] = payload[field]

        data = False
        meta_data = False
        if update_fields:
            data = _update_project_custom_field_detail(conn, update_fields, project_id, id, username)
        if update_fields_meta:
            meta_data = _update_custom_field_detail(conn, update_fields_meta, customfield_meta_id, username)

        if data or meta_data:
            response['status'] = 200
            response['message'] = "Data updated successfully."
        else:
            response['status'] = 409
            response['message'] = "Data updation failed, field already exists or id not valid"
    except IntegrityError as e:
        logger.error(traceback.format_exc())
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 409
            response['message'] = "name: This project already exists."
        else:
            response['status'] = 500
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response