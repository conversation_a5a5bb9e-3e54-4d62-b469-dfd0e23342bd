import traceback
from analytics import customer_order
from mongo_db import customer_db
import new_mongodb
import new_pgdb
from plugin import bc_customers
import logging
from datetime import datetime
from sqlalchemy import text

from utils import common

logger = logging.getLogger()


def get_customer_by_id(store, customer_id):
    return customer_db.fetch_customer_by_id(store, int(customer_id))


def process_customer_created(store, payload):
    try:
        if store:
            data = payload['data']
            customer_id = data['id']
            res = bc_customers.fetch_customer_by_id(store, customer_id)
            if res.status_code < 299:
                logger.info('Processing creating customer...')
                customer_json = res.json()
                customer = customer_json['data'][0]
                customer["_id"] = customer["id"]
                customer['loyalty_points'] = 10000
                customer['name'] = customer['first_name'] + \
                    " " + customer['last_name']
                add_loyalty_reward_history(store, 10000, customer["id"])
                customer_db.insert_into_customer(store, customer)
    except Exception as e:
        logger.error(traceback.format_exc())


def process_customer_updated(store, payload):
    try:
        if store:
            data = payload['data']
            customer_id = data['id']
            res = bc_customers.fetch_customer_by_id(store, customer_id)
            if res.status_code < 299:
                logger.info('Updating Customer...')
                customer_json = res.json()
                customer = bc_customers.process_bc_customers(
                    customer_json['data'])[0]
                customer["_id"] = customer["id"]
                customer_db.update_customer_by_id(store, customer)

    except Exception as e:
        logger.error(traceback.format_exc())


def process_customer_deleted(store, payload):
    if store:
        logger.info('Removing Customer From DB...')
        data = payload['data']
        customer_id = data['id']
        customer_db.remove_customer_by_id(store, customer_id)

def add_loyalty_reward_history(store, points, customer_id):   
    data = {}
    data['customer_id'] = int(customer_id)
    data['balance'] = int(points)
    data['earned/used'] = int(points)
    data['created_at'] = int(datetime.utcnow().timestamp())
    data['description'] = 'Earned ' + str(points) + ' Points through welcome bonus.'
    customer_db.add_customer_loyalty_history(store, data)

def get_customer_history(store, customer_id, payload):
    result = {
        'status': 401
    }
    if customer_id:
        db = new_mongodb.get_store_db_client(store)
        payload['customer_id'] = int(customer_id)
        categories, total_data_length, page, limit = common.get_paginated_records_updated(
            db, 'loyalty_history', payload, {},'')
        response = common.calculatePaginationData(
            categories, page, limit, total_data_length)
        
        for res in response['data']:
            if res['earned/used'] >= 0:
                res['rewards'] = res['earned/used']
                res['redemption'] = 0
            else:
                res['rewards'] = 0
                res['redemption'] = res['earned/used']
            order = customer_order.get_order_by_id(store['id'], res['order_id'])
            if order['status'] == 200:
                res['order_status'] = order['data']['status']
                res['order_total'] = order['data']['total']
            else:
                res['order_status'] = None
                res['order_total'] = None
        if response:                       
            result['status'] = 200
            result['data'] = response
        else:
            result['status'] = 404
            result['message'] = "There are no history for this customer."
    else:
        result['status'] = 404
        result['message'] = "Customer not found"

    return result

def get_customer_salesforce_data(store, customer_id):
    conn = new_pgdb.get_connection(store['id'])
    try:
        # Initial SQL query
        base_query = f"""
                SELECT customer_id, customer_name, type, rep_name, rep_email, rep_phone, rep_mobile, is_credit_card_payment_block
                FROM salesforce_customer_rep WHERE customer_id = :customer_id
        """
        result = conn.execute(text(base_query).params(customer_id=customer_id))
        customer_data = {}
        if result:
            for row in result:
                customer_data = {
                    'customer_id': row[0],
                    'customer_name': row[1],
                    'type': row[2] if row[2] else '',
                    'rep_name': row[3],
                    'rep_email': row[4],
                    'rep_phone': row[5],
                    'rep_mobile': row[6],
                    'is_credit_card_payment_block': row[7]
                }
        
    except Exception as e:
        raise e
    
    finally:
        if conn:
            conn.close()

    return customer_data