import calendar
from sqlalchemy import text
import task
import utils
import new_pgdb
from utils import common
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from decimal import Decimal
import copy
import logging
import traceback
from utils.common import calculatePaginationData, convert_to_timestamp
from collections import defaultdict
from new_pgdb.analytics_db import AnalyticsDB
import time

logger = logging.getLogger()

def _format_amount(amount):
    if amount >= 1000000:
        new_amount = "{:.1f}M".format(amount / 1000000)
    elif amount >= 1000:
        new_amount = "{:.1f}k".format(amount / 1000)
    else:
        new_amount = "{:,.0f}".format(amount)
    
    return new_amount


def _fetch_customers_profitability(conn, start_date, end_date, page, limit, sort_array, search, customer_group_ids, sales_rep_emails):
    offset = (page - 1) * limit

    # Build WHERE clause
    where_clauses = []

    if start_date and end_date:
        # Use the common function to get the date condition
        where_clauses.append(_get_date_range_condition(start_date, end_date, "ct.order_date"))

    if search:
        search = search.strip().replace(" ", "")
        search_condition = (
            f"(c.first_name ILIKE '%{search}%' "
            f"OR c.last_name ILIKE '%{search}%' "
            f"OR REPLACE(CONCAT(c.first_name,' ',c.last_name), ' ', '') ILIKE '%{search}%' "
            f"OR c.email ILIKE '%{search}%')"
        )
        where_clauses.append(search_condition)
    
    if customer_group_ids:
        where_clauses.append("c.customer_group_id = ANY(:customer_group_ids)")
    
    sales_rep_emails_array = []
    if sales_rep_emails:
        sales_rep_emails_array = sales_rep_emails.split(",") if isinstance(sales_rep_emails, str) else []
        where_clauses.append("scr.rep_email = ANY(:sales_rep_emails)")

    where_sql = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""
    
    base_query =f"""SELECT
                        ct.customer_id,
                        (SUM(ct.revenue) + COALESCE(sum(sc.shipping_cost), 0)) AS total_revenue,
                        (SUM(ct.profit) + COALESCE(sum(sc.shipping_cost), 0)) AS total_profit,
                        SUM(ct.total_cost) AS total_cost,
                        SUM(ct.orders) AS total_orders,
                        CONCAT(c.first_name, ' ', c.last_name) AS name,
                        c.email,
                        c.customer_group_id,
                        c.customer_group_name,
                        scr.rep_name,
                        scr.rep_email,
                        scr.payment_term
                    FROM
                        {AnalyticsDB.get_customers_trend_table()} ct
                    JOIN 
                        {new_pgdb.DBTables.customers_table} c ON c.customer_id = ct.customer_id
                    JOIN 
                        {new_pgdb.DBTables.salesforce_customer_rep} scr ON scr.customer_id = c.customer_id
                    LEFT JOIN  
                        {AnalyticsDB.get_customers_trend_shipping_cost_table()} sc ON ct.customer_id = sc.customer_id and ct.order_date = sc.order_date
                    {where_sql}
                    GROUP BY
                        ct.customer_id,
                        c.first_name,
                        c.last_name,
                        c.email,
                        c.customer_group_id,
                        c.customer_group_name,
                        scr.rep_name,
                        scr.rep_email,
                        scr.payment_term
                   """
    
    if len(sort_array):
        sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
        nulls_order = "NULLS FIRST" if sort_direction.lower() == "asc" else "NULLS LAST"        
        if sort_array[0] in ["total_revenue", "total_profit", "total_cost", "total_orders", "name", "email", "customer_group_name", "rep_name", "rep_email"]:                
            base_query += f" ORDER BY {sort_array[0]} {sort_direction} {nulls_order}"
        
    base_query += " LIMIT :limit OFFSET :offset"

    # Query Parameters
    params = {
        "limit": limit,
        "offset": offset,
    }

    if start_date and end_date:
        # Use the common function to prepare date parameters
        date_params = _prepare_date_range_params(start_date, end_date)
        params.update(date_params)
    if customer_group_ids:
        params["customer_group_ids"] = customer_group_ids
    if sales_rep_emails_array:
        params["sales_rep_emails"] = sales_rep_emails_array
    

    result = conn.execute(text(base_query), params)
    data = []
    
    for row in result.fetchall():
        total_revenue = float(round(row[1] or 0, 2))
        total_profit = float(round(row[2] or 0, 2))
        total_cost = float(round(row[3] or 0, 2))
        total_orders = int(row[4] or 0)
        data.append({
            "customer_id": row[0],
            "total_revenue": total_revenue,
            "total_profit": total_profit,
            "total_cost": total_cost,
            "total_orders": total_orders,
            "name": row[5],
            "email": row[6],
            "customer_group_id": row[7],
            "customer_group_name": row[8],
            "rep_name": row[9],
            "rep_email": row[10],
            "payment_term": row[11]
        })

    # Count Query
    count_query = f"""
        SELECT COUNT(DISTINCT ct.customer_id)
        FROM
            {AnalyticsDB.get_customers_trend_table()} ct
        JOIN 
            {new_pgdb.DBTables.customers_table} c ON c.customer_id = ct.customer_id
        JOIN 
            {new_pgdb.DBTables.salesforce_customer_rep} scr ON scr.customer_id = c.customer_id
        {where_sql}
    """
    filtered_params = {k: v for k, v in params.items() if k not in ['limit']}
    total_count = conn.execute(text(count_query), filtered_params).scalar()

    # Total Profit Query
    exclude_keywords = ["c.customer_group_id", "c.first_name"]
    total_where_clauses = [clause for clause in where_clauses if not any(keyword in clause for keyword in exclude_keywords)]
    total_where_sql = f"WHERE {' AND '.join(total_where_clauses)}" if total_where_clauses else ""
    total_profit_query = f"""
        SELECT (SUM(ct.profit) + COALESCE(sum(sc.shipping_cost), 0)) AS total_profit,
        (SUM(ct.revenue) + COALESCE(sum(sc.shipping_cost), 0)) AS total_revenue
        FROM
            {AnalyticsDB.get_customers_trend_table()} ct
        JOIN 
            {new_pgdb.DBTables.customers_table} c ON c.customer_id = ct.customer_id
        JOIN 
            {new_pgdb.DBTables.salesforce_customer_rep} scr ON scr.customer_id = c.customer_id
        LEFT JOIN  
            {AnalyticsDB.get_customers_trend_shipping_cost_table()} sc ON ct.customer_id = sc.customer_id and ct.order_date = sc.order_date
        {total_where_sql}
    """
    result = conn.execute(text(total_profit_query), {k: v for k, v in params.items() if k not in ['customer_group_ids', 'search']})
    total_profit, total_revenue = result.fetchone()

    customers_profitability = _calculate_profit_and_contribution(data, total_profit, total_revenue)
    
    return {
        "total_count": total_count,
        "customers": customers_profitability
    }

def _calculate_profit_and_contribution(data, total_profit_all_groups=None, total_revenue_all_groups=None, calculate_profit_percentage=True):
    """Calculate profit percentage and contributed profit."""

    # Calculate total profit if not passed
    if total_profit_all_groups is None:
        total_profit_all_groups = sum(group["total_profit"] for group in data)
    
    if total_revenue_all_groups is None:
        total_revenue_all_groups = sum((group.get("total_revenue") or group.get("total_sales") or 0) for group in data)

    for group in data:
        total_revenue = float(group.get("total_revenue") or group.get("total_sales") or 0)
        total_profit = float(group["total_profit"] or 0)

        if calculate_profit_percentage:
            # Calculate profit percentage (profit relative to revenue)
            group["profit_percentage"] = round(total_profit / total_revenue  * 100, 2) if total_revenue else 0

        # Calculate contributed profit percentage (relative to total)
        group["contributed_profit_percentage"] = round(
            (float(total_profit) / float(total_profit_all_groups) * 100), 2
        ) if total_profit_all_groups else 0

        group["contributed_revenue_percentage"] = round(
            (float(total_revenue) / float(total_revenue_all_groups) * 100), 2
        ) if total_revenue_all_groups else 0

    return data


def _fetch_customer_groups_profitability(conn, start_date, end_date, sort_array, customer_group_ids):
    if customer_group_ids:
        where_condition = "c.customer_group_id = ANY(:customer_group_ids)"
    else:
        where_condition = "c.customer_group_id IN (1, 19, 28, 32, 45, 48, 75, 90, 91, 96, 98, 103, 104, 105)"

    # First get all customer groups
    query = text(f"""
        SELECT DISTINCT c.customer_group_id, c.customer_group_name 
        FROM {new_pgdb.DBTables.customers_table} c
        WHERE {where_condition}
        ORDER BY c.customer_group_id
    """)
    
    if customer_group_ids:
        result = conn.execute(query.params(customer_group_ids=customer_group_ids)).fetchall()
    else:
        result = conn.execute(query).fetchall()
    customer_groups = {
        row[0]: {
            "total_revenue": 0,
            "total_profit": 0,
            "total_cost": 0,
            "total_orders": 0,
            "customer_group_id": int(row[0]),
            "customer_group_name": row[1],
            "profit_percentage": 0
        }
        for row in result
    }

    # Get metrics for customer groups with data
    base_query = text(f"""
        SELECT
            (sum(ct.revenue) + COALESCE(sum(sc.shipping_cost), 0)) as total_revenue,
            (sum(ct.profit) + COALESCE(sum(sc.shipping_cost), 0)) AS total_profit,
            sum(ct.total_cost) as total_cost,
            sum(ct.orders) as total_orders,
            c.customer_group_id,
            c.customer_group_name
        FROM
            {new_pgdb.DBTables.customers_table} c
        LEFT JOIN
            {AnalyticsDB.get_customers_trend_table()} ct ON c.customer_id = ct.customer_id
        LEFT JOIN  
            {AnalyticsDB.get_customers_trend_shipping_cost_table()} sc ON ct.customer_id = sc.customer_id and ct.order_date = sc.order_date
        WHERE
            {_get_date_range_condition(start_date, end_date, "ct.order_date")} 
            AND {where_condition}
        GROUP BY
            c.customer_group_id,
            c.customer_group_name
        ORDER BY
            total_profit DESC
    """)

    if customer_group_ids:
        # Use the common function to prepare date parameters
        date_params = _prepare_date_range_params(start_date, end_date)
        params = {**date_params, "customer_group_ids": customer_group_ids}
        result = conn.execute(base_query.params(**params)).fetchall()
    else:
        # Use the common function to prepare date parameters
        date_params = _prepare_date_range_params(start_date, end_date)
        result = conn.execute(base_query.params(**date_params)).fetchall()
    
    # Update customer_groups with actual data
    for row in result:
        if row[4] in customer_groups:  # Check if group exists in our dictionary
            customer_groups[row[4]].update({
                "total_revenue": float(round(row[0] or 0, 2)),
                "total_profit": float(round(float(row[0]) - float(row[2]), 2)),
                "total_cost": float(round(row[2] or 0, 2)),
                "total_orders": int(row[3] or 0)
            })
    
    # Convert dictionary to list
    customer_groups_list = list(customer_groups.values())

    total_profit = 0 
    total_revenue = 0
    if customer_group_ids:
        total_profit_query = f"""SELECT (sum(ct.profit) + COALESCE(sum(sc.shipping_cost), 0)) AS total_profit,
                                (sum(ct.revenue) + COALESCE(sum(sc.shipping_cost), 0)) as total_revenue
                            FROM {new_pgdb.DBTables.customers_table} c
                            LEFT JOIN
                                {AnalyticsDB.get_customers_trend_table()} ct ON c.customer_id = ct.customer_id
                            LEFT JOIN  
                                {AnalyticsDB.get_customers_trend_shipping_cost_table()} sc ON ct.customer_id = sc.customer_id and ct.order_date = sc.order_date
                            WHERE
                                {_get_date_range_condition(start_date, end_date, "ct.order_date")} 
                                AND c.customer_group_id IN (1, 19, 28, 32, 45, 48, 75, 90, 91, 96, 98, 103, 104, 105)"""
        # Use the common function to prepare date parameters
        date_params = _prepare_date_range_params(start_date, end_date)
        result = conn.execute(text(total_profit_query).params(**date_params))
        total_profit, total_revenue = result.fetchone()


    # Apply profit and contribution calculation
    if customer_group_ids:
        customer_groups_list = _calculate_profit_and_contribution(customer_groups_list, total_profit, total_revenue)
    else:
        customer_groups_list = _calculate_profit_and_contribution(customer_groups_list)

    # # Convert dictionary to list and sort by profit percentage
    sort_direction = True
    sort_column = "total_profit"
    if len(sort_array):
        sort_direction = False if sort_array[1] == '1' else True
        if sort_array[0] in ["total_revenue", "total_profit", "total_cost", "total_orders", "customer_group_name", "profit_percentage", "contributed_profit_percentage", "contributed_revenue_percentage"]:                
            sort_column = sort_array[0]
    data = sorted(
        customer_groups_list,
        key=lambda x: x.get(sort_column, 0),
        reverse=sort_direction
    )

    return {
        "customer_groups": data
    }

def get_customers_profitability_report(store , start_date, end_date, page, limit, sort_array, search, customer_group_id, sales_rep_emails):

    # current_date = datetime.datetime.now()
    # month_start_date = current_date - datetime.timedelta(days=current_date.day - 1) - datetime.timedelta(weeks=12)

    # month_start_date = month_start_date.replace(day=1).date()
    # month_end_date = current_date.date()

    # _months = _get_months_in_range(copy.deepcopy(month_start_date), copy.deepcopy(month_end_date))
    # _months.reverse()
    if customer_group_id:
        customer_group_ids = list(map(int, customer_group_id.split(","))) if isinstance(customer_group_id, str) else []
    else:
        customer_group_ids = []

    tasks = []
    tasks.append({
        "func": _fetch_customers_profitability,
        "args": [start_date, end_date, page, limit, sort_array, search, customer_group_ids, sales_rep_emails]
    })

    range_data = utils.concurrent_db_execution(store, tasks)
    total_count = range_data[0].get('total_count', 0)
    customers_data = range_data[0].get('customers', {})
    
    data = calculatePaginationData(customers_data, page, limit, total_count)

    return data

def get_customer_groups_profitability_report(store , start_date, end_date, sort_array, customer_group_id):

    if customer_group_id:
        customer_group_ids = list(map(int, customer_group_id.split(","))) if isinstance(customer_group_id, str) else []
    else:
        customer_group_ids = []

    tasks = []
    tasks.append({
        "func": _fetch_customer_groups_profitability,
        "args": [start_date, end_date, sort_array, customer_group_ids]
    })

    range_data = utils.concurrent_db_execution(store, tasks)
    customer_groups_data = range_data[0].get('customer_groups', [])
    

    return customer_groups_data

def get_report_summary(store, start_date, end_date):
    conn = new_pgdb.get_connection(store['id'])
    try:
        # Prepare date parameters
        params = _prepare_date_range_params(start_date, end_date)
        date_condition = _get_date_range_condition(start_date, end_date, "ct.order_date")
        
        # 1. Get total metrics with a simpler query
        total_metrics_query = text(f"""
            SELECT 
                (sum(ct.revenue) + COALESCE(sum(sc.shipping_cost), 0)) as total_revenue, 
                (SUM(ct.profit) + COALESCE(sum(sc.shipping_cost), 0)) AS total_profit,
                sum(ct.total_cost) as total_cost,
                sum(ct.orders) as total_orders
            FROM {AnalyticsDB.get_customers_trend_table()} ct
            LEFT JOIN {AnalyticsDB.get_customers_trend_shipping_cost_table()} sc 
                ON ct.customer_id = sc.customer_id and ct.order_date = sc.order_date
            WHERE {date_condition}
        """)
        
        total_metrics_result = conn.execute(total_metrics_query, params).fetchone()
        
        # Extract total metrics
        total_revenue = float(round(total_metrics_result[0] or 0, 2))
        total_profit = float(round(total_metrics_result[1] or 0, 2))
        total_cost = float(round(total_metrics_result[2] or 0, 2))
        total_orders = int(total_metrics_result[3] or 0)
        
        # 2. Get customer type metrics with a separate query
        customer_type_metrics_query = text(f"""
            SELECT
                CASE 
                    WHEN scr.type = 'Distributor' THEN 'distributor'
                    ELSE 'other'
                END as customer_type,
                (sum(ct.revenue) + COALESCE(sum(sc.shipping_cost), 0)) as type_revenue,
                (SUM(ct.profit) + COALESCE(sum(sc.shipping_cost), 0)) AS type_profit,
                sum(ct.total_cost) as type_cost,
                sum(ct.orders) as type_orders
            FROM {AnalyticsDB.get_customers_trend_table()} ct
            JOIN {new_pgdb.DBTables.salesforce_customer_rep} scr ON scr.customer_id = ct.customer_id
            LEFT JOIN {AnalyticsDB.get_customers_trend_shipping_cost_table()} sc 
                ON ct.customer_id = sc.customer_id and ct.order_date = sc.order_date
            WHERE {date_condition}
            GROUP BY 
                CASE 
                    WHEN scr.type = 'Distributor' THEN 'distributor'
                    ELSE 'other'
                END
        """)
        
        customer_type_results = conn.execute(customer_type_metrics_query, params).fetchall()
        
        # Process customer type metrics
        distributor_revenue = 0
        distributor_profit = 0
        distributor_orders = 0
        other_revenue = 0
        other_profit = 0
        other_orders = 0
        
        for row in customer_type_results:
            if row[0] == 'distributor':
                distributor_revenue = float(round(row[1] or 0, 2))
                distributor_profit = float(round(row[2] or 0, 2))
                distributor_orders = int(row[4] or 0)
            else:
                other_revenue = float(round(row[1] or 0, 2))
                other_profit = float(round(row[2] or 0, 2))
                other_orders = int(row[4] or 0)
        
        # Calculate percentages
        distributor_percentage = float(round((distributor_revenue / total_revenue * 100), 2)) if total_revenue > 0 else 0
        other_percentage = float(round((other_revenue / total_revenue * 100), 2)) if total_revenue > 0 else 0
        
        distributor_profit_percentage = float(round((distributor_profit / distributor_revenue * 100), 2)) if distributor_revenue > 0 else 0
        other_profit_percentage = float(round((other_profit / other_revenue * 100), 2)) if other_revenue > 0 else 0
        
        threshold_date_condition = _get_date_range_condition(start_date, end_date, "order_date")
        # 3. Get top 20% customers with a more efficient approach
        # First, get the threshold for top 20%
        threshold_query = text(f"""
            SELECT sum(revenue) * 0.2 as threshold_revenue
            FROM (
                SELECT sum(revenue) as revenue
                FROM {AnalyticsDB.get_customers_trend_table()}
                WHERE {threshold_date_condition}
                GROUP BY customer_id
            ) customer_revenue
        """)
        
        threshold_result = conn.execute(threshold_query, params).scalar()
        threshold_revenue = float(threshold_result or 0)
        
        # Then get top customers that contribute to 20% of revenue
        top_customers_query = text(f"""
            WITH customer_revenue AS (
                SELECT 
                    customer_id,
                    sum(revenue) as customer_revenue,
                    sum(orders) as customer_orders
                FROM {AnalyticsDB.get_customers_trend_table()}
                WHERE {threshold_date_condition}
                GROUP BY customer_id
                ORDER BY customer_revenue DESC
            ),
            cumulative_revenue AS (
                SELECT 
                    customer_id,
                    customer_revenue,
                    customer_orders,
                    sum(customer_revenue) OVER (ORDER BY customer_revenue DESC) as running_total
                FROM customer_revenue
            )
            SELECT 
                count(DISTINCT customer_id) as top_customers_count,
                sum(customer_revenue) as top_revenue,
                sum(customer_orders) as top_orders
            FROM cumulative_revenue
            WHERE running_total <= :threshold_revenue
        """)
        
        # Add threshold to params
        params["threshold_revenue"] = threshold_revenue
        
        top_customers_result = conn.execute(top_customers_query, params).fetchone()
        
        # Extract top customers metrics
        top_customers_count = int(top_customers_result[0] or 0)
        top_revenue = float(round(top_customers_result[1] or 0, 2))
        top_orders = int(top_customers_result[2] or 0)
        revenue_percentage = float(round((top_revenue / total_revenue * 100), 2)) if total_revenue > 0 else 0
        
        # Return the complete summary
        return {
            "total_metrics": {
                "revenue": total_revenue,
                "revenue_label": _format_amount(total_revenue),
                "profit": total_profit,
                "profit_label": _format_amount(total_profit),
                "cost": total_cost,
                "cost_label": _format_amount(total_cost),
                "orders": total_orders
            },
            "customer_type_metrics": {
                "distributor": {
                    "revenue_label": _format_amount(distributor_revenue),
                    "revenue": distributor_revenue,
                    "profit_label": _format_amount(distributor_profit),
                    "profit": distributor_profit,
                    "orders": distributor_orders,
                    "revenue_percentage": distributor_percentage,
                    "profit_percentage": distributor_profit_percentage
                },
                "other": {
                    "revenue_label": _format_amount(other_revenue),
                    "revenue": other_revenue,
                    "profit_label": _format_amount(other_profit),
                    "profit": other_profit,
                    "orders": other_orders,
                    "revenue_percentage": other_percentage,
                    "profit_percentage": other_profit_percentage
                }
            },
            "top_20_percent": {
                "customer_count": top_customers_count,
                "revenue_label": _format_amount(top_revenue),
                "revenue": top_revenue,
                "orders": top_orders,
                "revenue_percentage": revenue_percentage
            }
        }
        
    except Exception as e:
        logger.error(f"Error in get_report_summary: {e}")
        raise e
    finally:
        conn.close()


def get_customer_types_profitability_report(store_id, start_date, end_date, sort_array):
    conn = new_pgdb.get_connection(store_id)
    try:
        query = f"""SELECT
                    scr.type AS customer_type,
                    (SUM(ct.revenue) + COALESCE(sum(sc.shipping_cost), 0)) AS total_sales,
                    SUM(ct.total_cost) AS total_cost,
                    (SUM(ct.profit) + COALESCE(sum(sc.shipping_cost), 0)) AS total_profit,
                    SUM(ct.orders) AS total_orders
                FROM
                    {new_pgdb.DBTables.salesforce_customer_rep} scr
                JOIN
                    {AnalyticsDB.get_customers_trend_table()} ct
                    ON scr.customer_id = ct.customer_id
                LEFT JOIN  
                        {AnalyticsDB.get_customers_trend_shipping_cost_table()} sc ON ct.customer_id = sc.customer_id and ct.order_date = sc.order_date
                WHERE
                    {_get_date_range_condition(start_date, end_date, "ct.order_date")}
                GROUP BY
                    scr.type"""
        
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            nulls_order = "NULLS FIRST" if sort_direction.lower() == "asc" else "NULLS LAST" 
            if sort_array[0] in ["customer_type", "total_sales", "total_cost", "total_profit", "total_orders"]:
                query += f" ORDER BY {sort_array[0]} {sort_direction} {nulls_order}"                
        
        # Use the common function to prepare date parameters
        date_params = _prepare_date_range_params(start_date, end_date)
        result = conn.execute(text(query), date_params).fetchall()
        data = []
        for row in result:
            data.append(
                {
                    "customer_type": row[0],
                    "total_sales": float(round(row[1] or 0, 2)),
                    "total_cost": float(round(row[2] or 0, 2)),
                    "total_profit": float(round(row[3] or 0, 2)),
                    "total_orders": int(row[4] or 0)
                }
            )

        customer_types = _calculate_profit_and_contribution(data)

        if len(sort_array) and sort_array[0] in ["profit_percentage", "contributed_profit_percentage", "contributed_revenue_percentage"]:
            sort_direction = False if sort_array[1] == '1' else True
            sort_column = sort_array[0]
            customer_types = sorted(customer_types, key=lambda x: x.get(sort_column, 0), reverse=sort_direction)

        return customer_types
    except Exception as e:
        logger.error(f"Error in get_customer_types_profitability_report: {e}")
        raise e
    finally:
        conn.close()

def get_customer_reps_profitability_report(store_id, start_date, end_date, search, page=1, limit=10, sort_array=[], sales_rep_type=''):
    conn = new_pgdb.get_connection(store_id)
    try:
        # Build WHERE clause
        where_clauses = []
        where_clauses.append("scr.rep_name IS NOT NULL AND scr.rep_name != ''")
        if start_date and end_date:
            where_clauses.append(_get_date_range_condition(start_date, end_date, "ct.order_date"))

        if search:
            search = search.strip()
            search_condition = (f" scr.rep_name ILIKE '%{search}%'")
            where_clauses.append(search_condition)    
        
        sales_rep_type_array = []
        if sales_rep_type != '':
            sales_rep_type_array = list(map(str, sales_rep_type.split(","))) if isinstance(sales_rep_type, str) else []
            where_clauses.append("scr.rep_type = ANY(:sales_rep_type)")

        where_sql = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""

        query = f"""SELECT
                    scr.rep_name AS customer_rep,
                    (SUM(ct.revenue) + COALESCE(sum(sc.shipping_cost), 0)) AS total_sales,
                    SUM(ct.total_cost) AS total_cost,
                    (SUM(ct.profit) + COALESCE(sum(sc.shipping_cost), 0)) AS total_profit,
                    scr.rep_type,
                    SUM(ct.orders) AS total_orders,
                    scr.rep_email,
                    COUNT(CASE WHEN bo.profit_percentage >= 0 THEN 1 END) AS profitable_orders,
                    COUNT(CASE WHEN bo.profit_percentage < 0 THEN 1 END) AS non_profitable_orders
                FROM
                    {new_pgdb.DBTables.salesforce_customer_rep} scr
                LEFT JOIN
                    {AnalyticsDB.get_customers_trend_table()} ct
                    ON scr.customer_id = ct.customer_id
                LEFT JOIN  
                    {AnalyticsDB.get_customers_trend_shipping_cost_table()} sc ON ct.customer_id = sc.customer_id and ct.order_date = sc.order_date
                LEFT JOIN
                    {AnalyticsDB.get_order_analytics_table()} bo ON scr.customer_id = bo.customer_id AND bo.order_date = ct.order_date
                {where_sql} GROUP BY scr.rep_name, scr.rep_type, scr.rep_email """

        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            nulls_order = "NULLS FIRST" if sort_direction.lower() == "asc" else "NULLS LAST" 
            if sort_array[0] in ["customer_rep", "total_sales", "total_cost", "total_profit", "rep_type", "total_orders", "rep_email", "profitable_orders", "non_profitable_orders"]:
                query += f" ORDER BY {sort_array[0]} {sort_direction} {nulls_order}"                
        
        params = {}
        if start_date and end_date:
            # Use the common function to prepare date parameters
            date_params = _prepare_date_range_params(start_date, end_date)
            params.update(date_params)
        if sales_rep_type_array:
            params["sales_rep_type"] = sales_rep_type_array
        
        result = conn.execute(text(query), params).fetchall()
        data = []
        for row in result:
            data.append(
                {
                    "customer_rep": row[0],
                    "total_sales": float(round(row[1] or 0, 2)),
                    "total_cost": float(round(row[2] or 0, 2)),
                    "total_profit": float(round(row[3] or 0, 2)),
                    "rep_type": row[4],
                    "total_orders": int(row[5] or 0),
                    "rep_email": row[6],
                    "profitable_orders": int(row[7]) if row[7] is not None else None,
                    "non_profitable_orders": int(row[8]) if row[8] is not None else None
                }
            )
        
        # count_query = f"""SELECT
        #                     COUNT(DISTINCT scr.rep_name) AS total_count
        #                 FROM
        #                     {new_pgdb.DBTables.salesforce_customer_rep} scr
        #                 LEFT JOIN
        #                     {AnalyticsDB.get_customers_trend_table()} ct
        #                     ON scr.customer_id = ct.customer_id
        #                 LEFT JOIN
        #                     {AnalyticsDB.get_customers_trend_shipping_cost_table()} ctc ON scr.customer_id = ctc.customer_id
        #                 {where_sql}
        #                 """
        # total_count = conn.execute(text(count_query), params).scalar()
        
        exclude_keywords = ["scr.rep_type", "scr.rep_name"]
        total_where_clauses = [clause for clause in where_clauses if not any(keyword in clause for keyword in exclude_keywords)]
        total_where_sql = f"WHERE {' AND '.join(total_where_clauses)}" if total_where_clauses else ""
        total_profit_query = f"""SELECT
                                    (sum(ct.profit) + COALESCE(sum(sc.shipping_cost), 0)) AS total_profit,
                                    (SUM(ct.revenue) + COALESCE(sum(sc.shipping_cost), 0)) AS total_revenue
                                FROM
                                    {new_pgdb.DBTables.salesforce_customer_rep} scr
                                LEFT JOIN
                                    {AnalyticsDB.get_customers_trend_table()} ct
                                    ON scr.customer_id = ct.customer_id
                                LEFT JOIN  
                                    {AnalyticsDB.get_customers_trend_shipping_cost_table()} sc ON ct.customer_id = sc.customer_id and ct.order_date = sc.order_date
                                {total_where_sql} """

        result = conn.execute(text(total_profit_query), {k: v for k, v in params.items() if k not in ['sales_rep_type', 'search']})
        total_profit, total_revenue = result.fetchone()

        customer_reps = _calculate_profit_and_contribution(data, total_profit, total_revenue)
        
        if len(sort_array) and sort_array[0] in ["profit_percentage", "contributed_profit_percentage", "contributed_revenue_percentage"]:
            sort_direction = False if sort_array[1] == '1' else True
            sort_column = sort_array[0]
            customer_reps = sorted(customer_reps, key=lambda x: x.get(sort_column, 0), reverse=sort_direction)
        # paginated_data = calculatePaginationData(customer_reps, page, limit, total_count)

        return {"data": customer_reps}
    except Exception as e:
        logger.error(f"Error in get_customer_reps_profitability_report: {e}")
        raise e
    finally:
        conn.close()


def get_puchasers_profitability_report(store_id, start_date, end_date, sort_array):
    conn = new_pgdb.get_connection(store_id)
    try:
        query = f"""
        SELECT 
            ol.purchaser_name,
            SUM(ol.total_ex_tax) AS total_sales,
            SUM(ol.total_cost) AS total_cost,
            SUM(ol.profit) AS total_profit,
            COUNT(DISTINCT ol.order_id) AS total_orders
        FROM analytics.bc_order_line_items ol
        WHERE {_get_date_range_condition(start_date, end_date, "ol.order_created_date_time")}
        GROUP BY ol.purchaser_name
        """
        
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            nulls_order = "NULLS FIRST" if sort_direction.lower() == "asc" else "NULLS LAST" 
            if sort_array[0] in ["purchaser_name", "total_sales", "total_cost", "total_profit", "total_orders"]:
                query += f" ORDER BY {sort_array[0]} {sort_direction} {nulls_order}"   

        # Use the common function to prepare date parameters
        params = _prepare_date_range_params(start_date, end_date)
        result = conn.execute(text(query), params).fetchall()
        purchasers = []
        for row in result:
            purchasers.append({
                "purchaser_name": row[0],
                "total_sales": float(round(row[1] or 0, 2)),
                "total_cost": float(round(row[2] or 0, 2)),
                "total_profit": float(round(row[3] or 0, 2)),
                "total_orders": int(row[4] or 0)
            })

        data = _calculate_profit_and_contribution(purchasers)

        if len(sort_array) and sort_array[0] in ["profit_percentage", "contributed_profit_percentage", "contributed_revenue_percentage"]:
            sort_direction = False if sort_array[1] == '1' else True
            sort_column = sort_array[0]
            data = sorted(data, key=lambda x: x.get(sort_column, 0), reverse=sort_direction)

        return data
    except Exception as e:
        logger.error(f"Error in get_puchasers_profitability_report: {e}")
        raise e
    finally:
        conn.close()


def get_product_wise_profitability_report(store_id, start_date, end_date, search, page, limit, sort_array, classification_filter=None, products_filter=None, supplier_filter=None):
    conn = new_pgdb.get_connection(store_id)
    try:
        offset = (page - 1) * limit

        # Build WHERE clause
        where_clauses = []
        CTE_where_clauses = []
        if start_date and end_date:
            where_clauses.append(_get_date_range_condition(start_date, end_date, "apr.order_date"))

        if search:
            search = search.strip()
            # Check if search is a number or starts with SKU
            if search.isdigit() or search.upper().startswith('SKU'): 
                search_condition = (f" apr.parent_sku ILIKE '%{search}%' ")
            else:
                search_condition = (f" p.product_name ILIKE '%{search}%'")
            where_clauses.append(search_condition)
        
        classification_array = []
        if classification_filter:
            classification_array = classification_filter.split(',')
            # where_clauses.append("sv.classification = ANY(:classification)")
            CTE_where_clauses.append("classification IN :classification_filter_list")

        supplier_array = []
        if supplier_filter:
            supplier_array = supplier_filter.split(';')
            # where_clauses.append("sv.primary_supplier = ANY(:primary_supplier)")
            CTE_where_clauses.append("primary_supplier IN :primary_supplier_list")
        
        products_ids = []
        if products_filter:
            products_ids = list(map(int, products_filter.split(','))) if isinstance(products_filter, str) else []
            where_clauses.append("apr.product_id = ANY(:products_ids)")

        where_sql = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""
        cte_where_sql = f"WHERE {' AND '.join(CTE_where_clauses)}" if CTE_where_clauses else ""
        query = f"""WITH sv_aggregated AS (
                        SELECT
                            parent_sku,
                            ARRAY_AGG(DISTINCT classification) AS classification,
                            ARRAY_AGG(DISTINCT primary_supplier) AS primary_supplier
                        FROM
                            {new_pgdb.DBTables.skuvault_catalog}
                        {cte_where_sql}
                        GROUP BY
                            parent_sku
                    )
                SELECT
                    apr.parent_sku AS product_sku, apr.product_id, p.product_name,
                    SUM(apr.revenue) AS total_sales,
                    SUM(apr.total_cost) AS total_cost,
                    SUM(apr.profit) AS total_profit,
                    sv.classification,
                    sv.primary_supplier,
                    SUM(apr.orders) AS total_orders
                FROM
                    {AnalyticsDB.get_products_revenue_table()} apr
                LEFT JOIN
                    {new_pgdb.DBTables.products} p ON apr.product_id = p.product_id
                INNER JOIN sv_aggregated sv ON sv.parent_sku = p.sku
                {where_sql}
                GROUP BY apr.parent_sku, apr.product_id, p.product_name, sv.classification, sv.primary_supplier"""

        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            nulls_order = "NULLS FIRST" if sort_direction.lower() == "asc" else "NULLS LAST"        
            if sort_array[0] in ["total_sales", "total_profit", "total_cost", "product_sku", "product_name", "classification", "primary_supplier", "total_orders"]:                
                query += f" ORDER BY {sort_array[0]} {sort_direction} {nulls_order}"
        else:
            query += f" ORDER BY p.total_profit {sort_direction} {nulls_order}"
        
        query += " LIMIT :limit OFFSET :offset"

        # Query Parameters
        params = {
            "limit": limit,
            "offset": offset,
        }
        
        if start_date and end_date:
            # Use the common function to prepare date parameters
            date_params = _prepare_date_range_params(start_date, end_date)
            params.update(date_params)
        if classification_array:
            params["classification_filter_list"] = tuple(classification_array)
        if supplier_array:
            params["primary_supplier_list"] = tuple(supplier_array)
        if products_ids:
            params["products_ids"] = products_ids

        result = conn.execute(text(query), params).fetchall()
        
        data = []
        for row in result:
            data.append({
                "product_sku": row[0],
                "product_id": row[1],
                "product_name": row[2],
                "total_sales": float(round(row[3] or 0, 2)),
                "total_cost": float(round(row[4] or 0, 2)),
                "total_profit": float(round(row[5] or 0, 2)),
                "classification": row[6],
                "primary_supplier": row[7],
                "total_orders": int(row[8] or 0)
            })

        count_query = f"""
                    WITH sv_aggregated AS (
                        SELECT
                            parent_sku,
                            ARRAY_AGG(DISTINCT classification) AS classifications,
                            ARRAY_AGG(DISTINCT primary_supplier) AS primary_suppliers
                        FROM
                            {new_pgdb.DBTables.skuvault_catalog}
                        {cte_where_sql}
                        GROUP BY
                            parent_sku
                    )
                SELECT COUNT(DISTINCT apr.parent_sku) AS total_count
                FROM
                    {AnalyticsDB.get_products_revenue_table()} apr
                LEFT JOIN
                    {new_pgdb.DBTables.products} p ON p.product_id = apr.product_id
                INNER JOIN sv_aggregated sv ON sv.parent_sku = p.sku
                {where_sql}
                """
        filtered_params = {k: v for k, v in params.items() if k not in ['limit']}
        total_count = conn.execute(text(count_query), filtered_params).scalar()

        exclude_keywords = ["classification", "primary_supplier", "p.product_name", "apr.product_id", "apr.parent_sku"]
        total_cte_where_clauses = [clause for clause in CTE_where_clauses if not any(keyword in clause for keyword in exclude_keywords)]
        total_cte_where_sql = f"WHERE {' AND '.join(total_cte_where_clauses)}" if total_cte_where_clauses else ""
        total_where_clauses = [clause for clause in where_clauses if not any(keyword in clause for keyword in exclude_keywords)]
        total_where_sql = f"WHERE {' AND '.join(total_where_clauses)}" if total_where_clauses else ""

        total_profit_query = f"""WITH sv_aggregated AS (
                                    SELECT
                                        parent_sku,
                                        ARRAY_AGG(DISTINCT classification) AS classifications,
                                        ARRAY_AGG(DISTINCT primary_supplier) AS primary_suppliers
                                    FROM
                                        {new_pgdb.DBTables.skuvault_catalog}
                                    {total_cte_where_sql}
                                    GROUP BY
                                        parent_sku
                                )
                                SELECT
                                    SUM(apr.profit) AS total_profit,
                                    SUM(apr.revenue) AS total_revenue
                                FROM
                                    {AnalyticsDB.get_products_revenue_table()} apr
                                LEFT JOIN
                                    {new_pgdb.DBTables.products} p ON p.product_id = apr.product_id
                                INNER JOIN sv_aggregated sv ON sv.parent_sku = p.sku
                                {total_where_sql}
                                """
        result = conn.execute(text(total_profit_query), {k: v for k, v in params.items() if k not in ['classification_filter_list', 'primary_supplier_list', 'products_ids', 'search']})
        total_profit, total_revenue = result.fetchone() 

        products = _calculate_profit_and_contribution(data, total_profit, total_revenue)

        paginated_data = calculatePaginationData(products, page, limit, total_count)

        return paginated_data
    except Exception as e:
        logger.error(f"Error in get_product_wise_profitability_report: {e}")
        raise e
    finally:
        conn.close()

def get_classification_profitability_report(store_id, start_date, end_date, search, page, limit, sort_array, classification_filter):
    conn = new_pgdb.get_connection(store_id)
    try:
        offset = (page - 1) * limit

        # Build WHERE clause for skuvault_catalog
        catalog_where_clauses = []
        catalog_where_clauses.append("sc.classification IS NOT NULL AND sc.classification != ''")
        
        if search:
            search = search.strip()
            catalog_where_clauses.append("sc.classification ILIKE :search_pattern")
        
        classification_array = []
        if classification_filter:
            classification_array = classification_filter.split(',')
            catalog_where_clauses.append("sc.classification = ANY(:classification_list)")

        catalog_where_sql = f"WHERE {' AND '.join(catalog_where_clauses)}" if catalog_where_clauses else ""
        
        # Main Query using skuvault_catalog for all classifications and bc_order_line_items for financial data
        query = f"""
        WITH all_classifications AS (
            SELECT DISTINCT sc.classification
            FROM public.skuvault_catalog sc
            {catalog_where_sql}
        ),
        financial_data AS (
            SELECT 
                ol.classification,
                SUM(ol.total_ex_tax) AS total_sales,
                SUM(ol.total_cost) AS total_cost,
                SUM(ol.profit) AS total_profit,
                COUNT(DISTINCT ol.order_id) AS total_orders
            FROM analytics.bc_order_line_items ol
            WHERE {_get_date_range_condition(start_date, end_date, "ol.order_created_date_time")}
            GROUP BY ol.classification
        )
        SELECT 
            ac.classification,
            COALESCE(fd.total_sales, 0) AS total_sales,
            COALESCE(fd.total_cost, 0) AS total_cost,
            COALESCE(fd.total_profit, 0) AS total_profit,
            COALESCE(fd.total_orders, 0) AS total_orders
        FROM all_classifications ac
        LEFT JOIN financial_data fd ON ac.classification = fd.classification
        """
        
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            nulls_order = "NULLS FIRST" if sort_direction.lower() == "asc" else "NULLS LAST"
            if sort_array[0] in ["total_sales", "total_profit", "total_cost", "total_orders"]:
                query += f" ORDER BY {sort_array[0]} {sort_direction} {nulls_order}"
            else:
                query += f" ORDER BY ac.classification {sort_direction} {nulls_order}"

        query += " LIMIT :limit OFFSET :offset"

        # Query Parameters
        params = {
            "limit": limit,
            "offset": offset,
        }
        
        if start_date and end_date:
            # Use the common function to prepare date parameters
            date_params = _prepare_date_range_params(start_date, end_date)
            params.update(date_params)
        
        if search:
            params["search_pattern"] = f'%{search}%'
            
        if classification_array:
            params["classification_list"] = classification_array

        result = conn.execute(text(query), params).fetchall()
        data = []
        for row in result:
            data.append({
                "classification": row[0],
                "total_sales": float(round(row[1] or 0, 2)),
                "total_cost": float(round(row[2] or 0, 2)),
                "total_profit": float(round(row[3] or 0, 2)),
                "total_orders": int(row[4] or 0)
            })
        
        # Count Query - count all classifications from skuvault_catalog
        count_query = f"""
        SELECT COUNT(DISTINCT sc.classification) AS total_count
        FROM public.skuvault_catalog sc
        {catalog_where_sql}
        """
        
        # Execute count query with the same parameters (excluding pagination params)
        count_params = {k: v for k, v in params.items() if k not in ['limit', 'offset']}
        total_count = conn.execute(text(count_query), count_params).scalar()

        # Total Profit Query - only from bc_order_line_items for the date range
        total_profit_query = f"""
        SELECT 
            COALESCE(SUM(ol.profit), 0) as total_profit,
            COALESCE(SUM(ol.total_ex_tax), 0) as total_revenue
        FROM analytics.bc_order_line_items ol
        WHERE {_get_date_range_condition(start_date, end_date, "ol.order_created_date_time")}
        """
        
        # Execute total profit query with only date parameters
        total_profit_params = _prepare_date_range_params(start_date, end_date)
        result = conn.execute(text(total_profit_query), total_profit_params).fetchone()
        total_profit = result[0] if result else 0
        total_revenue = result[1] if result else 0
        
        # Calculate profit and contribution
        classifications = _calculate_profit_and_contribution(data, total_profit, total_revenue)

        # Return paginated data
        return calculatePaginationData(classifications, page, limit, total_count)
    except Exception as e:
        logger.error(f"Error in get_classification_profitability_report: {e}")
        raise e
    finally:
        conn.close()

def get_suppliers_profitability_report(store_id, start_date, end_date, search, page, limit, sort_array, supplier_filter):
    conn = new_pgdb.get_connection(store_id)
    try:
        offset = (page - 1) * limit

        # Build WHERE clause
        where_clauses = []
        where_clauses.append("sc.primary_supplier IS NOT NULL AND sc.primary_supplier != ''")
        
        if search:
            search = search.strip()
            where_clauses.append("sc.primary_supplier ILIKE :search_pattern")
        
        supplier_array = []
        if supplier_filter:
            supplier_array = supplier_filter.split(';')
            where_clauses.append("sc.primary_supplier = ANY(:supplier_list)")

        where_sql = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""
        
        # Main Query using bc_order_line_items directly
        query = f"""
        SELECT 
            sc.primary_supplier AS supplier_name,
            SUM(ol.total_ex_tax) AS total_sales,
            SUM(ol.total_cost) AS total_cost,
            SUM(ol.profit) AS total_profit,
            COUNT(DISTINCT ol.order_id) AS total_orders
        FROM public.skuvault_catalog sc
        LEFT JOIN public.variants v ON v.variants_sku = sc.sku 
        LEFT JOIN analytics.bc_order_line_items ol ON ol.variant_id = v.variants_id
        AND {_get_date_range_condition(start_date, end_date, "ol.order_created_date_time")}
        {where_sql}
        GROUP BY sc.primary_supplier
        """
        
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            nulls_order = "NULLS FIRST" if sort_direction.lower() == "asc" else "NULLS LAST"
            if sort_array[0] in ["total_profit", "total_sales", "total_cost", "supplier_name", "total_orders"]:
                query += f" ORDER BY {sort_array[0]} {sort_direction} {nulls_order}"
            else:
                query += f" ORDER BY sc.primary_supplier {sort_direction} {nulls_order}"

        query += " LIMIT :limit OFFSET :offset"

        # Query Parameters
        params = {
            "limit": limit,
            "offset": offset,
        }
        
        if start_date and end_date:
            # Use the common function to prepare date parameters
            date_params = _prepare_date_range_params(start_date, end_date)
            params.update(date_params)
        
        if search:
            params["search_pattern"] = f'%{search}%'
            
        if supplier_array:
            params["supplier_list"] = supplier_array

        result = conn.execute(text(query), params).fetchall()
        data = []
        for row in result:
            data.append({
                "supplier_name": row[0],
                "total_sales": float(round(row[1] or 0, 2)),
                "total_cost": float(round(row[2] or 0, 2)),
                "total_profit": float(round(row[3] or 0, 2)),
                "total_orders": int(row[4] or 0)
            })
        
        # Count Query
        count_query = f"""
        SELECT COUNT(DISTINCT sc.primary_supplier) AS total_count
        FROM public.skuvault_catalog sc
        LEFT JOIN public.variants v ON v.variants_sku = sc.sku 
        LEFT JOIN analytics.bc_order_line_items ol ON ol.variant_id = v.variants_id
        AND {_get_date_range_condition(start_date, end_date, "ol.order_created_date_time")}
        {where_sql}
        """
        
        # Execute count query with the same parameters (excluding pagination params)
        count_params = {k: v for k, v in params.items() if k not in ['limit', 'offset']}
        total_count = conn.execute(text(count_query), count_params).scalar()

        # Total Profit Query
        total_profit_query = f"""
        SELECT 
            COALESCE(SUM(ol.profit), 0) as total_profit,
            COALESCE(SUM(ol.total_ex_tax), 0) as total_revenue
        FROM public.skuvault_catalog sc
        LEFT JOIN public.variants v ON v.variants_sku = sc.sku 
        LEFT JOIN analytics.bc_order_line_items ol ON ol.variant_id = v.variants_id
        AND {_get_date_range_condition(start_date, end_date, "ol.order_created_date_time")}
        """
        
        # Execute total profit query with only date parameters
        result = conn.execute(text(total_profit_query), params).fetchone()
        total_profit = result[0] if result else 0
        total_revenue = result[1] if result else 0
        
        # Calculate profit and contribution
        suppliers = _calculate_profit_and_contribution(data, total_profit, total_revenue)

        # Return paginated data
        return calculatePaginationData(suppliers, page, limit, total_count)
    except Exception as e:
        logger.error(f"Error in get_suppliers_profitability_report: {e}")
        raise e
    finally:
        conn.close()

def _prepare_date_range_params(start_date, end_date):
    """
    Prepare date range parameters for PostgreSQL queries, handling same-day queries correctly.
    
    Args:
        start_date: The start date string
        end_date: The end date string
        
    Returns:
        dict: Dictionary with start_date and end_date parameters
    """
    params = {"start_date": start_date, "end_date": f"{end_date} 23:59:59"}
    
    # Handle same-day queries by adding time to end_date if they're the same
    if start_date == end_date:
        params["end_date"] = f"{end_date} 23:59:59"
    
    return params

def _get_date_range_condition(start_date, end_date, date_column="order_date"):
    """
    Generate the appropriate date range condition for SQL queries.
    
    Args:
        start_date: The start date string
        end_date: The end date string
        date_column: The column name to filter on (default: order_date)
        
    Returns:
        str: SQL condition for date filtering
    """
    if start_date == end_date:
        return f"{date_column} >= :start_date AND {date_column} <= :end_date"
    else:
        return f"{date_column} BETWEEN :start_date AND :end_date"

def _get_report_type_condition(report_type):
    if report_type == "discount":
        return "o.discount_amount > 0"
    elif report_type == "coupon_discount":
        return "o.coupon_discounts > 0"
    elif report_type == "shipping":
        return "o.base_shipping_cost > 0"
    # elif report_type == "profit":
    #     return "o.profit > 0"
    # elif report_type == "revenue":
    #     return "o.revenue > 0"
    # elif report_type == "cost":
    #     return "o.total_cost > 0"
    else:
        # Default condition - no specific filtering
        return ""


# Common CASE expression
COUPON_CASE_EXPR = (
    "CASE "
    "WHEN o.coupon_code ILIKE '%%FREESHIP%%' THEN 'freeship' "
    "WHEN o.coupon_code ILIKE '%%SF%%' THEN 'sf' "
    "WHEN o.coupon_code ILIKE '%%SLL%%' THEN 'sll' "
    "WHEN o.coupon_code ILIKE '%%5%OFFONEORDER%%' THEN '5offoneorder' "
    "ELSE 'other' "
    "END"
)

def _get_coupon_code_type_condition(coupon_code_type):
    valid_types = {"freeship", "sf", "sll", "5offoneorder", "other"}
    if coupon_code_type in valid_types:
        return f"{COUPON_CASE_EXPR} = '{coupon_code_type}'"
    return ""


def get_orders_profitability_report(store_id, start_date, end_date, search, page, limit, sort_array, customer_group_ids, sales_rep_emails, customer_id, customer_type, supplier_name, classification, purchaser_name, product_id, rep_type, report_type, brand_id, coupon_code, coupon_code_type, profit_percentage, is_profitable):
    response = {"status": 400}
    conn = new_pgdb.get_connection(store_id)
    
    try:
        offset = (page - 1) * limit
        params = {"limit": limit, "offset": offset}
        where_clauses = []
        item_where_clauses = []
        
        if report_type:
            report_type_condition = _get_report_type_condition(report_type)
            where_clauses.append(report_type_condition)

        if coupon_code_type:
            coupon_code_type_condition = _get_coupon_code_type_condition(coupon_code_type)
            where_clauses.append(coupon_code_type_condition)

        # Add date range filter if provided
        if start_date and end_date:
            # Use the common function to prepare date parameters
            date_params = _prepare_date_range_params(start_date, end_date)
            params.update(date_params)
            # Use the common function to get the date condition
            where_clauses.append(_get_date_range_condition(start_date, end_date, "o.order_date"))

        # Add search filter if provided
        if search:
            search = search.strip().replace(" ", "")
            where_clauses.append(f"""(
                c.first_name ILIKE :search_pattern OR
                c.last_name ILIKE :search_pattern OR
                REPLACE(CONCAT(c.first_name, ' ', c.last_name), ' ', '') ILIKE :search_pattern OR
                c.email ILIKE :search_pattern OR
                o.order_id::text ILIKE :search_pattern
            )""")
            params["search_pattern"] = f"%{search}%"

        # Add customer group filter if provided
        if customer_group_ids:
            group_id_array = list(map(int, customer_group_ids.split(","))) if isinstance(customer_group_ids, str) else []
            where_clauses.append("c.customer_group_id = ANY(:customer_group_ids)")
            params["customer_group_ids"] = group_id_array

        # Add sales rep filter if provided
        if sales_rep_emails:
            emails = sales_rep_emails.split(",") if isinstance(sales_rep_emails, str) else []
            where_clauses.append("scr.rep_email = ANY(:sales_rep_emails)")
            params["sales_rep_emails"] = emails
        
        # Add customer ID filter if provided
        if customer_id:
            where_clauses.append("o.customer_id = :customer_id")
            params["customer_id"] = int(customer_id)
        
        # Add customer type filter if provided
        if customer_type:
            where_clauses.append("scr.type = :customer_type")
            params["customer_type"] = str(customer_type)
        
        # Add supplier filter if provided
        if supplier_name:
            item_where_clauses.append("ol.primary_supplier = :supplier_name")
            params["supplier_name"] = str(supplier_name)

        # Add classification filter if provided
        if classification:
            item_where_clauses.append("ol.classification = :classification")
            params["classification"] = str(classification)
        
        # Add purchaser filter if provided
        if purchaser_name:
            item_where_clauses.append("ol.purchaser_name = :purchaser_name")
            params["purchaser_name"] = str(purchaser_name)
        
        # Add product filter if provided
        if product_id:
            item_where_clauses.append("ol.product_id = :product_id")
            params["product_id"] = int(product_id)
        
        # Add rep type filter if provided
        if rep_type:
            where_clauses.append("scr.rep_type = :rep_type")
            # where_clauses.append("scr.rep_type != ''")  
            params["rep_type"] = str(rep_type)
        
        if brand_id:
            item_where_clauses.append("ol.brand_id = :brand_id")
            params["brand_id"] = int(brand_id)
        
        if coupon_code:
            where_clauses.append("o.coupon_code = :coupon_code")
            params["coupon_code"] = str(coupon_code)

        if profit_percentage is not None and profit_percentage != '':
            if profit_percentage == '0':
                where_clauses.append("o.profit_percentage = 0")
            elif profit_percentage == '20_plus':
                where_clauses.append("o.profit_percentage > 20")
            elif profit_percentage == '-20_plus':
                where_clauses.append("o.profit_percentage < -20")
            else:
                lower, upper = map(int, profit_percentage.split('_to_'))
                if lower >= 0:
                    where_clauses.append("o.profit_percentage > :lower AND o.profit_percentage <= :upper")
                else:
                    where_clauses.append("o.profit_percentage >= :lower AND o.profit_percentage < :upper")
                params["lower"] = lower
                params["upper"] = upper
            
        if is_profitable == 'true':
            where_clauses.append("o.profit_percentage >= 0")
        elif is_profitable == 'false':
            where_clauses.append("o.profit_percentage < 0")

        # Build the WHERE clauses
        where_sql = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""
        item_where_sql = f"WHERE {' AND '.join(item_where_clauses)}" if item_where_clauses else ""

        # Main Query - Use CTE for better performance and readability
        query = f"""
        WITH filtered_orders AS (
            SELECT DISTINCT o.order_id
            FROM analytics.bc_orders o
            LEFT JOIN customers c ON o.customer_id = c.customer_id
            LEFT JOIN {new_pgdb.DBTables.salesforce_customer_rep} scr ON scr.customer_id = c.customer_id
            {where_sql}
        ),
        filtered_items AS (
            SELECT DISTINCT ol.order_id
            FROM analytics.bc_order_line_items ol
            {item_where_sql}
        ),
        final_filtered_orders AS (
            SELECT fo.order_id
            FROM filtered_orders fo
            {f"JOIN filtered_items fi ON fo.order_id = fi.order_id" if item_where_clauses else ""}
        )
        SELECT DISTINCT
            o.order_date,
            o.order_id,
            CONCAT(c.first_name, ' ', c.last_name) AS name,
            c.email,
            c.customer_group_name,
            scr.rep_name AS sales_rep_name,
            o.subtotal_ex_tax AS subtotal,
            o.coupon_discounts AS coupon_discounts,
            o.total_tax AS tax,
            o.shipping_inc_tax AS shipping,
            o.total_inc_tax AS total,
            o.total_items AS total_sell,
            o.total_revenue AS total_revenue,
            o.total_cost AS total_cost,
            o.total_profit AS total_profit,
            c.customer_group_id,
            c.customer_id,
            o.profit_percentage
        FROM analytics.bc_orders o
        JOIN final_filtered_orders ffo ON o.order_id = ffo.order_id
        LEFT JOIN customers c ON o.customer_id = c.customer_id
        LEFT JOIN {new_pgdb.DBTables.salesforce_customer_rep} scr ON scr.customer_id = c.customer_id
        """

        # Add sorting if provided
        if sort_array and sort_array[0]:
            col, direction = sort_array
            sort_col = col if col in [
                "order_date", "order_id", "name", "email", "customer_group_name", "sales_rep_name", "subtotal",
                "coupon_discounts", "tax", "shipping", "total", "total_cost", "total_sell", 'total_revenue', 'total_profit', 'profit_percentage'
            ] else "order_date"
            sort_direction = "ASC" if direction == "1" else "DESC"
            nulls_order = "NULLS FIRST" if sort_direction == "ASC" else "NULLS LAST"
            query += f" ORDER BY {sort_col} {sort_direction} {nulls_order}"

        # Add pagination
        query += " LIMIT :limit OFFSET :offset"

        # Execute the query
        result = conn.execute(text(query), params).fetchall()

        # Process the results
        order_data = []
        for row in result:
            order_data.append({
                "order_date": convert_to_timestamp(row[0]),
                "order_id": row[1],
                "name": row[2],
                "email": row[3],
                "customer_group_name": row[4],
                "sales_rep_name": row[5],
                "subtotal": float(row[6] or 0),
                "coupon_discounts": float(row[7] or 0),
                "tax": float(row[8] or 0),
                "shipping": float(row[9] or 0),
                "total": float(row[10] or 0),
                "total_sell": int(row[11] or 0),
                "total_cost": float(row[13] or 0),
                "total_revenue": float(row[12] or 0),
                "total_profit": float(row[14] or 0),
                "customer_id": row[16],
                "profit_percentage": float(row[17] or 0)
            })

        # Count Query - Use the same CTEs for consistency
        count_query = f"""
        WITH filtered_orders AS (
            SELECT DISTINCT o.order_id
            FROM analytics.bc_orders o
            LEFT JOIN customers c ON o.customer_id = c.customer_id
            LEFT JOIN {new_pgdb.DBTables.salesforce_customer_rep} scr ON scr.customer_id = c.customer_id
            {where_sql}
        ),
        filtered_items AS (
            SELECT DISTINCT ol.order_id
            FROM analytics.bc_order_line_items ol
            {item_where_sql}
        ),
        final_filtered_orders AS (
            SELECT fo.order_id
            FROM filtered_orders fo
            {f"JOIN filtered_items fi ON fo.order_id = fi.order_id" if item_where_clauses else ""}
        )
        SELECT COUNT(DISTINCT fo.order_id) FROM final_filtered_orders fo
        """
        
        # Execute count query with the same parameters (excluding pagination params)
        count_params = {k: v for k, v in params.items() if k not in ['limit', 'offset']}
        total_count = conn.execute(text(count_query), count_params).scalar()

        # Total Profit Query - Only apply date range filter
        total_profit_query = f"""
        SELECT COALESCE(SUM(o.total_profit), 0) as profit 
        FROM analytics.bc_orders o
        """
        
        # Add date range filter to total profit query
        if start_date and end_date:
            total_profit_query += f" WHERE {_get_date_range_condition(start_date, end_date, 'o.order_date')}"
        
        # Execute total profit query with only date range parameters
        total_profit_params = _prepare_date_range_params(start_date, end_date)
        total_profit = conn.execute(text(total_profit_query), total_profit_params).scalar()
        
        # Calculate profit and contribution
        order_data = _calculate_profit_and_contribution(order_data, total_profit, calculate_profit_percentage=False)
        
        # Return paginated data
        response["data"] = calculatePaginationData(order_data, page, limit, total_count)
        response["status"] = 200

    except Exception as e:
        response.update({"status": 422, "message": str(e)})
        raise
    finally:
        conn.close()
    
    return response


def customer_profitability_csv(store, query_params):
    response = {
        "status": 400,
    }
    if query_params:
        task_id = task.send_task(task.CUSTOMER_PROFITABILITY_CSV_MAIL_TASK, args=(store['id'], query_params))
        if task_id:
            response['status'] = 200
            response['message'] = f"We are generating the data you requested for download as a CSV file. This process may take a few minutes. Once complete, the CSV will be sent to your email: {query_params['username']}."
        else:
            response['status'] = 400
            response['message'] = 'Something went wrong to generate the CSV file.'
    else:
        response['status'] = 400
        response['message'] = 'query params are required'

    return response

def product_wise_profitability_csv(store, query_params):
    response = {
        "status": 400,
    }
    if query_params:
        task_id = task.send_task(task.PRODUCT_WISE_PROFITABILITY_CSV_MAIL_TASK, args=(store['id'], query_params))
        if task_id:
            response['status'] = 200
            response['message'] = f"We are generating the data you requested for download as a CSV file. This process may take a few minutes. Once complete, the CSV will be sent to your email: {query_params['username']}."
        else:
            response['status'] = 400
            response['message'] = 'Something went wrong to generate the CSV file.'
    else:
        response['status'] = 400
        response['message'] = 'query params are required'

    return response

def suppliers_profitability_csv(store, query_params):
    response = {
        "status": 400,
    }
    if query_params:
        task_id = task.send_task(task.SUPPLIERS_PROFITABILITY_CSV_MAIL_TASK, args=(store['id'], query_params))
        if task_id:
            response['status'] = 200
            response['message'] = f"We are generating the data you requested for download as a CSV file. This process may take a few minutes. Once complete, the CSV will be sent to your email: {query_params['username']}."
        else:
            response['status'] = 400
            response['message'] = 'Something went wrong to generate the CSV file.'
    else:
        response['status'] = 400
        response['message'] = 'query params are required'

    return response

def classification_profitability_csv(store, query_params):
    response = {
        "status": 400,
    }
    if query_params:
        task_id = task.send_task(task.CLASSIFICATION_PROFITABILITY_CSV_MAIL_TASK, args=(store['id'], query_params))
        if task_id:
            response['status'] = 200
            response['message'] = f"We are generating the data you requested for download as a CSV file. This process may take a few minutes. Once complete, the CSV will be sent to your email: {query_params['username']}."
        else:
            response['status'] = 400
            response['message'] = 'Something went wrong to generate the CSV file.'
    else:
        response['status'] = 400
        response['message'] = 'query params are required'

    return response

def orders_profitability_csv(store, query_params):
    response = {
        "status": 400,
    }
    if query_params:
        task_id = task.send_task(task.ORDERS_PROFITABILITY_CSV_MAIL_TASK, args=(store['id'], query_params))
        if task_id:
            response['status'] = 200
            response['message'] = f"We are generating the data you requested for download as a CSV file. This process may take a few minutes. Once complete, the CSV will be sent to your email: {query_params['username']}."
        else:
            response['status'] = 400
            response['message'] = 'Something went wrong to generate the CSV file.'
    else:
        response['status'] = 400
        response['message'] = 'query params are required'

    return response

def brands_profitability_csv(store, query_params):
    response = {
        "status": 400,
    }
    if query_params:
        task_id = task.send_task(task.BRANDS_PROFITABILITY_CSV_MAIL_TASK, args=(store['id'], query_params))
        if task_id:
            response['status'] = 200
            response['message'] = f"We are generating the data you requested for download as a CSV file. This process may take a few minutes. Once complete, the CSV will be sent to your email: {query_params['username']}."
        else:
            response['status'] = 400
            response['message'] = 'Something went wrong to generate the CSV file.'
    else:
        response['status'] = 400
        response['message'] = 'query params are required'

    return response

def get_brands_profitability_report(store, start_date, end_date, page, limit, search, sort_array, brand_ids, purchaser_emails):
    response = {
        "status": 400,
        "message": "Something went wrong"
    }
    conn = new_pgdb.get_connection(store['id'])
    try:
        offset = (page - 1) * limit
        params = {"limit": limit, "offset": offset}
        brand_where_clauses = []
        purchaser_where_clauses = []
        
        # Add date range filter if provided
        if start_date and end_date:
            # Use the common function to prepare date parameters
            date_params = _prepare_date_range_params(start_date, end_date)
            params.update(date_params)

        # Add brand filter if provided - applied in first CTE
        if brand_ids:
            brand_id_array = list(map(int, brand_ids.split(","))) if isinstance(brand_ids, str) else []
            brand_where_clauses.append("b.brand_id = ANY(:brand_ids)")
            params["brand_ids"] = brand_id_array

        # Add purchaser filter if provided - applied in purchaser_info CTE
        if purchaser_emails:
            emails = purchaser_emails.split(",") if isinstance(purchaser_emails, str) else []
            purchaser_where_clauses.append("string_to_array(pi.purchaser_emails, ', ') && ARRAY[:purchaser_emails]")
            params["purchaser_emails"] = emails

        # Build the WHERE clauses
        brand_where_sql = f"WHERE {' AND '.join(brand_where_clauses)}" if brand_where_clauses else ""
        purchaser_where_sql = f"WHERE {' AND '.join(purchaser_where_clauses)}" if purchaser_where_clauses else ""

        # Main Query - Using correct tables with aggregated purchaser information
        query = f"""
        WITH brand_metrics AS (
            SELECT
                b.brand_id,
                b.brand_name,
                SUM(oli.total_ex_tax) AS total_revenue,
                SUM(oli.total_cost) AS total_cost,
                SUM(oli.profit) AS total_profit,
                COUNT(DISTINCT oli.order_id) AS total_orders
            FROM analytics.bc_brands b
            LEFT JOIN analytics.bc_order_line_items oli
                ON oli.brand_id = b.brand_id
                AND {_get_date_range_condition(start_date, end_date, 'oli.order_created_date_time')}
            {brand_where_sql}
            GROUP BY b.brand_id, b.brand_name
        ),
        purchaser_info AS (
            SELECT
                bpm.brand_id,
                STRING_AGG(DISTINCT bpm.purchaser_name, ', ') AS purchaser_names,
                STRING_AGG(DISTINCT bpm.purchaser_email, ', ') AS purchaser_emails
            FROM public.brand_purchaser_mapping bpm
            GROUP BY bpm.brand_id
        )
        SELECT
            bm.brand_id,
            bm.brand_name,
            COALESCE(pi.purchaser_names, '-') AS purchaser_name,
            COALESCE(pi.purchaser_emails, '-') AS purchaser_email,
            COALESCE(bm.total_revenue, 0) AS total_revenue,
            COALESCE(bm.total_cost, 0) AS total_cost,
            COALESCE(bm.total_profit, 0) AS total_profit,
            COALESCE(bm.total_orders, 0) AS total_orders
        FROM brand_metrics bm
        LEFT JOIN purchaser_info pi
            ON bm.brand_id = pi.brand_id
        {purchaser_where_sql}
        """

        # Count Query - Fixed to properly handle multiple WHERE clauses
        count_query = f"""
        SELECT COUNT(DISTINCT b.brand_id)
        FROM analytics.bc_brands b
        LEFT JOIN public.brand_purchaser_mapping bpm
            ON b.brand_id = bpm.brand_id
        """

        if search:
            search = search.strip()
            if 'WHERE' in purchaser_where_sql:
                query += f" AND (bm.brand_name ILIKE :search_pattern OR pi.purchaser_names ILIKE :search_pattern)"
                count_query += f" AND (b.brand_name ILIKE :search_pattern OR bpm.purchaser_name ILIKE :search_pattern)"
                params["search_pattern"] = f'%{search}%'
            else:
                query += f" WHERE (bm.brand_name ILIKE :search_pattern OR pi.purchaser_names ILIKE :search_pattern)"
                count_query += f" WHERE (b.brand_name ILIKE :search_pattern OR bpm.purchaser_name ILIKE :search_pattern)"
                params["search_pattern"] = f'%{search}%'

        # Add sorting if provided
        if sort_array and sort_array[0]:
            col, direction = sort_array
            sort_col = col if col in [
                "brand_id", "brand_name", "purchaser_name", "total_revenue", 
                "total_cost", "total_profit", "total_orders"
            ] else "brand_name"
            sort_direction = "ASC" if direction == "1" else "DESC"
            nulls_order = "NULLS FIRST" if sort_direction == "ASC" else "NULLS LAST"
            query += f" ORDER BY {sort_col} {sort_direction} {nulls_order}"

        # Add pagination
        query += " LIMIT :limit OFFSET :offset"

        where_clauses = []
        if brand_where_clauses:
            where_clauses.extend(brand_where_clauses)
        if purchaser_where_clauses:
            where_clauses.append("bpm.purchaser_email = ANY(:purchaser_emails)")
        
        if where_clauses:
            count_query += f" WHERE {' AND '.join(where_clauses)}"
        
        # Execute count query with the same parameters (excluding pagination params)
        count_params = {k: v for k, v in params.items() if k not in ['limit', 'offset']}
        total_count = conn.execute(text(count_query), count_params).scalar()

        # Execute the query
        result = conn.execute(text(query), params).fetchall()

        # Process the results
        brand_data = []
        for row in result:
            brand_data.append({
                "brand_id": row[0],
                "brand_name": row[1],
                "purchaser_name": row[2],
                "purchaser_email": row[3],
                "total_revenue": float(round(row[4] or 0, 2)),
                "total_cost": float(round(row[5] or 0, 2)),
                "total_profit": float(round(row[6] or 0, 2)),
                "total_orders": int(row[7] or 0)
            })

       
        # Total Profit Query - Only apply date range filter and consider only products with brand IDs
        total_profit_query = f"""
        SELECT 
            COALESCE(SUM(oli.profit), 0) as total_profit,
            COALESCE(SUM(oli.total_ex_tax), 0) as total_revenue
        FROM analytics.bc_order_line_items oli
        JOIN analytics.bc_brands b ON oli.brand_id = b.brand_id
        WHERE {_get_date_range_condition(start_date, end_date, 'oli.order_created_date_time')}
        """
        
        # Execute total profit query with only date range parameters
        total_profit_params = _prepare_date_range_params(start_date, end_date)
        result = conn.execute(text(total_profit_query), total_profit_params).fetchone()
        total_profit = result[0] if result else 0
        total_revenue = result[1] if result else 0

        # Calculate profit and contribution
        brand_data = _calculate_profit_and_contribution(brand_data, total_profit, total_revenue)
        
        # Return paginated data
        response["data"] = calculatePaginationData(brand_data, page, limit, total_count)
        response["status"] = 200

    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        conn.close()
    
    return response

def get_mapped_purchasers(store_id):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        query = f"""SELECT DISTINCT purchaser_name, purchaser_email FROM {new_pgdb.DBTables.brand_purchaser_mapping}"""
        result = conn.execute(text(query)).fetchall()
        purchasers = []
        for row in result:
            purchasers.append({
                "purchaser_name": row[0],
                "purchaser_email": row[1]
            })
        response["data"] = purchasers
        response["status"] = 200
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        conn.close()
    return response

def get_purchaser_by_brand_profitability_report(store_id, start_date, end_date, sort_array):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        date_params = _prepare_date_range_params(start_date, end_date)
        query = f"""WITH unique_purchasers AS (
                        SELECT DISTINCT purchaser_name, brand_id, purchaser_email
                        FROM brand_purchaser_mapping
                    ),
                    revenue_by_purchaser AS (
                        SELECT 
                            up.purchaser_name,
                            up.purchaser_email,
                            SUM(oli.total_ex_tax) AS total_sales,
                            SUM(oli.total_cost) AS total_cost,
                            SUM(oli.profit) AS total_profit,
                            COUNT(DISTINCT oli.order_id) AS total_orders
                        FROM unique_purchasers up
                        LEFT JOIN analytics.bc_order_line_items oli
                            ON oli.brand_id = up.brand_id
                        AND {_get_date_range_condition(start_date, end_date, 'oli.order_created_date_time')}
                        GROUP BY up.purchaser_name, up.purchaser_email
                    )
                    SELECT *
                    FROM revenue_by_purchaser"""
        
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            nulls_order = "NULLS FIRST" if sort_direction.lower() == "asc" else "NULLS LAST" 
            if sort_array[0] in ["purchaser_name", "purchaser_email", "total_sales", "total_cost", "total_profit", "total_orders"]:
                query += f" ORDER BY {sort_array[0]} {sort_direction} {nulls_order}"   

        result = conn.execute(text(query), date_params).fetchall()

        purchasers = []
        for row in result:
            purchasers.append({
                "purchaser_name": row[0],
                "purchaser_email": row[1],
                "total_sales": float(round(row[2] or 0, 2)),
                "total_cost": float(round(row[3] or 0, 2)),
                "total_profit": float(round(row[4] or 0, 2)),
                "total_orders": int(row[5] or 0)
            })

        data = _calculate_profit_and_contribution(purchasers)

        if len(sort_array) and sort_array[0] in ["profit_percentage", "contributed_profit_percentage", "contributed_revenue_percentage"]:
            sort_direction = False if sort_array[1] == '1' else True
            sort_column = sort_array[0]
            data = sorted(data, key=lambda x: x.get(sort_column, 0), reverse=sort_direction)

        response["data"] = data
        response["status"] = 200
    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        conn.close()
    return response
    
def get_orders_wise_profitability(store_id, start_date, end_date):
    response = {
        "status": 400
    }
    conn = new_pgdb.get_connection(store_id)
    try:
        date_params = _prepare_date_range_params(start_date, end_date)

        query = f"""
        SELECT
            -- Profit ranges
            COUNT(*) FILTER (WHERE profit_percentage > 0 AND profit_percentage <= 5) AS "0_to_5_profit",
            COUNT(*) FILTER (WHERE profit_percentage > 5 AND profit_percentage <= 10) AS "5_to_10_profit",
            COUNT(*) FILTER (WHERE profit_percentage > 10 AND profit_percentage <= 15) AS "10_to_15_profit",
            COUNT(*) FILTER (WHERE profit_percentage > 15 AND profit_percentage <= 20) AS "15_to_20_profit",
            COUNT(*) FILTER (WHERE profit_percentage > 20) AS "20_plus_profit",
            -- Loss ranges
            COUNT(*) FILTER (WHERE profit_percentage < 0 AND profit_percentage >= -5) AS "-5_to_0_loss",
            COUNT(*) FILTER (WHERE profit_percentage < -5 AND profit_percentage >= -10) AS "-10_to_-5_loss",
            COUNT(*) FILTER (WHERE profit_percentage < -10 AND profit_percentage >= -15) AS "-15_to_-10_loss",
            COUNT(*) FILTER (WHERE profit_percentage < -15 AND profit_percentage >= -20) AS "-20_to_-15_loss",
            COUNT(*) FILTER (WHERE profit_percentage < -20) AS "more_than_-20_loss",
            -- Zero profit
            COUNT(*) FILTER (WHERE profit_percentage = 0) AS "0_profit"
        FROM {AnalyticsDB.get_order_analytics_table()}
        WHERE {_get_date_range_condition(start_date, end_date, 'order_date')}
        """

        row = conn.execute(text(query), date_params).fetchone()

        profit_analysis = {
            "0_to_5": row[0],
            "5_to_10": row[1],
            "10_to_15": row[2],
            "15_to_20": row[3],
            "20_plus": row[4],
            "-5_to_0": row[5],
            "-10_to_-5": row[6],
            "-15_to_-10": row[7],
            "-20_to_-15": row[8],
            "-20_plus": row[9],
            "0": row[10]
        }

        response["data"] = profit_analysis
        response["status"] = 200

    except Exception as e:
        logger.error(traceback.format_exc())
        raise e
    finally:
        conn.close()
    return response

def _get_chart_report_type_condition(report_type, date_condition, customer_group_id, customer_type, sales_rep_email, supplier_id, classification, purchaser_name, brand_id):
    base_condition = f"AND {date_condition}"
    
    if report_type == "customer_group":
        tables = f""" FROM {new_pgdb.DBTables.customers_table} c
                  LEFT JOIN {AnalyticsDB.get_customers_trend_table()} ct ON c.customer_id = ct.customer_id
                  LEFT JOIN {AnalyticsDB.get_customers_trend_shipping_cost_table()} sc 
                    ON ct.customer_id = sc.customer_id and ct.order_date = sc.order_date
                  WHERE c.customer_group_id = :customer_group_id {base_condition} """
        params = {"customer_group_id": customer_group_id}
        
    elif report_type == "customer_type":
        tables = f""" FROM {new_pgdb.DBTables.salesforce_customer_rep} scr
                    JOIN {AnalyticsDB.get_customers_trend_table()} ct ON scr.customer_id = ct.customer_id
                    LEFT JOIN {AnalyticsDB.get_customers_trend_shipping_cost_table()} sc 
                        ON ct.customer_id = sc.customer_id and ct.order_date = sc.order_date
                    WHERE scr.type = :customer_type  {base_condition} """
        params = {"customer_type": customer_type}
        
    elif report_type == "sales_rep":
        tables = f""" FROM {new_pgdb.DBTables.salesforce_customer_rep} scr
                    LEFT JOIN {AnalyticsDB.get_customers_trend_table()} ct ON scr.customer_id = ct.customer_id
                    LEFT JOIN {AnalyticsDB.get_customers_trend_shipping_cost_table()} sc 
                        ON ct.customer_id = sc.customer_id and ct.order_date = sc.order_date
                    WHERE scr.rep_email = :sales_rep_email {base_condition} """
        params = {"sales_rep_email": sales_rep_email}
        
    elif report_type == "supplier":
        tables = f""" FROM public.skuvault_catalog sc
                    LEFT JOIN public.variants v ON v.variants_sku = sc.sku 
                    LEFT JOIN analytics.bc_order_line_items ol 
                        ON ol.variant_id = v.variants_id
                    WHERE sc.primary_supplier = :supplier_name {base_condition} """
        params = {"supplier_name": supplier_id}
        
    elif report_type == "classification":
        tables = f""" FROM analytics.bc_order_line_items ol
                    WHERE {date_condition}
                    AND ol.classification = :classification """
        params = {"classification": classification}
        
    elif report_type == "purchaser_by_supplier":
        tables = f""" FROM analytics.bc_order_line_items ol
                    WHERE {date_condition}
                    AND ol.purchaser_name = :purchaser_name """
        params = {"purchaser_name": purchaser_name}
        
    elif report_type == "brand":
        tables = f""" FROM analytics.bc_brands b
                    LEFT JOIN analytics.bc_order_line_items ol
                        ON ol.brand_id = b.brand_id
                    WHERE b.brand_id = :brand_id {base_condition} """
        params = {"brand_id": brand_id}
        
    elif report_type == "purchaser_by_brand":
        tables = f""" FROM brand_purchaser_mapping bpm
                    LEFT JOIN analytics.bc_order_line_items ol
                        ON ol.brand_id = bpm.brand_id
                    WHERE bpm.purchaser_name = :purchaser_name {base_condition} """
        params = {
            "purchaser_name": purchaser_name
        }
        
    elif report_type == "all":
        tables = f"""  FROM {AnalyticsDB.get_customers_trend_table()} ct
                    LEFT JOIN {AnalyticsDB.get_customers_trend_shipping_cost_table()} sc 
                    ON ct.customer_id = sc.customer_id and ct.order_date = sc.order_date
                    WHERE {date_condition} """
        params = {}
    else:
        raise ValueError(f"Invalid report type: {report_type}")
        
    return tables, params

def _get_period_columns_and_grouping(period, report_type):
    # Define the base table prefix based on report type
    table_prefix = 'ct' if report_type in ["customer_group", "customer_type", "sales_rep", "all"] else 'ol'
    
    date_col = 'order_date' if table_prefix == 'ct' and period == 'day' else 'order_created_date_time'
    week_col = 'order_date' if table_prefix == 'ct' and period == 'week' else 'order_created_date_time'

    # Define period configurations
    period_configs = {
        'day': {
            'date_col': f'date({table_prefix}.{date_col})' if table_prefix == 'ct' else f'date({table_prefix}.{date_col})',
            'group_cols': ['order_year', date_col],
            'order_cols': ['order_year', date_col]
        },
        'week': {
            'date_col': f'{table_prefix}.{week_col}',
            'group_cols': ['week_number', 'iso_year'],
            'order_cols': ['iso_year', 'week_number']
        },
        'month': {
            'date_col': f'{table_prefix}.order_month',
            'group_cols': ['order_year', 'order_month'],
            'order_cols': ['order_year', 'order_month']
        },
        'year': {
            'date_col': f'{table_prefix}.order_year',
            'group_cols': ['order_year'],
            'order_cols': ['order_year']
        }
    }
    
    if period not in period_configs:
        raise ValueError(f"Invalid period: {period}. Must be one of {list(period_configs.keys())}")
    
    config = period_configs[period]
    
    # Build column string
    if period == 'day':
        column_str = f"{table_prefix}.order_year, {config['date_col']} as order_date"
    elif period == 'week':
        column_str = f"EXTRACT(ISOYEAR FROM {config['date_col']}) AS iso_year, EXTRACT(WEEK FROM {config['date_col']}) AS week_number"   
    else:
        column_str = ', '.join(f"{table_prefix}.{col}" for col in config['group_cols'])
    
    # Build group by condition
    if period == 'day':
        group_by_cols = [f"date({table_prefix}.{col})" if col == date_col else f"{table_prefix}.{col}" for col in config['group_cols']]
        order_by_cols = [f"date({table_prefix}.{col})" if col == date_col else f"{table_prefix}.{col}" for col in config['order_cols']]
    elif period == 'week':
        group_by_cols = [f"{col}" for col in config['group_cols']]
        order_by_cols = [f"{col}" for col in config['order_cols']]
    else:
        group_by_cols = [f"{table_prefix}.{col}" for col in config['group_cols']]
        order_by_cols = [f"{table_prefix}.{col}" for col in config['order_cols']]
    
    group_by_condition = (
        f"GROUP BY {', '.join(group_by_cols)} "
        f"ORDER BY {', '.join(order_by_cols)}"
    )
    
    return column_str, group_by_condition

def get_profitability_report_charts(store_id, start_date, end_date, period, report_type, customer_group_id=None, customer_type=None, sales_rep_email=None, supplier_id=None, classification=None, purchaser_name=None, brand_id=None):
    response = {
        "message": "",
        "status": 400,
        "data": None
    }

    conn = new_pgdb.get_connection(store_id)
    try:        
        # Prepare date parameters
        params = _prepare_date_range_params(start_date, end_date)
        date_condition = _get_date_range_condition(
            start_date, 
            end_date, 
            "ct.order_date" if report_type in ["customer_group", "customer_type", "sales_rep", "all"] else "ol.order_created_date_time"
        )

        # Get tables and conditions based on report type
        tables, conditional_param = _get_chart_report_type_condition(report_type, date_condition, customer_group_id, customer_type, sales_rep_email, supplier_id, classification, purchaser_name, brand_id)
        params.update(conditional_param)
        
        # Get period-based column selection and grouping
        column_str, group_by_condition = _get_period_columns_and_grouping(period, report_type)
        
        # Build the query with appropriate columns based on report type
        is_customer_report = report_type in ["customer_group", "customer_type", "sales_rep", "all"]
        revenue_expr = (
            "SUM(ct.revenue) + COALESCE(SUM(sc.shipping_cost), 0)" if is_customer_report 
            else "SUM(ol.total_ex_tax)"
        )
        cost_expr = "SUM(ct.total_cost)" if is_customer_report else "SUM(ol.total_cost)"
        profit_expr = (
            "SUM(ct.profit) + COALESCE(SUM(sc.shipping_cost), 0)" if is_customer_report 
            else "SUM(ol.profit)"
        )
        orders_expr = "SUM(ct.orders)" if is_customer_report else "COUNT(DISTINCT ol.order_id)"
        
        query = f"""
            SELECT
                ({revenue_expr}) as revenue,
                ({cost_expr}) as cost,
                ({profit_expr}) as profit,
                {orders_expr} as total_orders,
                {column_str}
            {tables}
            {group_by_condition}
        """
        
        # Execute query and process results
        result = conn.execute(text(query), params).fetchall()

        # Generate complete sequence of periods
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')

        # Create a dictionary to store all periods with zero values
        all_periods = {}

        if period == 'day':
            current = start
            while current <= end:
                # Keep original format for key but use datetime for sorting
                key = f"{current.strftime('%Y-%m-%d')}_{current.year}"
                sort_key = current.strftime('%Y-%m-%d')  # Used only for sorting
                all_periods[key] = {
                    'revenue': 0,
                    'cost': 0,
                    'profit': 0,
                    'orders': 0,
                    'sort_key': sort_key
                }
                current += timedelta(days=1)
                
        elif period == 'week':
            # Get ISO week numbers
            current = start
            while current <= end:
                year, week, _ = current.isocalendar()
                # Keep original format for key but use datetime for sorting
                key = f"{week}_{year}"
                sort_key = f"{year:04d}-{week:02d}"  # Used only for sorting
                all_periods[key] = {
                    'revenue': 0,
                    'cost': 0,
                    'profit': 0,
                    'orders': 0,
                    'sort_key': sort_key
                }
                current += timedelta(days=7)
                
        elif period == 'month':
            current = start.replace(day=1)
            while current <= end:
                # Keep original format for key but use datetime for sorting
                key = f"{str(int(current.month))}_{current.year}"
                sort_key = f"{current.year:04d}-{current.month:02d}"  # Used only for sorting
                all_periods[key] = {
                    'revenue': 0,
                    'cost': 0,
                    'profit': 0,
                    'orders': 0,
                    'sort_key': sort_key
                }
                # Move to first day of next month
                if current.month == 12:
                    current = current.replace(year=current.year + 1, month=1)
                else:
                    current = current.replace(month=current.month + 1)
                    
        elif period == 'year':
            current = start.replace(month=1, day=1)
            while current <= end:
                # Keep original format for key but use datetime for sorting
                key = str(current.year)
                sort_key = f"{current.year:04d}"  # Used only for sorting
                all_periods[key] = {
                    'revenue': 0,
                    'cost': 0,
                    'profit': 0,
                    'orders': 0,
                    'sort_key': sort_key
                }
                current = current.replace(year=current.year + 1)

        # Update the dictionary with actual data from query results
        for row in result:
            if period == 'day':
                key = f"{row[5].strftime('%Y-%m-%d')}_{row[4]}"
                sort_key = row[5].strftime('%Y-%m-%d')
            elif period == 'week':
                week_num = int(row[5]) if row[5] is not None else 0
                year_num = int(row[4]) if row[4] is not None else 0
                key = f"{week_num}_{year_num}"
                sort_key = f"{year_num:04d}-{week_num:02d}"
            elif period == 'month':
                month_num = int(row[5]) if row[5] is not None else 0
                year_num = int(row[4]) if row[4] is not None else 0
                key = f"{month_num}_{year_num}"
                sort_key = f"{year_num:04d}-{month_num:02d}"
            else:  # year
                year_num = int(row[4]) if row[4] is not None else 0
                key = str(year_num)
                sort_key = f"{year_num:04d}"
                
            all_periods[key] = {
                'revenue': float(round(row[0] or 0, 2)),
                'cost': float(round(row[1] or 0, 2)),
                'profit': float(round(row[2] or 0, 2)),
                'orders': int(row[3] or 0),
                'sort_key': sort_key
            }

        # Convert to sorted lists for chart data
        labels = []
        revenue_data = []
        cost_data = []
        profit_data = []
        orders_data = []
        
        # Sort periods chronologically using sort_key
        sorted_periods = sorted(all_periods.items(), key=lambda x: x[1]['sort_key'])
        
        for period_key, values in sorted_periods:
            labels.append(period_key)  # Keep original label format
            revenue_data.append(values['revenue'])
            cost_data.append(values['cost'])
            profit_data.append(values['profit'])
            orders_data.append(values['orders'])

        chart_data = {
            'labels': labels,
            'datasets': [
                {'label': 'Total Sales', 'data': revenue_data},
                {'label': 'Total Cost', 'data': cost_data},
                {'label': 'Total Profit', 'data': profit_data},
                {'label': 'Orders', 'data': orders_data}
            ]
        }
        
        response.update({
            "data": chart_data,
            "message": "Profitability report charts fetched successfully",
            "status": 200
        })
        
    except Exception as e:
        logger.error(f"Error in get_profitability_report_charts: {e}")
        response.update({
            "message": f"Error in get_profitability_report_charts: {str(e)}",
            "status": 500
        })
        raise e
    finally:
        conn.close()
        
    return response

