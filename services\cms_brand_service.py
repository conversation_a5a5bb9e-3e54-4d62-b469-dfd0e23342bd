import bson
import pymongo
from services import Service
from bson import ObjectId
from datetime import datetime
from utils.common import calculatePaginationData, parse_json, processList, processListCategory
from fields.cms_fields import cms_brand_fields
from mongo_db import cms_brands_db, product_db, user_db, cms_db
import os
from werkzeug.utils import secure_filename
from flask import send_file, make_response
import logging

logger = logging.getLogger()

ALLOWED_EXTENSIONS = set(['png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov', 'wmv', 'mkv'])


def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def change_file_name(filename):
    rendom = str(round(datetime.utcnow().timestamp()))
    if ('.' in filename):
        ext = filename.rsplit('.', 1)[1].lower()
        fname = rendom + '.' + ext
        return fname
    

class Cms(Service):
    def _init_(self, repository):
        super()._init_(repository)
    
    def get_data(self,store, body, cdn_baseurl):        
        result = []        
        if 'webpage_id' in body:
            res = {}
            webpage_id = body['webpage_id']
            webPage = super().find_one({"_id": ObjectId(str(webpage_id))})
            if 'preview_state' in webPage and webPage['preview_state']:               
                preview_state = webPage['preview_state']
            else:
                preview_state = webPage['default_layout']

            preview_state =  self.addCdnToImageUrl(cdn_baseurl, preview_state)

            res['id'] = webPage['id']
            res['name'] = webPage['name']
            res['created_at'] = webPage['created_at']
            res['updated_at'] = webPage['updated_at']
            res['url'] = webPage['url']
            res['is_customers_only'] = webPage['is_customers_only']
            res['versions'] = preview_state

            result.append(res)
            return result
        else:            
            body['filterBy'] = ['name']            
            pages, total_data_length, paginationPage, limit = super().get_paginated_records_brands(body, cms_brand_fields,'')
            brands = super().find_all()
            nameResult = []
            for page in brands:
                test = {}
                test['Name'] = page['name']
                test['URL'] = page['url']
                nameResult.append(test)

            for page in pages:
                res = {}
                
                if page['url'] is None or page['url'] == "":
                    continue
                product_count=product_db.get_product_count_by_brand_id(store,page['bc_id'])
                versions = page['versions']
                if (len(versions) == 0):
                    activeVersion = page['default_layout']
                else:
                    for version in versions:
                        if (version['status'] == 'active'):
                            activeVersion = version
                            break
                
                activeVersion =  self.addCdnToImageUrl(cdn_baseurl, activeVersion)
                
                res['id'] = page['id']
                res['name'] = page['name']
                res['created_at'] = page['created_at']
                res['updated_at'] = page['updated_at']
                res['url'] = page['url']
                res['status'] = page['status']
                res["image_url"]=page['image_url']
                res["product_count"]=product_count
                res["type"] = page["type"]
                res['versions'] = activeVersion

                result.append(res)
            data = calculatePaginationData(
                result, paginationPage, limit, total_data_length)
            data['meta']['name_info'] = nameResult
            return data  
  
    def set_version(self, payload, id=None):
        response = {
            "status": 400            
        }
        created_by = {}
        if 'created_by' in payload:
            user = user_db.fetch_user_by_username(payload['created_by'])        
            created_by = {
                "user_id": str(user['_id']),
                "user_name": user['name']
            } 
        brand = super().find_one({"_id": ObjectId(id)})
        
        if brand:                                                                                           
            totalVersions = len(brand['versions'])
            versions = brand['versions']

            if totalVersions > 0:
                lastVersion = brand['versions'][-1]['version']
                for version in versions:
                    version['status'] = 'inactive'                    
            else:
                lastVersion = 0

            if (totalVersions >= 10):
                brand['versions'].pop(0)

            payload['created_by'] = created_by
            payload['created_at'] = int(datetime.utcnow().timestamp())
            payload['version'] = lastVersion + 1
            versions.append(payload)

            id = super().update_one({"_id": ObjectId(id)}, {"$set":
                                                                        {                                                                            
                                                                            "versions": brand['versions'],
                                                                            "updated_at":  int(datetime.utcnow().timestamp()),
                                                                            "image_url":payload['image_url']
                                                                        }})
                   
            response['message'] = "changes saved sucessfuly"
            response['status'] = 200
        else:
            response['message'] = "Brand not found."
            response['status'] = 404
        return response

    def update_customer_only_flag(self, body, id=None):
        response = {
            "status": 400
        }
        if id:
            id = super().update_one({"_id": ObjectId(str(id))}, {"$set":
                                                                            {                                                                             
                                                                                "is_customers_only": body['is_customers_only'],                                                                             
                                                                                "updated_at":  int(datetime.utcnow().timestamp())
                                                                            }
                                                                            })
            response['message'] = "Category Updated successfully"
            response['status'] = 200        

        return response

    def create_list(self, body):
        response = {
            "status": 400
        }
        url = body['name']
        # url = '/pages/' + url.replace(" ", "_").lower()
        url = url.replace(" ", "_").lower()
        # isUniqueName = self.checkForUniquePageName(body['name'])
        if True:
            body["created_at"] = int(datetime.utcnow().timestamp())
            body["updated_at"] = ""
            body["type"]=body["type"]
            body["url"] = url
            body["default_layout"] = {}
            body["versions"] = []
            body["preview_state"] = {}
            body['is_customers_only'] = False
            id = super().create(body)
            response['message'] = "Page created successfully"
            response['status'] = 200
        else:
            response['message'] = "The provided page Name has already been matched with other pages. Please provide a different page Name."
            response['status'] = 409

        return response
        # data['store_id'] = store['id']
        
        # # check if permission is already exits in DB.
        # permission = self.get_permission_by_store_id(store)

        # if permission == None:
        #     id = super().create(data)
        #     if id:
        #         return {"status": "success" }, 201
        # else:
        #     return {'message': '1 permission object already attched with the store name: ' + store['name']}, 400

    def get_brand(self, category_id=None):
        result = {}
        brand = super().find_one({"_id": ObjectId(str(category_id))})
        if (len(brand['versions']) == 0):
            activeVersion = brand['default_layout']
        else:
            versions = brand['versions']
            for version in versions:
                if (version['status'] == 'active'):
                    activeVersion = version
                    break

        result['id'] = brand['id']
        result['name'] = brand['name']
        result['created_at'] = brand['created_at']
        result['updated_at'] = brand['updated_at']
        result['url'] = brand['url']
        result['image_url']=brand['image_url']
        # result['is_customers_only'] = category['is_customers_only']
        result['versions'] = activeVersion

        return result

    def get_cms_versions(self, id=None):
        result = super().find_one({"_id": ObjectId(id)})
        list = {}
        versionArray = []
        if result is not None:
            if (len(result['versions']) > 0):
               versions = result['versions']
               for version in versions:
                  versionArray.append(version['version'])

               list['webpageVersions'] = versionArray

        return list
    
    def get_cms_versionData(self, body, id=None):
        list = {}
        result = super().find_one({"_id": ObjectId(id)})
        if result is not None:
           if (len(result['versions']) == 0):
               activeVersion = result['default_layout']
           else:
               versions = result['versions']
               for version in versions:
                   if (version['version'] == int(body['version'])):
                      activeVersion = version
                      break

           list['id'] = result['id']
           list['name'] = result['name']
           list['created_at'] = result['created_at']
           list['updated_at'] = result['updated_at']
           list['url'] = result['url']
           list['image_url']=result['image_url']
          #  list['is_customers_only'] = result['is_customers_only'] 
           list['versions'] = activeVersion

        return list
    
    def create_bc_brands(self, brand, req_body,store):
        created_by = {}
        if 'created_by' in req_body:
            user = user_db.fetch_user_by_username(req_body['created_by'])        
            created_by = {
                "user_id": str(user['_id']),
                "user_name": user['name']
            }           
        # Find in DB if available.
        db_brand = super().find_one({"id": int(brand['id'])}) 
          
        # description = brand["description"] 
        # if '%%GLOBAL_ShopPathSSL%%' in description or '%%GLOBAL_CdnStorePath%%' in description or '<a href="%%GLOBAL_ShopPathSSL%%' in description or '<a href="%%GLOBAL_CdnStorePath%%' in description:                                        
        #     print(category["id"], '  ||  ', category["name"], '  ||  ', category["custom_url"]["url"])

        # if '/searchresults.html?search_query=' in description:
        #     print(category["id"], '  ||  ', category["name"], '  ||  ', category["custom_url"]["url"])


        # description = description.replace('<a href="%%GLOBAL_ShopPathSSL%%', '<a href="') 
        # description = description.replace('<a href="%%GLOBAL_CdnStorePath%%', '<a href="')
        # description = description.replace("%%GLOBAL_ShopPathSSL%%", "https://cdn11.bigcommerce.com/s-964anr")
        # description = description.replace("%%GLOBAL_CdnStorePath%%", "https://cdn11.bigcommerce.com/s-964anr")   
        # description = description.replace("/searchresults.html?search_query=", "/search/?q=")             
        
           # If webpage not exists then create
        if not db_brand:            
            self.generate_page(brand, created_by)

        # If exists then check if it's modified or not if not then replace content
        if db_brand:                  
            self.update_page(brand, created_by) 
            self.getNavigationForBrands(brand,store)   

    def getNavigationForBrands(self, brand, store):
            navigations=cms_db.get_sub_nav(store)
            navigations=processList(navigations)
            for navigation in navigations:
                navigation['navigation_id']=ObjectId(navigation['navigation_id'])
                is_updated=False
                for data in navigation['navigations']:
                    if data['type'] == 'Brands' and data['id'] == 'Brands'+'_'+str(brand['id']) :
                        is_updated=True
                        data['url']=brand['custom_url']['url']
                if is_updated:
                   cms_db.update_sub_nav_by_id(store,navigation)
            return
    
    def generate_page(self, brand,created_by):
            new_list={} 
            seo_details={}
            seo_details["page_name"]=brand["name"]
            seo_details["page_url"]=brand["custom_url"]["url"]
            seo_details["meta_title"]=brand["page_title"]
            seo_details["meta_description"]=brand["meta_description"]           
            
            new_list["id"]=brand["id"]
            new_list["name"]=brand["name"]
            new_list["type"]="brand"            
            new_list["url"]=brand["custom_url"]["url"]                                         
            new_list["default_layout"] = {}
            new_list["versions"]= [
                { 
                "name":brand["name"],
                "status":"active", 
                "class":"",
                "type": "brand",
                "seo_details":seo_details,
                "components":[
                    {
                    "id": 1,
                    "name": "HTML Block",
                    "code": "html_block",  
                    "variant": {
                        "id": "1",
                        "name": "HTML Block",
                        "admin_layout": "style1",
                        "class": [],                          
                        "config": {
                            "data": ""   
                                }
                    },  
                    "children": []
                    }
                ],
                "created_at":int(datetime.utcnow().timestamp()),
                "created_by": created_by,                                                                              
                "version":0,
            } ]
            new_list["preview_state"] = {} 
            new_list["updated_at"]=""
            new_list["created_at"]=int(datetime.utcnow().timestamp())
            new_list["status"] = "active"
            new_list["image_url"]=brand['image_url']

            return cms_brands_db.create(new_list)

    def update_page(self, brand, created_by):  
            # print(brand,"brand")         
            update_obj = {
                'name': brand['name'],
                'type': 'brand',
                'url': brand["custom_url"]["url"],                            
                'versions': [{
                    'name': brand['name'],
                    'status': 'active',
                    'class': '',  
                    'type': 'brand',                  
                    'components': [{
                            'id': 1,
                            'name': 'HTML Block',                            
                            'code': 'html_block',
                            'variant': {
                                "id": '1',
                                'name': 'HTML Block',
                                'admin_layout': 'style1',
                                'class': [],                                
                                'config': {
                                    'data': ""    
                                }
                            },
                            'children': []
                    }],
                    'seo_details': {
                        'page_name': brand['name'],
                        'page_url': brand["custom_url"]["url"],
                        'meta_title': brand['page_title'],
                        'meta_description': brand['meta_description']
                    },
                    'created_by': created_by,
                    'created_at': int(datetime.utcnow().timestamp()),
                    'updated_at': int(datetime.utcnow().timestamp()),
                    'version': 1,
                    
                }],
                'image_url':brand['image_url']
            }                               
            return cms_brands_db.update({"id": brand['id']}, {"$set": update_obj})

        
    def addCdnToImageUrl(self, cdn_baseurl, activeVersion):
        if 'components' in activeVersion:           
            for component in activeVersion['components']:
                if 'variant' in component:
                    variant = component['variant']
                    if 'config' in variant:
                        config = variant['config']
                        if 'image_url' in config:
                            if config['image_url'] != '':
                                config['image_url'] = cdn_baseurl + '/brands' + config['image_url'].replace('/brands/images', '') 
                        if 'mobile_image_url' in config:
                            if config['mobile_image_url'] != '':
                                config['mobile_image_url'] = cdn_baseurl + '/brands' + config['mobile_image_url'].replace('/brands/images', '')                           
                        elif 'slider' in  config and 'side_images' in config:                            
                            sliders = config['slider']
                            side_images = config['side_images']
                            for slider in sliders:
                                if 'image_url' in slider:
                                    if slider['image_url'] != '':
                                        slider['image_url'] = cdn_baseurl + '/brands' + slider['image_url'].replace('/brands/images', '')

                            for side_image in side_images:
                                if 'image_url' in side_image:
                                    if side_image['image_url'] != '':
                                        side_image['image_url'] = cdn_baseurl + '/brands' + side_image['image_url'].replace('/brands/images', '')
                        elif 'banners' in  config:
                            banners = config['banners']
                            for banner in banners:
                                if 'image_url' in banner:
                                    if banner['image_url'] != '':
                                        banner['image_url'] = cdn_baseurl + '/brands' + banner['image_url'].replace('/brands/images', '')   
                                if 'mobile_image_url' in banner:
                                    if banner['mobile_image_url'] != '':
                                        banner['mobile_image_url'] = cdn_baseurl + '/brands' + banner['mobile_image_url'].replace('/brands/images', '')                          
                        elif 'logos' in config:
                            logos = config['logos'] 
                            for logo in logos:
                                if 'image_url' in logo:
                                    if logo['image_url'] != '':
                                        logo['image_url'] = cdn_baseurl + '/brands' + logo['image_url'].replace('/brands/images', '')

        return activeVersion             
        

    def get_user_by_username(self, username):
        res = user_db.fetch_user_by_username(username)
        res['id'] = str(res['_id'])

        del res["yoyo"]
        del res['_id']
        del res['created_at']
        del res['updated_at']

        return res
    

class SetImages():
    def setImage(self, body):
        response = {
            "status": 400
        }

        if not os.path.exists('images/brands/images'):
            os.makedirs('images/brands/images')

        file = body['image']
        UPLOAD_FOLDER = os.path.join('images/brands/images')

        if (file.filename == ''):
            response['message'] = "No image selected for uploading"
            response['status'] = 500

        if (file and allowed_file(file.filename)):
            newName = change_file_name(file.filename)
            fname = secure_filename(newName)
            file.save(os.path.join(UPLOAD_FOLDER, fname))
            base_path = os.path.join(os.path.abspath(
                os.getcwd()), UPLOAD_FOLDER, newName)
            
            if '/app/images' in base_path:
                base_path = base_path.replace('/app/images', '')             
            response['message'] = base_path
            response['status'] = 200
        else:
            response['message'] = "Allowed image types are -> png, jpg, jpeg, gif, mp4, avi, mov, wmv, mkv"
            response['status'] = 500

        return response

    def getImage(self, body):
        url = './images/brands/images/' + body['image']
        if not os.path.exists(url):
            return make_response({'error': 'Image not found'}, 404)

        return send_file(url, as_attachment=True)