from utils.common import parse_json, processListCategory
import datetime
from bson import ObjectId
from pymongo.collation import Collation

class Services():

    def __init__(self, app, db):
        self.app = app
        self.db = db

        from services import tenant_service, component_service, permissions_service,cms_service,cms_brand_service, user_role_service, tasks_service, tokens_service, jobDetails_service, app_menu_service, email_template_service, store_info_service
        repository = self.db.get_tenant_repository()
        self._tenant_service = tenant_service.Tenant(repository)

        repository = self.db.get_store_repository()
        self._store_service = tenant_service.TenantStore(repository)

        repository = self.db.get_component_repository()
        self._component_service = component_service.Component(repository)

        repository = self.db.get_permission_repository()
        self._permission_service = permissions_service.Permissions(repository)

        repository = self.db.get_cms_repository()
        self._cms_service = cms_service.Cms(repository)

        repository = self.db.get_cms_brand_repository()
        self._cms_brand_service = cms_brand_service.Cms(repository)

        repository = self.db.get_user_role_repository()
        self._user_role_service = user_role_service.UserRole(repository)

        repository = self.db.get_app_menu_repository()
        self._app_menu_service = app_menu_service.AppMenu(repository)

        repository = self.db.get_tasks_repository()
        self.tasks_service = tasks_service.Tasks(repository)

        repository = self.db.get_tokens_repository()
        self.tokens_service = tokens_service.Configs(repository)
        
        repository = self.db.get_job_details_repository()
        self.jobDetails_service = jobDetails_service.Details(repository)

        repository = self.db.get_email_template_repository()
        self.email_template_service = email_template_service.EmailTemplates(repository)

        repository = self.db.get_storeinfo_repository()
        self._storeinfo_service = store_info_service.StoreInfo(repository)

    def get_tenant_service(self):
        return self._tenant_service

    # Component service ...
    def get_components_service(self):
        return self._component_service
    
    # Email service ...
    def get_email_template_service(self):
        return self.email_template_service

    # Permission service ...
    def get_permission_service(self):
        return self._permission_service
    
    def get_cms_service(self):
        return self._cms_service
    
    def get_cms_brand_service(self):
        return self._cms_brand_service
    
    def get_storeinfo_service(self):
        return self._storeinfo_service

    # Permission service ...
    def get_user_role_service(self):
        return self._user_role_service
    
    def get_app_menu_service(self):
        return self._app_menu_service
    
    def get_task_service(self):
        return self.tasks_service
    
    def get_token_service(self):
        return self.tokens_service
    
    def get_jobDetails_service(self):
        return self.jobDetails_service

    def get_store_service(self):
        return self._store_service

    def get_store(self, store_url):
        return self.get_store_service().get_store_by_url(store_url)

    def get_store_by_id(self, tenant_id, store_id):
        return self.get_store_service().get_store_by_id(store_id)

    # Customer Service ...
    def get_customers_service(self, db_name):
        repository = self.db.get_customers_repository(db_name)

        from services import customer_service
        return customer_service.Customers(repository)

    # Navigation Service ...
    def get_navigations_service(self, db_name):
        repository = self.db.get_navigations_repository(db_name)

        from services import navigation_service
        return navigation_service.Navigations(repository)

    # Sub Navigation Service ...
    def get_sub_navigations_service(self, db_name):
        repository = self.db.get_sub_navigations_repository(db_name)

        from services import sub_navigations_service
        return sub_navigations_service.SubNavigations(repository)

    # brands service ...
    def get_brands_service(self, db_name):
        repository = self.db.get_brands_repository(db_name)

        from services import brands_service
        return brands_service.Brands(repository)

    # category service ...
    def get_category_service(self, db_name):
        repository = self.db.get_categories_repository(db_name)

        from services import category_service
        return category_service.Categories(repository)

    # webpages service ...
    def get_web_pages_service(self, db_name):
        repository = self.db.get_web_pages_repository(db_name)

        from services import webpages_service
        return webpages_service.WebPages(repository)

    # dynamic pages service ...
    def get_dynamic_pages_service(self, db_name):
        repository = self.db.get_dynamic_pages_repository(db_name)

        from services import dynamic_pages_service
        return dynamic_pages_service.Pages(repository)

    def set_dynamic_pages_versions(self, db_name):
        repository = self.db.get_dynamic_pages_repository(db_name)

        from services import dynamic_pages_service
        return dynamic_pages_service.PageVersions(repository)

    def set_images(self):
        from services import dynamic_pages_service
        return dynamic_pages_service.SetImages()
    
    def get_bc_product_data(self):
        from services import dynamic_pages_service
        return dynamic_pages_service.BcProductData()
    
    def set_app_builder_category_images(self):
        from services import cms_service
        return cms_service.SetImages()
    
    def set_cms_brand_images(self):
        from services import cms_brand_service
        return cms_brand_service.SetImages()
    
    def set_store_info_images(self):
        from services import store_info_service
        return store_info_service.SetImages()

    # blogs service ...
    def get_blogs_service(self, db_name):
        repository = self.db.get_blogs_repository(db_name)

        from services import blogs_service
        return blogs_service.Blogs(repository)

    def set_blog_data(self, db_name):
        repository = self.db.get_blogs_repository(db_name)

        from services import blogs_service
        return blogs_service.BlogData(repository)

    def set_blog_images(self):
        from services import blogs_service
        return blogs_service.SetImages()

    def get_blogs_author_service(self, db_name):
        repository = self.db.get_blogs_author_repository(db_name)

        from services import blogs_author_service
        return blogs_author_service.Authors(repository)

    def get_blogs_category_service(self, db_name):
        repository = self.db.get_blogs_category_repository(db_name)

        from services import blogs_category_service
        return blogs_category_service.Categories(repository)
    
    # Redirects service ...
    def get_redirects_service(self, db_name):
        repository = self.db.get_redirects_repository(db_name)

        from services import redirects_service
        return redirects_service.Redirects(repository)

    # rewards service ...

    def get_loyalty_rewards_service(self, db_name):
        repository = self.db.get_loyalty_rewards_repository(db_name)

        from services import loyalty_rewards_service
        return loyalty_rewards_service.Rewards(repository)

    def get_customers_points_service(self, db_name):
        repository = self.db.get_customers_repository(db_name)

        from services import loyalty_rewards_service
        return loyalty_rewards_service.points(repository)

    # Mails Templates Services
    def get_mail_template_type_service(self, db_name):
        repository = self.db.get_mail_template_type_repository(db_name)

        from services import mail_template_type_service
        return mail_template_type_service.TemplateTypes(repository)

    # Theme Build Service ...

    def get_build_service(self, db_name):
        repository = self.db.get_theme_build_repository(db_name)

        from services import theme_build_service
        return theme_build_service.Builds(repository)

     # Google Sheet Data Service ...
    def get_sheet_data_service(self):
        # repository = self.db.get_theme_build_repository(db_name)

        from services import google_sheet_service
        return google_sheet_service.Data()

    # MappingTable Service ...
    def get_mappingtables_service(self, db_name):
        repository = self.db.get_mappingtables_repository(db_name)

        from services import google_sheet_service
        return google_sheet_service.MappingTables(repository)

    def get_mappingtables_data_service(self, db_name):
        repository = self.db.get_mappingtables_repository(db_name)

        from services import google_sheet_service
        return google_sheet_service.MappingTablesData(repository)


class Service():
    def __init__(self, repository):
        self.repository = repository

    def processDocument(self, obj):
        if obj:
            if '_id' in obj:
                obj['id'] = str(obj['_id'])
                del obj['_id']

            for key, value in obj.items():
                if isinstance(value, ObjectId):
                    obj[key] = str(value)
                elif isinstance(value, datetime.datetime):
                    obj[key] = int(value.timestamp())

        return obj

    def processList(self, data):
        result = []
        if data:
            for _obj in data:
                result.append(self.processDocument(_obj))
        return result

    def create(self, dto):
        id = self.repository.create(dto).inserted_id
        return id

    def find_one(self, query):
        result = self.repository.find_one(query)
        return self.processDocument(result)

    def find_by_id(self, _id):
        result = self.repository.find_one({'_id': ObjectId(str(_id))})
        return self.processDocument(result)

    def find_all_active(self):
        result = self.repository.find({"status": "active"})
        return result

    def find(self, query):
        data = self.repository.find(query)
        return self.processList(data)

    def get_all_records_with_selected_fields(self, query, fields):
        data = self.repository.find(query, fields)
        return self.processList(data)

    def soft_delete(self, query):
        self.repository.soft_delete(query)

    def find_all(self):
        data = self.repository.find({})
        result = []
        for document in data:
            result.append(document)
        return result
    
    def find_all_categories(self):
        data = self.repository.find({})
        data = processListCategory(data)
        return parse_json(data)

    def update_one(self, query, updated_data):
        return self.repository.update_one(query, updated_data)

    def delete_by_id(self, id):
        result = self.repository.hard_delete(ObjectId(id))
        return result.acknowledged and result.deleted_count == 1

    def delete(self, query):
        result = self.repository.hard_delete_by_query(query)
        return result.deleted_count

    def get_paginated_records(self, payload, fields, db, flag):

        def create_reg_ex_query(filterBy, value):
            regex = "^" + value + ".*"

            return {
                filterBy: {
                    "$regex": regex,
                    "$options": 'i'
                }
            }

        limit = int(payload["limit"]) if payload.__contains__("limit") else 10
        page = int(payload["page"]) if payload.__contains__("page") else 1
        skips = payload['skips'] if payload.__contains__('skips') else 0

        query = create_reg_ex_query(
            payload["filterBy"], payload['filterValue'])

        # Calculate number of records to skip ...
        skips = limit * (page - 1)

        # Sort by date item last added ...

        sort_by = str(payload['filterBy']) if payload.__contains__(
            "filterBy") else "date_created"
        data = []
        document_length = any
        if flag:
            if payload['table_collection'] in db.list_collection_names():
                collection = db[payload['table_collection']]
                sort_by = payload['sortBy']
                if (not sort_by):
                    sort_by = ' '

                # sort_direction = 1 if payload['sortDirection'] else -1
                sort_direction = payload['sortDirection']

                if int(sort_direction) == 1:
                    documents = list(collection.find(query, fields).sort(
                        sort_by, 1).skip(skips).limit(limit))
                else:
                    documents = list(collection.find(query, fields).sort(
                        sort_by, -1).skip(skips).limit(limit))

                for doc in documents:
                    doc['_id'] = str(doc['_id'])

                data = documents
                document_length = collection.count_documents(query)
        else:
            sort_by = "created_at" if sort_by == "coupon_value" else sort_by
            data = self.repository.find(query, fields).sort(
                sort_by, -1).skip(skips).limit(limit)
            document_length = self.repository.count_documents(query)

        # ProcessList ...
        data = self.processList(data)
        return parse_json(data), document_length, page, limit

    def get_paginated_records_updated(self, payload, fields, additionalQuery):
        sort = {
            'sort_by': payload['sort_by'] or 'date_created'
        }

        if payload['sort_order'] == 'asc':
            sort['sort_order'] = 1
        else:
            sort['sort_order'] = -1

        def create_reg_ex_query(filterBy, filter):
            query = {
                "$or": [],
            }

            for i in filterBy:
                query['$or'].append({i: {"$regex": filter, "$options": "i"}},)

            if "type" not in query and "type" in payload:
                 query["type"] = payload["type"]

            if "status" in payload:
               if payload["status"] == "active":
                  query["is_visible"] = True
               elif payload["status"] == "inactive":
                  query["is_visible"] = False
               elif payload["status"] == "out_of_stock":
                  query["$expr"]={
                        "$lte": ["$inventory_level", "$inventory_warning_level"]
                    }          
            query.update(additionalQuery)
            return query

        limit = int(payload["limit"]) if payload.__contains__("limit") else 10
        page = int(payload["page"]) if payload.__contains__("page") else 1
        skips = payload['skips'] if payload.__contains__('skips') else 0

        query = create_reg_ex_query(payload["filterBy"], payload['filter']) if len(
            payload["filterBy"]) else {}
        # Calculate number of records to skip ...
        skips = limit * (page - 1)
        collation = Collation(locale='en', strength=2)
        data = self.repository.find(query, fields).collation(collation).sort(
            sort['sort_by'], sort['sort_order']).skip(skips).limit(limit)
        # ProcessList ...
        data = self.processList(data)
       

        document_length = self.repository.count_documents(query)

        return parse_json(data), document_length, page, limit
    
    # def get_paginated_records_taskName(self, payload, fields, additionalQuery):
    #     sort = {
    #         'sort_by': payload['sort_by'] or 'date_created'
    #     }

    #     if payload['sort_order'] == 'asc':
    #         sort['sort_order'] = 1
    #     else:
    #         sort['sort_order'] = -1

    #     def create_reg_ex_query(filterBy, filter):
    #         query = {
    #             "$or": [],
    #         }

    #         for i in filterBy:
    #             query['$or'].append({i: {"$regex": filter, "$options": "i"}},)

    #         if "type" not in query and "type" in payload:
    #             query["type"] = payload["type"]


    #         # Include filtering based on taskName if provided
    #         if "taskName" in payload:
    #             query["taskName"] = payload["taskName"]

    #         query.update(additionalQuery)
    #         return query

    #     limit = int(payload["limit"]) if "limit" in payload else 10
    #     page = int(payload["page"]) if "page" in payload else 1
    #     skips = payload.get('skips', 0)

    #     query = create_reg_ex_query(payload.get("filterBy", []), payload.get('filter', ''))
        
    #     # Calculate number of records to skip ...
    #     skips = limit * (page - 1)
    #     collation = Collation(locale='en', strength=2)
    #     data = self.repository.find(query, fields).collation(collation).sort(
    #         sort['sort_by'], sort['sort_order']).skip(skips).limit(limit)
    #     # ProcessList ...
    #     data = self.processList(data)

    #     document_length = self.repository.count_documents(query)

    #     return parse_json(data), document_length, page, limit


    #     if payload['sort_order'] == 'asc':
    #         sort['sort_order'] = 1
    #     else:
    #         sort['sort_order'] = -1

    #     def create_reg_ex_query(filterBy, filter):
    #         query = {
    #             "$or": [],
    #         }

    #         for i in filterBy:
    #             query['$or'].append({i: {"$regex": filter, "$options": "i"}},)

    #         if "type" not in query and "type" in payload:
    #             query["type"] = payload["type"]


    #         # Include filtering based on taskName if provided
    #         if "taskName" in payload:
    #             query["taskName"] = payload["taskName"]

    #         query.update(additionalQuery)
    #         return query

    #     limit = int(payload["limit"]) if "limit" in payload else 10
    #     page = int(payload["page"]) if "page" in payload else 1
    #     skips = payload.get('skips', 0)

    #     query = create_reg_ex_query(payload.get("filterBy", []), payload.get('filter', ''))
        
    #     # Calculate number of records to skip ...
    #     skips = limit * (page - 1)
    #     collation = Collation(locale='en', strength=2)
    #     data = self.repository.find(query, fields).collation(collation).sort(
    #         sort['sort_by'], sort['sort_order']).skip(skips).limit(limit)
    #     # ProcessList ...
    #     data = self.processList(data)

    #     document_length = self.repository.count_documents(query)

    #     return parse_json(data), document_length, page, limit


    def get_paginated_records_brands(self, payload, fields, additionalQuery):
        sort = {
            'sort_by': payload['sort_by'] or 'date_created'
        }

        if payload['sort_order'] == 'asc':
            sort['sort_order'] = 1
        else:
            sort['sort_order'] = -1

        def create_reg_ex_query(filterBy, filter):
            query = {
                "$or": [],
            }

            for i in filterBy:
                query['$or'].append({i: {"$regex": filter, "$options": "i"}},)

            if "type" not in query and "type" in payload:
                 query["type"] = payload["type"]

            if "status" in payload:
               if payload["status"] == "active":
                  query["is_visible"] = True
               elif payload["status"] == "inactive":
                  query["is_visible"] = False
               elif payload["status"] == "out_of_stock":
                  query["inventory_level"] = 0              
            query.update(additionalQuery)
            return query

        limit = int(payload["limit"]) if payload.__contains__("limit") else 10
        page = int(payload["page"]) if payload.__contains__("page") else 1
        skips = payload['skips'] if payload.__contains__('skips') else 0

        query = create_reg_ex_query(payload["filterBy"], payload['filter']) if len(
            payload["filterBy"]) else {}
        # Calculate number of records to skip ...
        skips = limit * (page - 1)
        collation = Collation(locale='en', strength=2)
        data = self.repository.find(query, fields).collation(collation).sort(
            sort['sort_by'], sort['sort_order']).skip(skips).limit(limit)
        # ProcessList ...
        data = processListCategory(data)
       

        document_length = self.repository.count_documents(query)

        return parse_json(data), document_length, page, limit

  
    def get_paginated_records_loyalty_points(self, payload, fields):
        def create_reg_ex_query(filterBy, value):
            regex = "^" + value + ".*"

            return {
                filterBy: {
                    "$regex": regex,
                    "$options": 'i'
                }
            }

        limit = int(payload["limit"]) if payload.__contains__("limit") else 10
        page = int(payload["page"]) if payload.__contains__("page") else 1
        skips = payload['skips'] if payload.__contains__('skips') else 0

        query = create_reg_ex_query(
            payload["filterBy"], payload['filterValue'])

        # Calculate number of records to skip ...
        skips = limit * (page - 1)

        query = {
            "$and": [
                query,  # Your original query, if any
                {"loyalty_points": {"$exists": True}}
            ]
        }
        # Sort by date item last added ...
        sort_by = "created_at"
        data = self.repository.find(query, fields).sort(
            sort_by, -1).skip(skips).limit(limit)

        # ProcessList ...
        data = self.processList(data)

        document_length = self.repository.count_documents(query)

        return parse_json(data), document_length, page, limit

    def get_shorted_data(self, db):
        collection = db['blogs']
        documents = list(collection.find().sort("created_at", -1))
        data = self.processList(documents)
        return parse_json(data)

    def get_related_blogs(self, blog_tags, current_blog_id, db):
        collection = db['blogs']
        related_blogs = list(collection.find({
            'tags': {'$in': blog_tags},
            '_id': {'$ne': ObjectId(str(current_blog_id))}
        }).sort("created_at", -1).limit(3))
        related_blogs = self.processList(related_blogs)
        return parse_json(related_blogs)
    
    def get_paginated_records_updated_category(self, payload, fields, additionalQuery):
        sort = {
            'sort_by': payload['sort_by'] or 'date_created'
        }
        if payload['sort_order'] == 'asc':
            sort['sort_order'] = 1
        else:
            sort['sort_order'] = -1

        def create_reg_ex_query(filterBy, filter):
            query = {
                "$or": [],
            }

            for i in filterBy:
                query['$or'].append({i: {"$regex": filter, "$options": "i"}},)

            if "type" not in query and "type" in payload:
                 query["type"] = payload["type"]

            if "status" in payload:
               if payload["status"] == "active":
                  query["is_visible"] = True
               elif payload["status"] == "inactive":
                  query["is_visible"] = False
               elif payload["status"] == "out_of_stock":
                  query["inventory_level"] = 0              
            query.update(additionalQuery)
            return query

        limit = int(payload["limit"]) if payload.__contains__("limit") else 10
        page = int(payload["page"]) if payload.__contains__("page") else 1
        skips = payload['skips'] if payload.__contains__('skips') else 0

        query = create_reg_ex_query(payload["filterBy"], payload['filter']) if len(
            payload["filterBy"]) else {}
        query['parent_id'] = 0

        # Calculate number of records to skip ...
        skips = limit * (page - 1)
        collation = Collation(locale='en', strength=2)
        data = self.repository.find(query, fields).collation(collation).sort(
            sort['sort_by'], sort['sort_order']).skip(skips).limit(limit)
        # ProcessList ...
        data = processListCategory(data)
       
        document_length = self.repository.count_documents(query)

        return parse_json(data), document_length, page, limit

   
