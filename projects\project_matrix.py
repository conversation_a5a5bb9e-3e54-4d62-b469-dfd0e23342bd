from sqlalchemy import text
import pg_db
from mongo_db import user_db
import logging
from datetime import datetime, timedelta
from collections import defaultdict
from sqlalchemy.sql import text
from utils.common import convert_time_format

logger = logging.getLogger()


def get_matrix(project_id, username, x_axis, y_axis, sort_array):
    response = {
        "status": 400
    }

    conn = pg_db.get_connection()
    try:
        # Check user access to the project
        project_access_query = """
        SELECT DISTINCT apa.project_id
        FROM agile_project_access apa
        WHERE apa.username = :username AND apa.status = 'active'
        """
        project_access_query = text(project_access_query).params(username=username)
        user_projects = conn.execute(project_access_query).fetchall()

        if not user_projects:
            response['status'] = 404
            response['message'] = "No projects found for the given username."
            return response

        project_ids = [row[0] for row in user_projects]

        # Initialize result data
        data = {
            "data": [],
            "meta": {}
        }

        # Fetch x_axis and y_axis data
        axis_data = {}

        if x_axis == "Module" or y_axis == "Module":
            module_query = """
            SELECT apm.id, apm.name
            FROM agile_project_modules apm
            WHERE apm.project_id = :project_id
            """
            module_query = text(module_query).params(project_id=project_id)
            modules = conn.execute(module_query).fetchall()
            axis_data["Module"] = {row[0]: row[1].split('_')[0] for row in modules}

        if x_axis == "Assignee" or y_axis == "Assignee":
            assignee_query = """
            SELECT DISTINCT apa.username
            FROM agile_project_access apa
            WHERE apa.project_id = :project_id
            """
            assignee_query = text(assignee_query).params(project_id=project_id)
            assignees = conn.execute(assignee_query).fetchall()
            axis_data["Assignee"] = {}
            for row in assignees:
                user_name = user_db.fetch_user_by_username(row[0])
                axis_data["Assignee"][row[0]] = user_name.get('name', 'Unassigned') if user_name else 'Unassigned'

        if x_axis == "Priority" or y_axis == "Priority":
            priority_query = """
            SELECT DISTINCT acp.id, acp.label
            FROM agile_card_priorities acp
            """
            priority_query = text(priority_query)
            priorities = conn.execute(priority_query).fetchall()
            axis_data["Priority"] = {row[0]: row[1] for row in priorities}

        if x_axis == "Stage" or y_axis == "Stage":
            stage_query = """
            SELECT DISTINCT apc.id, apc.name
            FROM agile_project_columns apc
            WHERE apc.project_id = :project_id
            """
            stage_query = text(stage_query).params(project_id=project_id)
            stages = conn.execute(stage_query).fetchall()
            axis_data["Stage"] = {row[0]: row[1].split('_')[0] for row in stages}

        # Fetch card counts for all combinations
        if x_axis in axis_data and y_axis in axis_data:
            join_table = {
                ("Module", "Assignee"): ("agile_project_cards apc", "apc.module_id", "apc.assigned_to"),
                ("Assignee", "Module"): ("agile_project_cards apc", "apc.assigned_to", "apc.module_id"),
                ("Stage", "Module"): ("agile_project_cards apc", "apc.current_column_id", "apc.module_id"),
                ("Module", "Stage"): ("agile_project_cards apc", "apc.module_id", "apc.current_column_id"),
                ("Stage", "Assignee"): ("agile_project_cards apc", "apc.current_column_id", "apc.assigned_to"),
                ("Assignee", "Stage"): ("agile_project_cards apc", "apc.assigned_to", "apc.current_column_id"),
                ("Priority", "Stage"): ("agile_project_cards apc", "apc.priority", "apc.current_column_id"),
                ("Stage", "Priority"): ("agile_project_cards apc", "apc.current_column_id", "apc.priority"),
                ("Priority", "Module"): ("agile_project_cards apc", "apc.priority", "apc.module_id"),
                ("Module", "Priority"): ("agile_project_cards apc", "apc.module_id", "apc.priority"),
                ("Priority", "Assignee"): ("agile_project_cards apc", "apc.priority", "apc.assigned_to"),
                ("Assignee", "Priority"): ("agile_project_cards apc", "apc.assigned_to", "apc.priority")
            }

            table, x_field, y_field = join_table.get((x_axis, y_axis), (None, None, None))

            if table:
                card_query = f"""
                SELECT {x_field} AS x_value, {y_field} AS y_value, COUNT(*) AS card_count
                FROM {table}
                WHERE apc.project_id = :project_id
                """
                card_query += f" GROUP BY {x_field}, {y_field}"
                
                if len(sort_array):
                    sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'        
                    if sort_array[0] in ["value"]:                
                        card_query += f" ORDER BY y_value {sort_direction}"

                card_query = text(card_query).params(project_id=project_id)
                cards = conn.execute(card_query).fetchall()

                # Generate meta mapping with unique names instead of IDs
                unique_x_values = sorted(set("Unassigned" if not row[0] else row[0] for row in cards)) 
                field_index = 1
                mapped_fields = {}
                unassigned_field_name = None

                for x_value in unique_x_values:
                    name = axis_data[x_axis].get(x_value, 'Unassigned') if isinstance(axis_data[x_axis], dict) else 'Unassigned'
                    if name == 'Unassigned':
                        if not unassigned_field_name:
                            unassigned_field_name = f"field_{field_index}"
                            data["meta"][unassigned_field_name] = name
                            field_index += 1
                        mapped_fields[x_value] = unassigned_field_name
                    else:
                        field_name = f"field_{field_index}"
                        data["meta"][field_name] = name
                        mapped_fields[x_value] = field_name
                        field_index += 1

                # Organize data with consolidated "Unassigned"
                grouped_data = {}
                unassigned_key = None
                for row in cards:
                    x_value, y_value, card_count = row
                    field_name = mapped_fields.get(x_value, unassigned_field_name)
                    y_name = axis_data[y_axis].get(y_value, 'Unassigned') if isinstance(axis_data[y_axis], dict) else 'Unassigned'

                    # Handle "Unassigned" consolidation
                    if y_name == 'Unassigned':
                        if unassigned_key is None:
                            unassigned_key = "unassigned_combined"
                            grouped_data[unassigned_key] = {"value": "Unassigned", "card_counts": {}}
                        grouped_data[unassigned_key]["card_counts"].setdefault(field_name, 0)
                        grouped_data[unassigned_key]["card_counts"][field_name] += card_count
                    else:
                        if y_value not in grouped_data:
                            grouped_data[y_value] = {"value": y_name, "card_counts": {}}
                        grouped_data[y_value]["card_counts"].setdefault(field_name, 0)
                        grouped_data[y_value]["card_counts"][field_name] += card_count

                # Ensure card counts are sorted by field index
                for item in grouped_data.values():
                    item["card_counts"] = dict(sorted(item["card_counts"].items(), key=lambda k: int(k[0].split("_")[1])))

                data["data"] = list(grouped_data.values())
            else:
                response["message"] = f"Invalid x_axis ({x_axis}) or y_axis ({y_axis}) provided."

        response["status"] = 200
        response["data"] = data
    except Exception as e:
        response["status"] = 500
        response["message"] = str(e)
    finally:
        conn.close()

    return response