import queue
from bson import ObjectId
import datetime
from new_mongodb import get_admin_db_client_for_store_id, get_store_by_id
from new_mongodb import RoleKeys, UserKeys, StoreKeys, StoreAdminDBCollections

def create_role(store_id, role):
    admin_db = get_admin_db_client_for_store_id(store_id=store_id)
    return admin_db[StoreAdminDBCollections.ROLES_COLLECTION].insert_one(role)

def fetch_role_by_name(store_id, role_name, projection={}):
    admin_db = get_admin_db_client_for_store_id(store_id=store_id)
    roles_coll = admin_db[StoreAdminDBCollections.ROLES_COLLECTION]
    return roles_coll.find_one({"role": role_name}, projection)

def fetch_role_by_id(store_id, role_id, projection=None):
    admin_db = get_admin_db_client_for_store_id(store_id=store_id)
    roles_coll = admin_db[StoreAdminDBCollections.ROLES_COLLECTION]
    if not projection:
        projection = {RoleKeys.PERMISSIONS: 1, RoleKeys.ROLE: 1, RoleKeys.STATUS: 1,
                        RoleKeys.IS_ADMIN_ACCESS: 1, RoleKeys.IS_SUPER_ADMIN: 1}

    return roles_coll.find_one({"_id": ObjectId(str(role_id))}, projection)

def update_role(store_id, role_id, role):
    admin_db = get_admin_db_client_for_store_id(store_id=store_id)
    role['updated_at'] = int(datetime.datetime.now(datetime.timezone.utc).timestamp())
    update = {
        "$set": role
    } 
    query = {
        "_id": ObjectId(str(role_id))
    }    
    admin_db[StoreAdminDBCollections.ROLES_COLLECTION].update_one(query, update)

def delete_role(store_id, role_id):
    admin_db = get_admin_db_client_for_store_id(store_id=store_id)
    query = {
        "_id": ObjectId(str(role_id))
    }
    return admin_db[StoreAdminDBCollections.ROLES_COLLECTION].delete_one(query)
    
def fetch_users(store_id, query={}, fields={}):
    admin_db = get_admin_db_client_for_store_id(store_id=store_id)
    users_coll = admin_db[StoreAdminDBCollections.USERS_COLLECTION]
    return users_coll.find(query, fields)

def fetch_user_by_username(store_id, username):
    admin_db = get_admin_db_client_for_store_id(store_id=store_id)
    users_coll = admin_db[StoreAdminDBCollections.USERS_COLLECTION]
    return users_coll.find_one({UserKeys.USERNAME: username})

def fetch_one_user(store_id, query={}, field={}):
    admin_db = get_admin_db_client_for_store_id(store_id=store_id)
    users_coll = admin_db[StoreAdminDBCollections.USERS_COLLECTION]
    return users_coll.find_one(query, field)

def fetch_user_role(store_id, username):
    user_role = None
    admin_db = get_admin_db_client_for_store_id(store_id=store_id)
    print(admin_db)
    user_coll = admin_db[StoreAdminDBCollections.USERS_COLLECTION]
    
    user = user_coll.find_one({UserKeys.USERNAME: username}, {UserKeys.ROLE_ID: 1})
    if user:
        role_id = user[UserKeys.ROLE_ID]        
        if role_id:
            user_role = fetch_role_by_id(store_id, role_id)            
    return user_role

def fetch_google_creds(store_id):
    store = get_store_by_id(store_id)
    return store.get(StoreKeys.GOOGLE_OAUTH_CREDS, None)

def update_user_role_count(store_id, role_id, value):
    user_count = {
        "$inc": {
            'total_users': value
        }
    }
    admin_db = get_admin_db_client_for_store_id(store_id=store_id)
    return admin_db[StoreAdminDBCollections.ROLES_COLLECTION].update_one({"_id": ObjectId(str(role_id))}, user_count)
    
def create_user(store_id, user):
    admin_db = get_admin_db_client_for_store_id(store_id=store_id)
    user['updated_at'] = int(datetime.datetime.now(datetime.timezone.utc).timestamp())
    return admin_db[StoreAdminDBCollections.USERS_COLLECTION].insert_one(user).inserted_id

def update_user(store_id, query, user):
    admin_db = get_admin_db_client_for_store_id(store_id=store_id)
    user['updated_at'] = int(datetime.datetime.now(datetime.timezone.utc).timestamp())
    update = {
        "$set": user
    } 
    return admin_db[StoreAdminDBCollections.USERS_COLLECTION].update_one(query, update)

def delete_user_by_username(store_id, username):
    admin_db = get_admin_db_client_for_store_id(store_id=store_id)
    query = {UserKeys.USERNAME: username}
    update = { 
        "$set": {
            "status": 'deleted',
            "updated_at": int(datetime.datetime.now(datetime.timezone.utc).timestamp())
        }
    }
    return admin_db[StoreAdminDBCollections.USERS_COLLECTION].update_one(query, update)

def delete_user_by_user_id(store_id, user_id):
    admin_db = get_admin_db_client_for_store_id(store_id=store_id)
    query = {UserKeys.ID: ObjectId(str(user_id))}
    update = { 
        "$set": {
            "status": 'deleted',
            "updated_at": int(datetime.datetime.now(datetime.timezone.utc).timestamp())
        }
    }
    return admin_db[StoreAdminDBCollections.USERS_COLLECTION].update_one(query, update)

def delete_user(store_id, user_id):
    admin_db = get_admin_db_client_for_store_id(store_id=store_id)
    query = {UserKeys.ID: ObjectId(str(user_id))}
    return admin_db[StoreAdminDBCollections.USERS_COLLECTION].delete_one(query)

def fetch_store_users(store_id, query={}, project={}):
    admin_db = get_admin_db_client_for_store_id(store_id=store_id)
    return admin_db[StoreAdminDBCollections.USERS_COLLECTION].find(query, project)

def count_store_users(store_id, query={}):
    query_with_status = {**query, "status": {"$ne": "deleted"}}
    admin_db = get_admin_db_client_for_store_id(store_id=store_id)
    return admin_db[StoreAdminDBCollections.USERS_COLLECTION].count_documents(query_with_status)

def change_user_status(store_id, user_id, status):
    admin_db = get_admin_db_client_for_store_id(store_id=store_id)
    return admin_db[StoreAdminDBCollections.USERS_COLLECTION].update_one({UserKeys.ID: ObjectId(str(user_id))}, {"$set": {UserKeys.STATUS: status}})
