from datetime import datetime
import logging

from bson import ObjectId
import mongo_db
import new_mongodb
from utils.common import processListCategory

logger = logging.getLogger()

CMS_BRANDS_COLLECTION = "cms_brands"

def get_all_brands():
    db = mongo_db.get_admin_db_client()
    result= db[CMS_BRANDS_COLLECTION].find()
    return result

def get_page_by_id(id):
    db = mongo_db.get_admin_db_client()
    result= db[CMS_BRANDS_COLLECTION].find_one({"_id": ObjectId(id)})
    return result

def create(payload):
    db = mongo_db.get_admin_db_client()
    result=db[CMS_BRANDS_COLLECTION].insert_one(payload)
    return result

def update(query, payload):
    db = mongo_db.get_admin_db_client()
    result=db[CMS_BRANDS_COLLECTION].update_one(query, payload)
    return result

def has_categories_with_parent_id(parent_id):
    db = mongo_db.get_admin_db_client()
    result= db[CMS_BRANDS_COLLECTION].find_one({"parent_id": int(parent_id)})
    return result is not None

def add_has_sub_child_flag(id,has_sub_child):
    db = mongo_db.get_admin_db_client()
    if id:
        db[CMS_BRANDS_COLLECTION].update_one(
            {"_id":ObjectId(id)},
            {"$set": {"has_sub_child": has_sub_child}}
        )
    
def get_categories_by_parent_id(parent_id):
    db = mongo_db.get_admin_db_client()
    pipeline = [
        {"$match": {"parent_id": int(parent_id)}},  # Filter documents by parent_id
        {"$addFields": {"versions": {"$filter": {
            "input": "$versions",  # Input array to filter
            "as": "version",  # Variable name for each element
            "cond": {"$eq": ["$$version.status", "active"]}  # Condition to filter by status
        }}}},
        {"$project": {"_id":1,"id":1,"name":1,"created_at":1,"updated_at":1,"url":1,"status":1,"is_visible":1,"parent_id":1,"views":1,"sort_order":1,"type":1,"is_customers_only":1,"versions": "$versions","has_sub_child":1}}  # Project only the active_versions field
    ]
    result= db[CMS_BRANDS_COLLECTION].aggregate(pipeline)
    result=processListCategory(result)
    return result

def processDocumentCms(obj):
        if obj:
            if 'id' in obj:
                obj['id']=obj['id']
            if '_id' in obj:
                # obj['id'] = str(obj['_id'])
                del obj['_id']
        
            for key, value in obj.items():
                if isinstance(value, datetime) or isinstance(value, ObjectId):
                    obj[key] = str(value)

        return obj

def processListCms( data):
        result = []
        if data:
            for _obj in data:
                result.append(processDocumentCms(_obj))
        return result

def get_cms_brands(store, payload, fields):
        db = new_mongodb.get_admin_db_client(store)
        def get_query(search_result):
            # return all products if search params is empty.
            if search_result == "":
                return {}

            # return text based search if search param's exists.        
            return {"$or": [{'name':{"$regex": search_result, "$options": "i"}}]}                                  

        def set_limit(limit):
            page_limit = 0
            skips = 0
            
            if int(limit) != 0 and int(limit) > 0:
                page_limit = int(limit)

            return page_limit, skips
        
        page_limit , skips = set_limit(payload['limit'] if 'limit' in payload else 0)
        
        query = get_query(payload["search"]) if "search" in payload else {} 
        result= db[CMS_BRANDS_COLLECTION].find(query, fields).skip(skips).limit(page_limit)
        result=processListCms(result)
        return result
