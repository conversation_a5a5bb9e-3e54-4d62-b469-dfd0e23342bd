import new_utils
import new_pgdb
from sqlalchemy import text
import logging
from mongo_db import user_db
from utils.common import convert_to_timestamp, paginate_data
from plugin import bc_price_list

logger = logging.getLogger()

def get_price_list_logs(store, page, limit, sort_array, filter, user_filter, price_list_filter):
    data = []
    conn = new_pgdb.get_connection(store['id'])
    try:
        offset = (page - 1) * limit
        
        count_query = f"SELECT COUNT(*) FROM pricelist_change_logs"
        select_query = f"""SELECT id, price_list_id, product_id, variant_id, parent_product_name, parent_product_sku, 
                           variant_name, variant_sku, old_price, updated_price, updated_by, updated_at FROM pricelist_change_logs"""

        # Prepare dynamic filters
        where_clauses = []
        params = {}

        if filter:
            where_clauses.append(
                """(parent_product_name ILIKE :filter OR parent_product_sku ILIKE :filter 
                    OR variant_name ILIKE :filter OR variant_sku ILIKE :filter 
                    OR (parent_product_name || ' ' || variant_name) ILIKE :filter)"""
            )
            params['filter'] = f"%{filter}%"

        if user_filter:
            where_clauses.append("updated_by = :user_filter")
            params['user_filter'] = user_filter
        
        if price_list_filter == 'null':
            where_clauses.append("price_list_id IS NULL")
        elif price_list_filter:
            where_clauses.append("price_list_id = :price_list_filter")
            params['price_list_filter'] = price_list_filter
        

        # Combine filters with AND logic
        if where_clauses:
            where_clause = " WHERE " + " AND ".join(where_clauses)
            count_query += where_clause
            select_query += where_clause

        # Apply sorting
        if len(sort_array):
            sort_direction = 'ASC' if sort_array[1] == '1' else 'DESC'
            select_query += f" ORDER BY {sort_array[0]} {sort_direction}"
        
        # Apply pagination
        if limit and page:
            select_query += " OFFSET :offset LIMIT :limit"
            params['offset'] = offset
            params['limit'] = limit

        # Execute count query
        count_rs = conn.execute(text(count_query), params)
        total_count = int(count_rs.scalar())
        # Execute main query
        result = conn.execute(text(select_query), params)

        price_list_logs_list = []
        for row in result:
            if row[10] == 'BigCommerce':
                updated_by = 'BigCommerce'
            else:
                user_data = user_db.fetch_user_by_username(row[10])
                if user_data:
                    updated_by = user_data.get('name', '')
                else:
                    updated_by = None

            if row[1] in [52]:
                # Map static IDs to their names
                static_price_list_map = {
                    52: "VIP"
                }
                price_list_name = static_price_list_map[row[1]]
            elif row[1] is None:
                price_list_name = "Wholesale"
            else:
                # Fetch price list data from BigCommerce for other IDs
                price_list_data, _ = bc_price_list.fetch_price_list_by_id(store, row[1])
                if price_list_data:
                    price_list_name = price_list_data.get('data', {}).get('name', '')
                else:
                    price_list_name = None

            row_data = {
                'id': row[0],
                'price_list_id': row[1],
                'price_list_name': price_list_name,
                'product_id': row[2],
                'variant_id': row[3],
                'parent_product_name': row[4],
                'parent_product_sku': row[5],
                'variant_name': row[6],
                'variant_sku': row[7] if row[7] != '' and row[7] is not None else row[5],
                'old_price': row[8],
                'updated_price': row[9],
                'updated_by': updated_by,
                'updated_at': convert_to_timestamp(row[11])
            }
            price_list_logs_list.append(row_data)

        # Apply pagination logic
        if page and limit:
            paginated_rows, current_page, total_pages, total_items = paginate_data(
                total_count, price_list_logs_list, page, limit
            )
            data = new_utils.calculate_pagination(
                paginated_rows, current_page, limit, total_items
            )
        else:
            data = price_list_logs_list

    finally:
        conn.close()

    return data

def get_price_list_logs_users(store):
    conn = new_pgdb.get_connection(store['id'])
    query = f"SELECT DISTINCT updated_by FROM pricelist_change_logs"
    result = conn.execute(text(query))
    users = []
    for row in result:
        username = row[0]
        user_data = user_db.fetch_user_by_username(username)
        if user_data:
            name = user_data.get('name', '')
            username = user_data.get('username', '')
            users.append({
                'name': name,
                'username': username
            })

    conn.close()
    return users

def get_price_list_logs_dropdown(store):
    static_price_lists = [
        {"id": 51, "name": "Partner", "active": True},
        {"id": 52, "name": "VIP", "active": True},
        {"id": 53, "name": "Distro", "active": True},
        {"id": 'null', "name": "Wholesale", "active": True}
    ]

    # Fetch all available price lists from BigCommerce
    price_lists, _ = bc_price_list.fetch_price_lists(store)

    for static_price_list in static_price_lists:
        price_lists['data'].append({
            'id': static_price_list['id'],
            'name': static_price_list['name'],
            'date_created': None,
            'date_modified': None,
            'active': static_price_list['active']
        })
    
    active_price_lists = []
    if price_lists['data'] != []:
        # Separate the price list with ID 7
        prioritized_list = []
        other_price_lists = []
        for price_list in price_lists['data']:
            if price_list['active']:
                row_data = {
                    'id': price_list['id'],
                    'name': price_list['name']
                }
                if price_list['id'] == 7:
                    prioritized_list.append(row_data)
                else:
                    other_price_lists.append(row_data)

        # Combine prioritized list with the rest
        active_price_lists = prioritized_list + other_price_lists

    return active_price_lists
