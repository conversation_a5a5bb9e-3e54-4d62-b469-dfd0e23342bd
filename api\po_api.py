from flask import request
from api import APIResource
from analytics import po_util
import logging
import traceback

logger = logging.getLogger()

class POReorderAPI(APIResource):

    def post_executor(self, request, token_payload, store):
        logger.debug("Entering POReorderAPI GET")
        try:
            payload = request.get_json(force=True)
            if payload:
                sku = payload.get("sku", None)
                quantity = payload.get("quantity", -1)
                if sku and quantity >= 0:
                    result, message = po_util.save_reorder_quantity(store,
                        token_payload['username'], sku, quantity, payload)
                    if result:
                        return {"message": message}, 200
                    else:
                        return {"message": message}, 409
            return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting POReorderAPI GET")

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering POReorderAPI data GET")
        try:
            query_params = request.args.to_dict()
            if query_params:
                res = po_util.get_reorders_data(store, query_params)
                if res['status'] == 200:
                    return res['data'], 200
                else:
                    return {"message": res['message']}, 422
            else:
                return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting POReorderAPI data")

    def get(self):
        return self.execute_store_request(request, self.get_executor)

    def delete_all_executor(self, request, token_payload, store):
        logger.debug("Entering POReorderAPI Delete")
        try:
            query_params = request.args.to_dict()
            if query_params:
                po_util.clear_filtered_reorders(store, query_params)
            else:
                po_util.clear_reorders(store)
            return {"message": "Cleared successfully."}, 200
        finally:
            logger.debug("Exiting POReorderAPI Delete")


    def post(self):
        return self.execute_store_request(request, self.post_executor)

    def delete(self):
        return self.execute_store_request(request, self.delete_all_executor)


class POReorderDeleteRecord(APIResource):
    def delete_executor(self, request, token_payload, store):
        logger.debug("Entering POReorderAPI Delete Record")
        try:
            payload = request.get_json(force=True)
            if payload and "sku" in payload:
                po_util.delete_single_record(store, payload["sku"])
                return {"message": "Record deleted successfully"}, 200
            return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting POReorderAPI Delete Record")

    def delete(self):
        return self.execute_store_request(request, self.delete_executor)


class POReorderCsv(APIResource):

    def get_executor(self, request, token_payload, store):
        logger.debug("Entering POReorderAPI csv data GET")
        try:
            query_params = request.args.to_dict()
            if query_params:
                res = po_util.get_reorders_data_csv(store, query_params)
                if res['status'] == 200:
                    return res['data'], 200
                else:
                    return {"message": res['message']}, 422
            else:
                return {"error": "Invalid request."}, 400
        finally:
            logger.debug("Exiting POReorderAPI csv data GET")

    def get(self):
        return self.execute_store_request(request, self.get_executor)
