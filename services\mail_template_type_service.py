from services import Service
from utils.common import parse_json
from bson import ObjectId
from datetime import datetime
from flask import send_file, make_response
import os
from werkzeug.utils import secure_filename
import logging
import requests

logger = logging.getLogger()


class TemplateTypes(Service):
    def get_types(self, body):
        result = []
        if (body):
            res = {}
            type_id = body['type_id']
            type = super().find_one({"_id": ObjectId(str(type_id))})

            res['id'] = type['id']
            res['name'] = type['name']
            res['short_code'] = type['short_code']
            res['created_at'] = type['created_at']
            res['updated_at'] = type['updated_at']
            res['status'] = type['status']

            result.append(res)
        else:
            types = super().find_all()
            types = parse_json(self.processList(types))
            for type in types:
                res = {}

                res['id'] = type['id']
                res['name'] = type['name']
                res['short_code'] = type['short_code']
                res['created_at'] = type['created_at']
                res['updated_at'] = type['updated_at']
                res['status'] = type['status']

                result.append(res)

        return result

    def create_type(self, body):
        response = {
            "status": 400
        }
        isUniqueName = self.checkForUniqueTypeName(body['name'])
        if isUniqueName:
            body["created_at"] = int(datetime.utcnow().timestamp())
            body["updated_at"] = ""
            body["short_code"] = body['name'].replace(" ", "_").lower()

            id = super().create(body)

            response['message'] = "Type created successfully"
            response['status'] = 200
        else:
            response['message'] = "The provided type name has already been matched with other types. Please provide a different type name."
            response['status'] = 409

        return response

    def update_template(self, body, type_id=None):
        response = {
            "status": 400
        }
        short_code = ''
        if body['status_update'] == 'false':
            isUniqueName = self.checkForUniqueTypeName(body['name'])
            short_code = body['name'].replace(" ", "_").lower()
        else:
            isUniqueName = True
            short_code = body['name'].replace(" ", "_").lower()

        if isUniqueName:
            id = super().update_one({"_id": ObjectId(str(type_id))}, {"$set":
                                                                      {
                                                                          "name": body['name'],
                                                                          "short_code": short_code,
                                                                          "status": body['status'],
                                                                          "updated_at":  int(datetime.utcnow().timestamp())
                                                                      }
                                                                      })
            response['message'] = "Template Updated successfully"
            response['status'] = 200
        else:
            response['message'] = "The provided template name has already been matched with other templates. Please provide a different template name."
            response['status'] = 409

        return response

    def get_type(self, type_id=None):
        result = {}
        type = super().find_one({"_id": ObjectId(str(type_id))})

        result['id'] = type['id']
        result['name'] = type['name']
        result['short_code'] = type['short_code']
        result['created_at'] = type['created_at']
        result['updated_at'] = type['updated_at']
        result['status'] = type['status']

        return result

    def delete_by_id(self, type_id):
        return super().delete({"_id": ObjectId(str(type_id))})

    def checkForUniqueTypeName(self, name):
        name = name.replace(" ", "_").lower()
        types = super().find_all()
        types = parse_json(self.processList(types))
        for type in types:
            if type['short_code'] == name:
                return False
        return True
