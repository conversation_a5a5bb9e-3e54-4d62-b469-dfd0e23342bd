from sqlalchemy import text
import pg_db
from mongo_db import user_db
from datetime import datetime
from utils import common
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import logging
import traceback

logger = logging.getLogger()


def _insert_new_board(conn, name, description, is_archived, username):
    query = text(
        f"""INSERT INTO {pg_db.agile_project_boards} (name, description, is_archived, created_by, updated_by)
            VALUES (:name, :description, :is_archived, :created_by, :created_by)
            RETURNING id;
        """
    )
    query = query.params(name=name, description=description, is_archived=is_archived, created_by=username)
    result = conn.execute(query)
    board_id = result.fetchone()[0]
    return board_id
    

def add_board(name, description, is_archived, username):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:                
        data = _insert_new_board(conn, name, description, is_archived, username)
        if data:
            response['status'] = 200
            response['message'] = "Data inserted successfully."
        else:
            response['status'] = 500
            response['message'] = "Data insertion failed."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response