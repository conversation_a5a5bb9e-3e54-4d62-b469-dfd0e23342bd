from datetime import datetime
from mongo_db import cms_brands_db, cms_db
from services import Service
from fields.navigation_fields import brands_fields
from utils import bc
from utils.common import parse_json

class Brands(Service):
    def get_brands(self, payload):
        # def get_query(search_result):
        #     # return all products if search params is empty.
        #     if search_result == "":
        #         return {}

        #     # return text based search if search param's exists.
        #     return {"$text": {"$search": search_result}}

        # def set_limit(limit):
        #     page_limit = 0
        #     skips = 0
            
        #     if int(limit) != 0 and int(limit) > 0:
        #         page_limit = int(limit)

        #     return page_limit, skips
        
        # page_limit , skips = set_limit(payload['limit'] if 'limit' in payload else 0)
        
        # query = get_query(payload["search"]) if "search" in payload else {}
        
        data = cms_brands_db.get_cms_brands(payload,brands_fields)

        # ProcessList ...
        # data = self.processList(data)
        return parse_json(data)
    
    def sync_all_brands(self,store):
        
        brands=super().find_all()
        bc_brands=bc.get_bc_brands(store)
        
        bc_brand_ids = {brand['id'] for brand in bc_brands}
        for brand in brands:
          if brand['id'] in bc_brand_ids:
            if True:
              new_list={} 
              seo_details={}
              seo_details["page_name"]=brand["name"]
              
              seo_details["page_url"]=brand["custom_url"]["url"] if brand['custom_url'] and 'url' in brand['custom_url'] else ''
              seo_details["meta_title"]=brand["page_title"]
              seo_details["meta_description"]=brand["meta_description"]

              user = {}
              user["user_id"]= "" 
              user["user_name"]= ""              

              
              new_list["id"]=brand["id"]
              new_list["name"]=brand["name"]
              new_list["type"]="brand"            
              new_list["url"]=brand["custom_url"]["url"] if brand['custom_url'] and 'url' in brand['custom_url'] else ''                                         
              new_list["default_layout"] = {}
              new_list["versions"]= [
                   { 
                    "name":brand["name"],
                    "status":"active", 
                    "class":"",
                    "type": "brand",
                    "seo_details":seo_details,
                    "components":[
                        {
                        "id": 1,
                        "name": "HTML Block",
                        "code": "html_block",  
                        "variant": {
                          "id": "1",
                          "name": "HTML Block",
                          "admin_layout": "style1",
                          "class": [],                          
                          "config": {
                               "data": ""   
                                 }
                        },  
                        "children": []
                        }
                    ],
                    "created_at":int(datetime.utcnow().timestamp()),
                    "created_by": user,                                                                              
                    "version":0,
             } ]
              new_list["preview_state"] = {} 
              new_list["updated_at"]=""
              new_list["created_at"]=int(datetime.utcnow().timestamp())
              new_list["status"] = "active"
              new_list["image_url"]=brand['image_url']
              res=cms_brands_db.create(new_list)
          else:
            super().delete_by_id(brand['id']) 
              
        result='success'
        return result
    
