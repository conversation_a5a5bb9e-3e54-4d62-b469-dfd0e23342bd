from flask import request
import logging
import traceback
from api import APIResource
from iam import role_service

logger = logging.getLogger()

class App(APIResource):
  def post_executor(self, request, token_payload, store):
    try:
      payload = request.get_json(force=True)
      permission_obj = self.service.get_permission_service().get_permission_by_store_id(store)
      all_roles = role_service.get_roles(store)
      self.service.get_app_menu_service().process_data(store, payload, permission_obj, all_roles)    
    finally:
      logger.debug("Exiting Permissions GET")
      
  def post(self):
        return self.execute_store_request(request, self.post_executor)