from sqlalchemy import text
import pg_db
from mongo_db import user_db
from psycopg2.errors import UniqueViolation
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from datetime import datetime
import logging
import traceback
from utils.common import convert_to_timestamp

logger = logging.getLogger()


def _fetch_module_detail(conn, project_id, id):
    data = []
    query = text(
        f"""SELECT * FROM {pg_db.project_modules}
            WHERE project_id = :project_id
            AND id = :id;
        """
    )
    query = query.params(project_id=project_id, id=id)
    result = conn.execute(query)    
    for row in result.fetchall():
        row_data = {
            'id': row[0],
            'project_id': row[1],
            'name': row[2],
            'description': row[3],
            'is_archived': row[4],
            'created_by': row[5],
            'updated_by': row[6],
            'created_at': convert_to_timestamp(row[7]),
            'updated_at': convert_to_timestamp(row[8]),
            'sort_id': row[9],
            'is_visible': row[10],
            'is_default': row[11]
        }
        data.append(row_data)
    return data
  

def get_module_detail(project_id, id):
    response = {
        "status" :400        
    }
    conn = pg_db.get_connection()
    try:
        data = _fetch_module_detail(conn, project_id, id)
        if data:
            response['data'] = data
            response['status'] = 200
        else:
            response['data'] = []
            response['status'] = 200
    finally:
        if conn:
            conn.close()
    return response


def update_project_module_detail(payload, username, project_id, id):
    response = {
        "status": 400
    }
    conn = pg_db.get_connection()
    try:
        if not id and not project_id:
            response['status'] = 500
            response['message'] = "Invalid payload, 'id' or 'project Id' is missing."
            return response

        existing_module = _fetch_module_detail(conn, project_id, id)
        if not existing_module:
            response['status'] = 404
            response['message'] = "Module not found."
            return response

        existing_module = existing_module[0]

        # Update fields if provided in payload
        update_fields = {}
        for field in ['name', 'description', 'is_archived', 'is_visible', 'is_default']:   
            if field in payload:
                update_fields[field] = payload[field]

        # Update the project with the modified data
        data = modify_project_module_detail(conn, update_fields, project_id, id, username)

        if data:
            if 'is_default' in payload:
                query = text(
                    f"""UPDATE {pg_db.project_modules} set is_default = FALSE where project_id = :project_id and id != :id and is_default = TRUE;"""
                )
                query = query.params(project_id=project_id, id=id)
                conn.execute(query)
            response['status'] = 200
            response['message'] = "Data updated successfully."
        else:
            response['status'] = 400
            response['message'] = "Data updation failed."
    except IntegrityError as e:
        logger.error(traceback.format_exc())
        if isinstance(e.orig, UniqueViolation):
            response['status'] = 409
            response['message'] = "Duplicate key violation: This module already exists in the rules."
        else:
            response['status'] = 500
            response['message'] = "Something went wrong."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response


def modify_project_module_detail(conn, update_fields, project_id, id, username):
    set_clause = ", ".join([f"{field} = :{field}" for field in update_fields])
    query = text(
        f"""UPDATE {pg_db.project_modules}
            SET 
                {set_clause},
                updated_by = :updated_by,
                updated_at = CURRENT_TIMESTAMP
            WHERE 
                id = :id
                AND project_id = :project_id;"""
    )

    params = update_fields.copy()
    params.update({'updated_by': username, 'id': id, 'project_id': project_id})    
    result = conn.execute(query, params)    
    return result.rowcount > 0
            
def _remove_project_module(conn, project_id, id):
    dependency_query = text(
        f"""SELECT COUNT(pc.module_id) AS card_count
            FROM {pg_db.project_modules} AS pm
            LEFT JOIN {pg_db.project_cards} AS pc ON pm.id = pc.module_id
            WHERE pm.project_id = :project_id
            AND pm.id = :id
        """
    )
    dependency_query = dependency_query.params(project_id=project_id, id=id)
    result = conn.execute(dependency_query)
    dependency_result = result.fetchone()
    card_count = dependency_result[0]
   
    if card_count > 0:
        return False
    
    delete_query = text(
        f"""DELETE FROM {pg_db.project_modules}
            WHERE project_id = :project_id
            AND id = :id
        """
    )
    delete_query = delete_query.params(project_id=project_id, id=id)
    delete_result = conn.execute(delete_query)       
    
    return delete_result.rowcount > 0


def delete_module(project_id, id):
    response = {
        "status" :400
    }
    conn = pg_db.get_connection()
    try:
        if id:
            data = _remove_project_module(conn, project_id, id)
            if data:
                response['status'] = 200
                response['message'] = "Data deleted successfully."
            else:
                response['status'] = 400
                response['message'] = "Data deletion failed."
        else:
            response['status'] = 400
            response['message'] = "id is missing."
    finally:
        if conn:
            conn.commit()
            conn.close()
    return response
