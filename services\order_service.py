

from mongo_db import customer_db, user_db
import pg_db
from sqlalchemy import text
from datetime import datetime
from utils import bc
from utils.common import get_order_status_name
from pg_db import order_consignment_db
from pg_db.express_orders_db import ExpressOrders
from services import Service
from utils.common import calculatePaginationData, parse_json, convert_to_timestamp
from utils.bc import get_bc_order_details_with_queryparams, get_bc_orders, get_bc_order_details, get_bc_total_orders_count, get_bc_order_by_id
from operator import itemgetter
from utils import redis_util, store_util
import time


class Orders(Service):
    def get_all(self, store, payload):
        response = {'status': 400}
        conn = pg_db.get_connection()
        try:
            status_id = payload.get('status_id')
            status_id = int(status_id) if status_id not in ('', None) else None
            sort_by = str(payload.get('sort_by',''))
            sort_order = str(payload.get('sort_order', ''))
            page = int(payload.get('page', 1))
            limit = int(payload.get('limit', 10))
            filter = payload.get('filter', '')
            is_express_orders = payload.get('is_express_orders', 'false')
            rep_name = payload.get('rep_name', '')
            
            condition = ""
            status_id_condition = ""
            if status_id or status_id == 0:
                status_id_condition = f" WHERE o.order_status_id = {status_id}"

            rep_name_condition = ""
            if rep_name:
                rep_name_condition = f"scr.rep_name = '{rep_name}'"

            search_condition = ""
            if filter.isdigit():
                if is_express_orders.lower() == 'true':
                    search_condition = f"CAST(eo.order_id AS TEXT) ILIKE '%{filter}%'"
                else:
                    search_condition = f"CAST(o.order_id AS TEXT) ILIKE '%{filter}%'"
            else:
                search_condition = f"((CONCAT(cs.first_name, ' ', cs.last_name) ILIKE '%{filter}%') OR (cs.email ILIKE '%{filter}%'))"

            if status_id_condition and search_condition:
                condition = f"{status_id_condition} AND {search_condition}"
            elif status_id_condition:
                condition = status_id_condition
            elif search_condition:
                condition = f"WHERE {search_condition}"

            if rep_name_condition:
                if condition:
                    condition += f" AND {rep_name_condition}"
                else:
                    condition = f"WHERE {rep_name_condition}"

            
            order_express = False
            if is_express_orders.lower() == 'true':
                order_express = True                
                total_count_query = f"""SELECT COUNT(*) FROM (SELECT o.order_created_date_time AS date_created, eo.order_id, cs.customer_id, STRING_AGG(DISTINCT oc.order_type, ',') AS order_type
                                FROM express_orders AS eo LEFT JOIN orders AS o ON eo.order_id = o.order_id
                                LEFT JOIN customers AS cs ON eo.customer_id = cs.customer_id
                                LEFT JOIN order_consignment as oc ON o.order_id = oc.order_id
                                LEFT JOIN salesforce_customer_rep AS scr ON o.customer_id = scr.customer_id
                                {condition} GROUP BY o.order_created_date_time, eo.order_id, cs.customer_id) AS subquery""" 
                
                result_count = conn.execute(text(total_count_query))
                total_count = int(result_count.scalar())                               

                query = text(f"""SELECT 
                                    o.order_created_date_time AS date_created, 
                                    eo.order_id AS id, 
                                    o.order_status AS status, 
                                    cs.first_name, 
                                    cs.last_name, 
                                    cs.customer_id, 
                                    o.total_including_tax, 
                                    o.order_status_id,
                                    STRING_AGG(DISTINCT oc.order_type, ',') AS order_type,
                                    scr.rep_name,
                                    cs.email
                                FROM express_orders AS eo
                                LEFT JOIN orders AS o ON eo.order_id = o.order_id
                                LEFT JOIN customers AS cs ON eo.customer_id = cs.customer_id
                                LEFT JOIN order_consignment as oc ON o.order_id = oc.order_id
                                LEFT JOIN salesforce_customer_rep AS scr ON o.customer_id = scr.customer_id
                                {condition}
                                GROUP BY o.order_created_date_time, eo.order_id,
                                o.order_status, cs.first_name, cs.last_name, cs.customer_id, o.total_including_tax, o.order_status_id, scr.rep_name
                            """)
                
            else:
                order_express = False
                total_count_query = f"""SELECT COUNT(*) FROM (SELECT o.order_created_date_time AS date_created, o.order_id, cs.customer_id, STRING_AGG(DISTINCT oc.order_type, ',') AS order_type
                                FROM orders AS o LEFT JOIN customers AS cs ON o.customer_id = cs.customer_id
                                LEFT JOIN order_consignment AS oc ON o.order_id = oc.order_id
                                LEFT JOIN salesforce_customer_rep AS scr ON o.customer_id = scr.customer_id
                                {condition} GROUP BY o.order_created_date_time, o.order_id, cs.customer_id) AS subquery"""               
                                
                result_count = conn.execute(text(total_count_query))
                total_count = int(result_count.scalar())                                    

                query = text(f"""SELECT 
                                    o.order_created_date_time AS date_created, 
                                    o.order_id AS id, 
                                    o.order_status AS status, 
                                    cs.first_name, 
                                    cs.last_name, 
                                    cs.customer_id, 
                                    o.total_including_tax, 
                                    o.order_status_id,
                                    STRING_AGG(DISTINCT oc.order_type, ',') AS order_type,
                                    scr.rep_name,
                                    cs.email
                                FROM orders AS o
                                LEFT JOIN customers AS cs ON o.customer_id = cs.customer_id
                                LEFT JOIN order_consignment AS oc ON o.order_id = oc.order_id
                                LEFT JOIN salesforce_customer_rep AS scr ON o.customer_id = scr.customer_id
                                {condition}
                                GROUP BY o.order_created_date_time, o.order_id, 
                                o.order_status, cs.first_name, cs.last_name, cs.customer_id, o.total_including_tax, o.order_status_id, scr.rep_name
                                """)
            
            if sort_by and sort_order:
                query = text(str(query) + f" ORDER BY {sort_by} {sort_order}")

            offset = (page - 1) * limit
            query = text(str(query) + f" LIMIT {limit} OFFSET {offset}")
            result = conn.execute(query)           
            res = result.fetchall()

            orders = []
            if res:
                for row in res:
                    row_data = {
                        'date_created': convert_to_timestamp(row[0]),
                        'id': row[1],
                        'status': row[2] if row[2] else get_order_status_name(row[7]),
                        'first_name': row[3],
                        'last_name': row[4],
                        'customer_id': row[5],
                        'total_inc_tax': row[6],
                        'status_id': row[7],
                        'order_type': row[8],
                        'is_express_orders': order_express,
                        'customer_rep_name': row[9],
                        'email': row[10]
                    }
                    orders.append(row_data)
            
            if orders:
                for order in orders:
                    rewards=customer_db.get_order_reward_points(store,order['id'])
                    if rewards:
                        order['rewards']=rewards
                    else:
                        order['rewards']=0
                
                                  
                limit = int(payload["limit"]) if payload.__contains__("limit") else 1
                page = int(payload["page"]) if payload.__contains__("page") else 1

                data = calculatePaginationData(orders, page, limit, total_count)

                response['data'] = data
                response['status'] = 200
            else:
                response['data'] = orders
                response['status'] = 200
        except Exception as e:
            error_message = str(e)
            response['status'] = 422
            response['message'] = error_message
            raise e
        finally:
            if conn:
                conn.close()       
        return response

    def get_order(self, store, order_id):
        bc_api = store_util.get_bc_api_creds(store)
        res = get_bc_order_details(bc_api, order_id, None, store)
        
        return parse_json(res)
    
    def get_all_customer_orders(self, store, customer_id, payload):
        response = {'status': 400}
        conn = pg_db.get_connection()
        try:
            sort_by = str(payload.get('sort_by',''))
            sort_order = str(payload.get('sort_order', ''))
            page = int(payload.get('page', 1))
            limit = int(payload.get('limit', 10))              
            total_count_query = text(f"""SELECT COUNT(*) FROM orders WHERE customer_id= :customer_id""")
            
            result_count = conn.execute(total_count_query.params(customer_id=customer_id))
            total_count = int(result_count.scalar())                               

            query = f"""SELECT 
                                o.order_created_date_time AS date_created, 
                                o.order_id AS id, 
                                oc.order_type AS order_type,
                                o.order_status AS status, 
                                o.total_including_tax, 
                                o.order_status_id
                                
                            FROM orders AS o
                            LEFT JOIN order_consignment AS oc ON o.order_id = oc.order_id
                            WHERE o.customer_id = {customer_id}
                            GROUP BY o.order_created_date_time, o.order_id, 
                            o.order_status, o.total_including_tax, o.order_status_id, oc.order_type  
                            """
            if sort_by and sort_order:
                query = text(str(query) + f" ORDER BY {sort_by} {sort_order}")

            offset = (page - 1) * limit
            query = text(str(query) + f" LIMIT {limit} OFFSET {offset}")
            result = conn.execute(query)           
            res = result.fetchall()

            orders = []
            if res:
                for row in res:
                    row_data = {
                        'date_created': convert_to_timestamp(row[0]),
                        'id': row[1],
                        'type': row[2],
                        'status': row[3] if row[3] else get_order_status_name(row[5]),
                        'total': row[4]
                    }
                    bc_order = bc.get_bc_order_by_id(store, row_data['id'])
                    if bc_order:
                       row_data['comment'] = bc_order[0]['customer_message']
                    else:
                        row_data['comment'] = None
                    orders.append(row_data)
            
            if orders:                
                limit = int(payload["limit"]) if payload.__contains__("limit") else 1
                page = int(payload["page"]) if payload.__contains__("page") else 1

                data = calculatePaginationData(orders, page, limit, total_count)

                response['data'] = data
                response['status'] = 200
            else:
                response['data'] = orders
                response['status'] = 200
        except Exception as e:
            error_message = str(e)
            response['status'] = 422
            response['message'] = error_message
            raise e
        finally:
            if conn:
                conn.close()       
        return response

  