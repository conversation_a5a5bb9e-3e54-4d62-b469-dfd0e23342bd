from flask import request
import logging
from api import APIResource
import traceback
from schemas.blogs import author_schema, author_update_schema
from cms.blogs import blogs_list

logger = logging.getLogger()


#get all webpages
class Authors(APIResource):
    def get_executor(self, request, token_payload, store):
        try:
            query_params = request.args.to_dict()                    
            res = blogs_list.get_authors(store, query_params)
            return res, 200
        finally:
            logger.debug("Exiting Authors GET")    
    
    def get(self, tenant_id, store_id):
        return self.execute_store_request(request, self.get_executor)

#create blogs
class CreateAuthors(APIResource):
    def post_executor(self, request, token_payload, store, token=None):
        try:
            req_body = request.get_json(force=True)
            validated_data = author_schema.validate(req_body)
            res = blogs_list.create_author(store, validated_data)                
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409
        finally:
            logger.debug("Exiting Author POST")        


    def post(self):
        return self.execute_store_request(request, self.post_executor)
    
#upadate and delete webpage
class AuthorOperations(APIResource):
    def put_executor(self, request, token_payload, store, author_id):
        try:
            req_body = request.get_json(force=True)
            validated_data = author_update_schema.validate(req_body)
            res = blogs_list.update_author(store, validated_data, author_id)                
            
            if res['status'] == 200:
                return {"message": res['message']}, 200
            else:
                return {"message": res['message']}, 409   
        finally:
            logger.debug("Exiting Author PUT")
                
    def get_executor(self, request, token_payload, store, author_id):
        try:
            res = blogs_list.get_author(store, author_id)    
            return res, 200
        finally:
            logger.debug("Exiting author GET") 


    def delete_executor(self, request, token_payload, store, author_id):
        try:            
            success = blogs_list.delete_author_by_id(store, author_id)
            if success:
                return {"status": "ok"}, 200
            return {"status": "failed"}, 500            
        finally:
            logger.debug("Exiting Author DELETE")


    def put(self, author_id):
        return self.execute_store_request(request, self.put_executor, author_id)
    
    def get(self, author_id):
        return self.execute_store_request(request, self.get_executor, author_id)

    def delete(self, author_id):
        return self.execute_store_request(request, self.delete_executor, author_id)